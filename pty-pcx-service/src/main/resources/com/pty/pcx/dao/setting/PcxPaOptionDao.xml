<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.setting.PcxPaOptionDao">

    <resultMap id="PaOptionResultMap" type="com.pty.pcx.entity.setting.PaOption">
        <id column="opt_id" property="optId" jdbcType="VARCHAR"/>
        <result column="sys_id" property="sysId" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="INTEGER"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="acb_code" property="acbCode" jdbcType="VARCHAR"/>
        <result column="opt_code" property="optCode" jdbcType="VARCHAR"/>
        <result column="opt_name" property="optName" jdbcType="VARCHAR"/>
        <result column="opt_value" property="optValue" jdbcType="VARCHAR"/>
        <result column="opt_desc" property="optDesc" jdbcType="VARCHAR"/>
        <result column="is_visible" property="isVisible" jdbcType="INTEGER"/>
        <result column="is_edit" property="isEdit" jdbcType="INTEGER"/>
        <result column="atom_code" property="atomCode" jdbcType="VARCHAR"/>
        <result column="field_disptype" property="fieldDisptype" jdbcType="VARCHAR"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="conmode_code" property="conmodeCode" jdbcType="INTEGER"/>
        <result column="field_valueset_code" property="fieldValuesetCode" jdbcType="VARCHAR"/>
        <result column="adm_code" property="admCode" jdbcType="VARCHAR"/>
        <result column="is_super_control" property="isSuperControl" jdbcType="INTEGER"/>
        <result column="ord_seq" property="ordSeq" jdbcType="INTEGER"/>
        <result column="is_enable_setting" property="isEnableSetting" jdbcType="INTEGER"/>
        <result column="setting_content" property="settingContent" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="allColumn">
        opt_id, sys_id, fiscal, agy_code, acb_code, opt_code, opt_name, opt_value, opt_desc,
        is_visible, is_edit, atom_code, field_disptype, group_name, conmode_code,
        field_valueset_code, adm_code, is_super_control, ord_seq, is_enable_setting,
        setting_content, mof_div_code
    </sql>

    <sql id="allColumnCond">
        <trim prefix="" suffixOverrides=",">
            <if test="optId != null and optId != ''">AND opt_id = #{optId,jdbcType=VARCHAR}</if>
            <if test="sysId != null and sysId != ''">AND sys_id = #{sysId,jdbcType=VARCHAR}</if>
            <if test="fiscal != null">AND fiscal = #{fiscal,jdbcType=INTEGER}</if>
            <if test="agyCode != null and agyCode != ''">AND agy_code = #{agyCode,jdbcType=VARCHAR}</if>
            <if test="acbCode != null and acbCode != ''">AND acb_code = #{acbCode,jdbcType=VARCHAR}</if>
            <if test="optCode != null and optCode != ''">AND opt_code = #{optCode,jdbcType=VARCHAR}</if>
            <if test="optName != null and optName != ''">AND opt_name = #{optName,jdbcType=VARCHAR}</if>
            <if test="optValue != null and optValue != ''">AND opt_value = #{optValue,jdbcType=VARCHAR}</if>
            <if test="optDesc != null and optDesc != ''">AND opt_desc = #{optDesc,jdbcType=VARCHAR}</if>
            <if test="isVisible != null">AND is_visible = #{isVisible,jdbcType=INTEGER}</if>
            <if test="isEdit != null">AND is_edit = #{isEdit,jdbcType=INTEGER}</if>
            <if test="atomCode != null and atomCode != ''">AND atom_code = #{atomCode,jdbcType=VARCHAR}</if>
            <if test="fieldDisptype != null and fieldDisptype != ''">AND field_disptype = #{fieldDisptype,jdbcType=VARCHAR}</if>
            <if test="groupName != null and groupName != ''">AND group_name = #{groupName,jdbcType=VARCHAR}</if>
            <if test="conmodeCode != null">AND conmode_code = #{conmodeCode,jdbcType=INTEGER}</if>
            <if test="fieldValuesetCode != null and fieldValuesetCode != ''">AND field_valueset_code = #{fieldValuesetCode,jdbcType=VARCHAR}</if>
            <if test="admCode != null and admCode != ''">AND adm_code = #{admCode,jdbcType=VARCHAR}</if>
            <if test="isSuperControl != null">AND is_super_control = #{isSuperControl,jdbcType=INTEGER}</if>
            <if test="ordSeq != null">AND ord_seq = #{ordSeq,jdbcType=INTEGER}</if>
            <if test="isEnableSetting != null">AND is_enable_setting = #{isEnableSetting,jdbcType=INTEGER}</if>
            <if test="settingContent != null and settingContent != ''">AND setting_content = #{settingContent,jdbcType=VARCHAR}</if>
            <if test="mofDivCode != null and mofDivCode != ''">AND mof_div_code = #{mofDivCode,jdbcType=VARCHAR}</if>
        </trim>
    </sql>

    <sql id="allColumnSet">
        <trim prefix="" suffixOverrides=",">
            <if test="acbCode != null and acbCode != ''">acb_code = #{acbCode,jdbcType=VARCHAR},</if>
            <if test="optCode != null and optCode != ''">opt_code = #{optCode,jdbcType=VARCHAR},</if>
            <if test="optName != null and optName != ''">opt_name = #{optName,jdbcType=VARCHAR},</if>
            <if test="optValue != null and optValue != ''">opt_value = #{optValue,jdbcType=VARCHAR},</if>
            <if test="optDesc != null and optDesc != ''">opt_desc = #{optDesc,jdbcType=VARCHAR},</if>
            <if test="isVisible != null">is_visible = #{isVisible,jdbcType=INTEGER},</if>
            <if test="isEdit != null">is_edit = #{isEdit,jdbcType=INTEGER},</if>
            <if test="atomCode != null and atomCode != ''">atom_code = #{atomCode,jdbcType=VARCHAR},</if>
            <if test="fieldDisptype != null and fieldDisptype != ''">field_disptype = #{fieldDisptype,jdbcType=VARCHAR},</if>
            <if test="groupName != null and groupName != ''">group_name = #{groupName,jdbcType=VARCHAR},</if>
            <if test="conmodeCode != null">conmode_code = #{conmodeCode,jdbcType=INTEGER},</if>
            <if test="fieldValuesetCode != null and fieldValuesetCode != ''">field_valueset_code = #{fieldValuesetCode,jdbcType=VARCHAR},</if>
            <if test="admCode != null and admCode != ''">adm_code = #{admCode,jdbcType=VARCHAR},</if>
            <if test="isSuperControl != null">is_super_control = #{isSuperControl,jdbcType=INTEGER},</if>
            <if test="ordSeq != null">ord_seq = #{ordSeq,jdbcType=INTEGER},</if>
            <if test="isEnableSetting != null">is_enable_setting = #{isEnableSetting,jdbcType=INTEGER},</if>
            <if test="settingContent != null and settingContent != ''">setting_content = #{settingContent,jdbcType=VARCHAR},</if>
        </trim>
    </sql>

    <select id="selectByQO" parameterType="com.pty.pcx.qo.setting.PaOptionQO" resultMap="PaOptionResultMap">
        SELECT <include refid="allColumn" />
         FROM pa_option WHERE 1=1
        <include refid="allColumnCond" />
        order by ord_seq,opt_code
    </select>

    <insert id="insert" parameterType="com.pty.pcx.qo.setting.PaOptionQO">
        INSERT INTO pa_option (opt_id, sys_id, fiscal, agy_code, acb_code, opt_code, opt_name, opt_value, opt_desc, is_visible, is_edit, atom_code, field_disptype, group_name, conmode_code, field_valueset_code, adm_code, is_super_control, ord_seq, is_enable_setting, setting_content, mof_div_code)
        VALUES (#{optId,jdbcType=VARCHAR}, #{sysId,jdbcType=VARCHAR}, #{fiscal,jdbcType=INTEGER}, #{agyCode,jdbcType=VARCHAR}, #{acbCode,jdbcType=VARCHAR}, #{optCode,jdbcType=VARCHAR}, #{optName,jdbcType=VARCHAR}, #{optValue,jdbcType=VARCHAR}, #{optDesc,jdbcType=VARCHAR}, #{isVisible,jdbcType=INTEGER}, #{isEdit,jdbcType=INTEGER}, #{atomCode,jdbcType=VARCHAR}, #{fieldDisptype,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR}, #{conmodeCode,jdbcType=INTEGER}, #{fieldValuesetCode,jdbcType=VARCHAR}, #{admCode,jdbcType=VARCHAR}, #{isSuperControl,jdbcType=INTEGER}, #{ordSeq,jdbcType=INTEGER}, #{isEnableSetting,jdbcType=INTEGER}, #{settingContent,jdbcType=VARCHAR}, #{mofDivCode,jdbcType=VARCHAR})
    </insert>

    <update id="updateById" parameterType="com.pty.pcx.qo.setting.PaOptionQO">
        UPDATE pa_option
        SET <include refid="allColumnSet"/>
        WHERE 1=1
            and agy_code = #{agyCode,jdbcType=VARCHAR}
            and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            and fiscal = #{fiscal,jdbcType=VARCHAR}
            and opt_id = #{optId,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByQO" parameterType="com.pty.pcx.qo.setting.PaOptionQO">
        DELETE FROM pa_option WHERE 1=1 <include refid="allColumnCond" />
    </delete>

</mapper>