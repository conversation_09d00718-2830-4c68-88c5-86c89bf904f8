<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.attachlist.PcxAttachListRelationDao">
    <sql id="allColumn">
        id,                                               attach_id,                                        attach_type_id,                                   bill_id,
        attach_list_id,                                   upload_way
    </sql>

    <sql id="allColumnAlias">
        id as id,                                         attach_id as attachId,                            attach_type_id as attachTypeId,                   bill_id as billId,
        attach_list_id as attachListId,                   upload_way as uploadWay
    </sql>

    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR},                           #{attachId,jdbcType=VARCHAR},                     #{attachTypeId,jdbcType=VARCHAR},                 #{billId,jdbcType=VARCHAR},
        #{attachListId,jdbcType=VARCHAR},                 #{uploadWay,jdbcType=INTEGER}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id=#{id,jdbcType=VARCHAR},
             </if>
            <if test="attachId != null and attachId != ''">
                attach_id=#{attachId,jdbcType=VARCHAR},
             </if>
            <if test="attachTypeId != null and attachTypeId != ''">
                attach_type_id=#{attachTypeId,jdbcType=VARCHAR},
             </if>
            <if test="billId != null and billId != ''">
                bill_id=#{billId,jdbcType=VARCHAR},
             </if>
            <if test="attachListId != null and attachListId != ''">
                attach_list_id=#{attachListId,jdbcType=VARCHAR},
             </if>
            <if test="uploadWay != null">
                upload_way=#{uploadWay,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="columnSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="attachId != null and attachId != ''">
                attach_id,
            </if>
            <if test="attachTypeId != null and attachTypeId != ''">
                attach_type_id,
            </if>
            <if test="billId != null and billId != ''">
                bill_id,
            </if>
            <if test="attachListId != null and attachListId != ''">
                attach_list_id,
            </if>
            <if test="uploadWay != null">
                upload_way,
            </if>
        </trim>
    </sql>

    <sql id="columnValueSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="attachId != null and attachId != ''">
                #{attachId,jdbcType=VARCHAR},
            </if>
            <if test="attachTypeId != null and attachTypeId != ''">
                #{attachTypeId,jdbcType=VARCHAR},
            </if>
            <if test="billId != null and billId != ''">
                #{billId,jdbcType=VARCHAR},
            </if>
            <if test="attachListId != null and attachListId != ''">
                #{attachListId,jdbcType=VARCHAR},
            </if>
            <if test="uploadWay != null">
                #{uploadWay,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="attachId != null and attachId != ''">
            AND attach_id=#{attachId,jdbcType=VARCHAR}
        </if>
        <if test="attachTypeId != null and attachTypeId != ''">
            AND attach_type_id=#{attachTypeId,jdbcType=VARCHAR}
        </if>
        <if test="billId != null and billId != ''">
            AND bill_id=#{billId,jdbcType=VARCHAR}
        </if>
        <if test="attachListId != null and attachListId != ''">
            AND attach_list_id=#{attachListId,jdbcType=VARCHAR}
        </if>
        <if test="uploadWay != null">
            AND upload_way=#{uploadWay,jdbcType=INTEGER}
        </if>
        <if test="billIds != null and billIds.size() > 0">
            <foreach collection="billIds" item="billId" open="AND bill_id IN (" close=")" separator=",">
                #{billId,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        INSERT INTO pa_attach_list_relation (
            <include refid="allColumn" />
        ) VALUES (
            <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        INSERT INTO pa_attach_list_relation (
            <include refid="columnSelective" />
        ) VALUES (
            <include refid="columnValueSelective" />
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        DELETE FROM pa_attach_list_relation
        WHERE
            id=#{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM pa_attach_list_relation
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        DELETE FROM pa_attach_list_relation
        WHERE 1=1
            <include refid="allColumnCond" />
    </delete>
    <delete id="deleteByBillId">
        DELETE FROM pa_attach_list_relation
        WHERE 1=1
        and bill_id=#{billId,jdbcType=VARCHAR}
    </delete>

    <delete id="removeByBillIdsAndAttachIds">
        DELETE FROM pa_attach_list_relation
        WHERE 1=1
        <if test="billIds != null and billIds.size() > 0">
            AND bill_id IN
            <foreach collection="billIds" item="billId" open="(" separator="," close=")">
                #{billId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="attachIds != null and attachIds.size() > 0">
            AND attach_id IN
            <foreach collection="attachIds" item="attachId" open="(" separator="," close=")">
                #{attachId,jdbcType=VARCHAR}
            </foreach>
        </if>
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        UPDATE pa_attach_list_relation
            <set>
                <include refid="allColumnSet" />
            </set>
        WHERE id=#{id,jdbcType=VARCHAR}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        SELECT
            <include refid="allColumnAlias" />
        FROM pa_attach_list_relation
        WHERE id=#{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        SELECT
            <include refid="allColumnAlias" />
        FROM pa_attach_list_relation
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectByAttachListIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        SELECT
        <include refid="allColumnAlias" />
        FROM pa_attach_list_relation
        WHERE attach_list_id IN
        <foreach collection="attachListIds" index="index" item="attachListId" open="(" separator="," close=")">
            #{attachListId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.attachlist.PcxAttachListRelation" resultType="com.pty.pcx.entity.attachlist.PcxAttachListRelation">
        SELECT
            <include refid="allColumnAlias" />
        FROM pa_attach_list_relation
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.attachlist.PcxAttachListRelation" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pa_attach_list_relation
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>
</mapper>
