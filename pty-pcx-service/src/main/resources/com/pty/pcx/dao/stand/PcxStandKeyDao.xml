<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.stand.PcxStandKeyDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.stand.PcxStandKey" id="PcxStandKeyMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="expenseTypeCode" column="expense_type_code" jdbcType="VARCHAR"/>
        <result property="expenseTypeName" column="expense_type_name" jdbcType="VARCHAR"/>
        <result property="standCode" column="stand_code" jdbcType="VARCHAR"/>
        <result property="standName" column="stand_name" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="rowKeyCode" column="row_key_code" jdbcType="VARCHAR"/>
        <result property="rowKeyName" column="row_key_name" jdbcType="VARCHAR"/>
        <result property="rowValueCode" column="row_value_code" jdbcType="VARCHAR"/>
        <result property="rowValueName" column="row_value_name" jdbcType="VARCHAR"/>
        <result property="columnKeyCode" column="column_key_code" jdbcType="VARCHAR"/>
        <result property="columnKeyName" column="column_key_name" jdbcType="VARCHAR"/>
        <result property="columnValueCode" column="column_value_code" jdbcType="VARCHAR"/>
        <result property="columnValueName" column="column_value_name" jdbcType="VARCHAR"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="effectiveDate" column="effective_date" jdbcType="VARCHAR"/>
        <result property="valueSource" column="value_source" jdbcType="VARCHAR"/>
        <result property="valueEditorCode" column="value_editor_code" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="expStandMap" type="com.pty.pcx.vo.bill.ExpStandVO">
        <result property="expenseTypeCode" column="expense_type_code" />
        <result property="expenseTypeName" column="expense_type_name" />
        <result property="standCode" column="stand_code" />
        <result property="standName" column="stand_name" />
        <result property="rowKeyCode" column="row_key_code" />
        <result property="rowKeyName" column="row_key_name" />
        <result property="rowValueCode" column="row_value_code" />
        <result property="rowValueName" column="row_value_name" />
        <result property="columnKeyCode" column="column_key_code" />
        <result property="columnKeyName" column="column_key_name" />
        <result property="columnValueCode" column="column_value_code" />
        <result property="columnValueName" column="column_value_name" />
        <result property="valueSource" column="value_source"/>
        <result property="valueEditorCode" column="value_editor_code"/>
        <result property="seq" column="seq"/>

        <!-- 一对一映射 -->
        <collection property="condition" ofType="com.pty.pcx.vo.bill.ExpStandConditionVO">
            <result property="condKeyCode" column="cond_key_code" />
            <result property="condKeyName" column="cond_key_name" />
            <result property="condValueCode" column="cond_value_code" />
            <result property="condValueName" column="cond_value_name" />
        </collection>

        <!-- 一对多映射 -->
        <collection property="standValue" ofType="com.pty.pcx.vo.bill.ExpStandValueVO">
            <result property="rowValueCode" column="v_row_value_code" />
            <result property="rowValueName" column="v_row_value_name" />
            <result property="columnValueCode" column="v_column_value_code" />
            <result property="columnValueName" column="v_column_value_name" />
            <result property="standValue" column="standard_value" />
        </collection>
    </resultMap>
    
    <!-- 全部字段 -->
	<sql id="allColumn">
        id, 
        expense_type_code, 
        expense_type_name, 
        stand_code, 
        stand_name, 
        seq, 
        row_key_code, 
        row_key_name, 
        row_value_code, 
        row_value_name, 
        column_key_code, 
        column_key_name, 
        column_value_code, 
        column_value_name, 
        creater, 
        create_time, 
        updater, 
        update_time, 
        is_deleted, 
        agy_code, 
        fiscal, 
        mof_div_code, 
        is_enabled,
        effective_date,
        value_source,
        value_editor_code
	</sql>
    

    <select id="selectMaxSeq" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(seq), 0) AS max_seq
        FROM pcx_stand_key
        WHERE expense_type_code = #{expenseTypeCode}
          AND agy_code = #{agyCode}
          AND fiscal = #{fiscal}
          AND mof_div_code = #{mofDivCode}
    </select>
<!--    <select id="selectByStandCode" resultMap="PcxStandKeyMap">-->
<!--        select <include refid="allColumn" />-->
<!--            from pcx_stand_key where stand_code = #{standCode} limit 1-->
<!--    </select>-->
    <select id="billExpenseStandList" resultMap="expStandMap">
        SELECT
            k.expense_type_code,
            k.expense_type_name,
            k.stand_code,
            k.stand_name,
            k.row_key_code,
            k.row_key_name,
            k.column_key_code,
            k.column_key_name,
            k.seq,
            k.value_source,
            k.value_editor_code,
            v.row_value_code as v_row_value_code,
            v.row_value_name as v_row_value_name,
            v.column_value_code as v_column_value_code,
            v.column_value_name as v_column_value_name,
            v.standard_value ,
            c.cond_key_code,
            c.cond_key_name,
            c.cond_value_code,
            c.cond_value_name
        FROM
            pcx_stand_key k
        left join pcx_stand_condition c on k.stand_code = c.stand_code and k.fiscal =c.fiscal and k.agy_code = c.agy_code and k.mof_div_code = c.mof_div_code
        LEFT JOIN pcx_stand_value v ON k.stand_code = v.stand_code and k.fiscal =v.fiscal and k.agy_code = v.agy_code and k.mof_div_code = v.mof_div_code
        WHERE
            k.fiscal = #{fiscal}
          AND k.agy_code = #{agyCode}
          AND k.mof_div_code = #{mofDivCode}
          AND k.expense_type_code IN <foreach collection="expenseTypeCodes" item="expenseTypeCode" open="(" separator="," close=")">
         #{expenseTypeCode}
        </foreach>
        order by k.seq asc
    </select>

    <select id="selectListByAgyCode" resultType="java.lang.String"
            parameterType="com.pty.pcx.entity.stand.PcxStandKey">
        select distinct row_value_code as rowValueCode
        from pcx_stand_key
        <where>
            <if test="rowKeyCode != null and rowKeyCode != ''">
                and row_key_code = #{rowKeyCode}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
        </where>
    </select>

    <update id="updateByStandCode">
        update pcx_stand_key
        <set>
            <trim suffixOverrides=",">
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                expense_type_code = #{expenseTypeCode},
            </if>
            <if test="expenseTypeName != null and expenseTypeName != ''">
                expense_type_name = #{expenseTypeName},
            </if>
            <if test="standName != null and standName != ''">
                stand_name = #{standName},
            </if>
            <if test="seq != null">
                seq = #{seq},
            </if>
            <if test="rowKeyCode != null and rowKeyCode != ''">
                row_key_code = #{rowKeyCode},
            </if>
            <if test="rowKeyName != null and rowKeyName != ''">
                row_key_name = #{rowKeyName},
            </if>
            <if test="rowValueCode != null and rowValueCode != ''">
                row_value_code = #{rowValueCode},
            </if>
            <if test="rowValueName != null and rowValueName != ''">
                row_value_name = #{rowValueName},
            </if>
            <if test="columnKeyCode != null and columnKeyCode != ''">
                column_key_code = #{columnKeyCode},
            </if>
            <if test="columnKeyName != null and columnKeyName != ''">
                column_key_name = #{columnKeyName},
            </if>
            <if test="columnValueCode != null and columnValueCode != ''">
                column_value_code = #{columnValueCode},
            </if>
            <if test="columnValueName != null and columnValueName != ''">
                column_value_name = #{columnValueName},
            </if>
            <if test="creater != null and creater != ''">
                creater = #{creater},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code = #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal = #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code = #{mofDivCode},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled},
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                effective_date = #{effectiveDate},
            </if>
            <if test="valueSource != null and valueSource != ''">
                value_source = #{valueSource},
            </if>
            <if test="valueEditorCode != null and valueEditorCode != ''">
                value_editor_code = #{valueEditorCode},
            </if>
            </trim>
        </set>
        where stand_code = #{standCode}
        and agy_code = #{agyCode}
        and fiscal = #{fiscal}
        and mof_div_code = #{mofDivCode}
    </update>

    <update id="updateByAgyCodeAndRowKeyCode" parameterType="com.pty.pcx.entity.stand.PcxStandKey">
        update pcx_stand_key
        set row_value_code = #{rowValueCode},
            row_value_name = #{rowValueName},
            update_time = #{updateTime}
        where agy_code = #{agyCode}
          and fiscal = #{fiscal}
          and mof_div_code = #{mofDivCode}
          and row_key_code = #{rowKeyCode}
    </update>


    <delete id="deleteStandKeyByStandCode">
        DELETE FROM pcx_stand_key
        WHERE stand_code = #{standCode}
          AND agy_code = #{agyCode}
          AND fiscal = #{fiscal}
          AND mof_div_code = #{mofDivCode}
    </delete>

</mapper>


