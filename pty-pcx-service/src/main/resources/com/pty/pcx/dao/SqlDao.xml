<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.SqlDao">


    <!-- 执行select list语句 -->
    <select id="queryListBySql" parameterType="java.lang.String" resultType="map">
        ${sqlStr}
    </select>

    <!-- 执行select form语句 -->
    <select id="queryFormData" parameterType="java.lang.String" resultType="map">
        ${sqlStr}
    </select>

    <!-- 执行insert语句 -->
    <insert id="executeInsertSQL" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="id"  keyColumn="id">
        ${execute_sql_string}
    </insert>

    <update id="executeUpdateSQL" parameterType="java.util.Map">
        ${execute_sql_string}
    </update>


</mapper>
