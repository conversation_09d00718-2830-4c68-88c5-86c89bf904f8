<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.treasurypay.change.PcxChangeBillDao">
    <sql id="allColumn">
        agy_code, bill_id, bill_status, change_bill_no,
        change_bill_status,change_date, change_reason, change_type,
        correction_desc,created_time, creator_code, creator_name,
        fiscal, id, modified_time, modifier,
        modifier_name,mof_div_code, source_reason, receiver
    </sql>


    <sql id="allColumnAlias">
        agy_code as agyCode, bill_id as billId, change_bill_no as changeBillNo, change_bill_status as changeBillStatus,
        change_date as changeDate, change_reason as changeReason, change_type as changeType, correction_desc as correctionDesc,
        created_time as createdTime, creator_code as creatorCode, creator_name as creator<PERSON><PERSON>, fiscal as fiscal,
        id as id, modified_time as modifiedTime, modifier as modifier, modifier_name as modifierName,
        mof_div_code as mofDivCode, source_reason as sourceReason, bill_status as billStatus
    </sql>


    <sql id="allColumnValue">
        #{agyCode,jdbcType=VARCHAR},            #{billId,jdbcType=VARCHAR},             #{billStatus,jdbcType=VARCHAR},         #{changeBillNo,jdbcType=VARCHAR},
        #{changeBillStatus,jdbcType=VARCHAR},   #{changeDate,jdbcType=VARCHAR},         #{changeReason,jdbcType=VARCHAR},       #{changeType,jdbcType=VARCHAR},
        #{correctionDesc,jdbcType=VARCHAR},     #{createdTime,jdbcType=VARCHAR},        #{creatorCode,jdbcType=VARCHAR},        #{creatorName,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},             #{id,jdbcType=VARCHAR},                 #{modifiedTime,jdbcType=VARCHAR},       #{modifier,jdbcType=VARCHAR},
        #{modifierName,jdbcType=VARCHAR},       #{mofDivCode,jdbcType=VARCHAR},         #{sourceReason,jdbcType=VARCHAR},
        #{receiver,jdbcType=VARCHAR}
    </sql>

    <sql id="batchAllColumnValue">
        #{item.agyCode,jdbcType=VARCHAR},         #{item.billId,jdbcType=VARCHAR},        #{item.billStatus,jdbcType=VARCHAR},    #{item.changeBillNo,jdbcType=VARCHAR},
        #{item.changeBillStatus,jdbcType=VARCHAR},#{item.changeDate,jdbcType=VARCHAR},    #{item.changeReason,jdbcType=VARCHAR},  #{item.changeType,jdbcType=VARCHAR},
        #{item.correctionDesc,jdbcType=VARCHAR},  #{item.createdTime,jdbcType=VARCHAR},   #{item.creatorCode,jdbcType=VARCHAR},   #{item.creatorName,jdbcType=VARCHAR},
        #{item.fiscal,jdbcType=VARCHAR},          #{item.id,jdbcType=VARCHAR},            #{item.modifiedTime,jdbcType=VARCHAR},  #{item.modifier,jdbcType=VARCHAR},
        #{item.modifierName,jdbcType=VARCHAR},    #{item.mofDivCode,jdbcType=VARCHAR},    #{item.sourceReason,jdbcType=VARCHAR},
        #{item.receiver,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">agy_code=#{agyCode,jdbcType=VARCHAR},</if>
            <if test="billId != null and billId != ''">bill_id=#{billId,jdbcType=VARCHAR},</if>
            <if test="billStatus != null and billStatus != ''">bill_status=#{billStatus,jdbcType=VARCHAR},</if>
            <if test="changeBillNo != null and changeBillNo != ''">change_bill_no=#{changeBillNo,jdbcType=VARCHAR},</if>
            <if test="changeBillStatus != null and changeBillStatus != ''">change_bill_status=#{changeBillStatus,jdbcType=VARCHAR},</if>
            <if test="changeDate != null and changeDate != ''">change_date=#{changeDate,jdbcType=VARCHAR},</if>
            <if test="changeReason != null and changeReason != ''">change_reason=#{changeReason,jdbcType=VARCHAR},</if>
            <if test="changeType != null and changeType != ''">change_type=#{changeType,jdbcType=VARCHAR},</if>
            <if test="correctionDesc != null and correctionDesc != ''">correction_desc=#{correctionDesc,jdbcType=VARCHAR},</if>
            <if test="createdTime != null and createdTime != ''">created_time=#{createdTime,jdbcType=VARCHAR},</if>
            <if test="creatorCode != null and creatorCode != ''">creator_code=#{creatorCode,jdbcType=VARCHAR},</if>
            <if test="creatorName != null and creatorName != ''">creator_name=#{creatorName,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''">fiscal=#{fiscal,jdbcType=VARCHAR},</if>
            <if test="modifiedTime != null and modifiedTime != ''">modified_time=#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="modifier != null and modifier != ''">modifier=#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null and modifierName != ''">modifier_name=#{modifierName,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code=#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="sourceReason != null and sourceReason != ''">source_reason=#{sourceReason,jdbcType=VARCHAR},</if>
            <if test="receiver != null and receiver != ''">receiver=#{receiver,jdbcType=VARCHAR},</if>
        </trim>
    </sql>


    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">agy_code,</if>
            <if test="billId != null and billId != ''">bill_id,</if>
            <if test="billStatus != null and billStatus != ''">bill_status,</if>
            <if test="changeBillNo != null and changeBillNo != ''">change_bill_no,</if>
            <if test="changeBillStatus != null and changeBillStatus != ''">change_bill_status,</if>
            <if test="changeDate != null and changeDate != ''">change_date,</if>
            <if test="changeReason != null and changeReason != ''">change_reason,</if>
            <if test="changeType != null and changeType != ''">change_type,</if>
            <if test="correctionDesc != null and correctionDesc != ''">correction_desc,</if>
            <if test="createdTime != null and createdTime != ''">created_time,</if>
            <if test="creatorCode != null and creatorCode != ''">creator_code,</if>
            <if test="creatorName != null and creatorName != ''">creator_name,</if>
            <if test="fiscal != null and fiscal != ''">fiscal,</if>
            <if test="id != null and id != ''">id,</if>
            <if test="modifiedTime != null and modifiedTime != ''">modified_time,</if>
            <if test="modifier != null and modifier != ''">modifier,</if>
            <if test="modifierName != null and modifierName != ''">modifier_name,</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code,</if>
            <if test="sourceReason != null and sourceReason != ''">source_reason,</if>
            <if test="receiver != null and receiver != ''">receiver,</if>
        </trim>
    </sql>

    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">#{agyCode,jdbcType=VARCHAR},</if>
            <if test="billId != null and billId != ''">#{billId,jdbcType=VARCHAR},</if>
            <if test="billStatus != null and billStatus != ''">#{billStatus,jdbcType=VARCHAR},</if>
            <if test="changeBillNo != null and changeBillNo != ''">#{changeBillNo,jdbcType=VARCHAR},</if>
            <if test="changeBillStatus != null">#{changeBillStatus,jdbcType=INTEGER},</if>
            <if test="changeDate != null and changeDate != ''">#{changeDate,jdbcType=VARCHAR},</if>
            <if test="changeReason != null and changeReason != ''">#{changeReason,jdbcType=VARCHAR},</if>
            <if test="changeType != null and changeType != ''">#{changeType,jdbcType=VARCHAR},</if>
            <if test="correctionDesc != null and correctionDesc != ''">#{correctionDesc,jdbcType=VARCHAR},</if>
            <if test="createdTime != null and createdTime != ''">#{createdTime,jdbcType=VARCHAR},</if>
            <if test="creatorCode != null and creatorCode != ''">#{creatorCode,jdbcType=VARCHAR},</if>
            <if test="creatorName != null and creatorName != ''">#{creatorName,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''">#{fiscal,jdbcType=VARCHAR},</if>
            <if test="id != null and id != ''">#{id,jdbcType=VARCHAR},</if>
            <if test="modifiedTime != null and modifiedTime != ''">#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="modifier != null and modifier != ''">#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null and modifierName != ''">#{modifierName,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''">#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="sourceReason != null and sourceReason != ''">#{sourceReason,jdbcType=VARCHAR},</if>
            <if test="receiver != null and receiver != ''">#{receiver,jdbcType=VARCHAR},</if>
        </trim>
    </sql>

    <sql id="updateSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">agy_code,</if>
            <if test="billId != null and billId != ''">bill_id,</if>
            <if test="billStatus != null and billStatus != ''">bill_status,</if>
            <if test="changeBillNo != null and changeBillNo != ''">change_bill_no,</if>
            <if test="changeBillStatus != null and changeBillStatus != ''">change_bill_status,</if>
            <if test="changeDate != null and changeDate != ''">change_date,</if>
            <if test="changeReason != null and changeReason != ''">change_reason,</if>
            <if test="changeType != null and changeType != ''">change_type,</if>
            <if test="correctionDesc != null and correctionDesc != ''">correction_desc,</if>
            <if test="createdTime != null and createdTime != ''">created_time,</if>
            <if test="creatorCode != null and creatorCode != ''">creator_code,</if>
            <if test="creatorName != null and creatorName != ''">creator_name,</if>
            <if test="fiscal != null and fiscal != ''">fiscal,</if>
            <if test="modifiedTime != null and modifiedTime != ''">modified_time,</if>
            <if test="modifier != null and modifier != ''">modifier,</if>
            <if test="modifierName != null and modifierName != ''">modifier_name,</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code,</if>
            <if test="sourceReason != null and sourceReason != ''">source_reason,</if>
            <if test="receiver != null and receiver != ''">receiver,</if>
        </trim>
    </sql>


    <sql id="allColumnCond">
        <if test="agyCode != null and agyCode != ''">AND agy_code=#{agyCode,jdbcType=VARCHAR}</if>
        <if test="billId != null and billId != ''">AND bill_id=#{billId,jdbcType=VARCHAR}</if>
        <if test="billStatus != null and billStatus != ''">AND bill_status=#{billStatus,jdbcType=VARCHAR}</if>
        <if test="changeBillNo != null and changeBillNo != ''">AND change_bill_no=#{changeBillNo,jdbcType=VARCHAR}</if>
        <if test="changeBillStatus != null and changeBillStatus != ''">AND change_bill_status=#{changeBillStatus,jdbcType=VARCHAR}</if>
        <if test="changeDate != null and changeDate != ''">AND change_date=#{changeDate,jdbcType=VARCHAR}</if>
        <if test="changeReason != null and changeReason != ''">AND change_reason=#{changeReason,jdbcType=VARCHAR}</if>
        <if test="changeType != null and changeType != ''">AND change_type=#{changeType,jdbcType=VARCHAR}</if>
        <if test="correctionDesc != null and correctionDesc != ''">AND correction_desc=#{correctionDesc,jdbcType=VARCHAR}</if>
        <if test="createdTime != null and createdTime != ''">AND created_time=#{createdTime,jdbcType=VARCHAR}</if>
        <if test="creatorCode != null and creatorCode != ''">AND creator_code=#{creatorCode,jdbcType=VARCHAR}</if>
        <if test="creatorName != null and creatorName != ''">AND creator_name=#{creatorName,jdbcType=VARCHAR}</if>
        <if test="fiscal != null and fiscal != ''">AND fiscal=#{fiscal,jdbcType=VARCHAR}</if>
        <if test="id != null and id != ''">AND id=#{id,jdbcType=VARCHAR}</if>
        <if test="modifiedTime != null and modifiedTime != ''">AND modified_time=#{modifiedTime,jdbcType=VARCHAR}</if>
        <if test="modifier != null and modifier != ''">AND modifier=#{modifier,jdbcType=VARCHAR}</if>
        <if test="modifierName != null and modifierName != ''">AND modifier_name=#{modifierName,jdbcType=VARCHAR}</if>
        <if test="mofDivCode != null and mofDivCode != ''">AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}</if>
        <if test="sourceReason != null and sourceReason != ''">AND source_reason=#{sourceReason,jdbcType=VARCHAR}</if>
        <if test="receiver != null and receiver != ''">AND receiver=#{receiver,jdbcType=VARCHAR}</if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill">
        INSERT INTO pcx_change_bill (
        <include refid="allColumn" />
        ) VALUES (
        <include refid="allColumnValue" />
        )
    </insert>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_change_bill
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill">
        DELETE FROM pcx_change_bill
        WHERE 1=1
        <include refid="allColumnCond" />
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill">
        UPDATE pcx_change_bill SET
        <include refid="allColumnSet" />
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill
        WHERE id=#{value}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByBillIds" parameterType="java.util.List" resultType="com.pty.pcx.vo.treasurypay.change.PcxChangeBillVO">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_change_bill
        WHERE bill_Id IN
        <foreach collection="list" index="index" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        <if test="changeBillStatus != null and changeBillStatus != ''">
          AND change_bill_status=#{changeBillStatus,jdbcType=VARCHAR}
        </if>
        <if test="changeBillStatus == null or changeBillStatus == ''">
            AND change_bill_status != '3'
        </if>
    </select>

    <select id="getCorrectionDesc" resultType="String">
        SELECT
        correction_desc as correctionDesc
        FROM pcx_change_bill
        WHERE 1=1
        AND agy_code=#{agyCode}
        AND mof_div_code=#{mofDivCode}
        AND creator_code=#{userCode}
    </select>

    <select id="select" parameterType="com.pty.pcx.qo.treasurypay.change.PcxChangeBillQO" resultType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBill" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_change_bill
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>
</mapper>