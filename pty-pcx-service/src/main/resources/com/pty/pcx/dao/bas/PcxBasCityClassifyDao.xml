<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasCityClassifyDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.bas.PcxBasCityClassify" id="PcxBasCityClassifyMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="classifyCode" column="classify_code" jdbcType="VARCHAR"/>
        <result property="classifyName" column="classify_name" jdbcType="VARCHAR"/>
        <result property="dataCode" column="data_code" jdbcType="VARCHAR"/>
        <result property="dataName" column="data_name" jdbcType="VARCHAR"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="classifyType" column="classify_type" jdbcType="INTEGER"/>
        <result property="expenseTypeCode" column="expense_type_code" jdbcType="VARCHAR"/>
        <result property="peakDateJson" column="peak_date_json" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 全部字段 -->
	<sql id="allColumn">
        id,
        classify_code,
        classify_name,
        data_code,
        data_name,
        creater,
        create_time,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        peak_date_json
	</sql>

    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxBasCityClassifyMap">
        select
          <include refid="allColumn" />
        from pcx_bas_classify
        where id = #{id}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxBasCityClassifyMap">
        select
        <include refid="allColumn" />
        from pcx_bas_classify
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="classifyCode != null and classifyCode != ''">
                and classify_code = #{classifyCode}
            </if>
            <if test="classifyName != null and classifyName != ''">
                and classify_name = #{classifyName}
            </if>
            <if test="dataCode != null and dataCode != ''">
                and data_code = #{dataCode}
            </if>
            <if test="dataName != null and dataName != ''">
                and data_name = #{dataName}
            </if>
            <if test="creater != null and creater != ''">
                and creater = #{creater}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
            <if test="classifyType != null and classifyType != ''">
                and classify_type = #{classifyType}
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                and expense_type_code = #{expenseTypeCode}
            </if>
        </where>
    </select>
    <select id="selectAllCity" resultType="com.pty.pcx.qo.bas.DataQO">
        select area_code as dataCode, area_name as dataName, full_name as fullName, parent_code as parentCode from mad_area where LENGTH(area_code) &lt; 6 and LENGTH(area_code) &gt; 3 order by area_code
    </select>

    <select id="selectAllProvince" resultType="com.pty.pcx.qo.bas.DataQO">
        select area_code as dataCode, area_name as dataName, full_name as fullName, parent_code as parentCode from mad_area where LENGTH(area_code) &lt; 4 order by area_code
    </select>
    <select id="selectCityClassify" resultMap="PcxBasCityClassifyMap">
        select distinct classify_code, classify_name
            from pcx_bas_classify
    </select>

    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_bas_classify (
			<if test="classifyCode != null and classifyCode != ''">
				classify_code,
			</if>
			<if test="classifyName != null and classifyName != ''">
				classify_name,
			</if>
			<if test="dataCode != null and dataCode != ''">
				data_code,
			</if>
			<if test="dataName != null and dataName != ''">
				data_name,
			</if>
			<if test="creater != null and creater != ''">
				creater,
			</if>
			<if test="createTime != null and createTime != ''">
				create_time,
			</if>
			<if test="agyCode != null and agyCode != ''">
				agy_code,
			</if>
			<if test="fiscal != null and fiscal != ''">
				fiscal,
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				mof_div_code,
			</if>
        <if test="classifyType != null">
            classify_type,
        </if>
        <if test="expenseTypeCode != null and expenseTypeCode != ''">
            expense_type_code,
        </if>
        )
        values (
			<if test="classifyCode != null and classifyCode != ''">
				#{classifyCode},
			</if>
			<if test="classifyName != null and classifyName != ''">
				#{classifyName},
			</if>
			<if test="dataCode != null and dataCode != ''">
				#{dataCode},
			</if>
			<if test="dataName != null and dataName != ''">
				#{dataName},
			</if>
			<if test="creater != null and creater != ''">
				#{creater},
			</if>
			<if test="createTime != null and createTime != ''">
				#{createTime},
			</if>
			<if test="agyCode != null and agyCode != ''">
				#{agyCode},
			</if>
			<if test="fiscal != null and fiscal != ''">
				#{fiscal},
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				#{mofDivCode},
			</if>
        <if test="classifyType != null">
            #{classifyType},
        </if>
        <if test="expenseTypeCode != null and expenseTypeCode != ''">
            #{expenseTypeCode},
        </if>
        )
    </insert>
    <insert id="batchInsert" databaseId="mysql">
        insert into pcx_bas_classify (
        id,
        classify_code,
        classify_name,
        data_code,
        data_name,
        parent_code,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        create_time,
        peak_date_json
        )
        values
        <foreach collection="list" item="item" separator=",">
			(
				#{item.id},
				#{item.classifyCode},
				#{item.classifyName},
				#{item.dataCode},
				#{item.dataName},
                #{item.parentCode},
				#{item.agyCode},
				#{item.fiscal},
                #{item.mofDivCode},
                #{item.classifyType},
                #{item.expenseTypeCode},
                #{item.createTime},
                #{item.peakDateJson}

			)
        </foreach>
    </insert>
    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_classify (
        id,
        classify_code,
        classify_name,
        data_code,
        data_name,
        parent_code,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        create_time,
        peak_date_json
        )
        values
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.id},
            #{item.classifyCode},
            #{item.classifyName},
            #{item.dataCode},
            #{item.dataName},
            #{item.parentCode},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode},
            #{item.classifyType},
            #{item.expenseTypeCode},
            #{item.createTime},
            #{item.peakDateJson}
            from dual
        </foreach>
    </insert>

    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_classify (
        id,
        classify_code,
        classify_name,
        data_code,
        data_name,
        parent_code,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        create_time,
        peak_date_json
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.id},
            #{item.classifyCode},
            #{item.classifyName},
            #{item.dataCode},
            #{item.dataName},
            #{item.parentCode},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode},
            #{item.classifyType},
            #{item.expenseTypeCode},
            #{item.createTime},
            #{item.peakDateJson}
            from dual
        </foreach>
    </insert>

    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_classify (
        id,
        classify_code,
        classify_name,
        data_code,
        data_name,
        parent_code,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        create_time,
        peak_date_json
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.id},
            #{item.classifyCode},
            #{item.classifyName},
            #{item.dataCode},
            #{item.dataName},
            #{item.parentCode},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode},
            #{item.classifyType},
            #{item.expenseTypeCode},
            #{item.createTime},
            #{item.peakDateJson}
            from dual
        </foreach>
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_bas_classify
        <set>
            <if test="classifyCode != null and classifyCode != ''">
                classify_code = #{classifyCode},
            </if>
            <if test="classifyName != null and classifyName != ''">
                classify_name = #{classifyName},
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code = #{cityCode},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName},
            </if>
            <if test="creater != null and creater != ''">
                creater = #{creater},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code = #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal = #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code = #{mofDivCode},
            </if>
            <if test="classifyType != null">
                classify_type = #{classifyType},
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                expense_type_code = #{expenseTypeCode},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete from pcx_bas_classify
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
        </where>
    </delete>
    <delete id="deleteClassify">
        delete from pcx_bas_classify
        <where>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
            <if test="classifyType != null and classifyType != ''">
                and classify_type = #{classifyType}
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                and expense_type_code = #{expenseTypeCode}
            </if>
        </where>
    </delete>

    <select id="selectCityPeakList" resultMap="PcxBasCityClassifyMap">
        select
        <include refid="allColumn" />
        from pcx_bas_classify
        <where>
            <if test="classifyType != null and classifyType != ''">
                and classify_type = #{classifyType}
            </if>
            <if test="dataCodes != null">
                and data_code in
                <foreach collection="dataCodes" item="dataCode" open="(" close=")" separator=",">
                    #{dataCode}
                </foreach>
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>

        </where>
    </select>

    <select id="selectCityPeak" resultType="com.pty.pcx.entity.bas.PcxBasCityClassify" databaseId="mysql">
        select CONCAT(GROUP_CONCAT(t.data_name)) data_name ,t.peak_date_json from pcx_bas_classify t where t.classify_code = #{classifyCode} group by t.classify_code,t.peak_date_json
    </select>
    <select id="selectCityPeak" resultType="com.pty.pcx.entity.bas.PcxBasCityClassify" databaseId="oracle">
        SELECT
            LISTAGG(t.data_name, ',') WITHIN GROUP (ORDER BY t.data_name) AS data_name,
            t.peak_date_json
        FROM pcx_bas_classify t
        WHERE t.classify_code = #{classifyCode}
        GROUP BY t.classify_code, t.peak_date_json
    </select>

    <select id="selectCityPeak" resultType="com.pty.pcx.entity.bas.PcxBasCityClassify" databaseId="oracle">
        SELECT
            LISTAGG(t.data_name, ',') WITHIN GROUP (ORDER BY t.data_name) AS data_name,
            t.peak_date_json
        FROM pcx_bas_classify t
        WHERE t.classify_code = #{classifyCode}
        GROUP BY t.classify_code, t.peak_date_json
    </select>

    <select id="selectCityPeak" resultType="com.pty.pcx.entity.bas.PcxBasCityClassify" databaseId="oracle">
        SELECT
            LISTAGG(t.data_name, ',') WITHIN GROUP (ORDER BY t.data_name) AS data_name,
            t.peak_date_json
        FROM pcx_bas_classify t
        WHERE t.classify_code = #{classifyCode}
        GROUP BY t.classify_code, t.peak_date_json
    </select>

</mapper>


