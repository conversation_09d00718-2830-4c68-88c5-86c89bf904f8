<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillDao">


    <!-- 定义PcxBill的ResultMap -->
    <resultMap id="PcxBillResultMap" type="com.pty.pcx.entity.bill.PcxBill">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="claimant_code" property="claimantCode" jdbcType="VARCHAR"/>
        <result column="claimant_user_code" property="claimantUserCode" jdbcType="VARCHAR"/>
        <result column="claimant_name" property="claimantName" jdbcType="VARCHAR"/>
        <result column="department_code" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="trans_date" property="transDate" jdbcType="VARCHAR"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="VARCHAR"/>
        <result column="audit_code" property="auditCode" jdbcType="VARCHAR"/>
        <result column="audit_name" property="auditName" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="VARCHAR"/>
        <result column="pay_status" property="payStatus" jdbcType="VARCHAR"/>
        <result column="bill_status" property="billStatus" jdbcType="VARCHAR"/>
        <result column="approve_status" property="approveStatus" jdbcType="VARCHAR"/>
        <result column="compared_status" property="comparedStatus" jdbcType="VARCHAR"/>
        <result column="is_vou" property="isVou" jdbcType="INTEGER"/>
        <result column="vou_id" property="vouId" jdbcType="VARCHAR"/>
        <result column="vou_date" property="vouDate" jdbcType="VARCHAR"/>
        <result column="vou_no" property="vouNo" jdbcType="VARCHAR"/>
        <result column="bill_func_code" property="billFuncCode" jdbcType="VARCHAR"/>
        <result column="bill_func_name" property="billFuncName" jdbcType="VARCHAR"/>
        <result column="input_amt" property="inputAmt" jdbcType="DOUBLE"/>
        <result column="check_amt" property="checkAmt" jdbcType="DOUBLE"/>
        <result column="settlement_amt" property="settlementAmt" jdbcType="DOUBLE"/>
        <result column="loan_amt" property="loanAmt" jdbcType="DOUBLE"/>
        <result column="repay_date" property="repayDate" jdbcType="VARCHAR"/>
        <result column="biz_type" property="bizType" jdbcType="INTEGER"/>
        <result column="biz_type_name" property="bizTypeName" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modifier_name" property="modifierName" jdbcType="VARCHAR"/>
        <result column="modified_time" property="modifiedTime" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="agy_name" property="agyName" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
        <result column="expense_codes" property="expenseCodes" jdbcType="VARCHAR"/>
        <result column="expense_names" property="expenseNames" jdbcType="VARCHAR"/>
        <result column="exp_department_codes" property="expDepartmentCodes" jdbcType="VARCHAR"/>
        <result column="exp_department_names" property="expDepartmentNames" jdbcType="VARCHAR"/>
        <result column="project_codes" property="projectCodes" jdbcType="VARCHAR"/>
        <result column="project_names" property="projectNames" jdbcType="VARCHAR"/>
        <result column="fundtype_codes" property="fundtypeCodes" jdbcType="VARCHAR"/>
        <result column="fundtype_names" property="fundtypeNames" jdbcType="VARCHAR"/>
        <result column="completed_status" property="completedStatus" jdbcType="VARCHAR"/>
        <result column="completed_time" property="completedTime" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="PcxBillWithHistoryResultMap" type="com.pty.pcx.entity.bill.PcxBill" extends="PcxBillResultMap">
        <result column="history" property="history" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 全部字段 -->
    <sql id="allColumn">
        t.id,
        t.bill_no,
        t.claimant_code,
        t.claimant_user_code,
        t.claimant_name,
        t.department_code,
        t.department_name,
        t.trans_date,
        t.reason,
        t.item_code,
        t.item_name,
        t.audit_time,
        t.audit_code,
        t.audit_name,
        t.pay_time,
        t.pay_status,
        t.bill_status,
        t.approve_status,
        t.compared_status,
        t.is_vou,
        t.vou_id,
        t.vou_date,
        t.vou_no,
        t.bill_func_code,
        t.bill_func_name,
        t.input_amt,
        t.check_amt,
        t.settlement_amt,
        t.loan_amt,
        t.repay_date,
        t.biz_type,
        t.biz_type_name,
        t.modifier,
        t.modifier_name,
        t.modified_time,
        t.creator,
        t.creator_name,
        t.created_time,
        t.agy_code,
        t.agy_name,
        t.fiscal,
        t.mof_div_code,
        t.expense_codes,
        t.expense_names,
        t.exp_department_codes,
        t.exp_department_names,
        t.project_codes,
        t.project_names,
        t.fundtype_codes,
        t.fundtype_names,
        t.completed_status,
        t.completed_time
    </sql>

    <!-- 定义PcxBill的ResultMap -->
    <resultMap id="PcxBillJoinVoucherResultMap" type="com.pty.pcx.vo.bill.PcxBillExpLedgerVO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="claimant_code" property="claimantCode" jdbcType="VARCHAR"/>
        <result column="claimant_user_code" property="claimantUserCode" jdbcType="VARCHAR"/>
        <result column="claimant_name" property="claimantName" jdbcType="VARCHAR"/>
        <result column="department_code" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="trans_date" property="transDate" jdbcType="VARCHAR"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="VARCHAR"/>
        <result column="audit_code" property="auditCode" jdbcType="VARCHAR"/>
        <result column="audit_name" property="auditName" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="VARCHAR"/>
        <result column="pay_status" property="payStatus" jdbcType="VARCHAR"/>
        <result column="bill_status" property="billStatus" jdbcType="VARCHAR"/>
        <result column="approve_status" property="approveStatus" jdbcType="VARCHAR"/>
        <result column="compared_status" property="comparedStatus" jdbcType="VARCHAR"/>
        <result column="is_vou" property="isVou" jdbcType="INTEGER"/>
        <result column="vou_id" property="vouId" jdbcType="VARCHAR"/>
        <result column="vou_date" property="vouDate" jdbcType="VARCHAR"/>
        <result column="vou_no" property="vouNo" jdbcType="VARCHAR"/>
        <result column="bill_func_code" property="billFuncCode" jdbcType="VARCHAR"/>
        <result column="bill_func_name" property="billFuncName" jdbcType="VARCHAR"/>
        <result column="input_amt" property="inputAmt" jdbcType="DOUBLE"/>
        <result column="check_amt" property="checkAmt" jdbcType="DOUBLE"/>
        <result column="settlement_amt" property="settlementAmt" jdbcType="DOUBLE"/>
        <result column="loan_amt" property="loanAmt" jdbcType="DOUBLE"/>
        <result column="repay_date" property="repayDate" jdbcType="VARCHAR"/>
        <result column="biz_type" property="bizType" jdbcType="INTEGER"/>
        <result column="biz_type_name" property="bizTypeName" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modifier_name" property="modifierName" jdbcType="VARCHAR"/>
        <result column="modified_time" property="modifiedTime" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="agy_name" property="agyName" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
        <result column="expense_codes" property="expenseCodes" jdbcType="VARCHAR"/>
        <result column="expense_names" property="expenseNames" jdbcType="VARCHAR"/>
        <result column="exp_department_codes" property="expDepartmentCodes" jdbcType="VARCHAR"/>
        <result column="exp_department_names" property="expDepartmentNames" jdbcType="VARCHAR"/>
        <result column="project_codes" property="projectCodes" jdbcType="VARCHAR"/>
        <result column="project_names" property="projectNames" jdbcType="VARCHAR"/>
        <result column="fundtype_codes" property="fundtypeCodes" jdbcType="VARCHAR"/>
        <result column="fundtype_names" property="fundtypeNames" jdbcType="VARCHAR"/>
        <result column="completed_status" property="completedStatus" jdbcType="VARCHAR"/>
        <result column="completed_time" property="completedTime" jdbcType="VARCHAR"/>
        <result column="voucher_date" property="voucherDate" jdbcType="VARCHAR"/>
        <result column="voucher_no" property="voucherNo" jdbcType="VARCHAR"/>
        <result column="voucher_word" property="voucherWord" jdbcType="VARCHAR"/>
        <result column="external_bill_no" property="externalBillNo" jdbcType="VARCHAR"/>
        <result column="payment_no" property="paymentNo" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="PcxBillRepaymentResultMap" type="com.pty.pcx.vo.bill.PcxBillRepaymentVO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="trans_date" property="transDate" jdbcType="VARCHAR"/>
        <result column="claimant_code" property="claimantCode" jdbcType="VARCHAR"/>
        <result column="claimant_user_code" property="claimantUserCode" jdbcType="VARCHAR"/>
        <result column="claimant_name" property="claimantName" jdbcType="VARCHAR"/>
        <result column="department_code" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="pay_status" property="payStatus" jdbcType="VARCHAR"/>
        <result column="bill_status" property="billStatus" jdbcType="VARCHAR"/>
        <result column="approve_status" property="approveStatus" jdbcType="VARCHAR"/>
        <result column="input_amt" property="inputAmt" jdbcType="DOUBLE"/>
        <result column="check_amt" property="checkAmt" jdbcType="DOUBLE"/>
        <result column="settlement_amt" property="settlementAmt" jdbcType="DOUBLE"/>
        <result column="loan_amt" property="loanAmt" jdbcType="DOUBLE"/>
        <result column="rel_bill_id" property="relBillId" jdbcType="VARCHAR"/>
        <result column="rel_bill_no" property="relBillNo" jdbcType="VARCHAR"/>
        <result column="rel_bill_name" property="relBillName" jdbcType="VARCHAR"/>
        <result column="balance_id" property="balanceId" jdbcType="VARCHAR"/>
        <result column="balance_no" property="balanceNo" jdbcType="VARCHAR"/>
        <result column="completed_status" property="completedStatus" jdbcType="VARCHAR"/>
        <result column="completed_time" property="completedTime" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="itemAndBillIdRelMap" type="com.pty.pcx.vo.bill.PcxBillItemRelVO">
        <result property="itemCode" column="item_code"/>
        <result property="itemName" column="item_name"/>
        <result property="id" column="id"/>
    </resultMap>
    <sql id="commonCond">
        <if test="param.ids != null and param.ids.size() > 0">
            and t.id in
            <foreach item="id" index="index" collection="param.ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="param.agyCode != null and param.agyCode != ''">
            and t.agy_code = #{param.agyCode}
        </if>
        <if test="param.agyCodeList != null and param.agyCodeList.size() > 0">
            and t.agy_code in
            <foreach item="agyCode" index="index" collection="param.agyCodeList" open="(" separator="," close=")">
                #{agyCode}
            </foreach>
        </if>
        <if test="param.fiscal != null and param.fiscal != ''">
            and t.fiscal = #{param.fiscal}
        </if>
        <if test="param.mofDivCode != null and param.mofDivCode != ''">
            and t.mof_div_code = #{param.mofDivCode}
        </if>
        <if test="param.billNo != null and param.billNo != ''">
            and t.bill_no like CONCAT('%', #{param.billNo}, '%')
        </if>
        <if test="param.claimantName != null and param.claimantName != ''">
            and t.claimant_name like CONCAT('%', #{param.claimantName}, '%')
        </if>
        <if test="param.transStartDate != null and param.transStartDate != ''">
            <![CDATA[ and t.trans_date >= #{param.transStartDate} ]]>
        </if>
        <if test="param.transEndDate != null and param.transEndDate != ''">
            <![CDATA[ and t.trans_date <= #{param.transEndDate} ]]>
        </if>
        <if test="param.departmentCode != null and param.departmentCode != ''">
            and t.department_code = #{param.departmentCode}
        </if>
        <if test="param.departmentCodeList != null and param.departmentCodeList.size() > 0">
            and t.department_code in
            <foreach item="departmentCode" index="index" collection="param.departmentCodeList" open="(" separator="," close=")">
                #{departmentCode}
            </foreach>
        </if>
        <if test="param.expDepartmentCodeList != null and param.expDepartmentCodeList.size() > 0">
            and (
            <foreach item="expDepartmentCode" index="index" collection="param.expDepartmentCodeList" open="" separator=" or " close="">
                FIND_IN_SET(#{expDepartmentCode}, t.exp_department_codes)
            </foreach>
            )
        </if>
        <if test="param.billFuncCode != null and param.billFuncCode != ''">
            and t.bill_func_code = #{param.billFuncCode}
        </if>
        <if test="param.billFuncCodes != null and param.billFuncCodes.size() > 0">
            and t.bill_func_code in
            <foreach item="billFuncCode" index="index" collection="param.billFuncCodes" open="(" separator="," close=")">
                #{billFuncCode}
            </foreach>
        </if>
        <if test="param.itemCode != null and param.itemCode != ''">
            and t.item_code = #{param.itemCode}
        </if>
        <if test="param.payStatus != null and param.payStatus != ''">
            and t.pay_status = #{param.payStatus}
        </if>
        <if test="param.billStatus != null and param.billStatus != ''">
            and t.bill_status = #{param.billStatus}
        </if>
        <if test="param.approveStatus != null and param.approveStatus != ''">
            and t.approve_status = #{param.approveStatus}
        </if>
        <if test="param.comparedStatus != null and param.comparedStatus != ''">
            and t.compared_status = #{param.comparedStatus}
        </if>
        <if test="param.dataScopeCode == '1'.toString() and param.dataScopeUserCode != null and param.dataScopeUserCode != ''">
            and t.creator = #{param.dataScopeUserCode}
        </if>
        <if test="param.dataScopeCode == '2'.toString() and param.dataScopeDeptCodes != null and param.dataScopeDeptCodes.length > 0">
            and t.department_code in
            <foreach item="departmentCode" index="index" collection="param.dataScopeDeptCodes" open="(" separator="," close=")">
                #{departmentCode}
            </foreach>
        </if>
        <if test="param.dataScopeCode == '3'.toString() and param.dataScopeAgyCodes != null and param.dataScopeAgyCodes.length > 0">
            and t.agy_code in
            <foreach item="agyCode" index="index" collection="param.dataScopeAgyCodes" open="(" separator="," close=")">
                #{agyCode}
            </foreach>
        </if>
        <if test="param.startCheckAmt != null and param.startCheckAmt != ''">
            <![CDATA[ and t.check_amt >= #{param.startCheckAmt} ]]>
        </if>
        <if test="param.endCheckAmt != null and param.endCheckAmt != ''">
            <![CDATA[ and t.check_amt <= #{param.endCheckAmt} ]]>
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            and (
            t.bill_no like concat('%', #{param.keyword}, '%')
            or t.claimant_name like concat('%', #{param.keyword}, '%')
            or t.department_name like concat('%', #{param.keyword}, '%')
            or t.reason like concat('%', #{param.keyword}, '%')
            or t.item_name like concat('%', #{param.keyword}, '%')
            or t.trans_date like concat('%', #{param.keyword}, '%')
            or t.check_amt like concat('%', #{param.keyword}, '%')
            )
        </if>
    </sql>
    <sql id="selectForIndexPageInnerQuery">
        <if test="(label == null or label.getCode() == 'APPROVE')">
            select *, 0 as history, unix_timestamp(created_time) as second_sort, 1 as undo_task, unix_timestamp(created_time) as first_sort
            from (
            select * from pcx_bill
            <where>
                <trim prefixOverrides="and">
                    <if test="param.billId != null and param.billId != ''">
                        and id = #{param.billId}
                    </if>
                    <if test="param.fiscal != null">
                        and fiscal = #{param.fiscal}
                    </if>
                    <if test="param.mofDivCode != null">
                        and mof_div_code = #{param.mofDivCode}
                    </if>
                    <if test="param.keyword != null and param.keyword != ''">
                        and (
                        claimant_name like concat('%', #{param.keyword}, '%')
                        or department_name like concat('%', #{param.keyword}, '%')
                        or reason like concat('%', #{param.keyword}, '%')
                        or item_name like concat('%', #{param.keyword}, '%')
                        or bill_func_name like concat('%', #{param.keyword}, '%')
                        )
                    </if>
                    <if test="param.billNoSuffix != null and param.billNoSuffix != ''">
                        <![CDATA[
                            and bill_no like concat('%', #{param.billNoSuffix})
                        ]]>
                    </if>
                    <if test="param.itemCode != null and param.itemCode != ''">
                        and item_code = #{param.itemCode}
                    </if>
                    <choose>
                        <when test="myApprovalBizIds != null and myApprovalBizIds.size() > 0">
                            and id in
                            <foreach collection="myApprovalBizIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            and false
                        </otherwise>
                    </choose>
                </trim>
            </where>
            order by created_time
            ) as sub_query_undo
        </if>
        <if test="label == null">
            union all
        </if>
        <if test="label == null or label.getCode() == 'EXPENSE' or label.getCode() == 'MY_APPLY' or label.getCode() == 'MY_EXPENSE' or label.getCode() == 'MY_LOAN' or label.getCode() == 'APPROVED'">
            select *, 0 as undo_task, second_sort as first_sort
            from (
            select *,
            IF(history, -unix_timestamp(created_time), unix_timestamp(created_time)) as second_sort
            from (
            select *,
            <trim prefix="(" suffix=")">
                <!-- 显示状态完结权重, 权重超过0的均为显示完结状态 -->
                <choose>
                    <when test="label != null and label.getCode() == 'APPROVED'">
                        true
                    </when>
                    <otherwise>
                        case
                            when bill_func_code = 'apply' and approve_status = '2' then true
                            when bill_func_code = 'expense' and pay_status = '2' then true
                            when bill_func_code = 'loan' and pay_status = '2' then true
                            else false
                        end
                    </otherwise>
                </choose>
            </trim>
            as history
            from pcx_bill
            <where>
                <trim prefixOverrides="and">
                    <if test="param.billId != null and param.billId != ''">
                        and id = #{param.billId}
                    </if>
                    <if test="param.fiscal != null">
                        and fiscal = #{param.fiscal}
                    </if>
                    <if test="label == null or label.getCode() != 'APPROVED' and param.agyCode != null">
                        and agy_code = #{param.agyCode}
                    </if>
                    <if test="param.mofDivCode != null">
                        and mof_div_code = #{param.mofDivCode}
                    </if>
                    <if test="param.keyword!= null and param.keyword != ''">
                        and (
                        claimant_name like concat('%', #{param.keyword}, '%')
                        or department_name like concat('%', #{param.keyword}, '%')
                        or reason like concat('%', #{param.keyword}, '%')
                        or item_name like concat('%', #{param.keyword}, '%')
                        or bill_func_name like concat('%', #{param.keyword}, '%')
                        )
                    </if>
                    <if test="label == null or label.getCode() != 'APPROVED' and param.claimantUserCode != null and param.claimantUserCode != null">
                        and (
                        claimant_user_code = #{param.claimantUserCode} or creator = #{param.claimantUserCode}
                        )
                    </if>
                    <choose>
                        <when test="label != null and label.getCode() == 'MY_APPLY'">
                            and bill_func_code = 'apply'
                        </when>
                        <when test="label != null and (label.getCode() == 'MY_EXPENSE' or label.getCode() == 'EXPENSE')">
                            and bill_func_code = 'expense'
                        </when>
                        <when test="label != null and label.getCode() == 'MY_LOAN'">
                            and bill_func_code = 'loan'
                        </when>
                    </choose>
                    <if test="label != null and label.getCode() == 'APPROVED'">
                        <choose>
                            <when test="myApprovedBizIds != null and myApprovedBizIds.size() > 0">
                                and id in
                                <foreach collection="myApprovedBizIds" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                and false
                            </otherwise>
                        </choose>
                    </if>
                    <if test="myApprovalBizIds != null and myApprovalBizIds.size() > 0">
                        and id not in
                        <foreach collection="myApprovalBizIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="param.itemCode != null and param.itemCode != ''">
                        and item_code = #{param.itemCode}
                    </if>
                </trim>
            </where>
            ORDER BY history
            ) as sub_query_history
            ) as sub_query_mine
        </if>
    </sql>

    <select id="selectPcxBillList" parameterType="com.pty.pcx.qo.bill.PcxBillExpLedgerQO" resultMap="PcxBillResultMap">
        select
        <include refid="allColumn" />
        from pcx_bill t
        where 1 = 1
        <include refid="commonCond"/>
        <if test="param.itemCodeLike != null and param.itemCodeLike != ''">
            and t.item_code like CONCAT(#{param.itemCodeLike}, '%')
        </if>
        <if test="param.sortField == 'transDate'.toString() and param.sortOrder == 'asc'.toString()">
            order by trans_date asc
        </if>
        <if test="param.sortField == 'transDate'.toString() and param.sortOrder == 'desc'.toString()">
            order by trans_date desc
        </if>
        <if test="param.sortField == 'checkAmt'.toString() and param.sortOrder == 'asc'.toString()">
            order by check_amt asc
        </if>
        <if test="param.sortField == 'checkAmt'.toString() and param.sortOrder == 'desc'.toString()">
            order by check_amt desc
        </if>
        <if test="param.sortField == 'loanAmt'.toString() and param.sortOrder == 'asc'.toString()">
            order by loan_amt asc
        </if>
        <if test="param.sortField == 'loanAmt'.toString() and param.sortOrder == 'desc'.toString()">
            order by loan_amt desc
        </if>
    </select>

    <select id="selectPcxBillJoinVoucherList" parameterType="com.pty.pcx.qo.bill.PcxBillExpLedgerQO" resultMap="PcxBillJoinVoucherResultMap">
        select
        <include refid="allColumn" />
            ,s.business_date as "voucher_date",s.voucher_no,s.voucher_word,s.external_bill_no,s.payment_no
        from pcx_bill t
        left join pcx_bill_sync s on t.id = s.bill_id
        where 1 = 1
        <include refid="commonCond"/>
        <if test="param.voucherDateStart != null and param.voucherDateStart != ''">
            <![CDATA[ and s.business_date >= #{param.voucherDateStart} ]]>
        </if>
        <if test="param.voucherDateEnd != null and param.voucherDateEnd != ''">
            <![CDATA[ and s.business_date <= #{param.voucherDateEnd} ]]>
        </if>
        <if test="param.voucherWord != null and param.voucherWord != ''">
            and s.voucher_word = #{param.voucherWord}
        </if>
        <if test="param.voucherNo != null and param.voucherNo != ''">
            and s.voucher_no like CONCAT(#{param.voucherNo}, '%')
        </if>
        <if test="param.externalBillNo != null and param.externalBillNo != ''">
            and s.external_bill_no like CONCAT(#{param.externalBillNo}, '%')
        </if>
        <if test="param.paymentNo != null and param.paymentNo != ''">
            and s.payment_no like CONCAT(#{param.paymentNo}, '%')
        </if>
        <if test="param.itemCodeLike != null and param.itemCodeLike != ''">
            and t.item_code like CONCAT(#{param.itemCodeLike}, '%')
        </if>
        <if test="param.sortField == 'transDate'.toString() and param.sortOrder == 'asc'.toString()">
            order by t.trans_date asc
        </if>
        <if test="param.sortField == 'transDate'.toString() and param.sortOrder == 'desc'.toString()">
            order by t.trans_date desc
        </if>
        <if test="param.sortField == 'checkAmt'.toString() and param.sortOrder == 'asc'.toString()">
            order by t.check_amt asc
        </if>
        <if test="param.sortField == 'checkAmt'.toString() and param.sortOrder == 'desc'.toString()">
            order by t.check_amt desc
        </if>
        <if test="param.sortField == 'loanAmt'.toString() and param.sortOrder == 'asc'.toString()">
            order by t.loan_amt asc
        </if>
        <if test="param.sortField == 'loanAmt'.toString() and param.sortOrder == 'desc'.toString()">
            order by t.loan_amt desc
        </if>
        <if test="param.sortField == 'voucherDate'.toString() and param.sortOrder == 'asc'.toString()">
            order by s.business_date asc
        </if>
        <if test="param.sortField == 'voucherDate'.toString() and param.sortOrder == 'desc'.toString()">
            order by s.business_date desc
        </if>
        <if test="param.sortField == 'voucherNo'.toString() and param.sortOrder == 'asc'.toString()">
            order by s.voucher_no asc
        </if>
        <if test="param.sortField == 'voucherNo'.toString() and param.sortOrder == 'desc'.toString()">
            order by s.voucher_no desc
        </if>
        <if test="param.sortField == 'voucherWord'.toString() and param.sortOrder == 'asc'.toString()">
            order by s.voucher_word asc
        </if>
        <if test="param.sortField == 'voucherWord'.toString() and param.sortOrder == 'desc'.toString()">
            order by s.voucher_word desc
        </if>
    </select>

    <select id="selectForIndexPage" resultMap="PcxBillWithHistoryResultMap">
        <!--        select * from (-->
        <!--            <include refid="indexPageInnerQuery"/>-->
        <!--        ) as `pb` limit #{param.start}, #{param.pageSize}-->
        select * from
        <trim prefix="(" suffix=")">
            <include refid="selectForIndexPageInnerQuery"/>
        </trim>
        as combined_query
        ORDER BY undo_task desc, history, first_sort
    </select>
    <select id="selectForIndexPageCount" resultType="java.lang.Integer">
        <!--        select count(*) from (-->
        <!--            <include refid="indexPageInnerQuery"/>-->
        <!--        ) as `pb`-->
        select count(*) from
        <trim prefix="(" suffix=")">
            <include refid="selectForIndexPageInnerQuery"/>
        </trim>
        as combined_query
    </select>
    <select id="selectNoValidBillByEmpCode" resultType="com.pty.pcx.entity.bill.PcxBill">
        select bill.*
            from pcx_bill bill
        where bill.agy_code=#{agyCode}
        and bill.fiscal = #{fiscal}
        and bill.mof_div_code = #{mofDivCode}
        and bill.claimant_code = #{empCode}
        and bill.bill_status=1
        and bill.input_amt = 0
        and bill.bill_func_code = 'expense'
        and not exists(select 1 from pcx_exp_detail_ecs_rel rel where rel.bill_id = bill.id)
        and not exists(select 1 from pcx_bill_exp_detail_travel detail where detail.bill_id = bill.id)
    </select>
    <select id="selectNoValidBill" resultType="com.pty.pcx.entity.bill.PcxBill">
        select bill.*
        from pcx_bill bill
        where bill.bill_status=1
          and bill.input_amt = 0
          and bill.bill_func_code = 'expense'
          and not exists(select 1 from pcx_exp_detail_ecs_rel rel where rel.bill_id = bill.id)
          and not exists(select 1 from pcx_bill_exp_detail_travel detail where detail.bill_id = bill.id)
    </select>

    <select id="selectPcxBillRepaymentPage" resultMap="PcxBillRepaymentResultMap">
        select t.id,t.bill_no,t.trans_date,t.claimant_code,t.claimant_name,t.claimant_user_code,t.department_code,t.department_name
             ,t.reason,t.repay_date,t.bill_status,t.input_amt,t.check_amt,t.settlement_amt,t.loan_amt,t.approve_status,t.pay_status
             ,r.rel_bill_id,r.rel_bill_no,r.rel_bill_name,t.completed_status
        from pcx_bill t
        left join pcx_bill_relation r on t.id = r.bill_id
        where t.bill_func_code = 'loan'
          and t.pay_status = '2'
        <if test="param.agyCode != null and param.agyCode != ''">
            and t.agy_code = #{param.agyCode}
        </if>
        <if test="param.fiscal != null and param.fiscal != ''">
            and t.fiscal = #{param.fiscal}
        </if>
        <if test="param.startDate != null and param.startDate != ''">
            and t.repay_date >= #{param.startDate}
        </if>
        <if test="param.mofDivCode != null and param.mofDivCode != ''">
            <![CDATA[ and t.mof_div_code = #{param.mofDivCode} ]]>
        </if>
        <if test="param.endDate != null and param.endDate != ''">
            <![CDATA[ and t.repay_date <= #{param.endDate} ]]>
        </if>
        <if test="param.isSettle != null and param.isSettle != ''">
            <if test="param.isSettle == 'unsettle'">
                and t.check_amt - t.loan_amt > 0
            </if>
            <if test="param.isSettle == 'settle'">
                <![CDATA[ and t.check_amt - t.loan_amt <= 0 ]]>
            </if>
        </if>
        <if test="param.repaymentKeyWord != null and param.repaymentKeyWord != ''">
            and (
                t.bill_no like concat('%', #{param.repaymentKeyWord}, '%')
                or t.rel_bill_no like concat('%', #{param.repaymentKeyWord}, '%')
                or t.reason like concat('%', #{param.repaymentKeyWord}, '%')
                or i.department_code like concat('%', #{param.repaymentKeyWord}, '%')
            )
        </if>
        <if test="param.isSettle != null and param.isSettle != ''">
            <if test="param.isSettle == 'unsettle'">
                order by t.trans_date asc
            </if>
            <if test="param.isSettle == 'settle'">
                order by t.trans_date desc
            </if>
        </if>


    </select>

    <select id="selectPcxBillRepaymentPageCount" resultType="java.lang.Integer">
        select count(1)
        from pcx_bill t
        where t.bill_func_code = 'loan'
            and t.pay_status = '2'
        <if test="param.agyCode != null and param.agyCode != ''">
            and t.agy_code = #{param.agyCode}
        </if>
        <if test="param.fiscal != null and param.fiscal != ''">
            and t.fiscal = #{param.fiscal}
        </if>
        <if test="param.startDate != null and param.startDate != ''">
            and t.repay_date >= #{param.startDate}
        </if>
        <if test="param.mofDivCode != null and param.mofDivCode != ''">
            <![CDATA[ and t.mof_div_code = #{param.mofDivCode} ]]>
        </if>
        <if test="param.endDate != null and param.endDate != ''">
            <![CDATA[ and t.repay_date <= #{param.endDate} ]]>
        </if>
        <if test="param.isSettle != null and param.isSettle != ''">
            <if test="param.isSettle == 'unsettle'">
                and t.check_amt - t.loan_amt > 0
            </if>
            <if test="param.isSettle == 'settle'">
                <![CDATA[ and t.check_amt - t.loan_amt <= 0 ]]>
            </if>
        </if>
        <if test="param.repaymentKeyWord != null and param.repaymentKeyWord != ''">
            and (
            t.bill_no like concat('%', #{param.repaymentKeyWord}, '%')
            or t.rel_bill_no like concat('%', #{param.repaymentKeyWord}, '%')
            or t.reason like concat('%', #{param.repaymentKeyWord}, '%')
            or i.department_code like concat('%', #{param.repaymentKeyWord}, '%')
            )
        </if>
    </select>

    <select id="selectPcxBillRepaymentListById" resultMap="PcxBillRepaymentResultMap">
        select t.id,t.bill_no,t.trans_date,t.claimant_code,t.claimant_name,t.claimant_user_code,t.department_code,t.department_name
             ,t.reason,t.repay_date,t.bill_status,t.input_amt,t.check_amt,t.settlement_amt,t.loan_amt,t.approve_status,t.pay_status
             ,r.rel_bill_id,r.rel_bill_no,r.rel_bill_name,t.completed_status
        from pcx_bill_relation r
        left join pcx_bill t on r.bill_id = t.id
        where r.rel_bill_id = #{billId}
          <if test="funcCodes != null and funcCodes.size() > 0">
            and t.bill_func_code in
            <foreach collection="funcCodes" item="funcCode" open="(" close=")" separator=",">
                #{funcCode}
            </foreach>
        </if>
    </select>

    <select id="selectCheckAmtCount" resultType="java.math.BigDecimal">
        select sum(t.check_amt)
        from pcx_bill t
        where 1 = 1
        <include refid="commonCond"/>
    </select>
    <select id="selectInvalidBillIds" resultType="java.lang.String" databaseId="mysql">
        SELECT
            b.id
        FROM
            pcx_bill b
        WHERE
            b.bill_func_code = 'expense'
          AND input_amt = 0
          AND check_amt = 0
          AND bill_status = 1
          AND NOT EXISTS ( SELECT 1 FROM pcx_bill_exp_detail_travel dt WHERE dt.bill_id = b.id )
          AND NOT EXISTS ( SELECT 1 FROM pcx_exp_detail_ecs_rel er WHERE er.bill_id = b.id )
          AND NOT EXISTS ( SELECT 1 FROM pcx_bill_contract_rel cr WHERE cr.bill_id = b.id )
        AND b.created_time &lt; DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        limit 20;
    </select>
    <select id="selectBillIdAndItem" resultMap="itemAndBillIdRelMap">
        select
            id AS id,
            item_code,
            item_name
        from pcx_bill
        <where>
            <choose>
                <when test="param.billId != null and param.billId != ''">
                    id = #{param.billId}
                </when>
                <otherwise>
                    fiscal = #{param.fiscal}
                    and mof_div_code = #{param.mofDivCode}
                    <if test="param.keyword != null and param.keyword != ''">
                        and (
                        claimant_name like concat('%', #{param.keyword}, '%')
                        or department_name like concat('%', #{param.keyword}, '%')
                        or reason like concat('%', #{param.keyword}, '%')
                        or item_name like concat('%', #{param.keyword}, '%')
                        or bill_func_name like concat('%', #{param.keyword}, '%')
                        )
                    </if>
                    <if test="param.billNoSuffix != null and param.billNoSuffix != ''">
                        <![CDATA[
                                and bill_no like concat('%', #{param.billNoSuffix})
                            ]]>
                    </if>
                    <choose>
                        <when test="myApprovalBizIds != null and myApprovalBizIds.size() > 0">
                            and id in
                            <foreach collection="myApprovalBizIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            and false
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectInvalidBillIds" resultType="java.lang.String" databaseId="oracle">
        SELECT
            b.id
        FROM
            pcx_bill b
        WHERE
            b.bill_func_code = 'expense'
          AND input_amt = 0
          AND check_amt = 0
          AND bill_status = 1
          AND NOT EXISTS ( SELECT 1 FROM pcx_bill_exp_detail_travel dt WHERE dt.bill_id = b.id )
          AND NOT EXISTS ( SELECT 1 FROM pcx_exp_detail_ecs_rel er WHERE er.bill_id = b.id )
          AND NOT EXISTS ( SELECT 1 FROM pcx_bill_contract_rel cr WHERE cr.bill_id = b.id )
          AND b.created_time &lt; DATEADD(MINUTE, -5, SYSDATE)
          AND ROWNUM &lt; 20
    </select>

    <update id="updateBillLoanAmt">
        update pcx_bill
        set <include refid="updateBillLoanAmt"/>
        where id = #{billId} and check_amt - (loan_amt + #{repayAmt}) >= 0
    </update>
    <sql id="updateBillLoanAmt">
        <trim suffixOverrides=",">
            <if test="billStatus != null">
                bill_status = #{billStatus}
            </if>
            <if test="repayAmt != null">
                loan_amt = loan_amt + #{repayAmt}
            </if>
        </trim>
    </sql>

    <update id="updateApplyBillLoanAmt" >
        update pcx_bill
        set loan_amt = loan_amt - #{loanAmt}
        where id = #{billId} and loan_amt - #{loanAmt} >= 0
    </update>
</mapper>
