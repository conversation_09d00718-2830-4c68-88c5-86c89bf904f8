<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.stand.PcxStandTaxRateDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.stand.PcxStandTaxRate" id="PcxStandTaxRateMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="minAmt" column="min_amt" jdbcType="NUMERIC"/>
        <result property="maxAmt" column="max_amt" jdbcType="NUMERIC"/>
        <result property="subExpense" column="sub_expense" jdbcType="NUMERIC"/>
        <result property="subRate" column="sub_rate" jdbcType="NUMERIC"/>
        <result property="taxRate" column="tax_rate" jdbcType="NUMERIC"/>
        <result property="subNum" column="sub_num" jdbcType="NUMERIC"/>
        <result property="rateTypeCode" column="rate_type_code" jdbcType="VARCHAR"/>
        <result property="rateTypeName" column="rate_type_name" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="effectiveDate" column="effective_date" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 全部字段 -->
    <sql id="allColumn">
        id
        ,
        min_amt,
        max_amt,
        sub_expense,
        sub_rate,
        tax_rate,
        sub_num,
        rate_type_code,
        rate_type_name,
        agy_code,
        fiscal,
        mof_div_code,
        is_enabled,
        effective_date
    </sql>

    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxStandTaxRateMap">
        select
        <include refid="allColumn"/>
        from pcx_stand_tax_rate
        where id = #{id}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxStandTaxRateMap">
        select
        <include refid="allColumn"/>
        from pcx_stand_tax_rate
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="minAmt != null">
                and min_amt = #{minAmt}
            </if>
            <if test="maxAmt != null">
                and max_amt = #{maxAmt}
            </if>
            <if test="subExpense != null">
                and sub_expense = #{subExpense}
            </if>
            <if test="subRate != null">
                and sub_rate = #{subRate}
            </if>
            <if test="taxRate != null">
                and tax_rate = #{taxRate}
            </if>
            <if test="subNum != null">
                and sub_num = #{subNum}
            </if>
            <if test="rateTypeCode != null and rateTypeCode != ''">
                and rate_type_code = #{rateTypeCode}
            </if>
            <if test="rateTypeName != null and rateTypeName != ''">
                and rate_type_name = #{rateTypeName}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
            <if test="isEnabled != null">
                and is_enabled = #{isEnabled}
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                and effective_date = #{effectiveDate}
            </if>
        </where>
    </select>

    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="minAmt != null">
                min_amt,
            </if>
            <if test="maxAmt != null">
                max_amt,
            </if>
            <if test="subExpense != null">
                sub_expense,
            </if>
            <if test="subRate != null">
                sub_rate,
            </if>
            <if test="taxRate != null">
                tax_rate,
            </if>
            <if test="subNum != null">
                sub_num,
            </if>
            <if test="rateTypeCode != null and rateTypeCode != ''">
                rate_type_code,
            </if>
            <if test="rateTypeName != null and rateTypeName != ''">
                rate_type_name,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="isEnabled != null">
                is_enabled,
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                effective_date,
            </if>
        </trim>
    </sql>
    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id},
            </if>
            <if test="minAmt != null">
                #{minAmt},
            </if>
            <if test="maxAmt != null">
                #{maxAmt},
            </if>
            <if test="subExpense != null">
                #{subExpense},
            </if>
            <if test="subRate != null">
                #{subRate},
            </if>
            <if test="taxRate != null">
                #{taxRate},
            </if>
            <if test="subNum != null">
                #{subNum},
            </if>
            <if test="rateTypeCode != null and rateTypeCode != ''">
                #{rateTypeCode},
            </if>
            <if test="rateTypeName != null and rateTypeName != ''">
                #{rateTypeName},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode},
            </if>
            <if test="isEnabled != null">
                #{isEnabled},
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                #{effectiveDate},
            </if>
        </trim>
    </sql>
    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_stand_tax_rate (
        <include refid="insertSelectiveColumn" />
        )
        values (
        <include refid="insertSelectiveValue" />
        )
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_stand_tax_rate
        <set>
            <if test="minAmt != null">
                min_amt = #{minAmt},
            </if>
            <if test="maxAmt != null">
                max_amt = #{maxAmt},
            </if>
            <if test="subExpense != null">
                sub_expense = #{subExpense},
            </if>
            <if test="subRate != null">
                sub_rate = #{subRate},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate},
            </if>
            <if test="subNum != null">
                sub_num = #{subNum},
            </if>
            <if test="rateTypeCode != null and rateTypeCode != ''">
                rate_type_code = #{rateTypeCode},
            </if>
            <if test="rateTypeName != null and rateTypeName != ''">
                rate_type_name = #{rateTypeName},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code = #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal = #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code = #{mofDivCode},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled},
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                effective_date = #{effectiveDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete
        from pcx_stand_tax_rate
        where id = #{id}
    </delete>

</mapper>

