<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasExpTypeDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.bas.PcxBasExpType" id="PcxBasExpTypeMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="expenseCode" column="expense_code" jdbcType="VARCHAR"/>
        <result property="expenseName" column="expense_name" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="lastCode" column="last_code" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="isRefine" column="is_refine" jdbcType="INTEGER"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="expendGuide" column="expend_guide" jdbcType="VARCHAR"/>
        <result property="isPlan" column="is_plan" jdbcType="INTEGER"/>
        <result property="isSystem" column="is_system" jdbcType="INTEGER"/>
        <result property="isLeaf" column="is_leaf" jdbcType="INTEGER"/>
        <result property="isCtrlBalance" column="is_ctrl_balance" jdbcType="INTEGER"/>
        <result property="applyCtrlCode" column="apply_ctrl_code" jdbcType="VARCHAR"/>
        <result property="applyCtrlName" column="apply_ctrl_name" jdbcType="VARCHAR"/>
        <result property="isApplyDetail" column="is_apply_detail" jdbcType="INTEGER"/>
        <result property="isNeedApply" column="is_need_apply" jdbcType="INTEGER"/>
        <result property="isNeedPlan" column="is_need_plan" jdbcType="INTEGER"/>
        <result property="applyCtrlLevel" column="apply_ctrl_level" jdbcType="INTEGER"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="isHasStand" column="is_has_stand" jdbcType="INTEGER"/>
        <result property="detailLevel" column="detail_level" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="com.pty.pcx.vo.PcxBasExpTypeVO" id="PcxBasExpTypeVOMap">
        <result property="expId" column="expId" jdbcType="VARCHAR"/>
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="expenseCode" column="expense_code" jdbcType="VARCHAR"/>
        <result property="expenseName" column="expense_name" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="lastCode" column="last_code" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="isRefine" column="is_refine" jdbcType="INTEGER"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="expendGuide" column="expend_guide" jdbcType="VARCHAR"/>
        <result property="isPlan" column="is_plan" jdbcType="INTEGER"/>
        <result property="isLeaf" column="is_leaf" jdbcType="INTEGER"/>
        <result property="isSystem" column="is_system" jdbcType="INTEGER"/>
        <result property="isCtrlBalance" column="is_ctrl_balance" jdbcType="INTEGER"/>
        <result property="applyCtrlCode" column="apply_ctrl_code" jdbcType="VARCHAR"/>
        <result property="applyCtrlName" column="apply_ctrl_name" jdbcType="VARCHAR"/>
        <result property="isApplyDetail" column="is_apply_detail" jdbcType="INTEGER"/>
        <result property="isNeedApply" column="is_need_apply" jdbcType="INTEGER"/>
        <result property="isNeedPlan" column="is_need_plan" jdbcType="INTEGER"/>
        <result property="applyCtrlLevel" column="apply_ctrl_level" jdbcType="INTEGER"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="isHasStand" column="is_has_stand" jdbcType="INTEGER"/>
        <result property="detailLevel" column="detail_level" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 全部字段 -->
    <sql id="allColumn">
        id, expense_code, expense_name, parent_code, last_code, agy_code, mof_div_code, fiscal,
        is_refine, is_enabled, expend_guide, is_plan, is_system, is_leaf , is_ctrl_balance, apply_ctrl_code, apply_ctrl_name, is_apply_detail,
        is_need_plan, is_need_apply, apply_ctrl_level, summary, modifier, modifier_name, modified_time, creator, creator_name, created_time,
        seq, is_has_stand, detail_level
    </sql>

    <sql id="allColumnSet">
        <trim prefix="" suffixOverrides=",">
            <if test="expenseCode != null and expenseCode != ''"> expense_code = #{expenseCode}, </if>
            <if test="expenseName != null and expenseName != ''"> expense_name = #{expenseName}, </if>
            <if test="parentCode != null and parentCode != ''"> parent_code = #{parentCode}, </if>
            <if test="lastCode != null and lastCode != ''"> last_code = #{lastCode}, </if>
            <if test="agyCode != null and agyCode != ''"> agy_code = #{agyCode}, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> mof_div_code = #{mofDivCode}, </if>
            <if test="fiscal != null and fiscal != ''"> fiscal = #{fiscal}, </if>
            <if test="isRefine != null"> is_refine = #{isRefine}, </if>
            <if test="isEnabled != null"> is_enabled = #{isEnabled}, </if>
            <if test="expendGuide != null and expendGuide != ''"> expend_guide = #{expendGuide}, </if>
            <if test="isPlan != null"> is_plan = #{isPlan}, </if>
            <if test="isSystem != null"> is_system = #{isSystem}, </if>
            <if test="isLeaf != null"> is_leaf = #{isLeaf}, </if>
            <if test="isCtrlBalance != null"> is_ctrl_balance = #{isCtrlBalance}, </if>
            <if test="applyCtrlCode != null and applyCtrlCode != ''"> apply_ctrl_code = #{applyCtrlCode}, </if>
            <if test="applyCtrlName != null and applyCtrlName != ''"> apply_ctrl_name = #{applyCtrlName}, </if>
            <if test="isApplyDetail != null"> is_apply_detail = #{isApplyDetail}, </if>
            <if test="isNeedApply != null"> is_need_apply = #{isNeedApply}, </if>
            <if test="isNeedPlan != null"> is_need_plan = #{isNeedPlan}, </if>
            <if test="applyCtrlLevel != null"> apply_ctrl_level = #{applyCtrlLevel}, </if>
            <if test="summary != null and summary != ''"> summary = #{summary}, </if>
            <if test="modifier != null and modifier != ''"> modifier = #{modifier}, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name = #{modifierName}, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time = #{modifiedTime}, </if>
            <if test="seq != null"> seq = #{seq}, </if>
            <if test="isHasStand != null"> is_has_stand = #{isHasStand}, </if>
            <if test="detailLevel != null"> detail_level = #{detailLevel}, </if>
        </trim>
    </sql>
    <!-- 条件字段 -->
    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            and id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="expenseCode != null and expenseCode != ''">
            and expense_code = #{expenseCode,jdbcType=VARCHAR}
        </if>
        <if test="expenseName != null and expenseName != ''">
            and expense_name = #{expenseName,jdbcType=VARCHAR}
        </if>
        <if test="parentCode != null and parentCode != ''">
            and parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test="lastCode != null and lastCode != ''">
            and last_code = #{lastCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            and agy_code = #{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            and fiscal = #{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="isRefine != null">
            and is_refine = #{isRefine,jdbcType=INTEGER}
        </if>
        <if test="isEnabled != null">
            and is_enabled = #{isEnabled,jdbcType=INTEGER}
        </if>
        <if test="isLeaf != null">
            and is_leaf = #{isLeaf,jdbcType=INTEGER}
        </if>
        <if test="expendGuide != null and expendGuide != ''">
            and expend_guide = #{expendGuide,jdbcType=VARCHAR}
        </if>
        <if test="isPlan != null">
            and is_plan = #{isPlan,jdbcType=INTEGER}
        </if>
        <if test="isSystem != null">
            and is_system = #{isSystem,jdbcType=INTEGER}
        </if>
        <if test="isCtrlBalance != null">
            and is_ctrl_balance = #{isCtrlBalance,jdbcType=INTEGER}
        </if>
        <if test="applyCtrlCode != null and applyCtrlCode != ''">
            and apply_ctrl_code = #{applyCtrlCode,jdbcType=VARCHAR}
        </if>
        <if test="applyCtrlName != null and applyCtrlName != ''">
            and apply_ctrl_name = #{applyCtrlName,jdbcType=VARCHAR}
        </if>
        <if test="isApplyDetail != null">
            and is_apply_detail = #{isApplyDetail,jdbcType=INTEGER}
        </if>
        <if test="isNeedApply != null">
            and is_need_apply = #{isNeedApply,jdbcType=INTEGER}
        </if>
        <if test="isNeedPlan != null">
            and is_need_plan = #{isNeedPlan,jdbcType=INTEGER}
        </if>
        <if test="applyCtrlLevel != null">
            and apply_ctrl_level = #{applyCtrlLevel,jdbcType=INTEGER}
        </if>
        <if test="summary != null and summary != ''">
            and summary = #{summary,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
            and modifier = #{modifier,jdbcType=VARCHAR}
        </if>
        <if test="modifierName != null and modifierName != ''">
            and modifier_name = #{modifierName,jdbcType=VARCHAR}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''">
            and modified_time = #{modifiedTime,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            and creator = #{creator,jdbcType=VARCHAR}
        </if>
        <if test="creatorName != null and creatorName != ''">
            and creator_name = #{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            and created_time = #{createdTime,jdbcType=VARCHAR}
        </if>
        <if test="seq != null">
            and seq = #{seq,jdbcType=INTEGER}
        </if>
        <if test="ids != null and ids.size &gt; 0">
            and id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="expTypeCodes != null and expTypeCodes.size &gt; 0">
            and expense_code in
            <foreach collection="expTypeCodes" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="expTypeNames != null and expTypeNames.size &gt; 0">
            and expense_name in
            <foreach collection="expTypeNames" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="parentCodes != null and parentCodes.size &gt; 0">
            and parent_code in
            <foreach collection="parentCodes" index="index" item="parentCode" open="(" separator="," close=")">
                #{parentCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="lastCodes != null and lastCodes.size &gt; 0">
            and last_code in
            <foreach collection="lastCodes" index="index" item="lastCode" open="(" separator="," close=")">
                #{lastCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="detailLevel != null">
            and detail_level = #{detailLevel,jdbcType=INTEGER}
        </if>
    </sql>


    <!-- 查询单个 -->
    <select id="getBaseExpByQO" resultMap="PcxBasExpTypeMap">
        select
        <include refid="allColumn" />
        from pcx_bas_exp_type
        where 1=1
        <include refid="allColumnCond"/>
    </select>

    <!-- 查询列表 -->
    <select id="selectSimpleList" resultMap="PcxBasExpTypeMap">
        select
        id, expense_code, expense_name, parent_code, last_code, agy_code, mof_div_code, fiscal,
        is_refine, is_enabled, is_plan, is_system,is_leaf, is_ctrl_balance, apply_ctrl_code, apply_ctrl_name, is_apply_detail,
        is_need_apply, is_need_plan, apply_ctrl_level, seq, is_has_stand, detail_level
        from pcx_bas_exp_type
        where 1=1
        <include refid="allColumnCond" />
        order by seq
    </select>

    <select id="getTreeData" resultMap="PcxBasExpTypeVOMap">
        select
        id, id as "expId" , expense_code, expense_name, parent_code, last_code, agy_code, mof_div_code, fiscal,
        is_refine, is_enabled, is_plan, is_system,is_leaf, is_ctrl_balance, apply_ctrl_code, apply_ctrl_name, is_apply_detail,
        is_need_apply, is_need_plan, apply_ctrl_level, seq, is_has_stand
        from pcx_bas_exp_type
        where 1=1
        <include refid="allColumnCond" />
    </select>
    <select id="selectNoBindItemExpTypeList" resultMap="PcxBasExpTypeMap">
        SELECT
            et.*
        FROM
            pcx_bas_exp_type et
        WHERE
            et.fiscal = #{fiscal}
          AND et.agy_code = #{agyCode}
          AND et.mof_div_code = #{mofDivCode}
          AND et.is_refine = 0
          AND is_leaf = 1
          AND is_enabled = 1
          AND NOT EXISTS (
            SELECT
                1
            FROM
                pcx_bas_item_exp ie
            WHERE
                ie.fiscal = et.fiscal
              AND ie.agy_code = et.agy_code
              AND ie.mof_div_code = et.mof_div_code
              AND ie.expense_code = et.expense_code
        )
    </select>
    <select id="selectNoAdditionFormsettingExpenseDetail" resultMap="PcxBasExpTypeMap" databaseId="mysql">
        SELECT
            *
        FROM
            pcx_bas_exp_type d
        WHERE
            d.is_refine = 1
          AND NOT EXISTS (
            SELECT
                1
            FROM
                pcx_bas_form_setting s
            WHERE
                ( d.fiscal, d.agy_code, d.mof_div_code, d.expense_code, d.tenant_id  ) = ( s.fiscal, s.agy_code, s.mof_div_code, s.form_code, s.tenant_id  )
              AND s.field_value = 'acitem03Code'
        )
            LIMIT 100;
    </select>

    <select id="selectNoAdditionFormsettingExpenseDetail" resultMap="PcxBasExpTypeMap" databaseId="oracle">
        SELECT
            *
        FROM
            pcx_bas_exp_type d
        WHERE
            d.is_refine = 1
          AND NOT EXISTS (
            SELECT
                1
            FROM
                pcx_bas_form_setting s
            WHERE
                ( d.fiscal, d.agy_code, d.mof_div_code, d.expense_code, d.tenant_id  ) = ( s.fiscal, s.agy_code, s.mof_div_code, s.form_code, s.tenant_id  )
              AND s.field_value = 'acitem03Code'
        )
          AND ROWNUM &lt; 100
    </select>

    <select id="selectSingleExpenseDetailCode" resultType="java.lang.String" databaseId="oracle">
        SELECT
            et.expense_code
        FROM
            pcx_bas_exp_type et
        WHERE
            et.fiscal = #{fiscal}
          AND et.agy_code = #{agyCode}
          AND et.mof_div_code = #{mofDivCode}
          AND et.parent_code = #{expenseTypeCode}
          AND et.is_refine = 1
          AND ROWNUM &lt; 1
    </select>

    <select id="selectSingleExpenseDetailCode" resultType="java.lang.String" databaseId="mysql">
        SELECT
            et.expense_code
        FROM
            pcx_bas_exp_type et
        WHERE
            et.fiscal = #{fiscal}
          AND et.agy_code = #{agyCode}
          AND et.mof_div_code = #{mofDivCode}
          AND et.parent_code = #{expenseTypeCode}
          AND et.is_refine = 1
        limit 1
    </select>

    <select id="selectCommonExpenseNoRemarkField" resultMap="PcxBasExpTypeMap" databaseId="mysql">
        SELECT
            d.*
        FROM
            pcx_bas_exp_type d
        WHERE
            d.is_refine = 1
          and d.parent_code != '30211'
          AND NOT EXISTS (
            SELECT
                1
            FROM
                pcx_bas_form_setting s
            WHERE
                ( d.fiscal, d.agy_code, d.mof_div_code, d.expense_code, d.tenant_id ) = ( s.fiscal, s.agy_code, s.mof_div_code, s.form_code, s.tenant_id )
              AND s.field_value = 'field05'
        )
            LIMIT 100;
    </select>
    <select id="selectItemExpenseTypeList" resultMap="PcxBasExpTypeMap">
        SELECT
            type.*
        FROM
            pcx_bas_exp_type type
                LEFT JOIN pcx_bas_item_exp exp ON type.fiscal = exp.fiscal
                AND type.agy_code = exp.agy_code
                AND type.mof_div_code = exp.mof_div_code
                AND type.expense_code = exp.expense_code
        WHERE
            exp.fiscal = #{fiscal}
          AND exp.agy_code = #{agyCode}
          AND exp.mof_div_code = #{mofDivCode}
          AND exp.item_code = #{itemCode}
          AND type.is_leaf = 1
          AND type.is_refine = 0
          AND type.is_enabled = 1;
    </select>

    <select id="selectCommonExpenseNoRemarkField" resultMap="PcxBasExpTypeMap" databaseId="oracle">
        SELECT
            d.*
        FROM
            pcx_bas_exp_type d
        WHERE
            d.is_refine = 1
          and d.parent_code != '30211'
          AND NOT EXISTS (
            SELECT
                1
            FROM
                pcx_bas_form_setting s
            WHERE
                ( d.fiscal, d.agy_code, d.mof_div_code, d.expense_code, d.tenant_id ) = ( s.fiscal, s.agy_code, s.mof_div_code, s.form_code, s.tenant_id )
              AND s.field_value = 'field05'
        )
        AND ROWNUM &lt; 100
    </select>

    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_bas_exp_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''"> id, </if>
            <if test="expenseCode != null and expenseCode != ''"> expense_code, </if>
            <if test="expenseName != null and expenseName != ''"> expense_name, </if>
            <if test="parentCode != null and parentCode != ''"> parent_code, </if>
            <if test="lastCode != null and lastCode != ''"> last_code, </if>
            <if test="agyCode != null and agyCode != ''"> agy_code, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> mof_div_code, </if>
            <if test="fiscal != null and fiscal != ''"> fiscal, </if>
            <if test="isRefine != null"> is_refine, </if>
            <if test="isEnabled != null"> is_enabled, </if>
            <if test="expendGuide != null and expendGuide != ''"> expend_guide, </if>
            <if test="isPlan != null"> is_plan, </if>
            <if test="isSystem != null"> is_system, </if>
            <if test="isLeaf != null"> is_leaf, </if>
            <if test="isCtrlBalance != null"> is_ctrl_balance, </if>
            <if test="applyCtrlCode != null and applyCtrlCode != ''"> apply_ctrl_code, </if>
            <if test="applyCtrlName != null and applyCtrlName != ''"> apply_ctrl_name, </if>
            <if test="isApplyDetail != null"> is_apply_detail, </if>
            <if test="isNeedApply != null"> is_need_apply, </if>
            <if test="isNeedPlan != null"> is_need_plan, </if>
            <if test="applyCtrlLevel != null"> apply_ctrl_level, </if>
            <if test="summary != null and summary != ''"> summary, </if>
            <if test="modifier != null and modifier != ''"> modifier, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time, </if>
            <if test="creator != null and creator != ''"> creator, </if>
            <if test="creatorName != null and creatorName != ''"> creator_name, </if>
            <if test="createdTime != null and createdTime != ''"> created_time, </if>
            <if test="seq != null"> seq, </if>
            <if test="isHasStand != null"> is_has_stand, </if>
            <if test="detailLevel != null"> detail_level, </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''"> #{id,jdbcType=VARCHAR}, </if>
            <if test="expenseCode != null and expenseCode != ''"> #{expenseCode,jdbcType=VARCHAR}, </if>
            <if test="expenseName != null and expenseName != ''"> #{expenseName,jdbcType=VARCHAR}, </if>
            <if test="parentCode != null and parentCode != ''"> #{parentCode,jdbcType=VARCHAR}, </if>
            <if test="lastCode != null and lastCode != ''"> #{lastCode,jdbcType=VARCHAR}, </if>
            <if test="agyCode != null and agyCode != ''"> #{agyCode,jdbcType=VARCHAR}, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> #{mofDivCode,jdbcType=VARCHAR}, </if>
            <if test="fiscal != null and fiscal != ''"> #{fiscal,jdbcType=VARCHAR}, </if>
            <if test="isRefine != null"> #{isRefine,jdbcType=INTEGER}, </if>
            <if test="isEnabled != null"> #{isEnabled,jdbcType=INTEGER}, </if>
            <if test="expendGuide != null and expendGuide != ''"> #{expendGuide,jdbcType=TEXT}, </if>
            <if test="isPlan != null"> #{isPlan,jdbcType=INTEGER}, </if>
            <if test="isSystem != null"> #{isSystem,jdbcType=INTEGER}, </if>
            <if test="isLeaf != null"> #{isLeaf,jdbcType=INTEGER}, </if>
            <if test="isCtrlBalance != null"> #{isCtrlBalance,jdbcType=INTEGER}, </if>
            <if test="applyCtrlCode != null and applyCtrlCode != ''"> #{applyCtrlCode,jdbcType=VARCHAR}, </if>
            <if test="applyCtrlName != null and applyCtrlName != ''"> #{applyCtrlName,jdbcType=VARCHAR}, </if>
            <if test="isApplyDetail != null"> #{isApplyDetail,jdbcType=INTEGER}, </if>
            <if test="isNeedApply != null"> #{isNeedApply,jdbcType=INTEGER}, </if>
            <if test="isNeedPlan != null"> #{isNeedPlan,jdbcType=INTEGER}, </if>
            <if test="applyCtrlLevel != null"> #{applyCtrlLevel,jdbcType=INTEGER}, </if>
            <if test="summary != null and summary != ''"> #{summary,jdbcType=TEXT}, </if>
            <if test="modifier != null and modifier != ''"> #{modifier,jdbcType=VARCHAR}, </if>
            <if test="modifierName != null and modifierName != ''"> #{modifierName,jdbcType=VARCHAR}, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> #{modifiedTime,jdbcType=VARCHAR}, </if>
            <if test="creator != null and creator != ''"> #{creator,jdbcType=VARCHAR}, </if>
            <if test="creatorName != null and creatorName != ''"> #{creatorName,jdbcType=VARCHAR}, </if>
            <if test="createdTime != null and createdTime != ''"> #{createdTime,jdbcType=VARCHAR}, </if>
            <if test="seq != null"> #{seq,jdbcType=INTEGER}, </if>
            <if test="isHasStand != null"> #{isHasStand,jdbcType=INTEGER}, </if>
            <if test="detailLevel != null"> #{detailLevel,jdbcType=INTEGER}, </if>
        </trim>
    </insert>

    <update id="updateByQo" parameterType="com.pty.pcx.qo.bas.PcxBasExpTypeQO">
        update pcx_bas_exp_type SET
        <include refid="allColumnSet" />
        where 1=1
        and id = #{id,jdbcType=VARCHAR}
        and agy_code = #{agyCode,jdbcType=VARCHAR}
        and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        and fiscal = #{fiscal,jdbcType=VARCHAR}
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete from pcx_bas_exp_type
        where 1=1
        <include refid="allColumnCond"/>
    </delete>

    <!-- 通过条件删除 -->
    <delete id="deleteByQO" parameterType="com.pty.pcx.qo.bas.PcxBasExpTypeQO">
        delete from pcx_bas_exp_type
        where 1=1
        <include refid="allColumnCond" />
    </delete>

    <update id="disEnableOrEnableById">
        update pcx_bas_exp_type SET
        is_enabled = #{isEnabled,jdbcType=INTEGER}
        where  id = #{id,jdbcType=VARCHAR}
            and agy_code = #{agyCode,jdbcType=VARCHAR}
            and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            and fiscal = #{fiscal,jdbcType=VARCHAR}
    </update>
</mapper>