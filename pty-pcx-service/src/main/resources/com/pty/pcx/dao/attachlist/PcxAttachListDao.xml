<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.attachlist.PcxAttachListDao">
    <sql id="allColumn">
        attach_list_id, attach_type_id, billtype_code, func_type,
        func_code, attach_list_name, remark, provide_way_code,
        provide_way_name, is_necessary, attach_format, agy_code,
        fiscal, row_num, template_name, template_id,
        category_code, upload_type, mof_div_code, relate_expense,
        seq
    </sql>

    <sql id="allColumnAlias">
        attach_list_id as attachListId, attach_type_id as attachTypeId, billtype_code as billtypeCode, func_type as funcType,
        func_code as funcCode, attach_list_name as attachListName, remark as remark, provide_way_code as provideWayCode,
        provide_way_name as provideWayName, is_necessary as isNecessary, attach_format as attachFormat, agy_code as agyCode,
        fiscal as fiscal, <include refid="rowNumSubSql"/>, template_name as templateName, template_id as templateId,
        category_code as categoryCode, upload_type as uploadType, mof_div_code as mofDivCode, relate_expense as relateExpense,
        seq as seq
    </sql>

    <sql id="rowNumSubSql" databaseId="oracle">
        row_num as "rowNum"
    </sql>
    <sql id="rowNumSubSql" databaseId="mysql">
        row_num as rowNum
    </sql>

    <sql id="allColumnValue">
        #{attachListId,jdbcType=VARCHAR}, #{attachTypeId,jdbcType=VARCHAR}, #{billtypeCode,jdbcType=VARCHAR}, #{funcType,jdbcType=VARCHAR},
        #{funcCode,jdbcType=VARCHAR}, #{attachListName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{provideWayCode,jdbcType=VARCHAR},
        #{provideWayName,jdbcType=VARCHAR}, #{isNecessary,jdbcType=VARCHAR}, #{attachFormat,jdbcType=VARCHAR}, #{agyCode,jdbcType=VARCHAR},
        #{fiscal,jdbcType=INTEGER}, #{rowNum,jdbcType=INTEGER}, #{templateName,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR},
        #{categoryCode,jdbcType=VARCHAR}, #{uploadType,jdbcType=VARCHAR}, #{mofDivCode,jdbcType=VARCHAR}, #{relateExpense,jdbcType=VARCHAR},
        #{seq,jdbcType=INTEGER}
    </sql>

    <sql id="dynamicColumnSet">
        <trim suffixOverrides=",">
            <if test="attachListName != null and attachListName !=''">
                attach_list_name=#{attachListName,jdbcType=VARCHAR},
            </if>
            <if test="templateName != null">
                template_name=#{templateName,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null">
                template_id=#{templateId,jdbcType=VARCHAR},
            </if>
            <if test="isNecessary != null">
                is_necessary=#{isNecessary,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark=#{remark,jdbcType=VARCHAR},
            </if>
            <if test="provideWayCode != null">
                provide_way_code=#{provideWayCode,jdbcType=VARCHAR},
            </if>
            <if test="provideWayName != null">
                provide_way_name=#{provideWayName,jdbcType=VARCHAR},
            </if>
            <if test="attachFormat != null">
                attach_format=#{attachFormat,jdbcType=VARCHAR},
            </if>
            <if test="rowNum != null">
                row_num=#{rowNum,jdbcType=INTEGER},
            </if>
            <if test="categoryCode != null">
                category_code=#{categoryCode,jdbcType=VARCHAR},
            </if>
            <if test="relateExpense != null">
                relate_expense=#{relateExpense,jdbcType=VARCHAR},
            </if>
            <if test="uploadType != null">
                upload_type=#{uploadType,jdbcType=VARCHAR},
            </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                billtype_code=#{billtypeCode,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                seq=#{seq,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="auditColumnCond">
        <trim suffixOverrides=",">
            <if test="attachListId != null and attachListId != ''">
                attach_list_id=#{attachListId,jdbcType=VARCHAR},
            </if>
            <if test="attachListName != null and attachListName != ''">
                attach_list_name=#{attachListName,jdbcType=VARCHAR},
            </if>
            <if test="attachTypeId != null and attachTypeId != ''">
                attach_type_id=#{attachTypeId,jdbcType=VARCHAR},
            </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                billtype_code=#{billtypeCode,jdbcType=VARCHAR},
            </if>
            <if test="funcType != null and funcType != ''">
                func_type=#{funcType,jdbcType=VARCHAR},
            </if>
            <if test="funcCode != null and funcCode != ''">
                func_code=#{funcCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null">
                fiscal=#{fiscal,jdbcType=INTEGER},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                seq=#{seq,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="columnSelective">
        <trim suffixOverrides=",">
            <if test="attachListId != null and attachListId != ''">
                attach_list_id,
            </if>
            <if test="attachTypeId != null and attachTypeId != ''">
                attach_type_id,
            </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                billtype_code,
            </if>
            <if test="funcType != null and funcType != ''">
                func_type,
            </if>
            <if test="funcCode != null and funcCode != ''">
                func_code,
            </if>
            <if test="attachListName != null and attachListName != ''">
                attach_list_name,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="provideWayCode != null and provideWayCode != ''">
                provide_way_code,
            </if>
            <if test="provideWayName != null and provideWayName != ''">
                provide_way_name,
            </if>
            <if test="isNecessary != null and isNecessary != ''">
                is_necessary,
            </if>
            <if test="attachFormat != null and attachFormat != ''">
                attach_format,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="fiscal != null">
                fiscal,
            </if>
            <if test="rowNum != null">
                row_num,
            </if>
            <if test="templateName != null and templateName != ''">
                template_name,
            </if>
            <if test="templateId != null and templateId != ''">
                template_id,
            </if>
            <if test="categoryCode != null and categoryCode != ''">
                category_code,
            </if>
            <if test="uploadType != null and uploadType != ''">
                upload_type,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="relateExpense != null and relateExpense != ''">
                relate_expense,
            </if>
            <if test="seq != null">
                seq,
            </if>
        </trim>
    </sql>

    <sql id="columnValueSelective">
        <trim suffixOverrides=",">
            <if test="attachListId != null and attachListId != ''">
                #{attachListId,jdbcType=VARCHAR},
            </if>
            <if test="attachTypeId != null and attachTypeId != ''">
                #{attachTypeId,jdbcType=VARCHAR},
            </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                #{billtypeCode,jdbcType=VARCHAR},
            </if>
            <if test="funcType != null and funcType != ''">
                #{funcType,jdbcType=VARCHAR},
            </if>
            <if test="funcCode != null and funcCode != ''">
                #{funcCode,jdbcType=VARCHAR},
            </if>
            <if test="attachListName != null and attachListName != ''">
                #{attachListName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="provideWayCode != null and provideWayCode != ''">
                #{provideWayCode,jdbcType=VARCHAR},
            </if>
            <if test="provideWayName != null and provideWayName != ''">
                #{provideWayName,jdbcType=VARCHAR},
            </if>
            <if test="isNecessary != null and isNecessary !=''">
                #{isNecessary,jdbcType=VARCHAR},
            </if>
            <if test="attachFormat != null and attachFormat != ''">
                #{attachFormat,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="rowNum != null">
                #{rowNum,jdbcType=VARCHAR},
            </if>
            <if test="templateName != null and templateName != ''">
                #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null and templateId != ''">
                #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="categoryCode != null and categoryCode != ''">
                #{categoryCode,jdbcType=VARCHAR},
            </if>
            <if test="uploadType != null and uploadType != ''">
                #{uploadType,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="relateExpense != null and relateExpense != ''">
                #{relateExpense,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                #{seq,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="attachListId != null and attachListId != ''">
            AND attach_list_id=#{attachListId,jdbcType=VARCHAR}
        </if>
        <if test="attachTypeId != null and attachTypeId != ''">
            AND attach_type_id=#{attachTypeId,jdbcType=VARCHAR}
        </if>
        <if test="billtypeCode != null and billtypeCode != ''">
            AND billtype_code LIKE '%${billtypeCode}%'
        </if>
        <if test="funcType != null and funcType != ''">
            AND func_type=#{funcType,jdbcType=VARCHAR}
        </if>
        <if test="funcCode != null and funcCode != ''">
            AND func_code=#{funcCode,jdbcType=VARCHAR}
        </if>
        <if test="attachListName != null and attachListName != ''">
            AND attach_list_name=#{attachListName,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            AND remark=#{remark,jdbcType=VARCHAR}
        </if>
        <if test="provideWayCode != null and provideWayCode != ''">
            AND provide_way_code=#{provideWayCode,jdbcType=VARCHAR}
        </if>
        <if test="provideWayName != null and provideWayName != ''">
            AND provide_way_name=#{provideWayName,jdbcType=VARCHAR}
        </if>
        <if test="isNecessary != null and isNecessary !=''">
            AND is_necessary=#{isNecessary,jdbcType=VARCHAR}
        </if>
        <if test="attachFormat != null and attachFormat != ''">
            AND attach_format=#{attachFormat,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null">
            AND fiscal=#{fiscal,jdbcType=INTEGER}
        </if>
        <if test="rowNum != null">
            AND row_num=#{rowNum,jdbcType=INTEGER}
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name=#{templateName,jdbcType=VARCHAR}
        </if>
        <if test="templateId != null and templateId != ''">
            AND template_id=#{templateId,jdbcType=VARCHAR}
        </if>
        <if test="categoryCode != null and categoryCode != ''">
            AND category_code=#{categoryCode,jdbcType=VARCHAR}
        </if>
        <if test="uploadType != null and uploadType != ''">
            AND upload_type=#{uploadType,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="relateExpense != null and relateExpense != ''">
            AND relate_expense=#{relateExpense,jdbcType=VARCHAR}
        </if>
        <if test="seq != null">
            AND seq=#{seq,jdbcType=INTEGER}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.attachlist.PcxAttachList">
        INSERT INTO pa_attach_list (
        <include refid="allColumn" />
        ) VALUES (
        <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.attachlist.PcxAttachList">
        INSERT INTO pa_attach_list (
        <include refid="columnSelective" />
        ) VALUES (
        <include refid="columnValueSelective" />
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        DELETE FROM pa_attach_list
        WHERE
            attach_list_id=#{attachListId}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pa_attach_list
        WHERE attach_list_id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.attachlist.PcxAttachList">
        DELETE FROM pa_attach_list
        WHERE 1=1
        <include refid="allColumnCond" />
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.attachlist.PcxAttachList">
        UPDATE pa_attach_list
        <set>
            <include refid="dynamicColumnSet" />
        </set>
        WHERE attach_list_id=#{attachListId}
    </update>


    <select id="selectById" parameterType="string" resultType="com.pty.pcx.vo.attachlist.PcxAttachListVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pa_attach_list
        WHERE attach_list_id=#{attachListId}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.vo.attachlist.PcxAttachListVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pa_attach_list
        WHERE attach_list_id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY seq ASC
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.attachlist.PcxAttachList" resultType="com.pty.pcx.vo.attachlist.PcxAttachListVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pa_attach_list
        WHERE 1=1
        <include refid="allColumnCond" />
        ORDER BY seq ASC
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.attachlist.PcxAttachList" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pa_attach_list
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>

    <select id="selectByQO" resultType="com.pty.pcx.vo.attachlist.PcxAttachListVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pa_attach_list
        WHERE 1=1
        <include refid="allColumnCond" />
        <if test="funcCodes != null and funcCodes.size &gt; 0">
            and func_code in
            <foreach collection="funcCodes" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY seq ASC
    </select>
</mapper>
