<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasItemExpDao">

    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.bas.PcxBasItemExp">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="expense_code" property="expenseCode" jdbcType="VARCHAR"/>
        <result column="expense_name" property="expenseName" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modifier_name" property="modifierName" jdbcType="VARCHAR"/>
        <result column="modified_time" property="modifiedTime" jdbcType="VARCHAR"/>
        <result column="creator_code" property="creatorCode" jdbcType="VARCHAR"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="VARCHAR"/>
        <result column="seq" property="seq" jdbcType="INTEGER"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="billtype_code" property="billtypeCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="allColumn">
        id, item_code, item_name, expense_code, expense_name, agy_code, fiscal, mof_div_code,
        modifier, modifier_name, modified_time, creator_code, creator_name, created_time, seq,
        tenant_id, billtype_code
    </sql>

    <sql id="allColumnSet">
        <trim prefix="" suffixOverrides=",">
            <if test="itemCode != null and itemCode != ''"> item_code = #{itemCode}, </if>
            <if test="itemName != null and itemName != ''"> item_name = #{itemName}, </if>
            <if test="itemCode != null and itemCode != ''"> item_code = #{itemCode,jdbcType=VARCHAR}, </if>
            <if test="itemName != null and itemName != ''"> item_name = #{itemName,jdbcType=VARCHAR}, </if>
            <if test="expenseCode != null and expenseCode != ''"> expense_code = #{expenseCodem,jdbcType=VARCHAR}, </if>
            <if test="expenseName != null and expenseName != ''"> expense_name = #{expenseName,jdbcType=VARCHAR}, </if>
            <if test="modifier != null and modifier != ''"> modifier = #{modifier,jdbcType=VARCHAR}, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name = #{modifierName,jdbcType=VARCHAR}, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time = #{modifiedTime,jdbcType=VARCHAR}, </if>
            <if test="creatorCode != null and creatorCode != ''"> creator_code = #{creatorCode,jdbcType=VARCHAR}, </if>
            <if test="creatorName != null and creatorName != ''"> creator_name = #{creatorName,jdbcType=VARCHAR}, </if>
            <if test="createdTime != null and createdTime != ''"> created_time = #{createdTime,jdbcType=VARCHAR}, </if>
            <if test="seq != null"> seq = #{seq,jdbcType=INTEGER}, </if>
            <if test="tenantId != null and tenantId != ''"> tenant_id = #{tenantId,jdbcType=VARCHAR}, </if>
            <if test="billtypeCode != null and billtypeCode != ''"> billtype_code = #{billtypeCode,jdbcType=VARCHAR}, </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">AND id = #{id,jdbcType=VARCHAR}</if>
        <if test="itemCode != null and itemCode != ''">AND item_code = #{itemCode,jdbcType=VARCHAR}</if>
        <if test="itemName != null and itemName != ''">AND item_name = #{itemName,jdbcType=VARCHAR}</if>
        <if test="expenseCode != null and expenseCode != ''">AND expense_code = #{expenseCode,jdbcType=VARCHAR}</if>
        <if test="expenseName != null and expenseName != ''">AND expense_name = #{expenseName,jdbcType=VARCHAR}</if>
        <if test="agyCode != null and agyCode != ''">AND agy_code = #{agyCode,jdbcType=VARCHAR}</if>
        <if test="fiscal != null and fiscal != ''">AND fiscal = #{fiscal,jdbcType=VARCHAR}</if>
        <if test="mofDivCode != null and mofDivCode != ''">AND mof_div_code = #{mofDivCode,jdbcType=VARCHAR}</if>
        <if test="modifier != null and modifier != ''">AND modifier = #{modifier,jdbcType=VARCHAR}</if>
        <if test="modifierName != null and modifierName != ''">AND modifier_name = #{modifierName,jdbcType=VARCHAR}</if>
        <if test="modifiedTime != null and modifiedTime != ''">AND modified_time = #{modifiedTime,jdbcType=VARCHAR}</if>
        <if test="creatorCode != null and creatorCode != ''">AND creator_code = #{creatorCode,jdbcType=VARCHAR}</if>
        <if test="creatorName != null and creatorName != ''">AND creator_name = #{creatorName,jdbcType=VARCHAR}</if>
        <if test="createdTime != null and createdTime != ''">AND created_time = #{createdTime,jdbcType=VARCHAR}</if>
        <if test="expenseCodes != null and expenseCodes.size &gt; 0">
            and expense_code in
            <foreach collection="expenseCodes" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="itemCodes != null and itemCodes.size &gt; 0">
            and item_code in
            <foreach collection="itemCodes" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="tenantId != null and tenantId != ''">AND tenant_id = #{tenantId,jdbcType=VARCHAR}</if>
        <if test="billtypeCode != null and billtypeCode != ''">AND FIND_IN_SET(#{billtypeCode,jdbcType=VARCHAR}, billtype_code) > 0</if>
    </sql>


    <select id="selectByQO" resultMap="BaseResultMap" parameterType="com.pty.pcx.qo.bas.PcxBasItemExpQO">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_bas_item_exp
        WHERE 1=1
        <include refid="allColumnCond"/>
        order by seq
    </select>
    <select id="selectValidIsLeafTypeIds" resultType="java.lang.String">
        SELECT
            ex.id
        FROM
            pcx_bas_exp_type ex
        WHERE
            ex.is_refine = 0
          AND ex.is_leaf = 1
          AND EXISTS (
            SELECT
                1
            FROM
                pcx_bas_exp_type et
            WHERE
                ( ex.fiscal, ex.agy_code, ex.mof_div_code, ex.tenant_id, ex.expense_code ) = ( et.fiscal, et.agy_code, et.mof_div_code, et.tenant_id, et.parent_code )
              AND et.is_refine = 0
        )
            LIMIT 100;
    </select>

    <insert id="insert" parameterType="com.pty.pcx.entity.bas.PcxBasItemExp">
        INSERT INTO pcx_bas_item_exp (
        <include refid="allColumn"/>
        ) VALUES (
        #{id, jdbcType=VARCHAR}, #{itemCode, jdbcType=VARCHAR}, #{itemName, jdbcType=VARCHAR}, #{expenseCode, jdbcType=VARCHAR}, #{expenseName, jdbcType=VARCHAR}, #{agyCode, jdbcType=VARCHAR}, #{fiscal, jdbcType=VARCHAR}, #{mofDivCode, jdbcType=VARCHAR},
        #{modifier, jdbcType=VARCHAR}, #{modifierName, jdbcType=VARCHAR}, #{modifiedTime, jdbcType=VARCHAR}, #{creatorCode, jdbcType=VARCHAR}, #{creatorName, jdbcType=VARCHAR}, #{createdTime, jdbcType=VARCHAR}, #{seq, jdbcType=INTEGER}, #{tenantId, jdbcType=VARCHAR}, #{billtypeCode, jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateById" parameterType="com.pty.pcx.entity.bas.PcxBasItemExp">
        UPDATE pcx_bas_item_exp
        SET
        <include refid="allColumnSet"/>
        WHERE 1=1
        <include refid="allColumnCond"/>
    </update>
    <update id="updateExpenseTypeIsLeaf">
        update pcx_bas_exp_type set is_leaf = 0 where id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>

    <delete id="deleteByItemCode" parameterType="com.pty.pcx.qo.bas.PcxBasItemExpQO">
        DELETE FROM pcx_bas_item_exp WHERE 1=1 AND item_code = #{itemCode, jdbcType=VARCHAR}
           AND agy_code = #{agyCode, jdbcType=VARCHAR}
           AND fiscal = #{fiscal, jdbcType=VARCHAR}
           AND mof_div_code = #{mofDivCode, jdbcType=VARCHAR}
    </delete>

    <select id="selectByBilltypeCode" resultMap="BaseResultMap" parameterType="com.pty.pcx.qo.bas.PcxBasItemExpQO">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_bas_item_exp
        WHERE 1=1
        <include refid="allColumnCond"/>
        order by seq
    </select>

</mapper>