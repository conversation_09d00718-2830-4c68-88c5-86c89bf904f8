<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.stand.PcxStandValueDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.stand.PcxStandValue" id="PcxStandValueMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="standCode" column="stand_code" jdbcType="VARCHAR"/>
        <result property="rowKeyCode" column="row_key_code" jdbcType="VARCHAR"/>
        <result property="rowKeyName" column="row_key_name" jdbcType="VARCHAR"/>
        <result property="rowValueCode" column="row_value_code" jdbcType="VARCHAR"/>
        <result property="rowValueName" column="row_value_name" jdbcType="VARCHAR"/>
        <result property="columnKeyCode" column="column_key_code" jdbcType="VARCHAR"/>
        <result property="columnKeyName" column="column_key_name" jdbcType="VARCHAR"/>
        <result property="columnValueCode" column="column_value_code" jdbcType="VARCHAR"/>
        <result property="columnValueName" column="column_value_name" jdbcType="VARCHAR"/>
        <result property="standardValue" column="standard_value" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 全部字段 -->
	<sql id="allColumn">
        id, 
        stand_code, 
        row_key_code, 
        row_key_name, 
        row_value_code, 
        row_value_name, 
        column_key_code, 
        column_key_name, 
        column_value_code, 
        column_value_name, 
        standard_value, 
        agy_code, 
        fiscal, 
        mof_div_code
	</sql>
    
    <select id="queryStandStandValueList" resultMap="PcxStandValueMap">
        select
        <include refid="allColumn" />
        from pcx_stand_value
        where
        agy_code = #{agyCode}
        and fiscal = #{fiscal}
        and mof_div_code = #{mofDivCode}
        and stand_code in
        <foreach collection="standCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectStandCodesByAgy" resultType="java.lang.String">
        select distinct stand_code
        from pcx_stand_value
        where
            REPLACE(UPPER(row_key_code), '_', '') = REPLACE(UPPER(#{rowKeyCode}), '_', '')
        and agy_code = #{agyCode}
        and fiscal = #{fiscal}
        and mof_div_code = #{mofDivCode}
    </select>

    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_stand_value (
			<if test="standCode != null and standCode != ''">
				stand_code,
			</if>
			<if test="rowKeyCode != null and rowKeyCode != ''">
				row_key_code,
			</if>
			<if test="rowKeyName != null and rowKeyName != ''">
				row_key_name,
			</if>
			<if test="rowValueCode != null and rowValueCode != ''">
				row_value_code,
			</if>
			<if test="rowValueName != null and rowValueName != ''">
				row_value_name,
			</if>
			<if test="columnKeyCode != null and columnKeyCode != ''">
				column_key_code,
			</if>
			<if test="columnKeyName != null and columnKeyName != ''">
				column_key_name,
			</if>
			<if test="columnValueCode != null and columnValueCode != ''">
				column_value_code,
			</if>
			<if test="columnValueName != null and columnValueName != ''">
				column_value_name,
			</if>
			<if test="standardValue != null and standardValue != ''">
				standard_value,
			</if>
			<if test="agyCode != null and agyCode != ''">
				agy_code,
			</if>
			<if test="fiscal != null and fiscal != ''">
				fiscal,
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				mof_div_code,
			</if>
        )
        values (
			<if test="standCode != null and standCode != ''">
				#{standCode},
			</if>
			<if test="rowKeyCode != null and rowKeyCode != ''">
				#{rowKeyCode},
			</if>
			<if test="rowKeyName != null and rowKeyName != ''">
				#{rowKeyName},
			</if>
			<if test="rowValueCode != null and rowValueCode != ''">
				#{rowValueCode},
			</if>
			<if test="rowValueName != null and rowValueName != ''">
				#{rowValueName},
			</if>
			<if test="columnKeyCode != null and columnKeyCode != ''">
				#{columnKeyCode},
			</if>
			<if test="columnKeyName != null and columnKeyName != ''">
				#{columnKeyName},
			</if>
			<if test="columnValueCode != null and columnValueCode != ''">
				#{columnValueCode},
			</if>
			<if test="columnValueName != null and columnValueName != ''">
				#{columnValueName},
			</if>
			<if test="standardValue != null and standardValue != ''">
				#{standardValue},
			</if>
			<if test="agyCode != null and agyCode != ''">
				#{agyCode},
			</if>
			<if test="fiscal != null and fiscal != ''">
				#{fiscal},
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				#{mofDivCode},
			</if>
        )
    </insert>
    <insert id="batchInsert" databaseId="mysql">
        INSERT INTO pcx_stand_value (
        id,
        stand_code,
        row_key_code,
        row_key_name,
        row_value_code,
        row_value_name,
        column_key_code,
        column_key_name,
        column_value_code,
        column_value_name,
        standard_value,
        agy_code,
        fiscal,
        mof_div_code
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.standCode},
            #{item.rowKeyCode},
            #{item.rowKeyName},
            #{item.rowValueCode},
            #{item.rowValueName},
            #{item.columnKeyCode},
            #{item.columnKeyName},
            #{item.columnValueCode},
            #{item.columnValueName},
            #{item.standardValue},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode}
            )
        </foreach>
    </insert>

    <insert id="batchInsert" databaseId="oracle">
        INSERT INTO pcx_stand_value (
        id,
        stand_code,
        row_key_code,
        row_key_name,
        row_value_code,
        row_value_name,
        column_key_code,
        column_key_name,
        column_value_code,
        column_value_name,
        standard_value,
        agy_code,
        fiscal,
        mof_div_code
        ) VALUES
        <foreach collection="list" item="item" separator="union all">
            select
            #{item.id},
            #{item.standCode},
            #{item.rowKeyCode},
            #{item.rowKeyName},
            #{item.rowValueCode},
            #{item.rowValueName},
            #{item.columnKeyCode},
            #{item.columnKeyName},
            #{item.columnValueCode},
            #{item.columnValueName},
            #{item.standardValue},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode}
            from DUAL
        </foreach>
    </insert>

    <delete id="deleteStandValueByStandCode">
        DELETE FROM pcx_stand_value
        WHERE stand_code = #{standCode}
          AND agy_code = #{agyCode}
          AND fiscal = #{fiscal}
          AND mof_div_code = #{mofDivCode}
    </delete>

    <delete id="deleteByRowKeyValue">
        DELETE FROM pcx_stand_value
        WHERE
            mof_div_code = #{mofDivCode}
          AND fiscal = #{fiscal}
          AND agy_code = #{agyCode}
          AND REPLACE(UPPER(row_key_code), '_', '') = REPLACE(UPPER(#{rowKeyCode}), '_', '')
          AND row_value_code = #{rowValueCode}
    </delete>

    <update id="updateValueByStandCode" parameterType="com.pty.pcx.entity.stand.vo.PcxStandValueVO">
        UPDATE pcx_stand_value
        SET
            standard_value = #{standardValue}
        WHERE
            mof_div_code = #{mofDivCode}
          AND fiscal = #{fiscal}
          AND agy_code = #{agyCode}
          AND stand_code = #{standCode}
          AND row_key_code = #{rowKeyCode}
          AND row_value_code = #{rowValueCode}
          AND column_key_code = #{columnKeyCode}
          AND column_value_code = #{columnValueCode}
    </update>
    
</mapper>


