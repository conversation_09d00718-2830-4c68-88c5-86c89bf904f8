<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pty.pcx.dao.bas.PcxBasItemAuthDao">

    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.bas.PcxBasItemAuth">
        <id column="id" property="id" />
        <result column="auth_code" property="authCode" />
        <result column="auth_name" property="authName" />
        <result column="auth_type" property="authType" />
        <result column="item_code" property="itemCode" />
        <result column="agy_code" property="agyCode" />
        <result column="mof_div_code" property="mofDivCode" />
        <result column="fiscal" property="fiscal" />
        <result column="modifier" property="modifier" />
        <result column="modifier_name" property="modifierName" />
        <result column="modified_time" property="modifiedTime" />
        <result column="creator" property="creator" />
        <result column="creator_name" property="creatorName" />
        <result column="created_time" property="createdTime" />
    </resultMap>

    <sql id="allColumn">
        id, auth_code, auth_type, item_code, agy_code, mof_div_code, fiscal, modifier, modifier_name, modified_time, creator, creator_name, created_time
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''"> AND id = #{id,jdbcType=VARCHAR} </if>
        <if test="authCode != null and authCode != ''"> AND auth_code = #{authCode,jdbcType=VARCHAR} </if>
        <if test="authName != null and authName != ''"> AND auth_name = #{authName,jdbcType=VARCHAR} </if>
        <if test="authType != null and authType != ''"> AND auth_type = #{authType,jdbcType=VARCHAR} </if>
        <if test="itemCode != null and itemCode != ''"> AND item_code = #{itemCode,jdbcType=VARCHAR} </if>
        <if test="agyCode != null and agyCode != ''"> AND agy_code = #{agyCode,jdbcType=VARCHAR} </if>
        <if test="mofDivCode != null and mofDivCode != ''"> AND mof_div_code = #{mofDivCode,jdbcType=VARCHAR} </if>
        <if test="fiscal != null and fiscal != ''"> AND fiscal = #{fiscal,jdbcType=VARCHAR} </if>
        <if test="modifier != null and modifier != ''"> AND modifier = #{modifier,jdbcType=VARCHAR} </if>
        <if test="modifierName != null and modifierName != ''"> AND modifier_name = #{modifierName,jdbcType=VARCHAR} </if>
        <if test="modifiedTime != null and modifiedTime != ''"> AND modified_time = #{modifiedTime,jdbcType=VARCHAR} </if>
        <if test="creator != null and creator != ''"> AND creator = #{creator,jdbcType=VARCHAR} </if>
        <if test="creatorName != null and creatorName != ''"> AND creator_name = #{creatorName,jdbcType=VARCHAR} </if>
        <if test="createdTime != null and createdTime != ''"> AND created_time = #{createdTime,jdbcType=VARCHAR} </if>
    </sql>


    <select id="selectByQO" resultMap="BaseResultMap">
        SELECT
        <include refid="allColumn" />
        FROM pcx_bas_item_auth
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>

    <insert id="insertSelective">
        insert into pcx_bas_item_auth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''"> id, </if>
            <if test="authCode != null and authCode != ''"> auth_code, </if>
            <if test="authName != null and authName != ''"> auth_name, </if>
            <if test="authType != null and authType != ''"> auth_type, </if>
            <if test="itemCode != null and itemCode != ''"> item_code, </if>
            <if test="agyCode != null and agyCode != ''"> agy_code, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> mof_div_code, </if>
            <if test="fiscal != null and fiscal != ''"> fiscal, </if>
            <if test="modifier != null and modifier != ''"> modifier, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time, </if>
            <if test="creator != null and creator != ''"> creator, </if>
            <if test="creatorName != null and creatorName != ''"> creator_name, </if>
            <if test="createdTime != null and createdTime != ''"> created_time, </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''"> #{id,jdbcType=VARCHAR}, </if>
            <if test="authCode != null and authCode != ''"> #{authCode,jdbcType=VARCHAR}, </if>
            <if test="authName != null and authName != ''"> #{authName,jdbcType=VARCHAR}, </if>
            <if test="authType != null and authType != ''"> #{authType,jdbcType=VARCHAR}, </if>
            <if test="itemCode != null and itemCode != ''"> #{itemCode,jdbcType=VARCHAR}, </if>
            <if test="agyCode != null and agyCode != ''"> #{agyCode,jdbcType=VARCHAR}, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> #{mofDivCode,jdbcType=VARCHAR}, </if>
            <if test="fiscal != null and fiscal != ''"> #{fiscal,jdbcType=VARCHAR}, </if>
            <if test="modifier != null and modifier != ''"> #{modifier,jdbcType=VARCHAR}, </if>
            <if test="modifierName != null and modifierName != ''"> #{modifierName,jdbcType=VARCHAR}, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> #{modifiedTime,jdbcType=VARCHAR}, </if>
            <if test="creator != null and creator != ''"> #{creator,jdbcType=VARCHAR}, </if>
            <if test="creatorName != null and creatorName != ''"> #{creatorName,jdbcType=VARCHAR}, </if>
            <if test="createdTime != null and createdTime != ''"> #{createdTime,jdbcType=VARCHAR}, </if>
        </trim>
    </insert>

    <delete id="deleteByItemCode">
        delete from pcx_bas_item_auth
        where item_code = #{itemCode,jdbcType=VARCHAR}
        and agy_code = #{agyCode,jdbcType=VARCHAR}
        and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        and fiscal = #{fiscal,jdbcType=VARCHAR}
    </delete>

    <select id="getOwnItem" resultType="com.pty.pcx.entity.bas.PcxBasItem">
        SELECT
            item.item_code,item.item_name,item.parent_code, item.seq
        FROM
            pcx_bas_item item
                LEFT JOIN pcx_bas_item_auth auth ON item.item_code = auth.item_code
                AND item.agy_code = auth.agy_code
                AND item.mof_div_code = auth.mof_div_code
                AND item.fiscal = auth.fiscal
        WHERE
           item.agy_code = #{agyCode,jdbcType=VARCHAR}
          AND item.mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
          AND item.fiscal = #{fiscal,jdbcType=VARCHAR}
          AND item.is_enabled = 1
          AND (item.billtype_code IS NULL OR item.billtype_code = '' OR FIND_IN_SET(#{billtypeCode,jdbcType=VARCHAR}, item.billtype_code) > 0)
          AND (
            auth_code IS NULL
            <if test="employeeCode != null and employeeCode!=''">
                OR ( auth.auth_type = '1' AND auth.auth_code =  #{employeeCode,jdbcType=VARCHAR})
            </if>
            <if test="departmentCode != null and departmentCode!=''">
                 OR (auth.auth_type = '2' AND auth.auth_code = #{departmentCode,jdbcType=VARCHAR})
            </if>
            )
            GROUP BY item.item_code,item_name,parent_code, seq
            ORDER BY item.item_code;
    </select>
</mapper>