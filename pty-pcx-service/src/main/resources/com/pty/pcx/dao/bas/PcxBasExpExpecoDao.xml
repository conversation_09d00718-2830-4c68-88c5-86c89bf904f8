<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasExpExpecoDao">

    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.bas.PcxBasExpExpeco">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="expense_code" property="expenseCode" jdbcType="VARCHAR"/>
        <result column="expense_name" property="expenseName" jdbcType="VARCHAR"/>
        <result column="expeco_code" property="expecoCode" jdbcType="VARCHAR"/>
        <result column="expeco_name" property="expecoName" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
        <result column="seq" property="seq" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="allColumn">
        id, expense_code, expense_name, expeco_code, expeco_name, agy_code, fiscal, mof_div_code, seq
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">AND id = #{id,jdbcType=VARCHAR}</if>
        <if test="expenseCode != null and expenseCode != ''">AND expense_code = #{expenseCode,jdbcType=VARCHAR}</if>
        <if test="expenseName != null and expenseName != ''">AND expense_name = #{expenseName,jdbcType=VARCHAR}</if>
        <if test="expecoCode != null and expecoCode != ''">AND expeco_code = #{expecoCode,jdbcType=VARCHAR}</if>
        <if test="expecoName != null and expecoName != ''">AND expeco_name = #{expecoName,jdbcType=VARCHAR}</if>
        <if test="agyCode != null and agyCode != ''">AND agy_code = #{agyCode,jdbcType=VARCHAR}</if>
        <if test="fiscal != null and fiscal != ''">AND fiscal = #{fiscal,jdbcType=VARCHAR}</if>
        <if test="mofDivCode != null and mofDivCode != ''">AND mof_div_code = #{mofDivCode,jdbcType=VARCHAR}</if>
        <if test="seq != null">AND seq = #{seq,jdbcType=INTEGER}</if>
    </sql>

    <sql id="allColumnSet">
        <trim prefix="" suffixOverrides=",">
            <if test="expenseCode != null and expenseCode != ''"> expense_code = #{expenseCode,jdbcType=VARCHAR}, </if>
            <if test="expenseName != null and expenseName != ''"> expense_name = #{expenseName,jdbcType=VARCHAR}, </if>
            <if test="expecoCode != null and expecoCode != ''"> expeco_code = #{expecoCode,jdbcType=VARCHAR}, </if>
            <if test="expecoName != null and expecoName != ''"> expeco_name = #{expecoName,jdbcType=VARCHAR}, </if>
            <if test="seq != null"> seq = #{seq,jdbcType=INTEGER}, </if>
        </trim>
    </sql>



    <select id="selectByQO" resultMap="BaseResultMap" parameterType="com.pty.pcx.qo.bas.PcxBasExpExpecoQO">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_bas_exp_expeco
        WHERE 1=1
        <include refid="allColumnCond"/>
        order by seq
    </select>

    <insert id="insert" parameterType="com.pty.pcx.entity.bas.PcxBasExpExpeco">
        INSERT INTO pcx_bas_exp_expeco (
        <include refid="allColumn"/>
        ) VALUES (
        #{id,jdbcType=VARCHAR}, #{expenseCode,jdbcType=VARCHAR}, #{expenseName,jdbcType=VARCHAR}, #{expecoCode,jdbcType=VARCHAR}, #{expecoName,jdbcType=VARCHAR}, #{agyCode,jdbcType=VARCHAR}, #{fiscal,jdbcType=VARCHAR}, #{mofDivCode,jdbcType=VARCHAR}, #{seq,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByQO" parameterType="com.pty.pcx.qo.bas.PcxBasExpExpecoQO">
        UPDATE pcx_bas_exp_expeco
        SET
        <include refid="allColumnSet" />
        WHERE 1=1
        <include refid="allColumnCond"/>
    </update>

    <delete id="deleteByQO" parameterType="com.pty.pcx.qo.bas.PcxBasExpExpecoQO">
        DELETE FROM pcx_bas_exp_expeco WHERE 1=1
        <include refid="allColumnCond"/>
    </delete>

    <select id="selectByExpeco" resultType="com.pty.pcx.entity.bas.PcxBasExpType">
        SELECT
            pbee.expense_code,
            pbee.expense_name,
            pbee.expeco_code,
            pbee.expeco_name,
            pbee.seq
        FROM
            pcx_bas_exp_expeco pbee
                LEFT JOIN pcx_bas_exp_type pbet ON pbee.mof_div_code = pbet.mof_div_code
                AND pbee.agy_code = pbet.agy_code
                AND pbee.fiscal = pbet.fiscal
                and pbee.expense_code = pbet.expense_code
        WHERE
            pbet.fiscal = #{fiscal,jdbcType=VARCHAR}
          AND pbet.mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
          AND pbet.agy_code = #{agyCode,jdbcType=VARCHAR}
          AND pbet.is_leaf = 1
          AND pbet.is_enabled =1
        <if test="expecoCodes != null and expecoCodes.size &gt; 0">
            and expeco_code in
            <foreach collection="expecoCodes" index="index" item="code" open="(" separator="," close=")">
                #{code,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>