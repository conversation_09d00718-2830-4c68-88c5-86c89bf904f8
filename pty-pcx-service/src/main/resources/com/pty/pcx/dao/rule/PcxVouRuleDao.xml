<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.rule.PcxVouRuleDao">
    <sql id="allColumn">
        acitem10_code,                                    pay_account_type_name,                            ecs_biz_type_code,                                acitem02_code,
        acitem04_name,                                    acitem03_code,                                    acitem10_name,                                    id,
        is_merge_by_fac_cr,                               acb_name,                                         acitem05_name,                                    acitem01_name,
        acitem04_code,                                    pay_account_type_code,                            acitem02_name,                                    expense_code,
        aca_code,                                         dr_cr,                                            acitem05_code,                                    settlement_name,
        fiscal,                                           expense_detail_name,                              is_merge_by_summary,                              is_merge_by_bac_dr,
        acb_code,                                         account_aco,                                      acs_code,                                         acitem03_name,
        field_name,                                       bill_func_code,                                   acitem06_code,                                    acitem08_name,
        expense_name,                                     atom_name,                                        acitem08_code,                                    summary_rule_content,
        bill_func_name,                                   acitem09_name,                                    acitem07_code,                                    is_merge_by_bac_cr,
        acitem06_name,                                    ord,                                              settlement_type,                                  field_code,
        mof_div_code,                                     acitem09_code,                                    agy_code,
        expense_detail_code,                              atom_code,                                        ecs_biz_type_name,                                acitem07_name,
        account_aco_name,                                 acitem01_code,                                    dr_type,                                          is_merge_by_fac_dr
    </sql>

    <sql id="allColumnAlias">
        acitem10_code as acitem10Code,                    pay_account_type_name as payAccountTypeName,      ecs_biz_type_code as ecsBizTypeCode,              acitem02_code as acitem02Code,
        acitem04_name as acitem04Name,                    acitem03_code as acitem03Code,                    acitem10_name as acitem10Name,                    id as id,
        is_merge_by_fac_cr as isMergeByFacCr,             acb_name as acbName,                              acitem05_name as acitem05Name,                    acitem01_name as acitem01Name,
        acitem04_code as acitem04Code,                    pay_account_type_code as payAccountTypeCode,      acitem02_name as acitem02Name,                    expense_code as expenseCode,
        aca_code as acaCode,                              dr_cr as drCr,                                    acitem05_code as acitem05Code,                    settlement_name as settlementName,
        fiscal as fiscal,                                 expense_detail_name as expenseDetailName,         is_merge_by_summary as isMergeBySummary,          is_merge_by_bac_dr as isMergeByBacDr,
        acb_code as acbCode,                              account_aco as accountAco,                        acs_code as acsCode,                              acitem03_name as acitem03Name,
        field_name as fieldName,                          bill_func_code as billFuncCode,                   acitem06_code as acitem06Code,                    acitem08_name as acitem08Name,
        expense_name as expenseName,                      atom_name as atomName,                            acitem08_code as acitem08Code,                    summary_rule_content as summaryRuleContent,
        bill_func_name as billFuncName,                   acitem09_name as acitem09Name,                    acitem07_code as acitem07Code,                    is_merge_by_bac_cr as isMergeByBacCr,
        acitem06_name as acitem06Name,                    ord as ord,                                       settlement_type as settlementType,                field_code as fieldCode,
        mof_div_code as mofDivCode,                       acitem09_code as acitem09Code,                    agy_code as agyCode,
        expense_detail_code as expenseDetailCode,         atom_code as atomCode,                            ecs_biz_type_name as ecsBizTypeName,              acitem07_name as acitem07Name,
        account_aco_name as accountAcoName,               acitem01_code as acitem01Code,                    dr_type as drType,                                is_merge_by_fac_dr as isMergeByFacDr
    </sql>

    <sql id="allColumnValue">
        #{acitem10Code,jdbcType=VARCHAR},       #{payAccountTypeName,jdbcType=VARCHAR}, #{ecsBizTypeCode,jdbcType=VARCHAR},     #{acitem02Code,jdbcType=VARCHAR},
        #{acitem04Name,jdbcType=VARCHAR},       #{acitem03Code,jdbcType=VARCHAR},       #{acitem10Name,jdbcType=VARCHAR},       #{id,jdbcType=VARCHAR},
        #{isMergeByFacCr,jdbcType=INTEGER},     #{acbName,jdbcType=VARCHAR},            #{acitem05Name,jdbcType=VARCHAR},       #{acitem01Name,jdbcType=VARCHAR},
        #{acitem04Code,jdbcType=VARCHAR},       #{payAccountTypeCode,jdbcType=VARCHAR}, #{acitem02Name,jdbcType=VARCHAR},       #{expenseCode,jdbcType=VARCHAR},
        #{acaCode,jdbcType=VARCHAR},            #{drCr,jdbcType=INTEGER},               #{acitem05Code,jdbcType=VARCHAR},       #{settlementName,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},             #{expenseDetailName,jdbcType=VARCHAR},  #{isMergeBySummary,jdbcType=INTEGER},   #{isMergeByBacDr,jdbcType=INTEGER},
        #{acbCode,jdbcType=VARCHAR},            #{accountAco,jdbcType=VARCHAR},         #{acsCode,jdbcType=VARCHAR},            #{acitem03Name,jdbcType=VARCHAR},
        #{fieldName,jdbcType=VARCHAR},          #{billFuncCode,jdbcType=VARCHAR},       #{acitem06Code,jdbcType=VARCHAR},       #{acitem08Name,jdbcType=VARCHAR},
        #{expenseName,jdbcType=VARCHAR},        #{atomName,jdbcType=VARCHAR},           #{acitem08Code,jdbcType=VARCHAR},       #{summaryRuleContent,jdbcType=VARCHAR},
        #{billFuncName,jdbcType=VARCHAR},       #{acitem09Name,jdbcType=VARCHAR},       #{acitem07Code,jdbcType=VARCHAR},       #{isMergeByBacCr,jdbcType=INTEGER},
        #{acitem06Name,jdbcType=VARCHAR},       #{ord,jdbcType=INTEGER},                #{settlementType,jdbcType=VARCHAR},     #{fieldCode,jdbcType=VARCHAR},
        #{mofDivCode,jdbcType=VARCHAR},         #{acitem09Code,jdbcType=VARCHAR},       #{agyCode,jdbcType=VARCHAR},
        #{expenseDetailCode,jdbcType=VARCHAR},  #{atomCode,jdbcType=VARCHAR},           #{ecsBizTypeName,jdbcType=VARCHAR},     #{acitem07Name,jdbcType=VARCHAR},
        #{accountAcoName,jdbcType=VARCHAR},     #{acitem01Code,jdbcType=VARCHAR},       #{drType,jdbcType=VARCHAR},             #{isMergeByFacDr,jdbcType=INTEGER}
    </sql>

    <sql id="allColumnItemValue">
        #{item.acitem10Code,jdbcType=VARCHAR},       #{item.payAccountTypeName,jdbcType=VARCHAR}, #{item.ecsBizTypeCode,jdbcType=VARCHAR},     #{item.acitem02Code,jdbcType=VARCHAR},
        #{item.acitem04Name,jdbcType=VARCHAR},       #{item.acitem03Code,jdbcType=VARCHAR},       #{item.acitem10Name,jdbcType=VARCHAR},       #{item.id,jdbcType=VARCHAR},
        #{item.isMergeByFacCr,jdbcType=INTEGER},     #{item.acbName,jdbcType=VARCHAR},            #{item.acitem05Name,jdbcType=VARCHAR},       #{item.acitem01Name,jdbcType=VARCHAR},
        #{item.acitem04Code,jdbcType=VARCHAR},       #{item.payAccountTypeCode,jdbcType=VARCHAR}, #{item.acitem02Name,jdbcType=VARCHAR},       #{item.expenseCode,jdbcType=VARCHAR},
        #{item.acaCode,jdbcType=VARCHAR},            #{item.drCr,jdbcType=INTEGER},               #{item.acitem05Code,jdbcType=VARCHAR},       #{item.settlementName,jdbcType=VARCHAR},
        #{item.fiscal,jdbcType=VARCHAR},             #{item.expenseDetailName,jdbcType=VARCHAR},  #{item.isMergeBySummary,jdbcType=INTEGER},   #{item.isMergeByBacDr,jdbcType=INTEGER},
        #{item.acbCode,jdbcType=VARCHAR},            #{item.accountAco,jdbcType=VARCHAR},         #{item.acsCode,jdbcType=VARCHAR},            #{item.acitem03Name,jdbcType=VARCHAR},
        #{item.fieldName,jdbcType=VARCHAR},          #{item.billFuncCode,jdbcType=VARCHAR},       #{item.acitem06Code,jdbcType=VARCHAR},       #{item.acitem08Name,jdbcType=VARCHAR},
        #{item.expenseName,jdbcType=VARCHAR},        #{item.atomName,jdbcType=VARCHAR},           #{item.acitem08Code,jdbcType=VARCHAR},       #{item.summaryRuleContent,jdbcType=VARCHAR},
        #{item.billFuncName,jdbcType=VARCHAR},       #{item.acitem09Name,jdbcType=VARCHAR},       #{item.acitem07Code,jdbcType=VARCHAR},       #{item.isMergeByBacCr,jdbcType=INTEGER},
        #{item.acitem06Name,jdbcType=VARCHAR},       #{item.ord,jdbcType=INTEGER},                #{item.settlementType,jdbcType=VARCHAR},     #{item.fieldCode,jdbcType=VARCHAR},
        #{item.mofDivCode,jdbcType=VARCHAR},         #{item.acitem09Code,jdbcType=VARCHAR},       #{item.agyCode,jdbcType=VARCHAR},
        #{item.expenseDetailCode,jdbcType=VARCHAR},  #{item.atomCode,jdbcType=VARCHAR},           #{item.ecsBizTypeName,jdbcType=VARCHAR},     #{item.acitem07Name,jdbcType=VARCHAR},
        #{item.accountAcoName,jdbcType=VARCHAR},     #{item.acitem01Code,jdbcType=VARCHAR},       #{item.drType,jdbcType=VARCHAR},             #{item.isMergeByFacDr,jdbcType=INTEGER}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="acitem10Code != null and acitem10Code != ''">
                acitem10_code=#{acitem10Code,jdbcType=VARCHAR},
            </if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">
                pay_account_type_name=#{payAccountTypeName,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                ecs_biz_type_code=#{ecsBizTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                acitem02_code=#{acitem02Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                acitem04_name=#{acitem04Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                acitem03_code=#{acitem03Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                acitem10_name=#{acitem10Name,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByFacCr != null">
                is_merge_by_fac_cr=#{isMergeByFacCr,jdbcType=INTEGER},
            </if>
            <if test="acbName != null and acbName != ''">
                acb_name=#{acbName,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                acitem05_name=#{acitem05Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                acitem01_name=#{acitem01Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                acitem04_code=#{acitem04Code,jdbcType=VARCHAR},
            </if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">
                pay_account_type_code=#{payAccountTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                acitem02_name=#{acitem02Name,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code=#{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="acaCode != null and acaCode != ''">
                aca_code=#{acaCode,jdbcType=VARCHAR},
            </if>
            <if test="drCr != null">
                dr_cr=#{drCr,jdbcType=INTEGER},
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                acitem05_code=#{acitem05Code,jdbcType=VARCHAR},
            </if>
            <if test="settlementName != null and settlementName != ''">
                settlement_name=#{settlementName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="expenseDetailName != null and expenseDetailName != ''">
                expense_detail_name=#{expenseDetailName,jdbcType=VARCHAR},
            </if>
            <if test="isMergeBySummary != null">
                is_merge_by_summary=#{isMergeBySummary,jdbcType=INTEGER},
            </if>
            <if test="isMergeByBacDr != null">
                is_merge_by_bac_dr=#{isMergeByBacDr,jdbcType=INTEGER},
            </if>
            <if test="acbCode != null and acbCode != ''">
                acb_code=#{acbCode,jdbcType=VARCHAR},
            </if>
            <if test="accountAco != null and accountAco != ''">
                account_aco=#{accountAco,jdbcType=VARCHAR},
            </if>
            <if test="acsCode != null and acsCode != ''">
                acs_code=#{acsCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                acitem03_name=#{acitem03Name,jdbcType=VARCHAR},
            </if>
            <if test="fieldName != null and fieldName != ''">
                field_name=#{fieldName,jdbcType=VARCHAR},
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                bill_func_code=#{billFuncCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                acitem06_code=#{acitem06Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                acitem08_name=#{acitem08Name,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name=#{expenseName,jdbcType=VARCHAR},
            </if>
            <if test="atomName != null and atomName != ''">
                atom_name=#{atomName,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                acitem08_code=#{acitem08Code,jdbcType=VARCHAR},
            </if>
            <if test="summaryRuleContent != null and summaryRuleContent != ''">
                summary_rule_content=#{summaryRuleContent,jdbcType=VARCHAR},
            </if>
            <if test="billFuncName != null and billFuncName != ''">
                bill_func_name=#{billFuncName,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                acitem09_name=#{acitem09Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                acitem07_code=#{acitem07Code,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByBacCr != null">
                is_merge_by_bac_cr=#{isMergeByBacCr,jdbcType=INTEGER},
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                acitem06_name=#{acitem06Name,jdbcType=VARCHAR},
            </if>
            <if test="ord != null">
                ord=#{ord,jdbcType=INTEGER},
            </if>
            <if test="settlementType != null and settlementType != ''">
                settlement_type=#{settlementType,jdbcType=VARCHAR},
            </if>
            <if test="fieldCode != null and fieldCode != ''">
                field_code=#{fieldCode,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                acitem09_code=#{acitem09Code,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseDetailCode != null and expenseDetailCode != ''">
                expense_detail_code=#{expenseDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="atomCode != null and atomCode != ''">
                atom_code=#{atomCode,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                ecs_biz_type_name=#{ecsBizTypeName,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                acitem07_name=#{acitem07Name,jdbcType=VARCHAR},
            </if>
            <if test="accountAcoName != null and accountAcoName != ''">
                account_aco_name=#{accountAcoName,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                acitem01_code=#{acitem01Code,jdbcType=VARCHAR},
            </if>
            <if test="drType != null and drType != ''">
                dr_type=#{drType,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByFacDr != null">
                is_merge_by_fac_dr=#{isMergeByFacDr,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="acitem10Code != null and acitem10Code != ''">
                acitem10_code,
            </if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">
                pay_account_type_name,
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                ecs_biz_type_code,
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                acitem02_code,
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                acitem04_name,
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                acitem03_code,
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                acitem10_name,
            </if>
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="isMergeByFacCr != null">
                is_merge_by_fac_cr,
            </if>
            <if test="acbName != null and acbName != ''">
                acb_name,
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                acitem05_name,
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                acitem01_name,
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                acitem04_code,
            </if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">
                pay_account_type_code,
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                acitem02_name,
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code,
            </if>
            <if test="acaCode != null and acaCode != ''">
                aca_code,
            </if>
            <if test="drCr != null">
                dr_cr,
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                acitem05_code,
            </if>
            <if test="settlementName != null and settlementName != ''">
                settlement_name,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="expenseDetailName != null and expenseDetailName != ''">
                expense_detail_name,
            </if>
            <if test="isMergeBySummary != null">
                is_merge_by_summary,
            </if>
            <if test="isMergeByBacDr != null">
                is_merge_by_bac_dr,
            </if>
            <if test="acbCode != null and acbCode != ''">
                acb_code,
            </if>
            <if test="accountAco != null and accountAco != ''">
                account_aco,
            </if>
            <if test="acsCode != null and acsCode != ''">
                acs_code,
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                acitem03_name,
            </if>
            <if test="fieldName != null and fieldName != ''">
                field_name,
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                bill_func_code,
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                acitem06_code,
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                acitem08_name,
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name,
            </if>
            <if test="atomName != null and atomName != ''">
                atom_name,
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                acitem08_code,
            </if>
            <if test="summaryRuleContent != null and summaryRuleContent != ''">
                summary_rule_content,
            </if>
            <if test="billFuncName != null and billFuncName != ''">
                bill_func_name,
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                acitem09_name,
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                acitem07_code,
            </if>
            <if test="isMergeByBacCr != null">
                is_merge_by_bac_cr,
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                acitem06_name,
            </if>
            <if test="ord != null">
                ord,
            </if>
            <if test="settlementType != null and settlementType != ''">
                settlement_type,
            </if>
            <if test="fieldCode != null and fieldCode != ''">
                field_code,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                acitem09_code,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="expenseDetailCode != null and expenseDetailCode != ''">
                expense_detail_code,
            </if>
            <if test="atomCode != null and atomCode != ''">
                atom_code,
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                ecs_biz_type_name,
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                acitem07_name,
            </if>
            <if test="accountAcoName != null and accountAcoName != ''">
                account_aco_name,
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                acitem01_code,
            </if>
            <if test="drType != null and drType != ''">
                dr_type,
            </if>
            <if test="isMergeByFacDr != null">
                is_merge_by_fac_dr,
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="acitem10Code != null and acitem10Code != ''">
                #{acitem10Code,jdbcType=VARCHAR},
            </if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">
                #{payAccountTypeName,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                #{ecsBizTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                #{acitem02Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                #{acitem04Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                #{acitem03Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                #{acitem10Name,jdbcType=VARCHAR},
            </if>
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByFacCr != null">
                #{isMergeByFacCr,jdbcType=INTEGER},
            </if>
            <if test="acbName != null and acbName != ''">
                #{acbName,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                #{acitem05Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                #{acitem01Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                #{acitem04Code,jdbcType=VARCHAR},
            </if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">
                #{payAccountTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                #{acitem02Name,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                #{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="acaCode != null and acaCode != ''">
                #{acaCode,jdbcType=VARCHAR},
            </if>
            <if test="drCr != null">
                #{drCr,jdbcType=INTEGER},
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                #{acitem05Code,jdbcType=VARCHAR},
            </if>
            <if test="settlementName != null and settlementName != ''">
                #{settlementName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="expenseDetailName != null and expenseDetailName != ''">
                #{expenseDetailName,jdbcType=VARCHAR},
            </if>
            <if test="isMergeBySummary != null">
                #{isMergeBySummary,jdbcType=INTEGER},
            </if>
            <if test="isMergeByBacDr != null">
                #{isMergeByBacDr,jdbcType=INTEGER},
            </if>
            <if test="acbCode != null and acbCode != ''">
                #{acbCode,jdbcType=VARCHAR},
            </if>
            <if test="accountAco != null and accountAco != ''">
                #{accountAco,jdbcType=VARCHAR},
            </if>
            <if test="acsCode != null and acsCode != ''">
                #{acsCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                #{acitem03Name,jdbcType=VARCHAR},
            </if>
            <if test="fieldName != null and fieldName != ''">
                #{fieldName,jdbcType=VARCHAR},
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                #{billFuncCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                #{acitem06Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                #{acitem08Name,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                #{expenseName,jdbcType=VARCHAR},
            </if>
            <if test="atomName != null and atomName != ''">
                #{atomName,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                #{acitem08Code,jdbcType=VARCHAR},
            </if>
            <if test="summaryRuleContent != null and summaryRuleContent != ''">
                #{summaryRuleContent,jdbcType=VARCHAR},
            </if>
            <if test="billFuncName != null and billFuncName != ''">
                #{billFuncName,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                #{acitem09Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                #{acitem07Code,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByBacCr != null">
                #{isMergeByBacCr,jdbcType=INTEGER},
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                #{acitem06Name,jdbcType=VARCHAR},
            </if>
            <if test="ord != null">
                #{ord,jdbcType=INTEGER},
            </if>
            <if test="settlementType != null and settlementType != ''">
                #{settlementType,jdbcType=VARCHAR},
            </if>
            <if test="fieldCode != null and fieldCode != ''">
                #{fieldCode,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                #{acitem09Code,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseDetailCode != null and expenseDetailCode != ''">
                #{expenseDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="atomCode != null and atomCode != ''">
                #{atomCode,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                #{ecsBizTypeName,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                #{acitem07Name,jdbcType=VARCHAR},
            </if>
            <if test="accountAcoName != null and accountAcoName != ''">
                #{accountAcoName,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                #{acitem01Code,jdbcType=VARCHAR},
            </if>
            <if test="drType != null and drType != ''">
                #{drType,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByFacDr != null">
                #{isMergeByFacDr,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="acitem10Code != null and acitem10Code != ''">
                acitem10_code,
            </if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">
                pay_account_type_name,
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                ecs_biz_type_code,
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                acitem02_code,
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                acitem04_name,
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                acitem03_code,
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                acitem10_name,
            </if>
            <if test="isMergeByFacCr != null">
                is_merge_by_fac_cr,
            </if>
            <if test="acbName != null and acbName != ''">
                acb_name,
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                acitem05_name,
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                acitem01_name,
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                acitem04_code,
            </if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">
                pay_account_type_code,
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                acitem02_name,
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code,
            </if>
            <if test="acaCode != null and acaCode != ''">
                aca_code,
            </if>
            <if test="drCr != null">
                dr_cr,
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                acitem05_code,
            </if>
            <if test="settlementName != null and settlementName != ''">
                settlement_name,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="expenseDetailName != null and expenseDetailName != ''">
                expense_detail_name,
            </if>
            <if test="isMergeBySummary != null">
                is_merge_by_summary,
            </if>
            <if test="isMergeByBacDr != null">
                is_merge_by_bac_dr,
            </if>
            <if test="acbCode != null and acbCode != ''">
                acb_code,
            </if>
            <if test="accountAco != null and accountAco != ''">
                account_aco,
            </if>
            <if test="acsCode != null and acsCode != ''">
                acs_code,
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                acitem03_name,
            </if>
            <if test="fieldName != null and fieldName != ''">
                field_name,
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                bill_func_code,
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                acitem06_code,
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                acitem08_name,
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name,
            </if>
            <if test="atomName != null and atomName != ''">
                atom_name,
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                acitem08_code,
            </if>
            <if test="summaryRuleContent != null and summaryRuleContent != ''">
                summary_rule_content,
            </if>
            <if test="billFuncName != null and billFuncName != ''">
                bill_func_name,
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                acitem09_name,
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                acitem07_code,
            </if>
            <if test="isMergeByBacCr != null">
                is_merge_by_bac_cr,
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                acitem06_name,
            </if>
            <if test="ord != null">
                ord,
            </if>
            <if test="settlementType != null and settlementType != ''">
                settlement_type,
            </if>
            <if test="fieldCode != null and fieldCode != ''">
                field_code,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                acitem09_code,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="expenseDetailCode != null and expenseDetailCode != ''">
                expense_detail_code,
            </if>
            <if test="atomCode != null and atomCode != ''">
                atom_code,
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                ecs_biz_type_name,
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                acitem07_name,
            </if>
            <if test="accountAcoName != null and accountAcoName != ''">
                account_aco_name,
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                acitem01_code,
            </if>
            <if test="drType != null and drType != ''">
                dr_type,
            </if>
            <if test="isMergeByFacDr != null">
                is_merge_by_fac_dr,
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveValue">
        <trim suffixOverrides=",">
            <if test="acitem10Code != null and acitem10Code != ''">
                #{acitem10Code,jdbcType=VARCHAR},
            </if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">
                #{payAccountTypeName,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                #{ecsBizTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                #{acitem02Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                #{acitem04Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                #{acitem03Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                #{acitem10Name,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByFacCr != null">
                #{isMergeByFacCr,jdbcType=INTEGER},
            </if>
            <if test="acbName != null and acbName != ''">
                #{acbName,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                #{acitem05Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                #{acitem01Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                #{acitem04Code,jdbcType=VARCHAR},
            </if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">
                #{payAccountTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                #{acitem02Name,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                #{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="acaCode != null and acaCode != ''">
                #{acaCode,jdbcType=VARCHAR},
            </if>
            <if test="drCr != null">
                #{drCr,jdbcType=INTEGER},
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                #{acitem05Code,jdbcType=VARCHAR},
            </if>
            <if test="settlementName != null and settlementName != ''">
                #{settlementName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="expenseDetailName != null and expenseDetailName != ''">
                #{expenseDetailName,jdbcType=VARCHAR},
            </if>
            <if test="isMergeBySummary != null">
                #{isMergeBySummary,jdbcType=INTEGER},
            </if>
            <if test="isMergeByBacDr != null">
                #{isMergeByBacDr,jdbcType=INTEGER},
            </if>
            <if test="acbCode != null and acbCode != ''">
                #{acbCode,jdbcType=VARCHAR},
            </if>
            <if test="accountAco != null and accountAco != ''">
                #{accountAco,jdbcType=VARCHAR},
            </if>
            <if test="acsCode != null and acsCode != ''">
                #{acsCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                #{acitem03Name,jdbcType=VARCHAR},
            </if>
            <if test="fieldName != null and fieldName != ''">
                #{fieldName,jdbcType=VARCHAR},
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                #{billFuncCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                #{acitem06Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                #{acitem08Name,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                #{expenseName,jdbcType=VARCHAR},
            </if>
            <if test="atomName != null and atomName != ''">
                #{atomName,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                #{acitem08Code,jdbcType=VARCHAR},
            </if>
            <if test="summaryRuleContent != null and summaryRuleContent != ''">
                #{summaryRuleContent,jdbcType=VARCHAR},
            </if>
            <if test="billFuncName != null and billFuncName != ''">
                #{billFuncName,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                #{acitem09Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                #{acitem07Code,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByBacCr != null">
                #{isMergeByBacCr,jdbcType=INTEGER},
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                #{acitem06Name,jdbcType=VARCHAR},
            </if>
            <if test="ord != null">
                #{ord,jdbcType=INTEGER},
            </if>
            <if test="settlementType != null and settlementType != ''">
                #{settlementType,jdbcType=VARCHAR},
            </if>
            <if test="fieldCode != null and fieldCode != ''">
                #{fieldCode,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                #{acitem09Code,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseDetailCode != null and expenseDetailCode != ''">
                #{expenseDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="atomCode != null and atomCode != ''">
                #{atomCode,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                #{ecsBizTypeName,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                #{acitem07Name,jdbcType=VARCHAR},
            </if>
            <if test="accountAcoName != null and accountAcoName != ''">
                #{accountAcoName,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                #{acitem01Code,jdbcType=VARCHAR},
            </if>
            <if test="drType != null and drType != ''">
                #{drType,jdbcType=VARCHAR},
            </if>
            <if test="isMergeByFacDr != null">
                #{isMergeByFacDr,jdbcType=INTEGER},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="acitem10Code != null and acitem10Code != ''">
            AND acitem10_code=#{acitem10Code,jdbcType=VARCHAR}
        </if>
        <if test="payAccountTypeName != null and payAccountTypeName != ''">
            AND pay_account_type_name=#{payAccountTypeName,jdbcType=VARCHAR}
        </if>
        <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
            AND ecs_biz_type_code=#{ecsBizTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="acitem02Code != null and acitem02Code != ''">
            AND acitem02_code=#{acitem02Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem04Name != null and acitem04Name != ''">
            AND acitem04_name=#{acitem04Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem03Code != null and acitem03Code != ''">
            AND acitem03_code=#{acitem03Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem10Name != null and acitem10Name != ''">
            AND acitem10_name=#{acitem10Name,jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="isMergeByFacCr != null">
            AND is_merge_by_fac_cr=#{isMergeByFacCr,jdbcType=INTEGER}
        </if>
        <if test="acbName != null and acbName != ''">
            AND acb_name=#{acbName,jdbcType=VARCHAR}
        </if>
        <if test="acitem05Name != null and acitem05Name != ''">
            AND acitem05_name=#{acitem05Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem01Name != null and acitem01Name != ''">
            AND acitem01_name=#{acitem01Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem04Code != null and acitem04Code != ''">
            AND acitem04_code=#{acitem04Code,jdbcType=VARCHAR}
        </if>
        <if test="payAccountTypeCode != null and payAccountTypeCode != ''">
            AND pay_account_type_code=#{payAccountTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="acitem02Name != null and acitem02Name != ''">
            AND acitem02_name=#{acitem02Name,jdbcType=VARCHAR}
        </if>
        <if test="expenseCode != null and expenseCode != ''">
            AND expense_code=#{expenseCode,jdbcType=VARCHAR}
        </if>
        <if test="acaCode != null and acaCode != ''">
            AND aca_code=#{acaCode,jdbcType=VARCHAR}
        </if>
        <if test="drCr != null">
            AND dr_cr=#{drCr,jdbcType=INTEGER}
        </if>
        <if test="acitem05Code != null and acitem05Code != ''">
            AND acitem05_code=#{acitem05Code,jdbcType=VARCHAR}
        </if>
        <if test="settlementName != null and settlementName != ''">
            AND settlement_name=#{settlementName,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="expenseDetailName != null and expenseDetailName != ''">
            AND expense_detail_name=#{expenseDetailName,jdbcType=VARCHAR}
        </if>
        <if test="isMergeBySummary != null">
            AND is_merge_by_summary=#{isMergeBySummary,jdbcType=INTEGER}
        </if>
        <if test="isMergeByBacDr != null">
            AND is_merge_by_bac_dr=#{isMergeByBacDr,jdbcType=INTEGER}
        </if>
        <if test="acbCode != null and acbCode != ''">
            AND acb_code=#{acbCode,jdbcType=VARCHAR}
        </if>
        <if test="accountAco != null and accountAco != ''">
            AND account_aco=#{accountAco,jdbcType=VARCHAR}
        </if>
        <if test="acsCode != null and acsCode != ''">
            AND acs_code=#{acsCode,jdbcType=VARCHAR}
        </if>
        <if test="acitem03Name != null and acitem03Name != ''">
            AND acitem03_name=#{acitem03Name,jdbcType=VARCHAR}
        </if>
        <if test="fieldName != null and fieldName != ''">
            AND field_name=#{fieldName,jdbcType=VARCHAR}
        </if>
        <if test="billFuncCode != null and billFuncCode != ''">
            AND bill_func_code=#{billFuncCode,jdbcType=VARCHAR}
        </if>
        <if test="acitem06Code != null and acitem06Code != ''">
            AND acitem06_code=#{acitem06Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem08Name != null and acitem08Name != ''">
            AND acitem08_name=#{acitem08Name,jdbcType=VARCHAR}
        </if>
        <if test="expenseName != null and expenseName != ''">
            AND expense_name=#{expenseName,jdbcType=VARCHAR}
        </if>
        <if test="atomName != null and atomName != ''">
            AND atom_name=#{atomName,jdbcType=VARCHAR}
        </if>
        <if test="acitem08Code != null and acitem08Code != ''">
            AND acitem08_code=#{acitem08Code,jdbcType=VARCHAR}
        </if>
        <if test="summaryRuleContent != null and summaryRuleContent != ''">
            AND summary_rule_content=#{summaryRuleContent,jdbcType=VARCHAR}
        </if>
        <if test="billFuncName != null and billFuncName != ''">
            AND bill_func_name=#{billFuncName,jdbcType=VARCHAR}
        </if>
        <if test="acitem09Name != null and acitem09Name != ''">
            AND acitem09_name=#{acitem09Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem07Code != null and acitem07Code != ''">
            AND acitem07_code=#{acitem07Code,jdbcType=VARCHAR}
        </if>
        <if test="isMergeByBacCr != null">
            AND is_merge_by_bac_cr=#{isMergeByBacCr,jdbcType=INTEGER}
        </if>
        <if test="acitem06Name != null and acitem06Name != ''">
            AND acitem06_name=#{acitem06Name,jdbcType=VARCHAR}
        </if>
        <if test="ord != null">
            AND ord=#{ord,jdbcType=INTEGER}
        </if>
        <if test="settlementType != null and settlementType != ''">
            AND settlement_type=#{settlementType,jdbcType=VARCHAR}
        </if>
        <if test="fieldCode != null and fieldCode != ''">
            AND field_code=#{fieldCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="acitem09Code != null and acitem09Code != ''">
            AND acitem09_code=#{acitem09Code,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="expenseDetailCode != null and expenseDetailCode != ''">
            AND expense_detail_code=#{expenseDetailCode,jdbcType=VARCHAR}
        </if>
        <if test="atomCode != null and atomCode != ''">
            AND atom_code=#{atomCode,jdbcType=VARCHAR}
        </if>
        <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
            AND ecs_biz_type_name=#{ecsBizTypeName,jdbcType=VARCHAR}
        </if>
        <if test="acitem07Name != null and acitem07Name != ''">
            AND acitem07_name=#{acitem07Name,jdbcType=VARCHAR}
        </if>
        <if test="accountAcoName != null and accountAcoName != ''">
            AND account_aco_name=#{accountAcoName,jdbcType=VARCHAR}
        </if>
        <if test="acitem01Code != null and acitem01Code != ''">
            AND acitem01_code=#{acitem01Code,jdbcType=VARCHAR}
        </if>
        <if test="drType != null and drType != ''">
            AND dr_type=#{drType,jdbcType=VARCHAR}
        </if>
        <if test="isMergeByFacDr != null">
            AND is_merge_by_fac_dr=#{isMergeByFacDr,jdbcType=INTEGER}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.rule.PcxVouRule">
        INSERT INTO pcx_vou_rule (
        <include refid="allColumn" />
        ) VALUES (
        <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.rule.PcxVouRule">
        INSERT INTO pcx_vou_rule (
        <include refid="insertSelectiveColumn" />
        ) VALUES (
        <include refid="insertSelectiveValue" />
        )
    </insert>

    <delete id="delById" parameterType="string">
        DELETE FROM pcx_vou_rule
        WHERE
            id=#{value}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_vou_rule
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.rule.PcxVouRule">
        DELETE FROM pcx_vou_rule
        WHERE 1=1
        <include refid="allColumnCond" />
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.rule.PcxVouRule">
        UPDATE pcx_vou_rule SET
        <include refid="allColumnSet" />
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.rule.PcxVouRule">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_vou_rule
        WHERE id=#{value}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.rule.PcxVouRule">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_vou_rule
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.rule.PcxVouRule" resultType="com.pty.pcx.vo.rule.PcxVouRuleVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_vou_rule
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.rule.PcxVouRule" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_vou_rule
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>

    <insert id="batchInsert" parameterType="java.util.List" databaseId="mysql">
        INSERT INTO pcx_vou_rule (
        <include refid="allColumn"/>
        )
        VALUES
        <foreach collection="list" item="item" separator="," index="index">
            (<include refid="allColumnItemValue"/>)
        </foreach>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List" databaseId="oracle">
        INSERT INTO pcx_vou_rule (
        <include refid="allColumn"/>
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            ( select
            <include refid="allColumnItemValue"/>
            from DUAL )
        </foreach>
    </insert>

    <delete id="delByQO" parameterType="com.pty.pcx.qo.rule.PcxVouRuleQO">
        DELETE FROM pcx_vou_rule
        WHERE fiscal=#{fiscal,jdbcType=VARCHAR}
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
            AND acb_code=#{acbCode,jdbcType=VARCHAR}
            <if test="billFuncCode != null and billFuncCode != ''">
                AND bill_func_code=#{billFuncCode,jdbcType=VARCHAR}
            </if>
            <if test="drCr != null">
                AND dr_cr=#{drCr,jdbcType=INTEGER}
            </if>
            <if test="acsCode != null and acsCode != ''">
                AND acs_code=#{acsCode,jdbcType=VARCHAR}
            </if>
    </delete>
</mapper>