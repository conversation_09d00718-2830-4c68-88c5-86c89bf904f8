<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillExpStandResultDao">

    <select id="getBillStandResultByBillId" resultType="com.pty.pcx.entity.bill.PcxBillExpStandResult">
        select * from pcx_bill_exp_stand_result where bill_id = #{billId}
    </select>
</mapper>
