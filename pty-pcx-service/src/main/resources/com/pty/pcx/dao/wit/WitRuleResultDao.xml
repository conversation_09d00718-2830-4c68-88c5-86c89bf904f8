<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.wit.WitRuleResultDao">
    <sql id="allColumn">
        ref_block, creator_name, audit_type, total_count,
        bill_status, created_time, id, bill_no,
        ok_count, creator, err_count, mof_div_code,
        agy_code, warn_count, bill_func, bill_amt,
        field1, agy_name, fiscal, bill_id,
        field3, field2, field5, field4
    </sql>

    <sql id="allColumnAlias">
        ref_block as refBlock, creator_name as creatorName, audit_type as auditType, total_count as totalCount,
        bill_status as billStatus, created_time as createdTime, id as id, bill_no as billNo,
        ok_count as okCount, creator as creator, err_count as errCount, mof_div_code as mofDivCode,
        agy_code as agyCode, warn_count as warnCount, bill_func as billFunc, bill_amt as billAmt,
        field1 as field1, agy_name as agyName, fiscal as fiscal, bill_id as billId,
        field3 as field3, field2 as field2, field5 as field5, field4 as field4
    </sql>

    <sql id="allColumnValue">
        #{refBlock,jdbcType=VARCHAR}, #{creatorName,jdbcType=VARCHAR}, #{auditType,jdbcType=VARCHAR},
        #{totalCount,jdbcType=INTEGER},
        #{billStatus,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR},
        #{billNo,jdbcType=VARCHAR},
        #{okCount,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, #{errCount,jdbcType=INTEGER},
        #{mofDivCode,jdbcType=VARCHAR},
        #{agyCode,jdbcType=VARCHAR}, #{warnCount,jdbcType=INTEGER}, #{billFunc,jdbcType=VARCHAR},
        #{billAmt,jdbcType=DECIMAL},
        #{field1,jdbcType=VARCHAR}, #{agyName,jdbcType=VARCHAR}, #{fiscal,jdbcType=VARCHAR}, #{billId,jdbcType=VARCHAR},
        #{field3,jdbcType=VARCHAR}, #{field2,jdbcType=VARCHAR}, #{field5,jdbcType=VARCHAR}, #{field4,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="refBlock != null and refBlock != ''">
                ref_block=#{refBlock,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name=#{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="auditType != null and auditType != ''">
                audit_type=#{auditType,jdbcType=VARCHAR},
            </if>
            <if test="totalCount != null">
                total_count=#{totalCount,jdbcType=INTEGER},
            </if>
            <if test="billStatus != null and billStatus != ''">
                bill_status=#{billStatus,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time=#{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="billNo != null and billNo != ''">
                bill_no=#{billNo,jdbcType=VARCHAR},
            </if>
            <if test="okCount != null">
                ok_count=#{okCount,jdbcType=INTEGER},
            </if>
            <if test="creator != null and creator != ''">
                creator=#{creator,jdbcType=VARCHAR},
            </if>
            <if test="errCount != null">
                err_count=#{errCount,jdbcType=INTEGER},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="warnCount != null">
                warn_count=#{warnCount,jdbcType=INTEGER},
            </if>
            <if test="billFunc != null and billFunc != ''">
                bill_func=#{billFunc,jdbcType=VARCHAR},
            </if>
            <if test="billAmt != null">
                bill_amt=#{billAmt,jdbcType=DECIMAL},
            </if>
            <if test="field1 != null and field1 != ''">
                field1=#{field1,jdbcType=VARCHAR},
            </if>
            <if test="agyName != null and agyName != ''">
                agy_name=#{agyName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="billId != null and billId != ''">
                bill_id=#{billId,jdbcType=VARCHAR},
            </if>
            <if test="field3 != null and field3 != ''">
                field3=#{field3,jdbcType=VARCHAR},
            </if>
            <if test="field2 != null and field2 != ''">
                field2=#{field2,jdbcType=VARCHAR},
            </if>
            <if test="field5 != null and field5 != ''">
                field5=#{field5,jdbcType=VARCHAR},
            </if>
            <if test="field4 != null and field4 != ''">
                field4=#{field4,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="refBlock != null and refBlock != ''">
            AND ref_block=#{refBlock,jdbcType=VARCHAR}
        </if>
        <if test="refBlocks != null and refBlocks.size() > 0">
            <foreach collection="refBlocks" item="refBlock" open="AND ref_block IN (" close=")" separator=",">
                #{refBlock}
            </foreach>
        </if>
        <if test="creatorName != null and creatorName != ''">
            AND creator_name=#{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="auditType != null and auditType != ''">
            AND audit_type=#{auditType,jdbcType=VARCHAR}
        </if>
        <if test="totalCount != null">
            AND total_count=#{totalCount,jdbcType=INTEGER}
        </if>
        <if test="billStatus != null and billStatus != ''">
            AND bill_status=#{billStatus,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time=#{createdTime,jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="billNo != null and billNo != ''">
            AND bill_no=#{billNo,jdbcType=VARCHAR}
        </if>
        <if test="okCount != null">
            AND ok_count=#{okCount,jdbcType=INTEGER}
        </if>
        <if test="errCount != null">
            AND err_count=#{errCount,jdbcType=INTEGER}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="warnCount != null">
            AND warn_count=#{warnCount,jdbcType=INTEGER}
        </if>
        <if test="billFunc != null and billFunc != ''">
            AND bill_func=#{billFunc,jdbcType=VARCHAR}
        </if>
        <if test="billAmt != null">
            AND bill_amt=#{billAmt,jdbcType=DECIMAL}
        </if>
        <if test="field1 != null and field1 != ''">
            AND field1=#{field1,jdbcType=VARCHAR}
        </if>
        <if test="agyName != null and agyName != ''">
            AND agy_name=#{agyName,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="billId != null and billId != ''">
            AND bill_id=#{billId,jdbcType=VARCHAR}
        </if>
        <if test="field3 != null and field3 != ''">
            AND field3=#{field3,jdbcType=VARCHAR}
        </if>
        <if test="field2 != null and field2 != ''">
            AND field2=#{field2,jdbcType=VARCHAR}
        </if>
        <if test="field5 != null and field5 != ''">
            AND field5=#{field5,jdbcType=VARCHAR}
        </if>
        <if test="field4 != null and field4 != ''">
            AND field4=#{field4,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.wit.WitRuleResult">
        INSERT INTO pty_rule_result (
        <include refid="allColumn"/>
        ) VALUES (
        <include refid="allColumnValue"/>
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        delete from pty_rule_result where id=#{value}
    </delete>
    <delete id="delByBillIdAndBlock">
        delete from pty_rule_result where bill_id=#{billId} and ref_block=#{refBlock}
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.wit.WitRuleResult">
        UPDATE pty_rule_result SET
        <include refid="allColumnSet"/>
        WHERE id=#{id}
    </update>

    <select id="select" parameterType="com.pty.pcx.entity.wit.WitRuleResult"
            resultType="com.pty.pcx.entity.wit.WitRuleResult">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pty_rule_result
        WHERE 1=1
        <include refid="allColumnCond"/>
        order by created_time desc
    </select>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.wit.WitRuleResult">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pty_rule_result
        WHERE id=#{value}
    </select>

</mapper>