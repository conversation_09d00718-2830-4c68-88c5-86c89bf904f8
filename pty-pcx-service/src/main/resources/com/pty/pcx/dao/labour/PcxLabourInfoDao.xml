<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.labour.PcxLabourInfoDao">
    <sql id="allColumn">
        id,                                               fiscal,                                           agy_code,                                         agy_name,
    user_code,                                        user_name,                                        user_fname,                                       lecturer_level,
    lecturer_level_name,                              organization,                                     id_type,                                          id_type_name,
    id_no,                                            phone_no,                                         email,                                            address,
    is_enabled,                                       account_name,                                     account_no,                                       bank_no,
    bank_name,                                        comments,                                         creator,                                          creator_name,
    created_time,                                     modifier,                                         modifier_name,                                    modified_time,
    ver,                                              input_type,                                       user_level,                                       expense_type_code,
    expense_type_name,                                field01,                                          field02,                                          field03,
    field04,                                          field05,                                          mof_div_code,                                     type_code,
    type_name,                                        labour_code,                                      bank_node_name,                                   bank_node_code

    </sql>

    <sql id="allColumnAlias">
        id as id,                                         fiscal as fiscal,                                 agy_code as agyCode,                              agy_name as agyName,
    user_code as userCode,                            user_name as userName,                            user_fname as userFname,                          lecturer_level as lecturerLevel,
    lecturer_level_name as lecturerLevelName,         organization as organization,                     id_type as idType,                                id_type_name as idTypeName,
    id_no as idNo,                                    phone_no as phoneNo,                              email as email,                                   address as address,
    is_enabled as isEnabled,                          account_name as accountName,                      account_no as accountNo,                          bank_no as bankNo,
    bank_name as bankName,                            comments as comments,                             creator as creator,                               creator_name as creatorName,
    created_time as createdTime,                      modifier as modifier,                             modifier_name as modifierName,                    modified_time as modifiedTime,
    ver as ver,                                       input_type as inputType,                          user_level as userLevel,                          expense_type_code as expenseTypeCode,
    expense_type_name as expenseTypeName,             field01 as field01,                               field02 as field02,                               field03 as field03,
    field04 as field04,                               field05 as field05,                               mof_div_code as mofDivCode,                       type_code as typeCode,
    type_name as typeName,                            labour_code as labourCode,                        bank_node_name as bankNodeName,                   bank_node_code as bankNodeCode

    </sql>

    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR},                 #{fiscal,jdbcType=VARCHAR},             #{agyCode,jdbcType=VARCHAR},            #{agyName,jdbcType=VARCHAR},
        #{userCode,jdbcType=VARCHAR},           #{userName,jdbcType=VARCHAR},           #{userFname,jdbcType=VARCHAR},          #{lecturerLevel,jdbcType=VARCHAR},
        #{lecturerLevelName,jdbcType=VARCHAR},  #{organization,jdbcType=VARCHAR},       #{idType,jdbcType=VARCHAR},             #{idTypeName,jdbcType=VARCHAR},
        #{idNo,jdbcType=VARCHAR},               #{phoneNo,jdbcType=VARCHAR},            #{email,jdbcType=VARCHAR},              #{address,jdbcType=VARCHAR},
        #{isEnabled,jdbcType=INTEGER},          #{accountName,jdbcType=VARCHAR},        #{accountNo,jdbcType=VARCHAR},          #{bankNo,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},           #{comments,jdbcType=VARCHAR},           #{creator,jdbcType=VARCHAR},            #{creatorName,jdbcType=VARCHAR},
        #{createdTime,jdbcType=VARCHAR},        #{modifier,jdbcType=VARCHAR},           #{modifierName,jdbcType=VARCHAR},       #{modifiedTime,jdbcType=VARCHAR},
        #{ver,jdbcType=INTEGER},                #{inputType,jdbcType=INTEGER},          #{userLevel,jdbcType=VARCHAR},          #{expenseTypeCode,jdbcType=VARCHAR},
        #{expenseTypeName,jdbcType=VARCHAR},    #{field01,jdbcType=VARCHAR},            #{field02,jdbcType=VARCHAR},            #{field03,jdbcType=VARCHAR},
        #{field04,jdbcType=VARCHAR},            #{field05,jdbcType=VARCHAR},            #{mofDivCode,jdbcType=VARCHAR},         #{typeCode,jdbcType=VARCHAR},
        #{typeName,jdbcType=VARCHAR},           #{labourCode,jdbcType=VARCHAR},         #{bankNodeName,jdbcType=VARCHAR},       #{bankNodeCode,jdbcType=VARCHAR}

    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id=#{id,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="agyName != null and agyName != ''">
                agy_name=#{agyName,jdbcType=VARCHAR},
            </if>
            <if test="userCode != null and userCode != ''">
                user_code=#{userCode,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                user_name=#{userName,jdbcType=VARCHAR},
            </if>
            <if test="userFname != null and userFname != ''">
                user_fname=#{userFname,jdbcType=VARCHAR},
            </if>
            <if test="lecturerLevel != null and lecturerLevel != ''">
                lecturer_level=#{lecturerLevel,jdbcType=VARCHAR},
            </if>
            <if test="lecturerLevelName != null and lecturerLevelName != ''">
                lecturer_level_name=#{lecturerLevelName,jdbcType=VARCHAR},
            </if>
            <if test="organization != null and organization != ''">
                organization=#{organization,jdbcType=VARCHAR},
            </if>
            <if test="idType != null and idType != ''">
                id_type=#{idType,jdbcType=VARCHAR},
            </if>
            <if test="idTypeName != null and idTypeName != ''">
                id_type_name=#{idTypeName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != ''">
                id_no=#{idNo,jdbcType=VARCHAR},
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                phone_no=#{phoneNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != ''">
                email=#{email,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != ''">
                address=#{address,jdbcType=VARCHAR},
            </if>
            <if test="isEnabled != null">
                is_enabled=#{isEnabled,jdbcType=INTEGER},
            </if>
            <if test="accountName != null and accountName != ''">
                account_name=#{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null and accountNo != ''">
                account_no=#{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null and bankNo != ''">
                bank_no=#{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name=#{bankName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null and comments != ''">
                comments=#{comments,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                creator=#{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name=#{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time=#{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier=#{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name=#{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time=#{modifiedTime,jdbcType=VARCHAR},
            </if>
            <if test="ver != null">
                ver=#{ver,jdbcType=INTEGER},
            </if>
            <if test="inputType != null">
                input_type=#{inputType,jdbcType=INTEGER},
            </if>
            <if test="userLevel != null and userLevel != ''">
                user_level=#{userLevel,jdbcType=VARCHAR},
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                expense_type_code=#{expenseTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseTypeName != null and expenseTypeName != ''">
                expense_type_name=#{expenseTypeName,jdbcType=VARCHAR},
            </if>
            <if test="field01 != null and field01 != ''">
                field01=#{field01,jdbcType=VARCHAR},
            </if>
            <if test="field02 != null and field02 != ''">
                field02=#{field02,jdbcType=VARCHAR},
            </if>
            <if test="field03 != null and field03 != ''">
                field03=#{field03,jdbcType=VARCHAR},
            </if>
            <if test="field04 != null and field04 != ''">
                field04=#{field04,jdbcType=VARCHAR},
            </if>
            <if test="field05 != null and field05 != ''">
                field05=#{field05,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null and typeCode != ''">
                type_code=#{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="typeName != null and typeName != ''">
                type_name=#{typeName,jdbcType=VARCHAR},
            </if>
            <if test="labourCode != null and labourCode != ''">
                labour_code=#{labourCode,jdbcType=VARCHAR},
            </if>
            <if test="bankNodeName != null and bankNodeName != ''">
                bank_node_name=#{bankNodeName,jdbcType=VARCHAR},
            </if>
            <if test="bankNodeCode != null and bankNodeCode != ''">
                bank_node_code=#{bankNodeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="agyName != null and agyName != ''">
                agy_name,
            </if>
            <if test="userCode != null and userCode != ''">
                user_code,
            </if>
            <if test="userName != null and userName != ''">
                user_name,
            </if>
            <if test="userFname != null and userFname != ''">
                user_fname,
            </if>
            <if test="lecturerLevel != null and lecturerLevel != ''">
                lecturer_level,
            </if>
            <if test="lecturerLevelName != null and lecturerLevelName != ''">
                lecturer_level_name,
            </if>
            <if test="organization != null and organization != ''">
                organization,
            </if>
            <if test="idType != null and idType != ''">
                id_type,
            </if>
            <if test="idTypeName != null and idTypeName != ''">
                id_type_name,
            </if>
            <if test="idNo != null and idNo != ''">
                id_no,
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                phone_no,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="address != null and address != ''">
                address,
            </if>
            <if test="isEnabled != null">
                is_enabled,
            </if>
            <if test="accountName != null and accountName != ''">
                account_name,
            </if>
            <if test="accountNo != null and accountNo != ''">
                account_no,
            </if>
            <if test="bankNo != null and bankNo != ''">
                bank_no,
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name,
            </if>
            <if test="comments != null and comments != ''">
                comments,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name,
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name,
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time,
            </if>
            <if test="ver != null">
                ver,
            </if>
            <if test="inputType != null">
                input_type,
            </if>
            <if test="userLevel != null and userLevel != ''">
                user_level,
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                expense_type_code,
            </if>
            <if test="expenseTypeName != null and expenseTypeName != ''">
                expense_type_name,
            </if>
            <if test="field01 != null and field01 != ''">
                field01,
            </if>
            <if test="field02 != null and field02 != ''">
                field02,
            </if>
            <if test="field03 != null and field03 != ''">
                field03,
            </if>
            <if test="field04 != null and field04 != ''">
                field04,
            </if>
            <if test="field05 != null and field05 != ''">
                field05,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="typeCode != null and typeCode != ''">
                type_code,
            </if>
            <if test="typeName != null and typeName != ''">
                type_name,
            </if>
            <if test="labourCode != null and labourCode != ''">
                labour_code,
            </if>
            <if test="bankNodeName != null and bankNodeName != ''">
                bank_node_name,
            </if>
            <if test="bankNodeCode != null and bankNodeCode != ''">
                bank_node_code,
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="agyName != null and agyName != ''">
                #{agyName,jdbcType=VARCHAR},
            </if>
            <if test="userCode != null and userCode != ''">
                #{userCode,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userFname != null and userFname != ''">
                #{userFname,jdbcType=VARCHAR},
            </if>
            <if test="lecturerLevel != null and lecturerLevel != ''">
                #{lecturerLevel,jdbcType=VARCHAR},
            </if>
            <if test="lecturerLevelName != null and lecturerLevelName != ''">
                #{lecturerLevelName,jdbcType=VARCHAR},
            </if>
            <if test="organization != null and organization != ''">
                #{organization,jdbcType=VARCHAR},
            </if>
            <if test="idType != null and idType != ''">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idTypeName != null and idTypeName != ''">
                #{idTypeName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != ''">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                #{phoneNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != ''">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != ''">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="isEnabled != null">
                #{isEnabled,jdbcType=INTEGER},
            </if>
            <if test="accountName != null and accountName != ''">
                #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null and accountNo != ''">
                #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null and bankNo != ''">
                #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != ''">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null and comments != ''">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                #{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                #{modifiedTime,jdbcType=VARCHAR},
            </if>
            <if test="ver != null">
                #{ver,jdbcType=INTEGER},
            </if>
            <if test="inputType != null">
                #{inputType,jdbcType=INTEGER},
            </if>
            <if test="userLevel != null and userLevel != ''">
                #{userLevel,jdbcType=VARCHAR},
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                #{expenseTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseTypeName != null and expenseTypeName != ''">
                #{expenseTypeName,jdbcType=VARCHAR},
            </if>
            <if test="field01 != null and field01 != ''">
                #{field01,jdbcType=VARCHAR},
            </if>
            <if test="field02 != null and field02 != ''">
                #{field02,jdbcType=VARCHAR},
            </if>
            <if test="field03 != null and field03 != ''">
                #{field03,jdbcType=VARCHAR},
            </if>
            <if test="field04 != null and field04 != ''">
                #{field04,jdbcType=VARCHAR},
            </if>
            <if test="field05 != null and field05 != ''">
                #{field05,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null and typeCode != ''">
                #{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="typeName != null and typeName != ''">
                #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="labourCode != null and labourCode != ''">
                #{labourCode,jdbcType=VARCHAR},
            </if>
            <if test="bankNodeName != null and bankNodeName != ''">
                #{bankNodeName,jdbcType=VARCHAR},
            </if>
            <if test="bankNodeCode != null and bankNodeCode != ''">
                #{bankNodeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="agyName != null and agyName != ''">
                agy_name,
            </if>
            <if test="userCode != null and userCode != ''">
                user_code,
            </if>
            <if test="userName != null and userName != ''">
                user_name,
            </if>
            <if test="userFname != null and userFname != ''">
                user_fname,
            </if>
            <if test="lecturerLevel != null and lecturerLevel != ''">
                lecturer_level,
            </if>
            <if test="lecturerLevelName != null and lecturerLevelName != ''">
                lecturer_level_name,
            </if>
            <if test="organization != null and organization != ''">
                organization,
            </if>
            <if test="idType != null and idType != ''">
                id_type,
            </if>
            <if test="idTypeName != null and idTypeName != ''">
                id_type_name,
            </if>
            <if test="idNo != null and idNo != ''">
                id_no,
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                phone_no,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="address != null and address != ''">
                address,
            </if>
            <if test="isEnabled != null">
                is_enabled,
            </if>
            <if test="accountName != null and accountName != ''">
                account_name,
            </if>
            <if test="accountNo != null and accountNo != ''">
                account_no,
            </if>
            <if test="bankNo != null and bankNo != ''">
                bank_no,
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name,
            </if>
            <if test="comments != null and comments != ''">
                comments,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name,
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name,
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time,
            </if>
            <if test="ver != null">
                ver,
            </if>
            <if test="inputType != null">
                input_type,
            </if>
            <if test="userLevel != null and userLevel != ''">
                user_level,
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                expense_type_code,
            </if>
            <if test="expenseTypeName != null and expenseTypeName != ''">
                expense_type_name,
            </if>
            <if test="field01 != null and field01 != ''">
                field01,
            </if>
            <if test="field02 != null and field02 != ''">
                field02,
            </if>
            <if test="field03 != null and field03 != ''">
                field03,
            </if>
            <if test="field04 != null and field04 != ''">
                field04,
            </if>
            <if test="field05 != null and field05 != ''">
                field05,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="typeCode != null and typeCode != ''">
                type_code,
            </if>
            <if test="typeName != null and typeName != ''">
                type_name,
            </if>
            <if test="labourCode != null and labourCode != ''">
                labour_code,
            </if>
            <if test="bankNodeName != null and bankNodeName != ''">
                bank_node_name,
            </if>
            <if test="bankNodeCode != null and bankNodeCode != ''">
                bank_node_code,
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveValue">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="agyName != null and agyName != ''">
                #{agyName,jdbcType=VARCHAR},
            </if>
            <if test="userCode != null and userCode != ''">
                #{userCode,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userFname != null and userFname != ''">
                #{userFname,jdbcType=VARCHAR},
            </if>
            <if test="lecturerLevel != null and lecturerLevel != ''">
                #{lecturerLevel,jdbcType=VARCHAR},
            </if>
            <if test="lecturerLevelName != null and lecturerLevelName != ''">
                #{lecturerLevelName,jdbcType=VARCHAR},
            </if>
            <if test="organization != null and organization != ''">
                #{organization,jdbcType=VARCHAR},
            </if>
            <if test="idType != null and idType != ''">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idTypeName != null and idTypeName != ''">
                #{idTypeName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != ''">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                #{phoneNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != ''">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != ''">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="isEnabled != null">
                #{isEnabled,jdbcType=INTEGER},
            </if>
            <if test="accountName != null and accountName != ''">
                #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null and accountNo != ''">
                #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null and bankNo != ''">
                #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != ''">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null and comments != ''">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                #{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                #{modifiedTime,jdbcType=VARCHAR},
            </if>
            <if test="ver != null">
                #{ver,jdbcType=INTEGER},
            </if>
            <if test="inputType != null">
                #{inputType,jdbcType=INTEGER},
            </if>
            <if test="userLevel != null and userLevel != ''">
                #{userLevel,jdbcType=VARCHAR},
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                #{expenseTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseTypeName != null and expenseTypeName != ''">
                #{expenseTypeName,jdbcType=VARCHAR},
            </if>
            <if test="field01 != null and field01 != ''">
                #{field01,jdbcType=VARCHAR},
            </if>
            <if test="field02 != null and field02 != ''">
                #{field02,jdbcType=VARCHAR},
            </if>
            <if test="field03 != null and field03 != ''">
                #{field03,jdbcType=VARCHAR},
            </if>
            <if test="field04 != null and field04 != ''">
                #{field04,jdbcType=VARCHAR},
            </if>
            <if test="field05 != null and field05 != ''">
                #{field05,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null and typeCode != ''">
                #{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="typeName != null and typeName != ''">
                #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="labourCode != null and labourCode != ''">
                #{labourCode,jdbcType=VARCHAR},
            </if>
            <if test="bankNodeName != null and bankNodeName != ''">
                #{bankNodeName,jdbcType=VARCHAR},
            </if>
            <if test="bankNodeCode != null and bankNodeCode != ''">
                #{bankNodeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="agyName != null and agyName != ''">
            AND agy_name=#{agyName,jdbcType=VARCHAR}
        </if>
        <if test="userCode != null and userCode != ''">
            AND user_code=#{userCode,jdbcType=VARCHAR}
        </if>
        <if test="userName != null and userName != ''">
            AND user_name=#{userName,jdbcType=VARCHAR}
        </if>
        <if test="userFname != null and userFname != ''">
            AND user_fname=#{userFname,jdbcType=VARCHAR}
        </if>
        <if test="lecturerLevel != null and lecturerLevel != ''">
            AND lecturer_level=#{lecturerLevel,jdbcType=VARCHAR}
        </if>
        <if test="lecturerLevelName != null and lecturerLevelName != ''">
            AND lecturer_level_name=#{lecturerLevelName,jdbcType=VARCHAR}
        </if>
        <if test="organization != null and organization != ''">
            AND organization=#{organization,jdbcType=VARCHAR}
        </if>
        <if test="idType != null and idType != ''">
            AND id_type=#{idType,jdbcType=VARCHAR}
        </if>
        <if test="idTypeName != null and idTypeName != ''">
            AND id_type_name=#{idTypeName,jdbcType=VARCHAR}
        </if>
        <if test="idNo != null and idNo != ''">
            AND id_no=#{idNo,jdbcType=VARCHAR}
        </if>
        <if test="phoneNo != null and phoneNo != ''">
            AND phone_no=#{phoneNo,jdbcType=VARCHAR}
        </if>
        <if test="email != null and email != ''">
            AND email=#{email,jdbcType=VARCHAR}
        </if>
        <if test="address != null and address != ''">
            AND address=#{address,jdbcType=VARCHAR}
        </if>
        <if test="isEnabled != null">
            AND is_enabled=#{isEnabled,jdbcType=INTEGER}
        </if>
        <if test="accountName != null and accountName != ''">
            AND account_name=#{accountName,jdbcType=VARCHAR}
        </if>
        <if test="accountNo != null and accountNo != ''">
            AND account_no=#{accountNo,jdbcType=VARCHAR}
        </if>
        <if test="bankNo != null and bankNo != ''">
            AND bank_no=#{bankNo,jdbcType=VARCHAR}
        </if>
        <if test="bankName != null and bankName != ''">
            AND bank_name=#{bankName,jdbcType=VARCHAR}
        </if>
        <if test="comments != null and comments != ''">
            AND comments=#{comments,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            AND creator=#{creator,jdbcType=VARCHAR}
        </if>
        <if test="creatorName != null and creatorName != ''">
            AND creator_name=#{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time=#{createdTime,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
            AND modifier=#{modifier,jdbcType=VARCHAR}
        </if>
        <if test="modifierName != null and modifierName != ''">
            AND modifier_name=#{modifierName,jdbcType=VARCHAR}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''">
            AND modified_time=#{modifiedTime,jdbcType=VARCHAR}
        </if>
        <if test="ver != null">
            AND ver=#{ver,jdbcType=INTEGER}
        </if>
        <if test="inputType != null">
            AND input_type=#{inputType,jdbcType=INTEGER}
        </if>
        <if test="userLevel != null and userLevel != ''">
            AND user_level=#{userLevel,jdbcType=VARCHAR}
        </if>
        <if test="expenseTypeCode != null and expenseTypeCode != ''">
            AND expense_type_code=#{expenseTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="expenseTypeName != null and expenseTypeName != ''">
            AND expense_type_name=#{expenseTypeName,jdbcType=VARCHAR}
        </if>
        <if test="field01 != null and field01 != ''">
            AND field01=#{field01,jdbcType=VARCHAR}
        </if>
        <if test="field02 != null and field02 != ''">
            AND field02=#{field02,jdbcType=VARCHAR}
        </if>
        <if test="field03 != null and field03 != ''">
            AND field03=#{field03,jdbcType=VARCHAR}
        </if>
        <if test="field04 != null and field04 != ''">
            AND field04=#{field04,jdbcType=VARCHAR}
        </if>
        <if test="field05 != null and field05 != ''">
            AND field05=#{field05,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND type_code=#{typeCode,jdbcType=VARCHAR}
        </if>
        <if test="typeName != null and typeName != ''">
            AND type_name=#{typeName,jdbcType=VARCHAR}
        </if>
        <if test="labourCode != null and labourCode != ''">
            AND labour_code=#{labourCode,jdbcType=VARCHAR}
        </if>
        <if test="bankNodeName != null and bankNodeName != ''">
            AND bank_node_name=#{bankNodeName,jdbcType=VARCHAR}
        </if>
        <if test="bankNodeCode != null and bankNodeCode != ''">
            AND bank_node_code=#{bankNodeCode,jdbcType=VARCHAR}
        </if>
        <if test="typeCodes != null and typeCodes.size>0">
            AND type_code in
            <foreach collection="typeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <delete id="delById" parameterType="string">
        DELETE FROM pcx_labour_info
        WHERE
        id=#{value,jdbcType=VARCHAR}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_labour_info
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="("
                 separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.qo.labour.PcxLabourInfoQO">
        DELETE FROM pcx_labour_info
        WHERE 1=1
        <include refid="allColumnCond"/>
    </delete>

    <select id="selectByIds" parameterType="java.util.List"
            resultType="com.pty.pcx.entity.bill.labour.PcxLabourInfo">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_labour_info
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="("
                 separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.qo.labour.PcxLabourInfoQO"
            resultType="com.pty.pcx.entity.bill.labour.PcxLabourInfo">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_labour_info
        WHERE 1=1
        <include refid="allColumnCond"/>
        <if test="keyword != null and keyword != ''">
            AND (organization like concat(concat('%',#{keyword,jdbcType=VARCHAR}),'%') or user_name like
            concat(concat('%',#{keyword,jdbcType=VARCHAR}),'%') or account_no like concat(concat('%',#{keyword,jdbcType=VARCHAR}),'%') or account_name
            like concat(concat('%',#{keyword,jdbcType=VARCHAR}),'%') or id_no like concat(concat('%',#{keyword,jdbcType=VARCHAR}),'%')
            or fiscal like concat(concat('%',#{keyword,jdbcType=VARCHAR}),'%'))
        </if>
    </select>

    <select id="selectByIdNos" parameterType="com.pty.pcx.qo.labour.PcxLabourInfoQO"
            resultType="com.pty.pcx.entity.bill.labour.PcxLabourInfo">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_labour_info
        WHERE 1=1
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="idNos != null and idNos.size > 0">
            AND id_no IN
            <foreach collection="idNos" item="idNo" open="(" separator="," close=")">
                #{idNo,jdbcType=VARCHAR,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>


    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List"
            databaseId="mysql">
        INSERT INTO pcx_labour_info (
        id,                                               fiscal,                                           agy_code,                                         agy_name,
        user_code,                                        user_name,                                        user_fname,                                       lecturer_level,
        lecturer_level_name,                              organization,                                     id_type,                                          id_type_name,
        id_no,                                            phone_no,                                         email,                                            address,
        is_enabled,                                       account_name,                                     account_no,                                       bank_no,
        bank_name,                                        comments,                                         creator,                                          creator_name,
        created_time,                                     modifier,                                         modifier_name,                                    modified_time,
        ver,                                              input_type,                                       user_level,                                       expense_type_code,
        expense_type_name,                                field01,                                          field02,                                          field03,
        field04,                                          field05,                                          mof_div_code,                                     type_code,
        type_name,                                        labour_code,                                      bank_node_name,                                   bank_node_code
        ) VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.id,jdbcType=VARCHAR},                 #{obj.fiscal,jdbcType=VARCHAR},             #{obj.agyCode,jdbcType=VARCHAR},            #{obj.agyName,jdbcType=VARCHAR},
            #{obj.userCode,jdbcType=VARCHAR},           #{obj.userName,jdbcType=VARCHAR},           #{obj.userFname,jdbcType=VARCHAR},          #{obj.lecturerLevel,jdbcType=VARCHAR},
            #{obj.lecturerLevelName,jdbcType=VARCHAR},  #{obj.organization,jdbcType=VARCHAR},       #{obj.idType,jdbcType=VARCHAR},             #{obj.idTypeName,jdbcType=VARCHAR},
            #{obj.idNo,jdbcType=VARCHAR},               #{obj.phoneNo,jdbcType=VARCHAR},            #{obj.email,jdbcType=VARCHAR},              #{obj.address,jdbcType=VARCHAR},
            #{obj.isEnabled,jdbcType=INTEGER},          #{obj.accountName,jdbcType=VARCHAR},        #{obj.accountNo,jdbcType=VARCHAR},          #{obj.bankNo,jdbcType=VARCHAR},
            #{obj.bankName,jdbcType=VARCHAR},           #{obj.comments,jdbcType=VARCHAR},           #{obj.creator,jdbcType=VARCHAR},            #{obj.creatorName,jdbcType=VARCHAR},
            #{obj.createdTime,jdbcType=VARCHAR},        #{obj.modifier,jdbcType=VARCHAR},           #{obj.modifierName,jdbcType=VARCHAR},       #{obj.modifiedTime,jdbcType=VARCHAR},
            #{obj.ver,jdbcType=INTEGER},                #{obj.inputType,jdbcType=INTEGER},          #{obj.userLevel,jdbcType=VARCHAR},          #{obj.expenseTypeCode,jdbcType=VARCHAR},
            #{obj.expenseTypeName,jdbcType=VARCHAR},    #{obj.field01,jdbcType=VARCHAR},            #{obj.field02,jdbcType=VARCHAR},            #{obj.field03,jdbcType=VARCHAR},
            #{obj.field04,jdbcType=VARCHAR},            #{obj.field05,jdbcType=VARCHAR},            #{obj.mofDivCode,jdbcType=VARCHAR},         #{obj.typeCode,jdbcType=VARCHAR},
            #{obj.typeName,jdbcType=VARCHAR},           #{obj.labourCode,jdbcType=VARCHAR},         #{obj.bankNodeName,jdbcType=VARCHAR},       #{obj.bankNodeCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertBatch" parameterType="java.util.List"
            databaseId="oracle">
        INSERT INTO pcx_labour_info(
        id,                                               fiscal,                                           agy_code,                                         agy_name,
        user_code,                                        user_name,                                        user_fname,                                       lecturer_level,
        lecturer_level_name,                              organization,                                     id_type,                                          id_type_name,
        id_no,                                            phone_no,                                         email,                                            address,
        is_enabled,                                       account_name,                                     account_no,                                       bank_no,
        bank_name,                                        comments,                                         creator,                                          creator_name,
        created_time,                                     modifier,                                         modifier_name,                                    modified_time,
        ver,                                              input_type,                                       user_level,                                       expense_type_code,
        expense_type_name,                                field01,                                          field02,                                          field03,
        field04,                                          field05,                                          mof_div_code,                                     type_code,
        type_name,                                        labour_code,                                      bank_node_name,                                   bank_node_code
        )
        <foreach item="obj" index="index" collection="list" separator="union all">
            (SELECT
            #{obj.id,jdbcType=VARCHAR},                 #{obj.fiscal,jdbcType=VARCHAR},             #{obj.agyCode,jdbcType=VARCHAR},            #{obj.agyName,jdbcType=VARCHAR},
            #{obj.userCode,jdbcType=VARCHAR},           #{obj.userName,jdbcType=VARCHAR},           #{obj.userFname,jdbcType=VARCHAR},          #{obj.lecturerLevel,jdbcType=VARCHAR},
            #{obj.lecturerLevelName,jdbcType=VARCHAR},  #{obj.organization,jdbcType=VARCHAR},       #{obj.idType,jdbcType=VARCHAR},             #{obj.idTypeName,jdbcType=VARCHAR},
            #{obj.idNo,jdbcType=VARCHAR},               #{obj.phoneNo,jdbcType=VARCHAR},            #{obj.email,jdbcType=VARCHAR},              #{obj.address,jdbcType=VARCHAR},
            #{obj.isEnabled,jdbcType=INTEGER},          #{obj.accountName,jdbcType=VARCHAR},        #{obj.accountNo,jdbcType=VARCHAR},          #{obj.bankNo,jdbcType=VARCHAR},
            #{obj.bankName,jdbcType=VARCHAR},           #{obj.comments,jdbcType=VARCHAR},           #{obj.creator,jdbcType=VARCHAR},            #{obj.creatorName,jdbcType=VARCHAR},
            #{obj.createdTime,jdbcType=VARCHAR},        #{obj.modifier,jdbcType=VARCHAR},           #{obj.modifierName,jdbcType=VARCHAR},       #{obj.modifiedTime,jdbcType=VARCHAR},
            #{obj.ver,jdbcType=INTEGER},                #{obj.inputType,jdbcType=INTEGER},          #{obj.userLevel,jdbcType=VARCHAR},          #{obj.expenseTypeCode,jdbcType=VARCHAR},
            #{obj.expenseTypeName,jdbcType=VARCHAR},    #{obj.field01,jdbcType=VARCHAR},            #{obj.field02,jdbcType=VARCHAR},            #{obj.field03,jdbcType=VARCHAR},
            #{obj.field04,jdbcType=VARCHAR},            #{obj.field05,jdbcType=VARCHAR},            #{obj.mofDivCode,jdbcType=VARCHAR},         #{obj.typeCode,jdbcType=VARCHAR},
            #{obj.typeName,jdbcType=VARCHAR},           #{obj.labourCode,jdbcType=VARCHAR},         #{obj.bankNodeName,jdbcType=VARCHAR},       #{obj.bankNodeCode,jdbcType=VARCHAR}
            FROM DUAL)
        </foreach>
    </insert>

    <update id="updateByIdNo" parameterType="com.pty.pcx.qo.labour.PcxLabourInfoQO">
        UPDATE pcx_labour_info SET
        <include refid="allColumnSet"/>
        WHERE id_no=#{idNo,jdbcType=VARCHAR} and agy_code=#{agyCode,jdbcType=VARCHAR} and mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
    </update>

    <update id="updateStatusByIds">
        UPDATE pcx_labour_info SET
        is_enabled=#{isEnabled,jdbcType=INTEGER}
        WHERE
        id IN
        <foreach collection="list" index="index" item="id" open="("
                 separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>