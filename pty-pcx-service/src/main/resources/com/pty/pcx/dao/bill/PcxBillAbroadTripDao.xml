<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillAbroadTripDao">

    <!-- 所有字段 -->
    <sql id="allColumn">
        id, bill_id, trip_expense_id, start_time, end_time, emp_code, emp_name, agy_code, fiscal,
        mof_div_code, tenant_id, trip_type, provide_food, provide_tool, currency, foreign_air,
        foreign_air_reason, remark, start_place_code, start_place_name, end_place_code, end_place_name,
        is_deleted_number
    </sql>

    <!-- 所有字段别名 -->
    <sql id="allColumnAlias">
        id as id, bill_id as billId, trip_expense_id as tripExpenseId, start_time as startTime,
        end_time as endTime, emp_code as empCode, emp_name as empName, agy_code as agyCode,
        fiscal as fiscal, mof_div_code as mofDivCode, tenant_id as tenantId, trip_type as tripType,
        provide_food as provideFood, provide_tool as provideTool, currency as currency,
        foreign_air as foreignAir, foreign_air_reason as foreignAirReason, remark as remark,
        start_place_code as startPlaceCode, start_place_name as startPlaceName,
        end_place_code as endPlaceCode, end_place_name as endPlaceName,
        is_deleted_number as isDeletedNumber
    </sql>

    <!-- 所有字段值 -->
    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR}, #{billId,jdbcType=VARCHAR}, #{tripExpenseId,jdbcType=VARCHAR},
        #{startTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR}, #{empCode,jdbcType=VARCHAR},
        #{empName,jdbcType=VARCHAR}, #{agyCode,jdbcType=VARCHAR}, #{fiscal,jdbcType=VARCHAR},
        #{mofDivCode,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{tripType,jdbcType=VARCHAR},
        #{provideFood,jdbcType=VARCHAR}, #{provideTool,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR},
        #{foreignAir,jdbcType=VARCHAR}, #{foreignAirReason,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{startPlaceCode,jdbcType=VARCHAR}, #{startPlaceName,jdbcType=VARCHAR},
        #{endPlaceCode,jdbcType=VARCHAR}, #{endPlaceName,jdbcType=VARCHAR},
        #{isDeletedNumber,jdbcType=INTEGER}
    </sql>

    <!-- 动态更新字段 -->
    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="billId != null and billId != ''">bill_id=#{billId,jdbcType=VARCHAR},</if>
            <if test="tripExpenseId != null and tripExpenseId != ''">trip_expense_id=#{tripExpenseId,jdbcType=VARCHAR},</if>
            <if test="startTime != null and startTime != ''">start_time=#{startTime,jdbcType=VARCHAR},</if>
            <if test="endTime != null and endTime != ''">end_time=#{endTime,jdbcType=VARCHAR},</if>
            <if test="empCode != null and empCode != ''">emp_code=#{empCode,jdbcType=VARCHAR},</if>
            <if test="empName != null and empName != ''">emp_name=#{empName,jdbcType=VARCHAR},</if>
            <if test="agyCode != null and agyCode != ''">agy_code=#{agyCode,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''">fiscal=#{fiscal,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code=#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="tenantId != null and tenantId != ''">tenant_id=#{tenantId,jdbcType=VARCHAR},</if>
            <if test="tripType != null and tripType != ''">trip_type=#{tripType,jdbcType=VARCHAR},</if>
            <if test="provideFood != null and provideFood != ''">provide_food=#{provideFood,jdbcType=VARCHAR},</if>
            <if test="provideTool != null and provideTool != ''">provide_tool=#{provideTool,jdbcType=VARCHAR},</if>
            <if test="currency != null and currency != ''">currency=#{currency,jdbcType=VARCHAR},</if>
            <if test="foreignAir != null and foreignAir != ''">foreign_air=#{foreignAir,jdbcType=VARCHAR},</if>
            <if test="foreignAirReason != null and foreignAirReason != ''">foreign_air_reason=#{foreignAirReason,jdbcType=VARCHAR},</if>
            <if test="remark != null and remark != ''">remark=#{remark,jdbcType=VARCHAR},</if>
            <if test="startPlaceCode != null and startPlaceCode != ''">start_place_code=#{startPlaceCode,jdbcType=VARCHAR},</if>
            <if test="startPlaceName != null and startPlaceName != ''">start_place_name=#{startPlaceName,jdbcType=VARCHAR},</if>
            <if test="endPlaceCode != null and endPlaceCode != ''">end_place_code=#{endPlaceCode,jdbcType=VARCHAR},</if>
            <if test="endPlaceName != null and endPlaceName != ''">end_place_name=#{endPlaceName,jdbcType=VARCHAR},</if>
            <if test="isDeletedNumber != null">is_deleted_number=#{isDeletedNumber,jdbcType=INTEGER},</if>
        </trim>
    </sql>


    <!-- 插入语句 -->
    <insert id="insert" parameterType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        INSERT INTO pcx_bill_abroad_trip (
        <include refid="allColumn" />
        ) VALUES (
        <include refid="allColumnValue" />
        )
    </insert>

    <!-- 条件插入语句 -->
    <insert id="insertSelective" parameterType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        INSERT INTO pcx_bill_abroad_trip (
        <include refid="allColumnSet" />
        ) VALUES (
        <include refid="allColumnValue" />
        )
    </insert>

    <!-- 删除语句 -->
    <delete id="delById" parameterType="string">
        DELETE FROM pcx_bill_abroad_trip
        WHERE id=#{value,jdbcType=VARCHAR}
    </delete>

    <!-- 批量删除语句 -->
    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_bill_abroad_trip
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 条件删除语句 -->
    <delete id="del" parameterType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        DELETE FROM pcx_bill_abroad_trip
        WHERE 1=1
        <include refid="allColumnSet" />
    </delete>

    <!-- 更新语句 -->
    <update id="updateById" parameterType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        UPDATE pcx_bill_abroad_trip
        <set>
            <include refid="allColumnSet" />
        </set>
        WHERE id=#{id,jdbcType=VARCHAR}
    </update>

    <!-- 查询单条记录 -->
    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_bill_abroad_trip
        WHERE id=#{value,jdbcType=VARCHAR}
    </select>

    <!-- 查询多条记录 -->
    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_bill_abroad_trip
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 条件查询 -->
    <select id="select" parameterType="com.pty.pcx.entity.bill.PcxBillAbroadTrip" resultType="com.pty.pcx.entity.bill.PcxBillAbroadTrip">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_bill_abroad_trip
        WHERE 1=1
        <include refid="allColumnSet" />
    </select>

    <!-- 统计记录数 -->
    <select id="count" parameterType="com.pty.pcx.entity.bill.PcxBillAbroadTrip" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_bill_abroad_trip
        WHERE 1=1
        <include refid="allColumnSet" />
    </select>
</mapper>