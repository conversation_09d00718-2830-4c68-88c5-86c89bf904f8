<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.wit.PcxAuditExpenseRelationDao">
    <sql id="allColumn">
        agy_name, fiscal, mof_div_code, agy_code,
        rule_name, id, rule_id, expense_code,
        expense_name
    </sql>

    <sql id="allColumnAlias">
        agy_name as agyName, fiscal as fiscal, mof_div_code as mofDivCode, agy_code as agyCode,
        rule_name as ruleName, id as id, rule_id as ruleId, expense_code as expenseCode,
        expense_name as expenseName
    </sql>

    <sql id="allColumnValue">
        #{agyName,jdbcType=VARCHAR}, #{fiscal,jdbcType=VARCHAR}, #{mofDivCode,jdbcType=VARCHAR},
        #{agyCode,jdbcType=VARCHAR},
        #{ruleName,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR}, #{ruleId,jdbcType=VARCHAR},
        #{expenseCode,jdbcType=VARCHAR},
        #{expenseName,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="agyName != null and agyName != ''">
                agy_name=#{agyName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                rule_name=#{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null and ruleId != ''">
                rule_id=#{ruleId,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code=#{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name=#{expenseName,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyName != null and agyName != ''">
                agy_name,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="ruleName != null and ruleName != ''">
                rule_name,
            </if>
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="ruleId != null and ruleId != ''">
                rule_id,
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code,
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name,
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyName != null and agyName != ''">
                #{agyName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null and ruleId != ''">
                #{ruleId,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                #{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                #{expenseName,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyName != null and agyName != ''">
                agy_name,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="ruleName != null and ruleName != ''">
                rule_name,
            </if>
            <if test="ruleId != null and ruleId != ''">
                rule_id,
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code,
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name,
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyName != null and agyName != ''">
                #{agyName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null and ruleId != ''">
                #{ruleId,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                #{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                #{expenseName,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="agyName != null and agyName != ''">
            AND agy_name=#{agyName,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND rule_name=#{ruleName,jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="ruleId != null and ruleId != ''">
            AND rule_id=#{ruleId,jdbcType=VARCHAR}
        </if>
        <if test="expenseCode != null and expenseCode != ''">
            AND expense_code=#{expenseCode,jdbcType=VARCHAR}
        </if>
        <if test="expenseName != null and expenseName != ''">
            AND expense_name=#{expenseName,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        INSERT INTO pcx_audit_expense_relation (
        <include refid="allColumn"/>
        ) VALUES (
        <include refid="allColumnValue"/>
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        INSERT INTO pcx_audit_expense_relation (
        <include refid="insertSelectiveColumn"/>
        ) VALUES (
        <include refid="insertSelectiveValue"/>
        )
    </insert>

    <delete id="delById" parameterType="string">
        DELETE FROM pcx_audit_expense_relation
        WHERE
        id=#{value}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_audit_expense_relation
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        DELETE FROM pcx_audit_expense_relation
        WHERE 1=1
        <include refid="allColumnCond"/>
    </delete>

    <delete id="delByRuleIds">
        DELETE FROM pcx_audit_expense_relation
        WHERE 1=1
        and rule_id IN
        <foreach collection="list" index="index" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        UPDATE pcx_audit_expense_relation SET
        <include refid="allColumnSet"/>
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_expense_relation
        WHERE id=#{value}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_expense_relation
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation"
            resultType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_expense_relation
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_audit_expense_relation
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="selectSysRationByExpenseCodeList" resultType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_expense_relation
        WHERE agy_code = '*'
        AND fiscal = '*'
        AND mof_div_code = '*'
        AND expense_code IN
        <foreach collection="list" index="index" item="expenseCode" open="(" separator="," close=")">
            #{expenseCode}
        </foreach>
    </select>

    <select id="selectSysRelationByRuleIdList" resultType="com.pty.pcx.entity.wit.PcxAuditExpenseRelation">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_expense_relation
        WHERE rule_id IN
        <foreach collection="list" index="index" item="ruleID" open="(" separator="," close=")">
            #{ruleID}
        </foreach>
    </select>

</mapper>