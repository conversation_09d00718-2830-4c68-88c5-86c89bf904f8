<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao">


    <!--    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.bill.PcxExpDetailEcsRel">-->
<!--        <id column="id" jdbcType="VARCHAR" property="id" />-->
<!--        <result column="detail_id" jdbcType="VARCHAR" property="detailId" />-->
<!--        <result column="expense_id" jdbcType="VARCHAR" property="expenseId" />-->
<!--        <result column="expense_type_code" jdbcType="VARCHAR" property="expenseTypeCode" />-->
<!--        <result column="ecs_bill_id" jdbcType="VARCHAR" property="ecsBillId" />-->
<!--        <result column="ecs_bill_type" jdbcType="VARCHAR" property="ecsBillType" />-->
<!--        <result column="bill_id" jdbcType="VARCHAR" property="billId" />-->
<!--        <result column="ecs_bill_date" jdbcType="VARCHAR" property="ecsBillDate" />-->
<!--        <result column="item_name" jdbcType="VARCHAR" property="itemName" />-->
<!--        <result column="emp_code" jdbcType="VARCHAR" property="empCode" />-->
<!--        <result column="ecs_amt" jdbcType="DECIMAL" property="ecsAmt" />-->
<!--        <result column="ecs_content" jdbcType="VARCHAR" property="ecsContent" />-->
<!--        <result column="creator" jdbcType="VARCHAR" property="creator" />-->
<!--        <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />-->
<!--        <result column="created_time" jdbcType="VARCHAR" property="createdTime" />-->
<!--    </resultMap>-->

<!--    <sql id="BaseColumn">-->
<!--        id,-->
<!--        detail_id,-->
<!--          expense_id,-->
<!--          expense_type_code,-->
<!--        ecs_bill_id,-->
<!--        ecs_bill_type,-->
<!--        bill_id,-->
<!--          ecs_bill_date,-->
<!--          item_name,-->
<!--          emp_code,-->
<!--          ecs_amt,-->
<!--        ecs_content,-->
<!--        creator,-->
<!--        creator_name,-->
<!--        created_time-->
<!--    </sql>-->
<!--    <select id="selectByBillId" resultMap="BaseResultMap">-->
<!--        select <include refid="BaseColumn"/>-->
<!--            from pcx_exp_detail_ecs_rel-->
<!--        where bill_id = #{billId}-->
<!--    </select>-->
<!--    <select id="selectByBillIdAndEcsBillId" resultMap="BaseResultMap">-->
<!--        select <include refid="BaseColumn"/>-->
<!--        from pcx_exp_detail_ecs_rel-->
<!--        where bill_id = #{billId} and ecs_bill_id = #{ecsBillId}-->
<!--    </select>-->
    <select id="countEcs" resultType="java.lang.Integer">
        select count(DISTINCT ecs_bill_id) from pcx_exp_detail_ecs_rel where bill_id = #{billId} and ecs_bill_id != ''
    </select>
    <select id="countEcsAmt" resultType="java.math.BigDecimal">
        select sum(ecs_amt) from pcx_exp_detail_ecs_rel
        where seller_tax_id = #{ecsSeller}
        <![CDATA[ and ecs_bill_date >= #{startDate} ]]>
        <![CDATA[ and ecs_bill_date <= #{endDate} ]]>
    </select>

    <select id="selectExistsAttachIdList" resultType="java.lang.String">
        select file_id from ecs_file where file_id IN <foreach collection="fileIds" open="(" separator="," close=")" item="fileId">#{fileId}</foreach> and is_deleted=2;
    </select>
    <select id="getDetailAttachIds" resultType="java.lang.String">
        select attach_id from pcx_bill_exp_attach_rel where bill_id=#{billId}
        UNION ALL
        select file_id from pcx_bill_extra_attach where bill_id=#{billId};
    </select>

    <update id="updateComparedNoNeedCompare">
        update pcx_exp_detail_ecs_rel set ecs_check_status=3 where bill_id = #{billId} and ecs_bill_kind in('std','elec') and ecs_check_status=-1;
    </update>

    <select id="selectDetailAttachIds" resultType="com.pty.pcx.entity.bill.PcxBillExpAttachRel">
        select attach_id,bill_id from pcx_bill_exp_attach_rel where bill_id in
            <foreach collection="billIds" open="(" separator="," close=")" item="billId">
                #{billId}
            </foreach>
        UNION ALL
        select file_id,bill_id from pcx_bill_extra_attach where bill_id in
            <foreach collection="billIds" open="(" separator="," close=")" item="billId">
                #{billId}
            </foreach>
    </select>
</mapper>
