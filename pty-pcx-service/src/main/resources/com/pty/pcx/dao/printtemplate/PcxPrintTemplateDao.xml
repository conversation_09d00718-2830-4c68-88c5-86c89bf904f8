<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.printtemplate.PcxPrintTemplateDao">
    <sql id="allColumn">
        id,                                               billtype_code,                                    func_type,                                        func_code,
        tmp_name,                                         tmp_value,                                        tmp_path,                                         fiscal,
        mof_div_code,                                     agy_code
    </sql>

    <sql id="allColumnAlias">
        id as id,                                         billtype_code as billtypeCode,                    func_type as funcType,                            func_code as funcCode,
        tmp_name as tmpName,                              tmp_value as tmpValue,                            tmp_path as tmpPath,                              fiscal as fiscal,
        mof_div_code as mofDivCode,                       agy_code as agyCode
    </sql>

    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR},                           #{billtypeCode,jdbcType=VARCHAR},                 #{funcType,jdbcType=VARCHAR},                     #{funcCode,jdbcType=VARCHAR},
        #{tmpName,jdbcType=VARCHAR},                      #{tmpValue,jdbcType=VARCHAR},                     #{tmpPath,jdbcType=VARCHAR},                      #{fiscal,jdbcType=INTEGER},
        #{mofDivCode,jdbcType=VARCHAR},                   #{agyCode,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id=#{id,jdbcType=VARCHAR},
             </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                billtype_code=#{billtypeCode,jdbcType=VARCHAR},
             </if>
            <if test="funcType != null and funcType != ''">
                func_type=#{funcType,jdbcType=VARCHAR},
             </if>
            <if test="funcCode != null and funcCode != ''">
                func_code=#{funcCode,jdbcType=VARCHAR},
             </if>
            <if test="tmpName != null and tmpName != ''">
                tmp_name=#{tmpName,jdbcType=VARCHAR},
             </if>
            <if test="tmpValue != null and tmpValue != ''">
                tmp_value=#{tmpValue,jdbcType=VARCHAR},
             </if>
            <if test="tmpPath != null and tmpPath != ''">
                tmp_path=#{tmpPath,jdbcType=VARCHAR},
             </if>
            <if test="fiscal != null">
                fiscal=#{fiscal,jdbcType=INTEGER},
             </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
             </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
             </if>
        </trim>
    </sql>

    <sql id="columnSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                billtype_code,
            </if>
            <if test="funcType != null and funcType != ''">
                func_type,
            </if>
            <if test="funcCode != null and funcCode != ''">
                func_code,
            </if>
            <if test="tmpName != null and tmpName != ''">
                tmp_name,
            </if>
            <if test="tmpValue != null and tmpValue != ''">
                tmp_value,
            </if>
            <if test="tmpPath != null and tmpPath != ''">
                tmp_path,
            </if>
            <if test="fiscal != null">
                fiscal,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
        </trim>
    </sql>

    <sql id="columnValueSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="billtypeCode != null and billtypeCode != ''">
                #{billtypeCode,jdbcType=VARCHAR},
            </if>
            <if test="funcType != null and funcType != ''">
                #{funcType,jdbcType=VARCHAR},
            </if>
            <if test="funcCode != null and funcCode != ''">
                #{funcCode,jdbcType=VARCHAR},
            </if>
            <if test="tmpName != null and tmpName != ''">
                #{tmpName,jdbcType=VARCHAR},
            </if>
            <if test="tmpValue != null and tmpValue != ''">
                #{tmpValue,jdbcType=VARCHAR},
            </if>
            <if test="tmpPath != null and tmpPath != ''">
                #{tmpPath,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null">
                #{fiscal,jdbcType=INTEGER},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="billtypeCode != null and billtypeCode != ''">
            AND billtype_code=#{billtypeCode,jdbcType=VARCHAR}
        </if>
        <if test="funcType != null and funcType != ''">
            AND func_type=#{funcType,jdbcType=VARCHAR}
        </if>
        <if test="funcCode != null and funcCode != ''">
            AND func_code=#{funcCode,jdbcType=VARCHAR}
        </if>
        <if test="tmpName != null and tmpName != ''">
            AND tmp_name=#{tmpName,jdbcType=VARCHAR}
        </if>
        <if test="tmpValue != null and tmpValue != ''">
            AND tmp_value=#{tmpValue,jdbcType=VARCHAR}
        </if>
        <if test="tmpPath != null and tmpPath != ''">
            AND tmp_path=#{tmpPath,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null">
            AND fiscal=#{fiscal,jdbcType=INTEGER}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        INSERT INTO pcx_print_template (
            <include refid="allColumn" />
        ) VALUES (
            <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        INSERT INTO pcx_print_template (
            <include refid="columnSelective" />
        ) VALUES (
            <include refid="columnValueSelective" />
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        DELETE FROM pcx_print_template
        WHERE
            id=#{id}
    </delete>

    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM pcx_print_template
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        DELETE FROM pcx_print_template
        WHERE 1=1
            <include refid="allColumnCond" />
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        UPDATE pcx_print_template
            <set>
                <include refid="allColumnSet" />
            </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_print_template
        WHERE id=#{id}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_print_template
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate" resultType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_print_template
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_print_template
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>

    <select id="selectByQO" parameterType="com.pty.pcx.qo.print.PcxPrintBillQO" resultType="com.pty.pcx.entity.printtemplate.PcxPrintTemplate">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_print_template
        WHERE 1=1
        <include refid="allColumnCond" />
        <if test="funcCodes != null and funcCodes.size &gt; 0">
            and func_code in
            <foreach collection="funcCodes" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>
