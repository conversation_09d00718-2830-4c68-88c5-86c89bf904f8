<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.setting.PcxPaFieldSettingDao">
    <sql id="allColumn">
            agy_code,                                         atom_code,                                        atom_data_url,                                    bud_sort,
    code,                                             code_name,                                        column_code,                                      column_name,
    column_width,                                     data_type,                                        disabled,                                         editor,
    ele_level,                                        explain_desc,                                     ext_field01,                                      ext_field02,
    ext_field03,                                      ext_field04,                                      ext_field05,                                      ext_field06,
    fiscal,                                           id,                                               is_locking,                                       is_null,
    is_show,                                          is_show_code,                                     menu_url,                                         mof_div_code,
    name,                                             ord_seq,                                          required,                                         son_title,
    sort,                                             sys_id,                                        type,
    user_code
    </sql>

    <sql id="allColumnAlias">
            agy_code as agyCode,                              atom_code as atomCode,                            atom_data_url as atomDataUrl,                     bud_sort as budSort,
    code as code,                                     code_name as codeName,                            column_code as columnCode,                        column_name as columnName,
    column_width as columnWidth,                      data_type as dataType,                            disabled as disabled,                             editor as editor,
    ele_level as eleLevel,                            explain_desc as explainDesc,                      ext_field01 as extField01,                        ext_field02 as extField02,
    ext_field03 as extField03,                        ext_field04 as extField04,                        ext_field05 as extField05,                        ext_field06 as extField06,
    fiscal as fiscal,                                 id as id,                                         is_locking as isLocking,                          is_null as isNull,
    is_show as isShow,                                is_show_code as isShowCode,                       menu_url as menuUrl,                              mof_div_code as mofDivCode,
    name as name,                                     ord_seq as ordSeq,                                required as required,                             son_title as sonTitle,
    sort as sort,                                     sys_id as sysId,                            type as type,
    user_code as userCode
    </sql>

    <sql id="allColumnValue">
        #{agyCode,jdbcType=VARCHAR},            #{atomCode,jdbcType=VARCHAR},           #{atomDataUrl,jdbcType=VARCHAR},        #{budSort,jdbcType=VARCHAR},
        #{code,jdbcType=VARCHAR},               #{codeName,jdbcType=VARCHAR},           #{columnCode,jdbcType=VARCHAR},         #{columnName,jdbcType=VARCHAR},
        #{columnWidth,jdbcType=VARCHAR},        #{dataType,jdbcType=VARCHAR},           #{disabled,jdbcType=VARCHAR},           #{editor,jdbcType=VARCHAR},
        #{eleLevel,jdbcType=VARCHAR},           #{explainDesc,jdbcType=VARCHAR},        #{extField01,jdbcType=VARCHAR},         #{extField02,jdbcType=VARCHAR},
        #{extField03,jdbcType=VARCHAR},         #{extField04,jdbcType=VARCHAR},         #{extField05,jdbcType=VARCHAR},         #{extField06,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},             #{id,jdbcType=VARCHAR},                 #{isLocking,jdbcType=INTEGER},          #{isNull,jdbcType=INTEGER},
        #{isShow,jdbcType=INTEGER},             #{isShowCode,jdbcType=VARCHAR},         #{menuUrl,jdbcType=VARCHAR},            #{mofDivCode,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR},               #{ordSeq,jdbcType=INTEGER},             #{required,jdbcType=VARCHAR},           #{sonTitle,jdbcType=VARCHAR},
        #{sort,jdbcType=VARCHAR},               #{sysId,jdbcType=VARCHAR},           #{type,jdbcType=VARCHAR},
        #{userCode,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="atomCode != null and atomCode != ''">
                atom_code=#{atomCode,jdbcType=VARCHAR},
            </if>
            <if test="atomDataUrl != null and atomDataUrl != ''">
                atom_data_url=#{atomDataUrl,jdbcType=VARCHAR},
            </if>
            <if test="budSort != null and budSort != ''">
                bud_sort=#{budSort,jdbcType=VARCHAR},
            </if>
            <if test="code != null and code != ''">
                code=#{code,jdbcType=VARCHAR},
            </if>
            <if test="codeName != null and codeName != ''">
                code_name=#{codeName,jdbcType=VARCHAR},
            </if>
            <if test="columnCode != null and columnCode != ''">
                column_code=#{columnCode,jdbcType=VARCHAR},
            </if>
            <if test="columnName != null and columnName != ''">
                column_name=#{columnName,jdbcType=VARCHAR},
            </if>
            <if test="columnWidth != null">
                column_width=#{columnWidth,jdbcType=VARCHAR},
            </if>
            <if test="dataType != null and dataType != ''">
                data_type=#{dataType,jdbcType=VARCHAR},
            </if>
            <if test="disabled != null and disabled != ''">
                disabled=#{disabled,jdbcType=VARCHAR},
            </if>
            <if test="editor != null and editor != ''">
                editor=#{editor,jdbcType=VARCHAR},
            </if>
            <if test="eleLevel != null and eleLevel != ''">
                ele_level=#{eleLevel,jdbcType=VARCHAR},
            </if>
            <if test="explainDesc != null and explainDesc != ''">
                explain_desc=#{explainDesc,jdbcType=VARCHAR},
            </if>
            <if test="extField01 != null and extField01 != ''">
                ext_field01=#{extField01,jdbcType=VARCHAR},
            </if>
            <if test="extField02 != null and extField02 != ''">
                ext_field02=#{extField02,jdbcType=VARCHAR},
            </if>
            <if test="extField03 != null and extField03 != ''">
                ext_field03=#{extField03,jdbcType=VARCHAR},
            </if>
            <if test="extField04 != null and extField04 != ''">
                ext_field04=#{extField04,jdbcType=VARCHAR},
            </if>
            <if test="extField05 != null and extField05 != ''">
                ext_field05=#{extField05,jdbcType=VARCHAR},
            </if>
            <if test="extField06 != null and extField06 != ''">
                ext_field06=#{extField06,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="isLocking != null">
                is_locking=#{isLocking,jdbcType=INTEGER},
            </if>
            <if test="isNull != null">
                is_null=#{isNull,jdbcType=INTEGER},
            </if>
            <if test="isShow != null">
                is_show=#{isShow,jdbcType=INTEGER},
            </if>
            <if test="isShowCode != null and isShowCode != ''">
                is_show_code=#{isShowCode,jdbcType=VARCHAR},
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                menu_url=#{menuUrl,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                name=#{name,jdbcType=VARCHAR},
            </if>
            <if test="ordSeq != null">
                ord_seq=#{ordSeq,jdbcType=INTEGER},
            </if>
            <if test="required != null and required != ''">
                required=#{required,jdbcType=VARCHAR},
            </if>
            <if test="sonTitle != null and sonTitle != ''">
                son_title=#{sonTitle,jdbcType=VARCHAR},
            </if>
            <if test="sort != null and sort != ''">
                sort=#{sort,jdbcType=VARCHAR},
            </if>
            <if test="sysId != null and sysId != ''">
                sys_id=#{sysId,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                type=#{type,jdbcType=VARCHAR},
            </if>
            <if test="userCode != null and userCode != ''">
                user_code=#{userCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCodeOrAll != null and agyCodeOrAll != ''">
            AND (agy_code=#{agyCodeOrAll,jdbcType=VARCHAR} or agy_code = '*')
        </if>
        <if test="atomCode != null and atomCode != ''">
            AND atom_code=#{atomCode,jdbcType=VARCHAR}
        </if>
        <if test="atomDataUrl != null and atomDataUrl != ''">
            AND atom_data_url=#{atomDataUrl,jdbcType=VARCHAR}
        </if>
        <if test="budSort != null and budSort != ''">
            AND bud_sort=#{budSort,jdbcType=VARCHAR}
        </if>
        <if test="code != null and code != ''">
            AND code=#{code,jdbcType=VARCHAR}
        </if>
        <if test="codeName != null and codeName != ''">
            AND code_name=#{codeName,jdbcType=VARCHAR}
        </if>
        <if test="columnCode != null and columnCode != ''">
            AND column_code=#{columnCode,jdbcType=VARCHAR}
        </if>
        <if test="columnName != null and columnName != ''">
            AND column_name=#{columnName,jdbcType=VARCHAR}
        </if>
        <if test="columnWidth != null">
            AND column_width=#{columnWidth,jdbcType=VARCHAR}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type=#{dataType,jdbcType=VARCHAR}
        </if>
        <if test="disabled != null and disabled != ''">
            AND disabled=#{disabled,jdbcType=VARCHAR}
        </if>
        <if test="editor != null and editor != ''">
            AND editor=#{editor,jdbcType=VARCHAR}
        </if>
        <if test="eleLevel != null and eleLevel != ''">
            AND ele_level=#{eleLevel,jdbcType=VARCHAR}
        </if>
        <if test="explainDesc != null and explainDesc != ''">
            AND explain_desc=#{explainDesc,jdbcType=VARCHAR}
        </if>
        <if test="extField01 != null and extField01 != ''">
            AND ext_field01=#{extField01,jdbcType=VARCHAR}
        </if>
        <if test="extField02 != null and extField02 != ''">
            AND ext_field02=#{extField02,jdbcType=VARCHAR}
        </if>
        <if test="extField03 != null and extField03 != ''">
            AND ext_field03=#{extField03,jdbcType=VARCHAR}
        </if>
        <if test="extField04 != null and extField04 != ''">
            AND ext_field04=#{extField04,jdbcType=VARCHAR}
        </if>
        <if test="extField05 != null and extField05 != ''">
            AND ext_field05=#{extField05,jdbcType=VARCHAR}
        </if>
        <if test="extField06 != null and extField06 != ''">
            AND ext_field06=#{extField06,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="fiscalOrAll != null and fiscalOrAll != ''">
            AND (fiscal=#{fiscalOrAll,jdbcType=VARCHAR} or fiscal = '*')
        </if>
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="isLocking != null">
            AND is_locking=#{isLocking,jdbcType=INTEGER}
        </if>
        <if test="isNull != null">
            AND is_null=#{isNull,jdbcType=INTEGER}
        </if>
        <if test="isShow != null">
            AND is_show=#{isShow,jdbcType=INTEGER}
        </if>
        <if test="isShowCode != null and isShowCode != ''">
            AND is_show_code=#{isShowCode,jdbcType=VARCHAR}
        </if>
        <if test="menuUrl != null and menuUrl != ''">
            AND menu_url=#{menuUrl,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCodeOrAll != null and mofDivCodeOrAll != ''">
            AND (mof_div_code=#{mofDivCodeOrAll,jdbcType=VARCHAR} or mof_div_code = '*')
        </if>
        <if test="name != null and name != ''">
            AND name=#{name,jdbcType=VARCHAR}
        </if>
        <if test="ordSeq != null">
            AND ord_seq=#{ordSeq,jdbcType=INTEGER}
        </if>
        <if test="required != null and required != ''">
            AND required=#{required,jdbcType=VARCHAR}
        </if>
        <if test="sonTitle != null and sonTitle != ''">
            AND son_title=#{sonTitle,jdbcType=VARCHAR}
        </if>
        <if test="sort != null and sort != ''">
            AND sort=#{sort,jdbcType=VARCHAR}
        </if>
        <if test="sysId != null and sysId != ''">
            AND sys_id=#{sysId,jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != ''">
            AND type=#{type,jdbcType=VARCHAR}
        </if>
        <if test="userCode != null and userCode != ''">
            AND user_code=#{userCode,jdbcType=VARCHAR}
        </if>
        <if test="userCodeOrAll != null and userCodeOrAll != ''">
            AND (user_code=#{userCodeOrAll,jdbcType=VARCHAR} or user_code = '*')
        </if>
    </sql>

    <sql id="delColumnCond">
        <if test="type != null and type != ''">
            AND type=#{type,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="atomCode != null and atomCode != ''">
                atom_code,
            </if>
            <if test="atomDataUrl != null and atomDataUrl != ''">
                atom_data_url,
            </if>
            <if test="budSort != null and budSort != ''">
                bud_sort,
            </if>
            <if test="code != null and code != ''">
                code,
            </if>
            <if test="codeName != null and codeName != ''">
                code_name,
            </if>
            <if test="columnCode != null and columnCode != ''">
                column_code,
            </if>
            <if test="columnName != null and columnName != ''">
                column_name,
            </if>
            <if test="columnWidth != null">
                column_width,
            </if>
            <if test="dataType != null and dataType != ''">
                data_type,
            </if>
            <if test="disabled != null and disabled != ''">
                disabled,
            </if>
            <if test="editor != null and editor != ''">
                editor,
            </if>
            <if test="eleLevel != null and eleLevel != ''">
                ele_level,
            </if>
            <if test="explainDesc != null and explainDesc != ''">
                explain_desc,
            </if>
            <if test="extField01 != null and extField01 != ''">
                ext_field01,
            </if>
            <if test="extField02 != null and extField02 != ''">
                ext_field02,
            </if>
            <if test="extField03 != null and extField03 != ''">
                ext_field03,
            </if>
            <if test="extField04 != null and extField04 != ''">
                ext_field04,
            </if>
            <if test="extField05 != null and extField05 != ''">
                ext_field05,
            </if>
            <if test="extField06 != null and extField06 != ''">
                ext_field06,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="isLocking != null">
                is_locking,
            </if>
            <if test="isNull != null">
                is_null,
            </if>
            <if test="isShow != null">
                is_show,
            </if>
            <if test="isShowCode != null and isShowCode != ''">
                is_show_code,
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                menu_url,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="ordSeq != null">
                ord_seq,
            </if>
            <if test="required != null and required != ''">
                required,
            </if>
            <if test="sonTitle != null and sonTitle != ''">
                son_title,
            </if>
            <if test="sort != null and sort != ''">
                sort,
            </if>
            <if test="sysId != null and sysId != ''">
                sys_id,
            </if>
            <if test="type != null and type != ''">
                type,
            </if>
            <if test="userCode != null and userCode != ''">
                user_code,
            </if>
        </trim>
    </sql>

    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="atomCode != null and atomCode != ''">
                #{atomCode,jdbcType=VARCHAR},
            </if>
            <if test="atomDataUrl != null and atomDataUrl != ''">
                #{atomDataUrl,jdbcType=VARCHAR},
            </if>
            <if test="budSort != null and budSort != ''">
                #{budSort,jdbcType=VARCHAR},
            </if>
            <if test="code != null and code != ''">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="codeName != null and codeName != ''">
                #{codeName,jdbcType=VARCHAR},
            </if>
            <if test="columnCode != null and columnCode != ''">
                #{columnCode,jdbcType=VARCHAR},
            </if>
            <if test="columnName != null and columnName != ''">
                #{columnName,jdbcType=VARCHAR},
            </if>
            <if test="columnWidth != null">
                #{columnWidth,jdbcType=VARCHAR},
            </if>
            <if test="dataType != null and dataType != ''">
                #{dataType,jdbcType=VARCHAR},
            </if>
            <if test="disabled != null and disabled != ''">
                #{disabled,jdbcType=VARCHAR},
            </if>
            <if test="editor != null and editor != ''">
                #{editor,jdbcType=VARCHAR},
            </if>
            <if test="eleLevel != null and eleLevel != ''">
                #{eleLevel,jdbcType=VARCHAR},
            </if>
            <if test="explainDesc != null and explainDesc != ''">
                #{explainDesc,jdbcType=VARCHAR},
            </if>
            <if test="extField01 != null and extField01 != ''">
                #{extField01,jdbcType=VARCHAR},
            </if>
            <if test="extField02 != null and extField02 != ''">
                #{extField02,jdbcType=VARCHAR},
            </if>
            <if test="extField03 != null and extField03 != ''">
                #{extField03,jdbcType=VARCHAR},
            </if>
            <if test="extField04 != null and extField04 != ''">
                #{extField04,jdbcType=VARCHAR},
            </if>
            <if test="extField05 != null and extField05 != ''">
                #{extField05,jdbcType=VARCHAR},
            </if>
            <if test="extField06 != null and extField06 != ''">
                #{extField06,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="isLocking != null">
                #{isLocking,jdbcType=INTEGER},
            </if>
            <if test="isNull != null">
                #{isNull,jdbcType=INTEGER},
            </if>
            <if test="isShow != null">
                #{isShow,jdbcType=INTEGER},
            </if>
            <if test="isShowCode != null and isShowCode != ''">
                #{isShowCode,jdbcType=VARCHAR},
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                #{menuUrl,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="ordSeq != null">
                #{ordSeq,jdbcType=INTEGER},
            </if>
            <if test="required != null and required != ''">
                #{required,jdbcType=VARCHAR},
            </if>
            <if test="sonTitle != null and sonTitle != ''">
                #{sonTitle,jdbcType=VARCHAR},
            </if>
            <if test="sort != null and sort != ''">
                #{sort,jdbcType=VARCHAR},
            </if>
            <if test="sysId != null and sysId != ''">
                #{sysId,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userCode != null and userCode != ''">
                #{userCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="atomCode != null and atomCode != ''">
                atom_code,
            </if>
            <if test="atomDataUrl != null and atomDataUrl != ''">
                atom_data_url,
            </if>
            <if test="budSort != null and budSort != ''">
                bud_sort,
            </if>
            <if test="code != null and code != ''">
                code,
            </if>
            <if test="codeName != null and codeName != ''">
                code_name,
            </if>
            <if test="columnCode != null and columnCode != ''">
                column_code,
            </if>
            <if test="columnName != null and columnName != ''">
                column_name,
            </if>
            <if test="columnWidth != null">
                column_width,
            </if>
            <if test="dataType != null and dataType != ''">
                data_type,
            </if>
            <if test="disabled != null and disabled != ''">
                disabled,
            </if>
            <if test="editor != null and editor != ''">
                editor,
            </if>
            <if test="eleLevel != null and eleLevel != ''">
                ele_level,
            </if>
            <if test="explainDesc != null and explainDesc != ''">
                explain_desc,
            </if>
            <if test="extField01 != null and extField01 != ''">
                ext_field01,
            </if>
            <if test="extField02 != null and extField02 != ''">
                ext_field02,
            </if>
            <if test="extField03 != null and extField03 != ''">
                ext_field03,
            </if>
            <if test="extField04 != null and extField04 != ''">
                ext_field04,
            </if>
            <if test="extField05 != null and extField05 != ''">
                ext_field05,
            </if>
            <if test="extField06 != null and extField06 != ''">
                ext_field06,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="isLocking != null">
                is_locking,
            </if>
            <if test="isNull != null">
                is_null,
            </if>
            <if test="isShow != null">
                is_show,
            </if>
            <if test="isShowCode != null and isShowCode != ''">
                is_show_code,
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                menu_url,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="ordSeq != null">
                ord_seq,
            </if>
            <if test="required != null and required != ''">
                required,
            </if>
            <if test="sonTitle != null and sonTitle != ''">
                son_title,
            </if>
            <if test="sort != null and sort != ''">
                sort,
            </if>
            <if test="sysId != null and sysId != ''">
                sys_id,
            </if>
            <if test="type != null and type != ''">
                type,
            </if>
            <if test="userCode != null and userCode != ''">
                user_code,
            </if>
        </trim>
    </sql>

    <sql id="updateSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="atomCode != null and atomCode != ''">
                #{atomCode,jdbcType=VARCHAR},
            </if>
            <if test="atomDataUrl != null and atomDataUrl != ''">
                #{atomDataUrl,jdbcType=VARCHAR},
            </if>
            <if test="budSort != null and budSort != ''">
                #{budSort,jdbcType=VARCHAR},
            </if>
            <if test="code != null and code != ''">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="codeName != null and codeName != ''">
                #{codeName,jdbcType=VARCHAR},
            </if>
            <if test="columnCode != null and columnCode != ''">
                #{columnCode,jdbcType=VARCHAR},
            </if>
            <if test="columnName != null and columnName != ''">
                #{columnName,jdbcType=VARCHAR},
            </if>
            <if test="columnWidth != null">
                #{columnWidth,jdbcType=VARCHAR},
            </if>
            <if test="dataType != null and dataType != ''">
                #{dataType,jdbcType=VARCHAR},
            </if>
            <if test="disabled != null and disabled != ''">
                #{disabled,jdbcType=VARCHAR},
            </if>
            <if test="editor != null and editor != ''">
                #{editor,jdbcType=VARCHAR},
            </if>
            <if test="eleLevel != null and eleLevel != ''">
                #{eleLevel,jdbcType=VARCHAR},
            </if>
            <if test="explainDesc != null and explainDesc != ''">
                #{explainDesc,jdbcType=VARCHAR},
            </if>
            <if test="extField01 != null and extField01 != ''">
                #{extField01,jdbcType=VARCHAR},
            </if>
            <if test="extField02 != null and extField02 != ''">
                #{extField02,jdbcType=VARCHAR},
            </if>
            <if test="extField03 != null and extField03 != ''">
                #{extField03,jdbcType=VARCHAR},
            </if>
            <if test="extField04 != null and extField04 != ''">
                #{extField04,jdbcType=VARCHAR},
            </if>
            <if test="extField05 != null and extField05 != ''">
                #{extField05,jdbcType=VARCHAR},
            </if>
            <if test="extField06 != null and extField06 != ''">
                #{extField06,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="isLocking != null">
                #{isLocking,jdbcType=INTEGER},
            </if>
            <if test="isNull != null">
                #{isNull,jdbcType=INTEGER},
            </if>
            <if test="isShow != null">
                #{isShow,jdbcType=INTEGER},
            </if>
            <if test="isShowCode != null and isShowCode != ''">
                #{isShowCode,jdbcType=VARCHAR},
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                #{menuUrl,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="ordSeq != null">
                #{ordSeq,jdbcType=INTEGER},
            </if>
            <if test="required != null and required != ''">
                #{required,jdbcType=VARCHAR},
            </if>
            <if test="sonTitle != null and sonTitle != ''">
                #{sonTitle,jdbcType=VARCHAR},
            </if>
            <if test="sort != null and sort != ''">
                #{sort,jdbcType=VARCHAR},
            </if>
            <if test="sysId != null and sysId != ''">
                #{sysId,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userCode != null and userCode != ''">
                #{userCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

        <insert id="insertSelective" parameterType="com.pty.pcx.entity.setting.PaFieldSetting">
            INSERT INTO pa_field_setting (
            <include refid="insertSelectiveColumn" />
            ) VALUES (
            <include refid="insertSelectiveValue" />
            )
        </insert>

        <update id="updateById" parameterType="com.pty.pcx.entity.setting.PaFieldSetting">
            UPDATE pa_field_setting SET
            <include refid="allColumnSet" />
            WHERE id=#{id,jdbcType=VARCHAR}
        </update>

        <delete id="delById" parameterType="com.pty.pcx.entity.setting.PaFieldSetting">
            DELETE FROM pa_field_setting
            WHERE id = #{id,jdbcType=VARCHAR}
        </delete>

        <delete id="delByUser" parameterType="com.pty.pcx.entity.setting.PaFieldSetting">
            DELETE FROM pa_field_setting
            WHERE user_code=#{userCode,jdbcType=VARCHAR}
            AND sys_id=#{sysId,jdbcType=VARCHAR}
            AND menu_url=#{menuUrl,jdbcType=VARCHAR}
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
            <include refid="delColumnCond" />
        </delete>

        <select id="select" parameterType="com.pty.pcx.entity.setting.PaFieldSetting" resultType="com.pty.pcx.vo.setting.PaFieldSettingVO">
            SELECT
            <include refid="allColumnAlias" />
            FROM pa_field_setting
            WHERE 1=1
            <include refid="allColumnCond" />
            order by ord_seq
        </select>

        <select id="selectByType" parameterType="com.pty.pcx.entity.setting.PaFieldSetting" resultType="com.pty.pcx.vo.setting.PaFieldSettingVO">
            SELECT
            <include refid="allColumnAlias" />
            FROM pa_field_setting
            WHERE agy_code=#{agyCode,jdbcType=VARCHAR}
            AND fiscal=#{fiscal,jdbcType=INTEGER}
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
            AND type = #{type,jdbcType=VARCHAR}
        </select>

    <select id="selectByMenuUrl" parameterType="com.pty.pcx.entity.setting.PaFieldSetting" resultType="com.pty.pcx.vo.setting.PaFieldSettingVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pa_field_setting
        WHERE agy_code=#{agyCode,jdbcType=VARCHAR}
        AND fiscal=#{fiscal,jdbcType=INTEGER}
        AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        AND menu_url = #{menuUrl,jdbcType=VARCHAR}
    </select>

</mapper>