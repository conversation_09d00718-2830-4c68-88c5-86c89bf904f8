<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.wit.PcxAuditRuleDao">
    <sql id="allColumn">
        rule_create_date,                                 rule_must_condition,                              rule_correct,                                     rule_error_template,
        rule_enabled,                                     rule_desc,                                        rule_type,                                        rule_group,
        rule_name,                                        rule_id,                                          rule_reference,                                   mof_div_code,
        agy_code,                                         from_id,                                          parent_id,                                        field1,
        agy_name,                                         rule_creator,                                     rule_modify_date,                                 fiscal,
        rule_when_condition,                              rule_field,                                       rule_identify,                                    rule_module,
        rule_act,                                         field2
    </sql>

    <sql id="allColumnAlias">
        rule_create_date as ruleCreatedDate,              rule_must_condition as mustCondition,             rule_correct as ruleCorrect,                      rule_error_template as ruleErrorTemplate,
        rule_enabled as enabled,                          rule_desc as ruleDesc,                            rule_type as ruleType,                            rule_group as ruleGroup,
        rule_name as ruleName,                            rule_id as ruleId,                                rule_reference as ruleReference,                  mof_div_code as mofDivCode,
        agy_code as agyCode,                              from_id as fromId,                                parent_id as parentId,                            field1 as field1,
        agy_name as agyName,                              rule_creator as ruleCreator,                      rule_modify_date as ruleModifyDate,               fiscal as fiscal,
        rule_when_condition as whenCondition,             rule_field as ruleField,                          rule_identify as ruleIdentify,                    rule_module as ruleModule,
        rule_act as ruleAct,                              field2 as field2
    </sql>

    <sql id="allColumnValue">
        #{ruleCreatedDate,jdbcType=VARCHAR},    #{mustCondition,jdbcType=VARCHAR},      #{ruleCorrect,jdbcType=INTEGER},        #{ruleErrorTemplate,jdbcType=VARCHAR},
        #{enabled,jdbcType=INTEGER},            #{ruleDesc,jdbcType=VARCHAR},           #{ruleType,jdbcType=VARCHAR},           #{ruleGroup,jdbcType=VARCHAR},
        #{ruleName,jdbcType=VARCHAR},           #{ruleId,jdbcType=VARCHAR},             #{ruleReference,jdbcType=VARCHAR},      #{mofDivCode,jdbcType=VARCHAR},
        #{agyCode,jdbcType=VARCHAR},            #{fromId,jdbcType=VARCHAR},             #{parentId,jdbcType=VARCHAR},           #{field1,jdbcType=VARCHAR},
        #{agyName,jdbcType=VARCHAR},            #{ruleCreator,jdbcType=VARCHAR},        #{ruleModifyDate,jdbcType=VARCHAR},     #{fiscal,jdbcType=VARCHAR},
        #{whenCondition,jdbcType=VARCHAR},      #{ruleField,jdbcType=VARCHAR},          #{ruleIdentify,jdbcType=INTEGER},       #{ruleModule,jdbcType=VARCHAR},
        #{ruleAct,jdbcType=VARCHAR},            #{field2,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="ruleCreatedDate != null and ruleCreatedDate != ''">
                rule_create_date=#{ruleCreatedDate,jdbcType=VARCHAR},
            </if>
            <if test="mustCondition != null and mustCondition != ''">
                rule_must_condition=#{mustCondition,jdbcType=VARCHAR},
            </if>
            <if test="ruleCorrect != null">
                rule_correct=#{ruleCorrect,jdbcType=INTEGER},
            </if>
            <if test="ruleErrorTemplate != null and ruleErrorTemplate != ''">
                rule_error_template=#{ruleErrorTemplate,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                rule_enabled=#{enabled,jdbcType=INTEGER},
            </if>
            <if test="ruleDesc != null and ruleDesc != ''">
                rule_desc=#{ruleDesc,jdbcType=VARCHAR},
            </if>
            <if test="ruleType != null and ruleType != ''">
                rule_type=#{ruleType,jdbcType=VARCHAR},
            </if>
            <if test="ruleGroup != null and ruleGroup != ''">
                rule_group=#{ruleGroup,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                rule_name=#{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="ruleReference != null and ruleReference != ''">
                rule_reference=#{ruleReference,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="fromId != null and fromId != ''">
                from_id=#{fromId,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id=#{parentId,jdbcType=VARCHAR},
            </if>
            <if test="field1 != null">
                field1=#{field1,jdbcType=VARCHAR},
            </if>
            <if test="agyName != null and agyName != ''">
                agy_name=#{agyName,jdbcType=VARCHAR},
            </if>
            <if test="ruleCreator != null and ruleCreator != ''">
                rule_creator=#{ruleCreator,jdbcType=VARCHAR},
            </if>
            <if test="ruleModifyDate != null and ruleModifyDate != ''">
                rule_modify_date=#{ruleModifyDate,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="whenCondition != null and whenCondition != ''">
                rule_when_condition=#{whenCondition,jdbcType=VARCHAR},
            </if>
            <if test="ruleField != null">
                rule_field=#{ruleField,jdbcType=VARCHAR},
            </if>
            <if test="ruleIdentify != null">
                rule_identify=#{ruleIdentify,jdbcType=INTEGER},
            </if>
            <if test="ruleModule != null and ruleModule != ''">
                rule_module=#{ruleModule,jdbcType=VARCHAR},
            </if>
            <if test="ruleAct != null and ruleAct != ''">
                rule_act=#{ruleAct,jdbcType=VARCHAR},
            </if>
            <if test="field2 != null">
                field2=#{field2,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="ruleCreatedDate != null and ruleCreatedDate != ''">
            AND rule_create_date=#{ruleCreatedDate,jdbcType=VARCHAR}
        </if>
        <if test="mustCondition != null and mustCondition != ''">
            AND rule_must_condition=#{mustCondition,jdbcType=VARCHAR}
        </if>
        <if test="ruleCorrect != null">
            AND rule_correct=#{ruleCorrect,jdbcType=INTEGER}
        </if>
        <if test="ruleErrorTemplate != null and ruleErrorTemplate != ''">
            AND rule_error_template=#{ruleErrorTemplate,jdbcType=VARCHAR}
        </if>
        <if test="enabled != null">
            AND rule_enabled=#{enabled,jdbcType=INTEGER}
        </if>
        <if test="ruleDesc != null and ruleDesc != ''">
            AND rule_desc=#{ruleDesc,jdbcType=VARCHAR}
        </if>
        <if test="ruleType != null and ruleType != ''">
            AND rule_type=#{ruleType,jdbcType=VARCHAR}
        </if>
        <if test="ruleGroup != null and ruleGroup != ''">
            AND rule_group=#{ruleGroup,jdbcType=VARCHAR}
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND rule_name=#{ruleName,jdbcType=VARCHAR}
        </if>
        <if test="ruleId != null and ruleId != ''">
            AND rule_id=#{ruleId,jdbcType=VARCHAR}
        </if>
        <if test="ruleReference != null and ruleReference != ''">
            AND rule_reference=#{ruleReference,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="fromId != null and fromId != ''">
            AND from_id=#{fromId,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null and parentId != ''">
            AND parent_id=#{parentId,jdbcType=VARCHAR}
        </if>
        <if test="field1 != null and field1 != '' and field1Arr == null">
            AND field1=#{field1,jdbcType=VARCHAR}
        </if>
        <if test="agyName != null and agyName != ''">
            AND agy_name=#{agyName,jdbcType=VARCHAR}
        </if>
        <if test="ruleCreator != null and ruleCreator != ''">
            AND rule_creator=#{ruleCreator,jdbcType=VARCHAR}
        </if>
        <if test="ruleModifyDate != null and ruleModifyDate != ''">
            AND rule_modify_date=#{ruleModifyDate,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="whenCondition != null and whenCondition != ''">
            AND rule_when_condition=#{whenCondition,jdbcType=VARCHAR}
        </if>
        <if test="ruleField != null and ruleField != ''">
            AND rule_field=#{ruleField,jdbcType=VARCHAR}
        </if>
        <if test="ruleIdentify != null">
            AND rule_identify=#{ruleIdentify,jdbcType=INTEGER}
        </if>
        <if test="ruleModule != null and ruleModule != ''">
            AND rule_module=#{ruleModule,jdbcType=VARCHAR}
        </if>
        <if test="ruleAct != null and ruleAct != ''">
            AND rule_act=#{ruleAct,jdbcType=VARCHAR}
        </if>
        <if test="field2 != null and field2 != ''">
            AND field2=#{field2,jdbcType=VARCHAR}
        </if>
        <if test="field1Arr != null and field1Arr.size() > 0">
            <foreach collection="field1Arr" index="index" item="field1">
                AND find_in_set(#{field1,jdbcType=VARCHAR}, field1)
            </foreach>
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.wit.PcxAuditRule">
        INSERT INTO pcx_audit_rule (
        <include refid="allColumn"/>
        ) VALUES (
        <include refid="allColumnValue"/>
        )
    </insert>

    <delete id="delById" parameterType="string">
        DELETE FROM pcx_audit_rule
        WHERE
        rule_id=#{value}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_audit_rule
        WHERE rule_id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.wit.PcxAuditRule">
        DELETE FROM pcx_audit_rule
        WHERE 1=1
        <include refid="allColumnCond"/>
    </delete>


    <update id="updateById" parameterType="com.pty.pcx.entity.wit.PcxAuditRule">
        UPDATE pcx_audit_rule SET
        <include refid="allColumnSet"/>
        WHERE rule_id=#{ruleId}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.wit.PcxAuditRule">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_rule
        WHERE rule_id=#{value}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.wit.PcxAuditRule">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_rule
        WHERE rule_id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.wit.PcxAuditRule"
            resultType="com.pty.pcx.entity.wit.PcxAuditRule">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_audit_rule
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.wit.PcxAuditRule" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_audit_rule
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="exist" parameterType="com.pty.pcx.entity.wit.PcxAuditRule" resultType="java.lang.Integer">
        SELECT
        CASE WHEN EXISTS (SELECT 1 FROM pcx_audit_rule WHERE agy_code = #{agyCode} and fiscal = #{fiscal} and
        mof_div_code = #{mofDivCode}) THEN 1
        ELSE 0 END AS data_exists;
    </select>

    <select id="selectWithExpenseRelation" parameterType="com.pty.pcx.qo.wit.PcxAuditRuleQO"
            resultType="com.pty.pcx.entity.wit.PcxAuditRule">
        SELECT
        pcx_audit_rule.rule_create_date as ruleCreatedDate, pcx_audit_rule.rule_must_condition as mustCondition,
        pcx_audit_rule.rule_correct as ruleCorrect,
        pcx_audit_rule.rule_error_template as ruleErrorTemplate,
        pcx_audit_rule.rule_enabled as enabled, pcx_audit_rule.rule_desc as ruleDesc, pcx_audit_rule.rule_type as
        ruleType, pcx_audit_rule.rule_group as ruleGroup,
        pcx_audit_rule.rule_name as ruleName, pcx_audit_rule.rule_id as ruleId, pcx_audit_rule.rule_reference as
        ruleReference, pcx_audit_rule.mof_div_code as mofDivCode,
        pcx_audit_rule.agy_code as agyCode, pcx_audit_rule.from_id as fromId, pcx_audit_rule.parent_id as parentId,
        pcx_audit_rule.agy_name as agyName,
        pcx_audit_rule.rule_creator as ruleCreator, pcx_audit_rule.rule_modify_date as ruleModifyDate,
        pcx_audit_rule.fiscal as fiscal, pcx_audit_rule.rule_when_condition as
        whenCondition,
        pcx_audit_rule.rule_identify as ruleIdentify, pcx_audit_rule.rule_module as ruleModule, pcx_audit_rule.rule_act
        as ruleAct,
        pcx_audit_rule.rule_field as ruleField, pcx_audit_rule.field1 as field1
        FROM pcx_audit_rule
        <if test="expenseCode != null and expenseCode != ''">
            left join pcx_audit_expense_relation on pcx_audit_rule.rule_id = pcx_audit_expense_relation.rule_id
        </if>
        WHERE 1=1
        <if test="expenseCode != null and expenseCode != ''">
            AND pcx_audit_expense_relation.expense_code = #{expenseCode,jdbcType=VARCHAR}
        </if>
        <if test="ruleCreatedDate != null and ruleCreatedDate != ''">
            AND pcx_audit_rule.rule_create_date=#{ruleCreatedDate,jdbcType=VARCHAR}
        </if>
        <if test="mustCondition != null and mustCondition != ''">
            AND pcx_audit_rule.rule_must_condition=#{mustCondition,jdbcType=VARCHAR}
        </if>
        <if test="ruleCorrect != null">
            AND pcx_audit_rule.rule_correct=#{ruleCorrect,jdbcType=INTEGER}
        </if>
        <if test="ruleErrorTemplate != null and ruleErrorTemplate != ''">
            AND pcx_audit_rule.rule_error_template=#{ruleErrorTemplate,jdbcType=VARCHAR}
        </if>
        <if test="enabled != null">
            AND pcx_audit_rule.rule_enabled=#{enabled,jdbcType=INTEGER}
        </if>
        <if test="ruleDesc != null and ruleDesc != ''">
            AND pcx_audit_rule.rule_desc=#{ruleDesc,jdbcType=VARCHAR}
        </if>
        <if test="ruleType != null and ruleType != ''">
            AND pcx_audit_rule.rule_type=#{ruleType,jdbcType=VARCHAR}
        </if>
        <if test="ruleGroup != null and ruleGroup != ''">
            AND pcx_audit_rule.rule_group=#{ruleGroup,jdbcType=VARCHAR}
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND pcx_audit_rule.rule_name=#{ruleName,jdbcType=VARCHAR}
        </if>
        <if test="ruleId != null and ruleId != ''">
            AND pcx_audit_rule.rule_id=#{ruleId,jdbcType=VARCHAR}
        </if>
        <if test="ruleReference != null and ruleReference != ''">
            AND pcx_audit_rule.rule_reference=#{ruleReference,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND pcx_audit_rule.mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND pcx_audit_rule.agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="fromId != null and fromId != ''">
            AND pcx_audit_rule.from_id=#{fromId,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null and parentId != ''">
            AND pcx_audit_rule.parent_id=#{parentId,jdbcType=VARCHAR}
        </if>
        <if test="agyName != null and agyName != ''">
            AND pcx_audit_rule.agy_name=#{agyName,jdbcType=VARCHAR}
        </if>
        <if test="ruleCreator != null and ruleCreator != ''">
            AND pcx_audit_rule.rule_creator=#{ruleCreator,jdbcType=VARCHAR}
        </if>
        <if test="ruleModifyDate != null and ruleModifyDate != ''">
            AND pcx_audit_rule.rule_modify_date=#{ruleModifyDate,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND pcx_audit_rule.fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="whenCondition != null and whenCondition != ''">
            AND pcx_audit_rule.rule_when_condition=#{whenCondition,jdbcType=VARCHAR}
        </if>
        <if test="ruleIdentify != null">
            AND pcx_audit_rule.rule_identify=#{ruleIdentify,jdbcType=INTEGER}
        </if>
        <if test="ruleModule != null and ruleModule != ''">
            AND pcx_audit_rule.rule_module=#{ruleModule,jdbcType=VARCHAR}
        </if>
        <if test="ruleAct != null and ruleAct != ''">
            AND pcx_audit_rule.rule_act=#{ruleAct,jdbcType=VARCHAR}
        </if>
        <if test="keyWord != null and keyWord != ''">
            AND (rule_name like concat('%',#{keyWord,jdbcType=VARCHAR},'%')
            OR rule_id like concat('%',#{keyWord,jdbcType=VARCHAR},'%')
            OR rule_desc like concat('%',#{keyWord,jdbcType=VARCHAR},'%'))
        </if>
    </select>

    <update id="updateByConds" parameterType="com.pty.pcx.entity.wit.PcxAuditRule">
        UPDATE pcx_audit_rule SET
        <include refid="allColumnSet"/>
        WHERE 1=1
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="fromId != null and fromId != ''">
            AND from_id=#{fromId,jdbcType=VARCHAR}
        </if>
    </update>

    <delete id="removeByConds" parameterType="com.pty.pcx.entity.wit.PcxAuditRule">
        DELETE FROM pcx_audit_rule
        WHERE 1=1
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="fromId != null and fromId != ''">
            AND from_id=#{fromId,jdbcType=VARCHAR}
        </if>
    </delete>
</mapper>
