<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.wit.WitRuleResultDetailDao">
    <sql id="allColumn">
        ref_block, err_msg, audit_type, pass_cause,
        rule_type, created_time, rule_name, id,
        attach_id, cur_data, rule_id, creator,
        result_id, rule_reference, data_info, field1,
        pass_state, fiscal, bill_id, rule_field,
        rule_identify, field3, field2, field5,
        remarks, field4, status
    </sql>

    <sql id="allColumnAlias">
        ref_block as refBlock, err_msg as errMsg, audit_type as auditType, pass_cause as passCause,
        rule_type as ruleType, created_time as createdTime, rule_name as ruleName, id as id,
        attach_id as attachId, cur_data as curData, rule_id as ruleId, creator as creator,
        result_id as resultId, rule_reference as ruleReference, data_info as dataInfo, field1 as field1,
        pass_state as passState, fiscal as fiscal, bill_id as billId, rule_field as ruleField,
        rule_identify as ruleIdentify, field3 as field3, field2 as field2, field5 as field5,
        remarks as remarks, field4 as field4, status as status
    </sql>

    <sql id="allColumnValue">
        #{refBlock,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, #{auditType,jdbcType=VARCHAR},
        #{passCause,jdbcType=VARCHAR},
        #{ruleType,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR},
        #{id,jdbcType=VARCHAR},
        #{attachId,jdbcType=VARCHAR}, #{curData,jdbcType=VARCHAR}, #{ruleId,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{resultId,jdbcType=VARCHAR}, #{ruleReference,jdbcType=VARCHAR}, #{dataInfo,jdbcType=VARCHAR},
        #{field1,jdbcType=VARCHAR},
        #{passState,jdbcType=INTEGER}, #{fiscal,jdbcType=VARCHAR}, #{billId,jdbcType=VARCHAR},
        #{ruleField,jdbcType=VARCHAR},
        #{ruleIdentify,jdbcType=INTEGER}, #{field3,jdbcType=VARCHAR}, #{field2,jdbcType=VARCHAR},
        #{field5,jdbcType=VARCHAR},
        #{remarks,jdbcType=VARCHAR}, #{field4,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="refBlock != null and refBlock != ''">
                ref_block=#{refBlock,jdbcType=VARCHAR},
            </if>
            <if test="errMsg != null and errMsg != ''">
                err_msg=#{errMsg,jdbcType=VARCHAR},
            </if>
            <if test="auditType != null and auditType != ''">
                audit_type=#{auditType,jdbcType=VARCHAR},
            </if>
            <if test="passCause != null and passCause != ''">
                pass_cause=#{passCause,jdbcType=VARCHAR},
            </if>
            <if test="ruleType != null and ruleType != ''">
                rule_type=#{ruleType,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time=#{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                rule_name=#{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="attachId != null and attachId != ''">
                attach_id=#{attachId,jdbcType=VARCHAR},
            </if>
            <if test="curData != null and curData != ''">
                cur_data=#{curData,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null and ruleId != ''">
                rule_id=#{ruleId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                creator=#{creator,jdbcType=VARCHAR},
            </if>
            <if test="resultId != null and resultId != ''">
                result_id=#{resultId,jdbcType=VARCHAR},
            </if>
            <if test="ruleReference != null and ruleReference != ''">
                rule_reference=#{ruleReference,jdbcType=VARCHAR},
            </if>
            <if test="dataInfo != null and dataInfo != ''">
                data_info=#{dataInfo,jdbcType=VARCHAR},
            </if>
            <if test="field1 != null and field1 != ''">
                field1=#{field1,jdbcType=VARCHAR},
            </if>
            <if test="passState != null">
                pass_state=#{passState,jdbcType=INTEGER},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="billId != null and billId != ''">
                bill_id=#{billId,jdbcType=VARCHAR},
            </if>
            <if test="ruleField != null and ruleField != ''">
                rule_field=#{ruleField,jdbcType=VARCHAR},
            </if>
            <if test="ruleIdentify != null">
                rule_identify=#{ruleIdentify,jdbcType=INTEGER},
            </if>
            <if test="field3 != null and field3 != ''">
                field3=#{field3,jdbcType=VARCHAR},
            </if>
            <if test="field2 != null and field2 != ''">
                field2=#{field2,jdbcType=VARCHAR},
            </if>
            <if test="field5 != null and field5 != ''">
                field5=#{field5,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks=#{remarks,jdbcType=VARCHAR},
            </if>
            <if test="field4 != null and field4 != ''">
                field4=#{field4,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status=#{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="refBlock != null and refBlock != ''">
            AND ref_block=#{refBlock,jdbcType=VARCHAR}
        </if>
        <if test="errMsg != null and errMsg != ''">
            AND err_msg=#{errMsg,jdbcType=VARCHAR}
        </if>
        <if test="auditType != null and auditType != ''">
            AND audit_type=#{auditType,jdbcType=VARCHAR}
        </if>
        <if test="passCause != null and passCause != ''">
            AND pass_cause=#{passCause,jdbcType=VARCHAR}
        </if>
        <if test="ruleType != null and ruleType != ''">
            AND rule_type=#{ruleType,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time=#{createdTime,jdbcType=VARCHAR}
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND rule_name=#{ruleName,jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="attachId != null and attachId != ''">
            AND attach_id=#{attachId,jdbcType=VARCHAR}
        </if>
        <if test="curData != null and curData != ''">
            AND cur_data=#{curData,jdbcType=VARCHAR}
        </if>
        <if test="ruleId != null and ruleId != ''">
            AND rule_id=#{ruleId,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            AND creator=#{creator,jdbcType=VARCHAR}
        </if>
        <if test="resultId != null and resultId != ''">
            AND result_id=#{resultId,jdbcType=VARCHAR}
        </if>
        <if test="ruleReference != null and ruleReference != ''">
            AND rule_reference=#{ruleReference,jdbcType=VARCHAR}
        </if>
        <if test="dataInfo != null and dataInfo != ''">
            AND data_info=#{dataInfo,jdbcType=VARCHAR}
        </if>
        <if test="field1 != null and field1 != ''">
            AND field1=#{field1,jdbcType=VARCHAR}
        </if>
        <if test="passState != null">
            AND pass_state=#{passState,jdbcType=INTEGER}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="billId != null and billId != ''">
            AND bill_id=#{billId,jdbcType=VARCHAR}
        </if>
        <if test="ruleField != null and ruleField != ''">
            AND rule_field=#{ruleField,jdbcType=VARCHAR}
        </if>
        <if test="ruleIdentify != null">
            AND rule_identify=#{ruleIdentify,jdbcType=INTEGER}
        </if>
        <if test="field3 != null and field3 != ''">
            AND field3=#{field3,jdbcType=VARCHAR}
        </if>
        <if test="field2 != null and field2 != ''">
            AND field2=#{field2,jdbcType=VARCHAR}
        </if>
        <if test="field5 != null and field5 != ''">
            AND field5=#{field5,jdbcType=VARCHAR}
        </if>
        <if test="remarks != null and remarks != ''">
            AND remarks=#{remarks,jdbcType=VARCHAR}
        </if>
        <if test="field4 != null and field4 != ''">
            AND field4=#{field4,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            AND status=#{status,jdbcType=VARCHAR}
        </if>
        <if test="ruleIds != null and ruleIds.size() > 0">
            AND rule_id IN
            <foreach collection="ruleIds" item="ruleId" index="index" open="(" close=")" separator=",">
                #{ruleId}
            </foreach>
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.wit.WitRuleResultDetail">
        INSERT INTO pty_rule_result_detail (
        <include refid="allColumn"/>
        ) VALUES (
        <include refid="allColumnValue"/>
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        delete from pty_rule_result_detail where id=#{value}
    </delete>
    <delete id="delByRuleId">
        delete from pty_rule_result_detail where rule_id=#{ruleId}
    </delete>
    <delete id="delByBillIdAndBlock">
        delete from pty_rule_result_detail where bill_id=#{billId} and ref_block=#{refBlock}
    </delete>

    <update id="update" parameterType="com.pty.pcx.entity.wit.WitRuleResultDetail">
        UPDATE pty_rule_result_detail SET
        <include refid="allColumnSet"/>
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.wit.WitRuleResultDetail">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pty_rule_result_detail
        WHERE id=#{value}
    </select>

    <select id="selectByResultId" parameterType="string" resultType="com.pty.pcx.entity.wit.WitRuleResultDetail">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pty_rule_result_detail
        WHERE result_id=#{value}
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.wit.WitRuleResultDetail" resultType="com.pty.pcx.entity.wit.WitRuleResultDetail">
        SELECT
            <include refid="allColumnAlias"/>
        FROM pty_rule_result_detail
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

</mapper>