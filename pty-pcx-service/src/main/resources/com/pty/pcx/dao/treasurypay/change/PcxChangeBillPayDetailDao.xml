<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.treasurypay.change.PcxChangeBillPayDetailDao">
    <sql id="allColumn">
    agy_code,                                         agy_name,                                         balance_id,                                       balance_no,
    bill_id,                                          change_bill_id,                                   check_amt,                                        created_time,
    creator,                                          creator_name,                                     expense_code,                                     expense_id,
    fiscal,                                           id,                                               info_source,                                      info_source_id,
    is_deleted,                                       modified_time,                                    modifier,                                         modifier_name,
    mof_div_code,                                     paid_time,                                        pay_account_city,                                 pay_account_name,
    pay_account_no,                                   pay_account_type_code,                            pay_bank_code,                                    pay_bank_name,
    pay_no,                                           pay_status,                                       pay_type,                                         pay_type_code,
    pay_type_name,                                    payee_account_city,                               payee_account_name,                               payee_account_no,
    payee_bank_code,                                  payee_bank_name,                                  payee_bank_node_name,                             payee_bank_node_no,
    paying_time,                                      ref_set_id,                                       settlement_type,                                  settlement_uk,
    summary,                                          ver,                                              pay_account_type_name
    </sql>

    <sql id="allColumnAlias">
    agy_code as agyCode,                              agy_name as agyName,                              balance_id as balanceId,                          balance_no as balanceNo,
    bill_id as billId,                                change_bill_id as changeBillId,                   check_amt as checkAmt,                            created_time as createdTime,
    creator as creator,                               creator_name as creatorName,                      expense_code as expenseCode,                      expense_id as expenseId,
    fiscal as fiscal,                                 id as id,                                         info_source as infoSource,                        info_source_id as infoSourceId,
    is_deleted as isDeleted,                          modified_time as modifiedTime,                    modifier as modifier,                             modifier_name as modifierName,
    mof_div_code as mofDivCode,                       paid_time as paidTime,                            pay_account_city as payAccountCity,               pay_account_name as payAccountName,
    pay_account_no as payAccountNo,                   pay_account_type_code as payAccountTypeCode,      pay_bank_code as payBankCode,                     pay_bank_name as payBankName,
    pay_no as payNo,                                  pay_status as payStatus,                          pay_type as payType,                              pay_type_code as payTypeCode,
    pay_type_name as payTypeName,                     payee_account_city as payeeAccountCity,           payee_account_name as payeeAccountName,           payee_account_no as payeeAccountNo,
    payee_bank_code as payeeBankCode,                 payee_bank_name as payeeBankName,                 payee_bank_node_name as payeeBankNodeName,        payee_bank_node_no as payeeBankNodeNo,
    paying_time as payingTime,                        ref_set_id as refSetId,                           settlement_type as settlementType,                settlement_uk as settlementUk,
    summary as summary,                               ver as ver,                                       pay_account_type_name as payAccountTypeName,
    settlement_type_name as settlementTypeName,       payee_account_type_code as payeeAccountTypeCode,           payee_account_type_name as payeeAccountTypeName
    </sql>

    <sql id="allColumnValue">
        #{agyCode,jdbcType=VARCHAR},            #{agyName,jdbcType=VARCHAR},            #{balanceId,jdbcType=VARCHAR},          #{balanceNo,jdbcType=VARCHAR},
        #{billId,jdbcType=VARCHAR},             #{changeBillId,jdbcType=VARCHAR},       #{checkAmt,jdbcType=DECIMAL},           #{createdTime,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},            #{creatorName,jdbcType=VARCHAR},        #{expenseCode,jdbcType=VARCHAR},        #{expenseId,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},             #{id,jdbcType=VARCHAR},                 #{infoSource,jdbcType=VARCHAR},         #{infoSourceId,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=INTEGER},          #{modifiedTime,jdbcType=VARCHAR},       #{modifier,jdbcType=VARCHAR},           #{modifierName,jdbcType=VARCHAR},
        #{mofDivCode,jdbcType=VARCHAR},         #{paidTime,jdbcType=VARCHAR},           #{payAccountCity,jdbcType=VARCHAR},         #{payAccountName,jdbcType=VARCHAR},
        #{payAccountNo,jdbcType=VARCHAR},       #{payAccountTypeCode,jdbcType=VARCHAR}, #{payBankCode,jdbcType=VARCHAR},        #{payBankName,jdbcType=VARCHAR},
        #{payNo,jdbcType=VARCHAR},              #{payStatus,jdbcType=VARCHAR},          #{payType,jdbcType=VARCHAR},            #{payTypeCode,jdbcType=VARCHAR},
        #{payTypeName,jdbcType=VARCHAR},        #{payeeAccountCity,jdbcType=VARCHAR},   #{payeeAccountName,jdbcType=VARCHAR},   #{payeeAccountNo,jdbcType=VARCHAR},
        #{payeeBankCode,jdbcType=VARCHAR},      #{payeeBankName,jdbcType=VARCHAR},      #{payeeBankNodeName,jdbcType=VARCHAR},  #{payeeBankNodeNo,jdbcType=VARCHAR},
        #{payingTime,jdbcType=VARCHAR},         #{refSetId,jdbcType=VARCHAR},           #{settlementType,jdbcType=VARCHAR},     #{settlementUk,jdbcType=VARCHAR},
        #{summary,jdbcType=VARCHAR},           #{ver,jdbcType=INTEGER},               #{payAccountTypeName,jdbcType=VARCHAR},
        #{settlementTypeName,jdbcType=VARCHAR}, #{payeeAccountTypeCode,jdbcType=VARCHAR},   #{payeeAccountTypeName,jdbcType=VARCHAR}
    </sql>

    <sql id="batchAllColumnValue">
        #{item.agyCode,jdbcType=VARCHAR},       #{item.agyName,jdbcType=VARCHAR},       #{item.balanceId,jdbcType=VARCHAR},     #{item.balanceNo,jdbcType=VARCHAR},
        #{item.billId,jdbcType=VARCHAR},        #{item.changeBillId,jdbcType=VARCHAR},  #{item.checkAmt,jdbcType=DECIMAL},      #{item.createdTime,jdbcType=VARCHAR},
        #{item.creator,jdbcType=VARCHAR},       #{item.creatorName,jdbcType=VARCHAR},   #{item.expenseCode,jdbcType=VARCHAR},   #{item.expenseId,jdbcType=VARCHAR},
        #{item.fiscal,jdbcType=VARCHAR},        #{item.id,jdbcType=VARCHAR},            #{item.infoSource,jdbcType=VARCHAR},    #{item.infoSourceId,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=INTEGER},     #{item.modifiedTime,jdbcType=VARCHAR},  #{item.modifier,jdbcType=VARCHAR},      #{item.modifierName,jdbcType=VARCHAR},
        #{item.mofDivCode,jdbcType=VARCHAR},    #{item.paidTime,jdbcType=VARCHAR},      #{item.payAccountCity,jdbcType=VARCHAR},    #{item.payAccountName,jdbcType=VARCHAR},
        #{item.payAccountNo,jdbcType=VARCHAR},  #{item.payAccountTypeCode,jdbcType=VARCHAR},#{item.payBankCode,jdbcType=VARCHAR},   #{item.payBankName,jdbcType=VARCHAR},
        #{item.payNo,jdbcType=VARCHAR},         #{item.payStatus,jdbcType=VARCHAR},     #{item.payType,jdbcType=VARCHAR},       #{item.payTypeCode,jdbcType=VARCHAR},
        #{item.payTypeName,jdbcType=VARCHAR},   #{item.payeeAccountCity,jdbcType=VARCHAR},  #{item.payeeAccountName,jdbcType=VARCHAR},#{item.payeeAccountNo,jdbcType=VARCHAR},
        #{item.payeeBankCode,jdbcType=VARCHAR}, #{item.payeeBankName,jdbcType=VARCHAR}, #{item.payeeBankNodeName,jdbcType=VARCHAR},#{item.payeeBankNodeNo,jdbcType=VARCHAR},
        #{item.payingTime,jdbcType=VARCHAR},    #{item.refSetId,jdbcType=VARCHAR},      #{item.settlementType,jdbcType=VARCHAR},#{item.settlementUk,jdbcType=VARCHAR},
        #{item.summary,jdbcType=VARCHAR},      #{item.ver,jdbcType=INTEGER},           #{item.payAccountTypeName,jdbcType=VARCHAR},
        #{item.settlementTypeName,jdbcType=VARCHAR},#{item.payeeAccountTypeCode,jdbcType=VARCHAR},   #{item.payeeAccountTypeName,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">agy_code=#{agyCode,jdbcType=VARCHAR},</if>
            <if test="agyName != null and agyName != ''">agy_name=#{agyName,jdbcType=VARCHAR},</if>
            <if test="balanceId != null and balanceId != ''">balance_id=#{balanceId,jdbcType=VARCHAR},</if>
            <if test="balanceNo != null and balanceNo != ''">balance_no=#{balanceNo,jdbcType=VARCHAR},</if>
            <if test="billId != null and billId != ''">bill_id=#{billId,jdbcType=VARCHAR},</if>
            <if test="changeBillId != null and changeBillId != ''">change_bill_id=#{changeBillId,jdbcType=VARCHAR},</if>
            <if test="checkAmt != null">check_amt=#{checkAmt,jdbcType=DECIMAL},</if>
            <if test="createdTime != null and createdTime != ''">created_time=#{createdTime,jdbcType=VARCHAR},</if>
            <if test="creator != null and creator != ''">creator=#{creator,jdbcType=VARCHAR},</if>
            <if test="creatorName != null and creatorName != ''">creator_name=#{creatorName,jdbcType=VARCHAR},</if>
            <if test="expenseCode != null and expenseCode != ''">expense_code=#{expenseCode,jdbcType=VARCHAR},</if>
            <if test="expenseId != null and expenseId != ''">expense_id=#{expenseId,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''">fiscal=#{fiscal,jdbcType=VARCHAR},</if>
            <if test="infoSource != null and infoSource != ''">info_source=#{infoSource,jdbcType=VARCHAR},</if>
            <if test="infoSourceId != null and infoSourceId != ''">info_source_id=#{infoSourceId,jdbcType=VARCHAR},</if>
            <if test="isDeleted != null">is_deleted=#{isDeleted,jdbcType=INTEGER},</if>
            <if test="modifiedTime != null and modifiedTime != ''">modified_time=#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="modifier != null and modifier != ''">modifier=#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null and modifierName != ''">modifier_name=#{modifierName,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code=#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="paidTime != null and paidTime != ''">paid_time=#{paidTime,jdbcType=VARCHAR},</if>
            <if test="payAccountCity != null and payAccountCity != ''">pay_account_city=#{payAccountCity,jdbcType=VARCHAR},</if>
            <if test="payAccountName != null and payAccountName != ''">pay_account_name=#{payAccountName,jdbcType=VARCHAR},</if>
            <if test="payAccountNo != null and payAccountNo != ''">pay_account_no=#{payAccountNo,jdbcType=VARCHAR},</if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">pay_account_type_code=#{payAccountTypeCode,jdbcType=VARCHAR},</if>
            <if test="payBankCode != null and payBankCode != ''">pay_bank_code=#{payBankCode,jdbcType=VARCHAR},</if>
            <if test="payBankName != null and payBankName != ''">pay_bank_name=#{payBankName,jdbcType=VARCHAR},</if>
            <if test="payNo != null and payNo != ''">pay_no=#{payNo,jdbcType=VARCHAR},</if>
            <if test="payStatus != null and payStatus != ''">pay_status=#{payStatus,jdbcType=VARCHAR},</if>
            <if test="payType != null and payType != ''">pay_type=#{payType,jdbcType=VARCHAR},</if>
            <if test="payTypeCode != null and payTypeCode != ''">pay_type_code=#{payTypeCode,jdbcType=VARCHAR},</if>
            <if test="payTypeName != null and payTypeName != ''">pay_type_name=#{payTypeName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountCity != null and payeeAccountCity != ''">payee_account_city=#{payeeAccountCity,jdbcType=VARCHAR},</if>
            <if test="payeeAccountName != null and payeeAccountName != ''">payee_account_name=#{payeeAccountName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountNo != null and payeeAccountNo != ''">payee_account_no=#{payeeAccountNo,jdbcType=VARCHAR},</if>
            <if test="payeeBankCode != null and payeeBankCode != ''">payee_bank_code=#{payeeBankCode,jdbcType=VARCHAR},</if>
            <if test="payeeBankName != null and payeeBankName != ''">payee_bank_name=#{payeeBankName,jdbcType=VARCHAR},</if>
            <if test="payeeBankNodeName != null and payeeBankNodeName != ''">payee_bank_node_name=#{payeeBankNodeName,jdbcType=VARCHAR},</if>
            <if test="payeeBankNodeNo != null and payeeBankNodeNo != ''">payee_bank_node_no=#{payeeBankNodeNo,jdbcType=VARCHAR},</if>
            <if test="payingTime != null and payingTime != ''">paying_time=#{payingTime,jdbcType=VARCHAR},</if>
            <if test="refSetId != null and refSetId != ''">ref_set_id=#{refSetId,jdbcType=VARCHAR},</if>
            <if test="settlementType != null and settlementType != ''">settlement_type=#{settlementType,jdbcType=VARCHAR},</if>
            <if test="settlementUk != null and settlementUk != ''">settlement_uk=#{settlementUk,jdbcType=VARCHAR},</if>
            <if test="summary != null and summary != ''">summary=#{summary,jdbcType=VARCHAR},</if>
            <if test="ver != null">ver=#{ver,jdbcType=INTEGER},</if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">pay_account_type_name=#{payAccountTypeName,jdbcType=VARCHAR},</if>
            <if test="settlementTypeName != null and settlementTypeName != ''">settlement_type_name=#{settlementTypeName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountTypeCode != null and payeeAccountTypeCode != ''">payee_account_type_code=#{payeeAccountTypeCode,jdbcType=VARCHAR},</if>
            <if test="payeeAccountTypeName != null and payeeAccountTypeName != ''">payee_account_type_name=#{payeeAccountTypeName,jdbcType=INTEGER},</if>
        </trim>
    </sql>

    <sql id="insertSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">agy_code,</if>
            <if test="agyName != null and agyName != ''">agy_name,</if>
            <if test="balanceId != null and balanceId != ''">balance_id,</if>
            <if test="balanceNo != null and balanceNo != ''">balance_no,</if>
            <if test="billId != null and billId != ''">bill_id,</if>
            <if test="changeBillId != null and changeBillId != ''">change_bill_id,</if>
            <if test="checkAmt != null">check_amt,</if>
            <if test="createdTime != null and createdTime != ''">created_time,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="creatorName != null and creatorName != ''">creator_name,</if>
            <if test="expenseCode != null and expenseCode != ''">expense_code,</if>
            <if test="expenseId != null and expenseId != ''">expense_id,</if>
            <if test="fiscal != null and fiscal != ''">fiscal,</if>
            <if test="id != null and id != ''">id,</if>
            <if test="infoSource != null and infoSource != ''">info_source,</if>
            <if test="infoSourceId != null and infoSourceId != ''">info_source_id,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="modifiedTime != null and modifiedTime != ''">modified_time,</if>
            <if test="modifier != null and modifier != ''">modifier,</if>
            <if test="modifierName != null and modifierName != ''">modifier_name,</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code,</if>
            <if test="paidTime != null and paidTime != ''">paid_time,</if>
            <if test="payAccountCity != null and payAccountCity != ''">pay_account_city,</if>
            <if test="payAccountName != null and payAccountName != ''">pay_account_name,</if>
            <if test="payAccountNo != null and payAccountNo != ''">pay_account_no,</if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">pay_account_type_code,</if>
            <if test="payBankCode != null and payBankCode != ''">pay_bank_code,</if>
            <if test="payBankName != null and payBankName != ''">pay_bank_name,</if>
            <if test="payNo != null and payNo != ''">pay_no,</if>
            <if test="payStatus != null and payStatus != ''">pay_status,</if>
            <if test="payType != null and payType != ''">pay_type,</if>
            <if test="payTypeCode != null and payTypeCode != ''">pay_type_code,</if>
            <if test="payTypeName != null and payTypeName != ''">pay_type_name,</if>
            <if test="payeeAccountCity != null and payeeAccountCity != ''">payee_account_city,</if>
            <if test="payeeAccountName != null and payeeAccountName != ''">payee_account_name,</if>
            <if test="payeeAccountNo != null and payeeAccountNo != ''">payee_account_no,</if>
            <if test="payeeBankCode != null and payeeBankCode != ''">payee_bank_code,</if>
            <if test="payeeBankName != null and payeeBankName != ''">payee_bank_name,</if>
            <if test="payeeBankNodeName != null and payeeBankNodeName != ''">payee_bank_node_name,</if>
            <if test="payeeBankNodeNo != null and payeeBankNodeNo != ''">payee_bank_node_no,</if>
            <if test="payingTime != null and payingTime != ''">paying_time,</if>
            <if test="refSetId != null and refSetId != ''">ref_set_id,</if>
            <if test="settlementType != null and settlementType != ''">settlement_type,</if>
            <if test="settlementUk != null and settlementUk != ''">settlement_uk,</if>
            <if test="summary != null and summary != ''">summary,</if>
            <if test="ver != null">ver,</if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">pay_account_type_name,</if>
            <if test="settlementTypeName != null and settlementTypeName != ''">settlement_type_name,</if>
            <if test="payeeAccountTypeCode != null and payeeAccountTypeCode != ''">payee_account_type_code,</if>
            <if test="payeeAccountTypeName != null and payeeAccountTypeName != ''">payee_account_type_name,</if>
        </trim>
    </sql>

    <sql id="insertSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">#{agyCode,jdbcType=VARCHAR},</if>
            <if test="agyName != null and agyName != ''">#{agyName,jdbcType=VARCHAR},</if>
            <if test="balanceId != null and balanceId != ''">#{balanceId,jdbcType=VARCHAR},</if>
            <if test="balanceNo != null and balanceNo != ''">#{balanceNo,jdbcType=VARCHAR},</if>
            <if test="billId != null and billId != ''">#{billId,jdbcType=VARCHAR},</if>
            <if test="changeBillId != null and changeBillId != ''">#{changeBillId,jdbcType=VARCHAR},</if>
            <if test="checkAmt != null">#{checkAmt,jdbcType=DECIMAL},</if>
            <if test="createdTime != null and createdTime != ''">#{createdTime,jdbcType=VARCHAR},</if>
            <if test="creator != null and creator != ''">#{creator,jdbcType=VARCHAR},</if>
            <if test="creatorName != null and creatorName != ''">#{creatorName,jdbcType=VARCHAR},</if>
            <if test="expenseCode != null and expenseCode != ''">#{expenseCode,jdbcType=VARCHAR},</if>
            <if test="expenseId != null and expenseId != ''">#{expenseId,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''">#{fiscal,jdbcType=VARCHAR},</if>
            <if test="id != null and id != ''">#{id,jdbcType=VARCHAR},</if>
            <if test="infoSource != null and infoSource != ''">#{infoSource,jdbcType=VARCHAR},</if>
            <if test="infoSourceId != null and infoSourceId != ''">#{infoSourceId,jdbcType=VARCHAR},</if>
            <if test="isDeleted != null">#{isDeleted,jdbcType=INTEGER},</if>
            <if test="modifiedTime != null and modifiedTime != ''">#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="modifier != null and modifier != ''">#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null and modifierName != ''">#{modifierName,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''">#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="paidTime != null and paidTime != ''">#{paidTime,jdbcType=VARCHAR},</if>
            <if test="payAccountCity != null and payAccountCity != ''">#{payAccountCity,jdbcType=VARCHAR},</if>
            <if test="payAccountName != null and payAccountName != ''">#{payAccountName,jdbcType=VARCHAR},</if>
            <if test="payAccountNo != null and payAccountNo != ''">#{payAccountNo,jdbcType=VARCHAR},</if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">#{payAccountTypeCode,jdbcType=VARCHAR},</if>
            <if test="payBankCode != null and payBankCode != ''">#{payBankCode,jdbcType=VARCHAR},</if>
            <if test="payBankName != null and payBankName != ''">#{payBankName,jdbcType=VARCHAR},</if>
            <if test="payNo != null and payNo != ''">#{payNo,jdbcType=VARCHAR},</if>
            <if test="payStatus != null and payStatus != ''">#{payStatus,jdbcType=VARCHAR},</if>
            <if test="payType != null and payType != ''">#{payType,jdbcType=VARCHAR},</if>
            <if test="payTypeCode != null and payTypeCode != ''">#{payTypeCode,jdbcType=VARCHAR},</if>
            <if test="payTypeName != null and payTypeName != ''">#{payTypeName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountCity != null and payeeAccountCity != ''">#{payeeAccountCity,jdbcType=VARCHAR},</if>
            <if test="payeeAccountName != null and payeeAccountName != ''">#{payeeAccountName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountNo != null and payeeAccountNo != ''">#{payeeAccountNo,jdbcType=VARCHAR},</if>
            <if test="payeeBankCode != null and payeeBankCode != ''">#{payeeBankCode,jdbcType=VARCHAR},</if>
            <if test="payeeBankName != null and payeeBankName != ''">#{payeeBankName,jdbcType=VARCHAR},</if>
            <if test="payeeBankNodeName != null and payeeBankNodeName != ''">#{payeeBankNodeName,jdbcType=VARCHAR},</if>
            <if test="payeeBankNodeNo != null and payeeBankNodeNo != ''">#{payeeBankNodeNo,jdbcType=VARCHAR},</if>
            <if test="payingTime != null and payingTime != ''">#{payingTime,jdbcType=VARCHAR},</if>
            <if test="refSetId != null and refSetId != ''">#{refSetId,jdbcType=VARCHAR},</if>
            <if test="settlementType != null and settlementType != ''">#{settlementType,jdbcType=VARCHAR},</if>
            <if test="settlementUk != null and settlementUk != ''">#{settlementUk,jdbcType=VARCHAR},</if>
            <if test="summary != null and summary != ''">#{summary,jdbcType=VARCHAR},</if>
            <if test="ver != null">#{ver,jdbcType=INTEGER},</if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">#{payAccountTypeName,jdbcType=VARCHAR},</if>
            <if test="settlementTypeName != null and settlementTypeName != ''">#{settlementTypeName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountTypeCode != null and payeeAccountTypeCode != ''">#{payeeAccountTypeCode,jdbcType=VARCHAR},</if>
            <if test="payeeAccountTypeName != null and payeeAccountTypeName != ''">#{payeeAccountTypeName,jdbcType=VARCHAR},</if>
        </trim>
    </sql>


    <sql id="updateSelectiveColumn">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">agy_code,</if>
            <if test="agyName != null and agyName != ''">agy_name,</if>
            <if test="balanceId != null and balanceId != ''">balance_id,</if>
            <if test="balanceNo != null and balanceNo != ''">balance_no,</if>
            <if test="billId != null and billId != ''">bill_id,</if>
            <if test="changeBillId != null and changeBillId != ''">change_bill_id,</if>
            <if test="checkAmt != null">check_amt,</if>
            <if test="createdTime != null and createdTime != ''">created_time,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="creatorName != null and creatorName != ''">creator_name,</if>
            <if test="expenseCode != null and expenseCode != ''">expense_code,</if>
            <if test="expenseId != null and expenseId != ''">expense_id,</if>
            <if test="fiscal != null and fiscal != ''">fiscal,</if>
            <if test="infoSource != null and infoSource != ''">info_source,</if>
            <if test="infoSourceId != null and infoSourceId != ''">info_source_id,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="modifiedTime != null and modifiedTime != ''">modified_time,</if>
            <if test="modifier != null and modifier != ''">modifier,</if>
            <if test="modifierName != null and modifierName != ''">modifier_name,</if>
            <if test="mofDivCode != null and mofDivCode != ''">mof_div_code,</if>
            <if test="paidTime != null and paidTime != ''">paid_time,</if>
            <if test="payAccountCity != null and payAccountCity != ''">pay_account_city,</if>
            <if test="payAccountName != null and payAccountName != ''">pay_account_name,</if>
            <if test="payAccountNo != null and payAccountNo != ''">pay_account_no,</if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">pay_account_type_code,</if>
            <if test="payBankCode != null and payBankCode != ''">pay_bank_code,</if>
            <if test="payBankName != null and payBankName != ''">pay_bank_name,</if>
            <if test="payNo != null and payNo != ''">pay_no,</if>
            <if test="payStatus != null and payStatus != ''">pay_status,</if>
            <if test="payType != null and payType != ''">pay_type,</if>
            <if test="payTypeCode != null and payTypeCode != ''">pay_type_code,</if>
            <if test="payTypeName != null and payTypeName != ''">pay_type_name,</if>
            <if test="payeeAccountCity != null and payeeAccountCity != ''">payee_account_city,</if>
            <if test="payeeAccountName != null and payeeAccountName != ''">payee_account_name,</if>
            <if test="payeeAccountNo != null and payeeAccountNo != ''">payee_account_no,</if>
            <if test="payeeBankCode != null and payeeBankCode != ''">payee_bank_code,</if>
            <if test="payeeBankName != null and payeeBankName != ''">payee_bank_name,</if>
            <if test="payeeBankNodeName != null and payeeBankNodeName != ''">payee_bank_node_name,</if>
            <if test="payeeBankNodeNo != null and payeeBankNodeNo != ''">payee_bank_node_no,</if>
            <if test="payingTime != null and payingTime != ''">paying_time,</if>
            <if test="refSetId != null and refSetId != ''">ref_set_id,</if>
            <if test="settlementType != null and settlementType != ''">settlement_type,</if>
            <if test="settlementUk != null and settlementUk != ''">settlement_uk,</if>
            <if test="summary != null and summary != ''">summary,</if>
            <if test="ver != null">ver,</if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">pay_account_type_name,</if>
            <if test="settlementTypeName != null and settlementTypeName != ''">settlement_type_name,</if>
            <if test="payeeAccountTypeCode != null and payeeAccountTypeCode != ''">payee_account_type_code,</if>
            <if test="payeeAccountTypeName != null and payeeAccountTypeName != ''">payee_account_type_name,</if>
        </trim>
    </sql>

    <sql id="updateSelectiveValue">
        <trim suffixOverrides=",">
            <if test="agyCode != null and agyCode != ''">#{agyCode,jdbcType=VARCHAR},</if>
            <if test="agyName != null and agyName != ''">#{agyName,jdbcType=VARCHAR},</if>
            <if test="balanceId != null and balanceId != ''">#{balanceId,jdbcType=VARCHAR},</if>
            <if test="balanceNo != null and balanceNo != ''">#{balanceNo,jdbcType=VARCHAR},</if>
            <if test="billId != null and billId != ''">#{billId,jdbcType=VARCHAR},</if>
            <if test="changeBillId != null and changeBillId != ''">#{changeBillId,jdbcType=VARCHAR},</if>
            <if test="checkAmt != null">#{checkAmt,jdbcType=DECIMAL},</if>
            <if test="createdTime != null and createdTime != ''">#{createdTime,jdbcType=VARCHAR},</if>
            <if test="creator != null and creator != ''">#{creator,jdbcType=VARCHAR},</if>
            <if test="creatorName != null and creatorName != ''">#{creatorName,jdbcType=VARCHAR},</if>
            <if test="expenseCode != null and expenseCode != ''">#{expenseCode,jdbcType=VARCHAR},</if>
            <if test="expenseId != null and expenseId != ''">#{expenseId,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''">#{fiscal,jdbcType=VARCHAR},</if>
            <if test="infoSource != null and infoSource != ''">#{infoSource,jdbcType=VARCHAR},</if>
            <if test="infoSourceId != null and infoSourceId != ''">#{infoSourceId,jdbcType=VARCHAR},</if>
            <if test="isDeleted != null">#{isDeleted,jdbcType=INTEGER},</if>
            <if test="modifiedTime != null and modifiedTime != ''">#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="modifier != null and modifier != ''">#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null and modifierName != ''">#{modifierName,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''">#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="paidTime != null and paidTime != ''">#{paidTime,jdbcType=VARCHAR},</if>
            <if test="payAccountCity != null and payAccountCity != ''">#{payAccountCity,jdbcType=VARCHAR},</if>
            <if test="payAccountName != null and payAccountName != ''">#{payAccountName,jdbcType=VARCHAR},</if>
            <if test="payAccountNo != null and payAccountNo != ''">#{payAccountNo,jdbcType=VARCHAR},</if>
            <if test="payAccountTypeCode != null and payAccountTypeCode != ''">#{payAccountTypeCode,jdbcType=VARCHAR},</if>
            <if test="payBankCode != null and payBankCode != ''">#{payBankCode,jdbcType=VARCHAR},</if>
            <if test="payBankName != null and payBankName != ''">#{payBankName,jdbcType=VARCHAR},</if>
            <if test="payNo != null and payNo != ''">#{payNo,jdbcType=VARCHAR},</if>
            <if test="payStatus != null and payStatus != ''">#{payStatus,jdbcType=VARCHAR},</if>
            <if test="payType != null and payType != ''">#{payType,jdbcType=VARCHAR},</if>
            <if test="payTypeCode != null and payTypeCode != ''">#{payTypeCode,jdbcType=VARCHAR},</if>
            <if test="payTypeName != null and payTypeName != ''">#{payTypeName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountCity != null and payeeAccountCity != ''">#{payeeAccountCity,jdbcType=VARCHAR},</if>
            <if test="payeeAccountName != null and payeeAccountName != ''">#{payeeAccountName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountNo != null and payeeAccountNo != ''">#{payeeAccountNo,jdbcType=VARCHAR},</if>
            <if test="payeeBankCode != null and payeeBankCode != ''">#{payeeBankCode,jdbcType=VARCHAR},</if>
            <if test="payeeBankName != null and payeeBankName != ''">#{payeeBankName,jdbcType=VARCHAR},</if>
            <if test="payeeBankNodeName != null and payeeBankNodeName != ''">#{payeeBankNodeName,jdbcType=VARCHAR},</if>
            <if test="payeeBankNodeNo != null and payeeBankNodeNo != ''">#{payeeBankNodeNo,jdbcType=VARCHAR},</if>
            <if test="payingTime != null and payingTime != ''">#{payingTime,jdbcType=VARCHAR},</if>
            <if test="refSetId != null and refSetId != ''">#{refSetId,jdbcType=VARCHAR},</if>
            <if test="settlementType != null and settlementType != ''">#{settlementType,jdbcType=VARCHAR},</if>
            <if test="settlementUk != null and settlementUk != ''">#{settlementUk,jdbcType=VARCHAR},</if>
            <if test="summary != null and summary != ''">#{summary,jdbcType=VARCHAR},</if>
            <if test="ver != null">#{ver,jdbcType=INTEGER},</if>
            <if test="payAccountTypeName != null and payAccountTypeName != ''">#{payAccountTypeName,jdbcType=VARCHAR},</if>
            <if test="settlementTypeName != null and settlementTypeName != ''">#{settlementTypeName,jdbcType=VARCHAR},</if>
            <if test="payeeAccountTypeCode != null and payeeAccountTypeCode != ''">#{payeeAccountTypeCode,jdbcType=VARCHAR},</if>
            <if test="payeeAccountTypeName != null and payeeAccountTypeName != ''">#{payeeAccountTypeName,jdbcType=VARCHAR},</if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="agyCode != null and agyCode != ''">AND agy_code=#{agyCode,jdbcType=VARCHAR}</if>
        <if test="agyName != null and agyName != ''">AND agy_name=#{agyName,jdbcType=VARCHAR}</if>
        <if test="balanceId != null and balanceId != ''">AND balance_id=#{balanceId,jdbcType=VARCHAR}</if>
        <if test="balanceNo != null and balanceNo != ''">AND balance_no=#{balanceNo,jdbcType=VARCHAR}</if>
        <if test="billId != null and billId != ''">AND bill_id=#{billId,jdbcType=VARCHAR}</if>
        <if test="changeBillId != null and changeBillId != ''">AND change_bill_id=#{changeBillId,jdbcType=VARCHAR}</if>
        <if test="checkAmt != null">AND check_amt=#{checkAmt,jdbcType=DECIMAL}</if>
        <if test="createdTime != null and createdTime != ''">AND created_time=#{createdTime,jdbcType=VARCHAR}</if>
        <if test="creator != null and creator != ''">AND creator=#{creator,jdbcType=VARCHAR}</if>
        <if test="creatorName != null and creatorName != ''">AND creator_name=#{creatorName,jdbcType=VARCHAR}</if>
        <if test="expenseCode != null and expenseCode != ''">AND expense_code=#{expenseCode,jdbcType=VARCHAR}</if>
        <if test="expenseId != null and expenseId != ''">AND expense_id=#{expenseId,jdbcType=VARCHAR}</if>
        <if test="fiscal != null and fiscal != ''">AND fiscal=#{fiscal,jdbcType=VARCHAR}</if>
        <if test="id != null and id != ''">AND id=#{id,jdbcType=VARCHAR}</if>
        <if test="infoSource != null and infoSource != ''">AND info_source=#{infoSource,jdbcType=VARCHAR}</if>
        <if test="infoSourceId != null and infoSourceId != ''">AND info_source_id=#{infoSourceId,jdbcType=VARCHAR}</if>
        <if test="isDeleted != null">AND is_deleted=#{isDeleted,jdbcType=INTEGER}</if>
        <if test="modifiedTime != null and modifiedTime != ''">AND modified_time=#{modifiedTime,jdbcType=VARCHAR}</if>
        <if test="modifier != null and modifier != ''">AND modifier=#{modifier,jdbcType=VARCHAR}</if>
        <if test="modifierName != null and modifierName != ''">AND modifier_name=#{modifierName,jdbcType=VARCHAR}</if>
        <if test="mofDivCode != null and mofDivCode != ''">AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}</if>
        <if test="paidTime != null and paidTime != ''">AND paid_time=#{paidTime,jdbcType=VARCHAR}</if>
        <if test="payAccountCity != null and payAccountCity != ''">AND pay_account_city=#{payAccountCity,jdbcType=VARCHAR}</if>
        <if test="payAccountName != null and payAccountName != ''">AND pay_account_name=#{payAccountName,jdbcType=VARCHAR}</if>
        <if test="payAccountNo != null and payAccountNo != ''">AND pay_account_no=#{payAccountNo,jdbcType=VARCHAR}</if>
        <if test="payAccountTypeCode != null and payAccountTypeCode != ''">AND pay_account_type_code=#{payAccountTypeCode,jdbcType=VARCHAR}</if>
        <if test="payBankCode != null and payBankCode != ''">AND pay_bank_code=#{payBankCode,jdbcType=VARCHAR}</if>
        <if test="payBankName != null and payBankName != ''">AND pay_bank_name=#{payBankName,jdbcType=VARCHAR}</if>
        <if test="payNo != null and payNo != ''">AND pay_no=#{payNo,jdbcType=VARCHAR}</if>
        <if test="payStatus != null and payStatus != ''">AND pay_status=#{payStatus,jdbcType=VARCHAR}</if>
        <if test="payType != null and payType != ''">AND pay_type=#{payType,jdbcType=VARCHAR}</if>
        <if test="payTypeCode != null and payTypeCode != ''">AND pay_type_code=#{payTypeCode,jdbcType=VARCHAR}</if>
        <if test="payTypeName != null and payTypeName != ''">AND pay_type_name=#{payTypeName,jdbcType=VARCHAR}</if>
        <if test="payeeAccountCity != null and payeeAccountCity != ''">AND payee_account_city=#{payeeAccountCity,jdbcType=VARCHAR}</if>
        <if test="payeeAccountName != null and payeeAccountName != ''">AND payee_account_name=#{payeeAccountName,jdbcType=VARCHAR}</if>
        <if test="payeeAccountNo != null and payeeAccountNo != ''">AND payee_account_no=#{payeeAccountNo,jdbcType=VARCHAR}</if>
        <if test="payeeBankCode != null and payeeBankCode != ''">AND payee_bank_code=#{payeeBankCode,jdbcType=VARCHAR}</if>
        <if test="payeeBankName != null and payeeBankName != ''">AND payee_bank_name=#{payeeBankName,jdbcType=VARCHAR}</if>
        <if test="payeeBankNodeName != null and payeeBankNodeName != ''">AND payee_bank_node_name=#{payeeBankNodeName,jdbcType=VARCHAR}</if>
        <if test="payeeBankNodeNo != null and payeeBankNodeNo != ''">AND payee_bank_node_no=#{payeeBankNodeNo,jdbcType=VARCHAR}</if>
        <if test="payingTime != null and payingTime != ''">AND paying_time=#{payingTime,jdbcType=VARCHAR}</if>
        <if test="refSetId != null and refSetId != ''">AND ref_set_id=#{refSetId,jdbcType=VARCHAR}</if>
        <if test="settlementType != null and settlementType != ''">AND settlement_type=#{settlementType,jdbcType=VARCHAR}</if>
        <if test="settlementUk != null and settlementUk != ''">AND settlement_uk=#{settlementUk,jdbcType=VARCHAR}</if>
        <if test="summary != null and summary != ''">AND summary=#{summary,jdbcType=VARCHAR}</if>
        <if test="ver != null">AND ver=#{ver,jdbcType=INTEGER}</if>
        <if test="payAccountTypeName != null and payAccountTypeName != ''">AND pay_account_type_name=#{payAccountTypeName,jdbcType=VARCHAR},</if>
        <if test="settlementTypeName != null and settlementTypeName != ''">AND settlement_type_name=#{settlementTypeName,jdbcType=VARCHAR},</if>
        <if test="payeeAccountTypeCode != null and payeeAccountTypeCode != ''">AND payee_account_type_code=#{payeeAccountTypeCode,jdbcType=VARCHAR},</if>
        <if test="payeeAccountTypeName != null and payeeAccountTypeName != ''">AND payee_account_type_name=#{payeeAccountTypeName,jdbcType=VARCHAR},</if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail">
        INSERT INTO pcx_change_bill_pay_detail (
        <include refid="allColumn" />
        ) VALUES (
        <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail">
        INSERT INTO pcx_change_bill_pay_detail (
        <include refid="insertSelectiveColumn" />
        ) VALUES (
        <include refid="insertSelectiveValue" />
        )
    </insert>

    <insert id="insertBatch" parameterType="java.util.List" databaseId="mysql">
        INSERT INTO pcx_change_bill_pay_detail (
        <include refid="allColumn" />
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            <include refid="batchAllColumnValue" />
            )
        </foreach>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List" databaseId="oracle">
        INSERT INTO pcx_change_bill_pay_detail (
        <include refid="allColumn" />
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            SELECT
            <include refid="batchAllColumnValue" />
            from dual
        </foreach>
    </insert>

    <delete id="delById" parameterType="string">
        DELETE FROM pcx_change_bill_pay_detail
        WHERE
            id=#{value}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_change_bill_pay_detail
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail">
        UPDATE pcx_change_bill_pay_detail SET
        <include refid="allColumnSet" />
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill_pay_detail
        WHERE id=#{value}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill_pay_detail
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByBillIds" parameterType="java.util.List" resultType="com.pty.pcx.vo.treasurypay.change.PcxChangeBillPayDetailVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill_pay_detail
        WHERE bill_id IN
        <foreach collection="list" index="index" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </select>


    <select id="selectByChangeIds" parameterType="java.util.List" resultType="com.pty.pcx.vo.treasurypay.change.PcxChangeBillPayDetailVO">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill_pay_detail
        WHERE change_bill_id IN
        <foreach collection="list" index="index" item="Id" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail" resultType="com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail">
        SELECT
        <include refid="allColumnAlias" />
        FROM pcx_change_bill_pay_detail
        WHERE 1=1
        <include refid="allColumnCond" />
    </select>

</mapper>