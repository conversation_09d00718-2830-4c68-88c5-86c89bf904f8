<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasOfficialCardDao">

    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.bas.PcxBasOfficialCard">
        <result column="account_no" property="accountNo" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="bank_node" property="bankNode" jdbcType="VARCHAR"/>
        <result column="bank_node_name" property="bankNodeName" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="VARCHAR"/>
        <result column="creator_code" property="creatorCode" jdbcType="VARCHAR"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="employee_code" property="employeeCode" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="info_source" property="infoSource" jdbcType="VARCHAR"/>
        <result column="modified_time" property="modifiedTime" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modifier_name" property="modifierName" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseVoResultMap" type="com.pty.pcx.vo.bas.PcxBasOfficialCardVO">
        <result column="account_no" property="accountNo" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="bank_node" property="bankNode" jdbcType="VARCHAR"/>
        <result column="bank_node_name" property="bankNodeName" jdbcType="VARCHAR"/>
        <result column="employee_code" property="employeeCode" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="allColumn">
        account_no, agy_code, bank_node, bank_node_name, created_time, creator_code, creator_name,
        employee_code,fiscal, id, info_source, modified_time, modifier, modifier_name, mof_div_code
    </sql>

    <sql id="voAllColumn">
        account_no, agy_code, bank_node, bank_node_name, employee_code,fiscal, id, mof_div_code
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="accountNo != null and accountNo != ''"> account_no=#{accountNo,jdbcType=VARCHAR},</if>
            <if test="agyCode != null and agyCode != ''"> agy_code=#{agyCode,jdbcType=VARCHAR},</if>
            <if test="bankNode != null and bankNode != ''"> bank_node=#{bankNode,jdbcType=VARCHAR},</if>
            <if test="bankNodeName != null and bankNodeName != ''"> bank_node_name=#{bankNodeName,jdbcType=VARCHAR},</if>
            <if test="createdTime != null and createdTime != ''"> created_time=#{createdTime,jdbcType=VARCHAR},</if>
            <if test="creatorCode != null and creatorCode != ''"> creator_code=#{creatorCode,jdbcType=VARCHAR},</if>
            <if test="creatorName != null and creatorName != ''"> creator_name=#{creatorName,jdbcType=VARCHAR},</if>
            <if test="employeeCode != null and employeeCode != ''"> employee_code=#{employeeCode,jdbcType=VARCHAR},</if>
            <if test="fiscal != null and fiscal != ''"> fiscal=#{fiscal,jdbcType=VARCHAR},</if>
            <if test="infoSource != null and infoSource != ''"> info_source=#{infoSource,jdbcType=VARCHAR},</if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time=#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="modifier != null and modifier != ''"> modifier=#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null and modifierName != ''"> modifier_name=#{modifierName,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null and mofDivCode != ''"> mof_div_code=#{mofDivCode,jdbcType=VARCHAR},</if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="accountNo != null and accountNo != ''"> AND account_no=#{accountNo,jdbcType=VARCHAR}</if>
        <if test="agyCode != null and agyCode != ''"> AND agy_code=#{agyCode,jdbcType=VARCHAR}</if>
        <if test="bankNode != null and bankNode != ''"> AND bank_node=#{bankNode,jdbcType=VARCHAR}</if>
        <if test="bankNodeName != null and bankNodeName != ''"> AND bank_node_name=#{bankNodeName,jdbcType=VARCHAR}</if>
        <if test="createdTime != null and createdTime != ''"> AND created_time=#{createdTime,jdbcType=VARCHAR}</if>
        <if test="creatorCode != null and creatorCode != ''"> AND creator_code=#{creatorCode,jdbcType=VARCHAR}</if>
        <if test="creatorName != null and creatorName != ''"> AND creator_name=#{creatorName,jdbcType=VARCHAR}</if>
        <if test="employeeCode != null and employeeCode != ''"> AND employee_code=#{employeeCode,jdbcType=VARCHAR}</if>
        <if test="fiscal != null and fiscal != ''"> AND fiscal=#{fiscal,jdbcType=VARCHAR}</if>
        <if test="id != null and id != ''"> AND id=#{id,jdbcType=VARCHAR}</if>
        <if test="infoSource != null and infoSource != ''"> AND info_source=#{infoSource,jdbcType=VARCHAR}</if>
        <if test="modifiedTime != null and modifiedTime != ''"> AND modified_time=#{modifiedTime,jdbcType=VARCHAR}</if>
        <if test="modifier != null and modifier != ''"> AND modifier=#{modifier,jdbcType=VARCHAR}</if>
        <if test="modifierName != null and modifierName != ''"> AND modifier_name=#{modifierName,jdbcType=VARCHAR}</if>
        <if test="mofDivCode != null and mofDivCode != ''"> AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}</if>
        <if test="employeeCodeList != null and employeeCodeList.size &gt; 0"> AND employee_code IN
            <foreach collection="employeeCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.bas.PcxBasOfficialCard">
        INSERT INTO pcx_bas_official_card (
        <include refid="allColumn"/>
        ) VALUES (
        #{accountNo,jdbcType=VARCHAR},          #{agyCode,jdbcType=VARCHAR},            #{bankNode,jdbcType=VARCHAR},           #{bankNodeName,jdbcType=VARCHAR},
        #{createdTime,jdbcType=VARCHAR},        #{creatorCode,jdbcType=VARCHAR},        #{creatorName,jdbcType=VARCHAR},        #{employeeCode,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},             #{id,jdbcType=VARCHAR},                 #{infoSource,jdbcType=VARCHAR},         #{modifiedTime,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},           #{modifierName,jdbcType=VARCHAR},       #{mofDivCode,jdbcType=VARCHAR}
        )
    </insert>

    <delete id="delById" parameterType="string">
        DELETE
        FROM pcx_bas_official_card
        WHERE id = #{value}
    </delete>

    <delete id="delByEmployeeCodeList" >
        DELETE
        FROM pcx_bas_official_card
        WHERE
        agy_code = #{agyCode}
        and fiscal = #{fiscal}
        and mof_div_code = #{mofDivCode}
        and employee_code in
        <foreach collection="employeeCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.bas.PcxBasOfficialCard">
        UPDATE pcx_bas_official_card SET
        <include refid="allColumnSet"/>
        WHERE id=#{id}
    </update>

    <select id="select" parameterType="com.pty.pcx.qo.bas.PcxBasOfficialCardQO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_bas_official_card
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="selectList" parameterType="com.pty.pcx.qo.bas.PcxBasOfficialCardQO"
            resultMap="BaseVoResultMap">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_bas_official_card
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_bas_official_card
        WHERE id = #{value}
    </select>

    <select id="searchOfficialInfos" resultType="com.pty.mad.vo.MadSearchAllAccInfoVO">
        SELECT
        a.employee_code as madCode,
        b.mad_name as madName,
        a.bank_node_name as bankName,
        a.account_no as bankAccountNo
        FROM pcx_bas_official_card a
        LEFT JOIN mad_employee b ON a.employee_code = b.mad_code
        WHERE 1=1
        <if test="agyCode != null and agyCode != ''"> AND a.agy_code = #{agyCode}</if>
        <if test="fiscal != null"> AND a.fiscal = #{fiscal}</if>
        <if test="mofDivCode != null and mofDivCode != ''"> AND a.mof_div_code = #{mofDivCode}</if>
        <if test="searchKey != null and searchKey != ''"> AND (b.mad_name like concat('%',#{searchKey},'%') or a.account_no like concat('%',#{searchKey},'%'))</if>
    </select>

</mapper>
