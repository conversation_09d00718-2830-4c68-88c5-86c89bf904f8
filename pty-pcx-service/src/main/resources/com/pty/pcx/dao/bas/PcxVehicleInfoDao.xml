<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxVehicleInfoDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.bas.PcxVehicleInfo" id="PcxVehicleInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="licensePlate" column="license_plate" jdbcType="VARCHAR"/>
        <result property="licensePlateColor" column="license_plate_color" jdbcType="VARCHAR"/>
        <result property="vehicleType" column="vehicle_type" jdbcType="VARCHAR"/>
        <result property="brand" column="brand" jdbcType="VARCHAR"/>
        <result property="model" column="model" jdbcType="VARCHAR"/>
        <result property="vehicleColor" column="vehicle_color" jdbcType="VARCHAR"/>
        <result property="vin" column="vin" jdbcType="VARCHAR"/>
        <result property="engineNumber" column="engine_number" jdbcType="VARCHAR"/>
        <result property="seatingCapacity" column="seating_capacity" jdbcType="INTEGER"/>
        <result property="loadCapacity" column="load_capacity" jdbcType="NUMERIC"/>
        <result property="fuelType" column="fuel_type" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 全部字段 -->
	<sql id="allColumn">
        id, 
        agy_code, 
        mof_div_code, 
        fiscal, 
        license_plate, 
        license_plate_color, 
        vehicle_type, 
        brand, 
        model, 
        vehicle_color, 
        vin, 
        engine_number, 
        seating_capacity, 
        load_capacity, 
        fuel_type, 
        modified_time, 
        created_time
	</sql>
    
    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxVehicleInfoMap">
        select
          <include refid="allColumn" />
        from pcx_vehicle_info
        where id = #{id}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxVehicleInfoMap">
        select
        <include refid="allColumn" />
        from pcx_vehicle_info
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="licensePlate != null and licensePlate != ''">
                and license_plate = #{licensePlate}
            </if>
            <if test="licensePlateColor != null and licensePlateColor != ''">
                and license_plate_color = #{licensePlateColor}
            </if>
            <if test="vehicleType != null and vehicleType != ''">
                and vehicle_type = #{vehicleType}
            </if>
            <if test="brand != null and brand != ''">
                and brand = #{brand}
            </if>
            <if test="model != null and model != ''">
                and model = #{model}
            </if>
            <if test="vehicleColor != null and vehicleColor != ''">
                and vehicle_color = #{vehicleColor}
            </if>
            <if test="vin != null and vin != ''">
                and vin = #{vin}
            </if>
            <if test="engineNumber != null and engineNumber != ''">
                and engine_number = #{engineNumber}
            </if>
            <if test="seatingCapacity != null">
                and seating_capacity = #{seatingCapacity}
            </if>
            <if test="loadCapacity != null">
                and load_capacity = #{loadCapacity}
            </if>
            <if test="fuelType != null and fuelType != ''">
                and fuel_type = #{fuelType}
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                and modified_time = #{modifiedTime}
            </if>
            <if test="createdTime != null and createdTime != ''">
                and created_time = #{createdTime}
            </if>
        </where>
    </select>
    
    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_vehicle_info (
        <trim suffixOverrides=",">
            id,
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="licensePlate != null and licensePlate != ''">
                license_plate,
            </if>
            <if test="licensePlateColor != null and licensePlateColor != ''">
                license_plate_color,
            </if>
            <if test="vehicleType != null and vehicleType != ''">
                vehicle_type,
            </if>
            <if test="brand != null and brand != ''">
                brand,
            </if>
            <if test="model != null and model != ''">
                model,
            </if>
            <if test="vehicleColor != null and vehicleColor != ''">
                vehicle_color,
            </if>
            <if test="vin != null and vin != ''">
                vin,
            </if>
            <if test="engineNumber != null and engineNumber != ''">
                engine_number,
            </if>
            <if test="seatingCapacity != null">
                seating_capacity,
            </if>
            <if test="loadCapacity != null">
                load_capacity,
            </if>
            <if test="fuelType != null and fuelType != ''">
                fuel_type,
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time,
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time,
            </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            #{id},
            <if test="agyCode != null and agyCode != ''">
                #{agyCode},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal},
            </if>
            <if test="licensePlate != null and licensePlate != ''">
                #{licensePlate},
            </if>
            <if test="licensePlateColor != null and licensePlateColor != ''">
                #{licensePlateColor},
            </if>
            <if test="vehicleType != null and vehicleType != ''">
                #{vehicleType},
            </if>
            <if test="brand != null and brand != ''">
                #{brand},
            </if>
            <if test="model != null and model != ''">
                #{model},
            </if>
            <if test="vehicleColor != null and vehicleColor != ''">
                #{vehicleColor},
            </if>
            <if test="vin != null and vin != ''">
                #{vin},
            </if>
            <if test="engineNumber != null and engineNumber != ''">
                #{engineNumber},
            </if>
            <if test="seatingCapacity != null">
                #{seatingCapacity},
            </if>
            <if test="loadCapacity != null">
                #{loadCapacity},
            </if>
            <if test="fuelType != null and fuelType != ''">
                #{fuelType},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                #{modifiedTime},
            </if>
            <if test="createdTime != null and createdTime != ''">
                #{createdTime},
            </if>
        </trim>
        )
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_vehicle_info
        <set>
            <trim suffixOverrides=",">
                <if test="agyCode != null and agyCode != ''">
                    agy_code = #{agyCode},
                </if>
                <if test="mofDivCode != null and mofDivCode != ''">
                    mof_div_code = #{mofDivCode},
                </if>
                <if test="fiscal != null and fiscal != ''">
                    fiscal = #{fiscal},
                </if>
                <if test="licensePlate != null and licensePlate != ''">
                    license_plate = #{licensePlate},
                </if>
                <if test="licensePlateColor != null and licensePlateColor != ''">
                    license_plate_color = #{licensePlateColor},
                </if>
                <if test="vehicleType != null and vehicleType != ''">
                    vehicle_type = #{vehicleType},
                </if>
                <if test="brand != null and brand != ''">
                    brand = #{brand},
                </if>
                <if test="model != null and model != ''">
                    model = #{model},
                </if>
                <if test="vehicleColor != null and vehicleColor != ''">
                    vehicle_color = #{vehicleColor},
                </if>
                <if test="vin != null and vin != ''">
                    vin = #{vin},
                </if>
                <if test="engineNumber != null and engineNumber != ''">
                    engine_number = #{engineNumber},
                </if>
                <if test="seatingCapacity != null">
                    seating_capacity = #{seatingCapacity},
                </if>
                <if test="loadCapacity != null">
                    load_capacity = #{loadCapacity},
                </if>
                <if test="fuelType != null and fuelType != ''">
                    fuel_type = #{fuelType},
                </if>
                <if test="modifiedTime != null and modifiedTime != ''">
                    modified_time = #{modifiedTime},
                </if>
                <if test="createdTime != null and createdTime != ''">
                    created_time = #{createdTime},
                </if>
            </trim>
        </set>
        where id = #{id}
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete from pcx_vehicle_info where id = #{id}
    </delete>
    
</mapper>
