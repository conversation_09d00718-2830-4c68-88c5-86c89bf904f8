<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.stand.PcxStandConditionDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.stand.PcxStandCondition" id="PcxStandConditionMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="standCode" column="stand_code" jdbcType="VARCHAR"/>
        <result property="condKeyCode" column="cond_key_code" jdbcType="VARCHAR"/>
        <result property="condKeyName" column="cond_key_name" jdbcType="VARCHAR"/>
        <result property="condValueCode" column="cond_value_code" jdbcType="VARCHAR"/>
        <result property="condValueName" column="cond_value_name" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 全部字段 -->
	<sql id="allColumn">
        id, 
        stand_code, 
        cond_key_code, 
        cond_key_name, 
        cond_value_code, 
        cond_value_name, 
        operator, 
        agy_code, 
        fiscal, 
        mof_div_code
	</sql>
    
    <select id="queryStandConditionList" resultMap="PcxStandConditionMap">
        select
        <include refid="allColumn" />
        from pcx_stand_condition
        where
        agy_code = #{agyCode}
        and fiscal = #{fiscal}
        and mof_div_code = #{mofDivCode}
        and stand_code in
        <foreach collection="standCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsert" databaseId="mysql">
        INSERT INTO pcx_stand_condition (
        id,
        stand_code,
        cond_key_code,
        cond_key_name,
        cond_value_code,
        cond_value_name,
        operator,
        agy_code,
        fiscal,
        mof_div_code
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
             #{item.id},
            #{item.standCode},
            #{item.condKeyCode},
            #{item.condKeyName},
            #{item.condValueCode},
            #{item.condValueName},
            #{item.operator},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode}
            )
        </foreach>
    </insert>


    <insert id="batchInsert" databaseId="oracle">
        INSERT INTO pcx_stand_condition (
        id,
        stand_code,
        cond_key_code,
        cond_key_name,
        cond_value_code,
        cond_value_name,
        operator,
        agy_code,
        fiscal,
        mof_div_code
        ) VALUES
        <foreach collection="list" item="item" separator="union all">
            select
            #{item.id},
            #{item.standCode},
            #{item.condKeyCode},
            #{item.condKeyName},
            #{item.condValueCode},
            #{item.condValueName},
            #{item.operator},
            #{item.agyCode},
            #{item.fiscal},
            #{item.mofDivCode}
            from DUAL
        </foreach>
    </insert>

    <delete id="deleteStandConditionByStandCode">
        DELETE FROM pcx_stand_condition
        WHERE stand_code = #{stand_code}
          AND agy_code = #{agy_code}
          AND fiscal = #{fiscal}
          AND mof_div_code = #{mof_div_code}
    </delete>

</mapper>


