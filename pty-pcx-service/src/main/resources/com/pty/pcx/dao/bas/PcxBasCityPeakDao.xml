<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasCityPeakDao">
    <!-- 结果集 -->
    <resultMap id="PcxBasCityPeakMap" type="com.pty.pcx.entity.bas.PcxBasCityPeak">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="dataCode" column="data_code" jdbcType="VARCHAR"/>
        <result property="dataName" column="data_name" jdbcType="VARCHAR"/>
        <result property="dataCodeLevel" column="dataCodeLevel" jdbcType="INTEGER"/>
        <result property="startDay" column="start_day" jdbcType="VARCHAR"/>
        <result property="endDay" column="end_day" jdbcType="VARCHAR"/>
        <result property="peakType" column="peak_type" jdbcType="INTEGER"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 全部字段 -->
	<sql id="allColumn">
        id,
        `type`,
        data_code,
        data_name,
        data_code_level,
        start_day,
        end_day,
        peak_type,
        agy_code,
        fiscal,
        mof_div_code,
        creator,
        creator_name,
        created_time,
        modifier,
        modifier_name,
        modified_time
	</sql>

    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxBasCityPeakMap">
        select
          <include refid="allColumn" />
        from pcx_bas_city_peak
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxBasCityPeakMap">
        select
        <include refid="allColumn" />
        from pcx_bas_city_peak
        <where>
            <if test="id != null and id != ''">
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and `type` = #{type,jdbcType=INTEGER}
            </if>
            <if test="dataCode != null and dataCode != ''">
                and data_code = #{dataCode,jdbcType=VARCHAR}
            </if>
            <if test="dataName != null and dataName != ''">
                and data_name = #{dataName,jdbcType=VARCHAR}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode,jdbcType=VARCHAR}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal,jdbcType=VARCHAR}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            </if>
            <if test="peakType != null">
                and peak_type = #{peakType,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <insert id="batchInsert" databaseId="mysql">
        insert into pcx_bas_city_peak (
        id,
        `type`,
        data_code,
        data_name,
        data_code_level,
        start_day,
        end_day,
        peak_type,
        agy_code,
        fiscal,
        mof_div_code,
        creator,
        creator_name,
        created_time,
        modifier,
        modifier_name,
        modified_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER},
            #{item.dataCode,jdbcType=VARCHAR},
            #{item.dataName,jdbcType=VARCHAR},
            #{item.dataCodeLevel,jdbcType=INTEGER},
            #{item.startDay,jdbcType=VARCHAR},
            #{item.endDay,jdbcType=VARCHAR},
            #{item.peakType,jdbcType=INTEGER},
            #{item.agyCode,jdbcType=VARCHAR},
            #{item.fiscal,jdbcType=VARCHAR},
            #{item.mofDivCode,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR},
            #{item.creatorName,jdbcType=VARCHAR},
            #{item.createdTime,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR},
            #{item.modifierName,jdbcType=VARCHAR},
            #{item.modifiedTime,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_city_peak (
        id,
        "type",
        data_code,
        data_name,
        data_code_level,
        start_day,
        end_day,
        peak_type,
        agy_code,
        fiscal,
        mof_div_code,
        creator,
        creator_name,
        created_time,
        modifier,
        modifier_name,
        modified_time
        )
        values
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.id,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER},
            #{item.dataCode,jdbcType=VARCHAR},
            #{item.dataName,jdbcType=VARCHAR},
            #{item.dataCodeLevel,jdbcType=INTEGER},
            #{item.startDay,jdbcType=VARCHAR},
            #{item.endDay,jdbcType=VARCHAR},
            #{item.peakType,jdbcType=INTEGER},
            #{item.agyCode,jdbcType=VARCHAR},
            #{item.fiscal,jdbcType=VARCHAR},
            #{item.mofDivCode,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR},
            #{item.creatorName,jdbcType=VARCHAR},
            #{item.createdTime,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR},
            #{item.modifierName,jdbcType=VARCHAR},
            #{item.modifiedTime,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_city_peak (
        id,
        "type",
        data_code,
        data_name,
        data_code_level,
        start_day,
        end_day,
        peak_type,
        agy_code,
        fiscal,
        mof_div_code,
        creator,
        creator_name,
        created_time,
        modifier,
        modifier_name,
        modified_time
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.id,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER},
            #{item.dataCode,jdbcType=VARCHAR},
            #{item.dataName,jdbcType=VARCHAR},
            #{item.dataCodeLevel,jdbcType=INTEGER},
            #{item.startDay,jdbcType=VARCHAR},
            #{item.endDay,jdbcType=VARCHAR},
            #{item.peakType,jdbcType=INTEGER},
            #{item.agyCode,jdbcType=VARCHAR},
            #{item.fiscal,jdbcType=VARCHAR},
            #{item.mofDivCode,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR},
            #{item.creatorName,jdbcType=VARCHAR},
            #{item.createdTime,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR},
            #{item.modifierName,jdbcType=VARCHAR},
            #{item.modifiedTime,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_city_peak (
        id,
        "type",
        data_code,
        data_name,
        data_code_level,
        start_day,
        end_day,
        peak_type,
        agy_code,
        fiscal,
        mof_div_code,
        creator,
        creator_name,
        created_time,
        modifier,
        modifier_name,
        modified_time
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.id,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER},
            #{item.dataCode,jdbcType=VARCHAR},
            #{item.dataName,jdbcType=VARCHAR},
            #{item.dataCodeLevel,jdbcType=INTEGER},
            #{item.startDay,jdbcType=VARCHAR},
            #{item.endDay,jdbcType=VARCHAR},
            #{item.peakType,jdbcType=INTEGER},
            #{item.agyCode,jdbcType=VARCHAR},
            #{item.fiscal,jdbcType=VARCHAR},
            #{item.mofDivCode,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR},
            #{item.creatorName,jdbcType=VARCHAR},
            #{item.createdTime,jdbcType=VARCHAR},
            #{item.modifier,jdbcType=VARCHAR},
            #{item.modifierName,jdbcType=VARCHAR},
            #{item.modifiedTime,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_bas_city_peak
        <set>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="cityCode != null and cityCode != ''">
                data_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                data_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="dataCodeLevel != null">
                data_code_level = #{dataCodeLevel,jdbcType=INTEGER},
            </if>
            <if test="startDay != null and startDay != ''">
                start_day = #{startDay,jdbcType=VARCHAR},
            </if>
            <if test="endDay != null and endDay != ''">
                end_day = #{endDay,jdbcType=VARCHAR},
            </if>
            <if test="peakType != null">
                peak_type = #{peakType,jdbcType=INTEGER},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time = #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name = #{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time = #{modifiedTime,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code = #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal = #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code = #{mofDivCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete from pcx_bas_city_peak
        <where>
            <if test="id != null and id != ''">
                and id = #{id,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>


</mapper>


