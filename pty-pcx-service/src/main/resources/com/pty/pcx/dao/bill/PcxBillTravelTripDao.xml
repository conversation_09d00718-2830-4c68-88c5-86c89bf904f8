<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillTravelTripDao">

    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.bill.PcxBillTravelTrip">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="bill_id" jdbcType="VARCHAR" property="billId" />
        <result column="start_time" jdbcType="VARCHAR" property="startTime" />
        <result column="end_time" jdbcType="VARCHAR" property="endTime" />
        <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
        <result column="emp_name" jdbcType="VARCHAR" property="empName" />
        <result column="agy_code" jdbcType="VARCHAR" property="agyCode" />
        <result column="fiscal" jdbcType="VARCHAR" property="fiscal" />
        <result column="mof_div_code" jdbcType="VARCHAR" property="mofDivCode" />
    </resultMap>

    <sql id="BaseColumn">
        id, bill_id, start_time, end_time, emp_code, emp_name, agy_code, fiscal, mof_div_code
    </sql>
    <select id="selectByBillId" resultMap="BaseResultMap">
        select <include refid="BaseColumn"/>
            from pcx_bill_travel_trip
        where bill_id =#{billId}
    </select>
</mapper>
