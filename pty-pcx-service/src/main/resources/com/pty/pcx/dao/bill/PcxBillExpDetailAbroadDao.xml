<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillExpDetailAbroadDao">
    <!-- 所有字段 -->
    <sql id="allColumn">
        id
        , bill_id, expense_id, trip_expense_id, expense_code, expense_name, exp_detail_code, exp_detail_name,
        input_amt, check_amt, agy_code, fiscal, mof_div_code, tenant_id, city, city_name, country, country_name,
        normal_amt, days, person_num, remark, creator, create_time, creator_name, modifier, modifier_name,
        modified_time, expense_type, is_deleted_number, currency
    </sql>

    <!-- 所有字段别名 -->
    <sql id="allColumnAlias">
        id
        as id, bill_id as billId, expense_id as expenseId, trip_expense_id as tripExpenseId,
        expense_code as expenseCode, expense_name as expenseName, exp_detail_code as expDetailCode,
        exp_detail_name as expDetailName, input_amt as inputAmt, check_amt as checkAmt, agy_code as agyCode,
        fiscal as fiscal, mof_div_code as mofDivCode, tenant_id as tenantId, city as city, city_name as cityName,
        country as country, country_name as countryName, normal_amt as normalAmt, days as days,
        person_num as personNum, remark as remark, creator as creator, create_time as createTime,
        creator_name as creatorName, modifier as modifier, modifier_name as modifierName,
        modified_time as modifiedTime, expense_type as expenseType, is_deleted_number as isDeletedNumber,currency as currency
    </sql>

    <!-- 所有字段值 -->
    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR}
        ,
        #{billId,jdbcType=VARCHAR},
        #{expenseId,jdbcType=VARCHAR},
        #{tripExpenseId,jdbcType=VARCHAR},
        #{expenseCode,jdbcType=VARCHAR},
        #{expenseName,jdbcType=VARCHAR},
        #{expDetailCode,jdbcType=VARCHAR},
        #{expDetailName,jdbcType=VARCHAR},
        #{inputAmt,jdbcType=DECIMAL},
        #{checkAmt,jdbcType=DECIMAL},
        #{agyCode,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},
        #{mofDivCode,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR},
        #{cityName,jdbcType=VARCHAR},
        #{country,jdbcType=VARCHAR},
        #{countryName,jdbcType=VARCHAR},
        #{normalAmt,jdbcType=DECIMAL},
        #{days,jdbcType=INTEGER},
        #{personNum,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=VARCHAR},
        #{creatorName,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},
        #{modifierName,jdbcType=VARCHAR},
        #{modifiedTime,jdbcType=VARCHAR},
        #{expenseType,jdbcType=INTEGER},
        #{isDeletedNumber,jdbcType=INTEGER},
        #{currency,jdbcType=VARCHAR}
    </sql>

    <!-- 插入语句 -->
    <insert id="insert" parameterType="com.pty.pcx.entity.bill.PcxBillExpDetailAbroad">
        INSERT INTO pcx_bill_exp_detail_abroad (
        <include refid="allColumn"/>
        ) VALUES (
        <include refid="allColumnValue"/>
        )
    </insert>

    <!-- 条件更新语句 -->
    <update id="updateById" parameterType="com.pty.pcx.entity.bill.PcxBillExpDetailAbroad">
        UPDATE pcx_bill_exp_detail_abroad
        <set>
            <if test="billId != null">bill_id=#{billId,jdbcType=VARCHAR},</if>
            <if test="expenseId != null">expense_id=#{expenseId,jdbcType=VARCHAR},</if>
            <if test="tripExpenseId != null">trip_expense_id=#{tripExpenseId,jdbcType=VARCHAR},</if>
            <if test="expenseCode != null">expense_code=#{expenseCode,jdbcType=VARCHAR},</if>
            <if test="expenseName != null">expense_name=#{expenseName,jdbcType=VARCHAR},</if>
            <if test="expDetailCode != null">exp_detail_code=#{expDetailCode,jdbcType=VARCHAR},</if>
            <if test="expDetailName != null">exp_detail_name=#{expDetailName,jdbcType=VARCHAR},</if>
            <if test="inputAmt != null">input_amt=#{inputAmt,jdbcType=DECIMAL},</if>
            <if test="checkAmt != null">check_amt=#{checkAmt,jdbcType=DECIMAL},</if>
            <if test="agyCode != null">agy_code=#{agyCode,jdbcType=VARCHAR},</if>
            <if test="fiscal != null">fiscal=#{fiscal,jdbcType=VARCHAR},</if>
            <if test="mofDivCode != null">mof_div_code=#{mofDivCode,jdbcType=VARCHAR},</if>
            <if test="tenantId != null">tenant_id=#{tenantId,jdbcType=VARCHAR},</if>
            <if test="city != null">city=#{city,jdbcType=VARCHAR},</if>
            <if test="cityName != null">city_name=#{cityName,jdbcType=VARCHAR},</if>
            <if test="country != null">country=#{country,jdbcType=VARCHAR},</if>
            <if test="countryName != null">country_name=#{countryName,jdbcType=VARCHAR},</if>
            <if test="normalAmt != null">normal_amt=#{normalAmt,jdbcType=DECIMAL},</if>
            <if test="days != null">days=#{days,jdbcType=INTEGER},</if>
            <if test="personNum != null">person_num=#{personNum,jdbcType=INTEGER},</if>
            <if test="remark != null">remark=#{remark,jdbcType=VARCHAR},</if>
            <if test="creator != null">creator=#{creator,jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time=#{createTime,jdbcType=VARCHAR},</if>
            <if test="creatorName != null">creator_name=#{creatorName,jdbcType=VARCHAR},</if>
            <if test="modifier != null">modifier=#{modifier,jdbcType=VARCHAR},</if>
            <if test="modifierName != null">modifier_name=#{modifierName,jdbcType=VARCHAR},</if>
            <if test="modifiedTime != null">modified_time=#{modifiedTime,jdbcType=VARCHAR},</if>
            <if test="expenseType != null">expense_type=#{expenseType,jdbcType=INTEGER},</if>
            <if test="isDeletedNumber != null">is_deleted_number=#{isDeletedNumber,jdbcType=INTEGER},</if>
            <if test="currency != null">currency=#{currency,jdbcType=VARCHAR},</if>
        </set>
        WHERE id=#{id,jdbcType=VARCHAR}
    </update>

    <!-- 查询语句 -->
    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.bill.PcxBillExpDetailAbroad">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_bill_exp_detail_abroad
        WHERE id=#{value}
    </select>

    <!-- 删除语句 -->
    <delete id="delById" parameterType="string">
        DELETE
        FROM pcx_bill_exp_detail_abroad
        WHERE id = #{value}
    </delete>
</mapper>