<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.positionblock.PcxPositionBlockDao">
    <sql id="allColumn">
        id,                                               bill_func_code,                                   position_code,                                    classify_code,
        classify_name,                                    fiscal,                                           agy_code,
        mof_div_code,                                     modifier,                                         modifier_name,                                    modified_time,
        creator,                                          creator_name,                                     created_time,                                     ver,
        seq,                                              position_context,                                 area,                                             show_name

    </sql>

    <sql id="allColumnAlias">
        id as id,                                         bill_func_code as billFuncCode,                   position_code as positionCode,                    classify_code as classifyCode,
        classify_name as classifyName,                    fiscal as fiscal,                                 agy_code as agyCode,
        mof_div_code as mofDivCode,                       modifier as modifier,                             modifier_name as modifier<PERSON><PERSON>,                    modified_time as modifiedTime,
        creator as creator,                               creator_name as creator<PERSON><PERSON>,                      created_time as createdTime,                      ver as ver,
        seq as seq,                                       position_context as positionContext,              area as area,                                     show_name as showName

    </sql>

    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR},                           #{billFuncCode,jdbcType=VARCHAR},                 #{positionCode,jdbcType=VARCHAR},                 #{classifyCode,jdbcType=VARCHAR},
        #{classifyName,jdbcType=VARCHAR},                 #{fiscal,jdbcType=VARCHAR},                       #{agyCode,jdbcType=VARCHAR},
        #{mofDivCode,jdbcType=VARCHAR},                   #{modifier,jdbcType=VARCHAR},                     #{modifierName,jdbcType=VARCHAR},                 #{modifiedTime,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},                      #{creatorName,jdbcType=VARCHAR},                  #{createdTime,jdbcType=VARCHAR},                  #{ver,jdbcType=VARCHAR},
        #{seq,jdbcType=INTEGER},                          #{positionContext,jdbcType=VARCHAR},              #{area,jdbcType=VARCHAR},                         #{showName,jdbcType=VARCHAR}

    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id=#{id,jdbcType=VARCHAR},
             </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                bill_func_code=#{billFuncCode,jdbcType=VARCHAR},
             </if>
            <if test="positionCode != null and positionCode != ''">
                position_code=#{positionCode,jdbcType=VARCHAR},
             </if>
            <if test="classifyCode != null and classifyCode != ''">
                classify_code=#{classifyCode,jdbcType=VARCHAR},
             </if>
            <if test="classifyName != null and classifyName != ''">
                classify_name=#{classifyName,jdbcType=VARCHAR},
             </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
             </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
             </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
             </if>
            <if test="modifier != null and modifier != ''">
                modifier=#{modifier,jdbcType=VARCHAR},
             </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name=#{modifierName,jdbcType=VARCHAR},
             </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time=#{modifiedTime,jdbcType=VARCHAR},
             </if>
            <if test="creator != null and creator != ''">
                creator=#{creator,jdbcType=VARCHAR},
             </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name=#{creatorName,jdbcType=VARCHAR},
             </if>
            <if test="createdTime != null and createdTime != ''">
                created_time=#{createdTime,jdbcType=VARCHAR},
             </if>
            <if test="ver != null and ver != ''">
                ver=#{ver,jdbcType=VARCHAR},
             </if>
            <if test="seq != null">
                seq=#{seq,jdbcType=INTEGER}
            </if>
            <if test="positionContext != null and positionContext != ''">
                position_context=#{positionContext,jdbcType=VARCHAR}
            </if>
            <if test="area != null and area != ''">
                area=#{area,jdbcType=VARCHAR}
            </if>
            <if test="showName != null and showName != ''">
                show_name=#{showName,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <sql id="columnSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                bill_func_code,
            </if>
            <if test="positionCode != null and positionCode != ''">
                position_code,
            </if>
            <if test="classifyCode != null and classifyCode != ''">
                classify_code,
            </if>
            <if test="classifyName != null and classifyName != ''">
                classify_name,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name,
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name,
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time,
            </if>
            <if test="ver != null and ver != ''">
                ver,
            </if>
            <if test="seq != null">
                seq,
            </if>
            <if test="positionContext != null and positionContext != ''">
                position_context,
            </if>
            <if test="area != null and area != ''">
                area,
            </if>
            <if test="showName != null and showName != ''">
                show_name,
            </if>
        </trim>
    </sql>

    <sql id="columnValueSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="billFuncCode != null and billFuncCode != ''">
                #{billFuncCode,jdbcType=VARCHAR},
            </if>
            <if test="positionCode != null and positionCode != ''">
                #{positionCode,jdbcType=VARCHAR},
            </if>
            <if test="classifyCode != null and classifyCode != ''">
                #{classifyCode,jdbcType=VARCHAR},
            </if>
            <if test="classifyName != null and classifyName != ''">
                #{classifyName,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                #{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                #{modifiedTime,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="ver != null and ver != ''">
                #{ver,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                #{seq,jdbcType=INTEGER}
            </if>
            <if test="positionContext != null and positionContext != ''">
                #{positionContext,jdbcType=VARCHAR}
            </if>
            <if test="area != null and area != ''">
                #{area,jdbcType=VARCHAR}
            </if>
            <if test="showName != null and showName != ''">
                #{showName,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="billFuncCode != null and billFuncCode != ''">
            AND bill_func_code=#{billFuncCode,jdbcType=VARCHAR}
        </if>
        <if test="positionCode != null and positionCode != ''">
            AND position_code=#{positionCode,jdbcType=VARCHAR}
        </if>
        <if test="classifyCode != null and classifyCode != ''">
            AND classify_code=#{classifyCode,jdbcType=VARCHAR}
        </if>
        <if test="classifyName != null and classifyName != ''">
            AND classify_name=#{classifyName,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
            AND modifier=#{modifier,jdbcType=VARCHAR}
        </if>
        <if test="modifierName != null and modifierName != ''">
            AND modifier_name=#{modifierName,jdbcType=VARCHAR}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''">
            AND modified_time=#{modifiedTime,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            AND creator=#{creator,jdbcType=VARCHAR}
        </if>
        <if test="creatorName != null and creatorName != ''">
            AND creator_name=#{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time=#{createdTime,jdbcType=VARCHAR}
        </if>
        <if test="ver != null and ver != ''">
            AND ver=#{ver,jdbcType=VARCHAR}
        </if>
        <if test="seq != null">
            AND seq=#{seq,jdbcType=INTEGER}
        </if>
        <if test="positionContext != null and positionContext != ''">
            AND position_context=#{positionContext,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            AND area=#{area,jdbcType=VARCHAR}
        </if>
        <if test="showName != null and showName != ''">
            AND show_name=#{showName,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO">
        INSERT INTO pcx_position_block (
            <include refid="allColumn" />
        ) VALUES (
            <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO">
        INSERT INTO pcx_position_block (
            <include refid="columnSelective" />
        ) VALUES (
            <include refid="columnValueSelective" />
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        DELETE FROM pcx_position_block
        WHERE
            id=#{id}
    </delete>

    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM pcx_position_block
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO">
        DELETE FROM pcx_position_block
        WHERE 1=1
            <include refid="allColumnCond" />
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO">
        UPDATE pcx_position_block
            <set>
                <include refid="allColumnSet" />
            </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.vo.positionblock.PcxPositionBlockVO">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_position_block
        WHERE id=#{id}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.vo.positionblock.PcxPositionBlockVO">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_position_block
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO" resultType="com.pty.pcx.vo.positionblock.PcxPositionBlockVO">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_position_block
        WHERE 1=1
            <include refid="allColumnCond" />
        order by seq
    </select>

    <select id="count" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_position_block
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>


    <select id="getVersion" parameterType="com.pty.pcx.qo.positionblock.PcxPositionBlockQO" resultType="java.lang.String">
        SELECT
        ver
        FROM pcx_position_block
        WHERE 1=1
        <include refid="allColumnCond" />
        order by seq
    </select>
</mapper>
