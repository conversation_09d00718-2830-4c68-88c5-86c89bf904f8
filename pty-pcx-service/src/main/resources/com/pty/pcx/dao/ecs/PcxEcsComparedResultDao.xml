<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.ecs.PcxEcsComparedResultDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.pty.pcx.entity.ecscompared.PcxEcsComparedResult">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="agy_code" property="agyCode" jdbcType="VARCHAR"/>
        <result column="mof_div_code" property="mofDivCode" jdbcType="VARCHAR"/>
        <result column="fiscal" property="fiscal" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modifier_name" property="modifierName" jdbcType="VARCHAR"/>
        <result column="modified_time" property="modifiedTime" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="VARCHAR"/>
        <result column="source_attach_id" property="sourceAttachId" jdbcType="VARCHAR"/>
        <result column="bill_context" property="billContext" jdbcType="LONGVARCHAR"/>
        <result column="attach_id" property="attachId" jdbcType="VARCHAR"/>
        <result column="compared_status" property="comparedStatus" jdbcType="VARCHAR"/>
        <result column="ecs_bill_no" property="ecsBillNo" jdbcType="VARCHAR"/>
        <result column="ecs_id" property="ecsId" jdbcType="VARCHAR"/>
        <result column="pcx_bill_id" property="pcxBillId" jdbcType="VARCHAR"/>
        <result column="pcx_bill_no" property="pcxBillNo" jdbcType="VARCHAR"/>
        <result column="ecs_bill_type" property="ecsBillType" jdbcType="VARCHAR"/>
        <result column="ecs_bill_kind" property="ecsBillKind" jdbcType="VARCHAR"/>
        <result column="ecs_bill_amt" property="ecsBillAmt" jdbcType="VARCHAR"/>
        <result column="ecs_summary" property="ecsSummary" jdbcType="VARCHAR"/>
        <result column="compared_summary" property="comparedSummary" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 所有字段 -->
    <sql id="allColumn">
        id, batch_id, tenant_id, agy_code, mof_div_code, fiscal, modifier, modifier_name, modified_time,
        creator, creator_name, created_time, source_attach_id, bill_context, attach_id,
        compared_status, ecs_bill_no, ecs_id, pcx_bill_id, pcx_bill_no, ecs_bill_type,
        ecs_bill_kind, ecs_bill_amt, ecs_summary, compared_summary
    </sql>

    <!-- 更新字段动态设置 -->
    <sql id="allColumnSet">
        <trim prefix="" suffixOverrides=",">
            <if test="batchId != null and batchId != ''"> batch_id = #{batchId,jdbcType=VARCHAR}, </if>
            <if test="tenantId != null and tenantId != ''"> tenant_id = #{tenantId,jdbcType=VARCHAR}, </if>
            <if test="agyCode != null and agyCode != ''"> agy_code = #{agyCode,jdbcType=VARCHAR}, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> mof_div_code = #{mofDivCode,jdbcType=VARCHAR}, </if>
            <if test="fiscal != null and fiscal != ''"> fiscal = #{fiscal,jdbcType=VARCHAR}, </if>
            <if test="modifier != null and modifier != ''"> modifier = #{modifier,jdbcType=VARCHAR}, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name = #{modifierName,jdbcType=VARCHAR}, </if>
            <if test="modifiedTime != null"> modified_time = #{modifiedTime,jdbcType=VARCHAR}, </if>
            <if test="creator != null and creator != ''"> creator = #{creator,jdbcType=VARCHAR}, </if>
            <if test="creatorName != null and creatorName != ''"> creator_name = #{creatorName,jdbcType=VARCHAR}, </if>
            <if test="createdTime != null"> created_time = #{createdTime,jdbcType=VARCHAR}, </if>
            <if test="sourceAttachId != null and sourceAttachId != ''"> source_attach_id = #{sourceAttachId,jdbcType=VARCHAR}, </if>
            <if test="billContext != null"> bill_context = #{billContext,jdbcType=VARCHAR}, </if>
            <if test="attachId != null and attachId != ''"> attach_id = #{attachId,jdbcType=VARCHAR}, </if>
            <if test="comparedStatus != null and comparedStatus != ''"> compared_status = #{comparedStatus,jdbcType=VARCHAR}, </if>
            <if test="ecsBillNo != null and ecsBillNo != ''"> ecs_bill_no = #{ecsBillNo,jdbcType=VARCHAR}, </if>
            <if test="ecsId != null and ecsId != ''"> ecs_id = #{ecsId,jdbcType=VARCHAR}, </if>
            <if test="pcxBillId != null and pcxBillId != ''"> pcx_bill_id = #{pcxBillId,jdbcType=VARCHAR}, </if>
            <if test="pcxBillNo != null and pcxBillNo != ''"> pcx_bill_no = #{pcxBillNo,jdbcType=VARCHAR}, </if>
            <if test="ecsBillType != null and ecsBillType != ''"> ecs_bill_type = #{ecsBillType,jdbcType=VARCHAR}, </if>
            <if test="ecsBillKind != null and ecsBillKind != ''"> ecs_bill_kind = #{ecsBillKind,jdbcType=VARCHAR}, </if>
            <if test="ecsBillAmt != null and ecsBillAmt != ''"> ecs_bill_amt = #{ecsBillAmt,jdbcType=VARCHAR}, </if>
            <if test="ecsSummary != null and ecsSummary != ''"> ecs_summary = #{ecsSummary,jdbcType=VARCHAR}, </if>
            <if test="comparedSummary != null and ecsSummary != ''">compared_summary = #{comparedSummary,jdbcType=VARCHAR},</if>
        </trim>
    </sql>

    <!-- 查询条件 -->
    <sql id="allColumnCond">
        <if test="id != null and id != ''"> AND id = #{id,jdbcType=VARCHAR} </if>
        <if test="batchId != null and batchId != ''"> AND batch_id = #{batchId,jdbcType=VARCHAR} </if>
        <if test="tenantId != null and tenantId != ''"> AND tenant_id = #{tenantId,jdbcType=VARCHAR} </if>
        <if test="agyCode != null and agyCode != ''"> AND agy_code = #{agyCode,jdbcType=VARCHAR} </if>
        <if test="mofDivCode != null and mofDivCode != ''"> AND mof_div_code = #{mofDivCode,jdbcType=VARCHAR} </if>
        <if test="fiscal != null and fiscal != ''"> AND fiscal = #{fiscal,jdbcType=VARCHAR} </if>
        <if test="modifier != null and modifier != ''"> AND modifier = #{modifier,jdbcType=VARCHAR} </if>
        <if test="modifierName != null and modifierName != ''"> AND modifier_name = #{modifierName,jdbcType=VARCHAR} </if>
        <if test="modifiedTime != null"> AND modified_time = #{modifiedTime,jdbcType=VARCHAR} </if>
        <if test="creator != null and creator != ''"> AND creator = #{creator,jdbcType=VARCHAR} </if>
        <if test="creatorName != null and creatorName != ''"> AND creator_name = #{creatorName,jdbcType=VARCHAR} </if>
        <if test="createdTime != null"> AND created_time = #{createdTime,jdbcType=VARCHAR} </if>
        <if test="sourceAttachId != null and sourceAttachId != ''"> AND source_attach_id = #{sourceAttachId,jdbcType=VARCHAR} </if>
        <if test="billContext != null"> AND bill_context = #{billContext,jdbcType=VARCHAR} </if>
        <if test="attachId != null and attachId != ''"> AND attach_id = #{attachId,jdbcType=VARCHAR} </if>
        <if test="comparedStatus != null and comparedStatus != ''"> AND compared_status = #{comparedStatus,jdbcType=VARCHAR} </if>
        <if test="ecsBillNo != null and ecsBillNo != ''"> AND ecs_bill_no = #{ecsBillNo,jdbcType=VARCHAR} </if>
        <if test="ecsId != null and ecsId != ''"> AND ecs_id = #{ecsId,jdbcType=VARCHAR} </if>
        <if test="pcxBillId != null and pcxBillId != ''"> AND pcx_bill_id = #{pcxBillId,jdbcType=VARCHAR} </if>
        <if test="pcxBillNo != null and pcxBillNo != ''"> AND pcx_bill_no = #{pcxBillNo,jdbcType=VARCHAR} </if>
        <if test="ecsBillType != null and ecsBillType != ''"> AND ecs_bill_type = #{ecsBillType,jdbcType=VARCHAR} </if>
        <if test="ecsBillKind != null and ecsBillKind != ''"> AND ecs_bill_kind = #{ecsBillKind,jdbcType=VARCHAR} </if>
        <if test="ecsBillAmt != null and ecsBillAmt != ''"> AND ecs_bill_amt = #{ecsBillAmt,jdbcType=VARCHAR} </if>
        <if test="ecsSummary != null and ecsSummary != ''"> AND ecs_summary = #{ecsSummary,jdbcType=VARCHAR} </if>
        <if test="comparedSummary != null and ecsSummary != ''">AND compared_summary = #{comparedSummary,jdbcType=VARCHAR}</if>
    </sql>

    <sql id="allColumnItemValue">
        #{item.id,jdbcType=VARCHAR}, #{item.batchId,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=VARCHAR}, #{item.agyCode,jdbcType=VARCHAR}, #{item.mofDivCode,jdbcType=VARCHAR},
        #{item.fiscal,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.modifierName,jdbcType=VARCHAR}, #{item.modifiedTime,jdbcType=VARCHAR},
        #{item.creator,jdbcType=VARCHAR}, #{item.creatorName,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=VARCHAR}, #{item.sourceAttachId,jdbcType=VARCHAR},
        #{item.billContext,jdbcType=VARCHAR}, #{item.attachId,jdbcType=VARCHAR}, #{item.comparedStatus,jdbcType=VARCHAR}, #{item.ecsBillNo,jdbcType=VARCHAR},
        #{item.ecsId,jdbcType=VARCHAR}, #{item.pcxBillId,jdbcType=VARCHAR}, #{item.pcxBillNo,jdbcType=VARCHAR}, #{item.ecsBillType,jdbcType=VARCHAR},
        #{item.ecsBillKind,jdbcType=VARCHAR}, #{item.ecsBillAmt,jdbcType=VARCHAR}, #{item.ecsSummary,jdbcType=VARCHAR}, #{item.comparedSummary,jdbcType=VARCHAR}
    </sql>

    <!-- 查询列表 -->
    <select id="queryList" resultMap="BaseResultMap" parameterType="com.pty.pcx.entity.ecscompared.PcxEcsComparedResult">
        SELECT
        <include refid="allColumn"/>
        FROM pcx_ecs_compared_result
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <!-- 单条插入 -->
    <insert id="insert" parameterType="com.pty.pcx.entity.ecscompared.PcxEcsComparedResult">
        INSERT INTO pcx_ecs_compared_result (
        <include refid="allColumn"/>
        ) VALUES (
        #{id,jdbcType=VARCHAR}, #{batchId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{agyCode,jdbcType=VARCHAR}, #{mofDivCode,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{modifierName,jdbcType=VARCHAR}, #{modifiedTime,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, #{creatorName,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, #{sourceAttachId,jdbcType=VARCHAR},
        #{billContext,jdbcType=LONGVARCHAR}, #{attachId,jdbcType=VARCHAR}, #{comparedStatus,jdbcType=VARCHAR}, #{ecsBillNo,jdbcType=VARCHAR},
        #{ecsId,jdbcType=VARCHAR}, #{pcxBillId,jdbcType=VARCHAR}, #{pcxBillNo,jdbcType=VARCHAR}, #{ecsBillType,jdbcType=VARCHAR},
        #{ecsBillKind,jdbcType=VARCHAR}, #{ecsBillAmt,jdbcType=VARCHAR}, #{ecsSummary,jdbcType=VARCHAR},
        #{comparedSummary,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入 (MySQL) -->
    <insert id="batchInsert" parameterType="java.util.List" databaseId="mysql">
        INSERT INTO pcx_ecs_compared_result (
        <include refid="allColumn"/>
        )
        VALUES
        <foreach collection="list" item="item" separator="," index="index">
            (<include refid="allColumnItemValue"/>)
        </foreach>
    </insert>

    <!-- 批量插入 (Oracle) -->
    <insert id="batchInsert" parameterType="java.util.List" databaseId="oracle">
        INSERT INTO pcx_ecs_compared_result (
        <include refid="allColumn"/>
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            ( select
            <include refid="allColumnItemValue"/>
            from DUAL )
        </foreach>
    </insert>

</mapper>