<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasExpBizTypeDao">
    <sql id="allColumn">
        id,                                               expense_code,                                     expense_name,                                     ecs_biz_type_code,
        ecs_biz_type_name,                                biz_rule_code,                                      biz_rule_name,                                    seq,
        agy_code,                                         mof_div_code,                                     fiscal,                                           modifier,
        modifier_name,                                    modified_time,                                    creator,                                          creator_name,
        created_time
    </sql>

    <sql id="allColumnAlias">
        id as id,                                         expense_code as expenseCode,                      expense_name as expenseName,                      ecs_biz_type_code as ecsBizTypeCode,
        ecs_biz_type_name as ecsBizTypeName,              biz_rule_code as bizRuleCode,                         biz_rule_name as bizRuleName,                     seq as seq,
        agy_code as agyCode,                              mof_div_code as mofDivCode,                       fiscal as fiscal,                                 modifier as modifier,
        modifier_name as modifierName,                    modified_time as modifiedTime,                    creator as creator,                               creator_name as creatorName,
        created_time as createdTime,                      tenant_id as tenantId
    </sql>

    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR},                           #{expenseCode,jdbcType=VARCHAR},                  #{expenseName,jdbcType=VARCHAR},                  #{ecsBizTypeCode,jdbcType=VARCHAR},
        #{ecsBizTypeName,jdbcType=VARCHAR},               #{bizRuleCode,jdbcType=VARCHAR},                    #{bizRuleName,jdbcType=VARCHAR},                  #{seq,jdbcType=INTEGER},
        #{agyCode,jdbcType=VARCHAR},                      #{mofDivCode,jdbcType=VARCHAR},                   #{fiscal,jdbcType=VARCHAR},                       #{modifier,jdbcType=VARCHAR},
        #{modifierName,jdbcType=VARCHAR},                 #{modifiedTime,jdbcType=VARCHAR},                 #{creator,jdbcType=VARCHAR},                      #{creatorName,jdbcType=VARCHAR},
        #{createdTime,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id=#{id,jdbcType=VARCHAR},
             </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code=#{expenseCode,jdbcType=VARCHAR},
             </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name=#{expenseName,jdbcType=VARCHAR},
             </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                ecs_biz_type_code=#{ecsBizTypeCode,jdbcType=VARCHAR},
             </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                ecs_biz_type_name=#{ecsBizTypeName,jdbcType=VARCHAR},
             </if>
            <if test="bizRuleCode != null and bizRuleCode != ''">
                biz_rule_code=#{bizRuleCode,jdbcType=VARCHAR},
             </if>
            <if test="bizRuleName != null and bizRuleName != ''">
                biz_rule_name=#{bizRuleName,jdbcType=VARCHAR},
             </if>
            <if test="seq != null">
                seq=#{seq,jdbcType=INTEGER},
             </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
             </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
             </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
             </if>
            <if test="modifier != null and modifier != ''">
                modifier=#{modifier,jdbcType=VARCHAR},
             </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name=#{modifierName,jdbcType=VARCHAR},
             </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time=#{modifiedTime,jdbcType=VARCHAR},
             </if>
            <if test="creator != null and creator != ''">
                creator=#{creator,jdbcType=VARCHAR},
             </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name=#{creatorName,jdbcType=VARCHAR},
             </if>
            <if test="createdTime != null and createdTime != ''">
                created_time=#{createdTime,jdbcType=VARCHAR},
             </if>
        </trim>
    </sql>

    <sql id="columnSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code,
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name,
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                ecs_biz_type_code,
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                ecs_biz_type_name,
            </if>
            <if test="bizRuleCode != null and bizRuleCode != ''">
                biz_rule_code,
            </if>
            <if test="bizRuleName != null and bizRuleName != ''">
                biz_rule_name,
            </if>
            <if test="seq != null">
                seq,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name,
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name,
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time,
            </if>
        </trim>
    </sql>

    <sql id="columnValueSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                #{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                #{expenseName,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
                #{ecsBizTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
                #{ecsBizTypeName,jdbcType=VARCHAR},
            </if>
            <if test="bizRuleCode != null and bizRuleCode != ''">
                #{bizRuleCode,jdbcType=VARCHAR},
            </if>
            <if test="bizRuleName != null and bizRuleName != ''">
                #{bizRuleName,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                #{seq,jdbcType=INTEGER},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                #{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                #{modifiedTime,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime != ''">
                #{createdTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="expenseCode != null and expenseCode != ''">
            AND expense_code=#{expenseCode,jdbcType=VARCHAR}
        </if>
        <if test="expenseName != null and expenseName != ''">
            AND expense_name=#{expenseName,jdbcType=VARCHAR}
        </if>
        <if test="ecsBizTypeCode != null and ecsBizTypeCode != ''">
            AND ecs_biz_type_code=#{ecsBizTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="ecsBizTypeName != null and ecsBizTypeName != ''">
            AND ecs_biz_type_name=#{ecsBizTypeName,jdbcType=VARCHAR}
        </if>
        <if test="bizRuleCode != null and bizRuleCode != ''">
            AND biz_rule_code=#{bizRuleCode,jdbcType=VARCHAR}
        </if>
        <if test="bizRuleName != null and bizRuleName != ''">
            AND biz_rule_name=#{bizRuleName,jdbcType=VARCHAR}
        </if>
        <if test="seq != null">
            AND seq=#{seq,jdbcType=INTEGER}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
            AND modifier=#{modifier,jdbcType=VARCHAR}
        </if>
        <if test="modifierName != null and modifierName != ''">
            AND modifier_name=#{modifierName,jdbcType=VARCHAR}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''">
            AND modified_time=#{modifiedTime,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            AND creator=#{creator,jdbcType=VARCHAR}
        </if>
        <if test="creatorName != null and creatorName != ''">
            AND creator_name=#{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            AND created_time=#{createdTime,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.bas.PcxBasExpBizType">
        INSERT INTO pcx_bas_exp_biz_type (
            <include refid="allColumn" />
        ) VALUES (
            <include refid="allColumnValue" />
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.qo.bas.PcxBasExpBizTypeQO">
        INSERT INTO pcx_bas_exp_biz_type (
            <include refid="columnSelective" />
        ) VALUES (
            <include refid="columnValueSelective" />
        )
    </insert>

    <delete id="deleteById" parameterType="string">
        DELETE FROM pcx_bas_exp_biz_type
        WHERE
            id=#{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM pcx_bas_exp_biz_type
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.qo.bas.PcxBasExpBizTypeQO">
        DELETE FROM pcx_bas_exp_biz_type
        WHERE 1=1
            <include refid="allColumnCond" />
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.qo.bas.PcxBasExpBizTypeQO">
        UPDATE pcx_bas_exp_biz_type
        <set>
            <include refid="allColumnSet" />
        </set>

        WHERE id=#{id,jdbcType=VARCHAR}
    </update>

    <select id="selectById" parameterType="com.pty.pcx.qo.bas.PcxBasExpBizTypeQO" resultType="com.pty.pcx.vo.bas.PcxBasExpBizTypeVO">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_bas_exp_biz_type
        WHERE id=#{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.vo.bas.PcxBasExpBizTypeVO">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_bas_exp_biz_type
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.qo.bas.PcxBasExpBizTypeQO" resultType="com.pty.pcx.vo.bas.PcxBasExpBizTypeVO">
        SELECT
            <include refid="allColumnAlias" />
        FROM pcx_bas_exp_biz_type
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.bas.PcxBasExpBizType" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_bas_exp_biz_type
        WHERE 1=1
            <include refid="allColumnCond" />
    </select>
</mapper>
