<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillBalanceDao">


    <select id="getSumExpenseLoanBackAmt" resultType="com.pty.pcx.entity.bill.PcxBillBalance">
        SELECT p.relate_balance_id AS balanceId, SUM(p.back_amt) AS usedAmt
        FROM pty_balance_back_detail p
        JOIN pcx_bill b ON p.bill_id = b.id
        WHERE
        (b.approve_status = '2' OR b.id = #{billId})
        AND p.bill_type = 'expense'
        AND p.relate_bill_type = 'loan'
        AND p.relate_balance_id IN
        <foreach collection="balanceIds" index="index" item="id" open="(" separator="," close=")">
            #{id, jdbcType=VARCHAR}
        </foreach>
        GROUP BY p.relate_balance_id
    </select>

    <select id="getSumExpenseLoanBackAmtExeCurrBill" resultType="com.pty.pcx.entity.bill.PcxBillBalance">
        SELECT p.relate_balance_id AS balanceId, SUM(p.back_amt) AS usedAmt
        FROM pty_balance_back_detail p
        JOIN pcx_bill b ON p.bill_id = b.id
        WHERE
        (b.approve_status = '2' AND b.id != #{billId})
        AND p.bill_type = 'expense'
        AND p.relate_bill_type = 'loan'
        AND p.relate_balance_id IN
        <foreach collection="balanceIds" index="index" item="id" open="(" separator="," close=")">
            #{id, jdbcType=VARCHAR}
        </foreach>
        GROUP BY p.relate_balance_id
    </select>

    <select id="getSumPayAmt" resultType="com.pty.pcx.entity.bill.PcxBillBalance">
        SELECT
        balance.balance_id AS balanceId,
        SUM(balance.used_amt) AS usedAmt,
        bill.bill_func_code AS billFuncCode
        FROM
        pcx_bill_balance balance JOIN pcx_bill bill ON balance.bill_id = bill.id
        WHERE
        (
        ((bill.bill_func_code = #{billFunc1, jdbcType=VARCHAR} AND bill.approve_status = '2') OR (bill.id = #{billId, jdbcType=VARCHAR}))
        OR
        (bill.bill_func_code = #{billFunc2, jdbcType=VARCHAR} AND bill.approve_status = '2')
        )
        AND balance.balance_id IN
        <foreach collection="balanceIds" index="index" item="id" open="(" separator="," close=")">
            #{id, jdbcType=VARCHAR}
        </foreach>
        GROUP BY
        balance.balance_id, bill.bill_func_code
    </select>
    <select id="selectByBalanceId" resultType="com.pty.pcx.dto.bill.PcxBillBalanceDTO">
        SELECT
        a.bill_no as billNo,
        a.creator_name as creatorName,
        a.trans_date as transDate,
        a.biz_type_name as billTypeName,
        a.biz_type as billType,
        b.used_amt as checkAmt,
        a.fiscal as fiscal,
        a.agy_code as agyCode,
        a.agy_name as agyName,
        a.mof_div_code as mofDivCode,
        a.bill_func_code as billFunc,
        a.bill_func_code as billType,
        a.bill_func_name as billTypeName,
        a.loan_amt as expenseUsedAmt,
        (a.check_amt - a.loan_amt) as expenseCanUsedAmt,
        a.loan_amt as backAmt,
        (a.expense_codes) as expenseTypeCode,
        (a.expense_names) as expenseTypeName,
        a.id as billId,
        a.reason as reason,
        a.department_name as departmentName,
        a.audit_time as auditedTime,
        case when a.approve_status ='0' then '未送审'
        when a.approve_status = '1' then '在审'
        when a.approve_status = '2' then '已终审'
        else '' end as billStatus
        FROM pcx_bill a
        INNER JOIN pcx_bill_balance b ON a.id = b.bill_id
        where a.bill_func_code = #{billFuncCode} and a.bill_status = 2 and b.balance_id in
        <foreach collection="balanceIds" open="(" close=")" item="balanceId" separator="," index="index">
            #{balanceId}
        </foreach>
        order by a.trans_date asc
    </select>
    <select id="getAuthIbalBalance" resultType="com.pty.pcx.vo.balance.ProjectBalVO">
        SELECT
            ibal_balance_id AS balance_id,
            sum( b.total_amt ) AS total_amt,
            sum( b.total_amt- b.used_amt ) AS balance_amt
        FROM
            (SELECT to_balance_id, ibal_balance_id FROM bud_bill_relation GROUP BY to_balance_id, ibal_balance_id) a
            LEFT JOIN bud_balance b ON a.to_balance_id = b.BALANCE_ID
        WHERE
            ibal_balance_id IS NOT NULL
          AND to_balance_id IN ( SELECT balance_id FROM bud_data_auth
                WHERE department_code in
                    <foreach collection="deptCodes" index="index" item="deptCode" open="(" separator="," close=")">
                        #{deptCode}
                    </foreach>
            )
        GROUP BY
            ibal_balance_id,
            b.agy_code,
            b.mof_div_code,
            b.fiscal
    </select>
    <select id="getDefaultBudByIbalId" resultType="java.lang.String">
        SELECT
            to_balance_id AS balance_id
        FROM
            bud_bill_relation
        WHERE
            ibal_balance_id = #{ibalId}
        limit 1
    </select>

</mapper>
