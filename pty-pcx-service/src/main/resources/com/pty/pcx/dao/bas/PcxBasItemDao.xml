<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasItemDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.bas.PcxBasItem" id="PcxBasItemMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="isLeaf" column="is_leaf" jdbcType="INTEGER"/>
        <result property="billtypeCode" column="billtype_code" jdbcType="VARCHAR"/>
        <result property="budgetCtrl" column="budget_ctrl" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap type="com.pty.pcx.vo.PcxBasItemVO" id="PcxBasItemVOMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="itemId" column="itemId" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="isLeaf" column="is_leaf" jdbcType="INTEGER"/>
        <result property="billtypeCode" column="billtype_code" jdbcType="VARCHAR"/>
        <result property="budgetCtrl" column="budget_ctrl" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 全部字段 -->
    <sql id="allColumn">
        id,
        parent_code,
        parent_name,
        item_code,
        item_name,
        summary,
        agy_code,
        mof_div_code,
        fiscal,
        modifier,
        modifier_name,
        modified_time,
        creator,
        creator_name,
        created_time,
        seq,
        is_enabled,
        is_leaf,
        billtype_code,
        budget_ctrl
    </sql>

    <sql id="allColumnSet">
        <trim prefix="" suffixOverrides=",">
            <if test="parentCode != null and parentCode != ''"> parent_code = #{parentCode,jdbcType=VARCHAR}, </if>
            <if test="parentName != null and parentName != ''"> parent_name = #{parentName,jdbcType=VARCHAR}, </if>
            <if test="itemCode != null and itemCode != ''"> item_code = #{itemCode,jdbcType=VARCHAR}, </if>
            <if test="itemName != null and itemName != ''"> item_name = #{itemName,jdbcType=VARCHAR}, </if>
            <if test="summary != null and summary != ''"> summary = #{summary,jdbcType=VARCHAR}, </if>
            <if test="fiscal != null and fiscal != ''"> fiscal = #{fiscal,jdbcType=VARCHAR}, </if>
            <if test="modifier != null and modifier != ''"> modifier = #{modifier,jdbcType=VARCHAR}, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name = #{modifierName,jdbcType=VARCHAR}, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time = #{modifiedTime,jdbcType=VARCHAR}, </if>
            <if test="seq != null"> seq = #{seq,jdbcType=INTEGER}, </if>
            <if test="isEnabled != null"> is_enabled = #{isEnabled,jdbcType=INTEGER}, </if>
            <if test="isLeaf != null"> is_leaf = #{isLeaf,jdbcType=INTEGER}, </if>
            <if test="billtypeCode != null and billtypeCode != ''"> billtype_code = #{billtypeCode,jdbcType=VARCHAR}, </if>
            <if test="budgetCtrl != null and budgetCtrl != ''"> budget_ctrl = #{budgetCtrl,jdbcType=VARCHAR}, </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            and id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="parentCode != null and parentCode != ''">
            and parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test="parentName != null and parentName != ''">
            and parent_name = #{parentName,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="itemName != null and itemName != ''">
            and item_name = #{itemName,jdbcType=VARCHAR}
        </if>
        <if test="summary != null and summary != ''">
            and summary = #{summary,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            and agy_code = #{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            and fiscal = #{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
            and modifier = #{modifier,jdbcType=VARCHAR}
        </if>
        <if test="modifierName != null and modifierName != ''">
            and modifier_name = #{modifierName,jdbcType=VARCHAR}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''">
            and modified_time = #{modifiedTime,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            and creator = #{creator,jdbcType=VARCHAR}
        </if>
        <if test="creatorName != null and creatorName != ''">
            and creator_name = #{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="createdTime != null and createdTime != ''">
            and created_time = #{createdTime,jdbcType=VARCHAR}
        </if>
        <if test="seq != null">
            and seq = #{seq,jdbcType=INTEGER}
        </if>
        <if test="isEnabled != null">
            and is_enabled = #{isEnabled,jdbcType=INTEGER}
        </if>
        <if test="isLeaf != null">
            and is_leaf = #{isLeaf,jdbcType=INTEGER}
        </if>
        <if test="billtypeCode != null and billtypeCode != ''">
            and billtype_code like concat('%',#{billtypeCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="budgetCtrl != null and budgetCtrl != ''">
            and budget_ctrl like concat('%',#{budgetCtrl,jdbcType=VARCHAR},'%')
        </if>
    </sql>

    <sql id="columnQOCond">
        <if test="ids != null and ids.size > 0">
            and id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="itemCodes != null and itemCodes.size > 0">
            and item_code in
            <foreach collection="itemCodes" index="index" item="itemCode" open="(" separator="," close=")">
                #{itemCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="parentCodes != null and parentCodes.size > 0">
            and parent_code in
            <foreach collection="parentCodes" index="index" item="parentCode" open="(" separator="," close=")">
                #{parentCode,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <!-- 查询单个 -->
    <select id="selectByQO" resultMap="PcxBasItemMap">
        select
        <include refid="allColumn" />
        from pcx_bas_item
        where 1=1
        <include refid="allColumnCond"/>
        <include refid="columnQOCond"/>
        ORDER BY seq ASC
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxBasItemMap">
        select
        <include refid="allColumn" />
        from pcx_bas_item
        where 1=1
        <include refid="allColumnCond" />
        ORDER BY seq ASC
    </select>

    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_bas_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''"> id, </if>
            <if test="parentCode != null and parentCode != ''"> parent_code, </if>
            <if test="parentName != null and parentName != ''"> parent_name, </if>
            <if test="itemCode != null and itemCode != ''"> item_code, </if>
            <if test="itemName != null and itemName != ''"> item_name, </if>
            <if test="summary != null and summary != ''"> summary, </if>
            <if test="agyCode != null and agyCode != ''"> agy_code, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> mof_div_code, </if>
            <if test="fiscal != null and fiscal != ''"> fiscal, </if>
            <if test="modifier != null and modifier != ''"> modifier, </if>
            <if test="modifierName != null and modifierName != ''"> modifier_name, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> modified_time, </if>
            <if test="creator != null and creator != ''"> creator, </if>
            <if test="creatorName != null and creatorName != ''"> creator_name, </if>
            <if test="createdTime != null and createdTime != ''"> created_time, </if>
            <if test="seq != null"> seq, </if>
            <if test="isEnabled != null"> is_enabled, </if>
            <if test="isLeaf != null"> is_leaf, </if>
            <if test="billtypeCode != null and billtypeCode != ''"> billtype_code, </if>
            <if test="budgetCtrl != null and budgetCtrl != ''"> budget_ctrl, </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''"> #{id,jdbcType=VARCHAR}, </if>
            <if test="parentCode != null and parentCode != ''"> #{parentCode,jdbcType=VARCHAR}, </if>
            <if test="parentName != null and parentName != ''"> #{parentName,jdbcType=VARCHAR}, </if>
            <if test="itemCode != null and itemCode != ''"> #{itemCode,jdbcType=VARCHAR}, </if>
            <if test="itemName != null and itemName != ''"> #{itemName,jdbcType=VARCHAR}, </if>
            <if test="summary != null and summary != ''"> #{summary,jdbcType=VARCHAR}, </if>
            <if test="agyCode != null and agyCode != ''"> #{agyCode,jdbcType=VARCHAR}, </if>
            <if test="mofDivCode != null and mofDivCode != ''"> #{mofDivCode,jdbcType=VARCHAR}, </if>
            <if test="fiscal != null and fiscal != ''"> #{fiscal,jdbcType=VARCHAR}, </if>
            <if test="modifier != null and modifier != ''"> #{modifier,jdbcType=VARCHAR}, </if>
            <if test="modifierName != null and modifierName != ''"> #{modifierName,jdbcType=VARCHAR}, </if>
            <if test="modifiedTime != null and modifiedTime != ''"> #{modifiedTime,jdbcType=VARCHAR}, </if>
            <if test="creator != null and creator != ''"> #{creator,jdbcType=VARCHAR}, </if>
            <if test="creatorName != null and creatorName != ''"> #{creatorName,jdbcType=VARCHAR}, </if>
            <if test="createdTime != null and createdTime != ''"> #{createdTime,jdbcType=VARCHAR}, </if>
            <if test="seq != null"> #{seq,jdbcType=INTEGER}, </if>
            <if test="isEnabled != null"> #{isEnabled,jdbcType=INTEGER}, </if>
            <if test="isLeaf != null"> #{isLeaf,jdbcType=INTEGER}, </if>
            <if test="billtypeCode != null and billtypeCode != ''"> #{billtypeCode,jdbcType=VARCHAR}, </if>
            <if test="budgetCtrl != null and budgetCtrl != ''"> #{budgetCtrl,jdbcType=VARCHAR}, </if>
        </trim>
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="updateById">
        update pcx_bas_item SET
        <include refid="allColumnSet"/>
        where 1=1
        and agy_code = #{agyCode,jdbcType=VARCHAR}
        and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        and fiscal = #{fiscal,jdbcType=VARCHAR}
        and id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectSimpleList" resultMap="PcxBasItemMap">
        select
        id,
        parent_code,
        parent_name,
        item_code,
        item_name,
        agy_code,
        mof_div_code,
        fiscal,
        seq,
        is_enabled,
        is_leaf,
        billtype_code,
        budget_ctrl
        from pcx_bas_item
        where 1=1
        <include refid="allColumnCond" />
        ORDER BY seq ASC
    </select>

    <update id="disEnableOrEnableId" parameterType="com.pty.pcx.entity.bas.PcxBasItem">
        update pcx_bas_item SET
        is_enabled = #{isEnabled,jdbcType=INTEGER}
        where 1=1
            and agy_code = #{agyCode,jdbcType=VARCHAR}
            and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            and fiscal = #{fiscal,jdbcType=VARCHAR}
            and id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteById" parameterType="com.pty.pcx.entity.bas.PcxBasItem">
        delete from pcx_bas_item
        where 1=1
        and agy_code = #{agyCode,jdbcType=VARCHAR}
        and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        and fiscal = #{fiscal,jdbcType=VARCHAR}
        and id = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="getTreeData" resultMap="PcxBasItemVOMap" parameterType="com.pty.pcx.qo.bas.PcxBasItemQO">
        select
        id,
        id as "itemId",
        parent_code,
        parent_name,
        item_code,
        item_name,
        agy_code,
        mof_div_code,
        fiscal,
        seq,
        is_enabled,
        is_leaf,
        billtype_code,
        budget_ctrl
        from pcx_bas_item
        where 1=1
        <include refid="allColumnCond" />
        <include refid="columnQOCond"/>
        ORDER BY seq ASC
    </select>

    <select id="getApplyItem" resultType="com.pty.pcx.entity.bas.PcxBasItem">
        SELECT
        pbi.parent_code,
        pbi.item_code
        FROM
        pcx_bas_exp_type pbet
        LEFT JOIN pcx_bas_item_exp pbie ON pbet.expense_code = pbie.expense_code
        AND pbet.agy_code = pbie.agy_code
        AND pbet.mof_div_code = pbie.mof_div_code
        AND pbet.fiscal = pbie.fiscal
        LEFT JOIN pcx_bas_item  pbi on pbie.item_code = pbi.item_code
        AND pbi.agy_code = pbie.agy_code
        AND pbi.mof_div_code = pbie.mof_div_code
        AND pbi.fiscal = pbie.fiscal
        WHERE
        pbet.is_need_apply = 1
        AND pbet.agy_code = #{agyCode,jdbcType=VARCHAR}
        AND pbet.mof_div_code= #{mofDivCode,jdbcType=VARCHAR}
        AND pbet.fiscal =#{fiscal,jdbcType=VARCHAR}
        AND pbi.parent_code IS NOT NULL
        GROUP BY
        pbi.parent_code,
        pbi.item_code
    </select>
    <select id="selectCommonItem" resultMap="PcxBasItemMap">
        SELECT
            item.*
        FROM
            pcx_bas_item item
                LEFT JOIN pcx_bas_item_exp exp ON item.fiscal = exp.fiscal
                AND item.agy_code = exp.agy_code
                AND item.mof_div_code = exp.mof_div_code
                AND item.item_code = exp.item_code
        WHERE
            exp.expense_code NOT IN <foreach collection="expenseCodes" open="(" separator="," close=")" item="expenseCode">#{expenseCode}</foreach>
          AND item.fiscal = #{fiscal}
          AND item.agy_code = #{agyCode}
          AND item.mof_div_code = #{mofDivCode}
          AND item.is_enabled = 1
          AND item.is_leaf = 1;
    </select>
</mapper>

