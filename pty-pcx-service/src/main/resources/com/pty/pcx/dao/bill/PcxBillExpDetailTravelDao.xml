<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillExpDetailTravelDao">

    <update id="updateTripId">
        update pcx_bill_exp_detail_travel set trip_id = #{tripId}
          where id in <foreach collection="ids" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
    </update>
    <update id="updateTripIdEmpty">
        update pcx_bill_exp_detail_travel set trip_id = '', trip_segment_id = '',trip_seq=0,trip_flag=0
        where bill_id = #{billId}
    </update>
    <update id="updateAddition">
        update pcx_bill_exp_detail_travel
        set department_code = #{departmentCode},
            department_name = #{departmentName},
            bud_get_item_code=#{budGetItemCode},
            bud_get_item=#{budGetItem},
            accounting_income_type_code=#{accountingIncomeTypeCode},
            accounting_income_type=#{accountingIncomeType},
            customer_code=#{customerCode},
            customer=#{customer},
            bi_income_type_code=#{biIncomeTypeCode},
            bi_income_type=#{biIncomeType},
        accounting_expense_item_code=#{accountingExpenseItemCode},
        accounting_expense_item=#{accountingExpenseItem},
            tendering_pro_code=#{tenderingProCode},
            tendering_pro=#{tenderingPro},
            dev_pro_code=#{devProCode},
            dev_pro=#{devPro},
            a8_pp_pro_code=#{a8PpProCode},
            a8_pp_pro=#{a8PpPro},
            deferred_assets_code=#{deferredAssetsCode},
            deferred_assets=#{deferredAssets}
        where bill_id = #{billId}
          and id in <foreach collection="detailIds" item="item" open="(" separator="," close=")">#{item}</foreach>;
    </update>
    <select id="sumYearSub" resultType="java.math.BigDecimal">
        SELECT
            sum( b.check_amt )
        FROM
            pcx_bill_exp_detail_travel a
                LEFT JOIN pcx_bill b ON b.id = a.bill_id
        WHERE
            a.exp_detail_code = '3021103'
          AND a.emp_code = #{empCode}
          AND b.trans_date >= #{startDate}

    </select>
</mapper>
