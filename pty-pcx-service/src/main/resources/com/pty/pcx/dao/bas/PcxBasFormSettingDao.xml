<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasFormSettingDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.bas.PcxBasFormSetting" id="PcxBasFormSettingMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="formClassify" column="form_classify" jdbcType="VARCHAR"/>
        <result property="formCode" column="form_code" jdbcType="VARCHAR"/>
        <result property="formName" column="form_name" jdbcType="VARCHAR"/>
        <result property="formType" column="form_type" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="billFuncCode" column="bill_func_code" jdbcType="INTEGER"/>
        <result property="fieldValue" column="field_value" jdbcType="VARCHAR"/>
        <result property="fieldLabel" column="field_label" jdbcType="VARCHAR"/>
        <result property="fieldTitle" column="field_title" jdbcType="VARCHAR"/>
        <result property="fieldName" column="field_name" jdbcType="VARCHAR"/>
        <result property="isEdit" column="is_edit" jdbcType="INTEGER"/>
        <result property="isNull" column="is_null" jdbcType="INTEGER"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="isAddition" column="is_addition" jdbcType="INTEGER"/>
        <result property="notes" column="notes" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="editorCode" column="editor_code" jdbcType="VARCHAR"/>
        <result property="editorName" column="editor_name" jdbcType="VARCHAR"/>
        <result property="fontStyle" column="font_style" jdbcType="VARCHAR"/>
        <result property="showType" column="show_type" jdbcType="VARCHAR"/>
        <result property="dataTypeCode" column="data_type_code" jdbcType="VARCHAR"/>
        <result property="dataTypeName" column="data_type_name" jdbcType="VARCHAR"/>
        <result property="dataSourceCode" column="data_source_code" jdbcType="VARCHAR"/>
        <result property="dataSourceName" column="data_source_name" jdbcType="VARCHAR"/>
        <result property="dataClassifyCode" column="data_classify_code" jdbcType="VARCHAR"/>
        <result property="dataClassifyName" column="data_classify_name" jdbcType="VARCHAR"/>
        <result property="defVal" column="def_val" jdbcType="VARCHAR"/>
        <result property="defValRule" column="def_val_rule" jdbcType="VARCHAR"/>
        <result property="fieldControl" column="field_control" jdbcType="VARCHAR"/>
        <result property="showRelation" column="show_relation" jdbcType="VARCHAR"/>
        <result property="maxLength" column="max_length" jdbcType="INTEGER"/>
        <result property="minLength" column="min_length" jdbcType="INTEGER"/>
        <result property="isExtend" column="is_extend" jdbcType="INTEGER"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR"/>
        <result property="modifiedTime" column="modified_time" jdbcType="VARCHAR"/>
        <result property="creatorCode" column="creator_code" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="VARCHAR"/>
        <result property="onlySelectLast" column="only_select_last" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 全部字段 -->
	<sql id="allColumn">
        id,
        form_classify,
        form_code,
        form_name,
        form_type,
        agy_code,
        fiscal,
        mof_div_code,
        bill_func_code,
        field_value,
        field_label,
        field_title,
        field_name,
        is_edit,
        is_null,
        is_enabled,
          is_addition,
        notes,
        remarks,
        editor_code,
        editor_name,
        font_style,
        show_type,
        data_type_code,
        data_type_name,
        data_source_code,
        data_source_name,
        data_classify_code,
        data_classify_name,
        def_val,
        def_val_rule,
        field_control,
        show_relation,
        max_length,
        min_length,
        is_extend,
        seq,
        modifier,
        modifier_name,
        modified_time,
        creator_code,
        creator_name,
        created_time,
        only_select_last
	</sql>


    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxBasFormSettingMap">
        select
          <include refid="allColumn" />
        from pcx_bas_form_setting
        where id = #{id}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxBasFormSettingMap">
        select
        <include refid="allColumn" />
        from pcx_bas_form_setting
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="formClassify != null and formClassify != ''">
                and form_classify = #{formClassify}
            </if>
            <if test="formCode != null and formCode != ''">
                and form_code = #{formCode}
            </if>
            <if test="formName != null and formName != ''">
                and form_name = #{formName}
            </if>
            <if test="formType != null and formType != ''">
                and form_type = #{formType}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
            <if test="billFuncCode != null">
                and bill_func_code &amp; #{billFuncCode} >0
            </if>
            <if test="fieldValue != null and fieldValue != ''">
                and field_value = #{fieldValue}
            </if>
            <if test="fieldTitle != null and fieldTitle != ''">
                and field_title = #{fieldTitle}
            </if>
            <if test="fieldName != null and fieldName != ''">
                and field_name = #{fieldName}
            </if>
            <if test="isEdit != null">
                and is_edit = #{isEdit}
            </if>
            <if test="isNull != null">
                and is_null = #{isNull}
            </if>
            <if test="isEnabled != null">
                and is_enabled = #{isEnabled}
            </if>
            <if test="isAddition != null">
                and is_addition = #{isAddition}
            </if>
            <if test="notes != null and notes != ''">
                and notes = #{notes}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks}
            </if>
            <if test="editorCode != null and editorCode != ''">
                and editor_code = #{editorCode}
            </if>
            <if test="editorName != null and editorName != ''">
                and editor_name = #{editorName}
            </if>
            <if test="fontStyle != null and fontStyle != ''">
                and font_style = #{fontStyle}
            </if>
            <if test="showType != null and showType != ''">
                and show_type = #{showType}
            </if>
            <if test="dataTypeCode != null and dataTypeCode != ''">
                and data_type_code = #{dataTypeCode}
            </if>
            <if test="dataTypeName != null and dataTypeName != ''">
                and data_type_name = #{dataTypeName}
            </if>
            <if test="dataSourceCode != null and dataSourceCode != ''">
                and data_source_code = #{dataSourceCode}
            </if>
            <if test="dataSourceName != null and dataSourceName != ''">
                and data_source_name = #{dataSourceName}
            </if>
            <if test="dataClassifyCode != null and dataClassifyCode != ''">
                and data_classify_code = #{dataClassifyCode}
            </if>
            <if test="dataClassifyName != null and dataClassifyName != ''">
                and data_classify_name = #{dataClassifyName}
            </if>
            <if test="defVal != null and defVal != ''">
                and def_val = #{defVal}
            </if>
            <if test="defValRule != null and defValRule != ''">
                and def_val_rule = #{defValRule}
            </if>
            <if test="fieldControl != null and fieldControl != ''">
                and field_control = #{fieldControl}
            </if>
            <if test="showRelation != null and showRelation != ''">
                and show_relation = #{showRelation}
            </if>
            <if test="maxLength != null">
                and max_length = #{maxLength}
            </if>
            <if test="minLength != null">
                and min_length = #{minLength}
            </if>
            <if test="isExtend != null">
                and is_extend = #{isExtend}
            </if>
            <if test="seq != null">
                and seq = #{seq}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="modifierName != null and modifierName != ''">
                and modifier_name = #{modifierName}
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                and modified_time = #{modifiedTime}
            </if>
            <if test="creatorCode != null and creatorCode != ''">
                and creator_code = #{creatorCode}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and creator_name = #{creatorName}
            </if>
            <if test="createdTime != null and createdTime != ''">
                and created_time = #{createdTime}
            </if>
            <if test="onlySelectLast != null">
                and only_select_last = #{onlySelectLast}
            </if>
        </where>
    </select>
    <select id="getBlockProperties" resultType="com.pty.pcx.dto.block.BlockProperty">
        select
        field_title as fieldTitle,
        field_name as fieldName,
        is_edit as isEdit,
        is_null as isRequired,
        show_type as showType,
        notes,
        remarks,
        editor_code as editorCode,
        data_type_code as dataTypeCode,
        data_type_name as dataTypeName,
        data_source_code as dataSourceCode,
        data_classify_code as dataClassifyCode,
        field_value as fieldValue,
        field_label as fieldLabel,
        form_code as formCode,
        form_name as formName,
        is_addition as isAddition,
        field_page as fieldPage
        from pcx_bas_form_setting
        where
            form_classify = #{formClassify,jdbcType=VARCHAR}
          and fiscal = #{fiscal,jdbcType=VARCHAR} and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
          and bill_func_code &amp; #{billFuncCode} >0
          and agy_code = #{agyCode,jdbcType=VARCHAR}
          and is_enabled = 1
          and is_addition = 1
          and form_type = #{formType,jdbcType=VARCHAR}
          and form_code in <foreach collection="formCodeList" item="formCode" open="(" separator="," close=")">
        #{formCode,jdbcType=VARCHAR}
    </foreach>
        order by seq
    </select>

    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert ignore into pcx_bas_form_setting (
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
			<if test="formClassify != null and formClassify != ''">
				form_classify,
			</if>
			<if test="formCode != null and formCode != ''">
				form_code,
			</if>
			<if test="formName != null and formName != ''">
				form_name,
			</if>
			<if test="formType != null and formType != ''">
				form_type,
			</if>
			<if test="agyCode != null and agyCode != ''">
				agy_code,
			</if>
			<if test="fiscal != null and fiscal != ''">
				fiscal,
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				mof_div_code,
			</if>
			<if test="billFuncCode != null">
				bill_func_code,
			</if>
			<if test="fieldValue != null and fieldValue != ''">
				field_value,
			</if>
            <if test="fieldLabel!= null and fieldLabel != ''">
                field_label,
            </if>
			<if test="fieldTitle != null and fieldTitle != ''">
				field_title,
			</if>
			<if test="fieldName != null and fieldName != ''">
				field_name,
			</if>
			<if test="isEdit != null">
				is_edit,
			</if>
			<if test="isNull != null">
				is_null,
			</if>
			<if test="isEnabled != null">
				is_enabled,
			</if>
            <if test="isAddition != null">
                is_addition,
            </if>
			<if test="notes != null and notes != ''">
				notes,
			</if>
			<if test="remarks != null and remarks != ''">
				remarks,
			</if>
			<if test="editorCode != null and editorCode != ''">
				editor_code,
			</if>
			<if test="editorName != null and editorName != ''">
				editor_name,
			</if>
			<if test="fontStyle != null and fontStyle != ''">
				font_style,
			</if>
			<if test="showType != null and showType != ''">
				show_type,
			</if>
			<if test="dataTypeCode != null and dataTypeCode != ''">
				data_type_code,
			</if>
			<if test="dataTypeName != null and dataTypeName != ''">
				data_type_name,
			</if>
			<if test="dataSourceCode != null and dataSourceCode != ''">
				data_source_code,
			</if>
            <if test="dataSourceName != null and dataSourceName != ''">
                data_source_name,
            </if>
			<if test="dataClassifyCode != null and dataClassifyCode != ''">
				data_classify_code,
			</if>
			<if test="dataClassifyName != null and dataClassifyName != ''">
				data_classify_name,
			</if>
			<if test="defVal != null and defVal != ''">
				def_val,
			</if>
            <if test="defValRule != null and defValRule != ''">
                def_val_rule,
            </if>
			<if test="fieldControl != null and fieldControl != ''">
				field_control,
			</if>
			<if test="showRelation != null and showRelation != ''">
				show_relation,
			</if>
			<if test="maxLength != null">
				max_length,
			</if>
			<if test="minLength != null">
				min_length,
			</if>
			<if test="isExtend != null">
				is_extend,
			</if>
			<if test="seq != null">
				seq,
			</if>
			<if test="modifier != null and modifier != ''">
				modifier,
			</if>
			<if test="modifierName != null and modifierName != ''">
				modifier_name,
			</if>
			<if test="modifiedTime != null and modifiedTime != ''">
				modified_time,
			</if>
			<if test="creatorCode != null and creatorCode != ''">
				creator_code,
			</if>
			<if test="creatorName != null and creatorName != ''">
				creator_name,
			</if>
			<if test="createdTime != null and createdTime != ''">
				created_time,
			</if>
            <if test="tenantId != null and tenantId != ''">
                tenant_id,
            </if>
            <if test="onlySelectLast != null">
                only_select_last,
            </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id},
            </if>
			<if test="formClassify != null and formClassify != ''">
				#{formClassify},
			</if>
			<if test="formCode != null and formCode != ''">
				#{formCode},
			</if>
			<if test="formName != null and formName != ''">
				#{formName},
			</if>
			<if test="formType != null and formType != ''">
				#{formType},
			</if>
			<if test="agyCode != null and agyCode != ''">
				#{agyCode},
			</if>
			<if test="fiscal != null and fiscal != ''">
				#{fiscal},
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				#{mofDivCode},
			</if>
			<if test="billFuncCode != null and billFuncCode != ''">
				#{billFuncCode},
			</if>
			<if test="fieldValue != null and fieldValue != ''">
				#{fieldValue},
			</if>
            <if test="fieldLabel!= null and fieldLabel != ''">
                #{fieldLabel},
            </if>
			<if test="fieldTitle != null and fieldTitle != ''">
				#{fieldTitle},
			</if>
			<if test="fieldName != null and fieldName != ''">
				#{fieldName},
			</if>
			<if test="isEdit != null">
				#{isEdit},
			</if>
			<if test="isNull != null">
				#{isNull},
			</if>
			<if test="isEnabled != null">
				#{isEnabled},
			</if>
            <if test="isAddition != null">
                #{isAddition},
            </if>
			<if test="notes != null and notes != ''">
				#{notes},
			</if>
			<if test="remarks != null and remarks != ''">
				#{remarks},
			</if>
			<if test="editorCode != null and editorCode != ''">
				#{editorCode},
			</if>
			<if test="editorName != null and editorName != ''">
				#{editorName},
			</if>
			<if test="fontStyle != null and fontStyle != ''">
				#{fontStyle},
			</if>
			<if test="showType != null and showType != ''">
				#{showType},
			</if>
			<if test="dataTypeCode != null and dataTypeCode != ''">
				#{dataTypeCode},
			</if>
			<if test="dataTypeName != null and dataTypeName != ''">
				#{dataTypeName},
			</if>
            <if test="dataSourceCode != null and dataSourceCode != ''">
                #{dataSourceCode},
            </if>
            <if test="dataSourceName != null and dataSourceName != ''">
                #{dataSourceName},
            </if>
			<if test="dataClassifyCode != null and dataClassifyCode != ''">
				#{dataClassifyCode},
			</if>
			<if test="dataClassifyName != null and dataClassifyName != ''">
				#{dataClassifyName},
			</if>
			<if test="defVal != null and defVal != ''">
				#{defVal},
			</if>
			<if test="fieldControl != null and fieldControl != ''">
				#{fieldControl},
			</if>
			<if test="showRelation != null and showRelation != ''">
				#{showRelation},
			</if>
			<if test="maxLength != null">
				#{maxLength},
			</if>
			<if test="minLength != null">
				#{minLength},
			</if>
			<if test="isExtend != null">
				#{isExtend},
			</if>
            <if test="seq != null">
				#{seq},
			</if>
			<if test="modifier != null and modifier != ''">
				#{modifier},
			</if>
			<if test="modifierName != null and modifierName != ''">
				#{modifierName},
			</if>
			<if test="modifiedTime != null and modifiedTime != ''">
				#{modifiedTime},
			</if>
			<if test="creatorCode != null and creatorCode != ''">
				#{creatorCode},
			</if>
			<if test="creatorName != null and creatorName != ''">
				#{creatorName},
			</if>
			<if test="createdTime != null and createdTime != ''">
				#{createdTime},
			</if>
            <if test="tenantId != null and tenantId != ''">
                #{tenantId},
            </if>
            <if test="onlySelectLast != null">
                #{onlySelectLast},
            </if>
        </trim>
        )
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_bas_form_setting
        <set>
            <trim suffixOverrides=",">
            <if test="formClassify != null and formClassify != ''">
                form_classify = #{formClassify},
            </if>
            <if test="formCode != null and formCode != ''">
                form_code = #{formCode},
            </if>
            <if test="formName != null and formName != ''">
                form_name = #{formName},
            </if>
            <if test="formType != null and formType != ''">
                form_type = #{formType},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code = #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal = #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code = #{mofDivCode},
            </if>
            <if test="billFuncCode != null">
                bill_func_code = #{billFuncCode},
            </if>
            <if test="fieldValue != null and fieldValue != ''">
                field_value = #{fieldValue},
            </if>
            <if test="fieldLabel != null and fieldLabel != ''">
                field_label = #{fieldLabel},
            </if>
            <if test="fieldTitle != null and fieldTitle != ''">
                field_title = #{fieldTitle},
            </if>
            <if test="fieldName != null and fieldName != ''">
                field_name = #{fieldName},
            </if>
            <if test="isEdit != null">
                is_edit = #{isEdit},
            </if>
            <if test="isNull != null">
                is_null = #{isNull},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled},
            </if>
                <if test="isAddition != null">
                    is_addition = #{isAddition},
                </if>
            <if test="notes != null and notes != ''">
                notes = #{notes},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="editorCode != null and editorCode != ''">
                editor_code = #{editorCode},
            </if>
            <if test="editorName != null and editorName != ''">
                editor_name = #{editorName},
            </if>
            <if test="fontStyle != null and fontStyle != ''">
                font_style = #{fontStyle},
            </if>
            <if test="showType != null and showType != ''">
                show_type = #{showType},
            </if>
            <if test="dataTypeCode != null and dataTypeCode != ''">
                data_type_code = #{dataTypeCode},
            </if>
            <if test="dataTypeName != null and dataTypeName != ''">
                data_type_name = #{dataTypeName},
            </if>
            <if test="dataSourceCode != null and dataSourceCode != ''">
                data_source_code = #{dataSourceCode},
            </if>
            <if test="dataSourceName != null and dataSourceName != ''">
                data_source_name = #{dataSourceName},
            </if>
            <if test="dataClassifyCode != null and dataClassifyCode != ''">
                data_classify_code = #{dataClassifyCode},
            </if>
            <if test="dataClassifyName != null and dataClassifyName != ''">
                data_classify_name = #{dataClassifyName},
            </if>
            <if test="defVal != null and defVal != ''">
                def_val = #{defVal},
            </if>
                <if test="defValRule != null and defValRule != ''">
                    def_val_rule = #{defValRule},
                </if>
            <if test="fieldControl != null and fieldControl != ''">
                field_control = #{fieldControl},
            </if>
            <if test="showRelation != null and showRelation != ''">
                show_relation = #{showRelation},
            </if>
            <if test="maxLength != null">
                max_length = #{maxLength},
            </if>
            <if test="minLength != null">
                min_length = #{minLength},
            </if>
            <if test="isExtend != null">
                is_extend = #{isExtend},
            </if>
            <if test="seq != null">
                seq = #{seq},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier},
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name = #{modifierName},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time = #{modifiedTime},
            </if>
            <if test="creatorCode != null and creatorCode != ''">
                creator_code = #{creatorCode},
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name = #{creatorName},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time = #{createdTime},
            </if>
            <if test="onlySelectLast != null">
                only_select_last = #{onlySelectLast},
            </if>
            </trim>
        </set>
        where id = #{id}
    </update>
    <select id="selectByQO" resultMap="PcxBasFormSettingMap">
        select
        <include refid="allColumn" />
        from pcx_bas_form_setting
        where  is_enabled =1
            <if test="formClassify != null and formClassify != ''">
                and form_classify = #{formClassify,jdbcType=VARCHAR}
            </if>
            <if test="formCode != null and formCode != ''">
                and form_code = #{formCode,jdbcType=VARCHAR}
            </if>
            <if test="formType != null and formType != ''">
                and form_type = #{formType,jdbcType=VARCHAR}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode,jdbcType=VARCHAR}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal,jdbcType=VARCHAR}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            </if>
            <if test="billFuncCode != null">
                and bill_func_code &amp; #{billFuncCode,jdbcType=INTEGER} > 0
            </if>
            <if test="isAddition != null ">
                and is_addition = #{isAddition,jdbcType=INTEGER}
            </if>
            <if test="formTypes != null and formTypes.size &gt; 0">
                and form_type in
                <foreach collection="formTypes" index="index" item="fromType" open="(" separator="," close=")">
                    #{fromType,jdbcType=VARCHAR}
                </foreach>
            </if>
        <if test="formCodes != null and formCodes.size &gt; 0">
            and form_code in
            <foreach collection="formCodes" index="index" item="fromCode" open="(" separator="," close=")">
                #{fromCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by seq
    </select>
    <select id="collectExpenseTypeAdditionField"
            resultType="com.pty.pcx.vo.positionblock.BlockPropertyVO">
        SELECT
            fs.field_label as fieldLabel,
            fs.field_value as fieldValue,
            fs.field_title as fieldTitle,
            fs.is_null as isRequired,
            fs.is_edit as isEdit,
            fs.field_name as fieldName,
            fs.show_type as showType,
            fs.notes as notes,
            fs.remarks as remarks,
            fs.editor_code as editorCode,
            fs.data_type_code as dataTypeCode,
            fs.data_classify_code as dataClassifyCode,
            fs.data_source_code as dataSourceCode,
            fs.is_addition as isAddition
        FROM
             pcx_bas_form_setting fs
                where fs.agy_code = #{agyCode}
                AND fs.mof_div_code = #{mofDivCode}
                AND fs.fiscal = #{fiscal}
                AND fs.form_code = #{expenseTypeCode}
                AND fs.form_classify = 'expense'
                AND fs.is_addition = 0
    </select>
</mapper>


