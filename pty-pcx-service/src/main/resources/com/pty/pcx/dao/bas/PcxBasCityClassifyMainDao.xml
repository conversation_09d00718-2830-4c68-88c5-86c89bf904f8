<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bas.PcxBasCityClassifyMainDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.bas.PcxBasCityClassify" id="PcxBasCityClassifyMainMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="classifyCode" column="classify_code" jdbcType="VARCHAR"/>
        <result property="classifyName" column="classify_name" jdbcType="VARCHAR"/>
        <result property="dataCode" column="data_code" jdbcType="VARCHAR"/>
        <result property="dataName" column="data_name" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="isSys" column="is_sys" jdbcType="INTEGER"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="classifyType" column="classify_type" jdbcType="INTEGER"/>
        <result property="expenseTypeCode" column="expense_type_code" jdbcType="VARCHAR"/>
        <result property="peakDateJson" column="peak_date_json" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 全部字段 -->
	<sql id="allColumn">
        id,
        classify_code,
        classify_name,
        is_sys,
        creater,
        create_time,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code
	</sql>

    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxBasCityClassifyMainMap">
        select
          <include refid="allColumn" />
        from pcx_bas_classify_main
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxBasCityClassifyMainMap">
        select
        <include refid="allColumn" />
        from pcx_bas_classify_main
        <where>
            <if test="id != null and id != ''">
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="classifyCode != null and classifyCode != ''">
                and classify_code = #{classifyCode,jdbcType=VARCHAR}
            </if>
            <if test="classifyName != null and classifyName != ''">
                and classify_name = #{classifyName,jdbcType=VARCHAR}
            </if>
            <if test="isSys != null">
                and is_sys = #{isSys,jdbcType=INTEGER}
            </if>
            <if test="creater != null and creater != ''">
                and creater = #{creater,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode,jdbcType=VARCHAR}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal,jdbcType=VARCHAR}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            </if>
            <if test="classifyType != null">
                and classify_type = #{classifyType,jdbcType=INTEGER}
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                and expense_type_code = #{expenseTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 关联主从表 -->
    <select id="selectAllList" resultMap="PcxBasCityClassifyMainMap">
        select distinct
        m.classify_code,
        m.classify_name,
        d.data_code,
        d.data_name,
        d.parent_code,
        is_sys,
        m.agy_code,
        m.fiscal,
        m.mof_div_code,
        m.classify_type,
        m.expense_type_code,
        d.create_time,
        m.peak_date_json
        from pcx_bas_classify_main m,pcx_bas_classify d
        where m.classify_type = d.classify_type and m.expense_type_code = d.expense_type_code
          and m.classify_code = d.classify_code and m.agy_code = d.agy_code and m.fiscal = d.fiscal and m.mof_div_code = d.mof_div_code
            <if test="classifyCode != null and classifyCode != ''">
                and m.classify_code = #{classifyCode,jdbcType=VARCHAR}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and m.agy_code = #{agyCode,jdbcType=VARCHAR}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and m.fiscal = #{fiscal,jdbcType=VARCHAR}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and m.mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            </if>
            <if test="classifyType != null">
                and m.classify_type = #{classifyType,jdbcType=INTEGER}
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                and m.expense_type_code = #{expenseTypeCode,jdbcType=VARCHAR}
            </if>
        order by d.create_time desc
    </select>

    <select id="selectAllCity" resultType="com.pty.pcx.qo.bas.DataQO">
        select area_code as dataCode, area_name as dataName, full_name as fullName, parent_code as parentCode from mad_area where LENGTH(area_code) &lt; 6 and area_name != '市辖区' order by area_code
    </select>

    <select id="selectAllRegion" resultType="com.pty.pcx.qo.bas.DataQO">
        select area_code as dataCode, area_name as dataName, full_name as fullName, parent_code as parentCode from mad_area where LENGTH(area_code) &lt; 7 and area_name != '市辖区' order by area_code
    </select>

    <select id="selectAllProvince" resultType="com.pty.pcx.qo.bas.DataQO">
        select area_code as dataCode, area_name as dataName, full_name as fullName, parent_code as parentCode from mad_area where LENGTH(area_code) &lt; 4 order by area_code
    </select>

    <select id="selectCityClassify" resultMap="PcxBasCityClassifyMainMap">
        select distinct classify_code, classify_name
        from pcx_bas_classify_main
        <where>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode,jdbcType=VARCHAR}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal,jdbcType=VARCHAR}
            </if>
            <if test="classifyType != null">
                and classify_type = #{classifyType,jdbcType=INTEGER}
            </if>
            <if test="expenseTypeCode != null">
                and expense_type_code = #{expenseTypeCode,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_bas_classify_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="classifyCode != null and classifyCode != ''">
				classify_code,
			</if>
			<if test="classifyName != null and classifyName != ''">
				classify_name,
			</if>
			<if test="isSys != null">
				is_sys,
			</if>
			<if test="creater != null and creater != ''">
				creater,
			</if>
			<if test="createTime != null and createTime != ''">
				create_time,
			</if>
			<if test="agyCode != null and agyCode != ''">
				agy_code,
			</if>
			<if test="fiscal != null and fiscal != ''">
				fiscal,
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				mof_div_code,
			</if>
            <if test="classifyType != null">
              classify_type,
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
             expense_type_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="classifyCode != null and classifyCode != ''">
				#{classifyCode,jdbcType=VARCHAR},
			</if>
			<if test="classifyName != null and classifyName != ''">
				#{classifyName,jdbcType=VARCHAR},
			</if>
			<if test="isSys != null">
				#{isSys,jdbcType=INTEGER},
			</if>
			<if test="creater != null and creater != ''">
				#{creater,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null and createTime != ''">
				#{createTime,jdbcType=VARCHAR},
			</if>
			<if test="agyCode != null and agyCode != ''">
				#{agyCode,jdbcType=VARCHAR},
			</if>
			<if test="fiscal != null and fiscal != ''">
				#{fiscal,jdbcType=VARCHAR},
			</if>
			<if test="mofDivCode != null and mofDivCode != ''">
				#{mofDivCode,jdbcType=VARCHAR},
			</if>
            <if test="classifyType != null">
             #{classifyType,jdbcType=INTEGER},
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
              #{expenseTypeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" databaseId="mysql">
        insert into pcx_bas_classify_main(
        id,
        classify_code,
        classify_name,
        is_sys,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        create_time,
        peak_date_json
        )
        values
        <foreach collection="mainList" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.classifyCode,jdbcType=VARCHAR},
            #{item.classifyName,jdbcType=VARCHAR},
            #{item.isSys,jdbcType=INTEGER},
            #{item.agyCode,jdbcType=VARCHAR},
            #{item.fiscal,jdbcType=VARCHAR},
            #{item.mofDivCode,jdbcType=VARCHAR},
            #{item.classifyType,jdbcType=INTEGER},
            #{item.expenseTypeCode,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=VARCHAR},
            #{item.peakDateJson,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="batchInsert" databaseId="oracle">
        insert into pcx_bas_classify_main(
        id,
        classify_code,
        classify_name,
        is_sys,
        agy_code,
        fiscal,
        mof_div_code,
        classify_type,
        expense_type_code,
        create_time,
        peak_date_json
        )
        values
        <foreach collection="mainList" item="item" index="index" separator="union all">
            select
            #{item.id,jdbcType=VARCHAR},
            #{item.classifyCode,jdbcType=VARCHAR},
            #{item.classifyName,jdbcType=VARCHAR},
            #{item.isSys,jdbcType=INTEGER},
            #{item.agyCode,jdbcType=VARCHAR},
            #{item.fiscal,jdbcType=VARCHAR},
            #{item.mofDivCode,jdbcType=VARCHAR},
            #{item.classifyType,jdbcType=INTEGER},
            #{item.expenseTypeCode,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=VARCHAR},
            #{item.peakDateJson,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>


    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_bas_classify_main
        <set>
            <trim suffixOverrides=",">
                <if test="classifyCode != null and classifyCode != ''">
                    classify_code = #{classifyCode,jdbcType=VARCHAR},
                </if>
                <if test="classifyName != null and classifyName != ''">
                    classify_name = #{classifyName,jdbcType=VARCHAR},
                </if>
                <if test="isSys != null">
                    is_sys = #{isSys,jdbcType=INTEGER},
                </if>
                <if test="creater != null and creater != ''">
                    creater = #{creater,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null and createTime != ''">
                    create_time = #{createTime,jdbcType=VARCHAR},
                </if>
                <if test="agyCode != null and agyCode != ''">
                    agy_code = #{agyCode,jdbcType=VARCHAR},
                </if>
                <if test="fiscal != null and fiscal != ''">
                    fiscal = #{fiscal,jdbcType=VARCHAR},
                </if>
                <if test="mofDivCode != null and mofDivCode != ''">
                    mof_div_code = #{mofDivCode,jdbcType=VARCHAR},
                </if>
                <if test="classifyType != null">
                    classify_type = #{classifyType,jdbcType=INTEGER},
                </if>
                <if test="expenseTypeCode != null and expenseTypeCode != ''">
                    expense_type_code = #{expenseTypeCode,jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete from pcx_bas_classify_main
        <where>
            <if test="id != null and id != ''">
                and id = #{id,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>

    <delete id="deleteClassify">
        delete from pcx_bas_classify_main
        <where>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode,jdbcType=VARCHAR}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal,jdbcType=VARCHAR}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
            </if>
            <if test="classifyType != null">
                and classify_type = #{classifyType,jdbcType=INTEGER}
            </if>
            <if test="expenseTypeCode != null and expenseTypeCode != ''">
                and expense_type_code = #{expenseTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>

</mapper>


