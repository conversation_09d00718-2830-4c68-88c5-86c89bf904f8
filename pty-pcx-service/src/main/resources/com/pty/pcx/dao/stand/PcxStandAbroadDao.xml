<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.stand.PcxStandAbroadDao">
    <!-- 结果集 -->
    <resultMap type="com.pty.pcx.entity.stand.PcxStandAbroad" id="PcxStandAbroadMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="countryCode" column="country_code" jdbcType="VARCHAR"/>
        <result property="countryName" column="country_name" jdbcType="VARCHAR"/>
        <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
        <result property="cityName" column="city_name" jdbcType="VARCHAR"/>
        <result property="stayAmt" column="stay_amt" jdbcType="NUMERIC"/>
        <result property="foodAmt" column="food_amt" jdbcType="NUMERIC"/>
        <result property="otherAmt" column="other_amt" jdbcType="NUMERIC"/>
        <result property="currencyCode" column="currency_code" jdbcType="VARCHAR"/>
        <result property="currencyName" column="currency_name" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="INTEGER"/>
        <result property="continentCode" column="continent_code" jdbcType="VARCHAR"/>
        <result property="continentName" column="continent_name" jdbcType="VARCHAR"/>
        <result property="agyCode" column="agy_code" jdbcType="VARCHAR"/>
        <result property="fiscal" column="fiscal" jdbcType="VARCHAR"/>
        <result property="mofDivCode" column="mof_div_code" jdbcType="VARCHAR"/>
        <result property="isEnabled" column="is_enabled" jdbcType="INTEGER"/>
        <result property="effectiveDate" column="effective_date" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 全部字段 -->
	<sql id="allColumn">
        id, 
        country_code, 
        country_name, 
        city_code, 
        city_name, 
        stay_amt, 
        food_amt, 
        other_amt, 
        currency_code, 
        currency_name, 
        seq, 
        continent_code, 
        continent_name, 
        agy_code, 
        fiscal, 
        mof_div_code, 
        is_enabled,
        effective_date
	</sql>
    
    <!-- 查询单个 -->
    <select id="selectById" resultMap="PcxStandAbroadMap">
        select
          <include refid="allColumn" />
        from pcx_stand_abroad
        where id = #{id}
    </select>

    <!-- 通过实体作为筛选条件查询 -->
    <select id="selectList" resultMap="PcxStandAbroadMap">
        select
        <include refid="allColumn" />
        from pcx_stand_abroad
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="countryCode != null and countryCode != ''">
                and country_code = #{countryCode}
            </if>
            <if test="countryName != null and countryName != ''">
                and country_name = #{countryName}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
            <if test="cityName != null and cityName != ''">
                and city_name = #{cityName}
            </if>
            <if test="stayAmt != null">
                and stay_amt = #{stayAmt}
            </if>
            <if test="foodAmt != null">
                and food_amt = #{foodAmt}
            </if>
            <if test="otherAmt != null">
                and other_amt = #{otherAmt}
            </if>
            <if test="currencyCode != null and currencyCode != ''">
                and currency_code = #{currencyCode}
            </if>
            <if test="currencyName != null and currencyName != ''">
                and currency_name = #{currencyName}
            </if>
            <if test="seq != null">
                and seq = #{seq}
            </if>
            <if test="continentCode != null and continentCode != ''">
                and continent_code = #{continentCode}
            </if>
            <if test="continentName != null and continentName != ''">
                and continent_name = #{continentName}
            </if>
            <if test="agyCode != null and agyCode != ''">
                and agy_code = #{agyCode}
            </if>
            <if test="fiscal != null and fiscal != ''">
                and fiscal = #{fiscal}
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                and mof_div_code = #{mofDivCode}
            </if>
            <if test="isEnabled != null">
                and is_enabled = #{isEnabled}
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                and effective_date = #{effectiveDate}
            </if>
        </where>
    </select>
    
    <!-- 动态插入非空字段 -->
    <insert id="insertSelective">
        insert into pcx_stand_abroad (
        <trim suffixOverrides=",">
            id,
            <if test="countryCode != null and countryCode != ''">
                country_code,
            </if>
            <if test="countryName != null and countryName != ''">
                country_name,
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code,
            </if>
            <if test="cityName != null and cityName != ''">
                city_name,
            </if>
            <if test="stayAmt != null">
                stay_amt,
            </if>
            <if test="foodAmt != null">
                food_amt,
            </if>
            <if test="otherAmt != null">
                other_amt,
            </if>
            <if test="currencyCode != null and currencyCode != ''">
                currency_code,
            </if>
            <if test="currencyName != null and currencyName != ''">
                currency_name,
            </if>
            <if test="seq != null">
                seq,
            </if>
            <if test="continentCode != null and continentCode != ''">
                continent_code,
            </if>
            <if test="continentName != null and continentName != ''">
                continent_name,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="isEnabled != null">
                is_enabled,
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                effective_date,
            </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            #{id},
            <if test="countryCode != null and countryCode != ''">
                #{countryCode},
            </if>
            <if test="countryName != null and countryName != ''">
                #{countryName},
            </if>
            <if test="cityCode != null and cityCode != ''">
                #{cityCode},
            </if>
            <if test="cityName != null and cityName != ''">
                #{cityName},
            </if>
            <if test="stayAmt != null">
                #{stayAmt},
            </if>
            <if test="foodAmt != null">
                #{foodAmt},
            </if>
            <if test="otherAmt != null">
                #{otherAmt},
            </if>
            <if test="currencyCode != null and currencyCode != ''">
                #{currencyCode},
            </if>
            <if test="currencyName != null and currencyName != ''">
                #{currencyName},
            </if>
            <if test="seq != null">
                #{seq},
            </if>
            <if test="continentCode != null and continentCode != ''">
                #{continentCode},
            </if>
            <if test="continentName != null and continentName != ''">
                #{continentName},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode},
            </if>
            <if test="isEnabled != null">
                #{isEnabled},
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                #{effectiveDate},
            </if>
        </trim>
        )
    </insert>

    <!-- 通过主键修改数据 -->
    <update id="update">
        update pcx_stand_abroad
        <set>
            <if test="countryCode != null and countryCode != ''">
                country_code = #{countryCode},
            </if>
            <if test="countryName != null and countryName != ''">
                country_name = #{countryName},
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code = #{cityCode},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName},
            </if>
            <if test="stayAmt != null">
                stay_amt = #{stayAmt},
            </if>
            <if test="foodAmt != null">
                food_amt = #{foodAmt},
            </if>
            <if test="otherAmt != null">
                other_amt = #{otherAmt},
            </if>
            <if test="currencyCode != null and currencyCode != ''">
                currency_code = #{currencyCode},
            </if>
            <if test="currencyName != null and currencyName != ''">
                currency_name = #{currencyName},
            </if>
            <if test="seq != null">
                seq = #{seq},
            </if>
            <if test="continentCode != null and continentCode != ''">
                continent_code = #{continentCode},
            </if>
            <if test="continentName != null and continentName != ''">
                continent_name = #{continentName},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code = #{agyCode},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal = #{fiscal},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code = #{mofDivCode},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled},
            </if>
            <if test="effectiveDate != null and effectiveDate != ''">
                effective_date = #{effectiveDate},
            </if>
        </set>
        where
            <choose>
                <when test="ids != null and ids.size() > 0">
                    id in
                    <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="id != null and id != ''">
                    id = #{id}
                </when>
                <otherwise>
                    1 = 0
                </otherwise>
            </choose>
    </update>

    <!-- 通过主键删除 -->
    <delete id="deleteById">
        delete from pcx_stand_abroad where id = #{id}
    </delete>
    <delete id="deleteForRebuild" parameterType="com.pty.pcx.entity.stand.PcxStandAbroad">
        delete from pcx_stand_abroad
        where
            fiscal = #{fiscal}
            and mof_div_code = #{mofDivCode}
            and agy_code = #{agyCode}
            and country_code = #{countryCode}
            and city_code in
            <foreach collection="cityCodes" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
    </delete>

    <select id="selectByCityCodes" parameterType="com.pty.pcx.qo.bill.PcxBillAbroadTripQO" resultMap="PcxStandAbroadMap">
        select
        <include refid="allColumn" />
        from pcx_stand_abroad
        where
        fiscal = #{fiscal,jdbcType=VARCHAR}
        and mof_div_code = #{mofDivCode,jdbcType=VARCHAR}
        and agy_code = #{agyCode,jdbcType=VARCHAR}
        and city_code in
        <foreach collection="cityCodes" item="cityCode" open="(" close=")" separator=",">
            #{cityCode,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>
