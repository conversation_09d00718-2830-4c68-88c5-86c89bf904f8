<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.bill.PcxBillExpAbroadDao">
    <sql id="allColumn">
        id
        ,                                               bill_id,                                          expense_code,                                     expense_name,
    department_code,                                  department_name,                                  input_amt,                                        check_amt,
    abroad_team_name,                                 abroad_team_type,                                 abroad_team_type_name,                            is_plan,
    plan_code,                                        plan_name,                                        abroad_start_date,                                abroad_finish_date,
    pre_start_month,                                  pre_finish_month,                                 pre_quarter,                                      remarks,
    abroad_job,                                       abroad_visit_days,                                abroad_people_num,                                abroad_approval_num,
    need_amt,                                         abroad_team_unit,                                 abroad_visit_country,                             abroad_visit_route,
    abroad_achievement,                               abroad_affairs_num,                               abroad_team_personnel,                            abroad_other_units,
    abroad_leader_name,                               abroad_leader_level,                              agy_code,                                         fiscal,
    mof_div_code,                                     tenant_id,                                        field01,                                          field02,
    field03,                                          field04,                                          field05,                                          field06,
    field07,                                          field08,                                          field09,                                          field10,
    acitem01_code,                                    acitem01_name,                                    acitem02_code,                                    acitem02_name,
    acitem03_code,                                    acitem03_name,                                    acitem04_code,
    acitem04_name,                                    acitem05_code,                                    acitem05_name,                                    acitem06_code,
    acitem06_name,                                    acitem07_code,                                    acitem07_name,                                    acitem08_code,
    acitem08_name,                                    acitem09_code,                                    acitem09_name,                                    acitem10_code,
    acitem10_name,                                    audit_agy,                                        audit_reason,                                     target_necessity,
    abroad_plan,                                      time_country_check,                               route_check,                                      people_num_check,
    remark,                                           list_year_budget,                                 before_spend,                                     creator,
    create_time,                                      creator_name,                                     modifier,                                         modifier_name,
    modified_time
    </sql>

    <sql id="allColumnAlias">
        id
        as id,                                         bill_id as billId,                                expense_code as expenseCode,                      expense_name as expenseName,
    department_code as departmentCode,                department_name as departmentName,                input_amt as inputAmt,                            check_amt as checkAmt,
    abroad_team_name as abroadTeamName,               abroad_team_type as abroadTeamType,               abroad_team_type_name as abroadTeamTypeName,      is_plan as isPlan,
    plan_code as planCode,                            plan_name as planName,                            abroad_start_date as abroadStartDate,             abroad_finish_date as abroadFinishDate,
    pre_start_month as preStartMonth,                 pre_finish_month as preFinishMonth,               pre_quarter as preQuarter,                        remarks as remarks,
    abroad_job as abroadJob,                          abroad_visit_days as abroadVisitDays,             abroad_people_num as abroadPeopleNum,             abroad_approval_num as abroadApprovalNum,
    need_amt as needAmt,                              abroad_team_unit as abroadTeamUnit,               abroad_visit_country as abroadVisitCountry,       abroad_visit_route as abroadVisitRoute,
    abroad_achievement as abroadAchievement,          abroad_affairs_num as abroadAffairsNum,           abroad_team_personnel as abroadTeamPersonnel,     abroad_other_units as abroadOtherUnits,
    abroad_leader_name as abroadLeaderName,           abroad_leader_level as abroadLeaderLevel,         agy_code as agyCode,                              fiscal as fiscal,
    mof_div_code as mofDivCode,                       tenant_id as tenantId,                            field01 as field01,                               field02 as field02,
    field03 as field03,                               field04 as field04,                               field05 as field05,                               field06 as field06,
    field07 as field07,                               field08 as field08,                               field09 as field09,                               field10 as field10,
    acitem01_code as acitem01Code,                    acitem01_name as acitem01Name,                    acitem02_code as acitem02Code,                    acitem02_name as acitem02Name,
    acitem03_code as acitem03Code,                    acitem03_name as acitem03Name,                    acitem04_code as acitem04Code,                    acitem04_name as acitem04Name,
    acitem05_code as acitem05Code,                    acitem05_name as acitem05Name,                    acitem06_code as acitem06Code,                    acitem06_name as acitem06Name,
    acitem07_code as acitem07Code,                    acitem07_name as acitem07Name,                    acitem08_code as acitem08Code,                    acitem08_name as acitem08Name,
    acitem09_code as acitem09Code,                    acitem09_name as acitem09Name,                    acitem10_code as acitem10Code,                    acitem10_name as acitem10Name,
    audit_agy as auditAgy,                            audit_reason as auditReason,                      target_necessity as targetNecessity,              abroad_plan as abroadPlan,
    time_country_check as timeCountryCheck,           route_check as routeCheck,                        people_num_check as peopleNumCheck,               remark as remark,
    list_year_budget as listYearBudget,               before_spend as beforeSpend,                      creator as creator,                               create_time as createTime,
    creator_name as creatorName,                      modifier as modifier,                             modifier_name as modifierName,                    modified_time as modifiedTime
    </sql>

    <sql id="allColumnValue">
        #{id,jdbcType=VARCHAR}
        ,
        #{billId,jdbcType=VARCHAR},
        #{expenseCode,jdbcType=VARCHAR},
        #{expenseName,jdbcType=VARCHAR},
        #{departmentCode,jdbcType=VARCHAR},
        #{departmentName,jdbcType=VARCHAR},
        #{inputAmt,jdbcType=DECIMAL},
        #{checkAmt,jdbcType=DECIMAL},
        #{abroadTeamName,jdbcType=VARCHAR},
        #{abroadTeamType,jdbcType=VARCHAR},
        #{abroadTeamTypeName,jdbcType=VARCHAR},
        #{isPlan,jdbcType=INTEGER},
        #{planCode,jdbcType=VARCHAR},
        #{planName,jdbcType=VARCHAR},
        #{abroadStartDate,jdbcType=VARCHAR},
        #{abroadFinishDate,jdbcType=VARCHAR},
        #{preStartMonth,jdbcType=VARCHAR},
        #{preFinishMonth,jdbcType=VARCHAR},
        #{preQuarter,jdbcType=VARCHAR},
        #{remarks,jdbcType=VARCHAR},
        #{abroadJob,jdbcType=VARCHAR},
        #{abroadVisitDays,jdbcType=INTEGER},
        #{abroadPeopleNum,jdbcType=INTEGER},
        #{abroadApprovalNum,jdbcType=VARCHAR},
        #{needAmt,jdbcType=DECIMAL},
        #{abroadTeamUnit,jdbcType=VARCHAR},
        #{abroadVisitCountry,jdbcType=VARCHAR},
        #{abroadVisitRoute,jdbcType=VARCHAR},
        #{abroadAchievement,jdbcType=VARCHAR},
        #{abroadAffairsNum,jdbcType=INTEGER},
        #{abroadTeamPersonnel,jdbcType=VARCHAR},
        #{abroadOtherUnits,jdbcType=VARCHAR},
        #{abroadLeaderName,jdbcType=VARCHAR},
        #{abroadLeaderLevel,jdbcType=VARCHAR},
        #{agyCode,jdbcType=VARCHAR},
        #{fiscal,jdbcType=VARCHAR},
        #{mofDivCode,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{field01,jdbcType=VARCHAR},
        #{field02,jdbcType=VARCHAR},
        #{field03,jdbcType=VARCHAR},
        #{field04,jdbcType=VARCHAR},
        #{field05,jdbcType=VARCHAR},
        #{field06,jdbcType=VARCHAR},
        #{field07,jdbcType=VARCHAR},
        #{field08,jdbcType=VARCHAR},
        #{field09,jdbcType=VARCHAR},
        #{field10,jdbcType=VARCHAR},
        #{acitem01Code,jdbcType=VARCHAR},
        #{acitem01Name,jdbcType=VARCHAR},
        #{acitem02Code,jdbcType=VARCHAR},
        #{acitem02Name,jdbcType=VARCHAR},
        #{acitem03Code,jdbcType=VARCHAR},
        #{acitem03Name,jdbcType=VARCHAR},
        #{acitem04Code,jdbcType=VARCHAR},
        #{acitem04Name,jdbcType=VARCHAR},
        #{acitem05Code,jdbcType=VARCHAR},
        #{acitem05Name,jdbcType=VARCHAR},
        #{acitem06Code,jdbcType=VARCHAR},
        #{acitem06Name,jdbcType=VARCHAR},
        #{acitem07Code,jdbcType=VARCHAR},
        #{acitem07Name,jdbcType=VARCHAR},
        #{acitem08Code,jdbcType=VARCHAR},
        #{acitem08Name,jdbcType=VARCHAR},
        #{acitem09Code,jdbcType=VARCHAR},
        #{acitem09Name,jdbcType=VARCHAR},
        #{acitem10Code,jdbcType=VARCHAR},
        #{acitem10Name,jdbcType=VARCHAR},
        #{auditAgy,jdbcType=VARCHAR},
        #{auditReason,jdbcType=VARCHAR},
        #{targetNecessity,jdbcType=VARCHAR},
        #{abroadPlan,jdbcType=VARCHAR},
        #{timeCountryCheck,jdbcType=VARCHAR},
        #{routeCheck,jdbcType=VARCHAR},
        #{peopleNumCheck,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{listYearBudget,jdbcType=VARCHAR},
        #{beforeSpend,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=VARCHAR},
        #{creatorName,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},
        #{modifierName,jdbcType=VARCHAR},
        #{modifiedTime,jdbcType=VARCHAR}
    </sql>

    <sql id="allColumnSet">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id=#{id,jdbcType=VARCHAR},
            </if>
            <if test="billId != null and billId != ''">
                bill_id=#{billId,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code=#{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name=#{expenseName,jdbcType=VARCHAR},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                department_code=#{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="departmentName != null and departmentName != ''">
                department_name=#{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="inputAmt != null">
                input_amt=#{inputAmt,jdbcType=DECIMAL},
            </if>
            <if test="checkAmt != null">
                check_amt=#{checkAmt,jdbcType=DECIMAL},
            </if>
            <if test="abroadTeamName != null and abroadTeamName != ''">
                abroad_team_name=#{abroadTeamName,jdbcType=VARCHAR},
            </if>
            <if test="abroadTeamType != null and abroadTeamType != ''">
                abroad_team_type=#{abroadTeamType,jdbcType=VARCHAR},
            </if>
            <if test="abroadTeamTypeName != null and abroadTeamTypeName != ''">
                abroad_team_type_name=#{abroadTeamTypeName,jdbcType=VARCHAR},
            </if>
            <if test="isPlan != null">
                is_plan=#{isPlan,jdbcType=INTEGER},
            </if>
            <if test="planCode != null and planCode != ''">
                plan_code=#{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null and planName != ''">
                plan_name=#{planName,jdbcType=VARCHAR},
            </if>
            <if test="abroadStartDate != null and abroadStartDate != ''">
                abroad_start_date=#{abroadStartDate,jdbcType=VARCHAR},
            </if>
            <if test="abroadFinishDate != null and abroadFinishDate != ''">
                abroad_finish_date=#{abroadFinishDate,jdbcType=VARCHAR},
            </if>
            <if test="preStartMonth != null and preStartMonth != ''">
                pre_start_month=#{preStartMonth,jdbcType=VARCHAR},
            </if>
            <if test="preFinishMonth != null and preFinishMonth != ''">
                pre_finish_month=#{preFinishMonth,jdbcType=VARCHAR},
            </if>
            <if test="preQuarter != null and preQuarter != ''">
                pre_quarter=#{preQuarter,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks=#{remarks,jdbcType=VARCHAR},
            </if>
            <if test="abroadJob != null and abroadJob != ''">
                abroad_job=#{abroadJob,jdbcType=VARCHAR},
            </if>
            <if test="abroadVisitDays != null">
                abroad_visit_days=#{abroadVisitDays,jdbcType=INTEGER},
            </if>
            <if test="abroadPeopleNum != null">
                abroad_people_num=#{abroadPeopleNum,jdbcType=INTEGER},
            </if>
            <if test="abroadApprovalNum != null and abroadApprovalNum != ''">
                abroad_approval_num=#{abroadApprovalNum,jdbcType=VARCHAR},
            </if>
            <if test="needAmt != null">
                need_amt=#{needAmt,jdbcType=DECIMAL},
            </if>
            <if test="abroadTeamUnit != null and abroadTeamUnit != ''">
                abroad_team_unit=#{abroadTeamUnit,jdbcType=VARCHAR},
            </if>
            <if test="abroadVisitCountry != null and abroadVisitCountry != ''">
                abroad_visit_country=#{abroadVisitCountry,jdbcType=VARCHAR},
            </if>
            <if test="abroadVisitRoute != null and abroadVisitRoute != ''">
                abroad_visit_route=#{abroadVisitRoute,jdbcType=VARCHAR},
            </if>
            <if test="abroadAchievement != null and abroadAchievement != ''">
                abroad_achievement=#{abroadAchievement,jdbcType=VARCHAR},
            </if>
            <if test="abroadAffairsNum != null">
                abroad_affairs_num=#{abroadAffairsNum,jdbcType=INTEGER},
            </if>
            <if test="abroadTeamPersonnel != null and abroadTeamPersonnel != ''">
                abroad_team_personnel=#{abroadTeamPersonnel,jdbcType=VARCHAR},
            </if>
            <if test="abroadOtherUnits != null and abroadOtherUnits != ''">
                abroad_other_units=#{abroadOtherUnits,jdbcType=VARCHAR},
            </if>
            <if test="abroadLeaderName != null and abroadLeaderName != ''">
                abroad_leader_name=#{abroadLeaderName,jdbcType=VARCHAR},
            </if>
            <if test="abroadLeaderLevel != null and abroadLeaderLevel != ''">
                abroad_leader_level=#{abroadLeaderLevel,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code=#{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal=#{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code=#{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null and tenantId != ''">
                tenant_id=#{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="field01 != null and field01 != ''">
                field01=#{field01,jdbcType=VARCHAR},
            </if>
            <if test="field02 != null and field02 != ''">
                field02=#{field02,jdbcType=VARCHAR},
            </if>
            <if test="field03 != null and field03 != ''">
                field03=#{field03,jdbcType=VARCHAR},
            </if>
            <if test="field04 != null and field04 != ''">
                field04=#{field04,jdbcType=VARCHAR},
            </if>
            <if test="field05 != null and field05 != ''">
                field05=#{field05,jdbcType=VARCHAR},
            </if>
            <if test="field06 != null and field06 != ''">
                field06=#{field06,jdbcType=VARCHAR},
            </if>
            <if test="field07 != null and field07 != ''">
                field07=#{field07,jdbcType=VARCHAR},
            </if>
            <if test="field08 != null and field08 != ''">
                field08=#{field08,jdbcType=VARCHAR},
            </if>
            <if test="field09 != null and field09 != ''">
                field09=#{field09,jdbcType=VARCHAR},
            </if>
            <if test="field10 != null and field10 != ''">
                field10=#{field10,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                acitem01_code=#{acitem01Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                acitem01_name=#{acitem01Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                acitem02_code=#{acitem02Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                acitem02_name=#{acitem02Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                acitem03_code=#{acitem03Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                acitem03_name=#{acitem03Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                acitem04_code=#{acitem04Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                acitem04_name=#{acitem04Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                acitem05_code=#{acitem05Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                acitem05_name=#{acitem05Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                acitem06_code=#{acitem06Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                acitem06_name=#{acitem06Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                acitem07_code=#{acitem07Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                acitem07_name=#{acitem07Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                acitem08_code=#{acitem08Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                acitem08_name=#{acitem08Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                acitem09_code=#{acitem09Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                acitem09_name=#{acitem09Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Code != null and acitem10Code != ''">
                acitem10_code=#{acitem10Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                acitem10_name=#{acitem10Name,jdbcType=VARCHAR},
            </if>
            <if test="auditAgy != null and auditAgy != ''">
                audit_agy=#{auditAgy,jdbcType=VARCHAR},
            </if>
            <if test="auditReason != null and auditReason != ''">
                audit_reason=#{auditReason,jdbcType=VARCHAR},
            </if>
            <if test="targetNecessity != null and targetNecessity != ''">
                target_necessity=#{targetNecessity,jdbcType=VARCHAR},
            </if>
            <if test="abroadPlan != null and abroadPlan != ''">
                abroad_plan=#{abroadPlan,jdbcType=VARCHAR},
            </if>
            <if test="timeCountryCheck != null and timeCountryCheck != ''">
                time_country_check=#{timeCountryCheck,jdbcType=VARCHAR},
            </if>
            <if test="routeCheck != null and routeCheck != ''">
                route_check=#{routeCheck,jdbcType=VARCHAR},
            </if>
            <if test="peopleNumCheck != null and peopleNumCheck != ''">
                people_num_check=#{peopleNumCheck,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark=#{remark,jdbcType=VARCHAR},
            </if>
            <if test="listYearBudget != null and listYearBudget != ''">
                list_year_budget=#{listYearBudget,jdbcType=VARCHAR},
            </if>
            <if test="beforeSpend != null and beforeSpend != ''">
                before_spend=#{beforeSpend,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                creator=#{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time=#{createTime,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name=#{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier=#{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name=#{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time=#{modifiedTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="columnSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="billId != null and billId != ''">
                bill_id,
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                expense_code,
            </if>
            <if test="expenseName != null and expenseName != ''">
                expense_name,
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                department_code,
            </if>
            <if test="departmentName != null and departmentName != ''">
                department_name,
            </if>
            <if test="inputAmt != null">
                input_amt,
            </if>
            <if test="checkAmt != null">
                check_amt,
            </if>
            <if test="abroadTeamName != null and abroadTeamName != ''">
                abroad_team_name,
            </if>
            <if test="abroadTeamType != null and abroadTeamType != ''">
                abroad_team_type,
            </if>
            <if test="abroadTeamTypeName != null and abroadTeamTypeName != ''">
                abroad_team_type_name,
            </if>
            <if test="isPlan != null">
                is_plan,
            </if>
            <if test="planCode != null and planCode != ''">
                plan_code,
            </if>
            <if test="planName != null and planName != ''">
                plan_name,
            </if>
            <if test="abroadStartDate != null and abroadStartDate != ''">
                abroad_start_date,
            </if>
            <if test="abroadFinishDate != null and abroadFinishDate != ''">
                abroad_finish_date,
            </if>
            <if test="preStartMonth != null and preStartMonth != ''">
                pre_start_month,
            </if>
            <if test="preFinishMonth != null and preFinishMonth != ''">
                pre_finish_month,
            </if>
            <if test="preQuarter != null and preQuarter != ''">
                pre_quarter,
            </if>
            <if test="remarks != null and remarks != ''">
                remarks,
            </if>
            <if test="abroadJob != null and abroadJob != ''">
                abroad_job,
            </if>
            <if test="abroadVisitDays != null">
                abroad_visit_days,
            </if>
            <if test="abroadPeopleNum != null">
                abroad_people_num,
            </if>
            <if test="abroadApprovalNum != null and abroadApprovalNum != ''">
                abroad_approval_num,
            </if>
            <if test="needAmt != null">
                need_amt,
            </if>
            <if test="abroadTeamUnit != null and abroadTeamUnit != ''">
                abroad_team_unit,
            </if>
            <if test="abroadVisitCountry != null and abroadVisitCountry != ''">
                abroad_visit_country,
            </if>
            <if test="abroadVisitRoute != null and abroadVisitRoute != ''">
                abroad_visit_route,
            </if>
            <if test="abroadAchievement != null and abroadAchievement != ''">
                abroad_achievement,
            </if>
            <if test="abroadAffairsNum != null">
                abroad_affairs_num,
            </if>
            <if test="abroadTeamPersonnel != null and abroadTeamPersonnel != ''">
                abroad_team_personnel,
            </if>
            <if test="abroadOtherUnits != null and abroadOtherUnits != ''">
                abroad_other_units,
            </if>
            <if test="abroadLeaderName != null and abroadLeaderName != ''">
                abroad_leader_name,
            </if>
            <if test="abroadLeaderLevel != null and abroadLeaderLevel != ''">
                abroad_leader_level,
            </if>
            <if test="agyCode != null and agyCode != ''">
                agy_code,
            </if>
            <if test="fiscal != null and fiscal != ''">
                fiscal,
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                mof_div_code,
            </if>
            <if test="tenantId != null and tenantId != ''">
                tenant_id,
            </if>
            <if test="field01 != null and field01 != ''">
                field01,
            </if>
            <if test="field02 != null and field02 != ''">
                field02,
            </if>
            <if test="field03 != null and field03 != ''">
                field03,
            </if>
            <if test="field04 != null and field04 != ''">
                field04,
            </if>
            <if test="field05 != null and field05 != ''">
                field05,
            </if>
            <if test="field06 != null and field06 != ''">
                field06,
            </if>
            <if test="field07 != null and field07 != ''">
                field07,
            </if>
            <if test="field08 != null and field08 != ''">
                field08,
            </if>
            <if test="field09 != null and field09 != ''">
                field09,
            </if>
            <if test="field10 != null and field10 != ''">
                field10,
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                acitem01_code,
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                acitem01_name,
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                acitem02_code,
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                acitem02_name,
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                acitem03_code,
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                acitem03_name,
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                acitem04_code,
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                acitem04_name,
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                acitem05_code,
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                acitem05_name,
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                acitem06_code,
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                acitem06_name,
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                acitem07_code,
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                acitem07_name,
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                acitem08_code,
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                acitem08_name,
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                acitem09_code,
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                acitem09_name,
            </if>
            <if test="acitem10Code != null and acitem10Code != ''">
                acitem10_code,
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                acitem10_name,
            </if>
            <if test="auditAgy != null and auditAgy != ''">
                audit_agy,
            </if>
            <if test="auditReason != null and auditReason != ''">
                audit_reason,
            </if>
            <if test="targetNecessity != null and targetNecessity != ''">
                target_necessity,
            </if>
            <if test="abroadPlan != null and abroadPlan != ''">
                abroad_plan,
            </if>
            <if test="timeCountryCheck != null and timeCountryCheck != ''">
                time_country_check,
            </if>
            <if test="routeCheck != null and routeCheck != ''">
                route_check,
            </if>
            <if test="peopleNumCheck != null and peopleNumCheck != ''">
                people_num_check,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="listYearBudget != null and listYearBudget != ''">
                list_year_budget,
            </if>
            <if test="beforeSpend != null and beforeSpend != ''">
                before_spend,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="createTime != null and createTime != ''">
                create_time,
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="modifierName != null and modifierName != ''">
                modifier_name,
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                modified_time,
            </if>
        </trim>
    </sql>

    <sql id="columnValueSelective">
        <trim suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="billId != null and billId != ''">
                #{billId,jdbcType=VARCHAR},
            </if>
            <if test="expenseCode != null and expenseCode != ''">
                #{expenseCode,jdbcType=VARCHAR},
            </if>
            <if test="expenseName != null and expenseName != ''">
                #{expenseName,jdbcType=VARCHAR},
            </if>
            <if test="departmentCode != null and departmentCode != ''">
                #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="departmentName != null and departmentName != ''">
                #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="inputAmt != null">
                #{inputAmt,jdbcType=DECIMAL},
            </if>
            <if test="checkAmt != null">
                #{checkAmt,jdbcType=DECIMAL},
            </if>
            <if test="abroadTeamName != null and abroadTeamName != ''">
                #{abroadTeamName,jdbcType=VARCHAR},
            </if>
            <if test="abroadTeamType != null and abroadTeamType != ''">
                #{abroadTeamType,jdbcType=VARCHAR},
            </if>
            <if test="abroadTeamTypeName != null and abroadTeamTypeName != ''">
                #{abroadTeamTypeName,jdbcType=VARCHAR},
            </if>
            <if test="isPlan != null">
                #{isPlan,jdbcType=INTEGER},
            </if>
            <if test="planCode != null and planCode != ''">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null and planName != ''">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="abroadStartDate != null and abroadStartDate != ''">
                #{abroadStartDate,jdbcType=VARCHAR},
            </if>
            <if test="abroadFinishDate != null and abroadFinishDate != ''">
                #{abroadFinishDate,jdbcType=VARCHAR},
            </if>
            <if test="preStartMonth != null and preStartMonth != ''">
                #{preStartMonth,jdbcType=VARCHAR},
            </if>
            <if test="preFinishMonth != null and preFinishMonth != ''">
                #{preFinishMonth,jdbcType=VARCHAR},
            </if>
            <if test="preQuarter != null and preQuarter != ''">
                #{preQuarter,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="abroadJob != null and abroadJob != ''">
                #{abroadJob,jdbcType=VARCHAR},
            </if>
            <if test="abroadVisitDays != null">
                #{abroadVisitDays,jdbcType=INTEGER},
            </if>
            <if test="abroadPeopleNum != null">
                #{abroadPeopleNum,jdbcType=INTEGER},
            </if>
            <if test="abroadApprovalNum != null and abroadApprovalNum != ''">
                #{abroadApprovalNum,jdbcType=VARCHAR},
            </if>
            <if test="needAmt != null">
                #{needAmt,jdbcType=DECIMAL},
            </if>
            <if test="abroadTeamUnit != null and abroadTeamUnit != ''">
                #{abroadTeamUnit,jdbcType=VARCHAR},
            </if>
            <if test="abroadVisitCountry != null and abroadVisitCountry != ''">
                #{abroadVisitCountry,jdbcType=VARCHAR},
            </if>
            <if test="abroadVisitRoute != null and abroadVisitRoute != ''">
                #{abroadVisitRoute,jdbcType=VARCHAR},
            </if>
            <if test="abroadAchievement != null and abroadAchievement != ''">
                #{abroadAchievement,jdbcType=VARCHAR},
            </if>
            <if test="abroadAffairsNum != null">
                #{abroadAffairsNum,jdbcType=INTEGER},
            </if>
            <if test="abroadTeamPersonnel != null and abroadTeamPersonnel != ''">
                #{abroadTeamPersonnel,jdbcType=VARCHAR},
            </if>
            <if test="abroadOtherUnits != null and abroadOtherUnits != ''">
                #{abroadOtherUnits,jdbcType=VARCHAR},
            </if>
            <if test="abroadLeaderName != null and abroadLeaderName != ''">
                #{abroadLeaderName,jdbcType=VARCHAR},
            </if>
            <if test="abroadLeaderLevel != null and abroadLeaderLevel != ''">
                #{abroadLeaderLevel,jdbcType=VARCHAR},
            </if>
            <if test="agyCode != null and agyCode != ''">
                #{agyCode,jdbcType=VARCHAR},
            </if>
            <if test="fiscal != null and fiscal != ''">
                #{fiscal,jdbcType=VARCHAR},
            </if>
            <if test="mofDivCode != null and mofDivCode != ''">
                #{mofDivCode,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null and tenantId != ''">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="field01 != null and field01 != ''">
                #{field01,jdbcType=VARCHAR},
            </if>
            <if test="field02 != null and field02 != ''">
                #{field02,jdbcType=VARCHAR},
            </if>
            <if test="field03 != null and field03 != ''">
                #{field03,jdbcType=VARCHAR},
            </if>
            <if test="field04 != null and field04 != ''">
                #{field04,jdbcType=VARCHAR},
            </if>
            <if test="field05 != null and field05 != ''">
                #{field05,jdbcType=VARCHAR},
            </if>
            <if test="field06 != null and field06 != ''">
                #{field06,jdbcType=VARCHAR},
            </if>
            <if test="field07 != null and field07 != ''">
                #{field07,jdbcType=VARCHAR},
            </if>
            <if test="field08 != null and field08 != ''">
                #{field08,jdbcType=VARCHAR},
            </if>
            <if test="field09 != null and field09 != ''">
                #{field09,jdbcType=VARCHAR},
            </if>
            <if test="field10 != null and field10 != ''">
                #{field10,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Code != null and acitem01Code != ''">
                #{acitem01Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem01Name != null and acitem01Name != ''">
                #{acitem01Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Code != null and acitem02Code != ''">
                #{acitem02Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem02Name != null and acitem02Name != ''">
                #{acitem02Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Code != null and acitem03Code != ''">
                #{acitem03Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem03Name != null and acitem03Name != ''">
                #{acitem03Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Code != null and acitem04Code != ''">
                #{acitem04Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem04Name != null and acitem04Name != ''">
                #{acitem04Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Code != null and acitem05Code != ''">
                #{acitem05Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem05Name != null and acitem05Name != ''">
                #{acitem05Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Code != null and acitem06Code != ''">
                #{acitem06Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem06Name != null and acitem06Name != ''">
                #{acitem06Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Code != null and acitem07Code != ''">
                #{acitem07Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem07Name != null and acitem07Name != ''">
                #{acitem07Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Code != null and acitem08Code != ''">
                #{acitem08Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem08Name != null and acitem08Name != ''">
                #{acitem08Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Code != null and acitem09Code != ''">
                #{acitem09Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem09Name != null and acitem09Name != ''">
                #{acitem09Name,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Code != null and acitem10Code != ''">
                #{acitem10Code,jdbcType=VARCHAR},
            </if>
            <if test="acitem10Name != null and acitem10Name != ''">
                #{acitem10Name,jdbcType=VARCHAR},
            </if>
            <if test="auditAgy != null and auditAgy != ''">
                #{auditAgy,jdbcType=VARCHAR},
            </if>
            <if test="auditReason != null and auditReason != ''">
                #{auditReason,jdbcType=VARCHAR},
            </if>
            <if test="targetNecessity != null and targetNecessity != ''">
                #{targetNecessity,jdbcType=VARCHAR},
            </if>
            <if test="abroadPlan != null and abroadPlan != ''">
                #{abroadPlan,jdbcType=VARCHAR},
            </if>
            <if test="timeCountryCheck != null and timeCountryCheck != ''">
                #{timeCountryCheck,jdbcType=VARCHAR},
            </if>
            <if test="routeCheck != null and routeCheck != ''">
                #{routeCheck,jdbcType=VARCHAR},
            </if>
            <if test="peopleNumCheck != null and peopleNumCheck != ''">
                #{peopleNumCheck,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="listYearBudget != null and listYearBudget != ''">
                #{listYearBudget,jdbcType=VARCHAR},
            </if>
            <if test="beforeSpend != null and beforeSpend != ''">
                #{beforeSpend,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null and createTime != ''">
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierName != null and modifierName != ''">
                #{modifierName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedTime != null and modifiedTime != ''">
                #{modifiedTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </sql>

    <sql id="allColumnCond">
        <if test="id != null and id != ''">
            AND id=#{id,jdbcType=VARCHAR}
        </if>
        <if test="billId != null and billId != ''">
            AND bill_id=#{billId,jdbcType=VARCHAR}
        </if>
        <if test="expenseCode != null and expenseCode != ''">
            AND expense_code=#{expenseCode,jdbcType=VARCHAR}
        </if>
        <if test="expenseName != null and expenseName != ''">
            AND expense_name=#{expenseName,jdbcType=VARCHAR}
        </if>
        <if test="departmentCode != null and departmentCode != ''">
            AND department_code=#{departmentCode,jdbcType=VARCHAR}
        </if>
        <if test="departmentName != null and departmentName != ''">
            AND department_name=#{departmentName,jdbcType=VARCHAR}
        </if>
        <if test="inputAmt != null">
            AND input_amt=#{inputAmt,jdbcType=DECIMAL}
        </if>
        <if test="checkAmt != null">
            AND check_amt=#{checkAmt,jdbcType=DECIMAL}
        </if>
        <if test="abroadTeamName != null and abroadTeamName != ''">
            AND abroad_team_name=#{abroadTeamName,jdbcType=VARCHAR}
        </if>
        <if test="abroadTeamType != null and abroadTeamType != ''">
            AND abroad_team_type=#{abroadTeamType,jdbcType=VARCHAR}
        </if>
        <if test="abroadTeamTypeName != null and abroadTeamTypeName != ''">
            AND abroad_team_type_name=#{abroadTeamTypeName,jdbcType=VARCHAR}
        </if>
        <if test="isPlan != null">
            AND is_plan=#{isPlan,jdbcType=INTEGER}
        </if>
        <if test="planCode != null and planCode != ''">
            AND plan_code=#{planCode,jdbcType=VARCHAR}
        </if>
        <if test="planName != null and planName != ''">
            AND plan_name=#{planName,jdbcType=VARCHAR}
        </if>
        <if test="abroadStartDate != null and abroadStartDate != ''">
            AND abroad_start_date=#{abroadStartDate,jdbcType=VARCHAR}
        </if>
        <if test="abroadFinishDate != null and abroadFinishDate != ''">
            AND abroad_finish_date=#{abroadFinishDate,jdbcType=VARCHAR}
        </if>
        <if test="preStartMonth != null and preStartMonth != ''">
            AND pre_start_month=#{preStartMonth,jdbcType=VARCHAR}
        </if>
        <if test="preFinishMonth != null and preFinishMonth != ''">
            AND pre_finish_month=#{preFinishMonth,jdbcType=VARCHAR}
        </if>
        <if test="preQuarter != null and preQuarter != ''">
            AND pre_quarter=#{preQuarter,jdbcType=VARCHAR}
        </if>
        <if test="remarks != null and remarks != ''">
            AND remarks=#{remarks,jdbcType=VARCHAR}
        </if>
        <if test="abroadJob != null and abroadJob != ''">
            AND abroad_job=#{abroadJob,jdbcType=VARCHAR}
        </if>
        <if test="abroadVisitDays != null">
            AND abroad_visit_days=#{abroadVisitDays,jdbcType=INTEGER}
        </if>
        <if test="abroadPeopleNum != null">
            AND abroad_people_num=#{abroadPeopleNum,jdbcType=INTEGER}
        </if>
        <if test="abroadApprovalNum != null and abroadApprovalNum != ''">
            AND abroad_approval_num=#{abroadApprovalNum,jdbcType=VARCHAR}
        </if>
        <if test="needAmt != null">
            AND need_amt=#{needAmt,jdbcType=DECIMAL}
        </if>
        <if test="abroadTeamUnit != null and abroadTeamUnit != ''">
            AND abroad_team_unit=#{abroadTeamUnit,jdbcType=VARCHAR}
        </if>
        <if test="abroadVisitCountry != null and abroadVisitCountry != ''">
            AND abroad_visit_country=#{abroadVisitCountry,jdbcType=VARCHAR}
        </if>
        <if test="abroadVisitRoute != null and abroadVisitRoute != ''">
            AND abroad_visit_route=#{abroadVisitRoute,jdbcType=VARCHAR}
        </if>
        <if test="abroadAchievement != null and abroadAchievement != ''">
            AND abroad_achievement=#{abroadAchievement,jdbcType=VARCHAR}
        </if>
        <if test="abroadAffairsNum != null">
            AND abroad_affairs_num=#{abroadAffairsNum,jdbcType=INTEGER}
        </if>
        <if test="abroadTeamPersonnel != null and abroadTeamPersonnel != ''">
            AND abroad_team_personnel=#{abroadTeamPersonnel,jdbcType=VARCHAR}
        </if>
        <if test="abroadOtherUnits != null and abroadOtherUnits != ''">
            AND abroad_other_units=#{abroadOtherUnits,jdbcType=VARCHAR}
        </if>
        <if test="abroadLeaderName != null and abroadLeaderName != ''">
            AND abroad_leader_name=#{abroadLeaderName,jdbcType=VARCHAR}
        </if>
        <if test="abroadLeaderLevel != null and abroadLeaderLevel != ''">
            AND abroad_leader_level=#{abroadLeaderLevel,jdbcType=VARCHAR}
        </if>
        <if test="agyCode != null and agyCode != ''">
            AND agy_code=#{agyCode,jdbcType=VARCHAR}
        </if>
        <if test="fiscal != null and fiscal != ''">
            AND fiscal=#{fiscal,jdbcType=VARCHAR}
        </if>
        <if test="mofDivCode != null and mofDivCode != ''">
            AND mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id=#{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="field01 != null and field01 != ''">
            AND field01=#{field01,jdbcType=VARCHAR}
        </if>
        <if test="field02 != null and field02 != ''">
            AND field02=#{field02,jdbcType=VARCHAR}
        </if>
        <if test="field03 != null and field03 != ''">
            AND field03=#{field03,jdbcType=VARCHAR}
        </if>
        <if test="field04 != null and field04 != ''">
            AND field04=#{field04,jdbcType=VARCHAR}
        </if>
        <if test="field05 != null and field05 != ''">
            AND field05=#{field05,jdbcType=VARCHAR}
        </if>
        <if test="field06 != null and field06 != ''">
            AND field06=#{field06,jdbcType=VARCHAR}
        </if>
        <if test="field07 != null and field07 != ''">
            AND field07=#{field07,jdbcType=VARCHAR}
        </if>
        <if test="field08 != null and field08 != ''">
            AND field08=#{field08,jdbcType=VARCHAR}
        </if>
        <if test="field09 != null and field09 != ''">
            AND field09=#{field09,jdbcType=VARCHAR}
        </if>
        <if test="field10 != null and field10 != ''">
            AND field10=#{field10,jdbcType=VARCHAR}
        </if>
        <if test="acitem01Code != null and acitem01Code != ''">
            AND acitem01_code=#{acitem01Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem01Name != null and acitem01Name != ''">
            AND acitem01_name=#{acitem01Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem02Code != null and acitem02Code != ''">
            AND acitem02_code=#{acitem02Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem02Name != null and acitem02Name != ''">
            AND acitem02_name=#{acitem02Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem03Code != null and acitem03Code != ''">
            AND acitem03_code=#{acitem03Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem03Name != null and acitem03Name != ''">
            AND acitem03_name=#{acitem03Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem04Code != null and acitem04Code != ''">
            AND acitem04_code=#{acitem04Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem04Name != null and acitem04Name != ''">
            AND acitem04_name=#{acitem04Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem05Code != null and acitem05Code != ''">
            AND acitem05_code=#{acitem05Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem05Name != null and acitem05Name != ''">
            AND acitem05_name=#{acitem05Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem06Code != null and acitem06Code != ''">
            AND acitem06_code=#{acitem06Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem06Name != null and acitem06Name != ''">
            AND acitem06_name=#{acitem06Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem07Code != null and acitem07Code != ''">
            AND acitem07_code=#{acitem07Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem07Name != null and acitem07Name != ''">
            AND acitem07_name=#{acitem07Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem08Code != null and acitem08Code != ''">
            AND acitem08_code=#{acitem08Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem08Name != null and acitem08Name != ''">
            AND acitem08_name=#{acitem08Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem09Code != null and acitem09Code != ''">
            AND acitem09_code=#{acitem09Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem09Name != null and acitem09Name != ''">
            AND acitem09_name=#{acitem09Name,jdbcType=VARCHAR}
        </if>
        <if test="acitem10Code != null and acitem10Code != ''">
            AND acitem10_code=#{acitem10Code,jdbcType=VARCHAR}
        </if>
        <if test="acitem10Name != null and acitem10Name != ''">
            AND acitem10_name=#{acitem10Name,jdbcType=VARCHAR}
        </if>
        <if test="auditAgy != null and auditAgy != ''">
            AND audit_agy=#{auditAgy,jdbcType=VARCHAR}
        </if>
        <if test="auditReason != null and auditReason != ''">
            AND audit_reason=#{auditReason,jdbcType=VARCHAR}
        </if>
        <if test="targetNecessity != null and targetNecessity != ''">
            AND target_necessity=#{targetNecessity,jdbcType=VARCHAR}
        </if>
        <if test="abroadPlan != null and abroadPlan != ''">
            AND abroad_plan=#{abroadPlan,jdbcType=VARCHAR}
        </if>
        <if test="timeCountryCheck != null and timeCountryCheck != ''">
            AND time_country_check=#{timeCountryCheck,jdbcType=VARCHAR}
        </if>
        <if test="routeCheck != null and routeCheck != ''">
            AND route_check=#{routeCheck,jdbcType=VARCHAR}
        </if>
        <if test="peopleNumCheck != null and peopleNumCheck != ''">
            AND people_num_check=#{peopleNumCheck,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            AND remark=#{remark,jdbcType=VARCHAR}
        </if>
        <if test="listYearBudget != null and listYearBudget != ''">
            AND list_year_budget=#{listYearBudget,jdbcType=VARCHAR}
        </if>
        <if test="beforeSpend != null and beforeSpend != ''">
            AND before_spend=#{beforeSpend,jdbcType=VARCHAR}
        </if>
        <if test="creator != null and creator != ''">
            AND creator=#{creator,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time=#{createTime,jdbcType=VARCHAR}
        </if>
        <if test="creatorName != null and creatorName != ''">
            AND creator_name=#{creatorName,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
            AND modifier=#{modifier,jdbcType=VARCHAR}
        </if>
        <if test="modifierName != null and modifierName != ''">
            AND modifier_name=#{modifierName,jdbcType=VARCHAR}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''">
            AND modified_time=#{modifiedTime,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="insert" parameterType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        INSERT INTO pcx_bill_exp_abroad (
        <include refid="allColumn"/>
        ) VALUES (
        <include refid="allColumnValue"/>
        )
    </insert>

    <insert id="insertSelective" parameterType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        INSERT INTO pcx_bill_exp_abroad (
        <include refid="columnSelective"/>
        ) VALUES (
        <include refid="columnValueSelective"/>
        )
    </insert>

    <delete id="delById" parameterType="string">
        DELETE
        FROM pcx_bill_exp_abroad
        WHERE id = #{value,jdbcType=VARCHAR}
    </delete>

    <delete id="delByIds" parameterType="java.util.List">
        DELETE FROM pcx_bill_exp_abroad
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="del" parameterType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        DELETE FROM pcx_bill_exp_abroad
        WHERE 1=1
        <include refid="allColumnCond"/>
    </delete>

    <update id="updateById" parameterType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        UPDATE pcx_bill_exp_abroad
        <set>
            <include refid="allColumnSet"/>
        </set>
        WHERE id=#{id,jdbcType=VARCHAR}
    </update>

    <select id="selectById" parameterType="string" resultType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_bill_exp_abroad
        WHERE id=#{value,jdbcType=VARCHAR}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_bill_exp_abroad
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="select" parameterType="com.pty.pcx.entity.bill.PcxBillExpAbroad"
            resultType="com.pty.pcx.entity.bill.PcxBillExpAbroad">
        SELECT
        <include refid="allColumnAlias"/>
        FROM pcx_bill_exp_abroad
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>

    <select id="count" parameterType="com.pty.pcx.entity.bill.PcxBillExpAbroad" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pcx_bill_exp_abroad
        WHERE 1=1
        <include refid="allColumnCond"/>
    </select>
</mapper>