<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.mad.PcxMadEmpTypeDao">

  <sql id="allColumnAlias">
    modified_time as modifiedTime,                    creator as creator,                               ver as ver,                                       modifier as modifier,
    agy_code as agyCode,                              creator_name as creatorName,                      type_name as typeName,                            type_code as typeCode,
    agy_name as agyName,                              is_deleted as isDeleted,                          fiscal as fiscal,
    is_enabled as isEnabled,                          created_time as createdTime,                      type_id as typeId,                                modifier_name as modifierName
    ,own_agy as ownAgy,                               mof_div_code as mofDivCode
</sql>

  <sql id="allColumnCond">
    <if test="modifiedTime != null and modifiedTime != ''">
      AND modified_time=#{modifiedTime,jdbcType=VARCHAR}
    </if>
    <if test="creator != null and creator != ''">
      AND creator=#{creator,jdbcType=VARCHAR}
    </if>
    <if test="ver != null">
      AND ver=#{ver,jdbcType=VARCHAR}
    </if>
    <if test="modifier != null and modifier != ''">
      AND modifier=#{modifier,jdbcType=VARCHAR}
    </if>
    <if test="agyCode != null and agyCode != ''">
      AND agy_code=#{agyCode,jdbcType=VARCHAR}
    </if>
    <if test="creatorName != null and creatorName != ''">
      AND creator_name=#{creatorName,jdbcType=VARCHAR}
    </if>
    <if test="typeName != null and typeName != ''">
      AND type_name=#{typeName,jdbcType=VARCHAR}
    </if>
    <if test="typeCode != null and typeCode != ''">
      AND type_code=#{typeCode,jdbcType=VARCHAR}
    </if>
    <if test="agyName != null and agyName != ''">
      AND agy_name=#{agyName,jdbcType=VARCHAR}
    </if>
    <if test="isDeleted != null">
      AND is_deleted=#{isDeleted,jdbcType=VARCHAR}
    </if>
    <if test="fiscal != null">
      AND fiscal=#{fiscal,jdbcType=VARCHAR}
    </if>
    <if test="admCode != null and admCode != ''">
      AND ADM_CODE=#{admCode,jdbcType=VARCHAR}
    </if>
    <if test="isEnabled != null">
      AND is_enabled=#{isEnabled,jdbcType=VARCHAR}
    </if>
    <if test="createdTime != null and createdTime != ''">
      AND created_time=#{createdTime,jdbcType=VARCHAR}
    </if>
    <if test="typeId != null and typeId != ''">
      AND type_id=#{typeId,jdbcType=VARCHAR}
    </if>
    <if test="modifierName != null and modifierName != ''">
      AND modifier_name=#{modifierName,jdbcType=VARCHAR}
    </if>
    <if test="ownAgy != null ">
      and own_agy=#{ownAgy,jdbcType=INTEGER}
    </if>
    <if test="mofDivCode != null and mofDivCode != ''">
      and mof_div_code=#{mofDivCode,jdbcType=VARCHAR}
    </if>
  </sql>

  <select id="select" parameterType="com.pty.mad.entity.MadEmpType" resultType="com.pty.mad.entity.MadEmpType">
    SELECT
    <include refid="allColumnAlias" />
    FROM mad_emp_type
    WHERE 1=1
    <include refid="allColumnCond" />
    order by type_code
  </select>
</mapper>
