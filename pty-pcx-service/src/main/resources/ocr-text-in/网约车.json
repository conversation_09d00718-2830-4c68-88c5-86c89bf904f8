{"ecsRelId": "1939878967246848000", "ecsBillId": "b72521eaedbb4f2f9df6c3041e0129d3", "ecsDate": "2025-06-27", "ecsAmt": 2826.96, "inputAmt": 353.37, "ecsBillNo": "25117000000880127235", "ecsSeller": "91110108MA01G0FB09", "bizTypeName": "市内交通费-网约车,市内交通费-网约车", "billTypeCode": "einv", "expenseTypeCodes": "0701", "expenseTypeNames": "交通费", "madEmployeeDTO": {}, "madWorkLocations": {}, "costLevelVO": {}, "daysType": 0, "monthEcsAmt": 0, "dayEcsAmt": 0, "ecsExpDetails": [], "ecsBill": {"fileName": "数电票-滴滴电子发票.pdf", "billTypeCode": "einv", "sellerName": "北京滴滴出行科技有限公司", "purTaxId": "91110108MA01DP6N3R", "agencyCode": "bscx", "sellerAccountNo": "***************", "purAdd": "", "extaxAmt": 343.08, "isDeleted": 2, "claimerName": "陈烨", "sellerTaxId": "91110108MA01G0FB09", "attachId": "b72521eaedbb4f2f9df6c3041e0129d3", "billNo": "25117000000880127235", "taxAmt": 10.29, "fileList": [{"billTypeCode": "file_taxi", "fileName": "滴滴出行行程报销单.pdf", "mofDivCode": "87", "createUserCode": "1002660", "pageSize": 10, "updateUser": "陈烨", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "billSeq": 1, "field01": "滴滴出行-行程单", "billAmt": 353.37, "updateUserCode": "1002660", "field04": "2025-06-27 08:45:00", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "***********", "pageIndex": 1, "field02": "2025-06-27", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "2025-06-18 09:06:00", "detailList": [{"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278272", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 1, "field06": "4.9", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "快车", "updateUserCode": "1002660", "detailAmt": 17.77, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局", "pageIndex": 1, "field02": "2025-06-18 09:06:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278273", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 2, "field06": "3.3", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "专车", "updateUserCode": "1002660", "detailAmt": 16.7, "field04": "天竺|埃力生商厦-东门(星程酒店旁)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-20 09:16:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278274", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 3, "field06": "12.7", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 31.5, "field04": "线|马泉营地铁站B口", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-23 08:30:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278275", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 4, "field06": "43.3", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 104.7, "field04": "沙河巩华家园北二村-西北门", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-24 08:32:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278276", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 5, "field06": "51.6", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 115.7, "field04": "首都机场公安局-东门", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "南邵|兴昌佳苑-南门", "pageIndex": 1, "field02": "2025-06-24 19:21:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278277", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 6, "field06": "12.8", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 32.2, "field04": "线|马泉营地铁站B口", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-25 08:34:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278278", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 7, "field06": "5.1", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 17.4, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-26 09:10:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278279", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 8, "field06": "5.0", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 17.4, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-27 08:45:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}], "createUser": "陈烨", "extId": "1939547274195501056", "fileId": "e3fa2ebaa81940738085ce9466b59e3e"}], "billDescpt": "*运输服务*客运服务费", "ver": 0, "invSubKind": "32", "isDisplayCheck": 1, "mofDivCode": "87", "createUserCode": "1002660", "isValid": 1, "sellerAdd": "北京市海淀区唐家岭北环路6号院1号楼C座四层408 010-83456275", "updateUser": "陈烨", "updateTime": "2025-06-30 12:50:52", "billDate": "2025-06-27", "invUniCode": "25117000000880127235", "billExpDetailList": [{"ordSeq": 4, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-24 08:32:00", "expenseTypeName": "市内交通费", "detailId": "204ba07354004caf8d668f0444705a55", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 3, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-24 19:21:00", "expenseTypeName": "市内交通费", "detailId": "3ec348de958949b3a32e9caa2298be18", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 6, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-26 09:10:00", "expenseTypeName": "市内交通费", "detailId": "44a8389d654f4bc098aa2ba3adc45eda", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 5, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-27 08:45:00", "expenseTypeName": "市内交通费", "detailId": "4dc086e66c454e3db7a203c0d017110c", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 7, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-23 08:30:00", "expenseTypeName": "市内交通费", "detailId": "646aaab0f25243afbcfa9b74e482cba1", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 1, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-18 09:06:00", "expenseTypeName": "市内交通费", "detailId": "855712c4888c477a95b45261e32ff4bb", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 2, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-25 08:34:00", "expenseTypeName": "市内交通费", "detailId": "a2ab4ea1033c4f68b45567b179b64381", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 8, "passengerName": "[{\"budLevel\":\"1902657324880883712,1909860699108143104\",\"empCode\":\"51696\",\"empName\":\"王娜\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-20 09:16:00", "expenseTypeName": "市内交通费", "detailId": "fce4614499c2458bbf6811e19aa3db7d", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}], "isBillEdited": 2, "billAmt": 353.37, "updateUserCode": "1002660", "totalAmt": 353.37, "purName": "北京博思财信网络科技有限公司", "claimer": "1002660", "createTime": "2025-06-30 12:50:52", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "detailList": [{"itemQty": 1, "billTypeCode": "einv", "itemUnit": "次", "bizTypeName": "市内交通费-网约车", "detailId": "1939547158097166336", "pageSize": 10, "agencyCode": "bscx", "specMod": "无", "itemName": "*运输服务*客运服务费", "extaxAmt": 428.13, "isDeleted": 2, "billNo": "25117000000880127235", "taxAmt": 12.84, "ver": 1, "mofDivCode": "87", "createUserCode": "1002660", "updateUser": "EcsBillBizTypeHandlerJob", "detailSeq": 1, "updateTime": "2025-06-30 12:50:52", "bizTypeCode": "BIZ_TYPE_0042", "taxRate": 0.03, "updateUserCode": "1002660", "totalAmt": 440.97, "createTime": "2025-06-30 12:50:52", "pageIndex": 1, "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "createUser": "陈烨", "itemPrice": 428.13}, {"itemQty": 0, "billTypeCode": "einv", "itemUnit": "", "bizTypeName": "市内交通费-网约车", "detailId": "1939547158097166337", "pageSize": 10, "agencyCode": "bscx", "specMod": "", "itemName": "*运输服务*客运服务费", "extaxAmt": -85.05, "isDeleted": 2, "billNo": "25117000000880127235", "taxAmt": -2.55, "ver": 1, "mofDivCode": "87", "createUserCode": "1002660", "updateUser": "EcsBillBizTypeHandlerJob", "detailSeq": 2, "updateTime": "2025-06-30 12:50:52", "bizTypeCode": "BIZ_TYPE_0042", "taxRate": 0.03, "updateUserCode": "1002660", "totalAmt": -87.6, "createTime": "2025-06-30 12:50:52", "pageIndex": 1, "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "createUser": "陈烨", "itemPrice": 0}], "isRelExp": 1, "billKind": "std", "createUser": "陈烨", "billTypeName": "数电票", "sellerBankName": "招商银行股份有限公司北京东三环支行", "remarks": "", "purAccountNo": ""}, "ecsBillDetail": [{"itemQty": 1, "billTypeCode": "einv", "itemUnit": "次", "bizTypeName": "市内交通费-网约车", "detailId": "1939547158097166336", "pageSize": 10, "agencyCode": "bscx", "specMod": "无", "itemName": "*运输服务*客运服务费", "extaxAmt": 428.13, "isDeleted": 2, "billNo": "25117000000880127235", "taxAmt": 12.84, "ver": 1, "mofDivCode": "87", "createUserCode": "1002660", "updateUser": "EcsBillBizTypeHandlerJob", "detailSeq": 1, "updateTime": "2025-06-30 12:50:52", "bizTypeCode": "BIZ_TYPE_0042", "taxRate": 0.03, "updateUserCode": "1002660", "totalAmt": 440.97, "createTime": "2025-06-30 12:50:52", "pageIndex": 1, "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "createUser": "陈烨", "itemPrice": 428.13}, {"itemQty": 0, "billTypeCode": "einv", "itemUnit": "", "bizTypeName": "市内交通费-网约车", "detailId": "1939547158097166337", "pageSize": 10, "agencyCode": "bscx", "specMod": "", "itemName": "*运输服务*客运服务费", "extaxAmt": -85.05, "isDeleted": 2, "billNo": "25117000000880127235", "taxAmt": -2.55, "ver": 1, "mofDivCode": "87", "createUserCode": "1002660", "updateUser": "EcsBillBizTypeHandlerJob", "detailSeq": 2, "updateTime": "2025-06-30 12:50:52", "bizTypeCode": "BIZ_TYPE_0042", "taxRate": 0.03, "updateUserCode": "1002660", "totalAmt": -87.6, "createTime": "2025-06-30 12:50:52", "pageIndex": 1, "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "createUser": "陈烨", "itemPrice": 0}], "ecsBillExpDetail": [{"ordSeq": 4, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-24 08:32:00", "expenseTypeName": "市内交通费", "detailId": "204ba07354004caf8d668f0444705a55", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 3, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-24 19:21:00", "expenseTypeName": "市内交通费", "detailId": "3ec348de958949b3a32e9caa2298be18", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 6, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-26 09:10:00", "expenseTypeName": "市内交通费", "detailId": "44a8389d654f4bc098aa2ba3adc45eda", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 5, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-27 08:45:00", "expenseTypeName": "市内交通费", "detailId": "4dc086e66c454e3db7a203c0d017110c", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 7, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-23 08:30:00", "expenseTypeName": "市内交通费", "detailId": "646aaab0f25243afbcfa9b74e482cba1", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 1, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-18 09:06:00", "expenseTypeName": "市内交通费", "detailId": "855712c4888c477a95b45261e32ff4bb", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 2, "passengerName": "[{\"budLevel\":\"1902657365381083136,1909860699108143104\",\"empCode\":\"1002660\",\"empName\":\"陈烨\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-25 08:34:00", "expenseTypeName": "市内交通费", "detailId": "a2ab4ea1033c4f68b45567b179b64381", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}, {"ordSeq": 8, "passengerName": "[{\"budLevel\":\"1902657324880883712,1909860699108143104\",\"empCode\":\"51696\",\"empName\":\"王娜\",\"empType\":\"inner\"}]", "billTypeCode": "einv", "mofDivCode": "87", "checkInTime": "2025-06-20 09:16:00", "expenseTypeName": "市内交通费", "detailId": "fce4614499c2458bbf6811e19aa3db7d", "expenseTypeCode": "3021112", "agencyCode": "bscx", "checkOutTime": "", "cityName": "[{\"code\":\"1101\",\"name\":\"北京市\"}]", "isDeleted": 2, "createTime": "2025-06-30 16:07:03", "billId": "b72521eaedbb4f2f9df6c3041e0129d3", "billTypeName": "数电票"}], "ecsBillSettlList": [], "ecsFileList": [{"billTypeCode": "file_taxi", "fileName": "滴滴出行行程报销单.pdf", "mofDivCode": "87", "createUserCode": "1002660", "pageSize": 10, "updateUser": "陈烨", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "billSeq": 1, "field01": "滴滴出行-行程单", "billAmt": 353.37, "updateUserCode": "1002660", "field04": "2025-06-27 08:45:00", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "***********", "pageIndex": 1, "field02": "2025-06-27", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "2025-06-18 09:06:00", "detailList": [{"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278272", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 1, "field06": "4.9", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "快车", "updateUserCode": "1002660", "detailAmt": 17.77, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局", "pageIndex": 1, "field02": "2025-06-18 09:06:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278273", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 2, "field06": "3.3", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "专车", "updateUserCode": "1002660", "detailAmt": 16.7, "field04": "天竺|埃力生商厦-东门(星程酒店旁)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-20 09:16:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278274", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 3, "field06": "12.7", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 31.5, "field04": "线|马泉营地铁站B口", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-23 08:30:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278275", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 4, "field06": "43.3", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 104.7, "field04": "沙河巩华家园北二村-西北门", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-24 08:32:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278276", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 5, "field06": "51.6", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 115.7, "field04": "首都机场公安局-东门", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "南邵|兴昌佳苑-南门", "pageIndex": 1, "field02": "2025-06-24 19:21:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278277", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 6, "field06": "12.8", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 32.2, "field04": "线|马泉营地铁站B口", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-25 08:34:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278278", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 7, "field06": "5.1", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 17.4, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-26 09:10:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278279", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 8, "field06": "5.0", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 17.4, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-27 08:45:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}], "createUser": "陈烨", "extId": "1939547274195501056", "fileId": "e3fa2ebaa81940738085ce9466b59e3e"}], "ecsFileDetailList": [{"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278272", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 1, "field06": "4.9", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "快车", "updateUserCode": "1002660", "detailAmt": 17.77, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局", "pageIndex": 1, "field02": "2025-06-18 09:06:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278273", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 2, "field06": "3.3", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "专车", "updateUserCode": "1002660", "detailAmt": 16.7, "field04": "天竺|埃力生商厦-东门(星程酒店旁)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-20 09:16:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278274", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 3, "field06": "12.7", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 31.5, "field04": "线|马泉营地铁站B口", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-23 08:30:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278275", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 4, "field06": "43.3", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 104.7, "field04": "沙河巩华家园北二村-西北门", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-24 08:32:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278276", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 5, "field06": "51.6", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 115.7, "field04": "首都机场公安局-东门", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "南邵|兴昌佳苑-南门", "pageIndex": 1, "field02": "2025-06-24 19:21:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278277", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 6, "field06": "12.8", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 32.2, "field04": "线|马泉营地铁站B口", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-25 08:34:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278278", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 7, "field06": "5.1", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 17.4, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-26 09:10:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}, {"billTypeCode": "file_taxi", "mofDivCode": "87", "createUserCode": "1002660", "detailId": "1939547274212278279", "pageSize": 10, "updateUser": "陈烨", "detailSeq": 8, "field06": "5.0", "updateTime": "2025-06-30 12:51:19", "agencyCode": "bscx", "field01": "特惠快车", "updateUserCode": "1002660", "detailAmt": 17.4, "field04": "如家精选酒店(北京新国展首都机场店)", "isDeleted": 2, "createTime": "2025-06-30 12:51:19", "field05": "首都机场公安局(东门)", "pageIndex": 1, "field02": "2025-06-27 08:45:00", "billId": "e3fa2ebaa81940738085ce9466b59e3e", "field03": "北京市", "createUser": "陈烨", "extId": "1939547274195501056"}]}