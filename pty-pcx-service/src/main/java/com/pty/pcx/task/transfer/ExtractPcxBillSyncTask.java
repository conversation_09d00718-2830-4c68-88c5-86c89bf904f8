package com.pty.pcx.task.transfer;

import com.pty.pcx.api.transfer.PcxBillSyncService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExtractPcxBillSyncTask implements Job {

    @Autowired
    private PcxBillSyncService pcxBillSyncService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("ExtractPcxBillSyncTask...");
            pcxBillSyncService.extractPcxBillSync();
        } catch (Exception e) {
            log.error("ExtractPcxBillSyncTask...", e);
        }
    }

}
