package com.pty.pcx.util;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

public class OptionCacheUtil {

    public static final String SYS_ID = "PCX";

    public static final String BUSINESS_RULE_OPTION_CACHE_KEY = "BUSINESS_RULE_OPTION";

    public static Cache getCache(CacheManager cacheManager) {
        if(cacheManager==null){
            return null;
        }
        return cacheManager.getCache(SYS_ID);
    }

    public static void putValue(Cache cache, String key, Object value) {
        if(cache==null){
            return;
        }
        if(key==null || key.length()==0){
            return;
        }
        cache.put(key, value);
    }

    public static String key(String agyCode, String tenantId,String fiscal, String mofDivCode) {
        return String.format("%s%s%s%s%s", BUSINESS_RULE_OPTION_CACHE_KEY, agyCode, tenantId, fiscal, mofDivCode);
    }

}
