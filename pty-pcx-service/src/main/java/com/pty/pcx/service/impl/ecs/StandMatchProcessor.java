package com.pty.pcx.service.impl.ecs;

import com.google.common.collect.Lists;
import com.pty.pcx.api.stand.PcxStandKeyService;
import com.pty.pcx.dto.ecs.StandMatchSubDTO;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.pcx.qo.bill.BillExpenseStandQO;
import com.pty.pcx.service.impl.ecs.handler.StandMatchHandler;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.bill.ExpStandVO;
import com.pty.pub.common.util.IDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxStandReason.AMT_REASON;

/**
 * 标准匹配处理
 */
@Indexed
@Service
@Slf4j
public class StandMatchProcessor{

    @Autowired
    private PcxStandKeyService pcxStandKeyService;

    /**
     * 根据票返回标准
     * @param details 一张票上的明细信息
     * @return
     */
    public List<PcxBillExpStandResult> matchByEcs(List<PcxBillExpDetailBase> details){
        List<PcxBillExpStandResult> results = new ArrayList<>();
        try {

            PcxBillExpDetailBase base = details.get(0);
            //获取当前费用的标准
            BillExpenseStandQO expenseStandQO = BillExpenseStandQO.builder()
                    .fiscal(base.getFiscal())
                    .agyCode(base.getAgyCode())
                    .mofDivCode(base.getMofDivCode())
                    .expenseTypeCodes(Collections.singletonList(base.getExpDetailCode())).build();
            List<ExpStandVO> expStandVOS = pcxStandKeyService.billExpenseStandList(expenseStandQO);
            if (CollectionUtils.isEmpty(expStandVOS)) return null;

            List<PcxBillExpDetailTravel> hotelList = EcsExpOptService.getTravelDetailList(details);
            //一张票多个时间段，多个城市的住宿，分别计算标准
//            Map<Integer, List<PcxBillExpDetailTravel>> hotelMap =
//                    hotelList.stream().filter(item-> Objects.nonNull(item.getHotelSeq())).collect(Collectors.groupingBy(PcxBillExpDetailTravel::getHotelSeq));
            //按照城市，时间，进行分组，这么判断是几个人是一起住的，然后就高算标准
            Map<String, List<PcxBillExpDetailTravel>> hotelMap = hotelList.stream().collect(Collectors.groupingBy(item -> {
                return String.format("%s#%s#%s", item.getEndCityCode(), item.getStartTime(), item.getFinishTime());
            }));
            BigDecimal allStandAmt = BigDecimal.ZERO;
            BigDecimal allEcsAmt = BigDecimal.ZERO;
            for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : hotelMap.entrySet()) {
                List<PcxBillExpStandResult> itemMatchResult = Lists.newArrayList();
                List<PcxBillExpDetailTravel> value = entry.getValue();
                int index = 0;
                for (PcxBillExpDetailBase detail : value) {
                    StandMatchHandler<PcxBillExpDetailBase> standMatchHandler = ExpenseBeanUtil.getStandMatchHandler(detail.getExpDetailCode());
                    if (standMatchHandler == null) continue;
                    PcxBillExpStandResult standResult = standMatchHandler.handle(detail,expStandVOS);
                    if (standResult != null){
                        value.get(index ++).setStandAmt(standResult.getStandBigDecimal());
                        itemMatchResult.add(standResult);
                    }
                }
                Double travelDays = value.get(0).getTravelDays();
                if (CollectionUtils.isNotEmpty(itemMatchResult)){
                    PcxBillExpStandResult myStandResult = itemMatchResult.stream()
                            .sorted((o1, o2) -> o2.getStandBigDecimal().compareTo(o1.getStandBigDecimal()))
                            .collect(Collectors.toList()).get(0);
                    //根据票去匹配生成最终的标准
                    PcxBillExpStandResult result = new PcxBillExpStandResult();
                    result.setId(IDGenerator.id());
                    result.setAgyCode(base.getAgyCode());
                    result.setFiscal(base.getFiscal());
                    result.setMofDivCode(base.getMofDivCode());
                    result.setTenantId(base.getTenantId());
                    //默认取第一个标准的快照
                    result.setStandSnapshot(myStandResult.getStandSnapshot());
                    //票金额
                    //计算标准中的standValue的总金额
                    BigDecimal standAmt = myStandResult.getStandBigDecimal()
                            .multiply(new BigDecimal(travelDays))
                            .multiply(new BigDecimal(value.size()))
                            .setScale(2, RoundingMode.HALF_UP);
                    BigDecimal ecsAmt = value.stream().map(PcxBillExpDetailBase::getInputAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //票的标准金额
                    allStandAmt = allStandAmt.add(standAmt);
                    allEcsAmt = allEcsAmt.add(ecsAmt);
                    result.setEcsBillId(value.get(0).getHotelGroup());
                    results.add(result);
                }
            }
            String amtReason = "";
            if (allEcsAmt.compareTo(allStandAmt)>0){
                amtReason = AMT_REASON;
            }
            for (PcxBillExpStandResult result : results) {
                result.setStandValue(allStandAmt.toString());
                result.setReason(amtReason);
            }

        }catch (Exception e){
            log.error("按票匹配标准失败: ",e);
        }

        return results;
    }


    /**
     * 根据明细返回标准
     * @param base
     * @return
     */
    public PcxBillExpStandResult matchByDetail(PcxBillExpDetailBase base){
        try {
            //改签费的标准使用城市间交通费的标准
            String expenseTypeCode = EcsExpOptService.transTripDetailExpenseTypeCode(base.getExpDetailCode());
            //获取当前费用的标准
            BillExpenseStandQO expenseStandQO = BillExpenseStandQO.builder()
                    .fiscal(base.getFiscal())
                    .agyCode(base.getAgyCode())
                    .mofDivCode(base.getMofDivCode())
                    .expenseTypeCodes(Collections.singletonList(expenseTypeCode)).build();
            List<ExpStandVO> expStandVOS = pcxStandKeyService.billExpenseStandList(expenseStandQO);
            if (CollectionUtils.isEmpty(expStandVOS)) return null;

            StandMatchHandler<PcxBillExpDetailBase> standMatchHandler = ExpenseBeanUtil.getStandMatchHandler(base.getExpDetailCode());
            if (standMatchHandler == null) return null;

            PcxBillExpStandResult standResult = standMatchHandler.handle(base,expStandVOS);

            return standResult;
        } catch (Exception e) {
            log.error("按明细匹配标准失败: ",e);
        }

        return null;

    }

    /**
     * 根据要素返回标准
     * @param factory
     * @return
     */
    public PcxBillExpStandResult matchByFactory(StandMatchSubDTO factory){
        try {
            BillExpenseStandQO expenseStandQO = BillExpenseStandQO.builder()
                    .fiscal(factory.getFiscal())
                    .agyCode(factory.getAgyCode())
                    .mofDivCode(factory.getMofDivCode())
                    .expenseTypeCodes(Collections.singletonList(factory.getExpenseCode())).build();
            List<ExpStandVO> expStandVOS = pcxStandKeyService.billExpenseStandList(expenseStandQO);
            if (CollectionUtils.isEmpty(expStandVOS)) return null;

            StandMatchHandler<PcxBillExpDetailBase> standMatchHandler = ExpenseBeanUtil.getStandMatchHandler(factory.getExpenseCode());
            if (standMatchHandler == null) return null;

            PcxBillExpStandResult standResult = standMatchHandler.handle(factory,expStandVOS);

            return standResult;
        }catch (Exception e){
            log.error("按要素匹配标准失败: ",e);
        }

        return null;

    }
}
