package com.pty.pcx.service.impl.positionblock.block;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.block.IBaseBlockService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.common.constant.BillHistoryConstant;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.FundPositionEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.enu.block.BlockBeanEnum;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.block.BlockPropertyQO;
import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.vo.PaOptionVO;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/***
 * 抽象实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public abstract class AbstractBlockService {

    @Autowired
    private Map<String, IBaseBlockService> blockServiceMap;

    @Autowired
    protected IBusinessRuleOptionService bussinessRuleOptionService;

    public static final String EDIT_KEY  = "isEdit";

    public static final String IMPORTANT_KEY = "isImportant";

    protected static final Integer DEFAULT_CTRL_LEVEL = 0;


    public static final String MULTI_PAGE_KEY = "isMultiPage";

    public static final String MULTI_PAGE_NUM_KEY = "multiPageNum";


    protected List<PcxBlockVO> getBlockInfo(PcxBlockCondQO qo) {
        if(ObjectUtils.isEmpty(qo)){
            return new ArrayList<>();
        }
        if(PositionBlockEnum.CONTRACT.getCode().equals(qo.getClassifyCode()) && !PcxConstant.UNIVERSAL_ITEM_CODE.equals(qo.getPcxBasItemVO().getItemCode())){
            return new ArrayList<>();
       }
        PcxBlockVO pcxBlockVO = new PcxBlockVO();
        pcxBlockVO.setClassifyCode(qo.getClassifyCode());
        pcxBlockVO.setClassifyName(qo.getClassifyName());
        pcxBlockVO.setBlockCode(qo.getClassifyCode());
        pcxBlockVO.setBlockName(qo.getClassifyName());
        pcxBlockVO.setBlockName(qo.getClassifyName());
        pcxBlockVO.setArea(qo.getArea());
        pcxBlockVO.setBlockTitle(qo.getShowName());
        List<BlockPropertyVO> blockPropertyVOS = JSONObject.parseArray(qo.getPositionContext(), BlockPropertyVO.class);
        blockPropertyVOS = filterBlockProperties(blockPropertyVOS, qo);
        pcxBlockVO.setProperties(blockPropertyVOS);
        List<PcxBlockVO> result = new ArrayList<>();
        result.add(pcxBlockVO);
        return result;
    }

    protected List<BlockPropertyVO> filterBlockProperties(List<BlockPropertyVO> blockPropertyVOS, PcxBlockCondQO qo) {
        if(CollectionUtil.isEmpty(blockPropertyVOS)){
            return Lists.newArrayList();
        }
        if (!BillFuncCodeEnum.APPLY.getCode().equals(qo.getBillFuncCode()) ||
                !PositionBlockEnum.BASIC_INFO.getCode().equals(qo.getClassifyCode())) {
            return blockPropertyVOS;
        }
        PcxBasItemVO pcxBasItemVO = qo.getPcxBasItemVO();
        boolean isUniversalItem = PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxBasItemVO.getItemCode());
        if (isUniversalItem) {
            return blockPropertyVOS;
        }
        return blockPropertyVOS.stream()
                .filter(item -> !"inputAmt".equals(item.getFieldValue()))
                .collect(Collectors.toList());
    }

    protected List<BlockProperty> getBlockProperties(PcxBlockCondQO qo) {
        if(ObjectUtils.isEmpty(qo)){
            return new ArrayList<>();
        }
        BlockBeanEnum blockBeanEnum = BlockBeanEnum.getBlockMap().get(qo.getClassifyCode());
        BlockPropertyQO blockPropertyQO = new BlockPropertyQO();
        blockPropertyQO.setFiscal(qo.getFiscal());
        blockPropertyQO.setUserCode(qo.getUserCode());
        blockPropertyQO.setBillId(qo.getBillId());
        blockPropertyQO.setMofDivCode(qo.getMofDivCode());
        blockPropertyQO.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        blockPropertyQO.setAgyCode(qo.getAgyCode());
        blockPropertyQO.setBasExpTypeCodes( new ArrayList<>(qo.getExpenseMap().keySet()));
        blockPropertyQO.setBillFuncCodeEnum(FormSettingEnums.BillFuncCodeEnum.getByCode(qo.getBillFuncCode()));
        blockPropertyQO.setClassifyCode(qo.getClassifyCode());
        blockPropertyQO.setItemCode(qo.getPcxBasItemVO().getItemCode());
        return blockServiceMap.get(blockBeanEnum.getBeanName()).getBlockProperties(blockPropertyQO);
    }
    /**
     * 根据给定的条件和费用类型列表获取费用表单属性
     * 此方法旨在通过查询条件和费用类型来检索相关的费用表单属性，并将它们组织成一个映射表，
     * 其中每个键是表单代码和名称的组合，值是对应的BlockProperty列表
     *
     * @param qo 包含查询条件的对象，用于筛选费用表单属性
     * @param expenseTypeList 费用类型列表，用于进一步筛选属性
     * @return 返回一个映射，其中键是表单代码和名称的组合，值是对应的BlockProperty列表
     */
    protected Map<String, List<BlockProperty>> getExpenseFormProperties(PcxBlockCondQO qo, List<PcxBasExpType> expenseTypeList) {
        if (ObjectUtils.isEmpty(qo) || CollectionUtil.isEmpty(expenseTypeList)) {
           return new HashMap<>();
        }
        BlockBeanEnum blockBeanEnum = BlockBeanEnum.getBlockMap().get(qo.getClassifyCode());
        BlockPropertyQO blockPropertyQo = new BlockPropertyQO();
        blockPropertyQo.setFiscal(qo.getFiscal());
        blockPropertyQo.setMofDivCode(qo.getMofDivCode());
        blockPropertyQo.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        blockPropertyQo.setAgyCode(qo.getAgyCode());
        blockPropertyQo.setBasExpTypeCodes(expenseTypeList.stream().map(PcxBasExpType::getExpenseCode).collect(Collectors.toList()));
        blockPropertyQo.setBillFuncCodeEnum(FormSettingEnums.BillFuncCodeEnum.getByCode(qo.getBillFuncCode()));
        blockPropertyQo.setClassifyCode(BlockBeanEnum.EXPENSE_DETAIL.getClassifyCode());
        blockPropertyQo.setItemCode(qo.getPcxBasItemVO().getItemCode());
        List<BlockProperty> blockProperties = blockServiceMap.get(blockBeanEnum.getBeanName()).getBlockProperties(blockPropertyQo);
        // 将获取的BlockProperty列表按表单代码和名称分组，并返回
        return blockProperties.stream().collect(Collectors.groupingBy(BlockProperty::getFormCode));
    }
    /****
     * 字段是否可以编辑;
     * 默认逻辑：除制单岗位外，其他岗位都均不可以编辑；
     * @param qo
     * @return
     */
    protected String getIsEdit(PcxBlockCondQO qo) {
        String positionCode = qo.getPositionCode();
        if(PcxNodeEnum.make_bill.getId().equals(positionCode)){
            return PubConstant.STR_LOGIC_TRUE;
        }
        return PubConstant.STR_LOGIC_FALSE;
    }

    /****
     * 获取经费来源录入岗位
     * @param qo
     * @return
     */
    protected FundPositionEnum getFundPosition(PcxBlockCondQO qo) {
        Map<String, PaOptionVO> optionMap = qo.getOptionMap();
        PaOptionVO paOptionVO = optionMap.get(BusinessRuleEnum.BusinessOptionEnum.POSITION_FUND_SOURCE.getOptCode());
        if(StringUtil.isNotBlank(paOptionVO.getOptValue())){
            return FundPositionEnum.fromCode(paOptionVO.getOptValue());
        }
        return null;
    }

    /****
     * 费用承担部门，默认属性
     * @param
     * @return
     */
    protected BlockProperty departmentProperty() {
        BlockProperty blockProperty = new BlockProperty();
        blockProperty.setFieldLabel("departmentName");
        blockProperty.setFieldValue("departmentCode");
        blockProperty.setFieldTitle("费用承担部门");
        blockProperty.setIsRequired(PubConstant.LOGIC_FALSE);
        blockProperty.setIsEdit(PubConstant.LOGIC_TRUE);
        blockProperty.setFieldName("费用承担部门");
        blockProperty.setShowType("CODE_NAME");
        blockProperty.setNotes("");
        blockProperty.setRemarks("");
        blockProperty.setEditorCode("tree");
        blockProperty.setDataTypeCode("string");
        blockProperty.setDataClassifyCode("agy");
        blockProperty.setDataSourceCode("DEPARTMENT");
        blockProperty.setIsAddition(PubConstant.STR_LOGIC_TRUE);
        blockProperty.setFormCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        blockProperty.setFormName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
        return blockProperty;
    }

    protected List<BlockPropertyVO> convertToBlockPropertyVO(List<BlockProperty> blockProperties) {
        if (CollectionUtil.isEmpty(blockProperties)) {
            return new ArrayList<>();
        }
        List<BlockPropertyVO> result = new ArrayList<>();
        for (BlockProperty blockProperty : blockProperties) {
            if (ObjectUtils.isEmpty(blockProperty.getEditorCode())) {
                continue;
            }
            BlockPropertyVO blockPropertyVO = new BlockPropertyVO();
            BeanUtils.copyProperties(blockProperty, blockPropertyVO);
            result.add(blockPropertyVO);
        }
        return result;
    }

    /****
     * 判断是否为费用申请
     * 事项下不存在费用明细 && 申请单  -- true
     * @param qo
     * @return
     */
    protected Boolean isBasExpApply(PcxBlockCondQO qo, List<PcxBasExpType> pcxBasExpTypeList) {
        return qo != null
                && CollectionUtil.isNotEmpty(pcxBasExpTypeList)
                && BillFuncCodeEnum.APPLY.getCode().equals(qo.getBillFuncCode());
    }
}
