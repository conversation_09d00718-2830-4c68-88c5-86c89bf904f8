package com.pty.pcx.service.impl.wit.func;

import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.WitConstants;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.wit.PcxRuleDomainObject;
import com.pty.pcx.vo.ecs.WitEcsVO;
import com.pty.pub.common.util.SpringUtil;

import java.util.List;
import java.util.Map;

public class FnBillValidateDetail extends BFunction {

    public static final String FUNC_NAME = "validateDetail";

    @Override
    public String getName() {
        return FUNC_NAME;
    }

    public String method() {
        return "validateDetail()";
    }

    @Override
    public String methodName() {
        return "校验某个块的信息的必填项";
    }

    @Override
    public String desc() {
        return "校验某个块的信息的必填项";
    }

    @Override
    public AviatorObject call(Map<String, Object> env) {
        PcxRuleDomainObject domainObject = (PcxRuleDomainObject) env.getOrDefault(WitConstants.PCX, new PcxRuleDomainObject());
        PcxBill pcxBill = domainObject.getMain();
        WitEcsVO ecsVO = (WitEcsVO)domainObject.getSingle();
        List<PcxBillExpDetailBase> details = ecsVO.getEcsExpDetails();
        BillExpenseDetailService billExpenseDetailService = SpringUtil.getBean(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.DEFAULT.getProcessBeanName(), BillExpenseDetailService.class);
        if (billExpenseDetailService != null) {
            CheckMsg<Void> billExpenseDetailValidate = billExpenseDetailService.validate(details,pcxBill.getBillFuncCode());
            if (!billExpenseDetailValidate.isSuccess()) {
                domainObject.getBillDto().putErrMsg(billExpenseDetailValidate.getMsgInfo());
                return AviatorBoolean.FALSE;
            }
        }
        return AviatorBoolean.TRUE;
    }
}
