package com.pty.pcx.service.impl.bill;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillAmtApportionService;
import com.pty.pcx.api.bill.PcxBillExpDetailTrainingService;
import com.pty.pcx.api.bill.PcxBillExpTrainingService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.bill.PcxBillSettlementInfoService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dao.bill.labour.PcxBillExpDetailLabourDao;
import com.pty.pcx.dao.labour.PcxLabourInfoDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.ecs.UpdateEcsBillDTO;
import com.pty.pcx.dto.ecs.settlement.EcsBillSettleDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.ecs.impl.EcsBillExternalServiceImpl;
import com.pty.pcx.ecs.impl.EcsDtoTransHelper;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.labour.PcxBillExpDetailLabour;
import com.pty.pcx.entity.bill.labour.PcxLabourInfo;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pcx.entity.bill.meeting.PcxBillExpMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.qo.bill.*;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.UpdateEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateNoEcsCommonQO;
import com.pty.pcx.qo.training.DelLabourQo;
import com.pty.pcx.qo.training.SaveLabourQo;
import com.pty.pcx.qo.training.ViewLabourQo;
import com.pty.pcx.qo.treasurypay.detail.PayDetailSaveQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.impl.*;
import com.pty.pcx.service.impl.bill.handler.util.PcxBillExpBasicUtils;
import com.pty.pcx.service.impl.ecs.EcsExpTransService;
import com.pty.pcx.service.impl.ecs.dto.CollectExpListDto;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.util.PcxBillViewWrapperUtil;
import com.pty.pcx.util.trans.PcxBillTransformer;
import com.pty.pcx.vo.PcxBasExpTypeVO;
import com.pty.pcx.vo.bill.PcxBillBalanceMergeVO;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.ecs.EcsCommonDetailVO;
import com.pty.pcx.vo.ecs.EcsDetailAmtVO;
import com.pty.pcx.vo.training.LabourExpDetailVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.pty.pcx.util.ExpenseBeanUtil.getDetailBean;

@Indexed
@Service
@Slf4j
public class PcxBillExpTrainingServiceImpl implements PcxBillExpTrainingService {
    @Resource
    private PcxBillService pcxBillService;
    @Resource
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;
    @Resource
    private PcxBillExpDetailLabourDao pcxBillExpDetailLabourDao;
    @Resource
    private PcxLabourInfoDao pcxLabourInfoDao;
    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Resource
    private PcxBillExpBasicUtils pcxBillExpBasicUtils;
    @Resource
    private BatchServiceUtil batchServiceUtil;
    @Resource
    private BillMainService billMainService;
    @Resource
    private PcxBillExpTrainingDao pcxBillExpTrainingDao;
    @Resource
    private PcxExpDetailEcsRelDao expDetailEcsRelDao;
    @Resource
    private PcxBillExpDetailTrainingDao pcxBillExpDetailTrainingDao;
    @Resource
    private PcxBasExpTypeDao basExpTypeDao;
    @Resource
    private IEcsBillExternalService ecsBillExternalService;
    @Resource
    private PcxBillViewWrapperUtil pcxBillViewWrapperUtil;
    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;
    @Autowired
    private PcxEcsSettlDao pcxEcsSettlDao;
    @Resource
    private PcxBasFormSettingService pcxBasFormSettingService;
    @Resource
    private PcxBillSettlementInfoService pcxBillSettlementInfoService;
    @Autowired
    private BillExpenseCommonService billExpenseCommonService;

    @Resource
    private PcxBillAmtApportionService pcxBillAmtApportionService;

    @Autowired
    private PcxBillExpDetailTrainingService pcxBillExpDetailTrainingService;

    @Resource
    private EcsExpTransService ecsExpTransService;

    @Override
    public List<PcxBillExpTraining> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpTrainingDao.selectList(new LambdaQueryWrapper<PcxBillExpTraining>()
                .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpTraining::getBillId, billIds)
                .eq(PcxBillExpTraining::getAgyCode, agyCode)
                .eq(PcxBillExpTraining::getFiscal, fiscal)
                .eq(PcxBillExpTraining::getMofDivCode, mofDivCode));

    }

    @Override
    public List<PcxBillExpTraining> selectList(List<String> billIds, String planCode, String agyCode, String fiscal, String mofDivCode) {

        return pcxBillExpTrainingDao.selectList(new LambdaQueryWrapper<PcxBillExpTraining>()
                .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpTraining::getBillId, billIds)
                .eq(PcxBillExpTraining::getAgyCode, agyCode)
                .eq(PcxBillExpTraining::getFiscal, fiscal)
                .eq(PcxBillExpTraining::getMofDivCode, mofDivCode)
                .eq(PcxBillExpTraining::getPlanCode, planCode));
    }

    @Override
    public String saveBillAndExpense(StartExpenseQO expenseQO, List<InvoiceDtoWrapper> wrappers){
        // 1. 获取事项关联的费用类型
        List<PcxBasItemExp> baseTypeList = pcxBillExpBasicUtils.getItemExpenseType(expenseQO);

        // 2. 创建报销单主对象
        PcxBill bill = buildBill(expenseQO, baseTypeList);

        // 3.根据费用类型构建费用实体
        List<PcxBillExpTraining> baseExpList = this.buildBaseExpByExpType(baseTypeList, expenseQO);
        //初始化集合容器
        //标准结果(快照)
        List<PcxBillExpStandResult> standList = new ArrayList<>();
        List<PcxExpDetailEcsRel> allEcsRel = new ArrayList<>();
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        //电子凭证带过来的支付信息
        List<PcxEcsSettl> settlList = new ArrayList<>();
        //所有票生成的费用明细
        List<PcxBillExpDetailBase> allExpDetailBase = new ArrayList<>();


        // 3. 收集所有票生成的费用，明细，并建立票与明细的关联关系
        if (CollectionUtils.isNotEmpty(wrappers)) {
            for (InvoiceDtoWrapper wrapper : wrappers) {
                List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = collectEcsRel(wrapper, attachRelList, expenseQO, settlList);
                allEcsRel.addAll(pcxExpDetailEcsRels);
                //整理票生成的所有费用，票生成的所有明细
                CollectExpListDto expDto = collectExpBase(wrapper);
                allExpDetailBase.addAll(expDto.getExpDetailList());
                //费用的支出标准列表
                if (CollectionUtils.isNotEmpty(wrapper.getStandList())) {
                    standList.addAll(wrapper.getStandList());
                }
                if (CollectionUtils.isNotEmpty(wrapper.getSettlList())) {
                    settlList.addAll(wrapper.getSettlList());
                }
            }
        }

        // 4. 收集报销单关联费用类型
        List<PcxBasExpType> expTypeList = pcxBillExpBasicUtils.collectEcsRelExpType(allEcsRel);
        pcxBillExpBasicUtils.enrichWithBaseExpType(baseTypeList, expTypeList);

        // 5. 设置明细部门信息
        this.disposeDetailDepartmentCode(bill, allExpDetailBase);

        // 6. 查询出明细对于的费用代码
        //所有费用明细的明细代码
        List<String> allDetailCodes = allExpDetailBase.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(expenseQO, allDetailCodes);
        // 7. 票生成的明细，查出费用明细对应的费用关系，给费用汇总加上金额，并把费用id打到费用明细上面
        Map<String, PcxBillExpBase> baseExpMap = baseExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        summaryExpBaseAmtAndMarkDetail(baseExpMap, allExpDetailBase, parentMap);

        // 8. 汇总票据金额
        this.collectBillAmt(bill, baseExpList);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(bill, baseExpList);

        // 9.保存相关表结构信息
        return this.createTrainingExpenseBill(
                bill,
                baseExpList,
                allEcsRel,
                attachRelList,
                allExpDetailBase,
                parentMap,
                standList,
                settlList,
                null,
                apportionPair.getLeft(),
                apportionPair.getRight()

        );
    }

    @Override
    @Transactional
    public CheckMsg<?> saveLabourExpDetail(SaveLabourQo req) {
        // 校验 & 保存劳务信息 & 保存附件关系...
        saveLabourInfo(req, billMainService.view(req.getBillId()));
        return queryBillView(req.getBillId());
    }

    private CheckMsg<?> queryBillView(String billId) {
        return pcxBillService.view(billId, Arrays.asList(PositionBlockEnum.ECSEXPMATCH.code, PositionBlockEnum.CONTRACT.getCode()));
    }

    @Transactional
    @Override
    public CheckMsg<?> delLabourExpDetail(DelLabourQo req) {
        String billId = req.getBillId();
        String labourInfoId = req.getLabourInfoId();

        // 1. 删除明细表中关联的 labour_info_id 数据
        pcxBillExpDetailLabourDao.delete(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                .eq(PcxBillExpDetailLabour::getLabourInfoId, labourInfoId)
                .eq(PcxBillExpDetailLabour::getBillId, billId));

        // 2. 删除附件关系表中关联的 labour_info_id 数据
        pcxBillExpAttachRelDao.delete(new LambdaQueryWrapper<PcxBillExpAttachRel>()
                .eq(PcxBillExpAttachRel::getRelId, labourInfoId)
                .eq(PcxBillExpAttachRel::getBillId, billId));

        // 3. 删除劳务人员关联费用的明细并修改相关费用金额,
        deleteOrUpdateBillExpData(billId, labourInfoId);

        return queryBillView(req.getBillId());
    }

    private void deleteOrUpdateBillExpData(String billId, String labourInfoId) {
        PcxBill pcxBill = billMainService.view(billId);
        if (StringUtil.isNotEmpty(pcxBill.getExpenseCodes()) && pcxBill.getExpenseCodes().contains(PcxConstant.MEETING_EXPENSE_30215)) {
            BillExpenseDetailService4Metting detailBean = getDetailBean(BillExpenseDetailService4Metting.class);
            BillExpenseService4Meeting meetingBean = getDetailBean(BillExpenseService4Meeting.class);
            PcxBillExpMeeting billExpMeeting = meetingBean.view(PcxConstant.MEETING_EXPENSE_30215, pcxBill);
            List<PcxBillExpDetailMeeting> pcxBillExpDetailMeetings = detailBean.listByExpenseCode(PcxConstant.MEETING_EXPENSE_302150101, pcxBill);
            PcxBillExpDetailMeeting pcxBillExpDetailMeeting = pcxBillExpDetailMeetings.stream()
                    .filter(item -> item.getLabourCode().equals(labourInfoId))
                    .findFirst()
                    .orElse(null);
            if(pcxBillExpDetailMeeting!= null){
                detailBean.deleteByIds(Arrays.asList(pcxBillExpDetailMeeting.getId()));
                billExpMeeting.setInputAmt(billExpMeeting.getInputAmt().subtract(pcxBillExpDetailMeeting.getInputAmt()));
                billExpMeeting.setCheckAmt(billExpMeeting.getCheckAmt().subtract(pcxBillExpDetailMeeting.getCheckAmt()));
                pcxBill.setInputAmt(pcxBill.getInputAmt().subtract(pcxBillExpDetailMeeting.getInputAmt()));
                pcxBill.setCheckAmt(pcxBill.getCheckAmt().subtract(pcxBillExpDetailMeeting.getCheckAmt()));
                meetingBean.saveOrUpdate(billExpMeeting, pcxBill);
                billMainService.saveOrUpdate(pcxBill);
            }
        }

        if (StringUtil.isNotEmpty(pcxBill.getExpenseCodes()) && pcxBill.getExpenseCodes().contains(PcxConstant.TRAINING_EXPENSE_30216)) {
            BillExpenseDetailService4Training detailBean = getDetailBean(BillExpenseDetailService4Training.class);
            BillExpenseService4Training trainingBean = getDetailBean(BillExpenseService4Training.class);
            PcxBillExpTraining billExpMeeting = trainingBean.view(PcxConstant.TRAINING_EXPENSE_30216, pcxBill);
            List<PcxBillExpDetailTraining> pcxBillExpDetailTrainings = detailBean.listByExpenseCode(PcxConstant.TRAINING_EXPENSE_302160201, pcxBill);
            PcxBillExpDetailTraining pcxBillExpDetailMeeting = pcxBillExpDetailTrainings.stream()
                    .filter(item -> item.getLabourCode().equals(labourInfoId))
                    .findFirst()
                    .orElse(null);
            if(pcxBillExpDetailMeeting!= null){
                detailBean.deleteByIds(Arrays.asList(pcxBillExpDetailMeeting.getId()));
                billExpMeeting.setInputAmt(billExpMeeting.getInputAmt().subtract(pcxBillExpDetailMeeting.getInputAmt()));
                billExpMeeting.setCheckAmt(billExpMeeting.getCheckAmt().subtract(pcxBillExpDetailMeeting.getCheckAmt()));
                pcxBill.setInputAmt(pcxBill.getInputAmt().subtract(pcxBillExpDetailMeeting.getInputAmt()));
                pcxBill.setCheckAmt(pcxBill.getCheckAmt().subtract(pcxBillExpDetailMeeting.getCheckAmt()));
                trainingBean.saveOrUpdate(billExpMeeting, pcxBill);
                billMainService.saveOrUpdate(pcxBill);
            }
        }

    }


    private void summaryExpBaseAmtAndMarkDetail(Map<String, PcxBillExpBase> baseExpMap, List<PcxBillExpDetailBase> allExpDetailBase,
                                                Map<String, PcxBasExpTypeVO> parentMap) {
        //从费用明细中收集金额，更新到对应的费用上面
        Map<String, List<PcxBillExpDetailBase>> detailMap = allExpDetailBase
                .stream().collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode()).getLastCode()));
        for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : detailMap.entrySet()) {
            PcxBillExpBase expBase = baseExpMap.get(entry.getKey());
            BigDecimal totalInputAmt = BigDecimal.ZERO;
            BigDecimal totalcheckAmt = BigDecimal.ZERO;
            for (PcxBillExpDetailBase detailBase : entry.getValue()) {
                detailBase.setExpenseId(expBase.getId());
                totalInputAmt = totalInputAmt.add(detailBase.getInputAmt());
                if (Objects.nonNull(detailBase.getCheckAmt())){
                    totalcheckAmt = totalcheckAmt.add(detailBase.getCheckAmt());
                }
            }
            expBase.setInputAmt(totalInputAmt);
            expBase.setCheckAmt(totalcheckAmt);
        }

    }
    private CollectExpListDto collectExpBase(InvoiceDtoWrapper wrapper) {
        CollectExpListDto dto = new CollectExpListDto();
        if (Objects.equals(wrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            dto.addExpBaseList(wrapper.getExpenseList());
            dto.addExpDetailList(wrapper.getExpDetailList());
        }
        return dto;
    }

    private Map<String, PcxBasExpTypeVO> getExpTypeParentMap(ExpInvoiceQO invoiceQO, List<String> allDetailCodes) {
        if (CollectionUtils.isNotEmpty(allDetailCodes)) {
            List<PcxBasExpTypeVO> pcxBasExpTypes = getExpTypeByCodes(invoiceQO, allDetailCodes);

            return pcxBasExpTypes.stream()
                    .collect(Collectors.toMap(
                            PcxBasExpTypeVO::getExpenseCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        } else {
            return new HashMap<>();
        }
    }

    private List<PcxBasExpTypeVO> getExpTypeByCodes(ExpInvoiceQO invoiceQO, List<String> allDetailCodes){
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setFiscal(invoiceQO.getFiscal());
        qo.setAgyCode(invoiceQO.getAgyCode());
        qo.setMofDivCode(invoiceQO.getMofDivCode());
        qo.setExpTypeCodes(allDetailCodes);
        return basExpTypeDao.getTreeData(qo);
    }

    public void saveLabourInfo(SaveLabourQo req, PcxBill pcxBill) {
        if (Objects.isNull(pcxBill)) {
            throw new CommonException(String.format("当前 billId: %s, 关联的单据不存在", req.getBillId()));
        }

        SaveLabourQo.LabourExpInfo labourExpInfo = req.getLabourExpInfo();
        SaveLabourQo.LabourExpDetail labourExpDetail = req.getLabourExpDetail();

        String labourInfoId = req.getLabourInfoId();
        PcxLabourInfo labourInfo = getOrInitLabourInfo(labourInfoId); // 获取或初始化主表对象抽离

        // 设置主表信息（抽离方法）
        setLabourMainInfo(labourInfo, labourExpInfo, req);
        // 设置银行信息（抽离方法）
        setBankInfo(labourInfo, labourExpDetail);
        // 构建明细数据（抽离方法）
        PcxBillExpDetailLabour detailLabour = buildDetailLabour(req, pcxBill, labourInfo, labourExpInfo, labourExpDetail);
        // 校验构建的明细数据
        validate(detailLabour, FormSettingEnums.BillFuncCodeEnum.EXPENSES_BILL.getCode());

        // 插入或更新主表
        if (StringUtil.isNotEmpty(labourInfoId)) {
            pcxLabourInfoDao.updateById(labourInfo);
        } else {
            pcxLabourInfoDao.insert(labourInfo);
        }

        req.setLabourInfoId(labourInfo.getId());

         // 劳务费生成费用明细(会议、培训)、修改补充费用主表信息、修改单据金额信息
        buildExpDetailAndSave(labourInfo.getId(), req, pcxBill, detailLabour);

        // 清理旧数据并插入新明细
        deleteExistingDetailLabour(labourInfo.getId(), pcxBill.getId());

        pcxBillExpDetailLabourDao.insert(detailLabour);
        // 处理附件关系
        handleAttachmentRelations(req, pcxBill, labourInfo.getId());
    }

    /**
     * 参数校验逻辑抽离
     */
    private void validateLabourParams(SaveLabourQo.LabourExpInfo labourExpInfo, SaveLabourQo.LabourExpDetail labourExpDetail) {
        if (Objects.isNull(labourExpInfo) || Objects.isNull(labourExpDetail)) {
            throw new CommonException("劳务信息或明细不能为空");
        }
        String idNo = labourExpInfo.getIdNo();
        if (StringUtil.isEmpty(idNo)) {
            throw new CommonException("证件号不能为空");
        }
    }

    public CheckMsg<Void> validate(PcxBillExpDetailBase expBase, String billFuncCode) {
        if (Objects.isNull(expBase)) {
            return CheckMsg.fail("培训费用信息为空");
        }
        FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum = FormSettingEnums.BillFuncCodeEnum.getByCode(billFuncCode);
        if (Objects.isNull(billFuncCodeEnum)) {
            return CheckMsg.fail("暂不支持单据类型为：[" + billFuncCode + "]的业务操作");
        }

        //查询差旅费启用的必填的专属字段
        PcxBasFormSettingQueryQO qo = new PcxBasFormSettingQueryQO();
        qo.setFormClassify(FormSettingEnums.FormClassifyEnum.EXPENSE.getCode());
        qo.setBillFuncCode(billFuncCodeEnum.getBit());
        qo.setFormCode(expBase.getExpDetailCode());
        qo.setFormType(FormSettingEnums.FormTypeEnum.EXPENSE_DETAIL.getCode());
        qo.setAgyCode(expBase.getAgyCode());
        qo.setFiscal(expBase.getFiscal());
        qo.setMofDivCode(expBase.getMofDivCode());
        Response<List<PcxBasFormSettingQO>> response = pcxBasFormSettingService.selectAllFormSetting(qo);
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            //非空校验
            List<PcxBasFormSettingQO> formSettingQOS = response.getData().stream().filter(item -> item.getIsEnabled() == 1
                    && item.getIsNull() == 1
                    && Objects.equals(item.getIsAddition(), 1)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(formSettingQOS)) {
                Map<String, Object> stringObjectMap = BeanUtil.beanToMap(expBase);
                for (PcxBasFormSettingQO formSettingQO : formSettingQOS) {
                    Object o = stringObjectMap.get(formSettingQO.getFieldValue());
                    if (Objects.isNull(o)) {
                        log.warn("费用明细:[{}]不能为空", formSettingQO.getFieldName());
                        return CheckMsg.fail("费用明细:[" + formSettingQO.getFieldName() + "]不能为空");
                    }
                }
            }
        }

        return CheckMsg.success();
    }

    /**
     * 获取或初始化劳务主表对象抽离
     */
    private PcxLabourInfo getOrInitLabourInfo(String labourInfoId) {
        if (StringUtil.isNotEmpty(labourInfoId)) {
            PcxLabourInfo labourInfo = pcxLabourInfoDao.selectById(labourInfoId);
            if (Objects.isNull(labourInfo)) {
                throw new CommonException("未找到对应的劳务人员信息");
            }
            return labourInfo;
        }
        return new PcxLabourInfo();
    }

    /**
     * 设置劳务主表核心信息（抽离方法）
     */
    private void setLabourMainInfo(PcxLabourInfo labourInfo, SaveLabourQo.LabourExpInfo labourExpInfo, SaveLabourQo req) {
        BeanUtils.copyProperties(labourExpInfo, labourInfo);
        labourInfo.setId(StringUtil.isNotEmpty(req.getLabourInfoId()) ? req.getLabourInfoId() : IDGenerator.id());
        labourInfo.setAgyCode(req.getAgyCode());
        labourInfo.setFiscal(req.getFiscal());
        labourInfo.setMofDivCode(req.getMofDivCode());
    }

    /**
     * 设置银行相关信息（抽离方法）
     */
    private void setBankInfo(PcxLabourInfo labourInfo, SaveLabourQo.LabourExpDetail labourExpDetail) {
        labourInfo.setAccountName(labourExpDetail.getAccountName());
        labourInfo.setAccountNo(labourExpDetail.getAccountNo());
        labourInfo.setBankName(labourExpDetail.getBankName());
        labourInfo.setBankNo(labourExpDetail.getBankCode());
        labourInfo.setBankNodeCode(labourExpDetail.getBankNodeCode());
        labourInfo.setBankNodeName(labourExpDetail.getBankNodeName());
    }

    public void buildExpDetailAndSave(String labourInfoId, SaveLabourQo req, PcxBill pcxBill, PcxBillExpDetailLabour detailLabour) {
        // 劳务费生成费用明细、修改补充费用主表信息、修改单据金额信息
        String expenseTypeCode = req.getExpenseTypeCode();
        //从 pcxBillExpDetailLabours中拿到 detailId 过滤为空的
        //3021503构建专家咨询费的费用明细
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        BeanUtils.copyProperties(pcxBill, invoiceQO);
        Map<String, PcxBasExpTypeVO> parentMap  = getExpTypeParentMap(invoiceQO, Arrays.asList(expenseTypeCode));
        PcxBillExpBase baseExp = null;
        if (StringUtil.isNotEmpty(expenseTypeCode) && expenseTypeCode.contains(PcxConstant.MEETING_EXPENSE_30215)) {
            //通过 detailIds 取查询 费用明细表 ，如果没有数据则进行新增构建（目前正常单条）
            BillExpenseDetailService4Metting detailBean = getDetailBean(BillExpenseDetailService4Metting.class);
            BillExpenseService4Meeting meetingBean = getDetailBean(BillExpenseService4Meeting.class);
            PcxBillExpDetailMeeting meeting = null;
            PcxBillExpMeeting view = meetingBean.view(PcxConstant.MEETING_EXPENSE_30215, pcxBill);
            baseExp = view;
            List<PcxBillExpDetailMeeting> pcxBillExpDetailMeetings = detailBean.listByExpenseCode(null, pcxBill);
            //判断是否有关联的明细数据
            if (CollectionUtils.isNotEmpty(pcxBillExpDetailMeetings)) {
                meeting = pcxBillExpDetailMeetings.stream()
                        .filter(item -> Objects.equals(item.getLabourCode(), labourInfoId))
                        .findFirst()
                        .orElse(null);
            }
            if (meeting != null) {
                //修改金额,直接覆盖核定金额。目前是新增修改单据约为上的劳务明细金额
                //费用主表修改 view.getInputAmt() - meeting.getinputAmt() + detailLabour.getInputAmt()) = view.setInputAmt();的值 这是BigDecimal类型
                view.setInputAmt(view.getInputAmt()
                        .subtract(meeting.getInputAmt())
                        .add(detailLabour.getInputAmt()));

                view.setCheckAmt(view.getCheckAmt()
                        .subtract(meeting.getCheckAmt())
                        .add(detailLabour.getCheckAmt()));
                //单据主表修改
                pcxBill.setInputAmt(pcxBill.getInputAmt()
                        .subtract(meeting.getInputAmt())
                        .add(detailLabour.getInputAmt()));
                pcxBill.setCheckAmt(pcxBill.getCheckAmt()
                        .subtract(meeting.getCheckAmt())
                        .add(detailLabour.getCheckAmt()));
                //费用明细表修改
                meeting.setInputAmt(detailLabour.getInputAmt());
                meeting.setCheckAmt(detailLabour.getCheckAmt());
            }else{
                //为空的话构建单据明细
                meeting = new PcxBillExpDetailMeeting();
                meeting.setExpDetailCode(expenseTypeCode);
                meeting.setExpDetailName(req.getExpenseTypeName());
                meeting.setLabourCode(labourInfoId);
                meeting.setInputAmt(detailLabour.getInputAmt());
                meeting.setCheckAmt(detailLabour.getCheckAmt());
                PcxBasExpTypeVO pcxBasExpTypeVO = parentMap.get(meeting.getExpDetailCode());
                if (pcxBasExpTypeVO != null) {
                    meeting.setParentCode(pcxBasExpTypeVO.getParentCode());
                    meeting.setLastCode(pcxBasExpTypeVO.getLastCode());
                }
                view.setInputAmt(view.getInputAmt().add(detailLabour.getInputAmt()));

                view.setCheckAmt(view.getCheckAmt()
                        .add(detailLabour.getCheckAmt()));
                pcxBill.setInputAmt(pcxBill.getInputAmt()
                        .add(detailLabour.getInputAmt()));
                pcxBill.setCheckAmt(pcxBill.getCheckAmt()
                        .add(detailLabour.getCheckAmt()));

            }
            detailBean.saveOrUpdate(view, Arrays.asList(meeting), pcxBill);
            meetingBean.saveOrUpdate(view,pcxBill);
        }
        //302160102 构建培训讲课费的费用明细
        if (StringUtil.isNotEmpty(expenseTypeCode) && expenseTypeCode.contains(PcxConstant.TRAINING_EXPENSE_30216)) {
            //通过 detailIds 取查询 费用明细表 ，如果没有数据则进行新增构建（目前正常单条）
            BillExpenseDetailService4Training detailBean = getDetailBean(BillExpenseDetailService4Training.class);
            BillExpenseService4Training trainingBean = getDetailBean(BillExpenseService4Training.class);
            PcxBillExpDetailTraining training = null;
            PcxBillExpTraining view = trainingBean.view(PcxConstant.TRAINING_EXPENSE_30216, pcxBill);
            baseExp = view;
            List<PcxBillExpDetailTraining> pcxBillExpDetailMeetings = detailBean.listByExpenseCode(null, pcxBill);
            //判断是否有关联的明细数据
            if(CollectionUtils.isNotEmpty(pcxBillExpDetailMeetings)){
                training= pcxBillExpDetailMeetings.stream().
                        filter(item -> item.getLabourCode().equals(labourInfoId)).findFirst().orElse(null);
            }
            if (training != null) {
                //修改金额,直接覆盖核定金额。目前是新增修改单据约为上的劳务明细金额
                //费用主表修改 view.getInputAmt() - meeting.getinputAmt() + detailLabour.getInputAmt()) = view.setInputAmt();的值 这是BigDecimal类型
                view.setInputAmt(view.getInputAmt() == null ? BigDecimal.ZERO : training.getInputAmt()
                        .subtract(training.getInputAmt() == null ? BigDecimal.ZERO : training.getInputAmt())
                        .add(detailLabour.getInputAmt()));

                view.setCheckAmt(view.getCheckAmt()
                        .subtract(training.getCheckAmt())
                        .add(detailLabour.getCheckAmt()));
                //单据主表修改
                pcxBill.setInputAmt(pcxBill.getInputAmt()
                        .subtract(training.getInputAmt())
                        .add(detailLabour.getInputAmt()));
                pcxBill.setCheckAmt(pcxBill.getCheckAmt()
                        .subtract(training.getCheckAmt())
                        .add(detailLabour.getCheckAmt()));
                //费用明细表修改
                training.setInputAmt(detailLabour.getInputAmt());
                training.setCheckAmt(detailLabour.getCheckAmt());
                // 设置 checkApprovedAmt
                training.setCheckApprovedAmt(training.getCheckAmt());
                if (training.getInputApprovedAmt() == null || training.getInputApprovedAmt().compareTo(BigDecimal.ZERO) == 0) {
                    training.setInputApprovedAmt(training.getInputAmt());
                }
            }else{
                //为空的话构建单据明细
                training = new PcxBillExpDetailTraining();
                training.setExpDetailCode(expenseTypeCode);
                training.setExpDetailName(req.getExpenseTypeName());
                training.setLabourCode(labourInfoId);
                training.setInputAmt(detailLabour.getInputAmt());
                training.setCheckAmt(detailLabour.getCheckAmt());
                training.setCheckApprovedAmt(detailLabour.getInputAmt());
                training.setInputApprovedAmt(detailLabour.getInputAmt());

                PcxBasExpTypeVO pcxBasExpTypeVO = parentMap.get(training.getExpDetailCode());
                if (pcxBasExpTypeVO != null) {
                    training.setParentCode(pcxBasExpTypeVO.getParentCode());
                    training.setLastCode(pcxBasExpTypeVO.getLastCode());
                }
                view.setInputAmt(view.getInputAmt().add(detailLabour.getInputAmt()));

                view.setCheckAmt(view.getCheckAmt()
                        .add(detailLabour.getCheckAmt()));
                pcxBill.setInputAmt(pcxBill.getInputAmt()
                        .add(detailLabour.getInputAmt()));
                pcxBill.setCheckAmt(pcxBill.getCheckAmt()
                        .add(detailLabour.getCheckAmt()));

            }
            detailBean.saveOrUpdate(view, Arrays.asList(training), pcxBill);
            trainingBean.saveOrUpdate(view,pcxBill);
        }
        //修改主表金额
        billMainService.saveOrUpdate(pcxBill);
        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(pcxBill, Collections.singletonList(baseExp));
        pcxBillExpBasicUtils.batchInsertBillAmtApportion(pcxBill, apportionPair.getLeft(), apportionPair.getRight());
    }

    /**
     * 构建劳务明细数据（抽离方法）
     */
    private PcxBillExpDetailLabour buildDetailLabour(SaveLabourQo req, PcxBill pcxBill, PcxLabourInfo labourInfo,
                                                     SaveLabourQo.LabourExpInfo labourExpInfo, SaveLabourQo.LabourExpDetail labourExpDetail) {
        PcxBillExpDetailLabour detailLabour = new PcxBillExpDetailLabour();
        BeanUtils.copyProperties(labourExpDetail, detailLabour);
        detailLabour.setId(IDGenerator.id());
        detailLabour.setLabourInfoId(labourInfo.getId());
        detailLabour.setBillId(pcxBill.getId());
        detailLabour.setAgyCode(req.getAgyCode());
        detailLabour.setMofDivCode(req.getMofDivCode());
        detailLabour.setFiscal(req.getFiscal());
        detailLabour.setExpDetailCode(req.getExpenseTypeCode());
        detailLabour.setExpDetailName(req.getExpenseTypeName());
        detailLabour.setIdNo(labourExpInfo.getIdNo());
        detailLabour.setBankNo(labourExpDetail.getBankCode());

        // 兼容schema样式块字段
        detailLabour.setStartTime(labourExpInfo.getStartTime());
        detailLabour.setEndTime(labourExpInfo.getEndTime());
        detailLabour.setClassHours(labourExpInfo.getClassHours());
        detailLabour.setDayNum(labourExpInfo.getDayNum());
        detailLabour.setShouldAmt(labourExpInfo.getShouldAmt());
        detailLabour.setRemarks(labourExpInfo.getRemark());
        if(labourExpInfo.getTaxAmt() == null){
            //没有税金，只有实发  然后应发等于实发
            labourExpInfo.setTaxAmt(BigDecimal.ZERO);
            detailLabour.setRealAmt(labourExpInfo.getRealAmt() == null ? BigDecimal.ZERO : labourExpInfo.getRealAmt());
            detailLabour.setShouldAmt(labourExpInfo.getRealAmt());
        }else{
            //存在税金，应发、实发、税金都一定有有
            detailLabour.setTaxAmt(labourExpInfo.getTaxAmt());
            detailLabour.setRealAmt(labourExpInfo.getRealAmt() == null ? BigDecimal.ZERO : labourExpInfo.getRealAmt());
            detailLabour.setShouldAmt(labourExpInfo.getShouldAmt() == null ? BigDecimal.ZERO : labourExpInfo.getShouldAmt());
        }
        detailLabour.setCheckAmt(detailLabour.getShouldAmt());
        detailLabour.setInputAmt(detailLabour.getShouldAmt());
        //判断 实付+税金=应发
        if(detailLabour.getRealAmt().add(detailLabour.getTaxAmt()).compareTo(detailLabour.getShouldAmt()) != 0){
            throw new RuntimeException("实付金额与代缴税金相加不等于应发金额");
        }

        return detailLabour;
    }

    private void deleteExistingDetailLabour(String labourInfoId, String billId) {
        if (StringUtil.isEmpty(labourInfoId) || StringUtil.isEmpty(billId)) {
            return;
        }

        pcxBillExpDetailLabourDao.delete(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                .eq(PcxBillExpDetailLabour::getLabourInfoId, labourInfoId)
                .eq(PcxBillExpDetailLabour::getBillId, billId));
    }

    private void handleAttachmentRelations(SaveLabourQo req, PcxBill pcxBill, String labourInfoId) {
        String billId = pcxBill.getId();
        String agyCode = req.getAgyCode();
        String fiscal = req.getFiscal();
        String mofDivCode = req.getMofDivCode();

        // 删除原有附件关系
        pcxBillExpAttachRelDao.delete(new LambdaQueryWrapper<PcxBillExpAttachRel>()
                .eq(PcxBillExpAttachRel::getRelId, labourInfoId)
                .eq(PcxBillExpAttachRel::getBillId, billId));

        if (CollectionUtils.isNotEmpty(req.getLabourAttachInfoList())) {
            List<PcxBillExpAttachRel> attachRels = req.getLabourAttachInfoList().stream()
                    .map(attach -> {
                        PcxBillExpAttachRel rel = new PcxBillExpAttachRel();
                        rel.setId(IDGenerator.id());
                        rel.setBillId(billId);
                        rel.setAttachId(attach.getAttachId());
                        rel.setFileName(attach.getFileName());
                        rel.setRelId(labourInfoId);
                        rel.setAgyCode(agyCode);
                        rel.setFiscal(fiscal);
                        rel.setMofDivCode(mofDivCode);
                        return rel;
                    })
                    .collect(Collectors.toList());

            batchServiceUtil.batchProcess(attachRels, PcxBillExpAttachRelDao.class, PcxBillExpAttachRelDao::insert);
        }
    }

    @Override
    public void addEcsBills(AddInvoicesQO invoiceQO, List<InvoiceDtoWrapper> wrappers, PcxBill bill) {
        //标准结果
        List<PcxBillExpStandResult> standList = new ArrayList<>();
        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();
        //票的支付信息
        List<PcxEcsSettl> settlList = new ArrayList<>();
        // 获取费用明细信息（非空校验）
        List<PcxBillExpTraining> baseExpList = selectList(
                Arrays.asList(bill.getId()), bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode()
        );
        //查询出报销单的费用数据
        List<PcxBillExpDetailTraining> baseExpDetailList = pcxBillExpDetailTrainingDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailTraining.class)
                .eq(PcxBillExpDetailTraining::getBillId, bill.getId())
                .eq(PcxBillExpDetailTraining::getAgyCode, bill.getAgyCode())
                .eq(PcxBillExpDetailTraining::getFiscal, bill.getFiscal())
                .eq(PcxBillExpDetailTraining::getMofDivCode, bill.getMofDivCode()));
        invoiceQO.setItemCode(bill.getItemCode());
        StartExpenseQO qo = new StartExpenseQO();
        BeanUtils.copyProperties(invoiceQO, qo);

        // 收集票的附件和费用数据
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        List<PcxBillExpDetailBase> allExpDetailBase = new ArrayList<>();

        // 3. 收集所有票生成的费用，明细，并建立票与明细的关联关系
        if (CollectionUtils.isNotEmpty(wrappers)) {
            for (InvoiceDtoWrapper wrapper : wrappers) {
                List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = collectEcsRel(wrapper, attachRelList, invoiceQO, settlList);
                addEcsRel.addAll(pcxExpDetailEcsRels);
                //整理票生成的所有费用，票生成的所有明细
                CollectExpListDto expDto = collectExpBase(wrapper);
                allExpDetailBase.addAll(expDto.getExpDetailList());
                //费用的支出标准列表
                if (CollectionUtils.isNotEmpty(wrapper.getStandList())) {
                    standList.addAll(wrapper.getStandList());
                }
                if (CollectionUtils.isNotEmpty(wrapper.getSettlList())) {
                    settlList.addAll(wrapper.getSettlList());
                }
            }
        }

        // 合并新旧数据
        List<PcxExpDetailEcsRel> allEcsRelList = getEcsRelList(bill.getId());
        allEcsRelList.addAll(addEcsRel);

        List<PcxBillExpAttachRel> allAttachRel = getAttachRelList(bill.getId(), a -> true);
        allAttachRel.addAll(attachRelList);
        //添加之前的费用明细数据
        allExpDetailBase.addAll(baseExpDetailList);

        List<String> allDetailCodes = allExpDetailBase.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());

        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(invoiceQO, allDetailCodes);

        Map<String, PcxBillExpBase> baseExpMap = baseExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        // 7.汇总明细金额到费用上
        summaryExpBaseAmtAndMarkDetail(baseExpMap, allExpDetailBase, parentMap);

        // 8.汇总金额更新报销单金额
        this.collectBillAmt(bill, baseExpList);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(bill, baseExpList);
        // 9.保存相关表结构信息
        this.createTrainingExpenseBill(
                bill,
                baseExpList,
                addEcsRel,
                attachRelList,
                allExpDetailBase,
                parentMap,
                standList,
                settlList,
                null,
                apportionPair.getLeft(),
                apportionPair.getRight()
        );
    }

    @Override
    public CheckMsg<LabourExpDetailVO> viewLabourExpDetail(ViewLabourQo req) {
        PcxLabourInfo labourInfo = pcxLabourInfoDao.selectById(req.getLabourInfoId());
        if (Objects.isNull(labourInfo)) {
            return CheckMsg.fail("未找到对应的劳务人员信息");
        }

        List<PcxBillExpDetailLabour> detailList = pcxBillExpDetailLabourDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                .eq(PcxBillExpDetailLabour::getBillId, req.getBillId())
                .eq(PcxBillExpDetailLabour::getLabourInfoId, req.getLabourInfoId()));

        if (CollectionUtils.isEmpty(detailList)) {
            return CheckMsg.fail("未找到对应的劳务明细信息");
        }

        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(new LambdaQueryWrapper<PcxBillExpAttachRel>()
                .eq(PcxBillExpAttachRel::getRelId, req.getLabourInfoId())
                .eq(PcxBillExpAttachRel::getBillId, req.getBillId()));

        LabourExpDetailVO vo = LabourExpDetailVO.builder()
                .labourExpInfo(buildLabourExpInfo(labourInfo, detailList.get(0)))
                .labourExpDetail(buildLabourExpDetail(detailList.get(0)))
                .labourAttachInfoList(buildAttachInfoVO(attachRelList))
                .labourInfoId(req.getLabourInfoId())
                .expenseTypeCode(detailList.get(0).getExpDetailCode())
                .expenseTypeName(detailList.get(0).getExpDetailName())
                .build();

        return CheckMsg.<LabourExpDetailVO>success().setData(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<PcxBillVO> updateLabourExpDetail(SaveLabourQo req) {
        //TODO 处理单据（目前只有pc调用）
        PcxBill billView = billMainService.view(req.getBillId());
        saveLabourInfo(req, billView);
        // 更新结算方式 劳务的结算方式
        List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(req.getBillId()));

        DefaultSettlementQO settlementQO = new DefaultSettlementQO();
        BeanUtils.copyProperties(req, settlementQO);
        CheckMsg<Map<String, List<PcxBillSettlement>>> mapCheckMsg = pcxBillService.defaultLabourSettlement(settlementQO);
        List<PcxBillSettlement> settlementList = mapCheckMsg.getData().values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<PcxBillSettlementQO> newSettlementList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(settlements)){
            // 循环settlements 集合 isLabour 不等于 “1” 的数据添加到newSettlementList 中
            for (PcxBillSettlement settlement : settlements) {
                if (!Objects.equals(PubConstant.STR_LOGIC_TRUE,settlement.getIsLabour())) {
                    PcxBillSettlementQO newSettlementQO = new PcxBillSettlementQO();
                    BeanUtils.copyProperties(settlement, newSettlementQO);
                    newSettlementList.add(newSettlementQO);
                }
            }
        }
        //将劳务费的进行转换存储进去
        for (PcxBillSettlement settlement : settlementList) {
            PcxBillSettlementQO newSettlementQO = new PcxBillSettlementQO();
            BeanUtils.copyProperties(settlement, newSettlementQO);
            newSettlementList.add(newSettlementQO);
        }
        // 添加劳务结算信息
        //这段代码需要转成 PcxBillSettlementQO
        Map<String, List<PcxBillSettlementQO>> collectMap = newSettlementList.stream().collect(Collectors.groupingBy(PcxBillSettlement::getSettlementType));
        billExpenseCommonService.processSettlement(collectMap, billView,req.getPositionCode());
        //获取最新单据信息
        CheckMsg<PcxBillVO> view = pcxBillService.view(req.getBillId(), Arrays.asList(PositionBlockEnum.EXPENSE_DETAIL.code,
                PositionBlockEnum.BASIC_INFO.getCode(), PositionBlockEnum.BUDGET.getCode(), PositionBlockEnum.FUND_SOURCE.getCode(), PositionBlockEnum.PAY_DETAIL.getCode()));
        if (!view.isSuccess()) {
            return CheckMsg.fail("未找到对应的报销单");
        }

        PcxBillViewQO qo = new PcxBillViewQO();
        BeanUtils.copyProperties(req, qo);
        ThreadLocalUtil.set(qo.getPositionCode());

        //处理经费俩元以及支付明细
        pcxBillViewWrapperUtil.wrap(qo, view.getData());
        PcxBillVO pcxBillVO = view.getData();
        PcxBillCalculateQO calculateQO = new PcxBillCalculateQO();
        calculateQO.setBillId(pcxBillVO.getBillId());
        calculateQO.setExpenseDetail(pcxBillVO.getExpenseDetail());
        PayDetailSaveQO payDetail = new PayDetailSaveQO();
        BeanUtils.copyProperties(pcxBillVO.getPayDetail(), payDetail);
        calculateQO.setPayDetail(payDetail);
        //算最新指标
        List<PcxBillBalanceMergeVO> mergeVOs= pcxBillVO.getFundSource();
        //按照 expenseTypeCodes 进行金额汇总
        BigDecimal totalAmount = pcxBillVO.getExpenseDetail().stream().map(PcxBillExpDetailBase::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        mergeVOs.forEach(item -> {
            // 使用ROUND_HALF_UP舍入模式进行除法运算
            item.setUsedAmt(item.getShareRate().divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP).multiply(totalAmount));
        });
        List<PcxBillBalanceMergeQO> fundSource = pcxBillVO.getFundSource().stream()
                .map(vo -> {
                    PcxBillBalanceMergeQO mergeQO = new PcxBillBalanceMergeQO();
                    BeanUtils.copyProperties(vo, mergeQO);
                    return mergeQO;
                }).collect(Collectors.toList());
        //用最新指标计算明细
        calculateQO.setFundSource(fundSource);
        //计算支付明细
        PcxBillVO calculateBillAmt = pcxBillService.calculateBillAmt(calculateQO);
        pcxBillVO.setPayDetail(calculateBillAmt.getPayDetail());
        pcxBillVO.setFundSource(calculateBillAmt.getFundSource());
        return view;
    }

    public LabourExpDetailVO.LabourExpDetail buildLabourExpDetail(PcxBillExpDetailLabour pcxBillExpDetailLabour) {
        LabourExpDetailVO.LabourExpDetail labourExpDetail = new LabourExpDetailVO.LabourExpDetail();
        labourExpDetail.setSettlementType(pcxBillExpDetailLabour.getSettlementType());
        labourExpDetail.setSettlementName(pcxBillExpDetailLabour.getSettlementName());
        labourExpDetail.setAccountName(pcxBillExpDetailLabour.getAccountName());
        labourExpDetail.setBankName(pcxBillExpDetailLabour.getBankName());
        labourExpDetail.setBankCode(pcxBillExpDetailLabour.getBankNo());
        labourExpDetail.setAccountNo(pcxBillExpDetailLabour.getAccountNo());
        labourExpDetail.setBankNodeCode(pcxBillExpDetailLabour.getBankNodeCode());
        labourExpDetail.setBankNodeName(pcxBillExpDetailLabour.getBankNodeName());
        return labourExpDetail;
    }

    public LabourExpDetailVO.LabourExpInfo buildLabourExpInfo(PcxLabourInfo labourInfo, PcxBillExpDetailLabour pcxBillExpDetailLabour) {
        LabourExpDetailVO.LabourExpInfo labourExpInfo = new LabourExpDetailVO.LabourExpInfo();
        labourExpInfo.setUserName(labourInfo.getUserName());
        labourExpInfo.setLecturerLevelName(labourInfo.getLecturerLevelName());
        labourExpInfo.setLecturerLevel(labourInfo.getLecturerLevel());
        labourExpInfo.setIdType(labourInfo.getIdType());
        labourExpInfo.setIdTypeName(labourInfo.getIdTypeName());
        labourExpInfo.setIdNo(labourInfo.getIdNo());
        labourExpInfo.setOrganization(labourInfo.getOrganization());
        labourExpInfo.setPhoneNo(labourInfo.getPhoneNo());

        labourExpInfo.setStartTime(pcxBillExpDetailLabour.getStartTime());
        labourExpInfo.setEndTime(pcxBillExpDetailLabour.getEndTime());
        labourExpInfo.setDayNum(pcxBillExpDetailLabour.getDayNum());
        labourExpInfo.setClassHours(pcxBillExpDetailLabour.getClassHours());
        labourExpInfo.setShouldAmt(pcxBillExpDetailLabour.getShouldAmt());
        labourExpInfo.setTaxAmt(pcxBillExpDetailLabour.getTaxAmt());
        labourExpInfo.setRealAmt(pcxBillExpDetailLabour.getRealAmt());
        labourExpInfo.setInputAmt(pcxBillExpDetailLabour.getInputAmt());
        labourExpInfo.setCheckAmt(pcxBillExpDetailLabour.getCheckAmt());
        labourExpInfo.setRemark(pcxBillExpDetailLabour.getRemarks());
        return labourExpInfo;
    }

    public List<LabourExpDetailVO.LabourAttachInfo> buildAttachInfoVO(List<PcxBillExpAttachRel> attachRelList) {
        if (CollectionUtils.isEmpty(attachRelList)) {
            return Collections.emptyList();
        }
        return attachRelList.stream()
                .map(rel -> {
                    LabourExpDetailVO.LabourAttachInfo info = new LabourExpDetailVO.LabourAttachInfo();
                    info.setAttachId(rel.getAttachId());
                    info.setFileName(rel.getFileName());
                    return info;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void updateEcsBill(UpdateEcsCommonQO updateQO, PcxBill bill) {
        //转换出费用明细列表
        Map<String, List<PcxBillExpDetailBase>> itemDetailMap = analysisDetailListItemMap(updateQO.getItemExpenseList(), bill,
                updateQO.getExpenseTypeCode(), BillExpDetailSourceEnum.ECS.getCode());

        List<PcxBillExpDetailBase> newDetailList = itemDetailMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());

        //转换出付款信息列表
        List<PcxEcsSettl> ecsSettlList = analysisEcsSettl(updateQO);
        //转换出附件信息列表
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRelList(updateQO.getEcsBillClassRelList(), bill, updateQO.getEcsBillId());
        //转换出同步ecs数据
        UpdateEcsBillDTO updateEcsBillDTO = analysisUpdateEcsBillDTO(ecsSettlList, updateQO);
        //标准
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        //查询出报销单的费用数据
        List<PcxBillExpTraining> billExpList = queryBillExpBaseList(bill);
        List<PcxBillExpDetailBase> oldDetailList = queryBillExpDetailList(bill);

        // 获取事项关联的费用类型（非空校验）
        StartExpenseQO qo = new StartExpenseQO();
        BeanUtils.copyProperties(updateQO, qo);


        //旧费用明细
        List<PcxBillExpDetailBase> delOldDetailList = new ArrayList<>();

        //查询出报销单的票关联关系
        List<PcxExpDetailEcsRel> ecsRelList = expDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, updateQO.getBillId()));

        //找到处理需要修改的票据
        List<PcxExpDetailEcsRel> optEcsRelList = ecsRelList.stream().filter(item->item.getEcsBillId().equals(updateQO.getEcsBillId())
                && item.getEcsBillType().equals(updateQO.getEcsBillType())).collect(Collectors.toList());
        ecsRelList.removeAll(optEcsRelList);
        if (CollectionUtils.isEmpty(optEcsRelList)){
            throw new RuntimeException("未找到匹配的票");
        }
        //修改票的信息
        PcxExpDetailEcsRel rel = optEcsRelList.get(0);

        //根据编辑票明细页面修改的项目的票金额，更新ecsRel里面的ecsAmt
        changeEcsAmt(optEcsRelList, updateQO.getItemExpenseList());

        List<PcxExpDetailEcsRel> newRelList = analysisNewEcsRelEcsItem(optEcsRelList, itemDetailMap, updateQO.getExpenseTypeCode());
        ecsRelList.addAll(newRelList);


        List<String> newDetailListIds  = newDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList = oldDetailList.stream().filter(item->!newDetailListIds.contains(item.getId())).collect(Collectors.toList());
        needSaveOrUpdateDetailList.addAll(newDetailList);

        //TODO 暂时不涉及票费用标准
//        List<PcxBillExpStandResult> delOldStandResultList = getDelDetailStandResult(delOldDetailList);

        List<PcxBillExpAttachRel> allAttachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, updateQO.getBillId()));
        allAttachRelList = allAttachRelList.stream()
                .filter(item-> !item.getRelId().equals(rel.getEcsBillId()))
                .collect(Collectors.toList());
        allAttachRelList.addAll(attachRelList);

        List<String> allDetailCodes = needSaveOrUpdateDetailList.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(updateQO, allDetailCodes);

        Map<String, PcxBillExpBase> baseExpMap = billExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        // 7.汇总明细金额到费用上
        summaryExpBaseAmtAndMarkDetail(baseExpMap, needSaveOrUpdateDetailList, parentMap);

        // 8.汇总金额更新报销单金额
        this.collectBillAmt(bill, billExpList);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(bill, billExpList);
        // 9.保存相关表结构信息
        this.updateTrainingExpenseBill(
                bill,
                billExpList,
                newRelList,
                attachRelList,
                needSaveOrUpdateDetailList,
                parentMap,
                standResultList,
                ecsSettlList,
                updateEcsBillDTO,
                apportionPair.getLeft(),
                apportionPair.getRight(),
                null
        );
    }


    @Override
    public void updateNoEcs(UpdateNoEcsCommonQO commonQO, PcxBill bill) {

        List<PcxBillExpTraining> expBaseList = queryBillExpBaseList(bill);
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(bill.getId());
        List<PcxExpDetailEcsRel> delEcsRel = new ArrayList<>();
        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();
        List<PcxBillExpDetailBase> addDetail = new ArrayList<>();
        List<PcxBillExpDetailBase> allDetail = new ArrayList<>();
        List<PcxBillExpDetailTraining> delDetailList = new ArrayList<>();
        if (StringUtil.isNotEmpty(commonQO.getEcsRelId())) {
            PcxExpDetailEcsRel ecsRel = ecsRelList.stream()
                    .filter(item -> item.getId().equals(commonQO.getEcsRelId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(ecsRel)) {
                throw new RuntimeException("未查询到历史数据");
            }
            BeanUtils.copyProperties(commonQO, ecsRel);
            ecsRel.setEcsAmt(commonQO.getInputAmt());
            ecsRel.setInputAmt(commonQO.getInputAmt());
            ecsRel.setCheckAmt(commonQO.getInputAmt());
            if (StringUtil.isNotEmpty(ecsRel.getManualKey())) {
                delEcsRel = ecsRelList.stream()
                        .filter(item -> item.getManualKey().equals(ecsRel.getManualKey()))
                        .collect(Collectors.toList());
            } else {
                delEcsRel.add(ecsRel);
            }
            List<PcxBillExpDetailTraining> detailCommons = pcxBillExpDetailTrainingService.selectBatchIds(delEcsRel.stream().map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList()));
            delDetailList.addAll(detailCommons);
            Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailTraining>> pair = analysisNoEcsRelAndDetail(delEcsRel, commonQO, bill);
            addEcsRel.addAll(pair.getLeft());
            addDetail.addAll(pair.getRight());
        } else {
            ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
            invoiceQO.setFiscal(bill.getFiscal());
            invoiceQO.setMofDivCode(bill.getMofDivCode());
            invoiceQO.setAgyCode(bill.getAgyCode());
            PcxExpDetailEcsRel ecsRel = new PcxExpDetailEcsRel();
            BeanUtils.copyProperties(commonQO, ecsRel);
            ecsRel.setId(IDGenerator.id());
            ecsRel.setEcsAmt(commonQO.getInputAmt());
            ecsRel.setInputAmt(commonQO.getInputAmt());
            ecsRel.setCheckAmt(commonQO.getInputAmt());
            ecsRel.setEcsContent("");
            ecsRel.setBillId(bill.getId());
            ecsRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            ecsRel.setFiscal(bill.getFiscal());
            ecsRel.setAgyCode(bill.getAgyCode());
            ecsRel.setMofDivCode(bill.getMofDivCode());
            ecsRel.setManualKey(UUID.randomUUID().toString());
            if (CollectionUtils.isNotEmpty(commonQO.getDetailList())) {
                int index = 0;
                for (EcsCommonDetailVO detailVO : commonQO.getDetailList()) {
                    PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(ecsRel, copyRel);
                    copyRel.setId(IDGenerator.id());
                    PcxBillExpDetailTraining detail = initRelDetail(copyRel, invoiceQO);
                    BeanUtils.copyProperties(detailVO, detail);
                    detail.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                    detail.setExpSource(BillExpDetailSourceEnum.MANUAL.getCode());
                    if (index > 0){
                        copyRel.setEcsAmt(BigDecimal.ZERO);
                        copyRel.setInputAmt(BigDecimal.ZERO);
                        copyRel.setCheckAmt(BigDecimal.ZERO);
                        detail.setInputAmt(BigDecimal.ZERO);
                        detail.setCheckAmt(BigDecimal.ZERO);
                    }
                    addEcsRel.add(copyRel);
                    addDetail.add(detail);
                    index++;
                }
            } else {
                PcxBillExpDetailTraining detail = initRelDetail(ecsRel, invoiceQO);
                detail.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                detail.setExpSource(BillExpDetailSourceEnum.MANUAL.getCode());
                addDetail.add(detail);
                addEcsRel.add(ecsRel);
            }
        }
        ecsRelList.removeAll(delEcsRel);
        ecsRelList.addAll(addEcsRel);

        collectBillAmt(bill, expBaseList, ecsRelList);
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRel(commonQO.getAttachList(), addEcsRel.get(0).getManualKey(), bill);
        List<PcxBillExpAttachRel> allAttachRelList = getAttachRelList(bill.getId(), getDelExtraAttachRelPredicate(addEcsRel.get(0).getManualKey()));
        allAttachRelList.addAll(attachRelList);

        List<PcxBillExpDetailTraining> oldbaseExpDetailList = pcxBillExpDetailTrainingDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailTraining.class)
                .eq(PcxBillExpDetailTraining::getBillId, bill.getId())
                .eq(PcxBillExpDetailTraining::getAgyCode, bill.getAgyCode())
                .eq(PcxBillExpDetailTraining::getFiscal, bill.getFiscal())
                .eq(PcxBillExpDetailTraining::getMofDivCode, bill.getMofDivCode()));
        allDetail.addAll(oldbaseExpDetailList);
        allDetail.addAll(addDetail);
        List<String> allDetailCodes = allDetail.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());

        ExpInvoiceQO expInvoiceQO = new ExpInvoiceQO();
        expInvoiceQO.setAgyCode(bill.getAgyCode());
        expInvoiceQO.setFiscal(bill.getFiscal());
        expInvoiceQO.setMofDivCode(bill.getMofDivCode());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(expInvoiceQO, allDetailCodes);

        Map<String, PcxBillExpBase> baseExpMap = expBaseList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        summaryExpBaseAmtAndMarkDetail(baseExpMap, allDetail, parentMap);

        // 8. 汇总票据金额
        this.collectBillAmt(bill, expBaseList);

        this.updateNoEcsCommon(
                bill,
                expBaseList,
                addEcsRel,
                allAttachRelList,
                addDetail,
                parentMap,
                delDetailList
        );
    }


    private Predicate<PcxBillExpAttachRel> getDelExtraAttachRelPredicate(String relId){
        return (a)-> Objects.equals(a.getRelType(), 1) || !Objects.equals(a.getRelId(), relId);
    }

    /**
     * 生成附件关联信息
     * @param attachList
     * @param relKey
     * @param bill
     * @return
     */
    private List<PcxBillExpAttachRel> analysisAttachRel(List<DetailAttachRelQO> attachList, String relKey, PcxBill bill) {
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachList)) {
            for (DetailAttachRelQO attachRelQO : attachList) {
                PcxBillExpAttachRel rel = new PcxBillExpAttachRel();
                rel.setId(IDGenerator.id());
                rel.setRelType(PcxExpAttachRelType.EXP_DETAIL.getCode());
                rel.setBillId(bill.getId());
                rel.setRelId(relKey);
                rel.setAttachId(attachRelQO.getFileId());
                rel.setFileName(attachRelQO.getFileName());
                rel.setFiscal(bill.getFiscal());
                rel.setAgyCode(bill.getAgyCode());
                rel.setMofDivCode(bill.getMofDivCode());
                rel.setTenantId(bill.getTenantId());
                attachRelList.add(rel);
            }
        }
        return attachRelList;
    }

    private void collectBillAmt(PcxBill bill, List<PcxBillExpTraining> baseExpList, List<PcxExpDetailEcsRel> allEcsRel) {
        Map<String, BigDecimal> inputAmtMap = new HashMap<>();
        Map<String, BigDecimal> checkAmtMap = new HashMap<>();
        for (PcxExpDetailEcsRel rel : allEcsRel) {
            String expTypeCode = StringUtil.isEmpty(rel.getExpenseTypeCode()) ? PcxConstant.UNIVERSAL_EXPENSE_CODE : rel.getExpenseTypeCode();
            BigDecimal inputAmt = inputAmtMap.getOrDefault(expTypeCode, BigDecimal.ZERO);
            inputAmtMap.put(expTypeCode, inputAmt.add(rel.getInputAmt()));
            BigDecimal checkAmt = checkAmtMap.getOrDefault(expTypeCode, BigDecimal.ZERO);
            checkAmtMap.put(expTypeCode, checkAmt.add(rel.getCheckAmt()));
        }
        BigDecimal totalInputAmt = BigDecimal.ZERO;
        BigDecimal totalCheckAmt = BigDecimal.ZERO;
        for (PcxBillExpTraining expBase : baseExpList) {
            expBase.setInputAmt(inputAmtMap.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO));
            expBase.setCheckAmt(checkAmtMap.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO));
            totalInputAmt = totalInputAmt.add(expBase.getInputAmt());
            totalCheckAmt = totalCheckAmt.add(expBase.getCheckAmt());
        }
        bill.setInputAmt(totalInputAmt);
        bill.setCheckAmt(totalCheckAmt);
    }

    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailTraining>> analysisNoEcsRelAndDetail(List<PcxExpDetailEcsRel> optEcsRelList,
                                                                                                    UpdateNoEcsCommonQO noEcsCommonQO,
                                                                                                    PcxBill bill) {
        List<PcxExpDetailEcsRel> addEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailTraining> addDetailList = new ArrayList<>();
        PcxExpDetailEcsRel rel = optEcsRelList.get(0);
        PcxExpDetailEcsRel tempRel = new PcxExpDetailEcsRel();
        BeanUtils.copyProperties(rel, tempRel);
        tempRel.setId(IDGenerator.id());
        tempRel.setInputAmt(noEcsCommonQO.getInputAmt());
        tempRel.setCheckAmt(noEcsCommonQO.getInputAmt());
        tempRel.setRemark(noEcsCommonQO.getRemark());
        tempRel.setExpenseTypeCode(noEcsCommonQO.getExpenseTypeCode());
        tempRel.setExpenseTypeName(noEcsCommonQO.getExpenseTypeName());
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        invoiceQO.setFiscal(bill.getFiscal());
        invoiceQO.setMofDivCode(bill.getMofDivCode());
        invoiceQO.setAgyCode(bill.getAgyCode());
        if (CollectionUtils.isNotEmpty(noEcsCommonQO.getDetailList())) {
            int index = 0;
            for (EcsCommonDetailVO detailVO : noEcsCommonQO.getDetailList()) {
                PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                BeanUtils.copyProperties(tempRel, copyRel);
                copyRel.setId(IDGenerator.id());
                PcxBillExpDetailTraining common = initRelDetail(copyRel, invoiceQO);
                common.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                BeanUtils.copyProperties(detailVO, common);
                if (index > 0) {
                    copyRel.setEcsAmt(BigDecimal.ZERO);
                    copyRel.setInputAmt(BigDecimal.ZERO);
                    copyRel.setCheckAmt(BigDecimal.ZERO);
                    common.setInputAmt(BigDecimal.ZERO);
                    common.setCheckAmt(BigDecimal.ZERO);
                }
                index++;
                addDetailList.add(common);
                addEcsRelList.add(copyRel);
            }
        } else {
            PcxBillExpDetailTraining common = initRelDetail(tempRel, invoiceQO);
            common.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
            addDetailList.add(common);
            addEcsRelList.add(tempRel);
        }
        return Pair.of(addEcsRelList, addDetailList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateNoEcsCommon(PcxBill bill,
                                  List<PcxBillExpTraining> baseExpList,
                                  List<PcxExpDetailEcsRel> allEcsRel,
                                  List<PcxBillExpAttachRel> attachRelList,
                                  List<PcxBillExpDetailBase> detailCommonList,
                                  Map<String, PcxBasExpTypeVO> parentMap,
                                  List<PcxBillExpDetailTraining> delDetailList) {
        if(CollectionUtil.isEmpty(allEcsRel)){
            throw new CommonException("没有要更新的发票");
        }
        // 保存主单据信息
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);

        PcxExpDetailEcsRel rel = allEcsRel.get(0);
        //删除老的票关联关系
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, bill.getId())
                .eq(PcxExpDetailEcsRel::getEcsBillId, rel.getEcsBillId()));

        //删除旧的支付信息
        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, bill.getId())
                .eq(PcxEcsSettl::getEcsBillId, rel.getEcsBillId()));
        //删除旧的附件关系
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, rel.getEcsBillId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));

        // 保存会议费费用主单据信息
        saveOrUpdateTrainingDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        //删除需要删的明细
        if(CollectionUtil.isNotEmpty(delDetailList)){
            pcxBillExpDetailTrainingService.deleteBatchIds(delDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList()));
        }
        // 保存会议费费用明细信息
        saveOrUpdateTrainingDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        ecsExpTransService.batchInsertAttachRel(attachRelList, pcxBill.getId());

        // 保存ECS关系表信息
        pcxBillExpBasicUtils.batchInsertNewEcsRel(allEcsRel, pcxBill.getId());

    }


    @Transactional(rollbackFor = Exception.class)
    public void updateTrainingExpenseBill(PcxBill bill,
                                          List<PcxBillExpTraining> baseExpList,
                                          List<PcxExpDetailEcsRel> allEcsRel,
                                            List<PcxBillExpAttachRel> attachRelList,
                                          List<PcxBillExpDetailBase> detailCommonList,
                                            Map<String, PcxBasExpTypeVO> parentMap,
                                          List<PcxBillExpStandResult> standList,
                                          List<PcxEcsSettl> settlList,
                                            UpdateEcsBillDTO updateEcsBillDTO,
                                          List<PcxBillAmtApportion> apportions,
                                          List<PcxBillAmtApportionDepartment> apportionDepartmentList,
                                          List<PcxBillExpDetailTraining> delDetailList) {
        // 保存主单据信息
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);
        PcxExpDetailEcsRel rel = allEcsRel.get(0);
        //删除老的票关联关系
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, bill.getId())
                .eq(PcxExpDetailEcsRel::getEcsBillId, rel.getEcsBillId()));

        //删除旧的支付信息
        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, bill.getId())
                .eq(PcxEcsSettl::getEcsBillId, rel.getEcsBillId()));
        //删除旧的附件关系
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, rel.getEcsBillId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));

        // 保存会议费费用主单据信息
        saveOrUpdateExpTraining(baseExpList, pcxBill);

        // 保存会议费费用明细信息
        saveOrUpdateTrainingDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        // 构建报销单和ecs发票关系
        pcxBillExpBasicUtils.buildEcsRel(pcxBill, allEcsRel, attachRelList);

        // 保存ECS关系表信息
        pcxBillExpBasicUtils.batchInsertNewEcsRel(allEcsRel, pcxBill.getId());
        // 保存ecs支付信息
        pcxBillExpBasicUtils.batchInsertEcsSettlement(settlList, pcxBill.getId());
        // 保存支出标准
        pcxBillExpBasicUtils.batchInsertStandResult(standList, pcxBill.getId());
        pcxBillExpBasicUtils.batchInsertBillAmtApportion(pcxBill, apportions, apportionDepartmentList);
        // 同步ecs
        if (null != updateEcsBillDTO) {
            Response response = ecsBillExternalService.expInfoSave(updateEcsBillDTO);
            if (!response.getCode().equals(Response.SUCCESS_CODE)) {
                log.error("同步电子凭证信息失败 {}", response.getMsg());
                throw new RuntimeException("同步电子凭证信息失败");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String createTrainingExpenseBill(PcxBill bill,
                                            List<PcxBillExpTraining> baseExpList,
                                            List<PcxExpDetailEcsRel> allEcsRel,
                                           List<PcxBillExpAttachRel> attachRelList,
                                            List<PcxBillExpDetailBase> detailCommonList,
                                           Map<String, PcxBasExpTypeVO> parentMap,
                                            List<PcxBillExpStandResult> standList,
                                            List<PcxEcsSettl> settlList,
                                           UpdateEcsBillDTO updateEcsBillDTO,
                                            List<PcxBillAmtApportion> apportions,
                                            List<PcxBillAmtApportionDepartment> apportionDepartmentList) {
        // 保存主单据信息
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);

        // 保存会议费费用主单据信息
        saveOrUpdateExpTraining(baseExpList, pcxBill);

        // 保存会议费费用明细信息
        saveOrUpdateTrainingDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        // 构建报销单和ecs发票关系
        pcxBillExpBasicUtils.buildEcsRel(pcxBill, allEcsRel, attachRelList);

        // 保存ECS关系表信息
        pcxBillExpBasicUtils.batchInsertNewEcsRel(allEcsRel, pcxBill.getId());
        // 保存ecs支付信息
        pcxBillExpBasicUtils.batchInsertEcsSettlement(settlList, pcxBill.getId());
        // 保存支出标准
        pcxBillExpBasicUtils.batchInsertStandResult(standList, pcxBill.getId());
        pcxBillExpBasicUtils.batchInsertBillAmtApportion(pcxBill, apportions, apportionDepartmentList);
        // 同步ecs
        if (null != updateEcsBillDTO) {
            Response response = ecsBillExternalService.expInfoSave(updateEcsBillDTO);
            if (!response.getCode().equals(Response.SUCCESS_CODE)) {
                log.error("同步电子凭证信息失败 {}", response.getMsg());
                throw new RuntimeException("同步电子凭证信息失败");
            }
        }
        return pcxBill.getId();
    }

    private void saveOrUpdateExpTraining(Collection<PcxBillExpTraining> values, PcxBill view) {
        String targetCode = "30216";
        List<PcxBillExpTraining> filteredList = values.stream()
                .filter(e -> targetCode.equals(e.getExpenseCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            log.error("未找到 expenseCode={} 的费用记录", targetCode);
            return;
        }

        // 如果有多个匹配项，取第一个作为基准
        PcxBillExpTraining target = filteredList.get(0);

        // 将所有费用的金额累加到 30216 上
        BigDecimal totalInputAmt = values.stream()
                .map(PcxBillExpTraining::getInputAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCheckAmt = values.stream()
                .map(PcxBillExpTraining::getCheckAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        target.setInputAmt(totalInputAmt);
        target.setCheckAmt(totalCheckAmt);
        target.setBillId(view.getId());

        // 保存或更新这条记录
        PcxBillExpTraining existing = pcxBillExpTrainingDao.selectById(target.getId());
        if (existing == null) {
            pcxBillExpTrainingDao.insert(target);
        } else {
            pcxBillExpTrainingDao.updateById(target);
        }
    }

    private void saveOrUpdateTrainingDetail(List<PcxBillExpTraining> PcxBillExpMeetings,
                                           List<PcxBillExpDetailBase> detailCommonList, PcxBill pcxBill, Map<String, PcxBasExpTypeVO> parentMap)  {

        Map<String, List<PcxBillExpDetailBase>> detailMap = detailCommonList.stream()
                .collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode()).getLastCode()));

        Map<String, PcxBillExpBase> baseMap = PcxBillExpMeetings.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));
        // 在保存前统一设置 billId,parentCode
        for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : detailMap.entrySet()) {
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(entry.getKey());
            PcxBillExpBase pcxBillExpBase = baseMap.get(entry.getKey());
            //在这里面 entry 做类型实体转换 然后赋值 pranteCode 和 billId
            List<PcxBillExpDetailBase> saveValue = entry.getValue();
            for (PcxBillExpDetailBase value : saveValue) {
                PcxBillExpDetailTraining metting = (PcxBillExpDetailTraining) value;
                metting.setBillId(pcxBill.getId());
                PcxBasExpTypeVO pcxBasExpTypeVO = parentMap.get(value.getExpDetailCode());
                if (pcxBasExpTypeVO != null) {
                    metting.setExpDetailName(pcxBasExpTypeVO.getExpenseName());
                    metting.setParentCode(pcxBasExpTypeVO.getParentCode());
                    metting.setLastCode(pcxBasExpTypeVO.getLastCode());
                }
            }
            detailBean.saveOrUpdate(pcxBillExpBase, saveValue, pcxBill);
        }
    }


    private Map<String, List<PcxBillExpDetailBase>> analysisDetailListItemMap(List<EcsItemExpense> itemExpenseList, PcxBill view,
                                                                              String expDetailCode,
                                                                              String source) {
        Map<String, List<PcxBillExpDetailBase>> result = new HashMap<>();
        BigDecimal detailInputAmt = BigDecimal.ZERO;
        BigDecimal allEcsAmt = BigDecimal.ZERO;
        for (EcsItemExpense itemExpense : itemExpenseList) {
            JSONArray expenseList = itemExpense.getExpenseList();
            BigDecimal ecsAmt = itemExpense.getEcsAmt();
            allEcsAmt = allEcsAmt.add(ecsAmt);
            if (Objects.isNull(expenseList) || expenseList.isEmpty()){
                result.put(itemExpense.getEcsDetailId(), new ArrayList<>());
                continue;
            }
            LinkedHashMap<String, Object> detailLinkedHashMap = (LinkedHashMap<String, Object>) expenseList.get(0);
            JSONObject detailJson = new JSONObject(detailLinkedHashMap);
            PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
            qo.setExpenseCode(expDetailCode);
            qo.setAgyCode(view.getAgyCode());
            qo.setMofDivCode(view.getMofDivCode());
            qo.setFiscal(view.getFiscal());
            qo.setTenantId(view.getTenantId());
            PcxBasExpType baseExpByQO = basExpTypeDao.getBaseExpByQO(qo);
            List<PcxBillExpDetailBase> detailBaseList = new ArrayList<>();
            String uuid = UUID.randomUUID().toString();
            int seq = 1;
            for (Object o : expenseList) {
                detailJson = new JSONObject((LinkedHashMap<String, Object> )o);
                PcxBillExpDetailBase detailBase = ExpenseBeanUtil.getEntityDetailBean(baseExpByQO.getParentCode());
                detailBase = JSON.parseObject(detailJson.toJSONString(), detailBase.getClass());
                detailBase.setId(StringUtils.isEmpty(detailBase.getId()) ? IDGenerator.id() : detailBase.getId());
                detailBase.setExpenseId("");
                detailBase.setExpDetailCode(expDetailCode);
                detailBase.setInputAmt(Objects.isNull(detailBase.getInputAmt()) ? BigDecimal.ZERO : detailBase.getInputAmt());
                detailBase.setEcsAmt(Objects.isNull(detailBase.getEcsAmt()) ? ecsAmt : detailBase.getEcsAmt());
                detailInputAmt = detailInputAmt.add(detailBase.getInputAmt());
                detailBase.setCheckAmt(detailBase.getInputAmt());
                detailBase.setInputApprovedAmt(detailBase.getInputAmt());
                detailBase.setCheckApprovedAmt(detailBase.getInputAmt());
                detailBase.setSource(source);
                detailBaseList.add(detailBase);

            }
            if (CollectionUtils.isNotEmpty(detailBaseList)){
                for (PcxBillExpDetailBase detailBase : detailBaseList) {
                    detailBase.setFiscal(view.getFiscal());
                    detailBase.setMofDivCode(view.getMofDivCode());
                    detailBase.setAgyCode(view.getAgyCode());
                    detailBase.setTenantId(view.getTenantId());
                }
                BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(baseExpByQO.getParentCode());
                CheckMsg<Void> validate = detailBean.validate(detailBaseList, FormSettingEnums.BillFuncCodeEnum.EXPENSES_BILL.getCode());
                if (!validate.isSuccess()){
                    throw new RuntimeException(validate.getMsgInfo());
                }
            }
            result.put(itemExpense.getEcsDetailId(), detailBaseList);
        }
        if (detailInputAmt.compareTo(allEcsAmt) >0){
            throw new RuntimeException("报销金额不能大于发票金额");
        }
        return result;
    }

    private List<PcxBillExpTraining> buildBaseExpByExpType(List<PcxBasItemExp> expTypeList, ExpInvoiceQO qo) {
        String defaultKey = PcxConstant.UNIVERSAL_EXPENSE_CODE;

        return expTypeList.stream()
                .map(basItemExp -> {
                    PcxBillExpTraining entityBean = new PcxBillExpTraining();
                    BeanUtils.copyProperties(basItemExp, entityBean);
                    entityBean.setId(IDGenerator.id());
                    entityBean.setInputAmt(BigDecimal.ZERO);
                    entityBean.setFiscal(qo.getFiscal());
                    entityBean.setAgyCode(qo.getAgyCode());
                    entityBean.setMofDivCode(qo.getMofDivCode());
                    return entityBean;
                })
                .collect(Collectors.toList());
    }

    private void collectBillAmt(PcxBill bill, List<PcxBillExpTraining> baseExpList) {
        BigDecimal totalInputAmt = BigDecimal.ZERO;
        BigDecimal totalCheckAmt = BigDecimal.ZERO;

        for (PcxBillExpTraining expBase : baseExpList) {
            totalInputAmt = totalInputAmt.add(expBase.getInputAmt() == null ? BigDecimal.ZERO: expBase.getInputAmt());
            totalCheckAmt = totalCheckAmt.add(expBase.getCheckAmt() == null ? BigDecimal.ZERO: expBase.getCheckAmt());
        }

        bill.setInputAmt(totalInputAmt);
        bill.setCheckAmt(totalCheckAmt);
    }

    private PcxBill buildBill(StartExpenseQO expenseQO, List<PcxBasItemExp> pcxBasItemExps) {
        MadEmployeeDTO madEmployeeDTO = queryMadEmpDTO(expenseQO);

        return PcxBill.builder()
                .agyCode(expenseQO.getAgyCode())
                .fiscal(expenseQO.getFiscal())
                .mofDivCode(expenseQO.getMofDivCode())
                .itemCode(expenseQO.getItemCode())
                .itemName(expenseQO.getItemName())
                .claimantCode(madEmployeeDTO.getEmployeeCode())
                .claimantName(madEmployeeDTO.getEmployeeName())
                .departmentCode(madEmployeeDTO.getDepartmentCode())
                .departmentName(madEmployeeDTO.getDepartmentName())
                .billFuncCode(BillFuncCodeEnum.EXPENSE.getCode())
                .billFuncName(BillFuncCodeEnum.EXPENSE.getName())
                .transDate(expenseQO.getTransDate())
                .createdTime(DateUtil.nowTime())
                .creator(expenseQO.getUserCode())
                .creatorName(expenseQO.getUserName())
                .modifiedTime(DateUtil.nowTime())
                .modifier(expenseQO.getUserCode())
                .modifierName(expenseQO.getUserName())
                .reason("")
                .bizType(ItemBizTypeEnum.TRAINING.getCode())
                .bizTypeName(ItemBizTypeEnum.TRAINING.getName())
                .expenseCodes(pcxBasItemExps.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.joining(",")))
                .expenseNames(pcxBasItemExps.stream().map(PcxBasItemExp::getExpenseName).collect(Collectors.joining(",")))
                .build();
    }

    private MadEmployeeDTO queryMadEmpDTO(StartExpenseQO expenseQO) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setFiscal(expenseQO.getFiscal());
        pcxBaseDTO.setAgyCode(expenseQO.getAgyCode());
        pcxBaseDTO.setMofDivCode(expenseQO.getMofDivCode());
        return madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, expenseQO.getUserCode());
    }

    private void disposeDetailDepartmentCode(PcxBill bill, List<PcxBillExpDetailBase> detailList) {
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (PcxBillExpDetailBase common : detailList) {
                common.setDepartmentCode(bill.getDepartmentCode());
                common.setDepartmentName(bill.getDepartmentName());
            }
        }
    }


    private List<PcxExpDetailEcsRel> collectEcsRel(InvoiceDtoWrapper wrapper,
                                                   List<PcxBillExpAttachRel> attachRelList,
                                                   ExpInvoiceQO invoiceQO,
                                                   List<PcxEcsSettl> settlList) {
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        //给票建立一个基础的关联，因为票有可能关联多个费用明细，后面直接copy它
        List<PcxExpDetailEcsRel> copyRel = getEcsRel(wrapper, attachRelList, settlList);
        for (PcxBillExpAttachRel attachRel : attachRelList) {
            attachRel.setFiscal(invoiceQO.getFiscal());
            attachRel.setAgyCode(invoiceQO.getAgyCode());
            attachRel.setMofDivCode(invoiceQO.getMofDivCode());
            attachRel.setTenantId(invoiceQO.getTenantId());
        }
        for (PcxEcsSettl ecsSettl : settlList) {
            ecsSettl.setFiscal(invoiceQO.getFiscal());
            ecsSettl.setAgyCode(invoiceQO.getAgyCode());
            ecsSettl.setMofDivCode(invoiceQO.getMofDivCode());
        }
        if (CollectionUtils.isEmpty(copyRel)){
            return result;
        }
        copyRel.forEach(item->{
            item.setFiscal(invoiceQO.getFiscal());
            item.setAgyCode(invoiceQO.getAgyCode());
            item.setMofDivCode(invoiceQO.getMofDivCode());
            item.setExpenseTypeCode("");
            item.setExpenseTypeName("");
            item.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
        });

        if (Objects.nonNull(wrapper.getMatchExpType())){
            //未匹配的票有可能是匹配的费用类型，但是生成明细时数据不完整，保存费用类型，和费用明细
            copyRel.forEach(item->{
                item.setExpenseTypeCode(wrapper.getMatchExpType().getExpenseCode());
                item.setExpenseTypeName(wrapper.getMatchExpType().getExpenseName());
            });
            if (CollectionUtils.isNotEmpty(wrapper.getExpDetailList())){
                copyRel.forEach(item->{
                    item.setInputAmt(BigDecimal.ZERO);
                    item.setCheckAmt(BigDecimal.ZERO);
                });
                copyRel.get(0).setEcsContent(JSON.toJSONString(wrapper.getExpDetailList()));
                BigDecimal inputAmt = BigDecimal.ZERO;
                for (Object o : wrapper.getExpDetailList()) {
                    PcxBillExpDetailBase detailBase = (PcxBillExpDetailBase) o;
                    if (Objects.nonNull(detailBase.getInputAmt())){
                        inputAmt = inputAmt.add(detailBase.getInputAmt());
                    }
                }
                copyRel.get(0).setInputAmt(inputAmt);
            }
        }
        if (Objects.equals(wrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            //如果是匹配的票，则把上面保存的费用明细去掉，不需要保存在票里面
            copyRel.forEach(item->{
                item.setEcsContent("");
                item.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            });
            //解析出的费用明细都挂到第一个项目上
            PcxExpDetailEcsRel firstEcsRel = copyRel.get(0);
            if (CollectionUtils.isNotEmpty(wrapper.getExpDetailList())){
                for (Object o : wrapper.getExpDetailList()) {
                    PcxBillExpDetailBase detailBase = (PcxBillExpDetailBase) o;
                    PcxExpDetailEcsRel rel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(firstEcsRel, rel);
                    rel.setId(IDGenerator.id());
                    rel.setDetailId(detailBase.getId());
                    rel.setExpenseTypeCode(detailBase.getExpDetailCode());
                    rel.setInputAmt(detailBase.getInputAmt());
                    rel.setCheckAmt(detailBase.getInputAmt());
                    rel.setEmpCode(detailBase.getEmpCode());
                    result.add(rel);
                }
            }
            if (CollectionUtils.isNotEmpty(wrapper.getExpenseList())){
                for (Object o : wrapper.getExpenseList()) {
                    PcxBillExpBase expBase = (PcxBillExpBase) o;
                    PcxExpDetailEcsRel rel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(copyRel, rel);
                    rel.setId(IDGenerator.id());
                    rel.setExpenseTypeCode(expBase.getExpenseCode());
                    rel.setItemName(expBase.getEcsRelItemName());
                    result.add(rel);
                }
            }
            //如果条数大于1，则进行了copy，把第一条去掉
            if (copyRel.size()>1){
                result.addAll(copyRel.subList(1, copyRel.size()));
            }
        }else{
            //未匹配的票记录一下票信息
            result.addAll(copyRel);
        }
        return result;
    }

    private List<PcxExpDetailEcsRel> getEcsRel(InvoiceDtoWrapper wrapper, List<PcxBillExpAttachRel> attachRelList, List<PcxEcsSettl> settlList) {
        EcsDtoTransHelper.EcsBillDispose ecsBillDispose = EcsBillExternalServiceImpl.getEcsBillDisposeMap().get(wrapper.getType());
        if (Objects.nonNull(ecsBillDispose)){
            return ecsBillDispose.initEcsRelListTravel(wrapper.getDto(), attachRelList, settlList, Lists.newArrayList(), new HashMap<>());
        }
        return Lists.newArrayList();
    }

    /**
     * 根据 ecsRel 初始化费用明细对象 PcxBillExpDetailTraining
     */
    private PcxBillExpDetailTraining initRelDetail(PcxExpDetailEcsRel rel, ExpInvoiceQO invoiceQO) {
        PcxBillExpDetailTraining detail = new PcxBillExpDetailTraining();
        detail.setId(IDGenerator.id());
        detail.setEcsAmt(rel.getEcsAmt());
        detail.setInputAmt(rel.getInputAmt());
        detail.setCheckAmt(rel.getCheckAmt());
        // 设置 checkApprovedAmt
        if (detail.getCheckApprovedAmt() == null || detail.getCheckApprovedAmt().compareTo(BigDecimal.ZERO) == 0) {
            detail.setCheckApprovedAmt(rel.getCheckAmt());
        }
        // 设置 inputApprovedAmt
        if (detail.getInputApprovedAmt() == null || detail.getInputApprovedAmt().compareTo(BigDecimal.ZERO) == 0) {
            detail.setInputApprovedAmt(rel.getInputAmt());
        }
        detail.setExpDetailCode(rel.getExpenseTypeCode());
        detail.setExpenseTypeCode(rel.getExpenseTypeCode());
        detail.setExpenseTypeName(rel.getExpenseTypeName());
        detail.setParentCode(rel.getParentCode());
        detail.setLastCode(rel.getLastCode());
        detail.setSource(BillExpDetailSourceEnum.ECS.getCode());
        detail.setFiscal(invoiceQO.getFiscal());
        detail.setAgyCode(invoiceQO.getAgyCode());
        detail.setMofDivCode(invoiceQO.getMofDivCode());
        detail.setTaxAmt(rel.getTaxAmt());
        detail.setTaxRate(rel.getTaxRate());

        rel.setDetailId(detail.getId());
        // 设置扩展字段默认值（如果有）
        if (rel.getExtItemDefValMap() != null) {
            rel.getExtItemDefValMap().forEach((k, v) -> {
                try {
                    ReflectUtil.setFieldValue(detail, k, v);
                } catch (Exception e) {
                    log.error("设置扩展字段失败：{}", e.getMessage(), e);
                }
            });
        }
        return detail;
    }

    /**
     * TODO （待抽出公共方法）获取票据关系列表
     */
    private List<PcxExpDetailEcsRel> getEcsRelList(String billId) {
        return expDetailEcsRelDao.selectByBillId(billId);
    }

    private List<PcxBasExpType> collectEcsRelExpType(List<PcxExpDetailEcsRel> allEcsRel) {
        List<PcxBasExpType> result = new ArrayList<>();
        Set<String> expenseTypeCodeSet = new HashSet<>();
        allEcsRel.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode()))
                .forEach(item -> {
                    if (!expenseTypeCodeSet.contains(item.getExpenseTypeCode())) {
                        expenseTypeCodeSet.add(item.getExpenseTypeCode());
                        PcxBasExpType expType = new PcxBasExpType();
                        expType.setExpenseCode(item.getExpenseTypeCode());
                        expType.setExpenseName(item.getExpenseTypeName());
                        result.add(expType);
                    }
                });
        return result;
    }

    /**
     * （TODO (待抽出公共方法）获取单据关联票据的关系
     *
     * @param billId
     * @param filter
     * @return
     */
    private List<PcxBillExpAttachRel> getAttachRelList(String billId, Predicate<PcxBillExpAttachRel> filter) {
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, billId));
        return attachRelList.stream().filter(filter).collect(Collectors.toList());
    }

    /**
     * （TODO (待抽出公共方法） 添加票时，可能会产生新的费用类型，需要添加到主单
     *
     * @param baseExpList
     * @param expTypeList
     * @param bill
     */
    private void fillNoExistsExpType(List<PcxBillExpTraining> baseExpList, List<PcxBasExpType> expTypeList, PcxBill bill) {
        List<String> collect = baseExpList.stream().map(PcxBillExpBase::getExpenseCode).collect(Collectors.toList());
        expTypeList.stream().filter(item -> !collect.contains(item.getExpenseCode())).forEach(item -> {
            PcxBillExpTraining base = new PcxBillExpTraining();
            base.setId(IDGenerator.id());
            base.setBillId(bill.getId());
            base.setExpenseCode(item.getExpenseCode());
            base.setExpenseName(item.getExpenseName());
            base.setAgyCode(bill.getAgyCode());
            base.setFiscal(bill.getFiscal());
            base.setMofDivCode(bill.getMofDivCode());
            baseExpList.add(base);
        });
        bill.setExpenseCodes(baseExpList.stream().map(PcxBillExpBase::getExpenseCode).collect(Collectors.joining(",")));
        bill.setExpenseNames(baseExpList.stream().map(PcxBillExpBase::getExpenseName).collect(Collectors.joining(",")));
    }

    /**
     * （TODO (待抽出公共方法）  添加票时，需要将费用明细的 expenseId 填充到费用明细中
     *
     * @param baseExpList
     * @param detailCommons
     */
    private void fillDetailExpenseId(List<PcxBillExpTraining> baseExpList, List<PcxBillExpDetailTraining> detailCommons) {
        if (CollectionUtils.isNotEmpty(detailCommons)) {
            Map<String, List<PcxBillExpDetailTraining>> map = detailCommons.stream().collect(Collectors.groupingBy(PcxBillExpDetailTraining::getExpDetailCode));
            for (PcxBillExpTraining expBase : baseExpList) {
                List<PcxBillExpDetailTraining> detailList = map.get(expBase.getExpenseCode());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    for (PcxBillExpDetailTraining common : detailList) {
                        common.setExpenseId(expBase.getId());
                    }
                }
            }
        }
    }


    /**
     * （TODO (待抽出公共方法）  转换出付款信息列表
     *
     * @param updateQO
     * @return
     */
    private List<PcxEcsSettl> analysisEcsSettl(UpdateEcsCommonQO updateQO) {
        List<PcxEcsSettl> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getSettlList())) {
            for (EcsSettlQO ecsSettlQO : updateQO.getSettlList()) {
                PcxEcsSettl ecsSettl = new PcxEcsSettl();
                BeanUtils.copyProperties(ecsSettlQO, ecsSettl);
                ecsSettl.setBillId(updateQO.getBillId());
                ecsSettl.setEcsBillId(updateQO.getEcsBillId());
                ecsSettl.setEcsTypeCode(updateQO.getEcsBillType());
                ecsSettl.setFiscal(updateQO.getFiscal());
                ecsSettl.setAgyCode(updateQO.getAgyCode());
                ecsSettl.setMofDivCode(updateQO.getMofDivCode());
                result.add(ecsSettl);
            }
        }
        return result;
    }

    /**
     * （TODO (待抽出公共方法）  转换出票据关系列表
     *
     * @param attachRelList
     * @param view
     * @param ecsBillId
     * @return
     */
    private List<PcxBillExpAttachRel> analysisAttachRelList(List<AttachRelQO> attachRelList, PcxBill view, String ecsBillId) {
        List<PcxBillExpAttachRel> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachRelList)) {
            for (AttachRelQO attachRelQO : attachRelList) {
                PcxBillExpAttachRel attachRel = new PcxBillExpAttachRel();
                attachRel.setBillId(view.getId());
                attachRel.setFiscal(view.getFiscal());
                attachRel.setAgyCode(view.getAgyCode());
                attachRel.setMofDivCode(view.getMofDivCode());
                attachRel.setTenantId(view.getTenantId());
                attachRel.setAttachId(attachRelQO.getBillId());
                attachRel.setFileName(attachRelQO.getFileName());
                attachRel.setRelType(PcxExpAttachRelType.ECS.getCode());
                attachRel.setRelId(ecsBillId);
                result.add(attachRel);
            }
        }
        return result;
    }

    /**
     * (TODO (待抽出公共方法))  转换出更新票据的DTO
     */
    private UpdateEcsBillDTO analysisUpdateEcsBillDTO(List<PcxEcsSettl> ecsSettlList, UpdateEcsCommonQO updateQO) {
        UpdateEcsBillDTO dto = new UpdateEcsBillDTO();
        dto.setBillNo(updateQO.getBillNo());
        dto.setBillAmt(updateQO.getBillAmt());
        dto.setBillId(updateQO.getEcsBillId());
        dto.setFiscal(updateQO.getFiscal());
        dto.setAgencyCode(updateQO.getAgyCode());
        dto.setMofDivCode(updateQO.getMofDivCode());
        dto.setTenantId(updateQO.getTenantId());
        dto.setBillTypeCode(updateQO.getEcsBillType());
        List<UpdateEcsBillDTO.EcsAttachRelInfo> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getEcsBillClassRelList())) {
            for (AttachRelQO attachRelQO : updateQO.getEcsBillClassRelList()) {
                UpdateEcsBillDTO.EcsAttachRelInfo relInfo = UpdateEcsBillDTO.EcsAttachRelInfo.builder()
                        .agencyCode(updateQO.getAgyCode())
                        .fiscal(updateQO.getFiscal())
                        .mofDivCode(updateQO.getMofDivCode())
                        .billId(attachRelQO.getBillId())
                        .fileName(attachRelQO.getFileName())
                        .billTypeCode(attachRelQO.getBillTypeCode())
                        .createUser(updateQO.getUserName())
                        .createUserCode(updateQO.getUserCode())
                        .isDeleted("2")
                        .classType("bus").build();
                attachRelList.add(relInfo);
            }
        }
        dto.setEcsBillClassRelList(attachRelList);
        List<EcsBillSettleDTO> billSettlList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ecsSettlList)) {
            for (PcxEcsSettl ecsSettl : ecsSettlList) {
                EcsBillSettleDTO settleDTO = new EcsBillSettleDTO();
                BeanUtils.copyProperties(ecsSettl, settleDTO);
                billSettlList.add(settleDTO);
            }
        }
        dto.setSettlDetails(billSettlList);
        dto.setEcsBillInfo(updateQO.getEcsBillInfo());
        return dto;
    }

    /**
     * 获取报销单关联的费用数据
     * @param view
     * @return
     */
    public List<PcxBillExpTraining> queryBillExpBaseList(PcxBill view) {
        String[] expTypeCodeArr = StringUtil.getStringValue(view.getExpenseCodes()).split(",");
        List<PcxBillExpTraining> result = new ArrayList<>();
        for (String expType : expTypeCodeArr) {
            BillExpenseService<PcxBillExpBase> bean = ExpenseBeanUtil.getBean(expType, view.getBizType());
            PcxBillExpBase expBase = bean.view(expType, view);
            if (Objects.nonNull(expBase)){
                result.add((PcxBillExpTraining)expBase);
            }
        }
        return result;
    }

    /**
     * 获取报销单关联的费用明细数据
     * @param view
     * @return
     */
    private List<PcxBillExpDetailBase> queryBillExpDetailList(PcxBill view) {
        String[] split = StringUtil.getStringValue(view.getExpenseCodes()).split(",");
        List<PcxBillExpDetailBase> result = new ArrayList<>();
        for (String expTypeCode : split) {
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(expTypeCode);
            result.addAll(detailBean.listByExpenseCode(expTypeCode, view));
        }
        return result;
    }

    private void changeEcsAmt(List<PcxExpDetailEcsRel> optEcsRelList, List<EcsItemExpense> itemExpenseList) {
        Map<String, BigDecimal> itemEcsMap = itemExpenseList.stream().collect(Collectors.toMap(EcsItemExpense::getEcsDetailId, EcsItemExpense::getEcsAmt, (key1, key2) -> key1));
        optEcsRelList.forEach(item->{
            BigDecimal ecsAmt = itemEcsMap.get(item.getEcsDetailId());
            if (ecsAmt != null){
                item.setEcsAmt(ecsAmt);
            }
        });
    }
    private List<PcxExpDetailEcsRel> analysisNewEcsRelEcsItem(List<PcxExpDetailEcsRel> relList,
                                                              Map<String, List<PcxBillExpDetailBase>> detailMap,
                                                              String expenseTypeCode) {
        //根据票项目遍历，每个项目的费用明细进行生成ecsRel和明细的关联关系
        //如果项目没有费用明细，则把他自己在保存一下
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = relList.stream()
                .collect(Collectors.groupingBy(rel ->
                        Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                ));
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsDetailMap.entrySet()) {
            PcxExpDetailEcsRel rel = entry.getValue().get(0);
            List<PcxBillExpDetailBase> detailBases = detailMap.get(rel.getEcsDetailId());
            if (CollectionUtils.isNotEmpty(detailBases)){
                List<PcxExpDetailEcsRel> ecsRelList = analysisNewEcsRel(rel, detailBases);
                result.addAll(ecsRelList);
            }else{
                //旧的删掉，重新插入一个
                rel.setId(IDGenerator.id());
                rel.setDetailId("");
                rel.setInputAmt(BigDecimal.ZERO);
                rel.setCheckAmt(BigDecimal.ZERO);
                rel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
                rel.setExpenseTypeCode(expenseTypeCode);
                result.add(rel);
            }
        }
        return result;
    }

    private List<PcxExpDetailEcsRel> analysisNewEcsRel(PcxExpDetailEcsRel rel, List<PcxBillExpDetailBase> detailList) {
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        for (PcxBillExpDetailBase detail : detailList) {
            detail.setEcsAmt(rel.getEcsAmt());
            PcxExpDetailEcsRel newRel = JSON.parseObject(JSON.toJSONString(rel), PcxExpDetailEcsRel.class);
            newRel.setId(IDGenerator.id());
            newRel.setDetailId(detail.getId());
            newRel.setExpenseTypeCode(detail.getExpDetailCode());
            newRel.setEmpCode(detail.getEmpCode());
            newRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            newRel.setInputAmt(detail.getInputAmt());
            newRel.setCheckAmt(detail.getCheckAmt());
            result.add(newRel);
        }
        return result;
    }

    /**
     * (TODO (待抽出公共方法))  分析出票据关系列表和票据明细列表
     *
     * @param optEcsRelList
     * @param ecsDetailAmtVOMap
     * @param invoiceQO
     * @return
     */
    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailTraining>> analysisEcsRelAndDetailCommon
    (List<PcxExpDetailEcsRel> optEcsRelList, Map<String, EcsDetailAmtVO> ecsDetailAmtVOMap, ExpInvoiceQO invoiceQO) {
        List<PcxExpDetailEcsRel> addEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailTraining> addDetailList = new ArrayList<>();
        Map<String, PcxExpDetailEcsRel> ecsRelMap = optEcsRelList
                .stream().collect(Collectors.toMap(PcxExpDetailEcsRel::getId, Function.identity(), (key1, key2) -> key1));
        for (Map.Entry<String, EcsDetailAmtVO> entry : ecsDetailAmtVOMap.entrySet()) {
            String ecsRelId = entry.getKey();
            EcsDetailAmtVO amtVO = entry.getValue();
            PcxExpDetailEcsRel oldRel = ecsRelMap.get(ecsRelId);
            if (Objects.nonNull(oldRel)) {
                PcxExpDetailEcsRel newRel = new PcxExpDetailEcsRel();
                BeanUtils.copyProperties(oldRel, newRel);
                newRel.setId(IDGenerator.id());
                newRel.setInputAmt(amtVO.getInputAmt());
                newRel.setCheckAmt(amtVO.getInputAmt());
                newRel.setRemark(amtVO.getRemark());
                newRel.setExpenseTypeCode(amtVO.getExpenseTypeCode());
                newRel.setExpenseTypeName(amtVO.getExpenseTypeName());
                if (CollectionUtils.isNotEmpty(amtVO.getDetailList())) {
                    int index = 0;
                    for (EcsCommonDetailVO detailVO : amtVO.getDetailList()) {
                        PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                        BeanUtils.copyProperties(newRel, copyRel);
                        copyRel.setId(IDGenerator.id());
                        PcxBillExpDetailTraining training = initRelDetail(copyRel, invoiceQO);
                        BeanUtils.copyProperties(detailVO, training);
                        if (index > 0) {
                            copyRel.setInputAmt(BigDecimal.ZERO);
                            copyRel.setCheckAmt(BigDecimal.ZERO);
                            training.setInputAmt(BigDecimal.ZERO);
                            training.setCheckAmt(BigDecimal.ZERO);
                        }
                        training.setEcsNum(amtVO.getEcsNum());
                        addEcsRelList.add(copyRel);
                        addDetailList.add(training);
                        index++;
                    }
                } else {
                    PcxBillExpDetailTraining common = initRelDetail(newRel, invoiceQO);
                    common.setEcsNum(amtVO.getEcsNum());
                    addDetailList.add(common);
                    addEcsRelList.add(newRel);
                }
            }

        }
        return Pair.of(addEcsRelList, addDetailList);
    }
}