package com.pty.pcx.service.impl.basicbussiness;

import com.pty.pcx.CodeNameVO;
import com.pty.pcx.api.basicbussiness.IBasicBussinessService;
import com.pty.pcx.common.constant.FundPositionEnum;
import com.pty.pcx.qo.setting.BaseDataQO;
import com.pty.pub.common.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@Indexed
public class BasicBussinessService implements IBasicBussinessService {
    @Override
    public Response<?> getBudgetIdxStation(BaseDataQO baseDataQO) {

        List<CodeNameVO> list = new ArrayList<>();
        for (FundPositionEnum position : FundPositionEnum.values()) {
            CodeNameVO codeNameVO = new CodeNameVO();
            codeNameVO.setCode(position.getCode());
            codeNameVO.setName(position.getDescription());
            list.add(codeNameVO);
        }
        //获取经费来源录入岗
        return Response.success(list);
    }
}
