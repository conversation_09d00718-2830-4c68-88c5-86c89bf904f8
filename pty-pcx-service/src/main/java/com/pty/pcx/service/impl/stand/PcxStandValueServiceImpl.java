package com.pty.pcx.service.impl.stand;

import com.pty.pcx.api.stand.PcxStandValueService;
import com.pty.pcx.common.util.PcxUtil;
import com.pty.pcx.dao.stand.PcxStandConditionDao;
import com.pty.pcx.dao.stand.PcxStandKeyDao;
import com.pty.pcx.dao.stand.PcxStandValueDao;
import com.pty.pcx.entity.stand.PcxStandKey;
import com.pty.pcx.entity.stand.PcxStandValue;
import com.pty.pcx.entity.stand.qo.PcxStandQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支出标准值表(PcxStandValue)表服务实现类
 * <AUTHOR>
 * @since 2024-11-04 14:59:16
 */
@Slf4j
@Indexed
@Service
public class PcxStandValueServiceImpl implements PcxStandValueService {

	@Autowired
	private PcxStandValueDao pcxStandValueDao;
	@Autowired
	private PcxStandConditionDao pcxStandConditionDao;
	@Autowired
	private PcxStandKeyDao pcxStandKeyDao;

	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxStandValue selectById(String id) {
		return pcxStandValueDao.selectById(id);
	}

	/**
	 * 通过主键id删除数据
	 * @param id 主键
	 */
	@Override
	public int deleteById(String id) {
		return pcxStandValueDao.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delStand(PcxStandKey pcxStandKey) {
		pcxStandKeyDao.deleteStandKeyByStandCode(pcxStandKey.getStandCode(),
				pcxStandKey.getAgyCode(), pcxStandKey.getFiscal(), pcxStandKey.getMofDivCode());
	}

	private void deleteStandConditionAndValueByStandCode(String standCode, String agyCode, String fiscal, String mofDivCode){
		pcxStandConditionDao.deleteStandConditionByStandCode(standCode, agyCode, fiscal, mofDivCode);
		pcxStandValueDao.deleteStandValueByStandCode(standCode, agyCode, fiscal, mofDivCode);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void insertStand(PcxStandQO pcxStandQO) {
		pcxStandKeyDao.insert(pcxStandQO);
		batchInsertConditionAndValue(pcxStandQO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateStand(PcxStandQO pcxStandQO) {
		pcxStandKeyDao.updateByStandCode(pcxStandQO);
		deleteStandConditionAndValueByStandCode(pcxStandQO.getStandCode(),
				pcxStandQO.getAgyCode(), pcxStandQO.getFiscal(), pcxStandQO.getMofDivCode());
		batchInsertConditionAndValue(pcxStandQO);
	}
	private void batchInsertConditionAndValue(PcxStandQO pcxStandQO){
		if (CollectionUtils.isNotEmpty(pcxStandQO.getConditionList())){
			PcxUtil.getPartitionBatchInsertList(pcxStandQO.getConditionList()).forEach(o->pcxStandConditionDao.batchInsert(o));
		}
		if ((CollectionUtils.isNotEmpty(pcxStandQO.getStandValueList()))){
			PcxUtil.getPartitionBatchInsertList(pcxStandQO.getStandValueList()).forEach(o->pcxStandValueDao.batchInsert(o));
		}
	}
}


