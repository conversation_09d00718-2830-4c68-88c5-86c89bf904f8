package com.pty.pcx.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.pty.fileservice.api.IPubFileService;
import com.pty.fileservice.entity.PaAttach;
import com.pty.pcx.api.export.IPcxBillExportService;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.FileUtil;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dao.bill.PcxBillExpAttachRelDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpAttachRel;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.qo.PcxExportQo;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.vo.PaAttachVo;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.LogUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@Indexed
public class PcxBillExportService implements IPcxBillExportService {

    @Autowired
    private BillExpenseCommonService billExpenseCommonService;

    @Autowired
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;

    @Autowired
    @Lazy
    private IPubFileService pubFileService;

    @Autowired
    private PcxBillDao pcxBillDao;

    @Autowired
    private IWitAuditRuleService witAuditRuleService;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;
    private static final String PATH = System.getProperty("user.dir");

    /**
     *  billAndFile: 票据+附件; file: 附件;
     */
    private static final String DOWNLOAD_CONTENT_BILLANDFILE ="billAndFile";

    /**
     * billAndFile: 票据+附件; file: 附件;
     * @param billIdList
     * @param downloadContent
     * @return
     */
    @Override
    public CheckMsg attachExport(List<String> billIdList, String downloadContent) {
        try {
            CheckMsg msg = new CheckMsg();
            if (CollectionUtils.isEmpty(billIdList)){
                return msg.setSuccess(false).setMsgInfo("请选择需要下载的单据");
            }
            List<PcxBill> pcxBills = new ArrayList<>();
            List<List<String>> idList = Lists.partition(billIdList, 800);
            for (List<String> ids : idList) {
                List<PcxBill> pcxBillList = pcxBillDao.selectBatchIds(ids);
                pcxBills.addAll(pcxBillList);
            }
            if (CollectionUtils.isEmpty(pcxBills)){
                return msg.setSuccess(false).setMsgInfo("未查询到单据信息，请确认数据");
            }
            List<PaAttachVo> paAttachVos =new ArrayList<>();
            // 查询附件清单
            List<PcxBillAttachRelationVO> attachList = billExpenseCommonService.getAttachList(PcxBill.builder().ids(billIdList).build());
            List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
            if (downloadContent.equals(DOWNLOAD_CONTENT_BILLANDFILE)){
                attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class).in(PcxBillExpAttachRel::getBillId, billIdList));
            }
            Set<String> billIds = pcxBills.stream().map(PcxBill::getId).collect(Collectors.toSet());
            List<PcxBillExpAttachRel> pcxBillExpAttachRels = pcxExpDetailEcsRelDao.selectDetailAttachIds(new ArrayList<>(billIds));
            for (PcxBillExpAttachRel pcxBillExpAttachRel : pcxBillExpAttachRels) {
                PaAttachVo paAttachVo = new PaAttachVo();
                paAttachVo.setBillId(pcxBillExpAttachRel.getBillId());
                paAttachVo.setAttachId(pcxBillExpAttachRel.getAttachId());
                paAttachVos.add(paAttachVo);
            }
            // 查询附件
            for (PcxBill pcxBill : pcxBills) {
                WitRuleResult witRuleResult = new WitRuleResult();
                witRuleResult.setBillId(pcxBill.getId());
                witRuleResult.setAgyCode(pcxBill.getAgyCode());
                witRuleResult.setMofDivCode(pcxBill.getMofDivCode());
                witRuleResult.setFiscal(pcxBill.getFiscal());
                List<PaAttachVo> witRuleAttach = getWitRuleAttachIds(witRuleResult);
                paAttachVos.addAll(witRuleAttach);
            }
            // 提取重复逻辑：将 PcxBillAttachRelationVO 或 PcxBillExpAttachRel 转换为 PaAttachVo
            paAttachVos.addAll(convertToPaAttachVo(attachList));
            paAttachVos.addAll(convertToPaAttachVo(attachRelList));
            // 如果附件列表为空，返回错误信息
            if (CollectionUtils.isEmpty(paAttachVos)) {
                msg.setSuccess(false).setMsgInfo("单据中没有附件");
                return msg;
            }
            Map<String, PcxBill> pcxBillMap = pcxBills.stream().collect(Collectors.toMap(PcxBill::getId, m -> m));
            for (PaAttachVo paAttach: paAttachVos){
                if (pcxBillMap.containsKey(paAttach.getBillId())){
                    paAttach.setBillNo(pcxBillMap.get(paAttach.getBillId()).getBillNo());
                }
            }
            msg.setSuccess(true).setData(paAttachVos);
            return msg;
        }catch (Exception e){
            log.error("下载文件失败 req {}",billIdList,e);
            throw new CommonException("下载文件失败");
        }
    }

    private List<PaAttachVo> getWitRuleAttachIds(WitRuleResult witRuleResult) {
        if (ObjectUtils.isEmpty(witRuleResult)) {
            return Collections.emptyList();
        }
        witRuleResult.setFiscal(witRuleResult.getFiscal());
        witRuleResult.setAgyCode(witRuleResult.getAgyCode());
        witRuleResult.setMofDivCode(witRuleResult.getMofDivCode());
        witRuleResult.setBillId(witRuleResult.getBillId());
        List<String> attachIds = witAuditRuleService.queryRuleAttach(witRuleResult);
        if(CollectionUtils.isEmpty(attachIds)){
            return Collections.emptyList();
        }
        List<PaAttachVo> paAttachVos = new ArrayList<>();
        for (String attachId : attachIds) {
            PaAttachVo result = new PaAttachVo();
            result.setAttachId(attachId);
            result.setBillId(witRuleResult.getBillId());
            paAttachVos.add(result);
        }
        return paAttachVos;
    }

    // 提取重复逻辑的方法
    private List<PaAttachVo> convertToPaAttachVo(List<?> sourceList) {
        List<PaAttachVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return result;
        }
        for (Object item : sourceList) {
            PaAttachVo paAttachVo = new PaAttachVo();
            BeanUtil.copyProperties(item, paAttachVo);
            result.add(paAttachVo);
        }
        return result;
    }

    @Override
    public PcxExportQo attachExportDown(List<PaAttachVo> paAttachVos) {
        PcxExportQo qo = new PcxExportQo();
        if (CollectionUtil.isEmpty(paAttachVos)) {
            log.error("附件下载失败，请检查缓存数据");
            return qo;
        }
        Map<String, List<PaAttachVo>> attachMap = paAttachVos.stream()
                .filter(p -> StringUtil.isNotEmpty(p.getBillNo()))
                .collect(Collectors.groupingBy(PaAttachVo::getBillNo));
        // 下载文件到服务器指定目录上
        String tempFilePath = PATH + File.separator + StringUtil.getUUID();
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            String fileName;
            String zipPath = processAndSaveAttachments(tempFilePath, attachMap);
            if (1 == attachMap.keySet().size()) {
                String billNo = attachMap.keySet().iterator().next();
                fileName = billNo + ".zip";
            } else {
                fileName = "attach.zip";
            }

            File zipFile = ZipUtil.zip(zipPath); // 压缩
            FileUtil.processZipFile(zipFile, bos);// 将压缩文件内容写入ByteArrayOutputStream

            byte[] bytes = bos.toByteArray();
            qo.setBytes(bytes); // 设置压缩文件内容到qo
            qo.setFileName(fileName);
            // 删除压缩文件
            zipFile.delete();
        } catch (Exception e) {
            log.error("异常---->{}", LogUtil.stackTraceInfo(e));
            throw new CommonException("下载文件失败");
        }finally {
            try {
                // 删除临时文件夹及里面的文件 放最后执行确保所有文件都删除，防止占用磁盘空间
                Files.walk(Paths.get(tempFilePath)).sorted(Comparator.reverseOrder()).map(Path::toFile).forEach(File::delete);
            }catch (Exception ex) {
                log.error("删除临时文件失败：{}", LogUtil.stackTraceInfo(ex));
            }
        }
        return qo;
    }

    public String processAndSaveAttachments(String path, Map<String, List<PaAttachVo>> attachMap) {
        String finalPath = "";

        // 单据数量大于1，则添加"temp"子目录
        if (attachMap.size() > 1) {
            path += File.separator + "temp";
        }

        // 遍历并处理每个单据的附件
        for (Map.Entry<String, List<PaAttachVo>> entry : attachMap.entrySet()) {
            String billNo = entry.getKey();
            List<PaAttachVo> paAttachVoList = entry.getValue();

            try {
                // 下载并保存附件，获取保存的根目录路径
                finalPath = downloadAndSaveAttachmentsToDisk(paAttachVoList, path);
            } catch (IOException e) {
                log.error("单据 {} 附件下载失败", billNo, e);
                throw new CommonException(e.getMessage());
            }
        }

        return finalPath;
    }

    public String downloadAndSaveAttachmentsToDisk(List<PaAttachVo> paAttachVoList, String path) throws IOException {
        if (CollectionUtils.isEmpty(paAttachVoList)) {
            return "";
        }
        String billNo = paAttachVoList.get(0).getBillNo();
        // 构建基础目录路径,用于压缩包使用 需要把压缩包的实际地址返回
        String dirPath = path.contains("temp") ? path : path + File.separator + billNo;
        File dir = new File(dirPath);
        String absolutePath = dir.getAbsolutePath();
        log.info("Creating directory: {}", absolutePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        for (PaAttachVo paAttachVo : paAttachVoList) {
            PaAttach paAttach = new PaAttach();
            paAttach.setAttachId(paAttachVo.getAttachId());
            //目前pa_attach_list_relation与 pcx_bill_exp_attach_rel中没有relPath,此处为空。平台已兼容，可以预留。
            paAttach.setRelPath(paAttachVo.getRelPath());
            byte[] bytes = pubFileService.fileDownload(paAttach);
            dirPath = dirPath.contains(billNo) ? dirPath : dirPath + File.separator + billNo;
            // 创建存放文件的目录 如果不存在则创建
            File file = new File(dirPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            String filePath = dirPath + File.separator + paAttachVo.getFileName();
            try (FileOutputStream os = new FileOutputStream(filePath)) {
                os.write(bytes);
            }
        }
        return absolutePath;
    }

}
