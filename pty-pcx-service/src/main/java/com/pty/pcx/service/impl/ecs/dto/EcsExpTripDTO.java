package com.pty.pcx.service.impl.ecs.dto;

import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

@Data
public class EcsExpTripDTO {

    private String startTime;
    private String endTime;
    private String empName;
    private String empCode;
    private String tripType;
    private String id;

    //行程关联的城市间交通费，住宿费明细id集合
    private Set<String> detailIds;
    //行程关联的城市间交通费列表，添加票，删除票，有可能导致补充行程，城市间交通费有可能开始时间和结束时间相同
    //行程内展示票时就无法按照开始时间去排序，所以加了seq，seq有可能会变动，所以需要行程的全部城市间交通费更新一下seq
    private List<PcxBillExpDetailTravel> trafficList;

    //当前行程，在城市驻留的时间区间列表
    private List<CityDateVO> cityDateVOList;
    //人员的城市驻留时间列表
    private Map<String, List<CityDateVO>> empCityDateMap;
    //人员的城市住宿时间，一个城市会有多个住宿时间，还包含是否住宿舍
    private Map<String, Map<String, List<DateVO>>> empCityHotelDateMap = new HashMap<>();

    public void setEmpCityHotelDateMap(Map<String, List<DateVO>> empCityHotelDateMap){
        this.empCityHotelDateMap.put(this.empCode, empCityHotelDateMap);
    }

    public void addDetailId(String id){
        if (detailIds == null){
            detailIds = new HashSet<>();
        }
        detailIds.add(id);
    }

    public void addCityDate(CityDateVO cityDateVO){
        if (cityDateVOList == null){
            empCityDateMap = new HashMap<>();
            cityDateVOList = new ArrayList<>();
            empCityDateMap.put(empCode, cityDateVOList);
        }
        cityDateVOList.add(cityDateVO);
    }

    public void eatSameTrip(EcsExpTripDTO next){
        //取早的时间为开始时间，取晚的时间为结束时间
        if (this.startTime.compareTo(next.getStartTime()) > 0){
            this.startTime = next.getStartTime();
        }
        if (this.endTime.compareTo(next.getEndTime()) < 0){
            this.endTime = next.getEndTime();
        }
        if (!this.empCode.contains(next.empCode)){
            this.empCode = String.format("%s,%s", this.empCode, next.empCode);
            this.empName = String.format("%s,%s", this.empName, next.empName);
        }

        //合并行程关联的明细id
        this.detailIds.addAll(next.getDetailIds());
        //合并行程关联的城市间交通费
        //把城市间交通费重新排序
        this.trafficList.addAll(next.getTrafficList());
        this.trafficList.sort((o1, o2) -> {
            int compareStartTime = o1.getStartTime().compareTo(o2.getStartTime());
            if (compareStartTime ==0){
                return o1.getTripSeq().compareTo(o2.getTripSeq());
            }
            return compareStartTime;
        });
        int index = 1;
        for (PcxBillExpDetailTravel detailTravel : this.trafficList) {
            detailTravel.setTripSeq(index++);
        }
        this.cityDateVOList.addAll(next.cityDateVOList);
        for (Map.Entry<String, List<CityDateVO>> entry : next.getEmpCityDateMap().entrySet()) {
            List<CityDateVO> cityDateVOS = this.empCityDateMap.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(cityDateVOS)){
                cityDateVOS.addAll(entry.getValue());
            }else{
                this.empCityDateMap.put(entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, Map<String, List<DateVO>>> entry : next.getEmpCityHotelDateMap().entrySet()) {
            Map<String, List<DateVO>> cityHotelDateMap = this.empCityHotelDateMap.get(entry.getKey());
            if (Objects.isNull(cityHotelDateMap)){
                this.getEmpCityHotelDateMap().put(entry.getKey(), entry.getValue());
            }else{
                for (Map.Entry<String, List<DateVO>> hotelEntry : entry.getValue().entrySet()) {
                    List<DateVO> hotelDateList = cityHotelDateMap.get(hotelEntry.getKey());
                    if (CollectionUtils.isNotEmpty(hotelDateList)){
                        hotelDateList.addAll(hotelEntry.getValue());
                    }else{
                        cityHotelDateMap.put(hotelEntry.getKey(), hotelEntry.getValue());
                    }
                }
            }
        }

    }
}
