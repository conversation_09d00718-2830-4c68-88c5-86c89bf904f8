package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pty.pcx.api.bill.PcxBillExpDetailInlandfeeService;
import com.pty.pcx.dao.bill.PcxBillExpDetailInlandfeeDao;
import com.pty.pcx.entity.bill.PcxBillExpDetailInlandfee;
import com.pty.pub.common.util.CollectionUtil;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Indexed
public class PcxBillExpDetailInlandFeeServiceImpl extends ServiceImpl<PcxBillExpDetailInlandfeeDao,PcxBillExpDetailInlandfee> implements PcxBillExpDetailInlandfeeService {

    @Resource
    private PcxBillExpDetailInlandfeeDao pcxBillExpDetailInlandfeeDao;

    @Override
    public List<PcxBillExpDetailInlandfee> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpDetailInlandfeeDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailInlandfee>()
                .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpDetailInlandfee::getBillId, billIds)
                .eq(PcxBillExpDetailInlandfee::getAgyCode, agyCode)
                .eq(PcxBillExpDetailInlandfee::getFiscal, fiscal)
                .eq(PcxBillExpDetailInlandfee::getMofDivCode, mofDivCode));
    }

    /**
     * 批量插入
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<PcxBillExpDetailInlandfee> detail) {
        this.saveBatch(detail);
    }

    /**
     * 批量插入
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<PcxBillExpDetailInlandfee> toUpdate) {
        this.updateBatchById(toUpdate);
    }

    @Override
    public List<PcxBillExpDetailInlandfee> selectBatchIds(List<String> collect) {
        return pcxBillExpDetailInlandfeeDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailInlandfee>()
                .in(CollectionUtil.isNotEmpty(collect), PcxBillExpDetailInlandfee::getId, collect));
    }

    @Override
    public void deleteBatchIds(List<String> collect) {
        pcxBillExpDetailInlandfeeDao.deleteBatchIds( collect);
    }

}