package com.pty.pcx.service.impl.stand;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import com.google.common.collect.Lists;
import com.pty.mad.entity.MadGlobalGeographicInfo;
import com.pty.mad.qo.MadGlobalGeoQo;
import com.pty.pcx.api.stand.PcxStandAbroadService;
import com.pty.pcx.common.constant.WitConstants;
import com.pty.pcx.common.util.TripDaysCalculator;
import com.pty.pcx.dao.stand.PcxStandAbroadDao;
import com.pty.pcx.entity.stand.PcxStandAbroad;
import com.pty.pcx.entity.stand.vo.PcxStandAbroadCityVO;
import com.pty.pcx.entity.stand.vo.PcxStandAbroadCountryVO;
import com.pty.pcx.qo.bill.PcxBillAbroadTripQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.pty.mad.api.MadGlobalGeographicInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支出标准-出国标准(PcxStandAbroad)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:27:22
 */
@Slf4j
@Indexed
@Service
public class PcxStandAbroadServiceImpl implements PcxStandAbroadService {

    @Autowired
    private PcxStandAbroadDao pcxStandAbroadDao;
    public static final String GROUP_CONCAT_SEPARATOR = "@";
    @Qualifier("pcxBatchServiceUtil")
    @Autowired
    private BatchServiceUtil pcxBatchServiceUtil;
    @Autowired
    private MadGlobalGeographicInfoService madGlobalGeographicInfoService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PcxStandAbroad selectById(String id) {
        return pcxStandAbroadDao.selectById(id);
    }

    /**
     * 查询多条数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 对象列表
     */
    @Override
    public List<PcxStandAbroadCountryVO> selectList(PcxStandAbroad pcxStandAbroad) {
        // 记录方法调用的日志信息，使用参数化日志记录
        log.debug("pcxStandAbroad.selectList: {}", pcxStandAbroad);

        // 从数据库中查询符合条件的记录列表
        List<PcxStandAbroad> abroadList = pcxStandAbroadDao.selectList(pcxStandAbroad);

        // 创建一个空的结果列表，用于存储最终的数据
        List<PcxStandAbroadCountryVO> result = new ArrayList<>();

        // 按国家代码分组
        Map<String, List<PcxStandAbroad>> countryGroupedRecords = abroadList.stream()
                .collect(Collectors.groupingBy(PcxStandAbroad::getCountryCode));

        for (Map.Entry<String, List<PcxStandAbroad>> entry : countryGroupedRecords.entrySet()) {
            List<PcxStandAbroad> _records = entry.getValue();

            if (_records.isEmpty()) {
                continue; // 跳过空记录
            }

            // 获取任意一条记录作为代表
            PcxStandAbroad any = _records.get(0);

            // 构建一个代表对象，包含国家的基本信息
            PcxStandAbroad delegate = PcxStandAbroad.builder()
                    .agyCode(any.getAgyCode())
                    .continentCode(any.getContinentCode())
                    .continentName(any.getContinentName())
                    .countryCode(any.getCountryCode())
                    .countryName(any.getCountryName())
                    .tenantId(any.getTenantId())
                    .fiscal(any.getFiscal())
                    .mofDivCode(any.getMofDivCode())
                    .build();

            // 创建一个国家视图对象
            PcxStandAbroadCountryVO element = new PcxStandAbroadCountryVO(delegate);

            // 按照食品金额、其他金额和住宿金额组合分组
            Map<String, List<PcxStandAbroad>> groupedByAmounts = _records.stream()
                    .collect(Collectors.groupingBy(_r ->
                            _r.getFoodAmt() + GROUP_CONCAT_SEPARATOR +
                                    _r.getOtherAmt() + GROUP_CONCAT_SEPARATOR +
                                    _r.getStayAmt() + GROUP_CONCAT_SEPARATOR +
                                    _r.getCurrencyCode() + GROUP_CONCAT_SEPARATOR
                    ));

            for (Map.Entry<String, List<PcxStandAbroad>> amountEntry : groupedByAmounts.entrySet()) {
                List<PcxStandAbroad> _rs = amountEntry.getValue();

                // 对每组记录按序号排序
                _rs.sort(Comparator.comparingInt(PcxStandAbroad::getSeq));

                // 获取任意一条记录作为代表
                PcxStandAbroad _any = _rs.get(0);

                // 创建一个城市视图对象
                PcxStandAbroadCityVO _ele = new PcxStandAbroadCityVO(_any);

                // 设置城市视图对象的ID列表
                _ele.setIds(_rs.stream().map(PcxStandAbroad::getId).collect(Collectors.toList()));

                // 设置城市视图对象的城市代码集合
                _ele.setCityCodeSet(_rs.stream().map(PcxStandAbroad::getCityCode).collect(Collectors.toSet()));

                // 设置城市视图对象的城市名称集合
                _ele.setCityNameSet(_rs.stream().map(PcxStandAbroad::getCityName).collect(Collectors.toSet()));

                // 设置城市视图对象的总序号
                _ele.setSeq(_rs.stream().mapToInt(PcxStandAbroad::getSeq).sum());

                // 将_ele.getIds(),使用逗号分隔转换为字符串
                // 给前端制作唯一标识
                _ele.setIdentity(MD5.create().digestHex(StringUtils.join(_ele.getIds())));

                // 更新国家视图对象的总序号
                element.setSeq(ObjectUtils.firstNonNull(element.getSeq(), 0) + _ele.getSeq());

                // 将城市视图对象添加到国家视图对象的子列表中
                element.getChildren().add(_ele);
            }

            // 对国家视图对象的子列表按序号排序
            element.getChildren().sort(Comparator.comparingInt(PcxStandAbroadCityVO::getSeq));

            // 赋值国家已选城市
            element.setCheckedCityCodes(element.getChildren().stream().map(PcxStandAbroadCityVO::getCityCodeSet).flatMap(Set::stream).collect(Collectors.toList()));

            // 将国家视图对象添加到结果列表中
            result.add(element);
        }

        // 对结果列表按序号排序
        result.sort(Comparator.comparing(PcxStandAbroadCountryVO::getCountryCode));

        // 返回最终的结果列表
        return result;
    }


    /**
     * 新增数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 实例对象
     */
    @Override
    public int insertSelective(PcxStandAbroad pcxStandAbroad) {
        return pcxStandAbroadDao.insertSelective(pcxStandAbroad);
    }

    /**
     * 修改数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 实例对象
     */
    @Override
    public int update(PcxStandAbroad pcxStandAbroad) {
        log.debug("pcxStandAbroad.update{}", pcxStandAbroad);
        return pcxStandAbroadDao.update(pcxStandAbroad);
    }

    /**
     * 通过主键id删除数据
     *
     * @param id 主键
     */
    @Override
    public int deleteById(String id) {
        return pcxStandAbroadDao.deleteById(id);
    }

    @Override
    @Transactional
    public int rebuildCityInfo(PcxStandAbroad abroad) {
        log.debug("pcxStandAbroad.rebuildCityInfo: {}", abroad);
        MadGlobalGeoQo madGlobalGeoQo = new MadGlobalGeoQo();
        madGlobalGeoQo.setIncludedCodes(Collections.singletonList(abroad.getCountryCode()));
        List<MadGlobalGeographicInfo> globalGeographicInfos = madGlobalGeographicInfoService.selectList(madGlobalGeoQo);
        Assert.notEmpty(globalGeographicInfos, "未找到国家信息");
        MadGlobalGeographicInfo geographicInfo = globalGeographicInfos.get(0);
        Assert.notEmpty(geographicInfo.getChildren(), "未找到省/市信息");
        // 验证abroad的cityCodeSet是否全部在geographicInfo.getChildren()中
        Map<String, MadGlobalGeographicInfo> cityCodeRel = geographicInfo.getChildren().stream().collect(Collectors.toMap(MadGlobalGeographicInfo::getCode, Function.identity(), (a, b) -> a));
        Assert.isTrue(abroad.getCityCodes().stream().allMatch(cityCodeRel::containsKey), "城市编码集合中" +
                abroad.getCityCodes().stream().filter(cityCode -> !cityCodeRel.containsKey(cityCode)).collect(Collectors.toList())
                + "不存在");


        // 先按国家，城市集合，年度，区划，单位，删除城市
        PcxStandAbroad deleteParam = PcxStandAbroad.builder()
                .countryCode(abroad.getCountryCode())
                .cityCodes(abroad.getCityCodes())
                .fiscal(abroad.getFiscal())
                .mofDivCode(abroad.getMofDivCode())
                .agyCode(abroad.getAgyCode())
                .tenantId(abroad.getTenantId())
                .build();
        pcxStandAbroadDao.deleteForRebuild(deleteParam);
        List<PcxStandAbroad> abroads = new ArrayList<>();
        // 再按国家，城市集合，年度，区划，单位，币种，新增城市信息
        AtomicInteger seq = new AtomicInteger();
        abroad.getCityCodes().forEach(cityCode -> {
            PcxStandAbroad _a = PcxStandAbroad.builder().build();
            BeanUtil.copyProperties(abroad, _a);
            _a.setId(IDGenerator.id());
            _a.setCityCode(cityCode);
            _a.setCityName(cityCodeRel.get(cityCode).getAreaName());
            _a.setCountryName(geographicInfo.getAreaName());
            _a.setSeq(seq.incrementAndGet());
            abroads.add(_a);
        });
        AtomicInteger res = new AtomicInteger();
        pcxBatchServiceUtil.batchProcess(abroads, PcxStandAbroadDao.class, (a, b) -> res.addAndGet(a.insertSelective(b)));
        return res.get();
    }

    /**
     * 根据出国行程信息构建费用明细
     * <p>
     * 此方法处理的是出国行程的费用预算，主要步骤包括：
     * 1. 获取所有出发地点编码
     * 2. 查询每个城市的对应标准金额
     * 3. 根据行程信息和标准金额计算每个行程的费用明细，包括住宿费、伙食费和公杂费
     * 4. 将计算出的费用明细以列表形式返回
     *
     * @param tripQO 包含出国行程信息的查询对象
     * @return 返回一个列表，每个元素是一个映射，键是费用类型，值是该类型的费用明细
     */
    @Override
    public List<Map<String, Object>> buildByTripInfo(PcxBillAbroadTripQO tripQO) {
        if (ObjectUtil.isEmpty(tripQO)) {
            throw new RuntimeException("暂无出国行程信息");
        }
        List<Map<String, Object>> result = new ArrayList<>();
        // 获取所有出发地点编码
        List<PcxBillAbroadTripQO> aboradTrip = tripQO.getAbroadTrip();
        List<String> startPlaceCodes = aboradTrip.stream()
                .map(PcxBillAbroadTripQO::getStartPlaceCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(startPlaceCodes)) {
            log.error("【根据国外行程构建信息构建对应支出标准】因目的地地点编码为空,无法计算标准");
            return result;
        }
        Map<String, PcxStandAbroad> standardMap = selectStandAbroadList(tripQO, startPlaceCodes);

        for (PcxBillAbroadTripQO qo : aboradTrip) {
            PcxStandAbroad standard = standardMap.get(qo.getStartPlaceCode());
            if (ObjectUtil.isEmpty(standard)) {
                continue;
            }
            // 计算天数
            List<Pair<String, String>> trips = Collections.singletonList(
                    Pair.of(qo.getStartTime(), qo.getEndTime())
            );
            long days = TripDaysCalculator.calculateTotalDays(trips);
            // 计算人数
            int personNum = StringUtil.isEmpty(qo.getEmpCode()) ? 0 : qo.getEmpCode().split(",").length;
            if (days == 0 || personNum == 0) {
                log.error("出访期间与出行人不能为空");
                throw new RuntimeException("暂无出国行程信息");
            }
            //组装出国费固定的费用明细
            Map<String, Object> feeDetails = assembleExpDetail(qo, standard, days, personNum);
            // 构建返回对象
            result.add(feeDetails);
        }

        return result;
    }

    private Map<String, PcxStandAbroad> selectStandAbroadList(PcxBillAbroadTripQO tripQO, List<String> startPlaceCodes) {
        Map<String, PcxStandAbroad> standardMap = new HashMap<>();
        // 查询城市对应的标准金额
        List<PcxStandAbroad> standards = new ArrayList<>();
        List<List<String>> startPlaceCodeList = Lists.partition(startPlaceCodes, 800);
        for (List<String> codeList : startPlaceCodeList) {
            tripQO.setCityCodes(codeList);
            List<PcxStandAbroad> standardList = pcxStandAbroadDao.selectByCityCodes(tripQO);
            standards.addAll(standardList);
        }
        standardMap = standards.stream()
                .collect(Collectors.toMap(PcxStandAbroad::getCityCode, Function.identity()));
        return standardMap;
    }

    private Map<String, Object> assembleExpDetail(PcxBillAbroadTripQO qo, PcxStandAbroad standard, long days, int personNum) {
        // 参数校验
        if (days <= 0 || personNum <= 0) {
            throw new IllegalArgumentException("出差天数或出行人为必填项.");
        }

        // 初始化结果集
        Map<String, Object> feeDetails = new LinkedHashMap<>();

        // 构建标题以及行程Id
        String title = buildTitle(qo, standard);
        feeDetails.put("expDetailTitle", title);
        feeDetails.put("tripExpenseId", qo.getTripExpenseId());

        // 处理住宿费
        addFeeDetail(feeDetails, "3021206",  standard, days, personNum);

        // 处理伙食费
        if (WitConstants.wit_yes.equals(qo.getProvideFood())) {
            addFeeDetail(feeDetails, "3021208",  standard, days, personNum);
        }

        // 处理公杂费
        if (WitConstants.wit_yes.equals(qo.getProvideTool())) {
            addFeeDetail(feeDetails, "3021209", standard, days, personNum);
        }

        return feeDetails;
    }

    // 辅助方法：构建标题
    private String buildTitle(PcxBillAbroadTripQO qo, PcxStandAbroad standard) {
        StringBuilder titleBuilder = new StringBuilder();
        if (qo.getStartTime() != null && qo.getEndTime() != null && standard.getCountryName() != null && qo.getStartPlaceName() != null) {
            titleBuilder.append(qo.getStartTime()).append(" 至 ").append(qo.getEndTime())
                    .append(" ").append(standard.getCountryName()).append("(").append(qo.getStartPlaceName()).append(")");
        }
        return titleBuilder.toString();
    }

    // 辅助方法：添加费用明细
    private void addFeeDetail(Map<String, Object> feeDetails, String key,PcxStandAbroad standard, long days, int personNum) {
        if (standard != null) {
            Map<String, Object> fee = new HashMap<>();
            fee.put("inputAmt", standard.getOtherAmt().multiply(BigDecimal.valueOf(days * personNum)));
            fee.put("days", days);
            fee.put("normalAmt", standard.getOtherAmt());
            fee.put("personNum", personNum);
            fee.put("remark", "");
            fee.put("currencyName",standard.getCurrencyName());
            fee.put("currencyCode",standard.getCurrencyCode());
            feeDetails.put(key, fee);
        }
    }

}

