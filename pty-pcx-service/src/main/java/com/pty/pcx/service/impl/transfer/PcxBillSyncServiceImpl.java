package com.pty.pcx.service.impl.transfer;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pty.openapi.integration.Response;
import com.pty.openapi.integration.dto.reimbursement.ReimbursementSettlementDTO;
import com.pty.openapi.integration.dto.reimbursement.ReimbursementVoucherDTO;
import com.pty.openapi.sdk.client.kingdee.ReimbursementSettlementClient;
import com.pty.pcx.api.mybatisplus.bill.IPcxBillPlusService;
import com.pty.pcx.api.transfer.PcxBillSyncService;
import com.pty.pcx.common.enu.BillApproveStatusEnum;
import com.pty.pcx.common.enu.BillPayStatusEnum;
import com.pty.pcx.common.enu.PcxBillSyncStatus;
import com.pty.pcx.dao.bas.PcxBasItemDao;
import com.pty.pcx.dao.bill.PcxBillExpCommonDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.dao.transfer.PcxBillSyncDao;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpCommon;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.entity.transfer.PcxBillSync;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PcxBillSyncServiceImpl extends ServiceImpl<PcxBillSyncDao, PcxBillSync> implements PcxBillSyncService {

    @Autowired
    private IPcxBillPlusService pcxBillService;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;

    @Autowired
    private PcxBillExpCommonDao pcxBillExpCommonDao;

    @Autowired
    private PcxBasItemDao pcxBasItemDao;

    @Autowired
    private ReimbursementSettlementClient reimbursementSettlementClient;

    @Autowired
    private TransactionTemplate transactionTemplate;

    private static final String SYSTEM_CODE = "kingdee";
    private static final String SYSTEM_NAME = "金蝶云星空";
    // 通用报销单
    public static final Integer COMMON = 10;
    // 交通报销单
    public static final Integer TRAFFIC = 11;
    // 差旅报销单
    public static final Integer TRAVEl = 12;
    // 其他支付单
    public static final Integer OTHER = 13;


    @Override
    public PcxBillSync getPcxBillSyncByBillId(String billId) {
        return this.getOne(
                Wrappers.<PcxBillSync>lambdaQuery()
                        .eq(PcxBillSync::getBillId, billId)
                        .eq(PcxBillSync::getSystemCode, SYSTEM_CODE)
        );
    }

    @Override
    public List<PcxBill> getUnSyncedPcxBill(Integer bizType, Integer syncStatus) {
        Assert.notNull(bizType, "业务类型不能为空");

        Page<PcxBillSync> page = this.page(
                new Page<>(1, 5),
                Wrappers.<PcxBillSync>lambdaQuery()
                        .eq(PcxBillSync::getSyncStatus, syncStatus)
                        .eq(PcxBillSync::getBizType, bizType)
        );

        List<PcxBillSync> pcxBillSyncs = page.getRecords();

        if (pcxBillSyncs.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> billIds = pcxBillSyncs.stream().map(PcxBillSync::getBillId).collect(Collectors.toList());
        return pcxBillService.listByIds(billIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void extractDraftPcxBillSync() {
        Page<PcxBill> page = this.getBaseMapper().extractDraftPcxBills(new Page<>(1, 100));

        List<PcxBill> pcxBills = page.getRecords();
        if (pcxBills.isEmpty()) {
            return;
        }

        List<String> billIds = pcxBills.stream().map(PcxBill::getId).collect(Collectors.toList());

        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelDao.selectList(
                Wrappers.<PcxExpDetailEcsRel>lambdaQuery()
                        .in(PcxExpDetailEcsRel::getBillId, billIds)
                        .eq(PcxExpDetailEcsRel::getIsRealName, -1)
        );
        List<String> unHandledBillIds = pcxExpDetailEcsRels.stream().map(PcxExpDetailEcsRel::getBillId).collect(Collectors.toList());

        List<PcxBill> commonBills = new ArrayList<>();
        List<String> commonBillIds = new ArrayList<>();
        for (PcxBill pcxBill : pcxBills) {
            if (Objects.equals(COMMON, pcxBill.getBizType())) {
                commonBills.add(pcxBill);
                commonBillIds.add(pcxBill.getId());
            }
        }

        Set<String> trafficeBillIds = new HashSet<>();
        Set<String> travelBillIds = new HashSet<>();
        if (!commonBillIds.isEmpty()) {
            List<PcxBillExpCommon> pcxBillExpCommons = pcxBillExpCommonDao.selectList(
                    Wrappers.lambdaQuery(PcxBillExpCommon.class)
                            .in(PcxBillExpCommon::getBillId, commonBillIds)
                            .ne(PcxBillExpCommon::getExpenseCode, "*")
                            .ne(PcxBillExpCommon::getCheckAmt, 0)
            );

            Map<String, List<String>> expenseCodeMap = pcxBillExpCommons
                    .stream()
                    .collect(Collectors.groupingBy(
                            PcxBillExpCommon::getBillId,
                            Collectors.mapping(PcxBillExpCommon::getExpenseCode, Collectors.toList())
                    ));

            for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
                List<String> trafficeExpenses = Arrays.asList("0701");
                if (matchesAll(expenseCodeMap.get(pcxBillExpCommon.getBillId()), trafficeExpenses)) {
                    trafficeBillIds.add(pcxBillExpCommon.getBillId());
                }

                List<String> travelExpenses = Arrays.asList("0702");
                if (matchesAll(expenseCodeMap.get(pcxBillExpCommon.getBillId()), travelExpenses)) {
                    travelBillIds.add(pcxBillExpCommon.getBillId());
                }
            }
        }

        List<String> otherBillIds = new ArrayList<>();
        for (PcxBill pcxBill : commonBills) {
            PcxBasItem pcxBasItemQO = new PcxBasItem();
            pcxBasItemQO.setAgyCode(pcxBill.getAgyCode());
            pcxBasItemQO.setFiscal(pcxBill.getFiscal());
            pcxBasItemQO.setMofDivCode(pcxBill.getMofDivCode());
            pcxBasItemQO.setParentCode("99");
            List<PcxBasItem> pcxBasItems = pcxBasItemDao.selectList(pcxBasItemQO);
            Map<String, PcxBasItem> pcxBasItemMap =
                    pcxBasItems.stream().collect(Collectors.toMap(PcxBasItem::getItemCode, Function.identity(), (oldVal, newVal) -> newVal));

            String itemCode = pcxBill.getItemCode();
            if (pcxBasItemMap.containsKey(itemCode)) {
                otherBillIds.add(pcxBill.getId());
            }
        }

        List<PcxBillSync> pcxBillSyncs = pcxBills
                .stream()
                .map(pcxBill -> {
                    Integer bizType = pcxBill.getBizType();
                    if (trafficeBillIds.contains(pcxBill.getId())) {
                        // 交通报销单
                        bizType = TRAFFIC;
                    } else if (travelBillIds.contains(pcxBill.getId())) {
                        // 差旅报销单
                        bizType = TRAVEl;
                    } else if (otherBillIds.contains(pcxBill.getId())) {
                        // 其他支付单
                        bizType = OTHER;
                    }
                    PcxBillSync pcxBillSync = new PcxBillSync();
                    pcxBillSync.setBillId(pcxBill.getId());
                    pcxBillSync.setBizType(bizType);
                    pcxBillSync.setSyncStatus(PcxBillSyncStatus.DRAFT_PENDING_SYNC.getCode());
                    pcxBillSync.setSystemCode(SYSTEM_CODE);
                    pcxBillSync.setSystemName(SYSTEM_NAME);
                    pcxBillSync.setCreateTime(new Date());
                    pcxBillSync.setAgyCode(pcxBill.getAgyCode());
                    pcxBillSync.setFiscal(pcxBill.getFiscal());
                    pcxBillSync.setMofDivCode(pcxBill.getMofDivCode());
                    pcxBillSync.setTenantId(pcxBill.getTenantId());
                    return pcxBillSync;
                })
                .filter(pcxBillSync -> !unHandledBillIds.contains(pcxBillSync.getBillId()))
                .collect(Collectors.toList());

        this.saveBatch(pcxBillSyncs);
    }

    private boolean matchesAll(Collection<String> iterable, Collection<String> predicateIterable) {
        if (CollectionUtils.isEmpty(iterable)) {
            return false;
        }
        return IterableUtils.matchesAll(iterable, predicateIterable::contains);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void extractPcxBillSync() {
        Page<PcxBillSync> page = this.getBaseMapper().selectUnsyncedBills(new Page<>(1, 100));

        List<PcxBillSync> pcxBillSyncs = page.getRecords();
        if (pcxBillSyncs.isEmpty()) {
            return;
        }

        pcxBillSyncs.forEach(pcxBillSync -> pcxBillSync.setSyncStatus(PcxBillSyncStatus.PENDING_SYNC.getCode()));

        this.updateBatchById(pcxBillSyncs);
    }

    @Override
    public void updateSyncStatus(PcxBillSync pcxBillSync) {
        this.getBaseMapper().update(
                pcxBillSync,
                Wrappers.<PcxBillSync>lambdaUpdate()
                        .eq(PcxBillSync::getBillId, pcxBillSync.getBillId())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetPcxBillSync(String id) {
        LambdaUpdateWrapper<PcxBillSync> updateWrapper = Wrappers.lambdaUpdate();
        if (id == null) {
            LambdaUpdateWrapper<PcxBillSync> draftUpdateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(PcxBillSync::getSyncStatus, PcxBillSyncStatus.SYNC_FAILED.getCode())
                    .set(PcxBillSync::getExternalBillId, null)
                    .set(PcxBillSync::getExternalBillNo, null)
                    .set(PcxBillSync::getSyncStatus, PcxBillSyncStatus.PENDING_SYNC.getCode())
                    .set(PcxBillSync::getSyncTime, null)
                    .set(PcxBillSync::getSyncMessage, null);

            draftUpdateWrapper.eq(PcxBillSync::getSyncStatus, PcxBillSyncStatus.DRAFT_SYNC_FAILED.getCode())
                    .set(PcxBillSync::getExternalBillId, null)
                    .set(PcxBillSync::getExternalBillNo, null)
                    .set(PcxBillSync::getSyncStatus, PcxBillSyncStatus.DRAFT_PENDING_SYNC.getCode())
                    .set(PcxBillSync::getSyncTime, null)
                    .set(PcxBillSync::getSyncMessage, null);
            this.update(null, draftUpdateWrapper);
        } else {
            updateWrapper.eq(PcxBillSync::getBillId, id)
                    .set(PcxBillSync::getExternalBillId, null)
                    .set(PcxBillSync::getExternalBillNo, null)
                    .set(PcxBillSync::getSyncStatus, PcxBillSyncStatus.PENDING_SYNC.getCode())
                    .set(PcxBillSync::getSyncTime, null)
                    .set(PcxBillSync::getSyncMessage, null);
        }
        this.update(null, updateWrapper);
    }

    @Override
    public void setBillPaymentInfo() {
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

        long lastId = 0L;
        int size = 30;  // warning: 分页条数不宜设置太大，否则远程接口会报错

        while (true) {
            Page<PcxBillSync> pageRequest = new Page<>(1, size, false);

            LambdaQueryWrapper<PcxBillSync> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper
                    .gt(PcxBillSync::getId, lastId)
                    .eq(PcxBillSync::getSyncStatus, PcxBillSyncStatus.SYNCED.getCode())
                    .and(wrapper -> wrapper.isNull(PcxBillSync::getPaymentStatus)
                            .or().ne(PcxBillSync::getPaymentStatus, "C"))
                    .orderByAsc(PcxBillSync::getId);

            IPage<PcxBillSync> pageResult = this.page(pageRequest, queryWrapper);
            List<PcxBillSync> currentBatch = pageResult.getRecords();

            if (CollectionUtils.isEmpty(currentBatch)) {
                break;
            }

            try {
                transactionTemplate.execute(status -> {
                    Set<String> billIds = new HashSet<>();
                    Map<String, PcxBillSync> billSyncMap = new HashMap<>();
                    for (PcxBillSync pcxBillSync : currentBatch) {
                        billIds.add(pcxBillSync.getExternalBillId());
                        billSyncMap.put(pcxBillSync.getExternalBillId(), pcxBillSync);
                    }

                    Response<List<ReimbursementSettlementDTO>> response = reimbursementSettlementClient.pull(billIds);
                    if (response.isSuccess()) {
                        if (!CollectionUtils.isEmpty(response.getData())) {
                            List<ReimbursementSettlementDTO> reimbursementSettlementDTOS = response.getData();

                            List<PcxBillSync> toUpdateBillSyncs = new ArrayList<>();
                            for (ReimbursementSettlementDTO reimbursementSettlementDTO : reimbursementSettlementDTOS) {
                                String billId = reimbursementSettlementDTO.getBillId();
                                if (billSyncMap.containsKey(billId)) {

                                    PcxBillSync pcxBillSync = billSyncMap.get(billId);
                                    pcxBillSync.setPaymentId(reimbursementSettlementDTO.getPaymentId());
                                    pcxBillSync.setPaymentNo(reimbursementSettlementDTO.getPaymentNo());
                                    pcxBillSync.setPaymentAmt(reimbursementSettlementDTO.getPaymentAmount());
                                    pcxBillSync.setPaymentDate(reimbursementSettlementDTO.getPaymentDate());
                                    pcxBillSync.setPaymentStatus(reimbursementSettlementDTO.getPaymentStatus());
                                    List<ReimbursementVoucherDTO> vouchers = reimbursementSettlementDTO.getVouchers();

                                    StringBuilder businessDateBuilder = new StringBuilder();
                                    StringBuilder fiscalYearBuilder = new StringBuilder();
                                    StringBuilder fiscalPeriodBuilder = new StringBuilder();
                                    StringBuilder voucherWordBuilder = new StringBuilder();
                                    StringBuilder voucherNoBuilder = new StringBuilder();
                                    if (!CollectionUtils.isEmpty(vouchers)) {
                                        for (int i = 0; i < vouchers.size(); i++) {
                                            ReimbursementVoucherDTO voucherDTO = vouchers.get(i);
                                            if (i > 0) {
                                                businessDateBuilder.append(",");
                                                fiscalYearBuilder.append(",");
                                                fiscalPeriodBuilder.append(",");
                                                voucherWordBuilder.append(",");
                                                voucherNoBuilder.append(",");
                                            }
                                            businessDateBuilder.append(DateUtil.format(voucherDTO.getBusinessDate(), DatePattern.NORM_DATE_PATTERN));
                                            fiscalYearBuilder.append(voucherDTO.getFiscalYear());
                                            fiscalPeriodBuilder.append(voucherDTO.getFiscalPeriod());
                                            voucherWordBuilder.append(voucherDTO.getVoucherWord());
                                            voucherNoBuilder.append(voucherDTO.getVoucherNo());
                                        }
                                    }

                                    pcxBillSync.setBusinessDate(businessDateBuilder.toString());
                                    pcxBillSync.setFiscalYear(fiscalYearBuilder.toString());
                                    pcxBillSync.setFiscalPeriod(fiscalPeriodBuilder.toString());
                                    pcxBillSync.setVoucherWord(voucherWordBuilder.toString());
                                    pcxBillSync.setVoucherNo(voucherNoBuilder.toString());
                                    toUpdateBillSyncs.add(pcxBillSync);
                                }
                            }
                            if (!toUpdateBillSyncs.isEmpty()) {
                                this.updateBatchById(toUpdateBillSyncs);
                            }
                        }
                    }
                    return null;
                });
            } catch (Exception e) {
                log.error("{}", e.getMessage());
            }
            lastId = Long.parseLong(currentBatch.get(currentBatch.size() - 1).getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setBillPayStatus() {
        LambdaQueryWrapper<PcxBillSync> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PcxBillSync::getPaymentStatus, "C")
                .ne(PcxBillSync::getBillStatus, 1);

        PcxBillSyncDao pcxBillSyncDao = this.getBaseMapper();
        Page<PcxBillSync> page = pcxBillSyncDao.selectPage(new Page<>(1, 20), queryWrapper);
        List<PcxBillSync> pcxBillSyncs = page.getRecords();

        List<String> billIds = new ArrayList<>();
        Map<String, PcxBillSync> billSyncMap = new HashMap<>();
        for (PcxBillSync pcxBillSync : pcxBillSyncs) {
            billIds.add(pcxBillSync.getBillId());
            billSyncMap.put(pcxBillSync.getBillId(), pcxBillSync);
        }
        if (billIds.isEmpty()) {
            return;
        }
        List<PcxBill> pcxBills = pcxBillService.listByIds(billIds);

        List<PcxBill> pcxBillList = new ArrayList<>();
        List<PcxBillSync> pcxBillSyncList = new ArrayList<>();
        for (PcxBill pcxBill : pcxBills) {
            String billId = pcxBill.getId();
            if (billSyncMap.containsKey(billId)) {
                PcxBillSync pcxBillSync = billSyncMap.get(billId);
                pcxBillSync.setBillStatus(PcxBillSyncStatus.SYNCED.getCode());

                pcxBill.setPayStatus(BillPayStatusEnum.PAID.getCode());
                pcxBill.setPayTime(DateUtil.formatDateTime(pcxBillSync.getPaymentDate()));
                pcxBill.setSettlementAmt(pcxBillSync.getPaymentAmt());

                pcxBillList.add(pcxBill);
                pcxBillSyncList.add(pcxBillSync);
            }
        }

        pcxBillService.updateBatchById(pcxBillList);
        this.updateBatchById(pcxBillSyncList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void revokeBill(String billId) {
        PcxBillSync pcxBillSync = this.getById(billId);
        if (pcxBillSync != null && Objects.equals(pcxBillSync.getBillStatus(), PcxBillSyncStatus.SYNCED.getCode())) {
            pcxBillSync.setSyncStatus(PcxBillSyncStatus.PENDING_REVOKE.getCode());
            this.updateById(pcxBillSync);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resendRevokedBills() {
        LambdaQueryWrapper<PcxBillSync> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PcxBillSync::getSyncStatus, PcxBillSyncStatus.PENDING_REVOKE.getCode());
        List<PcxBillSync> pcxBillSyncs = this.list(queryWrapper);
        if (pcxBillSyncs.isEmpty()) {
            return;
        }
        List<String> billIds = pcxBillSyncs.stream().map(PcxBillSync::getBillId).collect(Collectors.toList());
        List<PcxBill> pcxBills = pcxBillService.listByIds(billIds);
        List<String> approvedBillIds = pcxBills
                .stream()
                .filter(pcxBill -> BillApproveStatusEnum.APPROVED.getCode().equals(pcxBill.getApproveStatus()))
                .map(PcxBill::getId)
                .collect(Collectors.toList());

        List<PcxBillSync> approvedPcxBillSyncs = pcxBillSyncs
                .stream()
                .filter(pcxBillSync -> approvedBillIds.contains(pcxBillSync.getBillId()))
                .peek(pcxBillSync -> pcxBillSync.setSyncStatus(PcxBillSyncStatus.PENDING_SYNC.getCode()))
                .collect(Collectors.toList());
        this.updateBatchById(approvedPcxBillSyncs);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resendClosedBills() {
        LambdaUpdateWrapper<PcxBillSync> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(PcxBillSync::getSyncStatus, PcxBillSyncStatus.IS_CLOSED.getCode())
                .set(PcxBillSync::getSyncStatus, PcxBillSyncStatus.PENDING_SYNC.getCode())
                .set(PcxBillSync::getSyncTime, null)
                .set(PcxBillSync::getSyncMessage, null);
        this.update(null, updateWrapper);
    }

}
