package com.pty.pcx.service.impl.ecs.compared;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.api.ecs.IPcxEcsComparedResultService;
import com.pty.pcx.common.enu.ComparedResultStatus;
import com.pty.pcx.common.enu.PcxBillComparedStatusEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.PcxUtil;
import com.pty.pcx.dao.ecs.PcxEcsComparedResultDao;
import com.pty.pcx.dto.ecs.inv.*;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.entity.ecscompared.PcxEcsComparedResult;
import com.pty.pcx.qo.bill.PcxBillQO;
import com.pty.pcx.qo.ecs.compared.PcxEcsComparedResultQO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class PcxEcsComparedResultServiceImpl implements IPcxEcsComparedResultService {

    @Autowired
    private PcxEcsComparedResultDao pcxEcsComparedResultDao;

    @Autowired
    private IEcsBillExternalService ecsBillExternalService;

    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;

    @Autowired
    private PcxBillService pcxBillService;

    private static final String MANUAL_SOURCE_ATTACH_ID = "manual";

    @Override
    public CheckMsg<Map<String, Integer>>  paperCompared(PcxEcsComparedResultQO qo) {
        CheckMsg<Map<String, Integer>>  result = new CheckMsg<>();
        if(StringUtil.isEmpty(qo.getSourceAttachId())){
            return result.setSuccess(false).setMsgInfo("原始附件参数不正确，请联系管理员");
        }
        if(StringUtil.isEmpty(qo.getBatchId())){
            return result.setSuccess(false).setMsgInfo("批次ID参数不正确，请联系管理员");
        }
        // 1. 获取到ecs 的数据
        List<Map<String, String>> analysisData = ecsBillExternalService.selectEcsAnalysisData(qo);
        if(CollectionUtil.isEmpty(analysisData)){
            return result.setSuccess(false).setMsgInfo("未识别到票据信息");
        }
        List<String> billNos = analysisData.stream()
                .map(map -> map.get("billNo"))
                .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(billNos)){
            insertNotFindComparedResult(qo, analysisData);
            return result.setSuccess(false).setMsgInfo("未识别到票号");
        }
        // 查询出根据ecs识别出来相关的票据
        List<PcxExpDetailEcsRel> agyConfirmEcs = getAgyConfirmEcsByBillNos(qo,billNos);
        if(CollectionUtil.isEmpty(agyConfirmEcs)){
            // 没有查询到相关联的报销单，组装票号没匹配到的异常记录
            insertNotFindComparedResult(qo, analysisData);
            return result.setSuccess(false).setMsgInfo("未查询到相关联的报销单");
        }
        Map<String, List<PcxExpDetailEcsRel>> agyConfirmEcsMap = agyConfirmEcs.stream()
                .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillNo));
        List<String> pcxBillId = agyConfirmEcs.stream().map(PcxExpDetailEcsRel::getBillId).collect(Collectors.toList());
        // 查询出相关联报销单
        List<PcxBill> pcxBills = pcxBillService.selectByBillIds(pcxBillId);
        Map<String, PcxBill> pcxBillMap = pcxBills.stream().collect(Collectors.toMap(PcxBill::getId, item -> item, (oldData, newdata) -> oldData));
        // 2. 将获取到的ecs数据与数据库中的数据进行对比
        // 识别出来的总票数
        List<PcxEcsComparedResult> pcxEcsComparedResultList = new ArrayList<>();
        // 票的匹配成功数量
        int billMatchedNum = 0;
        // 识别成功需要更新的pcx_exp_detail_ecs_rel
        for (Map<String, String> item : analysisData) {
            String billTypeCode = item.get("billTypeCode");
            String attachId = item.get("attachId");
            if(StringUtil.isEmpty(billTypeCode) || StringUtil.isEmpty(attachId)){
                // 缺少票类类型、切分后的附件id，跳出本次循环
                continue;
            }
            InvoiceBaseDto invoiceBaseDto = CollectionUtil.convertToObject(item, InvoiceBaseDto.class);
            String ecsContextJson = JSONObject.toJSONString(item);
            String ecsBillNo = invoiceBaseDto.getBillNo();
            // 2.1 票号，金额，日期作为对比的条件
            List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = agyConfirmEcsMap.get(invoiceBaseDto.getBillNo());
            if (CollectionUtil.isEmpty(pcxExpDetailEcsRels)) {
                // 对比失败-票号没匹配到
                pcxEcsComparedResultList.add(buildComparedResult(qo,invoiceBaseDto, ecsContextJson, ecsBillNo, attachId, PcxBillComparedStatusEnum.NOT_FIND));
                continue;
            }

            PcxExpDetailEcsRel pcxExpDetailEcsRel = pcxExpDetailEcsRels.get(0);
            BigDecimal billAmt = getBillAmt(pcxExpDetailEcsRels);
            if (billAmt.compareTo(invoiceBaseDto.getBillAmt()) != 0)  {
                // 金额不匹配
                PcxEcsComparedResult pcxEcsComparedResult = buildComparedResult(qo,invoiceBaseDto, ecsContextJson, ecsBillNo, attachId, PcxBillComparedStatusEnum.AMT_UNEQUAL);
                pcxEcsComparedResult.setEcsId(pcxExpDetailEcsRel.getEcsBillId());
                pcxEcsComparedResult.setPcxBillId(pcxExpDetailEcsRel.getBillId());
                pcxEcsComparedResult.setPcxBillNo(pcxBillMap.get(pcxExpDetailEcsRel.getBillId()).getBillNo());
                pcxEcsComparedResultList.add(pcxEcsComparedResult);
                continue;
            }
            // 2.2 对比成功的数据，将对比状态设置为成功；
            PcxEcsComparedResult pcxEcsComparedResult = buildComparedResult(qo, invoiceBaseDto,ecsContextJson, ecsBillNo, attachId, PcxBillComparedStatusEnum.COMPARED);
            pcxEcsComparedResult.setEcsId(pcxExpDetailEcsRel.getEcsBillId());
            pcxEcsComparedResult.setPcxBillId(pcxExpDetailEcsRel.getBillId());
            pcxEcsComparedResult.setPcxBillNo(pcxBillMap.get(pcxExpDetailEcsRel.getBillId()).getBillNo());
            pcxEcsComparedResultList.add(pcxEcsComparedResult);
            billMatchedNum++;
        }
        SpringUtil.getBean(PcxEcsComparedResultServiceImpl.class).updateCheckStatusAndProcessResults(qo, pcxBillMap, pcxEcsComparedResultList);
        Map<String, Integer> resultMap = buildResultMap(qo,billNos.size(),billMatchedNum);
        return result.setSuccess(true).setData(resultMap);
    }

    private BigDecimal getBillAmt(List<PcxExpDetailEcsRel> pcxExpDetailEcsRels) {
        if(CollectionUtil.isEmpty(pcxExpDetailEcsRels)){
            return BigDecimal.ZERO;
        }
        Map<String, List<PcxExpDetailEcsRel>> groupedByEcsDetailId = pcxExpDetailEcsRels
                .stream()
                .collect(Collectors.groupingBy(rel ->
                        Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                ));

        return groupedByEcsDetailId.values().stream()
                .map(group -> group.get(0).getEcsAmt())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void insertNotFindComparedResult(PcxEcsComparedResultQO qo, List<Map<String, String>> analysisData) {
        if(ObjectUtils.isEmpty(qo) || CollectionUtils.isEmpty(analysisData)){
            return;
        }
        List<PcxEcsComparedResult> pcxEcsComparedResultList = new ArrayList<>();
        for (Map<String, String> item : analysisData) {
            String billTypeCode = item.get("billTypeCode");
            String attachId = item.get("attachId");
            if(StringUtil.isEmpty(billTypeCode) || StringUtil.isEmpty(attachId)){
                // 缺少票类类型、切分后的附件id，跳出本次循环
                continue;
            }
            InvoiceBaseDto invoiceBaseDto = CollectionUtil.convertToObject(item, InvoiceBaseDto.class);
            String ecsContextJson = JSONObject.toJSONString(item);
            String ecsBillNo = invoiceBaseDto.getBillNo();
            pcxEcsComparedResultList.add( buildComparedResult(qo, invoiceBaseDto, ecsContextJson, ecsBillNo, attachId, PcxBillComparedStatusEnum.NOT_FIND));
        }
        PcxUtil.getPartitionBatchInsertList(pcxEcsComparedResultList).forEach(o->pcxEcsComparedResultDao.batchInsert(o));
    }

    private Map<String, Integer> buildResultMap(PcxEcsComparedResultQO qo, int size,int billMatchedNum) {
        // 初始化结果Map，设置默认值为0
        Map<String, Integer> result = new HashMap<>(6);
        result.put("invoiceTotal", size);      // 发票总数
        result.put("successInvoiceSize", 0); // 成功匹配的发票数
        result.put("failInvoiceSize", 0);   // 失败匹配的发票数
        result.put("pcxBillTotal", 0);      // PCX单据总数
        result.put("successBillSize", 0);   // 成功匹配的PCX单据数
        result.put("failBillSize", 0);      // 失败匹配的PCX单据数
        // 查询比对结果
        List<PcxEcsComparedResult> comparedResults = pcxEcsComparedResultDao.selectList(
                Wrappers.lambdaQuery(PcxEcsComparedResult.class)
                        .eq(PcxEcsComparedResult::getBatchId, qo.getBatchId())
        );
        if (CollectionUtil.isEmpty(comparedResults)) {
            return result; // 如果结果为空，直接返回默认Map
        }
        // 获取唯一单据ID
        Set<String> billIds = comparedResults.stream()
                .map(PcxEcsComparedResult::getPcxBillId)
                .filter(StringUtil::isNotBlank)
                .collect(Collectors.toSet());
        int pcxBillTotal = billIds.size(); // PCX单据总数
        int successBillSize = 0; // 成功匹配的单据数
        // 如果存在有效单据ID，计算单据相关指标
        if (!billIds.isEmpty()) {
            List<PcxEcsComparedResult> billResults = pcxEcsComparedResultDao.selectList(
                    Wrappers.lambdaQuery(PcxEcsComparedResult.class)
                            .in(PcxEcsComparedResult::getPcxBillId, billIds)
            );
            // 按单据ID分组
            Map<String, List<PcxEcsComparedResult>> billGroups = billResults.stream()
                    .filter(item -> StringUtil.isNotBlank(item.getPcxBillId()))
                    .collect(Collectors.groupingBy(PcxEcsComparedResult::getPcxBillId));
            // 统计所有比对状态均为成功的单据数
            successBillSize = (int) billGroups.entrySet().stream()
                    .filter(entry -> entry.getValue().stream()
                            .allMatch(item -> PcxBillComparedStatusEnum.COMPARED.getCode().equals(item.getComparedStatus())))
                    .count();
        }
        // 更新结果Map
        result.put("successInvoiceSize", billMatchedNum); // 成功匹配的发票数
        result.put("failInvoiceSize", size - billMatchedNum); // 失败匹配的发票数
        result.put("pcxBillTotal", pcxBillTotal); // PCX单据总数
        result.put("successBillSize", successBillSize); // 成功匹配的PCX单据数
        result.put("failBillSize", pcxBillTotal - successBillSize); // 失败匹配的PCX单据数
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCheckStatusAndProcessResults( PcxEcsComparedResultQO qo, Map<String, PcxBill> pcxBillMap, List<PcxEcsComparedResult> pcxEcsComparedResultList) {
        Map<String, List<PcxEcsComparedResult>> invoiceGroup = pcxEcsComparedResultList.stream()
                .collect(Collectors.groupingBy(PcxEcsComparedResult::getComparedStatus));
        // 2.3 更新pcx_exp_detail_ecs_rel
        List<PcxEcsComparedResult> comparedResults = invoiceGroup.get(PcxBillComparedStatusEnum.COMPARED.getCode());
        Set<String> successInvoiceNos = new HashSet<>();
        if(CollectionUtil.isNotEmpty(comparedResults)){
            successInvoiceNos = comparedResults.stream().map(PcxEcsComparedResult::getEcsBillNo).collect(Collectors.toSet());
            pcxExpDetailEcsRelService.updateCheckStatusByInvoiceNos(new ArrayList<>(successInvoiceNos), PubConstant.LOGIC_TRUE);
        }
        // 2.4 更新失败的票据状态
        List<PcxEcsComparedResult> failResults = new ArrayList<>();
        if (invoiceGroup.get(PcxBillComparedStatusEnum.AMT_UNEQUAL.getCode()) != null) {
            failResults.addAll(invoiceGroup.get(PcxBillComparedStatusEnum.AMT_UNEQUAL.getCode()));
        }
        if (invoiceGroup.get(PcxBillComparedStatusEnum.NOT_FIND.getCode()) != null) {
            failResults.addAll(invoiceGroup.get(PcxBillComparedStatusEnum.NOT_FIND.getCode()));
        }
        if(CollectionUtil.isNotEmpty(failResults)){
            Set<String> failInvoiceNos = failResults.stream().map(PcxEcsComparedResult::getEcsBillNo).collect(Collectors.toSet());
            pcxExpDetailEcsRelService.updateCheckStatusByInvoiceNos(new ArrayList<>(failInvoiceNos), ComparedResultStatus.REJECTED.getCode());
        }
        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelService.selectByBillIds(new ArrayList<>(pcxBillMap.keySet()));
        Map<String, List<PcxExpDetailEcsRel>> pcxExpDetailEcsRelMap = pcxExpDetailEcsRels.stream()
                .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getBillId));
        List<String> mainBillIds = new ArrayList<>(); // 主单据
        List<PcxEcsComparedResult> notFindList = new ArrayList<>();
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : pcxExpDetailEcsRelMap.entrySet()) {
            List<PcxExpDetailEcsRel> ecsRelList = entry.getValue();
            Map<String, List<PcxExpDetailEcsRel>> unCheckRelMap = ecsRelList.stream()
                    .filter(item ->ObjectUtils.isEmpty(item.getEcsCheckStatus()) || Objects.equals(ComparedResultStatus.UNCHECKED.getCode(), item.getEcsCheckStatus()))
                    .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillNo));
            if (CollectionUtil.isNotEmpty(unCheckRelMap)) {
                for (String escBillNo : unCheckRelMap.keySet()) {
                    PcxEcsComparedResult pcxEcsComparedResult = buildComparedResult(qo, null, StringUtil.EMPTY, escBillNo, StringUtil.EMPTY, PcxBillComparedStatusEnum.BILL_UNRELATED);
                    PcxExpDetailEcsRel pcxExpDetailEcsRel = unCheckRelMap.get(escBillNo).get(0);
                    pcxEcsComparedResult.setPcxBillId(pcxExpDetailEcsRel.getBillId());
                    notFindList.add(pcxEcsComparedResult);
                }
                continue;
            }
            mainBillIds.add(entry.getKey());
        }
        pcxEcsComparedResultList.addAll(notFindList);
        // 插入票对比结果的数据
        if (CollectionUtil.isNotEmpty(notFindList) || CollectionUtil.isNotEmpty(successInvoiceNos)) {
            // Ensure notFindList and successInvoiceNos are not null
            Set<String> invoiceBillNo = notFindList.stream().map(PcxEcsComparedResult::getEcsBillNo).collect(Collectors.toSet());
            List<String> deleteKey = new ArrayList<>(successInvoiceNos);
            deleteKey.addAll(invoiceBillNo);
            if (CollectionUtil.isNotEmpty(deleteKey)) {
                pcxEcsComparedResultDao.delete(Wrappers.lambdaQuery(PcxEcsComparedResult.class)
                        .in(PcxEcsComparedResult::getEcsBillNo, deleteKey));
            }
        }
        List<List<PcxEcsComparedResult>> partitionPcxEcsComparedResult = CollectionUtil.partition(pcxEcsComparedResultList, 500);
        for (List<PcxEcsComparedResult> pcxEcsComparedResults : partitionPcxEcsComparedResult) {
            pcxEcsComparedResultDao.batchInsert(pcxEcsComparedResults);
        }
        // 3 当对比成功的票据数量和单据的票据数量一致时，将单据pcx_bill 对比状态设置为成功
        if(CollectionUtil.isNotEmpty(mainBillIds)){
            updateBillStatus(mainBillIds, ComparedResultStatus.CHECKED.getCode());
        }
        if(CollectionUtil.isNotEmpty(pcxExpDetailEcsRels)){
            Map<Integer, List<PcxExpDetailEcsRel>> pcxExpDetailMap = pcxExpDetailEcsRels.stream().collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsCheckStatus));
            List<PcxExpDetailEcsRel> exceptionalRels = pcxExpDetailMap.get(ComparedResultStatus.REJECTED.getCode());
            if(CollectionUtil.isEmpty(exceptionalRels)){
                return;
            }
            List<String> exceptionalBillIds = exceptionalRels.stream().map(PcxExpDetailEcsRel::getBillId).collect(Collectors.toList());
            updateBillStatus(exceptionalBillIds, ComparedResultStatus.REJECTED.getCode());
        }
    }

    private void updateBillStatus(List<String> billIds, int statusCode) {
        PcxBillQO pcxBillQO = new PcxBillQO();
        pcxBillQO.setIds(billIds);
        PcxBill pcxBill = new PcxBill();
        pcxBill.setComparedStatus(statusCode);
        pcxBillQO.setPcxBill(pcxBill);
        pcxBillService.updateComparedStatus(pcxBillQO);
    }

    @Override
    public Response<PageInfo<PcxEcsComparedResult>> selectWithPage(PcxEcsComparedResultQO qo) {
        PageInfo<PcxEcsComparedResult> pageInfo = PageHelper.startPage(qo.getPageIndex(), qo.getPageSize())
                .doSelectPageInfo(() -> {
                    pcxEcsComparedResultDao.selectList(Wrappers.lambdaQuery(PcxEcsComparedResult.class).eq(PcxEcsComparedResult::getAgyCode, qo.getAgyCode())
                            .eq(PcxEcsComparedResult::getMofDivCode, qo.getMofDivCode())
                            .eq(PcxEcsComparedResult::getFiscal, qo.getFiscal())
                            .ne(PcxEcsComparedResult::getComparedStatus, PubConstant.STR_LOGIC_TRUE) // 过滤掉已对比的记录
                            .orderByDesc(PcxEcsComparedResult::getCreatedTime));
                });
        return Response.success(pageInfo);
    }

    @Override
    public CheckMsg remove(PcxEcsComparedResultQO qo) {
        /**
         * scc
         * todo msj 后续删除的功能还会有其他限定条件，比如只有未匹配到报销单时才能删除。这个后续再跟产品确认
         * 另外，我在QO中增加idList的参数，考虑后续兼容批操作
         */
        if(StringUtil.isEmpty(qo.getId())){
            return CheckMsg.fail("请选择要删除的数据");
        }
        pcxEcsComparedResultDao.deleteById(qo.getId());
        return CheckMsg.success();
    }

    @Override
    public CheckMsg<List<PcxEcsComparedResult>> selectResultByBillId(PcxEcsComparedResultQO qo) {
        if(ObjectUtils.isEmpty(qo) || StringUtil.isEmpty(qo.getPcxBillId())){
            return CheckMsg.fail("参数不正确");
        }
        List<PcxEcsComparedResult> pcxEcsComparedResults = pcxEcsComparedResultDao.queryList(qo);
        CheckMsg<List<PcxEcsComparedResult>> success = CheckMsg.success();
        success.setData(pcxEcsComparedResults);
        return success;
    }

    @Override
    public CheckMsg<PcxEcsComparedResult> selectComparedDetail(PcxEcsComparedResultQO qo) {
        CheckMsg<PcxEcsComparedResult> resultCheckMsg = CheckMsg.success();
        if(ObjectUtils.isEmpty(qo) || StringUtil.isEmpty(qo.getEcsBillNo())){
            return CheckMsg.fail("参数不正确");
        }
        PcxEcsComparedResult pcxEcsComparedResult = pcxEcsComparedResultDao.selectOne(Wrappers.lambdaQuery(PcxEcsComparedResult.class)
                .eq(PcxEcsComparedResult::getEcsBillNo, qo.getEcsBillNo()));
        return resultCheckMsg.setData(pcxEcsComparedResult);
    }

    @Override
    public CheckMsg<Map> selectById(PcxEcsComparedResultQO qo) {
        CheckMsg<Map> resultCheckMsg = CheckMsg.success();
        if(ObjectUtils.isEmpty(qo) || StringUtil.isEmpty(qo.getId())){
            return CheckMsg.fail("参数不正确");
        }
        PcxEcsComparedResult pcxEcsComparedResult = pcxEcsComparedResultDao.selectOne(Wrappers.lambdaQuery(PcxEcsComparedResult.class)
                .eq(PcxEcsComparedResult::getId, qo.getId()));
        String billContext = pcxEcsComparedResult.getBillContext();
        Map maps = JSONObject.parseObject(billContext, Map.class);
        return resultCheckMsg.setData(maps);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg manual(PcxEcsComparedResultQO qo) {
        // 验证输入参数
        if (StringUtil.isEmpty(qo.getEcsBillNo()) ||
                StringUtil.isEmpty(qo.getPcxBillId()) ||
                StringUtil.isEmpty(qo.getComparedStatus())) {
            return CheckMsg.fail("参数不正确");
        }
        // 验证驳回原因
        if (PubConstant.STR_LOGIC_FALSE.equals(qo.getComparedStatus()) &&
                StringUtil.isEmpty(qo.getComparedSummary())) {
            return CheckMsg.fail("请填写驳回原因");
        }
        // 创建并填充结果对象
        PcxEcsComparedResult result = new PcxEcsComparedResult();
        BeanUtils.copyProperties(qo, result);
        result.setId(StringUtil.getUUID());
        result.setCreator(qo.getUserCode());
        result.setCreatorName(qo.getUserName());
        result.setCreatedTime(DateUtil.nowTime());
        result.setModifier(qo.getUserCode());
        result.setModifierName(qo.getUserName());
        result.setModifiedTime(DateUtil.nowTime());
        // 设置状态并更新数据库
        boolean isRejected = PubConstant.STR_LOGIC_FALSE.equals(qo.getComparedStatus());
        result.setComparedStatus(isRejected ? PubConstant.STR_LOGIC_FALSE : PubConstant.STR_LOGIC_TRUE);

        //查询出报销单所有的票关联关系
        List<PcxExpDetailEcsRel> relations = pcxExpDetailEcsRelService.selectByBillId(qo.getPcxBillId());
        //报销单所有的票的比对结果
        Set<Integer> compareStatus = new HashSet<>();
        //过滤出当前操作的票
        List<PcxExpDetailEcsRel> optRelList = new ArrayList<>();
        for (PcxExpDetailEcsRel relation : relations) {
            if (Objects.equals(relation.getEcsBillNo(), qo.getEcsBillNo())) {
                relation.setEcsCheckStatus(isRejected ? ComparedResultStatus.REJECTED.getCode() : ComparedResultStatus.CHECKED.getCode());
                optRelList.add(relation);
            }
            if (StringUtil.isNotEmpty(relation.getEcsBillId())){
                compareStatus.add(Objects.nonNull(relation.getEcsCheckStatus())? relation.getEcsCheckStatus() : ComparedResultStatus.UNCHECKED.getCode());
            }
        }
        if(CollectionUtil.isNotEmpty(optRelList)){
            PcxExpDetailEcsRel rel = optRelList.get(0);
            result.setEcsId(rel.getEcsBillId());
            result.setEcsBillKind(rel.getEcsBillKind());
            for (PcxExpDetailEcsRel pcxExpDetailEcsRel : optRelList) {
                pcxExpDetailEcsRelService.updateById(pcxExpDetailEcsRel);
            }

        }
        result.setSourceAttachId(MANUAL_SOURCE_ATTACH_ID);
        pcxEcsComparedResultDao.insert(result);
        Integer billCompareStatus = ComparedResultStatus.CHECKED.getCode();
        //如果有一个票时对比异常，则报销单就是对比异常
        //如果都是比对完成或者无需比对，则报销单就是比对完成
        //剩下的情况就是票都未必对，则报销单就是未比对
        if (compareStatus.contains(ComparedResultStatus.REJECTED.getCode())){
            billCompareStatus = ComparedResultStatus.REJECTED.getCode();
        }else if (compareStatus.contains(ComparedResultStatus.UNCHECKED.getCode())){
            billCompareStatus = ComparedResultStatus.UNCHECKED.getCode();
        }
        // 返回成功信息
        updateBillStatus(Collections.singletonList(qo.getPcxBillId()), billCompareStatus);
        return CheckMsg.success();
    }

    /***
     * 判断是否需要更新主表信息
     * @param pcxBillId
     */
    private void updatePcxMailBill(String pcxBillId,List<PcxExpDetailEcsRel> relations) {
        if (StringUtil.isEmpty(pcxBillId) || relations == null || relations.isEmpty()) {
            return;
        }
        long count = relations.stream().filter(item -> PubConstant.LOGIC_FALSE == item.getEcsCheckStatus()).count();
        if (count == 0) {
            updateBillStatus(Collections.singletonList(pcxBillId), ComparedResultStatus.CHECKED.getCode());
        }
    }

    /**
     * 获取单位所有已确认的票据信息
     * @param qo
     * @return
     */
    private List<PcxExpDetailEcsRel> getAgyConfirmEcsByBillNos(PcxEcsComparedResultQO qo,List<String> billNos) {
        PcxExpDetailEcsRel pcxExpDetailEcsRel = new PcxExpDetailEcsRel(qo.getAgyCode(),qo.getFiscal()
                ,qo.getMofDivCode(),StringUtil.isEmpty(qo.getTenantId())? PtyContext.getTenantId() : qo.getTenantId());
        return pcxExpDetailEcsRelService.selectAgyConfirmEcsList(pcxExpDetailEcsRel,billNos);
    }

    private PcxEcsComparedResult buildComparedResult(PcxEcsComparedResultQO qo,InvoiceBaseDto invoiceBaseDto,String billContext,String ecsBillNo,String attachId , PcxBillComparedStatusEnum comparedStatusEnum){
        PcxEcsComparedResult comparedResult = new PcxEcsComparedResult();
        if(!ObjectUtils.isEmpty(invoiceBaseDto)){
            comparedResult.setEcsBillKind(invoiceBaseDto.getBillKind());
            comparedResult.setEcsBillType(invoiceBaseDto.getBillTypeCode());
            comparedResult.setEcsSummary(invoiceBaseDto.getBillDescpt());
            comparedResult.setEcsBillAmt(StringUtil.getStringValue(invoiceBaseDto.getBillAmt()));
        }
        comparedResult.setAgyCode(qo.getAgyCode());
        comparedResult.setBatchId(qo.getBatchId());
        comparedResult.setMofDivCode(qo.getMofDivCode());
        comparedResult.setFiscal(qo.getFiscal());
        comparedResult.setCreator(qo.getUserCode());
        comparedResult.setCreatorName(qo.getUserName());
        comparedResult.setCreatedTime(DateUtil.nowTime());
        comparedResult.setSourceAttachId(qo.getSourceAttachId());
        comparedResult.setAttachId(attachId);
        comparedResult.setEcsBillNo(ecsBillNo);
        comparedResult.setBillContext(billContext);
        if (PcxBillComparedStatusEnum.COMPARED.getCode().equals(comparedStatusEnum.getCode())) {
            comparedResult.setComparedStatus(StringUtil.getStringValue(ComparedResultStatus.CHECKED.getCode()));
        } else if(PcxBillComparedStatusEnum.BILL_UNRELATED.getCode().equals(comparedStatusEnum.getCode())){
            comparedResult.setComparedStatus(StringUtil.getStringValue(ComparedResultStatus.UNCHECKED.getCode()));
        }else {
            comparedResult.setComparedStatus(StringUtil.getStringValue(ComparedResultStatus.REJECTED.getCode()));
        }
        comparedResult.setComparedSummary(comparedStatusEnum.getName());
        comparedResult.setTenantId(StringUtil.isNotBlank(qo.getTenantId())? qo.getTenantId() : PtyContext.getTenantId());
        comparedResult.setId(StringUtil.getUUID());
        comparedResult.setModifiedTime(DateUtil.nowTime());
        comparedResult.setModifier(qo.getUserCode());
        comparedResult.setModifierName(qo.getUserName());
        return comparedResult;
    }
}
