package com.pty.pcx.service.impl.treasurypay.callback;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.ep.api.IEBankReceiptNoticeService;
import com.pty.ep.entity.vo.EPayResult;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillPayDetailPlusService;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Indexed
@Service
public class PcxEpBankReceiptNoticeService implements IEBankReceiptNoticeService {

    @Autowired
    private IPcxBillPayDetailPlusService pcxBillPayDetailPlusService;

    //银行回单结果通知接口
    @Override
    public void bankReceiptNotice(List<EPayResult> bankReceiptList) {
        for (EPayResult ePayResult : bankReceiptList) {
            if (ePayResult.getStatus() == 1 && StringUtils.isNoneBlank(ePayResult.getBillId()) && StringUtils.isNoneBlank(ePayResult.getSettlementId())){
                PcxBillPayDetail billPayDetail = pcxBillPayDetailPlusService.getOne(
                        new LambdaQueryWrapper<PcxBillPayDetail>()
                                .eq(PcxBillPayDetail::getBillId, ePayResult.getBillId())
                                .eq(PcxBillPayDetail::getPayNo, ePayResult.getSettlementId())
                                .eq(StringUtils.isNoneBlank(ePayResult.getAgyCode()), PcxBillPayDetail::getAgyCode, ePayResult.getAgyCode())
                                .eq(StringUtils.isNoneBlank(ePayResult.getFiscal()), PcxBillPayDetail::getFiscal, ePayResult.getFiscal()));
                if (billPayDetail != null){
                    // 设置已生成银行回单
                    billPayDetail.setBankReceipt("1");
                    pcxBillPayDetailPlusService.updateById(billPayDetail);
                }
            }
        }
    }
}
