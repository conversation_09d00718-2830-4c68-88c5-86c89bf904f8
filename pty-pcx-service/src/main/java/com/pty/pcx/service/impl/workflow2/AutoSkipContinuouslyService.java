package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.CollectionUtils;
import com.pty.mad.entity.PaOption;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.workflow2.api.IPtyWfNodeService;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import org.pty.mad.api.IPaOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.pty.pcx.common.constant.PcxConstant.OPTION_CODE_POSITION_MUST_APPROVE;
import static com.pty.pcx.common.constant.PcxConstant.OPTION_CODE_POSITION_NOT_AUTO_SKIP;


@Service(PcxConstant.SYS_ID + "PtyWfNodeService")
public class AutoSkipContinuouslyService implements IPtyWfNodeService {

    @Autowired
    IPaOptionService paOptionService;
    @Autowired
    BillMainService billMainService;


    @Override
    public Boolean checkJumpPermission(String billId, String nodeId) {

        PcxBill bill = billMainService.view(billId);

        Assert.notNull(bill, "判断是否可跳过节点[{}]未找到单据信息,id={}", nodeId, billId);
        PaOption paOption = new PaOption();
        paOption.setAgyCode(bill.getAgyCode());
        paOption.setOptCode(OPTION_CODE_POSITION_NOT_AUTO_SKIP);
        paOption.setFiscal(DateUtil.thisYear());
        List<PaOption> paOptions = paOptionService.select(paOption);

        if (CollectionUtils.isEmpty(paOptions)) {
            paOption.setAgyCode("*");
            paOptions = paOptionService.select(paOption);
        }

        Assert.notEmpty(paOptions, "判断是否可跳过节点[{}]未找到配置信息", nodeId);

        Boolean result = Boolean.TRUE;

        if (!paOptions.isEmpty()) {
            paOption = paOptions.get(0);
            String[] values = paOption.getOptValue().split(",");
            for (String value : values) {
                if (nodeId.startsWith(value)) {
                    return Boolean.FALSE;
                }
            }
        }
        return result;
    }
}
