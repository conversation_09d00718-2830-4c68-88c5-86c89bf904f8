package com.pty.pcx.service.impl.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.balance.entity.PtyBalanceData;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.block.IBaseBlockService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.setting.IPcxPaFieldSettingService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.balance.IBalanceExternalService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.FieldDisptypeEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.project.PcxProjectExpenseDao;
import com.pty.pcx.dao.setting.PcxPaFieldSettingDao;
import com.pty.pcx.dto.BatchProcessItem;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.entity.project.PcxProjectExpense;
import com.pty.pcx.entity.setting.PaFieldSetting;
import com.pty.pcx.mad.IMadDepartmentExternalService;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.block.BlockPropertyQO;
import com.pty.pcx.qo.setting.InvoiceGuideQO;
import com.pty.pcx.qo.setting.PaFieldSettingQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.qo.workflow2.PositionQO;
import com.pty.pcx.service.impl.bud.PcxBudBalanceService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.balance.ProjectBalVO;
import com.pty.pcx.vo.setting.InvoiceGuideVO;
import com.pty.pcx.vo.setting.PaFieldSettingVO;
import com.pty.pcx.vo.workflow2.PositionVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxConstant.BUDGET_MENU_URL;

@Slf4j
@Indexed
@Service
public class PcxPaFieldSettingServiceImpl implements IPcxPaFieldSettingService, IBaseBlockService {

    //没用启用分摊不展示的字段
    private static final Set<String> NO_SHARE_FIELD = new HashSet<>(Arrays.asList("shareRate", "shareAmt"));


    @Autowired
    private PcxPaFieldSettingDao paFieldSettingDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private IBusinessRuleOptionService bussinessRuleOptionService;

    @Autowired
    private Map<String, IBalanceExternalService> balanceExternalServiceMap;
    @Autowired
    private PcxBudBalanceService pcxBudBalanceService;

    @Autowired
    private PcxProjectExpenseDao pcxProjectExpenseDao;

    @Autowired
    private IPcxBasItemService iPcxBasItemService;

    @Autowired
    private IProcessService processService;

    @Autowired
    private IMadDepartmentExternalService madDepartmentExternalService;
    @Autowired
    private IBusinessRuleOptionService businessRuleOptionService;

    @Override
    public List<PaFieldSettingVO> getMetricSetting(PaFieldSettingQO paFieldSettingQO) {
        if (ObjectUtils.isEmpty(paFieldSettingQO)) {
            return new ArrayList<>();
        }
        IBalanceExternalService balanceExternalService = getBalanceBean(paFieldSettingQO.getMofDivCode(), paFieldSettingQO.getAgyCode(), paFieldSettingQO.getFiscal(), paFieldSettingQO.getTenantId());
        //查余额模块接口
        List<PaFieldSettingVO> balancePaFieldList = balanceExternalService.getBalanceElementList(paFieldSettingQO, BalanceTypeEnum.BUD.getCode());
        if (CollectionUtil.isEmpty(balancePaFieldList)) {
            return new ArrayList<>();
        }
        //查预算指标类型的数据
        List<PaFieldSettingVO> paFieldSettingList = paFieldSettingDao.selectByMenuUrl(paFieldSettingQO);
        //若单位级数据为空，则使用系统级数据
        if (CollectionUtil.isEmpty(paFieldSettingList)) {
            PaFieldSettingQO qo = new PaFieldSettingQO();
            qo.setAgyCode(PcxConstant.SYSTEM_FLAG);
            qo.setFiscal(PcxConstant.SYSTEM_FLAG);
            qo.setMofDivCode(PcxConstant.SYSTEM_FLAG);
            qo.setType(BalanceTypeEnum.BUD.getCode());
            qo.setMenuUrl(BUDGET_MENU_URL);
            paFieldSettingList = paFieldSettingDao.selectByType(qo);
            paFieldSettingList.forEach(item -> {
                item.setId(IDGenerator.id());
                item.setAgyCode(paFieldSettingQO.getAgyCode());
                item.setFiscal(paFieldSettingQO.getFiscal());
                item.setMofDivCode(paFieldSettingQO.getMofDivCode());
                item.setMenuUrl(paFieldSettingQO.getMenuUrl());
                item.setTenantId(paFieldSettingQO.getTenantId() == null ? PtyContext.getTenantId() : paFieldSettingQO.getTenantId());
            });
        }
        Map<String, PaFieldSettingVO> balanceElemenMap = paFieldSettingList.stream().collect(Collectors.toMap(PaFieldSetting::getColumnCode, Function.identity(), (existing, replacement) -> existing));
        List<PaFieldSettingVO> totalList = new ArrayList<>();
            for (PaFieldSettingVO paFieldSettingVO : balancePaFieldList) {
            //若本地有，则使用本地数据
            if (balanceElemenMap.containsKey(paFieldSettingVO.getColumnCode())) {
                PaFieldSettingVO balamceEle = balanceElemenMap.get(paFieldSettingVO.getColumnCode());
                PaFieldSettingVO balData = new PaFieldSettingVO();
                BeanUtils.copyProperties(balamceEle, balData);
                totalList.add(balData);
                balanceElemenMap.remove(paFieldSettingVO.getColumnCode());
                continue;
            }
            if (StringUtil.isEmpty(paFieldSettingVO.getTenantId())) {
                paFieldSettingVO.setTenantId(PtyContext.getTenantId());
            }
            paFieldSettingVO.setMenuUrl(paFieldSettingQO.getMenuUrl());
            // mashaojie-20250102 - 指标模版没有返回数据类型，所以默认为字符串
            paFieldSettingVO.setDataType(DataTypeEnum.STRING.getCode());
            paFieldSettingQO.setDisabled(PubConstant.STR_LOGIC_TRUE);
            paFieldSettingQO.setRequired(PubConstant.STR_LOGIC_TRUE);
            paFieldSettingQO.setEditor(FieldDisptypeEnum.INPUT.getCode());
            totalList.add(paFieldSettingVO);
        }
        //最终balanceElemenMap剩下预制的数据
        totalList.addAll(balanceElemenMap.values());
        //balancePaFieldList按照orderSeq排序,如果为空则放到末尾
        return totalList.stream()
                .sorted(Comparator.comparing(PaFieldSettingVO::getOrdSeq, Comparator.nullsLast(Integer::compare)))
                .collect(Collectors.toList());
    }

    private IBalanceExternalService getBalanceBean(String mofDivCode, String agyCode, String fiscal,String tenantId) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setFiscal(Integer.parseInt(fiscal));
        paOptionQO.setMofDivCode(mofDivCode);
        paOptionQO.setAgyCode(agyCode);
        paOptionQO.setTenantId(tenantId);
        String balanceServiceName = bussinessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.BALANCE_SERVICE_BEAN.getOptCode());
        IBalanceExternalService balanceExternalService = balanceExternalServiceMap.get(balanceServiceName);
        if(ObjectUtils.isEmpty(balanceExternalService)){
            log.error("获取bean失败,optCode{},mof_div_code: {},agy_code:{},fiscal:{},tenant_id:{}", BusinessRuleEnum.BusinessOptionEnum.BALANCE_SERVICE_BEAN.getOptCode(),mofDivCode, agyCode, fiscal, tenantId);
            throw new RuntimeException("业务处理失败");
        }
        return balanceExternalService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg deleteAndInsert(List<PaFieldSetting> paFieldSettingList) {
        BatchProcessItem batchProcessDeleteItem = new BatchProcessItem<>(paFieldSettingList, PcxPaFieldSettingDao.class, PcxPaFieldSettingDao::delById);
        BatchProcessItem batchProcessInsertItem = new BatchProcessItem<>(paFieldSettingList, PcxPaFieldSettingDao.class, PcxPaFieldSettingDao::insertSelective);
        batchServiceUtil.batchProcess(batchProcessDeleteItem, batchProcessInsertItem);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg updatePaFieldSettingByUser(List<PaFieldSetting> paFieldSettingList) {
        //删除用户自定义字段
        PaFieldSetting paFieldSetting = new PaFieldSetting();
        paFieldSetting.setUserCode(paFieldSettingList.get(0).getUserCode());
        paFieldSetting.setSysId(paFieldSettingList.get(0).getSysId());
        paFieldSetting.setMenuUrl(paFieldSettingList.get(0).getMenuUrl());
//        paFieldSetting.setFiscal(paFieldSettingList.get(0).getFiscal());// 年度不做限制
        paFieldSetting.setMofDivCode(paFieldSettingList.get(0).getMofDivCode());
        paFieldSetting.setAgyCode(paFieldSettingList.get(0).getAgyCode());
        paFieldSettingDao.delByUser(paFieldSetting);
        //新增用户自定义字段
        paFieldSettingList.stream().map(item -> {
            item.setId(IDGenerator.id());
            return item;
        }).collect(Collectors.toList());
        BatchProcessItem batchProcessInsertItem = new BatchProcessItem<>(paFieldSettingList, PcxPaFieldSettingDao.class, PcxPaFieldSettingDao::insertSelective);
        batchServiceUtil.batchProcess(batchProcessInsertItem);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    public List<PaFieldSettingVO> getSettings(PaFieldSettingQO paFieldSettingQO) {
        List<PaFieldSettingVO> settings = paFieldSettingDao.select(paFieldSettingQO);
        if(CollectionUtil.isEmpty(settings)){
            return new ArrayList<>();
        }
        return settings;
    }

    @Override
    public List<PaFieldSettingVO> getUserSettings(PaFieldSettingQO paFieldSettingQO) {
        //查询该菜单列表下用户自定义和系统预设的所有数据
        List<PaFieldSettingVO> settings = paFieldSettingDao.select(paFieldSettingQO);
        if(CollectionUtil.isEmpty(settings)){
            return new ArrayList<>();
        }
        List<PaFieldSettingVO> sysSettings = settings.stream().filter(item -> "*".equals(item.getUserCode())).collect(Collectors.toList());
        //查询该菜单列表下用户自定义的数据
        List<PaFieldSettingVO> userSettings = settings.stream().filter(item -> item.getUserCode().equals(paFieldSettingQO.getUserCodeOrAll())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(userSettings)) {//用户没有自定义数据，则返回系统预设的数据
            return sysSettings;
        } else {
            Map<String, PaFieldSettingVO> userSettingsMap = userSettings.stream().collect(Collectors.toMap(PaFieldSettingVO::getColumnCode, Function.identity(),(a, b) -> a));
            Map<String, PaFieldSettingVO> sysSettingsMap = sysSettings.stream().collect(Collectors.toMap(PaFieldSettingVO::getColumnCode, Function.identity(),(a, b) -> a));
            sysSettingsMap.forEach((k, v) -> {
                if (!userSettingsMap.containsKey(k)) {// 预设新字段数据没在用户自定义数据列表中，则添加到用户自定义字段数据列表
                    userSettingsMap.put(k, v);
                }
            });
            if (userSettingsMap.size() != sysSettingsMap.size()) {
                List<String> key = new ArrayList<String>();
                userSettingsMap.forEach((k, v) -> {
                    if (!sysSettingsMap.containsKey(k)) {// 用户自定义字段数据没在预设字段数据列表，则删除
                        key.add(k);
                    }
                });
                if (ObjectUtil.isNotEmpty(key)) {
                    key.forEach(k -> userSettingsMap.remove(k));
                }
            }
            if (ObjectUtil.isNotEmpty(userSettingsMap)) {
                List<PaFieldSettingVO> list = userSettingsMap.values().stream().sorted(Comparator.comparing(
                        PaFieldSettingVO::getOrdSeq,
                        Comparator.nullsFirst(Comparator.naturalOrder())  // null 排前面
                )).collect(Collectors.toList());
                return list;
            }
            return new ArrayList<>();
        }
    }

    /**
     * 查询开票助手-开票指引
     * @param invoiceGuideQO
     * @return
     */
    @Override
    public CheckMsg<List<InvoiceGuideVO>> selectInvoiceGuide(InvoiceGuideQO invoiceGuideQO) {
        // 校验补全查询参数
        InvoiceGuideQO qo = validateInvoiceGuideQuery(invoiceGuideQO);

        //查询该菜单列表下所有数据
        PaFieldSettingQO paFieldSettingQO = new PaFieldSettingQO();
        BeanUtils.copyProperties(qo, paFieldSettingQO);
        List<PaFieldSettingVO> paFieldSettings = paFieldSettingDao.select(paFieldSettingQO);
        //过滤可显示数据
        List<PaFieldSettingVO> paFieldSettingVOList = paFieldSettings.stream().filter(item -> item.getIsShow() == 1).collect(Collectors.toList());
        return CheckMsg.success(BeanUtil.copyToList(paFieldSettingVOList, InvoiceGuideVO.class));
    }

    /**
     * 校验补全查询参数
     * @param invoiceGuideQO
     */
    private InvoiceGuideQO validateInvoiceGuideQuery(InvoiceGuideQO invoiceGuideQO) {
        if(ObjectUtils.isEmpty(invoiceGuideQO)){
            // 请求体为空,设置默认值
            InvoiceGuideQO invoiceGuideQO1 = new InvoiceGuideQO();
            invoiceGuideQO1.setSysId("PCX");
            invoiceGuideQO1.setMenuUrl("/pcx/pcx-guide-app-icon");
            invoiceGuideQO1.setAgyCode("*");
            invoiceGuideQO1.setFiscal("*");
            invoiceGuideQO1.setMofDivCode("87");
            return invoiceGuideQO1;
        }
        if (StringUtil.isEmpty(invoiceGuideQO.getSysId())){
            invoiceGuideQO.setSysId("PCX");
        }
        if (StringUtil.isEmpty(invoiceGuideQO.getMenuUrl())){
            invoiceGuideQO.setMenuUrl("/pcx/pcx-guide-app-icon");
        }
        if (StringUtil.isEmpty(invoiceGuideQO.getAgyCode())){
            invoiceGuideQO.setAgyCode("*");
        }
        if (StringUtil.isEmpty(invoiceGuideQO.getFiscal())){
            invoiceGuideQO.setFiscal("*");
        }
        if (StringUtil.isEmpty(invoiceGuideQO.getMofDivCode())){
            invoiceGuideQO.setMofDivCode("87");
        }

        return invoiceGuideQO;
    }

    @Override
    public CheckMsg<List<ProjectBalVO>> getProjectData(PcxBalancesQO qo) {
        CheckMsg validationMsg = validateBalParam(qo);
        if (!validationMsg.isSuccess()) {
            return validationMsg;
        }

        IBalanceExternalService balanceExternalService = getBalanceBean(qo.getMofDivCode(), qo.getAgyCode(), qo.getFiscal(), qo.getTenantId());
        String[] balanceTypes = {BalanceTypeEnum.IBAL.getCode()};
        if (!PositionEnum.isFinance(qo.getPositionCode())){
            List<Pair<String, String>>  parentDept = pcxBudBalanceService.getParentDept(qo.getDepartmentCode(), qo.getFiscal(), qo.getMofDivCode());
            qo.setDepartmentCodes(parentDept.stream().map(Pair::getLeft).collect(Collectors.toList()));
        }
        List<ProjectBalVO> projectBalances = balanceExternalService.getBalances(qo, balanceTypes);
        //通过后置过滤筛选关键字对应的项目
        if(StringUtil.isNotEmpty(qo.getKeyword())){
            projectBalances = projectBalances.stream().filter(item -> item.getProjectName().contains(qo.getKeyword()) || item.getProjectCode().contains(qo.getKeyword())).collect(Collectors.toList());
        }
        if (!PositionEnum.isFinance(qo.getPositionCode())){
            List<String> balanceIds = projectBalances.stream().map(ProjectBalVO::getBalanceId).collect(Collectors.toList());
            //获取授权的项目额度信息
            List<ProjectBalVO> authDeptBalance = pcxBudBalanceService.getAuthDeptBalance(Collections.singletonList(qo.getDepartmentCode()));
            if (CollectionUtil.isNotEmpty(authDeptBalance)){
                //将projectBalances 中 balanceId不存在的 authDeptBalance 加载到authDeptBalance中
                authDeptBalance = authDeptBalance.stream().filter(item -> !balanceIds.contains(item.getBalanceId())).collect(Collectors.toList());
                projectBalances.addAll(authDeptBalance);
            }
        }

        //按照项目进行过滤
        if (CollectionUtil.isNotEmpty(qo.getProjectCodes())){
            projectBalances = projectBalances.stream().filter(item -> qo.getProjectCodes().contains(item.getProjectCode())).collect(Collectors.toList());
        }
        //项目开支范围财务需要过滤吗？
        if (StringUtil.isNotEmpty(qo.getItemCode()) && !PositionEnum.isFinance(qo.getPositionCode())){
            PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
            pcxBasItemQO.setAgyCode(qo.getAgyCode());
            pcxBasItemQO.setMofDivCode(qo.getMofDivCode());
            pcxBasItemQO.setFiscal(qo.getFiscal());
            pcxBasItemQO.setItemCode(qo.getItemCode());
            PcxBasItemVO pcxBasItemVO = iPcxBasItemService.selectById(pcxBasItemQO);
            List<String> expenseCodes = pcxBasItemVO.getExpenseCodes();
            if(CollectionUtil.isNotEmpty(expenseCodes)){
                List<PcxProjectExpense> pcxProjectExpenses = pcxProjectExpenseDao.selectList(Wrappers.lambdaQuery(PcxProjectExpense.class)
                        .eq(PcxProjectExpense::getAgyCode, qo.getAgyCode())
                        .eq(PcxProjectExpense::getMofDivCode, qo.getMofDivCode())
                        .eq(PcxProjectExpense::getFiscal, qo.getFiscal())
                        .in(PcxProjectExpense::getExpenseCode, expenseCodes)
                );
                Set<String> budgetSet = pcxProjectExpenses.stream().map(PcxProjectExpense::getBudgetItemCode).collect(Collectors.toSet());
                if(CollectionUtil.isNotEmpty(budgetSet)){
                    projectBalances = projectBalances.stream().filter(item->budgetSet.contains(item.getProjectCode())).collect(Collectors.toList());
                }
            }
        }
        return CheckMsg.success(projectBalances);
    }

    @Override
    public CheckMsg getBalanceData(PcxBalancesQO qo) {
        CheckMsg validationMsg = validateBalParam(qo);
        if (!validationMsg.isSuccess()) {
            return validationMsg;
        }
        IBalanceExternalService balanceExternalService = getBalanceBean(qo.getMofDivCode(), qo.getAgyCode(), qo.getFiscal(), qo.getTenantId());
        String[] balanceTypes = {BalanceTypeEnum.BUD.getCode()};
        PtyBalanceData balancesMap = balanceExternalService.getBalanceData(qo, balanceTypes);
        return CheckMsg.success().setData(balancesMap);
    }

    /***
     * 校验调用bal参数
     * @param qo
     * @return
     */
    private CheckMsg validateBalParam(PcxBalancesQO qo) {
        if (StringUtil.isEmpty(qo.getFiscal())) {
            return CheckMsg.fail().setMsgInfo("年度不能为空");
        }
        if (StringUtil.isEmpty(qo.getMofDivCode())) {
            return CheckMsg.fail().setMsgInfo("区划编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getDepartmentCode())) {
            return CheckMsg.fail().setMsgInfo("部门编码不能为空");
        }
        //加载分摊部门所在的单位
        Map<String,String> param = new HashMap<>();
        param.put("fiscal", qo.getFiscal());
        param.put("mofDivCode", qo.getMofDivCode());
        param.put("madCode", qo.getDepartmentCode());
        List<MadDepartmentDTO> depts = madDepartmentExternalService.selectAgyDepartment(param);
        qo.setAgyCode(depts.get(0).getAgyCode());
        return CheckMsg.success();
    }

    @Override
    public List<BlockProperty> getBlockProperties(BlockPropertyQO blockPropertyQO) {
        // 检查 blockPropertyQO 是否为空，如果为空则返回一个空列表
        if (ObjectUtils.isEmpty(blockPropertyQO)) {
            return new ArrayList<>();
        }
        List<BlockProperty> result = new ArrayList<>();
        if(PositionBlockEnum.FUND_SOURCE.getCode().equals(blockPropertyQO.getClassifyCode())){
            // 经费来源
            result.addAll(getFundSourceBlockProperties(blockPropertyQO));
        }else if(PositionBlockEnum.BUDGET.getCode().equals(blockPropertyQO.getClassifyCode())){
            // 预算指标
            result.addAll(getBudgetBlockProperties(blockPropertyQO));
        }
        return result;
    }

    /*****
     *
     * @param blockPropertyQO
     * @return
     */
    private List<BlockProperty>  getBudgetBlockProperties(BlockPropertyQO blockPropertyQO) {
        if (ObjectUtils.isEmpty(blockPropertyQO)) {
            return new ArrayList<>();
        }
        //此处需要获取实际的岗位代码信息
        String menuUrl = BUDGET_MENU_URL;
        if(StringUtil.isNotEmpty(blockPropertyQO.getBillId()) && StringUtil.isNotEmpty(blockPropertyQO.getUserCode())){
            PositionQO qo = new PositionQO();
            qo.setUserCode(blockPropertyQO.getUserCode());
            qo.setBillId(blockPropertyQO.getBillId());
            qo.setAgyCode(blockPropertyQO.getAgyCode());
            qo.setMofDivCode(blockPropertyQO.getMofDivCode());
            qo.setFiscal(blockPropertyQO.getFiscal());
            CheckMsg<PositionVO> positionCode = processService.getPositionCode(qo);
            if (positionCode.getData()!= null){
                String relPositionCode = positionCode.getData().getRelPositionCode();
                menuUrl = PositionCodeMapMenuUrl.mapping.get(relPositionCode);
            }
        }
        if (StringUtil.isEmpty(menuUrl)){
            menuUrl = BUDGET_MENU_URL;
        }
        PaFieldSettingQO paFieldSettingQO = new PaFieldSettingQO();
        paFieldSettingQO.setMofDivCode(blockPropertyQO.getMofDivCode());
        paFieldSettingQO.setFiscal(blockPropertyQO.getFiscal());
        paFieldSettingQO.setAgyCode(blockPropertyQO.getAgyCode());
        paFieldSettingQO.setTenantId(StringUtil.getStringValue(blockPropertyQO.getTenantId(), PtyContext.getTenantId()));
        paFieldSettingQO.setMenuUrl(menuUrl);
        paFieldSettingQO.setSysId(PcxConstant.SYS_ID);
        paFieldSettingQO.setIsShow(PubConstant.LOGIC_TRUE);
        List<PaFieldSettingVO> paFieldSettingVOList = getSettings(paFieldSettingQO);
        if (CollectionUtil.isEmpty(paFieldSettingVOList)){
            paFieldSettingQO.setMofDivCode(PubConstant.SYS_DEFAULT_CODE);
            paFieldSettingQO.setType(BalanceTypeEnum.BUD.getCode());
            paFieldSettingQO.setFiscal(PubConstant.SYS_DEFAULT_CODE);
            paFieldSettingQO.setAgyCode(PubConstant.SYS_DEFAULT_CODE);
            paFieldSettingQO.setMenuUrl(BUDGET_MENU_URL);
            paFieldSettingVOList = getSettings(paFieldSettingQO);
        }
        List<BlockProperty> result = new ArrayList<>();
        List<BlockProperty> finalResult = result;
        paFieldSettingVOList.forEach(item -> {
            BlockProperty blockProperty = new BlockProperty();
            blockProperty.setFormCode(PositionBlockEnum.BUDGET.getCode());
            blockProperty.setFormName(PositionBlockEnum.BUDGET.getName());
            blockProperty.setFieldTitle(item.getColumnName());
            blockProperty.setFieldName(item.getColumnName());
            blockProperty.setIsEdit(item.getIsLocking() == 1?PubConstant.LOGIC_FALSE:PubConstant.LOGIC_TRUE);
            blockProperty.setIsRequired(
                    item.getRequired() != null ? Integer.parseInt(item.getRequired()) : 0
            );
            blockProperty.setDataTypeCode(StringUtil.isEmpty(item.getDataType())? DataTypeEnum.STRING.getCode() :item.getDataType());
            blockProperty.setEditorCode(item.getEditor());
            blockProperty.setDataClassifyCode(item.getAtomCode());
            blockProperty.setDataSourceCode(item.getAtomDataUrl());
            blockProperty.setFieldValue(item.getCode());
            blockProperty.setFieldLabel(item.getName());
            finalResult.add(blockProperty);
        });
        //查询当前是否启用分摊
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(blockPropertyQO.getMofDivCode());
        paOptionQO.setAgyCode(blockPropertyQO.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(blockPropertyQO.getFiscal()));
        boolean enableExpenseApportion = businessRuleOptionService.isEnableExpenseApportion(paOptionQO);
        if (!enableExpenseApportion){
            //过滤掉 result 中的 分摊相关的字段 NO_SHARE_FIELD
            result = result.stream().filter(item -> !NO_SHARE_FIELD.contains(item.getFieldValue())).collect(Collectors.toList());
        }
        return  result;
    }

    /****
     * 经费来源属性获取
     * @param blockPropertyQO
     * @return
     */
    private List<BlockProperty> getFundSourceBlockProperties(BlockPropertyQO blockPropertyQO) {
        if (ObjectUtils.isEmpty(blockPropertyQO)) {
            return new ArrayList<>();
        }
        // 根据提供的参数获取余额外部服务 bean
        IBalanceExternalService balanceExternalService = getBalanceBean(
                blockPropertyQO.getMofDivCode(),
                blockPropertyQO.getAgyCode(),
                blockPropertyQO.getFiscal(),
                blockPropertyQO.getTenantId()
        );
        // 创建并填充 PaFieldSettingQO 对象
        PaFieldSettingQO paFieldSettingQO = createPaFieldSettingQO(blockPropertyQO);
        List<PaFieldSettingVO> balancePaFieldList = balanceExternalService.getBalanceElementList(paFieldSettingQO, BalanceTypeEnum.IBAL.getCode());
        // 如果列表为空，则返回一个空列表
        if (CollectionUtil.isEmpty(balancePaFieldList)) {
            return new ArrayList<>();
        }
        // 查询初始化固定值集合
        List<PaFieldSettingVO> fixedValueList = getFixedValues(blockPropertyQO);
        // 初始化 BlockProperty 对象列表
        List<BlockProperty> blockProperties = new ArrayList<>();
        Set<String> fixedValues = new HashSet<>();
        if (CollectionUtil.isNotEmpty(fixedValueList)) {
            fixedValues = fixedValueList.stream().map(PaFieldSettingVO::getColumnCode).collect(Collectors.toSet());
            addFixedBlockProperties(blockProperties, fixedValueList);
        }
        for (PaFieldSettingVO paFieldSettingVO : balancePaFieldList) {
            if (paFieldSettingVO == null || fixedValues.contains(paFieldSettingVO.getColumnCode())) {
                continue;
            }
            blockProperties.add(createBlockProperty(paFieldSettingVO, PositionBlockEnum.FUND_SOURCE.getCode(), PositionBlockEnum.FUND_SOURCE.getName(), FieldDisptypeEnum.INPUT.getCode()));
        }
        return blockProperties;
    }

    private PaFieldSettingQO createPaFieldSettingQO(BlockPropertyQO blockPropertyQO) {
        PaFieldSettingQO paFieldSettingQO = new PaFieldSettingQO();
        paFieldSettingQO.setMofDivCode(blockPropertyQO.getMofDivCode());
        paFieldSettingQO.setFiscal(blockPropertyQO.getFiscal());
        paFieldSettingQO.setAgyCode(blockPropertyQO.getAgyCode());
        paFieldSettingQO.setTenantId(Optional.ofNullable(blockPropertyQO.getTenantId()).orElse(PtyContext.getTenantId()));
        return paFieldSettingQO;
    }

    private List<PaFieldSettingVO> getFixedValues(BlockPropertyQO blockPropertyQO) {
        PaFieldSettingQO param = new PaFieldSettingQO();
        param.setMofDivCode(PubConstant.SYS_DEFAULT_CODE);
        param.setType(BalanceTypeEnum.IBAL.getCode());
        param.setFiscal(PubConstant.SYS_DEFAULT_CODE);
        param.setAgyCode(PubConstant.SYS_DEFAULT_CODE);
        param.setTenantId(Optional.ofNullable(blockPropertyQO.getTenantId()).orElse(PtyContext.getTenantId()));
        return paFieldSettingDao.select(param);
    }

    private void addFixedBlockProperties(List<BlockProperty> blockProperties, List<PaFieldSettingVO> fixedValueList) {
        if(CollectionUtil.isEmpty(fixedValueList)){
            return;
        }
        fixedValueList.forEach(paFieldSettingVO -> blockProperties.add(createBlockProperty(paFieldSettingVO, PositionBlockEnum.FUND_SOURCE.getCode(), PositionBlockEnum.FUND_SOURCE.getName(), FieldDisptypeEnum.INPUT.getCode())));
    }
    private BlockProperty createBlockProperty(PaFieldSettingVO paFieldSettingVO, String formCode, String formName, String editorCode) {
        BlockProperty blockProperty = new BlockProperty();
        blockProperty.setFormCode(formCode);
        blockProperty.setFormName(formName);
        blockProperty.setFieldTitle(paFieldSettingVO.getColumnName());
        blockProperty.setFieldName(paFieldSettingVO.getColumnName());
        blockProperty.setIsEdit(PubConstant.STR_LOGIC_FALSE.equals(paFieldSettingVO.getDisabled())?PubConstant.LOGIC_TRUE:PubConstant.LOGIC_FALSE);
        blockProperty.setIsRequired(PubConstant.STR_LOGIC_TRUE.equals(paFieldSettingVO.getRequired())?PubConstant.LOGIC_TRUE:PubConstant.LOGIC_FALSE);
        blockProperty.setDataTypeCode(StringUtil.isEmpty(paFieldSettingVO.getDataType())? DataTypeEnum.STRING.getCode() :paFieldSettingVO.getDataType());
        blockProperty.setEditorCode(StringUtil.isEmpty(paFieldSettingVO.getEditor())?editorCode:paFieldSettingVO.getEditor());
        blockProperty.setDataClassifyCode(paFieldSettingVO.getAtomCode());
        blockProperty.setDataSourceCode(paFieldSettingVO.getAtomDataUrl());
        String columnCode = paFieldSettingVO.getColumnCode();
        // mashaojie-20250115 - 如果 code 或 name 不为空，则使用 code 或 name 作为字段的值和标签
        if(StringUtil.isNotBlank(paFieldSettingVO.getCode())  && StringUtil.isNotBlank(paFieldSettingVO.getName())){
            blockProperty.setFieldValue(paFieldSettingVO.getCode());
            blockProperty.setFieldLabel(paFieldSettingVO.getName());
            return blockProperty;
        }
        blockProperty.setFieldValue(columnCode.endsWith("Code") || columnCode.endsWith("Amt") ? columnCode : columnCode + "Code");
        blockProperty.setFieldLabel(columnCode.endsWith("Name") || columnCode.endsWith("Amt") ? columnCode : columnCode + "Name");
        return blockProperty;
    }
}
