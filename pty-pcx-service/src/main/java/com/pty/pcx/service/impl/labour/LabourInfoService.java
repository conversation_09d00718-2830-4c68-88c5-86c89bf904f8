package com.pty.pcx.service.impl.labour;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.pty.mad.entity.MadEmpType;
import com.pty.mad.entity.PaValset;

import com.pty.pcx.api.labour.ILabourInfoService;
import com.pty.pcx.common.constant.PcxLabourConstants;
import com.pty.pcx.common.enu.PcxInputTypeEnum;
import com.pty.pcx.common.enu.labour.LabourInfoExportEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ExcelUtil;
import com.pty.pcx.common.util.PcxUtil;
import com.pty.pcx.dao.labour.PcxLabourInfoDao;
import com.pty.pcx.entity.bill.labour.PcxLabourInfo;
import com.pty.pcx.qo.PcxExportQo;
import com.pty.pcx.qo.labour.PcxLabourInfoQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.ExcelExportVo;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.pty.mad.api.IMadEmpTypeService;
import org.pty.mad.api.IPaValsetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Indexed
public class LabourInfoService implements ILabourInfoService {

    @Autowired
    private PcxLabourInfoDao labourInfoDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired(required = false)
    @RestClientReference(microServiceNames = "mad")
    private IPaValsetService paValsetService;

    @Autowired
    private IMadEmpTypeService madEmpTypeService;


    private static final String idCard = "1";//身份证


    @Override
    public Map<String, Object> listData(PcxLabourInfoQO labourInfo) {
        Map<String, Object> result = new HashMap<>();
        labourInfo.setFiscal(null);
        PageInfo<PcxLabourInfo> pageInfo = PageHelper.startPage(labourInfo.getPageIndex(), labourInfo.getPageSize())
                .doSelectPageInfo(() -> {
                    labourInfoDao.select(labourInfo);
                });
        PaValset paValset = new PaValset();
        paValset.setValsetCode(PcxLabourConstants.DOCUMENT_TYPE);
        Calendar calendar = Calendar.getInstance();//new一个Calendar类,把Date放进去
        int year = calendar.get(Calendar.YEAR);
        paValset.setFiscal(year);
        Map<String, String> collect = paValsetService.select(paValset).stream().collect(Collectors.toMap(PaValset::getValCode, PaValset::getVal));
        for (PcxLabourInfo list : pageInfo.getList()) {
            list.setIdTypeName(collect.get(list.getIdType()));
        }
        result.put("total", pageInfo.getTotal());
        result.put("result", pageInfo.getList());
        return result;
    }


    @Override
    public List<PcxLabourInfo> list(PcxLabourInfoQO param) {
        if (ObjectUtils.isEmpty(param)){
            return new ArrayList<>();
        }
        return labourInfoDao.select(param);
    }

    @Override
    public CheckMsg insertBatch(List<PcxLabourInfo> labourInfos) {
        for (PcxLabourInfo PcxLabourInfo : labourInfos) {
            PcxLabourInfo.setId(IDGenerator.id());
        }
        PcxUtil.getPartitionBatchInsertList(labourInfos).forEach(o->labourInfoDao.insertBatch(o));
        return CheckMsg.success();
    }


    @Override
    public CheckMsg update(PcxLabourInfoQO param) {
        try {
            param.setAccountName(StringUtils.isEmpty(param.getAccountName()) ? param.getUserName() : param.getAccountName());
            param.setModifiedTime(DateUtil.nowDate());
            setExenseType(param);
            labourInfoDao.updateById(param);
            return CheckMsg.success();
        }catch (Exception e) {
            return CheckMsg.fail("修改失败：" + e.getMessage());
        }
    }


    @Override
    public CheckMsg insert(PcxLabourInfoQO param) {
        param.setId(StringUtils.isEmpty(param.getId()) ? IDGenerator.id() : param.getId());
        param.setAccountName(StringUtils.isEmpty(param.getAccountName()) ? param.getUserName() : param.getAccountName());
        param.setCreatedTime(DateUtil.nowDate());
        param.setInputType(PcxInputTypeEnum.INPUT.getCode());
        setExenseType(param);
        // pex_labour_info 目前不结转，校验重复逻辑：根据机构，区划，身份证件号进行校验
        // 和导入，劳务费填单时插入逻辑保持一致
        Long count = labourInfoDao.selectCount(Wrappers.lambdaQuery(PcxLabourInfo.class)
                .eq(PcxLabourInfo::getAgyCode, param.getAgyCode())
                .eq(PcxLabourInfo::getMofDivCode, param.getMofDivCode())
                .eq(PcxLabourInfo::getAccountNo, param.getAccountNo())
                .eq(PcxLabourInfo::getIdNo, param.getIdNo()));
        if (Objects.nonNull(count) && count > 0) {
            return CheckMsg.fail("当前身份证号对应信息已存在: " + param.getIdNo() + " ,请勿重复新增");
        }
        labourInfoDao.insert(param);
        return CheckMsg.success();
    }


    private static void setExenseType(PcxLabourInfoQO t) {
        if (idCard.equals(t.getIdType())) {
            t.setExpenseTypeCode(PcxLabourConstants.wbExpenseTypeCode);
            t.setExpenseTypeName("聘请人员劳务");
        } else {
            t.setExpenseTypeName("外籍人员劳务");
            t.setExpenseTypeCode(PcxLabourConstants.wbExpenseTypeCode);
        }
    }

    @Override
    public CheckMsg delByIds(List<String> ids) {
        try {
            labourInfoDao.deleteBatchIds(ids);
        } catch (Exception e) {
            return CheckMsg.fail("删除失败：" + e.getMessage());
        }
        return CheckMsg.success();
    }

    @Override
    public CheckMsg updateStatusByIds(List<String> ids, Integer isEnabled) {
        try {
            if (CollectionUtils.isEmpty(ids)){
                return CheckMsg.fail("请求参数为空");
            }
            labourInfoDao.updateStatusByIds(ids, isEnabled);
        } catch (Exception e) {
            return CheckMsg.fail("修改失败：" + e.getMessage());
        }
        return CheckMsg.success();
    }


    /*
     * 劳务人员信息模板下载
     */
    @Override
    public PcxExportQo excelExport(List excelExportVoList, String userCode, String agyCode, String fiscal, String mofDivCode) {
        PcxExportQo qo = new PcxExportQo();
        try {
            XSSFWorkbook wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet("导出模板");
            // 第一行
            Row row = sheet.createRow(0);
            CellStyle style = ExcelUtil.getHeadStyle(wb);
            for (int a = 0; a < excelExportVoList.size(); a++) {
                String desc = StringUtil.nullToEmpty(((LinkedHashMap) excelExportVoList.get(a)).get("desc"));
                ExcelUtil.initCell(row.createCell(a), style, desc);
                Cell nameCell = row.createCell(a);
                nameCell.setCellValue(desc);
                String value = StringUtil.nullToEmpty(((LinkedHashMap) excelExportVoList.get(a)).get("value"));
                if (value.equals("idTypeName")) {
                    Sheet typeListSheet = wb.createSheet("typeListSheet");
                    PaValset PaValset = new PaValset();
                    PaValset.setValsetCode(PcxLabourConstants.DOCUMENT_TYPE);
                    PaValset.setIsEnable(PubConstant.LOGIC_TRUE);
                    PaValset.setFiscal(Integer.parseInt(fiscal));
                    List<PaValset> typeList = paValsetService.selectByValsetCode(PaValset);
                    List<String> nameList = new ArrayList<>();
                    for (PaValset paValset : typeList) {
                        nameList.add(paValset.getValCode() + "-" + paValset.getVal());
                    }
                    String[] typeListArray = nameList.toArray(new String[nameList.size()]);
                    genearteOtherSheet(wb, typeListArray, typeListSheet);
                    // 设置下拉列表直绑定对哪一页起作用
                    sheet.addValidationData(SetDataValidation(wb, "typeListSheet!$A$1:$A$" + typeListArray.length, 1, a, 100, a));

                    // 隐藏作为下拉列表值的Sheet
                    wb.setSheetHidden(wb.getSheetIndex("typeListSheet"), 1);
                } else if (value.equals("typeCode")) {
                    MadEmpType madEmpType = new MadEmpType();
                    madEmpType.setFiscal(Integer.parseInt(fiscal));
                    madEmpType.setAgyCode(agyCode);
                    madEmpType.setMofDivCode(mofDivCode);
                    madEmpType.setIsEnabled(PubConstant.LOGIC_TRUE);
                    List<MadEmpType> select = madEmpTypeService.select(madEmpType);
                    Sheet empTypeSheet = wb.createSheet("empTypeSheet");
                    for (int i = 0; i < select.size(); i++) {
                        Row empTypeRow = empTypeSheet.createRow(i);
                        empTypeRow.createCell(0).setCellValue(select.get(i).getTypeCode() + "-" + select.get(i).getTypeName());
                    }
                    String range = "empTypeSheet!$A$1:$A$" + select.size();
                    sheet.addValidationData(SetDataValidation(wb, range, 1, a, 100, a));
                    wb.setSheetHidden(wb.getSheetIndex("empTypeSheet"), 1);
                }
            }
            String saveFileName = "";
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSS");
            saveFileName += format.format(Calendar.getInstance().getTime()) + "劳务人员信息.xlsx";
            saveFileName = URLEncoder.encode(saveFileName, StandardCharsets.UTF_8.toString());
            qo.setFileName(saveFileName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            byte[] bytes = bos.toByteArray();
            qo.setBytes(bytes);
        } catch (Exception e) {
            log.error("查询导出excel异常:{}", e.getMessage());
        }
        return qo;
    }

    public static void genearteOtherSheet(Workbook wb, String[] typeArrays, Sheet sheet) {
        // 创建下拉列表值存储工作表
        //  Sheet sheet = wb.createSheet("typelist");
        // 循环往该sheet中设置添加下拉列表的值
        for (int i = 0; i < typeArrays.length; i++) {
            Row row = sheet.createRow(i);
            Cell cell = row.createCell((int) 0);
            cell.setCellValue(typeArrays[i]);
        }
    }

    public static DataValidation SetDataValidation(Workbook wb, String strFormula, int firstRow, int firstCol, int endRow, int endCol) {
        // 表示A列1-59行作为下拉列表来源数据
        // String formula = "typelist!$A$1:$A$59" ;
        // 原顺序为 起始行 起始列 终止行 终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) wb.getSheet("typelist"));
        DataValidationConstraint formulaListConstraint = dvHelper.createFormulaListConstraint(strFormula);
        return dvHelper.createValidation(formulaListConstraint, regions);
    }

    //劳务人员信息模板导入
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg uploadExcel(MultipartFile file, PcxLabourInfoQO params) {
        try {
            if (file == null || file.getSize() <= 0) {
                return CheckMsg.fail("文件为空或大小为0");
            }
            JSONArray jsonArray = JSON.parseArray(params.getExcelExportVoList());
            List<ExcelExportVo> columns = jsonArray.toJavaList(ExcelExportVo.class);
            // 将文件转换为临时文件
            File excel = File.createTempFile("temp", "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            try (InputStream ins = file.getInputStream()) {
                inputStreamToFile(ins, excel);
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 检查文件格式
            if (!excel.isFile() || !excel.exists()) {
                return CheckMsg.fail("文件不存在");
            }
            String extension = FilenameUtils.getExtension(excel.getName());
            if (!"xls".equals(extension) && !"xlsx".equals(extension)) {
                return CheckMsg.fail("不支持的文件格式");
            }

            // 读取Excel文件
            Workbook workbook = WorkbookFactory.create(excel);
            Sheet sheet = workbook.getSheetAt(0);
            int firstRowIndex = sheet.getFirstRowNum() + 1;
            int lastRowIndex = sheet.getLastRowNum();

            // 初始化变量
            List<PcxLabourInfo> labourInfos = new ArrayList<>();
            Set<String> importedIdNos = new HashSet<>();
            List<String> invalidFormatNames = new ArrayList<>();
            List<String> duplicateNamesInExcel = new ArrayList<>();

            // 遍历行
            for (int rIndex = firstRowIndex; rIndex <= lastRowIndex; rIndex++) {
                Row row = sheet.getRow(rIndex);
                if (row == null) {
                    continue;
                }
                PcxLabourInfo labourInfo = createLabourInfoFromRow(row, columns, params);

                // 校验身份证号和电话号格式
                if (idCard.equals(labourInfo.getIdType()) &&
                        (labourInfo.getIdNo() == null || !labourInfo.getIdNo().matches("\\d{17}[0-9X]"))) {
                    invalidFormatNames.add(labourInfo.getUserName() + "，身份证号格式不满足");
                    continue;
                }
                if (idCard.equals(labourInfo.getIdType()) && (labourInfo.getPhoneNo() != null && !labourInfo.getPhoneNo().matches("\\d{11}"))) {
                    invalidFormatNames.add(labourInfo.getUserName() + "，电话号格式不满足");
                    continue;
                }

                // 检查是否已经存在相同的身份证号
                if (importedIdNos.contains(labourInfo.getIdNo())) {
                    duplicateNamesInExcel.add(labourInfo.getIdNo());
                    continue;
                }
                importedIdNos.add(labourInfo.getIdNo());
                labourInfos.add(labourInfo);
            }

            // 导入前校验
            if (!duplicateNamesInExcel.isEmpty()) {
                return CheckMsg.fail("当前要导入的Excel表存在重复身份证号：" + String.join("，", duplicateNamesInExcel));
            }
            if (!invalidFormatNames.isEmpty()) {
                return CheckMsg.fail("以下记录格式不正确：\n" + String.join("\n", invalidFormatNames));
            }

            // 导入后校验
            List<String> duplicateIdNoInSystem = checkDuplicatesInSystem(labourInfos);
            if (!duplicateIdNoInSystem.isEmpty()) {
                return CheckMsg.fail("以下身份证号已存在：" + String.join("，", duplicateIdNoInSystem));
            }

            // 插入数据
            if (CollectionUtils.isNotEmpty(labourInfos)) {
                PcxUtil.getPartitionBatchInsertList(labourInfos).forEach(o->labourInfoDao.insertBatch(o));
                return CheckMsg.success("成功导入" + labourInfos.size() + "条");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CheckMsg.fail("找不到指定的文件");
    }

    //通过导入的excel表创建劳务人员信息
    private PcxLabourInfo createLabourInfoFromRow(Row row, List<ExcelExportVo> columns, PcxLabourInfoQO params) {
        PcxLabourInfo labourInfo = PcxLabourInfo.builder()
                .id(IDGenerator.id())
                .fiscal(params.getFiscal())
                .agyCode(params.getAgyCode())
                .createdTime(DateUtil.nowDate())
                .isEnabled(PubConstant.LOGIC_TRUE)
                .inputType(PcxInputTypeEnum.IMPORT.getCode())
                .mofDivCode(params.getMofDivCode()).build();
        for (int a = 0; a < columns.size(); a++) {
            Cell cell = row.getCell(a);
            if (cell != null) {
                cell.setCellType(CellType.STRING);
                String stringCellValue = cell.getStringCellValue();
                String titleValue = columns.get(a).getValue();
                switch (titleValue) {
                    case "idTypeName":
                        String[] splitValue = stringCellValue.split("-");
                        if (splitValue.length > 0) {
                            labourInfo.setIdType(splitValue[0]);
                        }
                        break;
                    case "typeCode":
                        if (StringUtil.isNotEmpty(stringCellValue)) {
                            String[] cellValueArr = stringCellValue.split("-");
                            if (cellValueArr.length > 0) {
                                labourInfo.setTypeCode(cellValueArr[0]);
                            }
                            if (cellValueArr.length > 1) {
                                labourInfo.setTypeName(cellValueArr[1]);
                            }
                        }
                        break;
                    default:
                        try {
                            Field field = PcxLabourInfo.class.getDeclaredField(titleValue);
                            field.setAccessible(true);
                            field.set(labourInfo, stringCellValue);
                        } catch (IllegalArgumentException | IllegalAccessException | NoSuchFieldException e) {
                            log.error("设置属性 {} 失败: {}", titleValue, e.getMessage());
                        }
                        break;
                }
            }
        }

        // 设置身份证类型
        if (idCard.equals(labourInfo.getIdType())) {
            labourInfo.setExpenseTypeCode(PcxLabourConstants.wbExpenseTypeCode);
            labourInfo.setExpenseTypeName("聘请人员劳务");
        } else {
            labourInfo.setExpenseTypeCode(PcxLabourConstants.wjExpenseTypeCode);
            labourInfo.setExpenseTypeName("外籍人员劳务");
        }
        return labourInfo;
    }

    //检查系统是否已存在重复劳务人员数据
    private List<String> checkDuplicatesInSystem(List<PcxLabourInfo> labourInfos) {
        if (CollectionUtil.isEmpty(labourInfos)) {
            return Collections.emptyList();
        }

        List<String> idNos = new ArrayList<>();
        Map<String, String> idToAccount = new HashMap<>();
        for (PcxLabourInfo labourInfo : labourInfos) {
            if (StringUtils.isEmpty(labourInfo.getIdNo())) {
                continue;
            }
            idNos.add(labourInfo.getIdNo());
            idToAccount.put(labourInfo.getIdNo(), labourInfo.getAccountNo());
        }
        if (CollectionUtils.isEmpty(idNos)){
            return Collections.emptyList();
        }
        List<PcxLabourInfo> labourInfoList = new ArrayList<>();
        List<List<String>> idNoLists = Lists.partition(idNos, 500);
        String agyCode = labourInfos.get(0).getAgyCode();
        String mofDivCode = labourInfos.get(0).getMofDivCode();
        for (List<String> idNoList : idNoLists) {
            PcxLabourInfoQO param = new PcxLabourInfoQO();
            param.setAgyCode(agyCode);
            param.setMofDivCode(mofDivCode);
            param.setIdNos(idNoList);
            List<PcxLabourInfo> pcxLabourInfos = labourInfoDao.selectByIdNos(param);
            labourInfoList.addAll(pcxLabourInfos);
        }
        Set<String> duplicateIdNos = new HashSet<>();
        for (PcxLabourInfo labourInfo : labourInfoList) {
            String idNo = labourInfo.getIdNo();
            if (StringUtils.isEmpty(idNo)) {
                continue;
            }
            String accountNo = idToAccount.get(idNo);
            if (Objects.equals(accountNo, labourInfo.getAccountNo())) {
                duplicateIdNos.add(idNo);
            }
        }
        return new ArrayList<>(duplicateIdNos);
    }

    @Override
    @Transactional
    public void updateById(PcxLabourInfo PcxLabourInfo) {
        labourInfoDao.updateById(PcxLabourInfo);
    }

    @Override
    public PcxExportQo exportLabourInfoExcel(PcxLabourInfoQO param) {
        PcxExportQo qo = new PcxExportQo();
        List<PcxLabourInfo> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getIds())) {//当前选中行数
            dataList = labourInfoDao.selectBatchIds(param.getIds());
        } else {
            dataList = labourInfoDao.select(param);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            throw new RuntimeException("导出劳务人员信息为空");
        }
        PaValset paValset = new PaValset();
        paValset.setValsetCode(PcxLabourConstants.DOCUMENT_TYPE);
        Calendar calendar = Calendar.getInstance();//new一个Calendar类,把Date放进去
        int year = calendar.get(Calendar.YEAR);
        paValset.setFiscal(year);
        Map<String, String> collect = paValsetService.select(paValset).stream().collect(Collectors.toMap(PaValset::getValCode, PaValset::getVal));
        for (PcxLabourInfo data : dataList) {
            data.setIdTypeName(collect.get(data.getIdType()));
        }
        try {
            List<Map<String, Object>> list = LabourInfoExportEnum.getList();
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet sheet = workbook.createSheet("劳务人员信息");
            HSSFRow rowHead = sheet.createRow(0);
            HSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.RIGHT);
            for (int i = 0; i < list.size(); i++) {
                rowHead.createCell(i).setCellValue(StringUtil.nullToEmpty(list.get(i).get("title")));
                sheet.setColumnWidth(i, 252 * 15 + 323);
            }
            for (PcxLabourInfo datum : dataList) {
                HSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);
                for (int j = 0; j < list.size(); j++) {
                    if (Objects.isNull(list.get(j).get("code")) || StringUtil.isEmpty(list.get(j).get("code"))) {  //code为空，不需要导出数据
                        continue;
                    }
                    Field field = datum.getClass().getDeclaredField(list.get(j).get("code") + "");
                    field.setAccessible(true);
                    row.createCell((j)).setCellValue(
                            StringUtil.isNotEmpty(field.get(datum) + "") ? field.get(datum) + "" : "");
                }
            }
            String fileName = "劳务人员信息.xls";
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            qo.setFileName(fileName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] bytes = bos.toByteArray();
            qo.setBytes(bytes);
        } catch (Exception e) {
            log.error("查询导出excel异常:{}", e.getMessage());
            e.printStackTrace();
        }
        return qo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg updateByIdCard(List<PcxLabourInfoQO> labourInfos) {
        if (CollectionUtil.isEmpty(labourInfos)) {
            return CheckMsg.fail("需要修改的人员信息列表为空");
        }
        try {
            List<PcxLabourInfo> validLabourInfos = labourInfos.stream()
                    .filter(labourInfo -> !StringUtil.isEmpty(labourInfo.getIdNo()))
                    .collect(Collectors.toList());
            if (validLabourInfos.isEmpty()) {
                return CheckMsg.fail("所有需要修改的人员信息身份证号均为空");
            }
            batchServiceUtil.batchProcess(labourInfos, PcxLabourInfoDao.class, PcxLabourInfoDao::updateByIdNo);
        } catch (Exception e) {
            return CheckMsg.fail("修改失败：" + e.getMessage());
        }
        return CheckMsg.success();
    }



    public static void inputStreamToFile(InputStream ins, File file) {
        try (OutputStream os = Files.newOutputStream(file.toPath())) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = ins.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
