package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.api.bill.PcxBillAmtApportionDepartmentService;
import com.pty.pcx.dao.bill.PcxBillAmtApportionDepartmentDao;
import com.pty.pcx.entity.bill.PcxBillAmtApportionDepartment;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报销单费用分摊部门(PcxBillAmtApportionDepartment)表服务实现类
 * <AUTHOR>
 * @since 2025-04-01 07:38:57
 */
@Slf4j
@Indexed
@Service
public class PcxBillAmtApportionDepartmentServiceImpl implements PcxBillAmtApportionDepartmentService {

	@Autowired
	private PcxBillAmtApportionDepartmentDao pcxBillAmtApportionDepartmentDao;
	@Autowired
	private BatchServiceUtil batchServiceUtil;

	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxBillAmtApportionDepartment selectById(String id) {
		return pcxBillAmtApportionDepartmentDao.selectById(id);
	}

	@Override
	public void batchUpdate(List<PcxBillAmtApportionDepartment> amtApportionDepartments) {
		if (amtApportionDepartments != null && !amtApportionDepartments.isEmpty()){
			batchServiceUtil.batchProcess(amtApportionDepartments, PcxBillAmtApportionDepartmentDao.class, PcxBillAmtApportionDepartmentDao::updateById);
		}
	}

	@Override
	public List<PcxBillAmtApportionDepartment> selectByApportionId(String apportionId) {
		return pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.eq(PcxBillAmtApportionDepartment::getApportionId, apportionId));
	}

	@Override
	public List<PcxBillAmtApportionDepartment> selectBatchByApportionId(List<String> apportionIds) {
		return pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.in(PcxBillAmtApportionDepartment::getApportionId, apportionIds));
	}

	@Override
	public List<PcxBillAmtApportionDepartment> selectByBillId(String billId) {
		return pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.eq(PcxBillAmtApportionDepartment::getBillId, billId));
	}

	@Override
	public List<PcxBillAmtApportionDepartment> selectByBillIdAndDept(String billId,String expenseCode,String deptCode) {
		return pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.eq(PcxBillAmtApportionDepartment::getBillId, billId)
				.eq(StringUtil.isNotEmpty(expenseCode),PcxBillAmtApportionDepartment::getExpenseTypeCode, expenseCode)
				.eq(PcxBillAmtApportionDepartment::getDepartmentCode, deptCode));
	}

}


