package com.pty.pcx.service.impl.workflow2;

import com.pty.pcx.api.workflow2.IPositionPostService;
import com.pty.pcx.api.workflow2.IPositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Indexed
@Service
public class PositionServiceManager {

    private final Map<String, IPositionService<String>> positionServiceRel = new HashMap<>();

    private final Map<String, IPositionPostService> positionPostServiceRel = new HashMap<>();

    public PositionServiceManager(@Autowired List<IPositionService<String>> positionServices, @Autowired List<IPositionPostService> positionPostServices) {
        positionServices.forEach(service -> {
            service.getPositionIds().forEach(id -> positionServiceRel.put(id, service));
        });
        positionPostServices.forEach(service -> {
            service.getPositionIds().forEach(id -> positionPostServiceRel.put(id, service));
        });
    }

    public IPositionService<String> getPositionService(String positionId) {
        return positionServiceRel.get(positionId);
    }

    public IPositionPostService getPositionPostService(String positionId) {
        return positionPostServiceRel.get(positionId);
    }
}
