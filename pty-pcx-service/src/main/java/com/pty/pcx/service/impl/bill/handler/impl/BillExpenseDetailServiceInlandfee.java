package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillExpDetailInlandfeeService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.enu.expense.InlandfeeExpenseTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillExpDetailInlandfeeDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.qo.bill.PcxExpDetailEcsRelQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("billExpenseDetailServiceInlandfee")
@Slf4j
@Indexed
public class BillExpenseDetailServiceInlandfee extends BillExpenseCommonService implements BillExpenseDetailService<PcxBillExpDetailInlandfee, PcxBillExpInlandfee> {
    @Autowired
    private PcxBillExpDetailInlandfeeDao pcxBillExpDetailInlandfeeDao;

    @Autowired
    private PcxBillExpDetailInlandfeeService pcxBillExpDetailInlandfeeService;

    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Autowired
    private PcxExpDetailEcsRelDao expDetailEcsRelDao;

    /**
     * 招待费明细校验
     * @param expDetail
     * @param billFuncCode
     * @return
     */
    @Override
    public CheckMsg<Void> validate(List<PcxBillExpDetailInlandfee> expDetail, String billFuncCode) {
        FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum = FormSettingEnums.BillFuncCodeEnum.getByCode(billFuncCode);
        if (Objects.isNull(billFuncCodeEnum)){
            return CheckMsg.success();
        }
        if (CollectionUtils.isEmpty(expDetail)){
            return CheckMsg.success();
        }
        PcxBillExpDetailBase base = expDetail.get(0);
        PcxBillExpDetailInlandfee index = new PcxBillExpDetailInlandfee();
        BeanUtil.copyProperties(base, index);

        List<PcxBillExpDetailInlandfee> detailTrainings = new ArrayList<>();
        for (Object item : expDetail) {
            if (item instanceof PcxBillExpDetailInlandfee) {
                detailTrainings.add((PcxBillExpDetailInlandfee) item);
            } else {
                PcxBillExpDetailInlandfee inlandfee = new PcxBillExpDetailInlandfee();
                BeanUtil.copyProperties(item, inlandfee);
                detailTrainings.add(inlandfee);
            }
        }


        for (PcxBillExpDetailInlandfee pcxBillExpDetailInlandfee : detailTrainings) {
            //查询招待费启用的必填的专属字段
            PcxBasFormSettingQueryQO qo = new PcxBasFormSettingQueryQO();
            qo.setFormClassify(FormSettingEnums.FormClassifyEnum.EXPENSE.getCode());
            qo.setBillFuncCode(billFuncCodeEnum.getBit());
            qo.setFormCode(pcxBillExpDetailInlandfee.getExpDetailCode());
            qo.setFormType(FormSettingEnums.FormTypeEnum.EXPENSE_DETAIL.getCode());
            qo.setAgyCode(index.getAgyCode());
            qo.setFiscal(index.getFiscal());
            qo.setMofDivCode(index.getMofDivCode());
            Response<List<PcxBasFormSettingQO>> response = pcxBasFormSettingService.selectAllFormSetting(qo);
            if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())){
                //非空校验
                List<PcxBasFormSettingQO> formSettingQOS = response.getData().stream().filter(item -> item.getIsEnabled() == 1 && item.getIsNull() == 1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(formSettingQOS)){
                    Map<String, Object> stringObjectMap = BeanUtil.beanToMap(pcxBillExpDetailInlandfee);
                    List<String> missingFields = new ArrayList<>();

                    for (PcxBasFormSettingQO formSettingQO : formSettingQOS) {
                        Object o = stringObjectMap.get(formSettingQO.getFieldValue());
                        if (Objects.isNull(o) || StringUtil.isEmpty(o.toString())) {
                            String fieldName = formSettingQO.getFieldName();
                            missingFields.add(fieldName);
                            log.warn("明细:[{}]不能为空", fieldName);
                        }
                    }

                    if (!missingFields.isEmpty()) {
                        String errorMessage = "明细:" + String.join("、", missingFields) + "不能为空";
                        return CheckMsg.fail(errorMessage);
                    }
                }
            }
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PcxBillExpDetailInlandfee> saveOrUpdate(PcxBillExpInlandfee expense, List<PcxBillExpDetailInlandfee> detail, PcxBill pcxBill) {
        if (CollectionUtil.isEmpty(detail)) {
            log.error("没有明细数据, 当前费用类型编码: {}", expense.getExpenseCode());
            return detail;
        }

        List<PcxBillExpDetailInlandfee> convertedDetails = new ArrayList<>();
        for (Object obj : detail) {
            if (obj instanceof PcxBillExpDetailInlandfee) {
                PcxBillExpDetailInlandfee inlandfee = (PcxBillExpDetailInlandfee) obj;
                convertedDetails.add(inlandfee);
            } else if (obj instanceof PcxBillExpDetailBase) {
                PcxBillExpDetailBase base = (PcxBillExpDetailBase) obj;
                // 构造新的对象并复制属性
                PcxBillExpDetailInlandfee inlandfee = new PcxBillExpDetailInlandfee();
                BeanUtil.copyProperties(base, inlandfee);
                convertedDetails.add(inlandfee);
            }
        }

        // 分类：需要插入的记录 和 需要更新的记录
        List<PcxBillExpDetailInlandfee> toInsert = Lists.newArrayList();
        List<PcxBillExpDetailInlandfee> toUpdate = Lists.newArrayList();

        List<String> existingIds = new ArrayList<>();
        if (!convertedDetails.isEmpty()) {
            List<String> idList = convertedDetails.stream()
                    .filter(Objects::nonNull)
                    .map(PcxBillExpDetailInlandfee::getId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (!idList.isEmpty()) {
                List<String> collect = pcxBillExpDetailInlandfeeDao.selectList(
                                new LambdaQueryWrapper<PcxBillExpDetailInlandfee>()
                                        .select(PcxBillExpDetailInlandfee::getId)
                                        .in(PcxBillExpDetailInlandfee::getId, idList)
                        ).stream()
                        .map(PcxBillExpDetailInlandfee::getId)
                        .collect(Collectors.toList());
                existingIds.addAll(collect);
            }
        }
        // 遍历 convertedDetails 并分类处理
        convertedDetails.stream()
                .filter(Objects::nonNull) // 确保 detailAbroad 不为 null
                .forEach(detailInlandfee -> {
                    // 设置公共字段
                    prepareCommonFields(detailInlandfee, pcxBill, expense);

                    // 判断是插入还是更新
                    if (StringUtil.isEmpty(detailInlandfee.getId())|| !existingIds.contains(detailInlandfee.getId())) {
                        toInsert.add(detailInlandfee);
                    } else {
                        toUpdate.add(detailInlandfee);
                    }
                });

        // 批量插入
        if (CollectionUtils.isNotEmpty(toInsert)) {
            pcxBillExpDetailInlandfeeService.insertBatch(toInsert);
        }
        // 批量更新
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            pcxBillExpDetailInlandfeeService.updateBatch(toUpdate);
        }

        // 更新票信息（保持原逻辑一致）
        updateTicketInfo(convertedDetails, pcxBill);

        return convertedDetails;
    }

    /**
     * 更新票信息
     *
     * @param detail  明细记录列表
     * @param pcxBill 单据对象
     */
    private void updateTicketInfo(List<PcxBillExpDetailInlandfee> detail, PcxBill pcxBill) {
        // 查询关联的票信息
        PcxExpDetailEcsRelQO param = new PcxExpDetailEcsRelQO();
        param.setBillId(pcxBill.getId());
        param.setFiscal(pcxBill.getFiscal());
        param.setAgyCode(pcxBill.getAgyCode());
        param.setMofDivCode(pcxBill.getMofDivCode());
        param.setTenantId(StringUtil.isNotBlank(pcxBill.getTenantId()) ? pcxBill.getTenantId() : PtyContext.getTenantId());
        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelService.selectByQo(param);
        if (CollectionUtils.isNotEmpty(pcxExpDetailEcsRels)) {
            // 根据明细ID分组
            Map<String, List<PcxExpDetailEcsRel>> groupedByDetailId = pcxExpDetailEcsRels.stream()
                    .filter(item -> StringUtil.isNotBlank(item.getDetailId()))
                    .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getDetailId));

            // 需要更新的票信息
            if (ObjectUtils.isNotEmpty(groupedByDetailId)){
                List<PcxExpDetailEcsRel> updates = Lists.newArrayList();

                detail.forEach(detailInlandfee -> {
                    List<PcxExpDetailEcsRel> relatedTickets = groupedByDetailId.get(detailInlandfee.getId());
                    if (CollectionUtils.isNotEmpty(relatedTickets)) {
                        PcxExpDetailEcsRel ticket = relatedTickets.get(0);
                        ticket.setEcsCheckStatus(detailInlandfee.getEcsCheckStatus());
                        ticket.setCheckReason(detailInlandfee.getCheckReason());
                        updates.add(ticket);
                    }
                });

                // 批量更新票信息
                if (CollectionUtils.isNotEmpty(updates)) {
                    batchServiceUtil.batchProcess(updates, PcxExpDetailEcsRelDao.class, PcxExpDetailEcsRelDao::updateById);
                }
            }
        }
    }

    /**
     * 设置公共字段
     * @param detailInlandfee
     * @param pcxBill
     * @param expense
     */
    private void prepareCommonFields(PcxBillExpDetailInlandfee detailInlandfee, PcxBill pcxBill, PcxBillExpInlandfee expense) {
        detailInlandfee.setBillId(pcxBill.getId());
        detailInlandfee.setId(Objects.isNull(detailInlandfee.getId()) ? IDGenerator.id() : detailInlandfee.getId());
        detailInlandfee.setExpenseId(expense.getId());
        detailInlandfee.setAgyCode(pcxBill.getAgyCode());
        detailInlandfee.setFiscal(pcxBill.getFiscal());
        detailInlandfee.setMofDivCode(pcxBill.getMofDivCode());
        detailInlandfee.setCreator(pcxBill.getCreator());
        detailInlandfee.setCreatorName(pcxBill.getCreatorName());
        detailInlandfee.setCreatedTime(pcxBill.getCreatedTime());
        detailInlandfee.setModifier(pcxBill.getModifier());
        detailInlandfee.setModifierName(pcxBill.getModifierName());
        detailInlandfee.setModifiedTime(pcxBill.getModifiedTime());

        // 设置 checkAmt
        if (detailInlandfee.getCheckAmt() == null || detailInlandfee.getCheckAmt().compareTo(BigDecimal.ZERO) == 0) {
            detailInlandfee.setCheckAmt(detailInlandfee.getInputAmt());
        }
    }

    @Override
    public List<PcxBillExpDetailInlandfee> viewByExpenseCode(String expenseCode, PcxBill pcxBill) {
        //参数校验
        checkParam(expenseCode,pcxBill);

        //查询明细
        List<PcxBillExpDetailInlandfee> detailInlandfees = pcxBillExpDetailInlandfeeDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailInlandfee.class)
                .likeRight(PcxBillExpDetailInlandfee::getExpDetailCode, expenseCode)
                .eq(PcxBillExpDetailInlandfee::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailInlandfee::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailInlandfee::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailInlandfee::getMofDivCode, pcxBill.getMofDivCode())
                .orderByAsc(PcxBillExpDetailInlandfee::getExpDetailCode));

        // 设置 expenseTypeCode 和 expenseTypeName
        Map<String, InlandfeeExpenseTypeEnum> enumMap = Arrays.stream(InlandfeeExpenseTypeEnum.values())
                .collect(Collectors.toMap(InlandfeeExpenseTypeEnum::getCode, Function.identity()));

        // ========== 票据关联金额赋值 ==========
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(pcxBill.getId());
        Map<String, PcxExpDetailEcsRel> ecsRelMap = ecsRelList.stream()
                .collect(Collectors.toMap(PcxExpDetailEcsRel::getDetailId, Function.identity(),(existing, replacement) -> existing));

        boolean paperBill = isPaperBill(pcxBill);
        for (PcxBillExpDetailInlandfee detail : detailInlandfees) {

            String expDetailCode = detail.getExpDetailCode();
            InlandfeeExpenseTypeEnum typeEnum = enumMap.get(expDetailCode);
            if (typeEnum != null) {
                detail.setExpenseTypeCode(typeEnum.getCode());
                detail.setExpenseTypeName(typeEnum.getName());
            }

            //设置 groupNo,不能为null
            detail.setGroupNo(new ArrayList<>());

            PcxExpDetailEcsRel pcxExpDetailEcsRel = ecsRelMap.get(detail.getId());
            if (Objects.nonNull(pcxExpDetailEcsRel)) {
                detail.setTicketAmt(pcxExpDetailEcsRel.getInputAmt());
                detail.setTicketAmt(pcxExpDetailEcsRel.getInputAmt());
                detail.setEcsBillId(pcxExpDetailEcsRel.getEcsBillId());
                detail.setEcsBillNo(pcxExpDetailEcsRel.getEcsBillNo());
                detail.setEcsBillKind(pcxExpDetailEcsRel.getEcsBillKind());
                detail.setEcsBillType(pcxExpDetailEcsRel.getEcsBillType());
                detail.setEcsCheckStatus(disposeCheckStatus(pcxExpDetailEcsRel.getEcsCheckStatus(), pcxExpDetailEcsRel.getEcsBillKind(), paperBill));
                detail.setCheckReason(pcxExpDetailEcsRel.getCheckReason());
                detail.setEcsBillDate(pcxExpDetailEcsRel.getEcsBillDate());
                detail.setEcsItems(pcxExpDetailEcsRel.getItemName());
            } else {
                detail.setTicketAmt(BigDecimal.ZERO);
            }
        }

        return detailInlandfees;
    }

    private List<PcxExpDetailEcsRel> getEcsRelList(String billId) {
        return expDetailEcsRelDao.selectByBillId(billId);
    }

    private PcxBillExpDetailInlandfee convertToDetail(InlandfeeExpenseTypeEnum type) {
        PcxBillExpDetailInlandfee detail = new PcxBillExpDetailInlandfee();
        detail.setExpenseTypeCode(type.getCode());
        detail.setExpenseTypeName(type.getName());
        detail.setParentCode(type.getParentCode());
        detail.setLastCode(type.getLastCode());
        detail.setExpDetailCode(type.getCode());
        detail.setExpDetailName(type.getName());
        detail.setInputAmt(BigDecimal.ZERO);
        detail.setCheckAmt(BigDecimal.ZERO);
        detail.setEcsAmt(BigDecimal.ZERO);
        return detail;
    }

    @Override
    public List<PcxBillExpDetailInlandfee> listByExpenseCode(String expenseCode, PcxBill pcxBill) {
        return pcxBillExpDetailInlandfeeDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailInlandfee.class)
                .like(StringUtil.isNotEmpty(expenseCode), PcxBillExpDetailInlandfee::getExpDetailCode, expenseCode)
                .eq(PcxBillExpDetailInlandfee::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailInlandfee::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailInlandfee::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailInlandfee::getMofDivCode, pcxBill.getMofDivCode()));
    }

    @Override
    public List<PcxBillExpDetailInlandfee> view(String expenseDetailCode, PcxBill pcxBill) {

        //参数校验
        checkParam(expenseDetailCode,pcxBill);

        //查询明细
        return pcxBillExpDetailInlandfeeDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailInlandfee.class)
                .likeRight(PcxBillExpDetailInlandfee::getExpDetailCode, expenseDetailCode)
                .eq(PcxBillExpDetailInlandfee::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailInlandfee::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailInlandfee::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailInlandfee::getMofDivCode, pcxBill.getMofDivCode())
        );
    }

    /**
     * 参数校验
     * @param pcxBill
     */
    private void checkParam(String expenseDetailCode, PcxBill pcxBill) {
        if (Objects.isNull(pcxBill)){
            throw new CommonException("单据信息为空");
        }
        if (StringUtil.isEmpty(expenseDetailCode)){
            throw new CommonException("费用类型编码为空");
        }
        if (StringUtil.isEmpty(pcxBill.getId())){
            throw new CommonException("单据ID不能为空");
        }
        if (StringUtil.isEmpty(pcxBill.getAgyCode())){
            throw new CommonException("单位不能为空");
        }
        if (StringUtil.isEmpty(pcxBill.getFiscal())){
            throw new CommonException("年度不能为空");
        }
        if (StringUtil.isEmpty(pcxBill.getMofDivCode())){
            throw new CommonException("区划不能为空");
        }
    }

    @Override
    public void deleteByExpenseCode(String expenseCode, PcxBill pcxBill) {
        //查询明细
        List<PcxBillExpDetailInlandfee> detailTravels = pcxBillExpDetailInlandfeeDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailInlandfee.class)
                .like(PcxBillExpDetailInlandfee::getExpDetailCode, expenseCode)
                .eq(PcxBillExpDetailInlandfee::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailInlandfee::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailInlandfee::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailInlandfee::getMofDivCode, pcxBill.getMofDivCode()));
        if(CollectionUtil.isNotEmpty(detailTravels)){
            pcxBillExpDetailInlandfeeDao.deleteBatchIds(detailTravels.stream().map(PcxBillExpDetailInlandfee::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public void delete(String expenseDetailCode, PcxBill pcxBill) {

    }

    @Override
    public void deleteByIds(List<String> ids) {

    }
}
