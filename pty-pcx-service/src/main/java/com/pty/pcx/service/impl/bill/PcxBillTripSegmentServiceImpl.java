package com.pty.pcx.service.impl.bill;

import com.pty.pcx.api.bill.PcxBillTripSegmentService;
import com.pty.pcx.dao.bill.PcxBillTripSegmentDao;
import com.pty.pcx.entity.bill.PcxBillTripSegment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 行程分段(PcxBillTripSegment)表服务实现类
 * <AUTHOR>
 * @since 2025-04-01 07:38:57
 */
@Slf4j
@Indexed
@Service
public class PcxBillTripSegmentServiceImpl implements PcxBillTripSegmentService {

	@Autowired
	private PcxBillTripSegmentDao pcxBillTripSegmentDao;

	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxBillTripSegment selectById(String id) {
		return pcxBillTripSegmentDao.selectById(id);
	}

}


