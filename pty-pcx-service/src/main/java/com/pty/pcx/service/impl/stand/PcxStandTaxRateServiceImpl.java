package com.pty.pcx.service.impl.stand;

import com.pty.pcx.api.stand.PcxStandTaxRateService;
import com.pty.pcx.dao.stand.PcxStandTaxRateDao;
import com.pty.pcx.entity.stand.PcxStandTaxRate;
import com.pty.pcx.entity.stand.qo.PcxStandTaxRateQO;
import com.pty.pcx.util.BatchServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 劳务标准税率表(PcxStandTaxRate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-04 11:45:51
 */
@Slf4j
@Indexed
@Service
public class PcxStandTaxRateServiceImpl implements PcxStandTaxRateService {

    @Autowired
    private PcxStandTaxRateDao pcxStandTaxRateDao;
    @Autowired
    private BatchServiceUtil pcxBatchServiceUtil;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PcxStandTaxRate selectById(String id) {
        return pcxStandTaxRateDao.selectById(id);
    }

    /**
     * 查询多条数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 对象列表
     */
    @Override
    public List<PcxStandTaxRate> selectList(PcxStandTaxRate pcxStandTaxRate) {
        return pcxStandTaxRateDao.selectList(pcxStandTaxRate);
    }

    /**
     * 新增数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 实例对象
     */
    @Override
    public int insertSelective(PcxStandTaxRate pcxStandTaxRate) {
        return pcxStandTaxRateDao.insertSelective(pcxStandTaxRate);
    }

    /**
     * 修改数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 实例对象
     */
    @Override
    public int update(PcxStandTaxRate pcxStandTaxRate) {
        return pcxStandTaxRateDao.update(pcxStandTaxRate);
    }

    /**
     * 通过主键id删除数据
     *
     * @param id 主键
     */
    @Override
    public int deleteById(String id) {
        log.debug("删除税率标准 id {}",id);
        return pcxStandTaxRateDao.deleteById(id);
    }

    /**
     * 根据税率代码查询对应的税率列表
     * @param pcxStandTaxRateQO
     * @return
     */
    @Override
    public List<PcxStandTaxRate> selectByRateTypeCode(PcxStandTaxRateQO pcxStandTaxRateQO) {
        PcxStandTaxRate pcxStandTaxRate = new PcxStandTaxRate();
        pcxStandTaxRate.setRateTypeCode(pcxStandTaxRateQO.getRateTypeCode());
        pcxStandTaxRate.setAgyCode(pcxStandTaxRateQO.getAgyCode());
        pcxStandTaxRate.setFiscal(pcxStandTaxRateQO.getFiscal());
        pcxStandTaxRate.setMofDivCode(pcxStandTaxRateQO.getMofDivCode());
        return pcxStandTaxRateDao.selectList(pcxStandTaxRate);
    }

    @Override
    public void batchUpdate(List<PcxStandTaxRate> pcxStandTaxRates) {
        pcxBatchServiceUtil.batchProcess(pcxStandTaxRates,PcxStandTaxRateDao.class,PcxStandTaxRateDao::update);
    }

}


