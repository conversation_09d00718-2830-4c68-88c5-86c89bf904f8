package com.pty.pcx.service.impl.bill;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.bill.PcxBillAmtApportionService;
import com.pty.pcx.api.bill.PcxBillExpDetailInlandfeeService;
import com.pty.pcx.api.bill.PcxBillExpInlandfeeService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.ecs.UpdateEcsBillDTO;
import com.pty.pcx.dto.ecs.settlement.EcsBillSettleDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.ecs.impl.EcsBillExternalServiceImpl;
import com.pty.pcx.ecs.impl.EcsDtoTransHelper;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.UpdateEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateNoEcsCommonQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.util.PcxBillExpBasicUtils;
import com.pty.pcx.service.impl.ecs.EcsExpTransService;
import com.pty.pcx.service.impl.ecs.dto.CollectExpListDto;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.PcxBasExpTypeVO;
import com.pty.pcx.vo.ecs.*;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
@Indexed
public class PcxBillExpInlandFeeServiceImpl implements PcxBillExpInlandfeeService {

    @Resource
    private PcxBillExpInlandfeeDao pcxBillExpInlandfeeDao;

    @Autowired
    private PcxBillExpBasicUtils pcxBillExpBasicUtils;

    @Resource
    private PcxBasExpTypeDao pcxBasExpTypeDao;

    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;

    @Resource
    private BillMainService billMainService;

    @Resource
    private PcxExpDetailEcsRelDao expDetailEcsRelDao;

    @Resource
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;

    @Resource
    private PcxBillExpDetailInlandfeeDao pcxBillExpDetailInlandfeeDao;

    @Resource
    private PcxBasExpTypeDao basExpTypeDao;
    @Autowired
    private IEcsBillExternalService ecsBillExternalService;

    @Autowired
    private PcxBillExpDetailInlandfeeService pcxBillExpDetailInlandfeeService;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;
    @Autowired
    private PcxEcsSettlDao pcxEcsSettlDao;

    @Resource
    private PcxBillAmtApportionService pcxBillAmtApportionService;

    @Resource
    private EcsExpTransService ecsExpTransService;

    @Override
    public List<PcxBillExpInlandfee> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpInlandfeeDao.selectList(new LambdaQueryWrapper<PcxBillExpInlandfee>()
                .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpInlandfee::getBillId, billIds)
                .eq(PcxBillExpInlandfee::getAgyCode, agyCode)
                .eq(PcxBillExpInlandfee::getFiscal, fiscal)
                .eq(PcxBillExpInlandfee::getMofDivCode, mofDivCode));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNoEcsCommon(PcxBill bill,
                                  List<PcxBillExpInlandfee> baseExpList,
                                  List<PcxExpDetailEcsRel> allEcsRel,
                                  List<PcxBillExpAttachRel> attachRelList,
                                  List<PcxBillExpDetailBase> detailCommonList,
                                  Map<String, PcxBasExpTypeVO> parentMap,
                                  List<PcxBillExpDetailInlandfee> delDetailList) {
        if(CollectionUtil.isEmpty(allEcsRel)){
            throw new CommonException("没有要更新的发票");
        }
        // 保存主单据信息
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);

        PcxExpDetailEcsRel rel = allEcsRel.get(0);
        //删除老的票关联关系
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, bill.getId())
                .eq(PcxExpDetailEcsRel::getEcsBillId, rel.getEcsBillId()));

        //删除旧的支付信息
        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, bill.getId())
                .eq(PcxEcsSettl::getEcsBillId, rel.getEcsBillId()));
        //删除旧的附件关系
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, rel.getEcsBillId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));

        // 保存会议费费用主单据信息
        saveOrUpdateInlandfeeDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        //删除需要删的明细
        if(CollectionUtil.isNotEmpty(delDetailList)){
            pcxBillExpDetailInlandfeeService.deleteBatchIds(delDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList()));
        }
        // 保存会议费费用明细信息
        saveOrUpdateInlandfeeDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        ecsExpTransService.batchInsertAttachRel(attachRelList, pcxBill.getId());

        // 保存ECS关系表信息
        pcxBillExpBasicUtils.batchInsertNewEcsRel(allEcsRel, pcxBill.getId());

    }

    @Transactional(rollbackFor = Exception.class)
    public String updateInlandfeeExpenseBill(PcxBill bill,
                                             List<PcxBillExpInlandfee> baseExpList,
                                             List<PcxExpDetailEcsRel> allEcsRel,
                                             List<PcxBillExpAttachRel> attachRelList,
                                             List<PcxBillExpDetailBase> detailCommonList,
                                             Map<String, PcxBasExpTypeVO> parentMap,
                                             List<PcxBillExpStandResult> standList,
                                             List<PcxEcsSettl> settlList,
                                             UpdateEcsBillDTO updateEcsBillDTO,
                                             List<PcxBillAmtApportion> apportions,
                                             List<PcxBillAmtApportionDepartment> apportionDepartmentList){
        // 保存主单据信息
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);
        PcxExpDetailEcsRel rel = allEcsRel.get(0);
        //删除老的票关联关系
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, bill.getId())
                .eq(PcxExpDetailEcsRel::getEcsBillId, rel.getEcsBillId()));

        //删除旧的支付信息
        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, bill.getId())
                .eq(PcxEcsSettl::getEcsBillId, rel.getEcsBillId()));
        //删除旧的附件关系
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, rel.getEcsBillId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));
        // 保存会议费费用主单据信息
        saveOrUpdateExpInlandfee(baseExpList, pcxBill);

        // 保存会议费费用明细信息
        saveOrUpdateInlandfeeDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        // 构建报销单和ecs发票关系
        pcxBillExpBasicUtils.buildEcsRel(pcxBill, allEcsRel, attachRelList);

        // 保存ECS关系表信息
        pcxBillExpBasicUtils.batchInsertNewEcsRel(allEcsRel, pcxBill.getId());

        // 保存ecs支付信息
        pcxBillExpBasicUtils.batchInsertEcsSettlement(settlList, pcxBill.getId());
        // 保存支出标准
        pcxBillExpBasicUtils.batchInsertStandResult(standList, pcxBill.getId());

        pcxBillExpBasicUtils.batchInsertBillAmtApportion(pcxBill, apportions, apportionDepartmentList);
        // 同步ecs
        if (null != updateEcsBillDTO) {
            Response response = ecsBillExternalService.expInfoSave(updateEcsBillDTO);
            if (!response.getCode().equals(Response.SUCCESS_CODE)) {
                log.error("同步电子凭证信息失败 {}", response.getMsg());
                throw new RuntimeException("同步电子凭证信息失败");
            }
        }
        return pcxBill.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    public String createInlandfeeExpenseBill(PcxBill bill,
                                             List<PcxBillExpInlandfee> baseExpList,
                                             List<PcxExpDetailEcsRel> allEcsRel,
                                             List<PcxBillExpAttachRel> attachRelList,
                                             List<PcxBillExpDetailBase> detailCommonList,
                                             Map<String, PcxBasExpTypeVO> parentMap,
                                             List<PcxBillExpStandResult> standList,
                                             List<PcxEcsSettl> settlList,
                                             UpdateEcsBillDTO updateEcsBillDTO,
                                             List<PcxBillAmtApportion> apportions,
                                             List<PcxBillAmtApportionDepartment> apportionDepartmentList){
        // 保存主单据信息
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);

        // 保存会议费费用主单据信息
        saveOrUpdateExpInlandfee(baseExpList, pcxBill);

        // 保存会议费费用明细信息
        saveOrUpdateInlandfeeDetail(baseExpList, detailCommonList, pcxBill, parentMap);

        // 构建报销单和ecs发票关系
        pcxBillExpBasicUtils.buildEcsRel(pcxBill, allEcsRel, attachRelList);

        // 保存ECS关系表信息
        pcxBillExpBasicUtils.batchInsertNewEcsRel(allEcsRel, pcxBill.getId());

        // 保存ecs支付信息
        pcxBillExpBasicUtils.batchInsertEcsSettlement(settlList, pcxBill.getId());
        // 保存支出标准
        pcxBillExpBasicUtils.batchInsertStandResult(standList, pcxBill.getId());
        // 保存分摊信息
        pcxBillExpBasicUtils.batchInsertBillAmtApportion(pcxBill, apportions, apportionDepartmentList);
        // 同步ecs
        if (null != updateEcsBillDTO) {
            Response response = ecsBillExternalService.expInfoSave(updateEcsBillDTO);
            if (!response.getCode().equals(Response.SUCCESS_CODE)) {
                log.error("同步电子凭证信息失败 {}", response.getMsg());
                throw new RuntimeException("同步电子凭证信息失败");
            }
        }
        return pcxBill.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveBillAndExpense(StartExpenseQO expenseQO, List<InvoiceDtoWrapper> wrappers) {
        //获取事项关联的费用类型
        List<PcxBasItemExp> baseTypeList = pcxBillExpBasicUtils.getItemExpenseType(expenseQO);

        //所有票关联费用类型
        List<PcxExpDetailEcsRel> allEcsRel = new ArrayList<>();
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        List<PcxEcsSettl> settlList = new ArrayList<>();

        // 收集票的附件和费用数据
        List<PcxBillExpDetailBase> allExpDetailBase = new ArrayList<>();
        List<PcxBillExpStandResult> standList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(wrappers)) {
            for (InvoiceDtoWrapper wrapper : wrappers) {
                List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = collectEcsRel(wrapper, attachRelList, expenseQO, settlList);
                allEcsRel.addAll(pcxExpDetailEcsRels);
                //整理票生成的所有费用，票生成的所有明细
                CollectExpListDto expDto = collectExpBase(wrapper);
                allExpDetailBase.addAll(expDto.getExpDetailList());
                //费用的支出标准列表
                if (CollectionUtils.isNotEmpty(wrapper.getStandList())) {
                    standList.addAll(wrapper.getStandList());
                }
                if (CollectionUtils.isNotEmpty(wrapper.getSettlList())) {
                    settlList.addAll(wrapper.getSettlList());
                }
            }
        }

        //创建报销单对象
        PcxBill bill = buildBill(expenseQO, baseTypeList);

        //设置明细部门信息
        disposeDetailDepartmentCode(bill, allExpDetailBase);

        //根据费用类型生成对于的费用实体类对象
        List<PcxBillExpInlandfee> baseExpList = buildBaseExpByExpType(baseTypeList, expenseQO);

        //所有费用明细的明细代码
        List<String> allDetailCodes = allExpDetailBase.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(expenseQO, allDetailCodes);
        // 7. 票生成的明细，查出费用明细对应的费用关系，给费用汇总加上金额，并把费用id打到费用明细上面
        Map<String, PcxBillExpBase> baseExpMap = baseExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));
        summaryExpBaseAmtAndMarkDetail(baseExpMap, allExpDetailBase, parentMap);


        //汇总费用金额，报销单金额
        collectBillAmt(bill, baseExpList);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(bill, baseExpList);

        return createInlandfeeExpenseBill(bill,
                baseExpList,
                allEcsRel,
                attachRelList,
                allExpDetailBase,
                parentMap,
                standList,
                settlList,
                null,
                apportionPair.getLeft(),
                apportionPair.getRight());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEcsBills(AddInvoicesQO invoiceQO, List<InvoiceDtoWrapper> wrappers, PcxBill bill) {
        //标准结果
        List<PcxBillExpStandResult> standList = new ArrayList<>();
        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();
        //票的支付信息
        List<PcxEcsSettl> settlList = new ArrayList<>();
        // 获取费用明细信息（非空校验）
        List<PcxBillExpInlandfee> baseExpList = selectList(
                Arrays.asList(bill.getId()), bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode()
        );
        //查询出报销单的费用数据
        List<PcxBillExpDetailInlandfee> baseExpDetailList = pcxBillExpDetailInlandfeeService.selectList(Arrays.asList(bill.getId()), bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode());
        invoiceQO.setItemCode(bill.getItemCode());
        StartExpenseQO qo = new StartExpenseQO();
        BeanUtils.copyProperties(invoiceQO, qo);

        // 收集票的附件和费用数据
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        List<PcxBillExpDetailBase> allExpDetailBase = new ArrayList<>();

        // 3. 收集所有票生成的费用，明细，并建立票与明细的关联关系
        if (CollectionUtils.isNotEmpty(wrappers)) {
            for (InvoiceDtoWrapper wrapper : wrappers) {
                List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = collectEcsRel(wrapper, attachRelList, invoiceQO, settlList);
                addEcsRel.addAll(pcxExpDetailEcsRels);
                //整理票生成的所有费用，票生成的所有明细
                CollectExpListDto expDto = collectExpBase(wrapper);
                allExpDetailBase.addAll(expDto.getExpDetailList());
                //费用的支出标准列表
                if (CollectionUtils.isNotEmpty(wrapper.getStandList())) {
                    standList.addAll(wrapper.getStandList());
                }
                if (CollectionUtils.isNotEmpty(wrapper.getSettlList())) {
                    settlList.addAll(wrapper.getSettlList());
                }
            }
        }

        List<PcxBillExpAttachRel> allAttachRel = getAttachRelList(bill.getId(), a -> true);
        allAttachRel.addAll(attachRelList);

        //添加之前的费用明细数据
        allExpDetailBase.addAll(baseExpDetailList);
        List<String> allDetailCodes = allExpDetailBase.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(invoiceQO, allDetailCodes);

        Map<String, PcxBillExpBase> baseExpMap = baseExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        // 7.汇总明细金额到费用上
        summaryExpBaseAmtAndMarkDetail(baseExpMap, allExpDetailBase, parentMap);

        // 8.汇总金额更新报销单金额
        this.collectBillAmt(bill, baseExpList);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(bill, baseExpList);

        // 9.保存相关表结构信息，附件allAttachRel
        createInlandfeeExpenseBill(
                bill,
                baseExpList,
                addEcsRel,
                attachRelList,
                allExpDetailBase,
                parentMap,
                standList,
                settlList,
                null,
                apportionPair.getLeft(),
                apportionPair.getRight());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEcsBill(UpdateEcsCommonQO updateQO, PcxBill bill) {
        //转换出费用明细列表
        Map<String, List<PcxBillExpDetailBase>> itemDetailMap = analysisDetailListItemMap(updateQO.getItemExpenseList(), bill,
                updateQO.getExpenseTypeCode(), BillExpDetailSourceEnum.ECS.getCode());

        List<PcxBillExpDetailBase> newDetailList = itemDetailMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());

        //转换出付款信息列表
        List<PcxEcsSettl> ecsSettlList = analysisEcsSettl(updateQO);
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRelList(updateQO.getEcsBillClassRelList(), bill, updateQO.getEcsBillId());

        //转换出同步ecs数据
        UpdateEcsBillDTO updateEcsBillDTO = analysisUpdateEcsBillDTO(ecsSettlList, updateQO);
        //标准
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();

        //查询出报销单的费用数据
        List<PcxBillExpInlandfee> billExpList = queryBillExpBaseList(bill);
        List<PcxBillExpDetailBase> oldDetailList = queryBillExpDetailList(bill);

        // 获取事项关联的费用类型（非空校验）
        StartExpenseQO qo = new StartExpenseQO();
        BeanUtils.copyProperties(updateQO, qo);


        //旧费用明细
        List<PcxBillExpDetailBase> delOldDetailList = new ArrayList<>();
        //查询出报销单的票关联关系
        List<PcxExpDetailEcsRel> ecsRelList = expDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, updateQO.getBillId()));
        //找出当前操作的票
        List<PcxExpDetailEcsRel> optEcsRelList = ecsRelList.stream().filter(item->item.getEcsBillId().equals(updateQO.getEcsBillId())
                && item.getEcsBillType().equals(updateQO.getEcsBillType())).collect(Collectors.toList());

        ecsRelList.removeAll(optEcsRelList);
        if (CollectionUtils.isEmpty(optEcsRelList)){
            throw new RuntimeException("未找到匹配的票");
        }
        //修改票的信息
        PcxExpDetailEcsRel rel = optEcsRelList.get(0);

        //根据编辑票明细页面修改的项目的票金额，更新ecsRel里面的ecsAmt
        changeEcsAmt(optEcsRelList, updateQO.getItemExpenseList());

        List<PcxExpDetailEcsRel> newRelList = analysisNewEcsRelEcsItem(optEcsRelList, itemDetailMap, updateQO.getExpenseTypeCode());
        ecsRelList.addAll(newRelList);


        List<String> oldNeedDelDetailIds  = delOldDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList = oldDetailList.stream().filter(item->!oldNeedDelDetailIds.contains(item.getId())).collect(Collectors.toList());
        needSaveOrUpdateDetailList.addAll(newDetailList);

        //TODO 暂时不涉及票费用标准
//        List<PcxBillExpStandResult> delOldStandResultList = getDelDetailStandResult(delOldDetailList);

        List<PcxBillExpAttachRel> allAttachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, updateQO.getBillId()));
        allAttachRelList = allAttachRelList.stream()
                .filter(item-> !item.getRelId().equals(rel.getEcsBillId()))
                .collect(Collectors.toList());
        allAttachRelList.addAll(attachRelList);

        List<String> allDetailCodes = needSaveOrUpdateDetailList.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(updateQO, allDetailCodes);

        Map<String, PcxBillExpBase> baseExpMap = billExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        // 7.汇总明细金额到费用上
        summaryExpBaseAmtAndMarkDetail(baseExpMap, needSaveOrUpdateDetailList, parentMap);

        // 8.汇总金额更新报销单金额
        this.collectBillAmt(bill, billExpList);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(bill, billExpList);

        // 9.保存相关表结构信息
        this.updateInlandfeeExpenseBill(
                bill,
                billExpList,
                newRelList,
                attachRelList,
                needSaveOrUpdateDetailList,
                parentMap,
                standResultList,
                ecsSettlList,
                updateEcsBillDTO,
                apportionPair.getLeft(),
                apportionPair.getRight()
        );

    }

    @Override
    public void updateNoEcs(UpdateNoEcsCommonQO commonQO, PcxBill bill) {

        List<PcxBillExpInlandfee> expBaseList = queryBillExpBaseList(bill);
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(bill.getId());
        List<PcxExpDetailEcsRel> delEcsRel = new ArrayList<>();
        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();
        List<PcxBillExpDetailBase> addDetail = new ArrayList<>();
        List<PcxBillExpDetailBase> allDetail = new ArrayList<>();
        List<PcxBillExpDetailInlandfee> delDetailList = new ArrayList<>();
        if (StringUtil.isNotEmpty(commonQO.getEcsRelId())) {
            PcxExpDetailEcsRel ecsRel = ecsRelList.stream()
                    .filter(item -> item.getId().equals(commonQO.getEcsRelId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(ecsRel)) {
                throw new RuntimeException("未查询到历史数据");
            }
            BeanUtils.copyProperties(commonQO, ecsRel);
            ecsRel.setEcsAmt(commonQO.getInputAmt());
            ecsRel.setInputAmt(commonQO.getInputAmt());
            ecsRel.setCheckAmt(commonQO.getInputAmt());
            if (StringUtil.isNotEmpty(ecsRel.getManualKey())) {
                delEcsRel = ecsRelList.stream()
                        .filter(item -> item.getManualKey().equals(ecsRel.getManualKey()))
                        .collect(Collectors.toList());
            } else {
                delEcsRel.add(ecsRel);
            }
            List<PcxBillExpDetailInlandfee> detailCommons = pcxBillExpDetailInlandfeeService.selectBatchIds(delEcsRel.stream().map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList()));
            delDetailList.addAll(detailCommons);
            Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailInlandfee>> pair = analysisNoEcsRelAndDetail(delEcsRel, commonQO, bill);
            addEcsRel.addAll(pair.getLeft());
            addDetail.addAll(pair.getRight());
        } else {
            ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
            invoiceQO.setFiscal(bill.getFiscal());
            invoiceQO.setMofDivCode(bill.getMofDivCode());
            invoiceQO.setAgyCode(bill.getAgyCode());
            PcxExpDetailEcsRel ecsRel = new PcxExpDetailEcsRel();
            BeanUtils.copyProperties(commonQO, ecsRel);
            ecsRel.setId(IDGenerator.id());
            ecsRel.setEcsAmt(commonQO.getInputAmt());
            ecsRel.setInputAmt(commonQO.getInputAmt());
            ecsRel.setCheckAmt(commonQO.getInputAmt());
            ecsRel.setEcsContent("");
            ecsRel.setBillId(bill.getId());
            ecsRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            ecsRel.setFiscal(bill.getFiscal());
            ecsRel.setAgyCode(bill.getAgyCode());
            ecsRel.setMofDivCode(bill.getMofDivCode());
            ecsRel.setManualKey(UUID.randomUUID().toString());
            if (CollectionUtils.isNotEmpty(commonQO.getDetailList())) {
                int index = 0;
                for (EcsCommonDetailVO detailVO : commonQO.getDetailList()) {
                    PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(ecsRel, copyRel);
                    copyRel.setId(IDGenerator.id());
                    PcxBillExpDetailInlandfee detail = initRelDetail(copyRel, invoiceQO);
                    BeanUtils.copyProperties(detailVO, detail);
                    detail.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                    detail.setExpSource(BillExpDetailSourceEnum.MANUAL.getCode());
                    if (index > 0){
                        copyRel.setEcsAmt(BigDecimal.ZERO);
                        copyRel.setInputAmt(BigDecimal.ZERO);
                        copyRel.setCheckAmt(BigDecimal.ZERO);
                        detail.setInputAmt(BigDecimal.ZERO);
                        detail.setCheckAmt(BigDecimal.ZERO);
                    }
                    addEcsRel.add(copyRel);
                    addDetail.add(detail);
                    index++;
                }
            } else {
                PcxBillExpDetailInlandfee detail = initRelDetail(ecsRel, invoiceQO);
                detail.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                detail.setExpSource(BillExpDetailSourceEnum.MANUAL.getCode());
                addDetail.add(detail);
                addEcsRel.add(ecsRel);
            }
        }
        ecsRelList.removeAll(delEcsRel);
        ecsRelList.addAll(addEcsRel);

        collectBillAmt(bill, expBaseList, ecsRelList);
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRel(commonQO.getAttachList(), addEcsRel.get(0).getManualKey(), bill);
        List<PcxBillExpAttachRel> allAttachRelList = getAttachRelList(bill.getId(), getDelExtraAttachRelPredicate(addEcsRel.get(0).getManualKey()));
        allAttachRelList.addAll(attachRelList);

        List<PcxBillExpDetailInlandfee> oldbaseExpDetailList = pcxBillExpDetailInlandfeeDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailInlandfee.class)
                .eq(PcxBillExpDetailInlandfee::getBillId, bill.getId())
                .eq(PcxBillExpDetailInlandfee::getAgyCode, bill.getAgyCode())
                .eq(PcxBillExpDetailInlandfee::getFiscal, bill.getFiscal())
                .eq(PcxBillExpDetailInlandfee::getMofDivCode, bill.getMofDivCode()));
        allDetail.addAll(oldbaseExpDetailList);
        allDetail.addAll(addDetail);
        List<String> allDetailCodes = allDetail.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());

        ExpInvoiceQO expInvoiceQO = new ExpInvoiceQO();
        expInvoiceQO.setAgyCode(bill.getAgyCode());
        expInvoiceQO.setFiscal(bill.getFiscal());
        expInvoiceQO.setMofDivCode(bill.getMofDivCode());
        Map<String, PcxBasExpTypeVO> parentMap = getExpTypeParentMap(expInvoiceQO, allDetailCodes);

        Map<String, PcxBillExpBase> baseExpMap = expBaseList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        summaryExpBaseAmtAndMarkDetail(baseExpMap, allDetail, parentMap);
        //汇总票据金额
        this.collectBillAmt(bill, expBaseList);

        this.updateNoEcsCommon(
                bill,
                expBaseList,
                addEcsRel,
                attachRelList,
                addDetail,
                parentMap,
                delDetailList
        );
    }


    private Predicate<PcxBillExpAttachRel> getDelExtraAttachRelPredicate(String relId){
        return (a)-> Objects.equals(a.getRelType(), 1) || !Objects.equals(a.getRelId(), relId);
    }

    /**
     * 生成附件关联信息
     * @param attachList
     * @param relKey
     * @param bill
     * @return
     */
    private List<PcxBillExpAttachRel> analysisAttachRel(List<DetailAttachRelQO> attachList, String relKey, PcxBill bill) {
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachList)) {
            for (DetailAttachRelQO attachRelQO : attachList) {
                PcxBillExpAttachRel rel = new PcxBillExpAttachRel();
                rel.setId(IDGenerator.id());
                rel.setRelType(PcxExpAttachRelType.EXP_DETAIL.getCode());
                rel.setBillId(bill.getId());
                rel.setRelId(relKey);
                rel.setAttachId(attachRelQO.getFileId());
                rel.setFileName(attachRelQO.getFileName());
                rel.setFiscal(bill.getFiscal());
                rel.setAgyCode(bill.getAgyCode());
                rel.setMofDivCode(bill.getMofDivCode());
                rel.setTenantId(bill.getTenantId());
                attachRelList.add(rel);
            }
        }
        return attachRelList;
    }

    private void collectBillAmt(PcxBill bill, List<PcxBillExpInlandfee> baseExpList, List<PcxExpDetailEcsRel> allEcsRel) {
        Map<String, BigDecimal> inputAmtMap = new HashMap<>();
        Map<String, BigDecimal> checkAmtMap = new HashMap<>();
        for (PcxExpDetailEcsRel rel : allEcsRel) {
            String expTypeCode = StringUtil.isEmpty(rel.getExpenseTypeCode()) ? PcxConstant.UNIVERSAL_EXPENSE_CODE : rel.getExpenseTypeCode();
            BigDecimal inputAmt = inputAmtMap.getOrDefault(expTypeCode, BigDecimal.ZERO);
            inputAmtMap.put(expTypeCode, inputAmt.add(rel.getInputAmt()));
            BigDecimal checkAmt = checkAmtMap.getOrDefault(expTypeCode, BigDecimal.ZERO);
            checkAmtMap.put(expTypeCode, checkAmt.add(rel.getCheckAmt()));
        }
        BigDecimal totalInputAmt = BigDecimal.ZERO;
        BigDecimal totalCheckAmt = BigDecimal.ZERO;
        for (PcxBillExpInlandfee expBase : baseExpList) {
            expBase.setInputAmt(inputAmtMap.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO));
            expBase.setCheckAmt(checkAmtMap.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO));
            totalInputAmt = totalInputAmt.add(expBase.getInputAmt());
            totalCheckAmt = totalCheckAmt.add(expBase.getCheckAmt());
        }
        bill.setInputAmt(totalInputAmt);
        bill.setCheckAmt(totalCheckAmt);
    }

    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailInlandfee>> analysisNoEcsRelAndDetail(List<PcxExpDetailEcsRel> optEcsRelList,
                                                                                                    UpdateNoEcsCommonQO noEcsCommonQO,
                                                                                                    PcxBill bill) {
        List<PcxExpDetailEcsRel> addEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailInlandfee> addDetailList = new ArrayList<>();
        PcxExpDetailEcsRel rel = optEcsRelList.get(0);
        PcxExpDetailEcsRel tempRel = new PcxExpDetailEcsRel();
        BeanUtils.copyProperties(rel, tempRel);
        tempRel.setId(IDGenerator.id());
        tempRel.setInputAmt(noEcsCommonQO.getInputAmt());
        tempRel.setCheckAmt(noEcsCommonQO.getInputAmt());
        tempRel.setRemark(noEcsCommonQO.getRemark());
        tempRel.setExpenseTypeCode(noEcsCommonQO.getExpenseTypeCode());
        tempRel.setExpenseTypeName(noEcsCommonQO.getExpenseTypeName());
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        invoiceQO.setFiscal(bill.getFiscal());
        invoiceQO.setMofDivCode(bill.getMofDivCode());
        invoiceQO.setAgyCode(bill.getAgyCode());
        if (CollectionUtils.isNotEmpty(noEcsCommonQO.getDetailList())) {
            int index = 0;
            for (EcsCommonDetailVO detailVO : noEcsCommonQO.getDetailList()) {
                PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                BeanUtils.copyProperties(tempRel, copyRel);
                copyRel.setId(IDGenerator.id());
                PcxBillExpDetailInlandfee common = initRelDetail(copyRel, invoiceQO);
                common.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                common.setExpSource(BillExpDetailSourceEnum.MANUAL.getCode());
                BeanUtils.copyProperties(detailVO, common);
                if (index > 0) {
                    copyRel.setEcsAmt(BigDecimal.ZERO);
                    copyRel.setInputAmt(BigDecimal.ZERO);
                    copyRel.setCheckAmt(BigDecimal.ZERO);
                    common.setInputAmt(BigDecimal.ZERO);
                    common.setCheckAmt(BigDecimal.ZERO);
                }
                index++;
                addDetailList.add(common);
                addEcsRelList.add(copyRel);
            }
        } else {
            PcxBillExpDetailInlandfee common = initRelDetail(tempRel, invoiceQO);
            common.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
            common.setExpSource(BillExpDetailSourceEnum.MANUAL.getCode());
            addDetailList.add(common);
            addEcsRelList.add(tempRel);
        }
        return Pair.of(addEcsRelList, addDetailList);
    }

    private Map<String, List<PcxBillExpDetailBase>> analysisDetailListItemMap(List<EcsItemExpense> itemExpenseList, PcxBill view,
                                                                              String expDetailCode,
                                                                              String source) {
        Map<String, List<PcxBillExpDetailBase>> result = new HashMap<>();
        BigDecimal detailInputAmt = BigDecimal.ZERO;
        BigDecimal allEcsAmt = BigDecimal.ZERO;
        for (EcsItemExpense itemExpense : itemExpenseList) {
            JSONArray expenseList = itemExpense.getExpenseList();
            BigDecimal ecsAmt = itemExpense.getEcsAmt();
            allEcsAmt = allEcsAmt.add(ecsAmt);
            if (Objects.isNull(expenseList) || expenseList.isEmpty()){
                result.put(itemExpense.getEcsDetailId(), new ArrayList<>());
                continue;
            }
            LinkedHashMap<String, Object> detailLinkedHashMap = (LinkedHashMap<String, Object>) expenseList.get(0);
            JSONObject detailJson = new JSONObject(detailLinkedHashMap);
            PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
            qo.setExpenseCode(expDetailCode);
            qo.setAgyCode(view.getAgyCode());
            qo.setMofDivCode(view.getMofDivCode());
            qo.setFiscal(view.getFiscal());
            qo.setTenantId(view.getTenantId());
            PcxBasExpType baseExpByQO = basExpTypeDao.getBaseExpByQO(qo);
            List<PcxBillExpDetailBase> detailBaseList = new ArrayList<>();
            String uuid = UUID.randomUUID().toString();
            int seq = 1;
            for (Object o : expenseList) {
                detailJson = new JSONObject((LinkedHashMap<String, Object> )o);
                PcxBillExpDetailBase detailBase = ExpenseBeanUtil.getEntityDetailBean(baseExpByQO.getParentCode());
                detailBase = JSON.parseObject(detailJson.toJSONString(), detailBase.getClass());
                detailBase.setId(IDGenerator.id());
                detailBase.setExpenseId("");
                detailBase.setExpDetailCode(expDetailCode);
                detailBase.setInputAmt(Objects.isNull(detailBase.getInputAmt()) ? BigDecimal.ZERO : detailBase.getInputAmt());
                detailBase.setEcsAmt(Objects.isNull(detailBase.getEcsAmt()) ? ecsAmt : detailBase.getEcsAmt());
                detailInputAmt = detailInputAmt.add(detailBase.getInputAmt());
                detailBase.setCheckAmt(detailBase.getInputAmt());
                detailBase.setSource(source);
                detailBaseList.add(detailBase);

            }
            if (CollectionUtils.isNotEmpty(detailBaseList)){
                for (PcxBillExpDetailBase detailBase : detailBaseList) {
                    detailBase.setFiscal(view.getFiscal());
                    detailBase.setMofDivCode(view.getMofDivCode());
                    detailBase.setAgyCode(view.getAgyCode());
                    detailBase.setTenantId(view.getTenantId());
                }
                BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(baseExpByQO.getParentCode());
                CheckMsg<Void> validate = detailBean.validate(detailBaseList, FormSettingEnums.BillFuncCodeEnum.EXPENSES_BILL.getCode());
                if (!validate.isSuccess()){
                    throw new RuntimeException(validate.getMsgInfo());
                }
            }
            result.put(itemExpense.getEcsDetailId(), detailBaseList);
        }
        if (detailInputAmt.compareTo(allEcsAmt) >0){
            throw new RuntimeException("报销金额不能大于发票金额");
        }
        return result;
    }

    private List<PcxExpDetailEcsRel> getEcsRelList(String billId) {
        return expDetailEcsRelDao.selectByBillId(billId);
    }

    /**
     * 理票-保存或更新招待费专属信息
     * @param values
     * @param view
     */
    private void saveOrUpdateExpInlandfee(Collection<PcxBillExpInlandfee> values, PcxBill view) {
        List<PcxBillExpInlandfee> filteredList = values.stream()
                .filter(e -> PcxConstant.TREAT_EXPENSE_30217.equals(e.getExpenseCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            log.error("未找到 expenseCode={} 的费用记录", PcxConstant.TREAT_EXPENSE_30217);
            return;
        }

        // 如果有多个匹配项，取第一个作为基准
        PcxBillExpInlandfee target = filteredList.get(0);

        // 将所有费用的金额累加
        BigDecimal totalInputAmt = values.stream()
                .map(PcxBillExpInlandfee::getInputAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCheckAmt = values.stream()
                .map(PcxBillExpInlandfee::getCheckAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        target.setInputAmt(totalInputAmt);
        target.setCheckAmt(totalCheckAmt);
        target.setBillId(view.getId());

        // 保存或更新这条记录
        PcxBillExpInlandfee existing = pcxBillExpInlandfeeDao.selectById(target.getId());
        if (existing == null) {
            pcxBillExpInlandfeeDao.insert(target);
        } else {
            pcxBillExpInlandfeeDao.updateById(target);
        }
    }

    /**
     * 理票-保存或更新招待费费用明细
     */
    private void saveOrUpdateInlandfeeDetail(List<PcxBillExpInlandfee> pcxBillExpInlandfees,
                                           List<PcxBillExpDetailBase> detailCommonList, PcxBill pcxBill, Map<String, PcxBasExpTypeVO> parentMap) {

        Map<String, List<PcxBillExpDetailBase>> detailMap = detailCommonList.stream()
                .collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode()).getLastCode()));

        Map<String, PcxBillExpBase> baseMap = pcxBillExpInlandfees.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : detailMap.entrySet()) {
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(entry.getKey());
            PcxBillExpBase pcxBillExpBase = baseMap.get(entry.getKey());
            //在这里面 entry 做类型实体转换 然后赋值 pranteCode 和 billId
            List<PcxBillExpDetailBase> saveValue = entry.getValue();
            for (PcxBillExpDetailBase value : saveValue) {
                PcxBillExpDetailInlandfee metting = new PcxBillExpDetailInlandfee();
                BeanUtils.copyProperties(value, metting);
                metting.setBillId(pcxBill.getId());
                PcxBasExpTypeVO pcxBasExpTypeVO = parentMap.get(value.getExpDetailCode());
                if (pcxBasExpTypeVO != null) {
                    metting.setParentCode(pcxBasExpTypeVO.getParentCode());
                    metting.setLastCode(pcxBasExpTypeVO.getLastCode());
                }
            }
            detailBean.saveOrUpdate(pcxBillExpBase, saveValue, pcxBill);
        }
    }



    private List<PcxExpDetailEcsRel> collectEcsRel(InvoiceDtoWrapper wrapper,
                                                   List<PcxBillExpAttachRel> attachRelList,
                                                   ExpInvoiceQO invoiceQO,
                                                   List<PcxEcsSettl> settlList) {
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        //给票建立一个基础的关联，因为票有可能关联多个费用明细，后面直接copy它
        List<PcxExpDetailEcsRel> copyRel = getEcsRel(wrapper, attachRelList, settlList);
        for (PcxBillExpAttachRel attachRel : attachRelList) {
            attachRel.setFiscal(invoiceQO.getFiscal());
            attachRel.setAgyCode(invoiceQO.getAgyCode());
            attachRel.setMofDivCode(invoiceQO.getMofDivCode());
            attachRel.setTenantId(invoiceQO.getTenantId());
        }
        for (PcxEcsSettl ecsSettl : settlList) {
            ecsSettl.setFiscal(invoiceQO.getFiscal());
            ecsSettl.setAgyCode(invoiceQO.getAgyCode());
            ecsSettl.setMofDivCode(invoiceQO.getMofDivCode());
        }
        if (CollectionUtils.isEmpty(copyRel)){
            return result;
        }
        copyRel.forEach(item->{
            item.setFiscal(invoiceQO.getFiscal());
            item.setAgyCode(invoiceQO.getAgyCode());
            item.setMofDivCode(invoiceQO.getMofDivCode());
            item.setExpenseTypeCode("");
            item.setExpenseTypeName("");
            item.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
        });

        if (Objects.nonNull(wrapper.getMatchExpType())){
            //未匹配的票有可能是匹配的费用类型，但是生成明细时数据不完整，保存费用类型，和费用明细
            copyRel.forEach(item->{
                item.setExpenseTypeCode(wrapper.getMatchExpType().getExpenseCode());
                item.setExpenseTypeName(wrapper.getMatchExpType().getExpenseName());
            });
            if (CollectionUtils.isNotEmpty(wrapper.getExpDetailList())){
                copyRel.forEach(item->{
                    item.setInputAmt(BigDecimal.ZERO);
                    item.setCheckAmt(BigDecimal.ZERO);
                });
                copyRel.get(0).setEcsContent(JSON.toJSONString(wrapper.getExpDetailList()));
                BigDecimal inputAmt = BigDecimal.ZERO;
                for (Object o : wrapper.getExpDetailList()) {
                    PcxBillExpDetailBase detailBase = (PcxBillExpDetailBase) o;
                    if (Objects.nonNull(detailBase.getInputAmt())){
                        inputAmt = inputAmt.add(detailBase.getInputAmt());
                    }
                }
                copyRel.get(0).setInputAmt(inputAmt);
            }
        }
        if (Objects.equals(wrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            //如果是匹配的票，则把上面保存的费用明细去掉，不需要保存在票里面
            copyRel.forEach(item->{
                item.setEcsContent("");
                item.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            });
            //解析出的费用明细都挂到第一个项目上
            PcxExpDetailEcsRel firstEcsRel = copyRel.get(0);
            if (CollectionUtils.isNotEmpty(wrapper.getExpDetailList())){
                for (Object o : wrapper.getExpDetailList()) {
                    PcxBillExpDetailBase detailBase = (PcxBillExpDetailBase) o;
                    PcxExpDetailEcsRel rel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(firstEcsRel, rel);
                    rel.setId(IDGenerator.id());
                    rel.setDetailId(detailBase.getId());
                    rel.setExpenseTypeCode(detailBase.getExpDetailCode());
                    rel.setInputAmt(detailBase.getInputAmt());
                    rel.setCheckAmt(detailBase.getInputAmt());
                    rel.setEmpCode(detailBase.getEmpCode());
                    result.add(rel);
                }
            }
            if (CollectionUtils.isNotEmpty(wrapper.getExpenseList())){
                for (Object o : wrapper.getExpenseList()) {
                    PcxBillExpBase expBase = (PcxBillExpBase) o;
                    PcxExpDetailEcsRel rel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(copyRel, rel);
                    rel.setId(IDGenerator.id());
                    rel.setExpenseTypeCode(expBase.getExpenseCode());
                    rel.setItemName(expBase.getEcsRelItemName());
                    result.add(rel);
                }
            }

        }else{
            //未匹配的票记录一下票信息
            result.addAll(copyRel);
        }
        return result;
    }
    private List<PcxExpDetailEcsRel> getEcsRel(InvoiceDtoWrapper wrapper, List<PcxBillExpAttachRel> attachRelList, List<PcxEcsSettl> settlList) {
        EcsDtoTransHelper.EcsBillDispose ecsBillDispose = EcsBillExternalServiceImpl.getEcsBillDisposeMap().get(wrapper.getType());
        if (Objects.nonNull(ecsBillDispose)){
            return ecsBillDispose.initEcsRelListTravel(wrapper.getDto(), attachRelList, settlList, Lists.newArrayList(), new HashMap<>());
        }
        return Lists.newArrayList();
    }

    private CollectExpListDto collectExpBase(InvoiceDtoWrapper wrapper) {
        CollectExpListDto dto = new CollectExpListDto();
        if (Objects.equals(wrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            dto.addExpBaseList(wrapper.getExpenseList());
            dto.addExpDetailList(wrapper.getExpDetailList());
        }
        return dto;
    }

    private void fillExpenseTypeName(List<PcxExpDetailEcsRel> allEcsRel, ExpInvoiceQO invoiceQO) {
        List<String> expenseTypeCode = allEcsRel.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode())
                        && StringUtil.isEmpty(item.getExpenseTypeName()))
                .map(PcxExpDetailEcsRel::getExpenseTypeCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expenseTypeCode)) {
            PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
            expTypeQO.setFiscal(invoiceQO.getFiscal());
            expTypeQO.setAgyCode(invoiceQO.getAgyCode());
            expTypeQO.setMofDivCode(invoiceQO.getMofDivCode());
            expTypeQO.setExpTypeCodes(expenseTypeCode);
            List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectSimpleList(expTypeQO);
            Map<String, String> expTypeNameMap = expTypeList.stream()
                    .collect(Collectors.toMap(PcxBasExpType::getExpenseCode,
                            PcxBasExpType::getExpenseName, (key1, key2) -> key1));
            for (PcxExpDetailEcsRel rel : allEcsRel) {
                if (StringUtil.isNotEmpty(rel.getExpenseTypeCode())
                        && StringUtil.isEmpty(rel.getExpenseTypeName())) {
                    rel.setExpenseTypeName(expTypeNameMap.get(rel.getExpenseTypeCode()));
                }
            }
        }
    }


    private PcxBillExpDetailInlandfee initRelDetail(PcxExpDetailEcsRel rel, ExpInvoiceQO invoiceQO) {
        PcxBillExpDetailInlandfee detail = new PcxBillExpDetailInlandfee();
        detail.setId(IDGenerator.id());
        detail.setEcsAmt(rel.getEcsAmt());
        detail.setInputAmt(rel.getInputAmt());
        detail.setCheckAmt(rel.getCheckAmt());
        detail.setExpDetailCode(rel.getExpenseTypeCode());
        detail.setExpDetailName(rel.getExpenseTypeName());
        detail.setParentCode(rel.getParentCode());
        detail.setLastCode(rel.getLastCode());
        detail.setSource(BillExpDetailSourceEnum.ECS.getCode());
        detail.setFiscal(invoiceQO.getFiscal());
        detail.setAgyCode(invoiceQO.getAgyCode());
        detail.setMofDivCode(invoiceQO.getMofDivCode());
        detail.setTaxAmt(rel.getTaxAmt());
        detail.setTaxRate(rel.getTaxRate());

        rel.setDetailId(detail.getId());

        // 按rel中的扩展要素默认值给detail扩展要素赋默认值
        if (rel.getExtItemDefValMap() != null) {
            rel.getExtItemDefValMap().forEach((k, v) -> {
                try {
                    ReflectUtil.setFieldValue(detail, k, v);
                } catch (Exception e) {
                    log.error(e.getLocalizedMessage(), e);
                }
            });
        }
        return detail;

    }

    private List<PcxBasExpType> collectEcsRelExpType(List<PcxExpDetailEcsRel> allEcsRel) {
        List<PcxBasExpType> result = new ArrayList<>();
        Set<String> expenseTypeCodeSet = new HashSet<>();
        allEcsRel.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode()))
                .forEach(item -> {
                    if (!expenseTypeCodeSet.contains(item.getExpenseTypeCode())) {
                        expenseTypeCodeSet.add(item.getExpenseTypeCode());
                        PcxBasExpType expType = new PcxBasExpType();
                        expType.setExpenseCode(item.getExpenseTypeCode());
                        expType.setExpenseName(item.getExpenseTypeName());
                        result.add(expType);
                    }
                });
        return result;
    }

    private PcxBill buildBill(StartExpenseQO expenseQO, List<PcxBasItemExp> expTypeList) {
        MadEmployeeDTO madEmployeeDTO = queryMadEmpDTO(expenseQO);
        return PcxBill.builder()
                .agyCode(expenseQO.getAgyCode())
                .fiscal(expenseQO.getFiscal())
                .mofDivCode(expenseQO.getMofDivCode())
                .itemCode(expenseQO.getItemCode())
                .itemName(expenseQO.getItemName())
                .claimantCode(madEmployeeDTO.getEmployeeCode())
                .claimantName(madEmployeeDTO.getEmployeeName())
                .departmentCode(madEmployeeDTO.getDepartmentCode())
                .departmentName(madEmployeeDTO.getDepartmentName())
                .billFuncCode(BillFuncCodeEnum.EXPENSE.getCode())
                .billFuncName(BillFuncCodeEnum.EXPENSE.getName())
                .transDate(expenseQO.getTransDate())
                .createdTime(DateUtil.nowTime())
                .creator(expenseQO.getUserCode())
                .creatorName(expenseQO.getUserName())
                .modifiedTime(DateUtil.nowTime())
                .modifier(expenseQO.getUserCode())
                .modifierName(expenseQO.getUserName())
                .reason("")
                .bizType(ItemBizTypeEnum.INLANDFEE.getCode())
                .bizTypeName(ItemBizTypeEnum.INLANDFEE.getName())
                .expenseCodes(expTypeList.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.joining(",")))
                .expenseNames(expTypeList.stream().map(PcxBasItemExp::getExpenseName).collect(Collectors.joining(",")))
                .build();
    }

    private MadEmployeeDTO queryMadEmpDTO(StartExpenseQO expenseQO) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setFiscal(expenseQO.getFiscal());
        pcxBaseDTO.setAgyCode(expenseQO.getAgyCode());
        pcxBaseDTO.setMofDivCode(expenseQO.getMofDivCode());
        return madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, expenseQO.getUserCode());
    }

    private void disposeDetailDepartmentCode(PcxBill bill, List<PcxBillExpDetailBase> detailList) {
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (PcxBillExpDetailBase common : detailList) {
                common.setDepartmentCode(bill.getDepartmentCode());
                common.setDepartmentName(bill.getDepartmentName());
            }
        }
    }

    private List<PcxBillExpInlandfee> buildBaseExpByExpType(List<PcxBasItemExp> baseTypeList, ExpInvoiceQO qo) {

        List<PcxBillExpInlandfee> expList = new ArrayList<>();

        for (PcxBasItemExp basItemExp : baseTypeList) {
            PcxBillExpInlandfee entityBean = new PcxBillExpInlandfee();
            BeanUtils.copyProperties(basItemExp, entityBean);
            entityBean.setId(IDGenerator.id());
            entityBean.setInputAmt(BigDecimal.ZERO);
            entityBean.setFiscal(qo.getFiscal());
            entityBean.setAgyCode(qo.getAgyCode());
            entityBean.setMofDivCode(qo.getMofDivCode());
            expList.add(entityBean);
        }
        return expList;
    }

    private void collectBillAmt(PcxBill bill, List<PcxBillExpInlandfee> baseExpList) {
        BigDecimal totalInputAmt = BigDecimal.ZERO;
        BigDecimal totalCheckAmt = BigDecimal.ZERO;

        for (PcxBillExpInlandfee expBase : baseExpList) {
            totalInputAmt = totalInputAmt.add(expBase.getInputAmt() == null ? BigDecimal.ZERO: expBase.getInputAmt());
            totalCheckAmt = totalCheckAmt.add(expBase.getCheckAmt() == null ? BigDecimal.ZERO: expBase.getCheckAmt());
        }

        bill.setInputAmt(totalInputAmt);
        bill.setCheckAmt(totalCheckAmt);

    }

    private List<PcxBillExpAttachRel> getAttachRelList(String billId, Predicate<PcxBillExpAttachRel> filter) {
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, billId));
        return attachRelList.stream().filter(filter).collect(Collectors.toList());
    }

    private void fillNoExistsExpType(List<PcxBillExpInlandfee> baseExpList, List<PcxBasExpType> expTypeList, PcxBill bill) {
        List<String> collect = baseExpList.stream().map(PcxBillExpBase::getExpenseCode).collect(Collectors.toList());
        expTypeList.stream().filter(item -> !collect.contains(item.getExpenseCode())).forEach(item -> {
            PcxBillExpInlandfee base = new PcxBillExpInlandfee();
            base.setId(IDGenerator.id());
            base.setBillId(bill.getId());
            base.setExpenseCode(item.getExpenseCode());
            base.setExpenseName(item.getExpenseName());
            base.setAgyCode(bill.getAgyCode());
            base.setFiscal(bill.getFiscal());
            base.setMofDivCode(bill.getMofDivCode());
            baseExpList.add(base);
        });
        bill.setExpenseCodes(baseExpList.stream().map(PcxBillExpBase::getExpenseCode).collect(Collectors.joining(",")));
        bill.setExpenseNames(baseExpList.stream().map(PcxBillExpBase::getExpenseName).collect(Collectors.joining(",")));
    }


    private void fillDetailExpenseId(List<PcxBillExpInlandfee> baseExpList, List<PcxBillExpDetailInlandfee> detailCommons) {
        if (CollectionUtils.isNotEmpty(detailCommons)) {
            Map<String, List<PcxBillExpDetailInlandfee>> map = detailCommons.stream().collect(Collectors.groupingBy(PcxBillExpDetailInlandfee::getExpDetailCode));
            for (PcxBillExpInlandfee expBase : baseExpList) {
                List<PcxBillExpDetailInlandfee> detailList = map.get(expBase.getExpenseCode());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    for (PcxBillExpDetailInlandfee common : detailList) {
                        common.setExpenseId(expBase.getId());
                    }
                }
            }
        }
    }

    /**
     *
     * @param updateQO
     * @return
     */
    private List<PcxEcsSettl> analysisEcsSettl(UpdateEcsCommonQO updateQO) {
        List<PcxEcsSettl> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getSettlList())){
            for (EcsSettlQO ecsSettlQO : updateQO.getSettlList()) {
                PcxEcsSettl ecsSettl = new PcxEcsSettl();
                BeanUtils.copyProperties(ecsSettlQO, ecsSettl);
                ecsSettl.setBillId(updateQO.getBillId());
                ecsSettl.setEcsBillId(updateQO.getEcsBillId());
                ecsSettl.setEcsTypeCode(updateQO.getEcsBillType());
                ecsSettl.setFiscal(updateQO.getFiscal());
                ecsSettl.setAgyCode(updateQO.getAgyCode());
                ecsSettl.setMofDivCode(updateQO.getMofDivCode());
                result.add(ecsSettl);
            }
        }
        return result;
    }

    private List<PcxBillExpAttachRel> analysisAttachRelList(List<AttachRelQO> attachRelList, PcxBill view, String ecsBillId) {
        List<PcxBillExpAttachRel> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachRelList)){
            for (AttachRelQO attachRelQO : attachRelList) {
                PcxBillExpAttachRel attachRel = new PcxBillExpAttachRel();
                attachRel.setBillId(view.getId());
                attachRel.setFiscal(view.getFiscal());
                attachRel.setAgyCode(view.getAgyCode());
                attachRel.setMofDivCode(view.getMofDivCode());
                attachRel.setTenantId(view.getTenantId());
                attachRel.setAttachId(attachRelQO.getBillId());
                attachRel.setFileName(attachRelQO.getFileName());
                attachRel.setRelType(PcxExpAttachRelType.ECS.getCode());
                attachRel.setRelId(ecsBillId);
                result.add(attachRel);
            }
        }
        return result;
    }


    /**
     * (TODO (待抽出公共方法))  转换出更新票据的DTO
     */
    private UpdateEcsBillDTO analysisUpdateEcsBillDTO(List<PcxEcsSettl> ecsSettlList, UpdateEcsCommonQO updateQO) {
        UpdateEcsBillDTO dto = new UpdateEcsBillDTO();
        dto.setBillNo(updateQO.getBillNo());
        dto.setBillAmt(updateQO.getBillAmt());
        dto.setBillId(updateQO.getEcsBillId());
        dto.setFiscal(updateQO.getFiscal());
        dto.setAgencyCode(updateQO.getAgyCode());
        dto.setMofDivCode(updateQO.getMofDivCode());
        dto.setTenantId(updateQO.getTenantId());
        dto.setBillTypeCode(updateQO.getEcsBillType());
        List<UpdateEcsBillDTO.EcsAttachRelInfo> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getEcsBillClassRelList())){
            for (AttachRelQO attachRelQO : updateQO.getEcsBillClassRelList()) {
                UpdateEcsBillDTO.EcsAttachRelInfo relInfo = UpdateEcsBillDTO.EcsAttachRelInfo.builder()
                        .agencyCode(updateQO.getAgyCode())
                        .fiscal(updateQO.getFiscal())
                        .mofDivCode(updateQO.getMofDivCode())
                        .billId(attachRelQO.getBillId())
                        .fileName(attachRelQO.getFileName())
                        .billTypeCode(attachRelQO.getBillTypeCode())
                        .createUser(updateQO.getUserName())
                        .createUserCode(updateQO.getUserCode())
                        .isDeleted("2")
                        .classType("bus").build();
                attachRelList.add(relInfo);
            }
        }
        dto.setEcsBillClassRelList(attachRelList);
        List<EcsBillSettleDTO> billSettlList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ecsSettlList)){
            for (PcxEcsSettl ecsSettl : ecsSettlList) {
                EcsBillSettleDTO settleDTO = new EcsBillSettleDTO();
                BeanUtils.copyProperties(ecsSettl, settleDTO);
                billSettlList.add(settleDTO);
            }
        }
        dto.setSettlDetails(billSettlList);
        dto.setEcsBillInfo(updateQO.getEcsBillInfo());
        return dto;
    }

    /**
     * (TODO (待抽出公共方法))  查询出票据关系对应的票据明细
     * @param optEcsRelList
     * @return
     */
    private List<PcxBillExpDetailInlandfee> queryEcsRelDetailCommon(List<PcxExpDetailEcsRel> optEcsRelList) {
        List<String> detailIds = optEcsRelList.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getDetailId()))
                .map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailIds)){
            return pcxBillExpDetailInlandfeeDao.selectBatchIds(detailIds);
        }else {
            return Lists.newArrayList();
        }
    }


    /**
     * (TODO (待抽出公共方法))  分析出票据关系列表和票据明细列表
     * @param optEcsRelList
     * @param detailCommonList
     * @param ecsDetailAmtVOMap
     * @param invoiceQO
     * @return
     */
    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailInlandfee>> analysisEcsRelAndDetailCommon(List<PcxExpDetailEcsRel> optEcsRelList,
                                                                                                        List<PcxBillExpDetailInlandfee> detailCommonList,
                                                                                                        Map<String, EcsDetailAmtVO> ecsDetailAmtVOMap,
                                                                                                        ExpInvoiceQO invoiceQO) {
        List<PcxExpDetailEcsRel> addEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailInlandfee> addDetailList = new ArrayList<>();
        Map<String, PcxExpDetailEcsRel> ecsRelMap = optEcsRelList
                .stream().collect(Collectors.toMap(PcxExpDetailEcsRel::getId, Function.identity(), (key1, key2) -> key1));
        for (Map.Entry<String, EcsDetailAmtVO> entry : ecsDetailAmtVOMap.entrySet()) {
            String ecsRelId = entry.getKey();
            EcsDetailAmtVO amtVO = entry.getValue();
            PcxExpDetailEcsRel oldRel = ecsRelMap.get(ecsRelId);
            if (Objects.nonNull(oldRel)){
                PcxExpDetailEcsRel newRel = new PcxExpDetailEcsRel();
                BeanUtils.copyProperties(oldRel, newRel);
                newRel.setId(IDGenerator.id());
                newRel.setInputAmt(amtVO.getInputAmt());
                newRel.setCheckAmt(amtVO.getInputAmt());
                newRel.setRemark(amtVO.getRemark());
                newRel.setExpenseTypeCode(amtVO.getExpenseTypeCode());
                newRel.setExpenseTypeName(amtVO.getExpenseTypeName());
                if (CollectionUtils.isNotEmpty(amtVO.getDetailList())){
                    int index = 0;
                    for (EcsCommonDetailVO detailVO : amtVO.getDetailList()) {
                        PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                        BeanUtils.copyProperties(newRel, copyRel);
                        copyRel.setId(IDGenerator.id());
                        PcxBillExpDetailInlandfee meeting = initRelDetail(copyRel, invoiceQO);
                        BeanUtils.copyProperties(detailVO, meeting);
                        if (index > 0){
                            copyRel.setInputAmt(BigDecimal.ZERO);
                            copyRel.setCheckAmt(BigDecimal.ZERO);
                            meeting.setInputAmt(BigDecimal.ZERO);
                            meeting.setCheckAmt(BigDecimal.ZERO);
                        }
                        meeting.setEcsNum(amtVO.getEcsNum());
                        addEcsRelList.add(copyRel);
                        addDetailList.add(meeting);
                        index ++;
                    }
                }else{
                    PcxBillExpDetailInlandfee common = initRelDetail(newRel, invoiceQO);
                    common.setEcsNum(amtVO.getEcsNum());
                    addDetailList.add(common);
                    addEcsRelList.add(newRel);
                }
            }

        }
        return Pair.of(addEcsRelList, addDetailList);
    }
    /**
     * 获取报销单关联的费用数据
     * @param view
     * @return
     */
    public List<PcxBillExpInlandfee> queryBillExpBaseList(PcxBill view) {
        String[] expTypeCodeArr = StringUtil.getStringValue(view.getExpenseCodes()).split(",");
        List<PcxBillExpInlandfee> result = new ArrayList<>();
        for (String expType : expTypeCodeArr) {
            BillExpenseService<PcxBillExpBase> bean = ExpenseBeanUtil.getBean(expType, view.getBizType());
            PcxBillExpBase expBase = bean.view(expType, view);
            if (Objects.nonNull(expBase)){
                result.add((PcxBillExpInlandfee)expBase);
            }
        }
        return result;
    }

    private Map<String, PcxBasExpTypeVO> getExpTypeParentMap(ExpInvoiceQO invoiceQO, List<String> allDetailCodes) {
        if (CollectionUtils.isNotEmpty(allDetailCodes)) {
            List<PcxBasExpTypeVO> pcxBasExpTypes = getExpTypeByCodes(invoiceQO, allDetailCodes);

            return pcxBasExpTypes.stream()
                    .collect(Collectors.toMap(
                            PcxBasExpTypeVO::getExpenseCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        } else {
            return new HashMap<>();
        }
    }

    private List<PcxBasExpTypeVO> getExpTypeByCodes(ExpInvoiceQO invoiceQO, List<String> allDetailCodes){
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setFiscal(invoiceQO.getFiscal());
        qo.setAgyCode(invoiceQO.getAgyCode());
        qo.setMofDivCode(invoiceQO.getMofDivCode());
        qo.setExpTypeCodes(allDetailCodes);
        return basExpTypeDao.getTreeData(qo);
    }

    private void summaryExpBaseAmtAndMarkDetail(Map<String, PcxBillExpBase> baseExpMap, List<PcxBillExpDetailBase> allExpDetailBase,
                                                Map<String, PcxBasExpTypeVO> parentMap) {
        //从费用明细中收集金额，更新到对应的费用上面
        Map<String, List<PcxBillExpDetailBase>> detailMap = allExpDetailBase
                .stream().collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode()).getLastCode()));
        for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : detailMap.entrySet()) {
            PcxBillExpBase expBase = baseExpMap.get(entry.getKey());
            BigDecimal totalInputAmt = BigDecimal.ZERO;
            BigDecimal totalcheckAmt = BigDecimal.ZERO;
            for (PcxBillExpDetailBase detailBase : entry.getValue()) {
                detailBase.setExpenseId(expBase.getId());
                totalInputAmt = totalInputAmt.add(detailBase.getInputAmt());
                if (Objects.nonNull(detailBase.getCheckAmt())){
                    totalcheckAmt = totalcheckAmt.add(detailBase.getCheckAmt());
                }            }
            expBase.setInputAmt(totalInputAmt);
            expBase.setCheckAmt(totalcheckAmt);
        }

    }

    private List<PcxBillExpDetailBase> queryBillExpDetailList(PcxBill view) {
        String[] split = StringUtil.getStringValue(view.getExpenseCodes()).split(",");
        List<PcxBillExpDetailBase> result = new ArrayList<>();
        for (String expTypeCode : split) {
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(expTypeCode);
            result.addAll(detailBean.listByExpenseCode(expTypeCode, view));
        }
        return result;
    }

    private void changeEcsAmt(List<PcxExpDetailEcsRel> optEcsRelList, List<EcsItemExpense> itemExpenseList) {
        Map<String, BigDecimal> itemEcsMap = itemExpenseList.stream().collect(Collectors.toMap(EcsItemExpense::getEcsDetailId, EcsItemExpense::getEcsAmt, (key1, key2) -> key1));
        optEcsRelList.forEach(item->{
            BigDecimal ecsAmt = itemEcsMap.get(item.getEcsDetailId());
            if (ecsAmt != null){
                item.setEcsAmt(ecsAmt);
            }
        });
    }

    private List<PcxExpDetailEcsRel> analysisNewEcsRelEcsItem(List<PcxExpDetailEcsRel> relList,
                                                              Map<String, List<PcxBillExpDetailBase>> detailMap,
                                                              String expenseTypeCode) {
        //根据票项目遍历，每个项目的费用明细进行生成ecsRel和明细的关联关系
        //如果项目没有费用明细，则把他自己在保存一下
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = relList.stream()
                .collect(Collectors.groupingBy(rel ->
                        Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                ));
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsDetailMap.entrySet()) {
            PcxExpDetailEcsRel rel = entry.getValue().get(0);
            List<PcxBillExpDetailBase> detailBases = detailMap.get(rel.getEcsDetailId());
            if (CollectionUtils.isNotEmpty(detailBases)){
                List<PcxExpDetailEcsRel> ecsRelList = analysisNewEcsRel(rel, detailBases);
                result.addAll(ecsRelList);
            }else{
                //旧的删掉，重新插入一个
                rel.setId(IDGenerator.id());
                rel.setDetailId("");
                rel.setInputAmt(BigDecimal.ZERO);
                rel.setCheckAmt(BigDecimal.ZERO);
                rel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
                rel.setExpenseTypeCode(expenseTypeCode);
                result.add(rel);
            }
        }
        return result;
    }

    private List<PcxExpDetailEcsRel> analysisNewEcsRel(PcxExpDetailEcsRel rel, List<PcxBillExpDetailBase> detailList) {
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        for (PcxBillExpDetailBase detail : detailList) {
            detail.setEcsAmt(rel.getEcsAmt());
            PcxExpDetailEcsRel newRel = JSON.parseObject(JSON.toJSONString(rel), PcxExpDetailEcsRel.class);
            newRel.setId(IDGenerator.id());
            newRel.setDetailId(detail.getId());
            newRel.setExpenseTypeCode(detail.getExpDetailCode());
            newRel.setEmpCode(detail.getEmpCode());
            newRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            newRel.setInputAmt(detail.getInputAmt());
            newRel.setCheckAmt(detail.getCheckAmt());
            result.add(newRel);
        }
        return result;
    }
}