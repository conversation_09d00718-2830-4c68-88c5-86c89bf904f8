package com.pty.pcx.service.impl.contract;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pct.entity.vo.PctBillInfoVO;
import com.pty.pcx.api.contract.PcxBillContractRelService;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.dao.contract.PcxBillContractRelDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.contract.PcxBillContractRel;
import com.pty.pcx.pct.IPctExternalService;
import com.pty.pcx.qo.contract.ContractVO;
import com.pty.pcx.util.BatchServiceUtil;
import liquibase.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 报销单关联合同付款计划(PcxBillContractRel)表服务实现类
 * <AUTHOR>
 * @since 2025-01-16 16:01:16
 */
@Slf4j
@Indexed
@Service
public class PcxBillContractRelServiceImpl implements PcxBillContractRelService {

	@Resource
	private PcxBillContractRelDao pcxBillContractRelDao;
	@Resource
	private BatchServiceUtil batchServiceUtil;
	@Resource
	private IPctExternalService pctExternalService;

	@Override
	public ContractVO getBillContract(PcxBill pcxBill) {
		if (!Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.COMMON.getCode())){
			return null;
		}
		List<PcxBillContractRel> relList = pcxBillContractRelDao.selectList(Wrappers.lambdaQuery(PcxBillContractRel.class)
				.eq(PcxBillContractRel::getBillId, pcxBill.getId()));
		if (CollectionUtils.isEmpty(relList)){
			return null;
		}
		//查询合同信息 ,把合同的付款计划返给前端，如果是自己选的，则表上selected
		PctBillInfoVO pctBillInfoVO = pctExternalService.selectBillInfo(relList.get(0).getContractId());
		if (Objects.isNull(pctBillInfoVO)){
			throw new RuntimeException("合同信息不存在");
		}
		ContractVO contractVO = collateContract(relList);
		fillPayPlanList(pctBillInfoVO, contractVO);
		return contractVO;
	}

	private void fillPayPlanList(PctBillInfoVO billInfoVO, ContractVO contractVO) {
		if (CollectionUtils.isNotEmpty(billInfoVO.getPlansList())){
			Map<String, PcxBillContractRel> planMap = contractVO.getPlanMap();
			contractVO.setPlansList(billInfoVO.getPlansList().stream().map(payPlan -> {
				ContractVO.PayPlan pcxPlan = new ContractVO.PayPlan();
				BeanUtils.copyProperties(payPlan, pcxPlan);
				PcxBillContractRel pay = planMap.get(payPlan.getPlanId());
				if (Objects.nonNull(pay)){
					pcxPlan.setPayAmt(pay.getPayAmt());
					pcxPlan.setSelected(pay.getPayAmt().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0);
				}else{
					pcxPlan.setPayAmt(BigDecimal.ZERO);
					pcxPlan.setSelected(0);
				}
				return pcxPlan;
			}).collect(Collectors.toList()));
		}
		if (CollectionUtils.isNotEmpty(billInfoVO.getAttachs())){
			contractVO.setAttachs(billInfoVO.getAttachs().stream().map(attach -> {
				ContractVO.ContractAttach pcxAttach = new ContractVO.ContractAttach();
				BeanUtils.copyProperties(attach, pcxAttach);
				return pcxAttach;
			}).collect(Collectors.toList()));
		}
		if (Objects.nonNull(billInfoVO.getPrimaryAttach())){
			ContractVO.PctAttach primaryAttach = new ContractVO.PctAttach();
			BeanUtils.copyProperties(billInfoVO.getPrimaryAttach(), primaryAttach);
			contractVO.setPrimaryAttach(primaryAttach);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateContractRel(ContractVO contract, PcxBill pcxBill) {
		pcxBillContractRelDao.delete(Wrappers.lambdaQuery(PcxBillContractRel.class)
				.eq(PcxBillContractRel::getBillId, pcxBill.getId()));
		BigDecimal checkAmt = pcxBill.getCheckAmt();
		//匹配付款计划支付金额
		if (Objects.nonNull(contract) && CollectionUtils.isNotEmpty(contract.getPlansList())){
			//保存合同的时候系统匹配支付金额
			allocatePayAmt(contract.getPlansList(), checkAmt);
			List<PcxBillContractRel> relList = contract.getPlansList().stream().map(payPlan -> {
				PcxBillContractRel rel = new PcxBillContractRel();
				rel.setBillId(pcxBill.getId());
				rel.setContractId(contract.getBillId());
				rel.setContractName(contract.getContractName());
				rel.setContractNo(contract.getBillNo());
				rel.setPartyAName(contract.getPartyAName());
				rel.setPartyBName(contract.getPartyBName());
				rel.setSignDate(contract.getSignDate());
				rel.setContractAmt(contract.getAmount());
				rel.setPlanId(payPlan.getPlanId());
				rel.setPlanName(payPlan.getPlanName());
				rel.setPlanContent(payPlan.getPlanContent());
				rel.setPlanPayRadio(payPlan.getPlanPayRatio());
				rel.setPlanPayAmt(payPlan.getPlanPayAmt());
				rel.setPlanPayDate(payPlan.getPlanPayDate());
				rel.setPlanPayCondition(payPlan.getPlanPayCondition());
				rel.setPayAmt(payPlan.getPayAmt());
				rel.setFiscal(pcxBill.getFiscal());
				rel.setAgyCode(pcxBill.getAgyCode());
				rel.setMofDivCode(pcxBill.getMofDivCode());
				rel.setTenantId(pcxBill.getTenantId());
				return rel;
			}).collect(Collectors.toList());
			batchServiceUtil.batchProcess(relList, PcxBillContractRelDao.class, PcxBillContractRelDao::insert);
		}
	}

	/**
	 * 获取单据关联合同付款计划id列表
	 * @param billId
	 * @return
	 */
	@Override
	public List<String> getBillPlanIdList(String billId) {
		if(StringUtils.isEmpty(billId)){
			return new ArrayList<>();
		}
		return pcxBillContractRelDao.selectList(Wrappers.lambdaQuery(PcxBillContractRel.class)
						.eq(PcxBillContractRel::getBillId, billId)
						.gt(PcxBillContractRel::getPlanPayAmt, 0))
				.stream().map(PcxBillContractRel::getPlanId).collect(Collectors.toList());
	}

	private void allocatePayAmt(List<ContractVO.PayPlan> plansList, BigDecimal checkAmt) {
		//财务岗会编辑付款金额，就不用系统分配金额了
		if (plansList.stream().allMatch(item->Objects.isNull(item.getPayAmt()) || item.getPayAmt().compareTo(BigDecimal.ZERO) == 0)){
			BigDecimal finalCheckAmt = checkAmt;
			for (ContractVO.PayPlan item : plansList) {
				if (item.getPlanStatus().compareTo(BigDecimal.ZERO) == 0){
					if (finalCheckAmt.compareTo(item.getPlanPayAmt()) >= 0){
						item.setPayAmt(item.getPlanPayAmt());
						finalCheckAmt = finalCheckAmt.subtract(item.getPlanPayAmt());
					}
				}
			}
		}
	}

	private ContractVO collateContract(List<PcxBillContractRel> relList) {
		ContractVO contractVO = new ContractVO();
		PcxBillContractRel rel = relList.get(0);
		contractVO.setBillId(rel.getContractId());
		contractVO.setContractName(rel.getContractName());
		contractVO.setBillNo(rel.getContractNo());
		contractVO.setPartyAName(rel.getPartyAName());
		contractVO.setPartyBName(rel.getPartyBName());
		contractVO.setAmount(rel.getContractAmt());
		contractVO.setSignDate(rel.getSignDate());
		contractVO.setPlanMap(relList.stream().collect(Collectors.toMap(PcxBillContractRel::getPlanId, Function.identity(),(key1, key2)->key1)));
		return contractVO;
	}
}


