package com.pty.pcx.service.impl.wit.func;

import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.pty.rule.aviator.BaseVariadicFunction;

import java.util.Collection;
import java.util.Map;

/**
 * 判断list或字符串是否包含其中的静态常量
 * 
 * <AUTHOR>
 *
 */
public class FnContain2Function extends BaseVariadicFunction {
	public static final String FUNC_NAME = "contain2";

	@Override
	public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
		if (args.length == 0) {
			throw new IllegalArgumentException("argument cann't be empty");
		}
		if (args.length < 2) {
			throw new IllegalArgumentException("至少包含两个参数");
		}
		Object one  = args[0].getValue(env);
		if (one == null) {
			return AviatorBoolean.FALSE;
		}
		if (one instanceof String) {
			String oneS = (String)one;
			Object value = args[1].getValue(env);
			if (value instanceof Collection) {
				for (Object item : (Collection<?>) value) {
					if (item instanceof String && oneS.contains((String) item)) {
						return AviatorBoolean.valueOf(true);
					}
				}
			}
			if (value instanceof String) {
				for(int i = 1;i<args.length;i++) {
					if (oneS.contains(args[i].stringValue(env))) {
						return AviatorBoolean.valueOf(true);
					}
				}
			}
			return AviatorBoolean.valueOf(false);
		} else if (one instanceof Collection) {
			// 如果第一个参数是集合类型,判断集合是否包含
			Collection oneC = (Collection)one;
			for(int i = 1;i<args.length;i++) {
				if (oneC.contains(args[i].getValue(env))) {
					return AviatorBoolean.valueOf(true);
				}
			}
			// 如果集合的元素为字符串，判断每个稽核字符串是否包含
			for (Object item : oneC) {
				if (item instanceof String) {
					String oneS = (String)item;
					for(int i = 1;i<args.length;i++) {
						if (oneS.contains(args[i].stringValue(env))) {
							return AviatorBoolean.valueOf(true);
						}
					}
				}
			}
			return AviatorBoolean.valueOf(false);
		} else if (one.getClass().isArray()) {
			Object[] oneA = (Object[]) one;
			for(int i = 1;i<args.length;i++) {
				for (Object item : oneA) {
					if (item != null) {
						if (args[i].equals(item)) {
							return AviatorBoolean.TRUE;
						}
					}
				}
			}
			return AviatorBoolean.FALSE;
		} else {
			return AviatorBoolean.FALSE;
		}
	}

	@Override
	public String getName() {
		return FUNC_NAME;
	}

	@Override
	public String desc() {
		return "contain2(list, exp1, exp2......), 当list中包含exp1或exp2...expn时返回true, 否则返回false;例子:contain(list, 'a', 'b')";
	}
}
