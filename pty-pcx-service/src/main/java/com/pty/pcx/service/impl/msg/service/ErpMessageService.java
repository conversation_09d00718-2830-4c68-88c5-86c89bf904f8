package com.pty.pcx.service.impl.msg.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pcx.api.bill.PcxBillExpTravelService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.dao.setting.PcxPaOptionDao;
import com.pty.pcx.dao.transfer.PcxBillSyncDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpTravel;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.entity.setting.PaOption;
import com.pty.pcx.entity.transfer.PcxBillSync;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.msg.event.PcxMsgApprovalPrintEvent;
import com.pty.pcx.service.impl.msg.event.PcxMsgApprovalRollbackEvent;
import com.pty.pcx.service.impl.msg.event.PcxMsgApprovalToDoEvent;
import com.pty.pcx.service.impl.msg.event.PcxMsgErpPushFailEvent;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.api.proxy.TaskContext;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@Indexed
public class ErpMessageService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private PcxBillSyncDao pcxBillSyncDao;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${pty.message.erp.tenantId:bosssoft}")
    private String erpTenantId;

    @Value("${pty.message.erp.agyCode:gykj}")
    private String erpAgyCode;

    @Value("${pty.message.erp.empCode:1005021|1004975}")
    private String erpEmpCode;

    /**
     * ERP推送失败消息
     */
    public void erpPushFailMessage() {

        RLock lock = redissonClient.getLock("ERPPUSHFAIL_MESSAGE_LOCK");
        if (!lock.isLocked()){
            try {
                // 防止多实例重复发送
                lock.tryLock(0, 10, TimeUnit.MINUTES);
                // 发送消息
                sendPushFailMessage();
            } catch (InterruptedException e) {
                log.error("ERPPUSHFAIL_MESSAGE_LOCK异常:",e);
                throw new RuntimeException(e);
            }
        }
    }

    private void sendPushFailMessage(){
        try {
            List<PcxBillSync> pcxBillSyncs = pcxBillSyncDao.selectList(new LambdaQueryWrapper<PcxBillSync>().eq(PcxBillSync::getSyncStatus, 2));
            if (CollectionUtil.isEmpty(pcxBillSyncs)) {
                return;
            }

            Set<String> billSet = pcxBillSyncs.stream().map(PcxBillSync::getBillId).collect(Collectors.toSet());
            RSet<String> pushFailMsgSet = redissonClient.getSet("ErpPushFailMsgSet");
            billSet.removeAll(pushFailMsgSet);
            pcxBillSyncs.removeIf(pcxBillSync -> !billSet.contains(pcxBillSync.getBillId()));

            for (PcxBillSync pcxBillSync : pcxBillSyncs) {
                log.info("发送ERP推送失败消息");
                JSONObject jsonObject = new JSONObject()
                        .fluentPut("tenantId", erpTenantId)
                        .fluentPut("agyCode", erpAgyCode)
                        .fluentPut("mofDivCode", "87")
                        .fluentPut("modFuncCode", "PCX_ERP_PUSHFAIL")
                        .fluentPut("miniURL", String.format("/pages/wechat-work-sign/index?appId=%s", erpTenantId))
                        .fluentPut("receivers", erpEmpCode)
                        .fluentPut("billId", pcxBillSync.getBillId())
                        .fluentPut("bizType", pcxBillSync.getBizType())
                        .fluentPut("systemCode", pcxBillSync.getSystemCode())
                        .fluentPut("externalBillId", pcxBillSync.getExternalBillId())
                        .fluentPut("externalBillNo", pcxBillSync.getExternalBillNo())
                        .fluentPut("syncTime", DateUtil.formatTime(pcxBillSync.getSyncTime()))
                        .fluentPut("syncMessage", pcxBillSync.getSyncMessage())
                        .fluentPut("createTime", DateUtil.formatTime(pcxBillSync.getCreateTime()));
                PcxMsgErpPushFailEvent pcxMsgErpPushFailEvent = new PcxMsgErpPushFailEvent(this, jsonObject);
                eventPublisher.publishEvent(pcxMsgErpPushFailEvent);
            }

            pushFailMsgSet.addAll(billSet);
            pushFailMsgSet.expire(1, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("工作流消息发送异常:{}---{}---{}---{}",e);
        }
    }
}
