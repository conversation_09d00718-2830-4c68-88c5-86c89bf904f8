package com.pty.pcx.service.impl.bill.handler;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.vo.bill.PcxBillListVO;

import java.util.List;

public interface BillExpenseService<T extends PcxBillExpBase> {

    /**
     * 校验逻辑（校验专属属性和明细项）
     * @param expBase 校验的块信息
     * @return
     */
    CheckMsg<Void> validate(T expBase,String billFuncCode);


    /**
     * 暂存
     * @param t 费用信息
     * @param pcxBill 单据
     * @return
     */
    T saveOrUpdate(T t, PcxBill pcxBill);


    /**
     * 费用查看
     * @param expenseCode
     * @param pcxBill
     * @return
     */
    T view(String expenseCode,PcxBill pcxBill);

    /**
     * 删除
     * @param expenseCode
     * @param pcxBill
     * @return
     */
    void delete(String expenseCode,PcxBill pcxBill);

    /**
     * 处理首页回显内容
     * @param value
     */
    void dealContent(List<PcxBillListVO> value);
}
