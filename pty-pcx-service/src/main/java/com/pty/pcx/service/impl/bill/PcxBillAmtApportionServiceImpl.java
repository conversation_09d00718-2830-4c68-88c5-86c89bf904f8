package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillAmtApportionService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.ApportionTypeEnum;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dto.ecs.CommonCheckExpenseTypeAmtDTO;
import com.pty.pcx.dto.ecs.CommonCheckUpdateExpTypeDTO;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bill.apportion.*;
import com.pty.pcx.qo.ecs.common.QueryExpenseTypeAdditionQO;
import com.pty.pcx.service.impl.bud.PcxBudBalanceService;
import com.pty.pcx.service.impl.ecs.EcsExpCommonService;
import com.pty.pcx.service.impl.ecs.EcsExpTransService;
import com.pty.pcx.service.impl.ecs.trip.TravelTripProcessor;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.bill.apportion.*;
import com.pty.pcx.vo.ecs.BillTripNodeVO;
import com.pty.pcx.vo.ecs.BillTripVO;
import com.pty.pcx.vo.ecs.EcsRelVO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 报销单费用分摊(PcxBillAmtApportion)表服务实现类
 * <AUTHOR>
 * @since 2025-04-01 07:38:48
 */
@Slf4j
@Indexed
@Service
public class PcxBillAmtApportionServiceImpl implements PcxBillAmtApportionService {

	@Autowired
	private PcxBillAmtApportionDao pcxBillAmtApportionDao;
	@Autowired
	private PcxBillTravelTripDao pcxBillTravelTripDao;
	@Resource
	private PcxBillAmtApportionDepartmentDao pcxBillAmtApportionDepartmentDao;
	@Resource
	private PcxBillTripSegmentDao pcxBillTripSegmentDao;
	@Resource
	private PcxBasFormSettingService pcxBasFormSettingService;
	@Resource
	private EcsExpCommonService ecsExpCommonService;
	@Resource
	private PcxBillExpCommonDao pcxBillExpCommonDao;
	@Resource
	private PcxBillDao pcxBillDao;
	@Resource
	private EcsExpTransService ecsExpTransService;
	@Resource
	private PcxBasExpTypeDao pcxBasExpTypeDao;

	@Resource
	private PcxBudBalanceService pcxBudBalanceService;

	private static final BaseDataVo DEFAULT_BASE_DATA_VO = new BaseDataVo();
	static {
		DEFAULT_BASE_DATA_VO.setCode("");
		DEFAULT_BASE_DATA_VO.setName("");
	}
	public BaseDataVo queryEmpBudDepartment(PcxBillAmtApportionDepartment department) {
		if (StringUtil.isEmpty(department.getDepartmentCode())){
			return DEFAULT_BASE_DATA_VO;
		}
		PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
		pcxBalancesQO.setFiscal(department.getFiscal());
		pcxBalancesQO.setAgyCode(department.getAgyCode());
		pcxBalancesQO.setMofDivCode(department.getMofDivCode());
		pcxBalancesQO.setDepartmentCode(department.getDepartmentCode());
		return pcxBudBalanceService.getDeFaultDepartment(pcxBalancesQO);
	}


	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxBillAmtApportion selectById(String id) {
		return pcxBillAmtApportionDao.selectById(id);
	}

	@Override
	public CheckMsg queryTravelAmtApportionList(ApportionBaseQO qo) {
		TravelAmtApportionListVO vo = new TravelAmtApportionListVO();

		//查询分摊列表，差旅的分摊，每个分摊只有一个部门，分摊部门子表的数据直接挂到分摊主表的vo上就行
		List<AmtApportionVO> amtApportionList = assemblyApportionList(qo.getBillId());
		disposeTravelApportionList(amtApportionList);

		List<PcxBillTravelTrip> tripList = pcxBillTravelTripDao.selectList(Wrappers.lambdaQuery(PcxBillTravelTrip.class)
				.eq(PcxBillTravelTrip::getBillId, qo.getBillId()));
		if (CollectionUtils.isNotEmpty(tripList)){
			//行程分段上有分摊id，收集每个分摊关联的行程分段id集合
			List<PcxBillTripSegment> tripSegments = queryBillTripSegmentList(qo.getBillId());
			fillApportionSegmentIds(amtApportionList, tripSegments);
			//整理行程数据
			List<TripVO> tripVoList = assembleTripList(tripList, tripSegments);
			vo.setTripList(tripVoList);
		}

		List<String> deptList = amtApportionList
				.stream()
				.map(AmtApportionVO::getDepartmentName)
				.distinct()
				.collect(Collectors.toList());
		vo.setApportionDeptList(deptList);
		vo.setApportionList(amtApportionList);
		vo.setBlockPropertyList(pcxBasFormSettingService.getTravelExpenseTypeAdditionField(qo));
		return CheckMsg.success(vo);
	}

	private void disposeTravelApportionList(List<AmtApportionVO> amtApportionList) {
		for (AmtApportionVO vo : amtApportionList) {
			vo.setDeptList(null);
		}
	}

	private List<AmtApportionVO> assemblyApportionList(String billId) {
		List<PcxBillAmtApportion> amtApportions = queryBillAmtApportionList(billId);
		List<PcxBillAmtApportionDepartment> amtApportionDepartments = queryBillAmtApportionDepartmentList(billId);

		List<AmtApportionVO> amtApportionList = assembleAmtApportionList(amtApportions, amtApportionDepartments);

		if (CollectionUtils.isEmpty(amtApportions)){
			return Lists.newArrayList();
		}

		return amtApportionList;
    }

	private List<PcxBillAmtApportion> queryBillAmtApportionList(String billId) {
		return pcxBillAmtApportionDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
				.eq(PcxBillAmtApportion::getBillId, billId));
	}

	private List<PcxBillAmtApportionDepartment> queryBillAmtApportionDepartmentList(String billId) {
		return pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.eq(PcxBillAmtApportionDepartment::getBillId, billId));
	}

	private List<PcxBillTripSegment> queryBillTripSegmentList(String billId) {
		return pcxBillTripSegmentDao.selectList(Wrappers.lambdaQuery(PcxBillTripSegment.class)
				.eq(PcxBillTripSegment::getBillId, billId));
	}


	@Override
	public CheckMsg updateTravelAmtApportion(TravelUpdateApportionQO qo, PcxBill bill) {
		log.info("updateTravelAmtApportion:{}", qo);
		List<PcxBillTravelTrip> tripList = pcxBillTravelTripDao.selectList(Wrappers.lambdaQuery(PcxBillTravelTrip.class)
				.eq(PcxBillTravelTrip::getBillId, qo.getBillId()));
		//如果有行程则要校验行程分段
		if (CollectionUtils.isNotEmpty(tripList)){
			return updateTravelApportionListForTrip(qo, bill);
		}

		//查询出行程分段数据
		//组装分摊列表，把金额汇总加一下
		//组装分摊部门列表
		List<PcxBillAmtApportion> apportionList = new ArrayList<>();
		List<UpdateApportionQO> apportionQOList = qo.getApportionList();
		List<PcxBillAmtApportionDepartment> amtApportionDepartmentList = new ArrayList<>();
		UpdateApportionQO updateApportionQO = apportionQOList.get(0);
		PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
		BeanUtils.copyProperties(updateApportionQO, amtApportion);
		amtApportion.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
		amtApportion.setExpenseTypeName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
		if (StringUtil.isEmpty(amtApportion.getId())) {
			amtApportion.setId(IDGenerator.id());
		}
		amtApportion.setSeq(1);
		amtApportion.setApportionAmt(bill.getInputAmt());
		amtApportion.setInputAmt(bill.getInputAmt());
		amtApportion.setBillId(qo.getBillId());
		amtApportion.setFiscal(qo.getFiscal());
		amtApportion.setAgyCode(qo.getAgyCode());
		amtApportion.setMofDivCode(qo.getMofDivCode());
		apportionList.add(amtApportion);


		PcxBillAmtApportionDepartment ad = new PcxBillAmtApportionDepartment();
		BeanUtils.copyProperties(updateApportionQO, ad);
		ad.setId(IDGenerator.id());
		ad.setApportionId(amtApportion.getId());
		ad.setBillId(qo.getBillId());
		ad.setFiscal(qo.getFiscal());
		ad.setAgyCode(qo.getAgyCode());
		ad.setMofDivCode(qo.getMofDivCode());
		ad.setDepartmentAmt(amtApportion.getApportionAmt());
		ad.setInputAmt(amtApportion.getApportionAmt());
		ad.setDepartmentRate(new BigDecimal(100));
		ad.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
		ad.setExpenseTypeName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
		BaseDataVo baseDataVo = queryEmpBudDepartment(ad);
		if (Objects.nonNull(baseDataVo)){
			ad.setBudDepartmentCode(baseDataVo.getCode());
			ad.setBudDepartmentName(baseDataVo.getName());
		}
		amtApportionDepartmentList.add(ad);
		ecsExpTransService.updateTravelAmtApportion(apportionList, amtApportionDepartmentList, Lists.newArrayList(), bill, false);
		return queryTravelAmtApportionList(qo);
	}

	private CheckMsg updateTravelApportionListForTrip(TravelUpdateApportionQO qo, PcxBill bill) {
		CheckMsg checkMsg = validDepartAndSegment(qo);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		List<PcxBillTripSegment> tripSegments = queryBillTripSegmentList(qo.getBillId());
		Set<String> segmentIds = (Set<String>) checkMsg.getData();
		checkMsg = validSegment(segmentIds, tripSegments);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		//查询出行程分段数据
		//组装分摊列表，把金额汇总加一下
		//组装分摊部门列表
		List<PcxBillAmtApportion> apportionList = new ArrayList<>();

		Map<String, PcxBillTripSegment> segmentAmtMap = tripSegments
				.stream()
				.collect(Collectors.toMap(PcxBillTripSegment::getId, Function.identity(), (key1, key2) -> key1));
		List<UpdateApportionQO> apportionQOList = qo.getApportionList();
		int seq = 1;
		List<PcxBillAmtApportionDepartment> amtApportionDepartmentList = new ArrayList<>();

		for (UpdateApportionQO updateApportionQO : apportionQOList) {
			List<String> segmentIdList = updateApportionQO.getSegmentIds();
			PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
			BeanUtils.copyProperties(updateApportionQO, amtApportion);
			amtApportion.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
			amtApportion.setExpenseTypeName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
			if (StringUtil.isEmpty(amtApportion.getId())){
				amtApportion.setId(IDGenerator.id());
			}
			BigDecimal amt = BigDecimal.ZERO;
			for (String sid : segmentIdList) {
				PcxBillTripSegment pcxBillTripSegment = segmentAmtMap.get(sid);
				pcxBillTripSegment.setApportionId(amtApportion.getId());
				amt = amt.add(pcxBillTripSegment.getInputAmt());
			}

			amtApportion.setSeq(seq++);
			amtApportion.setApportionAmt(amt);
			amtApportion.setInputAmt(amt);
			amtApportion.setBillId(qo.getBillId());
			amtApportion.setFiscal(qo.getFiscal());
			amtApportion.setAgyCode(qo.getAgyCode());
			amtApportion.setMofDivCode(qo.getMofDivCode());
			apportionList.add(amtApportion);

			PcxBillAmtApportionDepartment ad = new PcxBillAmtApportionDepartment();
			BeanUtils.copyProperties(updateApportionQO, ad);
			ad.setId(IDGenerator.id());
			ad.setApportionId(amtApportion.getId());
			ad.setBillId(qo.getBillId());
			ad.setFiscal(qo.getFiscal());
			ad.setAgyCode(qo.getAgyCode());
			ad.setMofDivCode(qo.getMofDivCode());
			ad.setDepartmentAmt(amtApportion.getApportionAmt());
			ad.setInputAmt(amtApportion.getApportionAmt());
			ad.setDepartmentRate(new BigDecimal(100));
			ad.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
			ad.setExpenseTypeName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
			BaseDataVo baseDataVo = queryEmpBudDepartment(ad);
			if (Objects.nonNull(baseDataVo)){
				ad.setBudDepartmentCode(baseDataVo.getCode());
				ad.setBudDepartmentName(baseDataVo.getName());
			}
			amtApportionDepartmentList.add(ad);
		}
		ecsExpTransService.updateTravelAmtApportion(apportionList, amtApportionDepartmentList, tripSegments, bill, false);
		return queryTravelAmtApportionList(qo);
	}

	private PcxBill selectBillById(String billId) {
		PcxBill bill = pcxBillDao.selectById(billId);
		if (Objects.isNull(bill)){
			throw new RuntimeException("找不到对应的单据");
		}
		return bill;
	}
	@Override
	public CheckMsg queryCommonAmtApportionList(ApportionBaseQO qo) {
		PcxBill bill = selectBillById(qo.getBillId());

		CommonAmtApportionListVO vo = new CommonAmtApportionListVO();
		List<AmtApportionVO> amtApportionList = assemblyApportionList(qo.getBillId());
		//整单分摊的分摊逻辑会把所有费用根据整单分摊的部门分摊比例拆分，回显的时候要汇总一下
		if (Objects.equals(bill.getApportionType(), ApportionTypeEnum.BILL.getCode())){
			for (AmtApportionVO amtApportionVO : amtApportionList) {
				List<AmtApportionDeptVO> result = new ArrayList<>();
				List<AmtApportionDeptVO> deptList = amtApportionVO.getDeptList();
				Map<String, List<AmtApportionDeptVO>> collect = deptList.stream().collect(Collectors.groupingBy(AmtApportionDeptVO::getDepartmentCode));
				for (Map.Entry<String, List<AmtApportionDeptVO>> entry : collect.entrySet()) {
					List<AmtApportionDeptVO> list = entry.getValue();
					AmtApportionDeptVO amtApportionDeptVO = list.get(0);
					BigDecimal totalAmt = list.stream().map(AmtApportionDeptVO::getDepartmentAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
					amtApportionDeptVO.setDepartmentAmt(totalAmt);
					result.add(amtApportionDeptVO);
				}
				amtApportionVO.setDeptList(result);
			}
		}
		//通用和差旅返回的分摊列表用的是一个vo，把差旅的相关字段置空，不用返回给前端
		disposeCommonApportionList(amtApportionList);
		Map<String, List<BlockPropertyVO>> blockPeropertyMap = new HashMap<>();
		List<String> expenseTypeCode = amtApportionList.stream().map(AmtApportionVO::getExpenseTypeCode).distinct().collect(Collectors.toList());
		for (String s : expenseTypeCode) {
			if (Objects.equals(s, PcxConstant.UNIVERSAL_EXPENSE_CODE)){
				blockPeropertyMap.put(s, pcxBasFormSettingService.getDefaultExpenseTypeAdditionField());
				continue;
			}
			QueryExpenseTypeAdditionQO typeAdditionQO = new QueryExpenseTypeAdditionQO();
			typeAdditionQO.setFiscal(qo.getFiscal());
			typeAdditionQO.setMofDivCode(qo.getMofDivCode());
			typeAdditionQO.setAgyCode(qo.getAgyCode());
			typeAdditionQO.setExpenseTypeCode(s);
			List<BlockPropertyVO> blockPropertyVOS = pcxBasFormSettingService.collectExpenseTypeAdditionField(typeAdditionQO);
			blockPeropertyMap.put(s, blockPropertyVOS);
		}
		List<String> deptList = amtApportionList
				.stream()
				.flatMap(amtApportionVO -> amtApportionVO.getDeptList().stream())
				.map(AmtApportionDeptVO::getDepartmentName)
				.distinct()
				.collect(Collectors.toList());
		vo.setApportionDeptList(deptList);
		vo.setApportionList(amtApportionList);
		vo.setBlockPropertyMap(blockPeropertyMap);
		vo.setApportionType(bill.getApportionType());
		return CheckMsg.success(vo);
	}

	private void disposeCommonApportionList(List<AmtApportionVO> amtApportionList) {
		for (AmtApportionVO vo : amtApportionList) {
			vo.setSegmentIds(null);
			vo.setTripSegmentNum(null);
			vo.setAcitem01Code(null);
			vo.setAcitem01Name(null);
			vo.setAcitem02Code(null);
			vo.setAcitem02Name(null);
			vo.setAcitem03Code(null);
			vo.setAcitem03Name(null);
			vo.setAcitem04Code(null);
			vo.setAcitem04Name(null);
			vo.setAcitem05Code(null);
			vo.setAcitem05Name(null);
			vo.setAcitem06Code(null);
			vo.setAcitem06Name(null);
			vo.setAcitem07Code(null);
			vo.setAcitem07Name(null);
			vo.setAcitem08Code(null);
			vo.setAcitem08Name(null);
			vo.setAcitem09Code(null);
			vo.setAcitem09Name(null);
			vo.setAcitem10Code(null);
			vo.setAcitem10Name(null);
			vo.setDepartmentRate(null);
			vo.setDepartmentAmt(null);
			vo.setDepartmentCode(null);
			vo.setDepartmentName(null);
		}
	}

	@Override
	public CheckMsg updateCommonAmtApportion(CommonUpdateApportionQO qo, PcxBill pcxBill) {
		log.info("updateCommonAmtApportion:{}", qo);
		Integer apportionType = pcxBill.getApportionType();
		boolean billApportion = Objects.equals(ApportionTypeEnum.BILL.getCode(), apportionType);
		List<PcxBillAmtApportionDepartment> amtApportionDepartmentList = new ArrayList<>();
		List<PcxBillAmtApportion> apportionList = new ArrayList<>();
		CheckMsg checkMsg = validUpdateCommonAmtApportion(qo);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		if (Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.COMMON.getCode())){
			if (billApportion){
				commonBillApportion(pcxBill, qo, amtApportionDepartmentList, apportionList, null);
			}else{
				commonExpenseApportion(pcxBill, qo, amtApportionDepartmentList, apportionList);
			}
		}else{
			otherExpenseApportion(pcxBill, qo, amtApportionDepartmentList, apportionList);
		}

		ecsExpTransService.updateTravelAmtApportion(apportionList, amtApportionDepartmentList, Lists.newArrayList(), pcxBill, false);
		return queryCommonAmtApportionList(qo);
	}

	private void otherExpenseApportion(PcxBill pcxBill,
									   CommonUpdateApportionQO qo,
									   List<PcxBillAmtApportionDepartment> amtApportionDepartmentList,
									   List<PcxBillAmtApportion> apportionList) {
		List<PcxBillAmtApportion> apportions = pcxBillAmtApportionDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
				.eq(PcxBillAmtApportion::getBillId, pcxBill.getId()));
		String expenseTypeCode = pcxBill.getExpenseCodes();
		String expenseTypeName = pcxBill.getExpenseNames();
		if (CollectionUtils.isNotEmpty(apportions)){
			PcxBillAmtApportion amtApportion = apportions.get(0);
			expenseTypeCode = amtApportion.getExpenseTypeCode();
			expenseTypeName = amtApportion.getExpenseTypeName();
		}
		for (CommonApportionQO commonApportionQO : qo.getApportionList()) {
			PcxBillAmtApportion pcxBillAmtApportion = new PcxBillAmtApportion();
			pcxBillAmtApportion.setId(IDGenerator.id());
			pcxBillAmtApportion.setBillId(pcxBill.getId());
			pcxBillAmtApportion.setFiscal(pcxBill.getFiscal());
			pcxBillAmtApportion.setAgyCode(pcxBill.getAgyCode());
			pcxBillAmtApportion.setMofDivCode(pcxBill.getMofDivCode());
			pcxBillAmtApportion.setInputAmt(commonApportionQO.getApportionAmt());
			pcxBillAmtApportion.setApportionAmt(commonApportionQO.getApportionAmt());
			pcxBillAmtApportion.setExpenseTypeCode(expenseTypeCode);
			pcxBillAmtApportion.setExpenseTypeName(expenseTypeName);
			apportionList.add(pcxBillAmtApportion);
			for (CommonApportionDeptQO commonApportionDeptQO : commonApportionQO.getDeptList()) {
				PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment = new PcxBillAmtApportionDepartment();
				BeanUtils.copyProperties(commonApportionDeptQO, pcxBillAmtApportionDepartment);
				pcxBillAmtApportionDepartment.setId(IDGenerator.id());
				pcxBillAmtApportionDepartment.setBillId(pcxBill.getId());
				pcxBillAmtApportionDepartment.setApportionId(pcxBillAmtApportion.getId());
				pcxBillAmtApportionDepartment.setDepartmentCode(commonApportionDeptQO.getDepartmentCode());
				pcxBillAmtApportionDepartment.setDepartmentName(commonApportionDeptQO.getDepartmentName());
				pcxBillAmtApportionDepartment.setDepartmentRate(commonApportionDeptQO.getDepartmentRate());
				pcxBillAmtApportionDepartment.setDepartmentAmt(commonApportionDeptQO.getDepartmentAmt());
				pcxBillAmtApportionDepartment.setInputAmt(commonApportionDeptQO.getDepartmentAmt());
				pcxBillAmtApportionDepartment.setExpenseTypeCode(expenseTypeCode);
				pcxBillAmtApportionDepartment.setExpenseTypeName(expenseTypeName);
				pcxBillAmtApportionDepartment.setFiscal(pcxBill.getFiscal());
				pcxBillAmtApportionDepartment.setAgyCode(pcxBill.getAgyCode());
				pcxBillAmtApportionDepartment.setMofDivCode(pcxBill.getMofDivCode());
				amtApportionDepartmentList.add(pcxBillAmtApportionDepartment);
			}
		}
	}

	private CheckMsg validUpdateCommonAmtApportion(CommonUpdateApportionQO qo) {
		for (CommonApportionQO commonApportionQO : qo.getApportionList()) {
			BigDecimal totalAmt = BigDecimal.ZERO;
			for (CommonApportionDeptQO commonApportionDeptQO : commonApportionQO.getDeptList()) {
				if (commonApportionDeptQO.getDepartmentRate().compareTo(BigDecimal.ZERO) <=0){
					return CheckMsg.fail("分摊部门比例不能小于等于0");
				}
				totalAmt = totalAmt.add(commonApportionDeptQO.getDepartmentAmt());
			}
			if (totalAmt.compareTo(commonApportionQO.getApportionAmt()) != 0){
				return CheckMsg.fail("分摊部门金额总和必须等于分摊总金额");
			}
		}
		return CheckMsg.success();
	}

	@Override
	public void commonBillApportion(PcxBill pcxBill,
									CommonUpdateApportionQO qo,
									List<PcxBillAmtApportionDepartment> amtApportionDepartmentList,
									List<PcxBillAmtApportion> apportionList,
									List<PcxBillExpCommon> pcxBillExpCommons) {
		if (CollectionUtils.isEmpty(pcxBillExpCommons)){
			pcxBillExpCommons = queryCommonExp(pcxBill);
		}
		pcxBillExpCommons = pcxBillExpCommons.stream().filter(item->item.getCheckAmt().compareTo(BigDecimal.ZERO)>0).collect(Collectors.toList());
		Map<String, CommonAmtTemp> expenseTypeAmtMap = new HashMap<>();
		for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
			String expenseTypeKey = getExpenseTypeKey(pcxBillExpCommon);
			CommonAmtTemp commonAmtTemp = expenseTypeAmtMap.computeIfAbsent(expenseTypeKey, key -> new CommonAmtTemp(expenseTypeKey, BigDecimal.ZERO, BigDecimal.ZERO));
			commonAmtTemp.addAmt(pcxBillExpCommon.getCheckAmt());
		}

		CommonApportionQO commonApportionQO = qo.getApportionList().get(0);
		List<CommonApportionDeptQO> deptList = commonApportionQO.getDeptList();
		PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
		amtApportion.setBillId(qo.getBillId());
		amtApportion.setId(IDGenerator.id());
		amtApportion.setFiscal(qo.getFiscal());
		amtApportion.setAgyCode(qo.getAgyCode());
		amtApportion.setMofDivCode(qo.getMofDivCode());
		amtApportion.setApportionAmt(commonApportionQO.getApportionAmt());
		amtApportion.setInputAmt(commonApportionQO.getApportionAmt());
		amtApportion.setExpenseTypeCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
		amtApportion.setExpenseTypeName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
		amtApportion.setTenantId(StringUtil.isEmpty(pcxBill.getTenantId()) ? PtyContext.getTenantId() : pcxBill.getTenantId());
		amtApportion.setSeq(1);
		apportionList.add(amtApportion);
		for (int i = 0; i < deptList.size(); i++) {
			boolean last = i == deptList.size() - 1;
			CommonApportionDeptQO commonApportionDeptQO = deptList.get(i);
			collectExpApportionDeptList(amtApportionDepartmentList, amtApportion, commonApportionDeptQO, last, expenseTypeAmtMap);
		}
	}

	@AllArgsConstructor
	@Data
	private static class CommonAmtTemp{
		private String expenseTypeKey;
		private BigDecimal totalAmt;
		private BigDecimal lastAmt;
		public CommonAmtTemp of(String expenseTypeKey, BigDecimal totalAmt, BigDecimal lastAmt){
			return new CommonAmtTemp(expenseTypeKey, totalAmt, lastAmt);
		}

		public void addAmt(BigDecimal amt){
			this.totalAmt = this.totalAmt.add(amt);
			this.lastAmt = this.lastAmt.add(amt);
		}

		public void minLast(BigDecimal amt){
			this.lastAmt = this.lastAmt.subtract(amt);
		}
	}

	private void collectExpApportionDeptList(List<PcxBillAmtApportionDepartment> amtApportionDepartmentList,
											 PcxBillAmtApportion amtApportion,
											 CommonApportionDeptQO commonApportionDeptQO,
											 boolean last,
											 Map<String, CommonAmtTemp> expenseTypeAmtMap) {
		int expenseSize = expenseTypeAmtMap.keySet().size();
		BigDecimal departmentAmt = commonApportionDeptQO.getDepartmentAmt();
		for (Map.Entry<String, CommonAmtTemp> entry : expenseTypeAmtMap.entrySet()) {
			expenseSize--;
			String[] split = entry.getKey().split(expenseTypeSpecialChar);
			PcxBillAmtApportionDepartment amtApportionDepartment = new PcxBillAmtApportionDepartment();
			BeanUtils.copyProperties(commonApportionDeptQO, amtApportionDepartment);
			amtApportionDepartment.setId(IDGenerator.id());
			amtApportionDepartment.setApportionId(amtApportion.getId());
			amtApportionDepartment.setDepartmentRate(commonApportionDeptQO.getDepartmentRate());
			amtApportionDepartment.setFiscal(amtApportion.getFiscal());
			amtApportionDepartment.setAgyCode(amtApportion.getAgyCode());
			amtApportionDepartment.setMofDivCode(amtApportion.getMofDivCode());
			amtApportionDepartment.setBillId(amtApportion.getBillId());
			if (last) {
				amtApportionDepartment.setDepartmentAmt(entry.getValue().getLastAmt());
				amtApportionDepartment.setInputAmt(entry.getValue().getLastAmt());
			} else {
				BigDecimal amt = BigDecimal.ZERO;
				if (expenseSize == 0){
					amt = departmentAmt;
				}else{
					amt = calculateRateAmt(entry.getValue().getTotalAmt(), commonApportionDeptQO.getDepartmentRate());
					departmentAmt = departmentAmt.subtract(amt);
				}
				amtApportionDepartment.setDepartmentAmt(amt);
				amtApportionDepartment.setInputAmt(amt);
				entry.getValue().minLast(amt);
			}
			amtApportionDepartment.setExpenseTypeCode(split[0]);
			amtApportionDepartment.setExpenseTypeName(split[1]);
			BaseDataVo baseDataVo = queryEmpBudDepartment(amtApportionDepartment);
			if (Objects.nonNull(baseDataVo)){
				amtApportionDepartment.setBudDepartmentCode(baseDataVo.getCode());
				amtApportionDepartment.setBudDepartmentName(baseDataVo.getName());
			}
			amtApportionDepartmentList.add(amtApportionDepartment);
		}
	}

	private BigDecimal calculateRateAmt(BigDecimal totalAmt, BigDecimal departmentRate) {
		return  totalAmt.multiply(departmentRate.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
	}

	private final String expenseTypeSpecialChar = "##";

	private String getExpenseTypeKey(PcxBillExpCommon common){
		return String.format("%s"+expenseTypeSpecialChar+"%s", common.getExpenseCode(), common.getExpenseName());
	}

	private List<PcxBillExpCommon> queryCommonExp(PcxBill pcxBill){
		return pcxBillExpCommonDao.selectList(Wrappers.lambdaQuery(PcxBillExpCommon.class)
				.eq(PcxBillExpCommon::getBillId, pcxBill.getId()));
	}
	private void commonExpenseApportion(PcxBill pcxBill, CommonUpdateApportionQO qo, List<PcxBillAmtApportionDepartment> amtApportionDepartmentList, List<PcxBillAmtApportion> apportionList) {
		int seq = 1;

		for (CommonApportionQO updateApportionQO : qo.getApportionList()) {
			PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
			BeanUtils.copyProperties(updateApportionQO, amtApportion);
			if (Objects.isNull(amtApportion.getId())){
				amtApportion.setId(IDGenerator.id());
			}

			amtApportion.setSeq(seq++);
			amtApportion.setBillId(qo.getBillId());
			amtApportion.setFiscal(qo.getFiscal());
			amtApportion.setAgyCode(qo.getAgyCode());
			amtApportion.setMofDivCode(qo.getMofDivCode());
			amtApportion.setInputAmt(updateApportionQO.getApportionAmt());
			apportionList.add(amtApportion);

			List<CommonApportionDeptQO> deptList = updateApportionQO.getDeptList();

			for (CommonApportionDeptQO updateApportionDeptQO : deptList) {
				PcxBillAmtApportionDepartment ad = new PcxBillAmtApportionDepartment();
				BeanUtils.copyProperties(updateApportionDeptQO, ad);
				ad.setApportionId(amtApportion.getId());
				ad.setBillId(qo.getBillId());
				ad.setFiscal(qo.getFiscal());
				ad.setAgyCode(qo.getAgyCode());
				ad.setMofDivCode(qo.getMofDivCode());
				ad.setExpenseTypeCode(amtApportion.getExpenseTypeCode());
				ad.setExpenseTypeName(amtApportion.getExpenseTypeName());
				ad.setInputAmt(updateApportionDeptQO.getDepartmentAmt());
				BaseDataVo baseDataVo = queryEmpBudDepartment(ad);
				if (Objects.nonNull(baseDataVo)){
					ad.setBudDepartmentCode(baseDataVo.getCode());
					ad.setBudDepartmentName(baseDataVo.getName());
				}
				amtApportionDepartmentList.add(ad);
			}
		}
	}

	@Override
	public CheckMsg switchApportionType(SwitchApportionTypeQO qo, PcxBill pcxBill) {
		ApportionTypeEnum apportionTypeEnum = ApportionTypeEnum.getByCode(qo.getApportionType());
		if (Objects.isNull(apportionTypeEnum)){
			return CheckMsg.fail("分摊类型不匹配");
		}
		if (Objects.equals(pcxBill.getApportionType(), qo.getApportionType())){
			return CheckMsg.success("已经是" + apportionTypeEnum.getName() + " 类型");
		}

        if (apportionTypeEnum == ApportionTypeEnum.BILL) {
            return toBillApportion(qo, pcxBill);
        }
        return toExpenseApportion(qo, pcxBill);
    }




	@Override
	public CheckMsg queryTravelApportionDetail(QueryTravelApportionDetailQO qo, PcxBill pcxBill) {
		TravelApportionDetailVO vo = new TravelApportionDetailVO();
		vo.setDepartmentCode(qo.getDepartmentCode());
		List<PcxBillAmtApportionDepartment> amtApportionDepartments = pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.eq(PcxBillAmtApportionDepartment::getBillId, qo.getBillId())
				.eq(PcxBillAmtApportionDepartment::getDepartmentCode, qo.getDepartmentCode()));
		if (CollectionUtils.isEmpty(amtApportionDepartments)){
			return CheckMsg.fail("未查询到补充信息，无需补充");
		}
		PcxBillAmtApportionDepartment amtApportionDepartment = amtApportionDepartments.get(0);
		vo.setDepartmentName(amtApportionDepartment.getDepartmentName());
		vo.setExpenseTypeCode(amtApportionDepartment.getExpenseTypeCode());
		vo.setExpenseTypeName(amtApportionDepartment.getExpenseTypeName());
		List<AmtApportionDeptVO> tripList = new ArrayList<>();
		vo.setTripList(tripList);
		for (PcxBillAmtApportionDepartment apportionDepartment : amtApportionDepartments) {
			AmtApportionDeptVO deptVO = new AmtApportionDeptVO();
			BeanUtils.copyProperties(apportionDepartment, deptVO);
			List<PcxBillTripSegment> tripSegments = pcxBillTripSegmentDao.selectList(Wrappers.lambdaQuery(PcxBillTripSegment.class)
					.eq(PcxBillTripSegment::getBillId, pcxBill.getId())
					.eq(PcxBillTripSegment::getApportionId, apportionDepartment.getApportionId()));
			deptVO.setTripSegment(tripSegments.stream().map(PcxBillTripSegment::getCityName).collect(Collectors.joining(",")));
			tripList.add(deptVO);
		}
		List<BlockPropertyVO> blockPropertyVOS = pcxBasFormSettingService.getTravelExpenseTypeAdditionField(qo);
		vo.setBlockPropertyList(blockPropertyVOS);
		return CheckMsg.success(vo);
	}

	@Override
	public CheckMsg updateTravelApportionDetail(UpdateTravelApportionDetailQO qo, PcxBill pcxBill) {
		List<PcxBillAmtApportionDepartment> list = new ArrayList<>();
		for (AmtApportionDeptVO amtApportionDeptVO : qo.getTripList()) {
			PcxBillAmtApportionDepartment amtApportionDepartment = new PcxBillAmtApportionDepartment();
			BeanUtils.copyProperties(amtApportionDeptVO, amtApportionDepartment);
			list.add(amtApportionDepartment);
		}
		ecsExpTransService.updateTravelApportionDetail(list);
		return CheckMsg.success("ok");
	}

	@Override
	public CheckMsg queryCommonApportionDetail(QueryCommonApportionDetailQO qo, PcxBill pcxBill) {
		CommonApportionDetailVO vo = new CommonApportionDetailVO();
		List<PcxBillAmtApportionDepartment> departments = pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
				.eq(PcxBillAmtApportionDepartment::getBillId, qo.getBillId())
				.eq(PcxBillAmtApportionDepartment::getDepartmentCode, qo.getDepartmentCode())
				.eq(PcxBillAmtApportionDepartment::getExpenseTypeCode, qo.getExpenseTypeCode()));
		if (CollectionUtils.isEmpty(departments)){
			return CheckMsg.fail("未查询到补充信息，无需补充");
		}
		PcxBillAmtApportionDepartment amtApportionDepartment = departments.get(0);
		BeanUtils.copyProperties(amtApportionDepartment, vo);
		QueryExpenseTypeAdditionQO typeAdditionQO = new QueryExpenseTypeAdditionQO();
		typeAdditionQO.setFiscal(qo.getFiscal());
		typeAdditionQO.setMofDivCode(qo.getMofDivCode());
		typeAdditionQO.setAgyCode(qo.getAgyCode());
		typeAdditionQO.setExpenseTypeCode(qo.getExpenseTypeCode());
		List<BlockPropertyVO> blockPropertyVOS = pcxBasFormSettingService.collectExpenseTypeAdditionField(typeAdditionQO);
		vo.setBlockPropertyList(blockPropertyVOS);
		return CheckMsg.success(vo);
	}

	@Override
	public CheckMsg updateCommonApportionDetail(UpdateCommonApportionDetailQO qo, PcxBill pcxBill) {
		PcxBillAmtApportionDepartment update = new PcxBillAmtApportionDepartment();
		BeanUtils.copyProperties(qo, update);
		pcxBillAmtApportionDepartmentDao.updateById(update);
		return CheckMsg.success("ok");
	}

	@Override
	@Deprecated
	public void reCollectApportionForUpdateEcsExpType(PcxBill bill,
													  List<PcxBillAmtApportion> amtApportions,
													  List<PcxBillAmtApportionDepartment> amtApportionDepartments,
													  List<CommonCheckUpdateExpTypeDTO> changeExpTypeCodeOrCheckAmtList) {

		List<String> targetTypeCodes = changeExpTypeCodeOrCheckAmtList
				.stream()
				.map(CommonCheckUpdateExpTypeDTO::getTargetTypeCode)
				.distinct()
				.collect(Collectors.toList());
		PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
		expTypeQO.setFiscal(bill.getFiscal());
		expTypeQO.setAgyCode(bill.getAgyCode());
		expTypeQO.setMofDivCode(bill.getMofDivCode());
		expTypeQO.setExpTypeCodes(targetTypeCodes);
		List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectSimpleList(expTypeQO);
		Map<String, String> expTypeNameMap = expTypeList.stream()
				.collect(Collectors.toMap(PcxBasExpType::getExpenseCode,
						PcxBasExpType::getExpenseName, (key1, key2) -> key1));

		List<PcxBillAmtApportion> dbAmtApportions = queryBillAmtApportionList(bill.getId());
		List<PcxBillAmtApportionDepartment> dbApportionDepartments = queryBillAmtApportionDepartmentList(bill.getId());
		Map<String, List<PcxBillAmtApportionDepartment>> expDeptMap = dbApportionDepartments
				.stream().collect(Collectors.groupingBy(PcxBillAmtApportionDepartment::getExpenseTypeCode));
		for (CommonCheckUpdateExpTypeDTO changeDTO : changeExpTypeCodeOrCheckAmtList) {
			//找到原费用类型部门分摊，按比例把原核定金额剪掉
			String sourceTypeCode = changeDTO.getSourceTypeCode();
			String targetTypeCode = changeDTO.getTargetTypeCode();
			BigDecimal sourceCheckAmt = changeDTO.getSourceCheckAmt();
			BigDecimal targetCheckAmt = changeDTO.getTargetCheckAmt();
			List<PcxBillAmtApportionDepartment> sourceTypeDeptList = expDeptMap.get(sourceTypeCode);
			minusSourceCheckAmt(sourceTypeDeptList, sourceCheckAmt);
			//找到新费用类型部门分摊，按比例把新核定金额加上
			List<PcxBillAmtApportionDepartment> targetTypeDeptList = expDeptMap.get(targetTypeCode);
			plusTargetCheckAmt(targetTypeDeptList, targetCheckAmt, sourceTypeDeptList, dbAmtApportions, bill.getApportionType(), expDeptMap, targetTypeCode, expTypeNameMap.get(targetTypeCode));
		}
		//把部门分摊金额大于0的过滤出来
		List<PcxBillAmtApportionDepartment> collect = expDeptMap.values().stream().flatMap(Collection::stream).filter(item -> item.getDepartmentAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
		//根据分摊主表id分组，求出每个分摊主表id的部门分摊金额之和
		Map<String, BigDecimal> apportionAmtMap = collect
				.stream()
				.collect(Collectors.groupingBy(PcxBillAmtApportionDepartment::getApportionId,
						Collectors.mapping(PcxBillAmtApportionDepartment::getDepartmentAmt,
								Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
		//更新分摊主表的分摊金额，分摊金额小于等于0的过滤掉
		Iterator<PcxBillAmtApportion> iterator = dbAmtApportions.iterator();
		while (iterator.hasNext()){
			PcxBillAmtApportion next = iterator.next();
			BigDecimal apportionAmt = apportionAmtMap.getOrDefault(next.getId(), BigDecimal.ZERO);
			next.setApportionAmt(apportionAmt);
			if (apportionAmt.compareTo(BigDecimal.ZERO) <= 0){
				iterator.remove();
			}
		}
		amtApportions.addAll(dbAmtApportions);
		amtApportionDepartments.addAll(collect);
	}

	@Override
	public void reCollectApportionForUpdateEcsExpTypeNew(PcxBill bill,
														 List<PcxBillAmtApportion> amtApportions,
														 List<PcxBillAmtApportionDepartment> amtApportionDepartments,
														 Set<String> originExpenseType,
														 Set<String> targetExpenseType,
														 Map<String, String> changeExpenseTypeMap,
														 Map<String, CommonCheckExpenseTypeAmtDTO> map) {
		//财务审核时修改费用类型或金额时重新计算分摊
		//使用实际金额去重新计算分摊，而不是去减改变的分摊
		List<PcxBillAmtApportion> dbAmtApportions = queryBillAmtApportionList(bill.getId());
		List<PcxBillAmtApportionDepartment> dbApportionDepartments = queryBillAmtApportionDepartmentList(bill.getId());
		Map<String, List<PcxBillAmtApportionDepartment>> expDeptMap = dbApportionDepartments
				.stream().collect(Collectors.groupingBy(PcxBillAmtApportionDepartment::getExpenseTypeCode));
		for (String originType : originExpenseType) {
			//如果原类型已经没有金额，就使用0进行计算
			CommonCheckExpenseTypeAmtDTO commonCheckExpenseTypeAmtDTO = map.getOrDefault(originType, new CommonCheckExpenseTypeAmtDTO());
			List<PcxBillAmtApportionDepartment> originDeptList = expDeptMap.get(originType);
			boolean isLast;
			for (int i = 0; i < originDeptList.size(); i++) {
				PcxBillAmtApportionDepartment department = originDeptList.get(i);
				isLast = i == originDeptList.size() - 1;
				BigDecimal amt = commonCheckExpenseTypeAmtDTO.getLastAmt();
				BigDecimal inputAmt = commonCheckExpenseTypeAmtDTO.getLastInputAmt();
				if (!isLast){
					amt = calculateRateAmt(commonCheckExpenseTypeAmtDTO.getAmt(), department.getDepartmentRate());
					inputAmt = calculateRateAmt(commonCheckExpenseTypeAmtDTO.getInputAmt(), department.getDepartmentRate());
				}
				department.setDepartmentAmt(amt);
				department.setInputAmt(inputAmt);
				commonCheckExpenseTypeAmtDTO.setLastAmt(commonCheckExpenseTypeAmtDTO.getLastAmt().subtract(amt));
				commonCheckExpenseTypeAmtDTO.setLastInputAmt(commonCheckExpenseTypeAmtDTO.getLastInputAmt().subtract(inputAmt));
			}
		}
		for (String targetType : targetExpenseType) {
			//如果目标类型包含在原类型中，则不用处理
			if (originExpenseType.contains(targetType)){
				continue;
			}
			CommonCheckExpenseTypeAmtDTO dto = map.get(targetType);
			//如果目标类型还没有，则使用原类型的分摊列表进行复制一份
			List<PcxBillAmtApportionDepartment> targetList = expDeptMap.get(targetType);
			boolean haveTarget = true;
			if (CollectionUtils.isEmpty(targetList)){
				haveTarget = false;
				String originType = changeExpenseTypeMap.get(targetType);
				List<PcxBillAmtApportionDepartment> originTypeDeptList = expDeptMap.get(originType);
				targetList= new ArrayList<>();
				for (int i = 0; i < originTypeDeptList.size(); i++) {
					PcxBillAmtApportionDepartment newDepartmentAmt = new PcxBillAmtApportionDepartment();
					PcxBillAmtApportionDepartment  departmentAmt = originTypeDeptList.get(i);
					BeanUtils.copyProperties(departmentAmt, newDepartmentAmt);
					newDepartmentAmt.setId(IDGenerator.id());

					newDepartmentAmt.setExpenseTypeCode(dto.getExpenseTypeCode());
					newDepartmentAmt.setExpenseTypeName(dto.getExpenseTypeName());
					targetList.add(newDepartmentAmt);
				}
				dbApportionDepartments.addAll(targetList);
				//整单分摊的，分摊主表就一条，直接打上他的id
				PcxBillAmtApportion amtApportion = dbAmtApportions.get(0);
				if (Objects.equals(bill.getApportionType(), ApportionTypeEnum.BILL.getCode())){
					for (PcxBillAmtApportionDepartment amtApportionDepartment : targetList) {
						amtApportionDepartment.setApportionId(amtApportion.getId());
					}
				}else{
					//如果是费用分摊，并且是新的费用，则创建一个新的分摊主表数据，总金额外面会金额汇总
					PcxBillAmtApportion newApportion = new PcxBillAmtApportion();
					BeanUtils.copyProperties(amtApportion, newApportion);
					newApportion.setId(IDGenerator.id());
					newApportion.setExpenseTypeCode(dto.getExpenseTypeCode());
					newApportion.setExpenseTypeName(dto.getExpenseTypeName());
					dbAmtApportions.add(newApportion);
					for (PcxBillAmtApportionDepartment amtApportionDepartment : targetList) {
						amtApportionDepartment.setApportionId(newApportion.getId());
					}
				}
			}
			boolean isLast;
			for (int i = 0; i < targetList.size(); i++) {
				PcxBillAmtApportionDepartment department = targetList.get(i);
				isLast = i == targetList.size() - 1;
				BigDecimal amt = dto.getLastAmt();
				BigDecimal inputAmt = dto.getLastInputAmt();
				if (!isLast){
					amt = calculateRateAmt(dto.getAmt(), department.getDepartmentRate());
					inputAmt = calculateRateAmt(dto.getInputAmt(), department.getDepartmentRate());
				}
				department.setDepartmentAmt(amt);
				department.setInputAmt(inputAmt);
				dto.setLastAmt(dto.getLastAmt().subtract(amt));
				dto.setLastInputAmt(dto.getLastInputAmt().subtract(inputAmt));
			}
		}
		//把部门分摊金额大于0的过滤出来
		List<PcxBillAmtApportionDepartment> collect = dbApportionDepartments.stream().filter(item -> item.getDepartmentAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
		//根据分摊主表id分组，求出每个分摊主表id的部门分摊金额之和
		Map<String, BigDecimal> apportionAmtMap = new HashMap<>();
		Map<String, BigDecimal> inputAmtMap = new HashMap<>();
		for (PcxBillAmtApportionDepartment department : collect) {
			BigDecimal checkAmt = apportionAmtMap.getOrDefault(department.getApportionId(), BigDecimal.ZERO);
			BigDecimal inputAmt = inputAmtMap.getOrDefault(department.getApportionId(), BigDecimal.ZERO);
			apportionAmtMap.put(department.getApportionId(), checkAmt.add(department.getDepartmentAmt()));
			inputAmtMap.put(department.getApportionId(), inputAmt.add(department.getInputAmt()));
		}
		//更新分摊主表的分摊金额，分摊金额小于等于0的过滤掉
		Iterator<PcxBillAmtApportion> iterator = dbAmtApportions.iterator();
		while (iterator.hasNext()){
			PcxBillAmtApportion next = iterator.next();
			BigDecimal apportionAmt = apportionAmtMap.getOrDefault(next.getId(), BigDecimal.ZERO);
			BigDecimal inputAmt = inputAmtMap.getOrDefault(next.getId(), BigDecimal.ZERO);
			next.setApportionAmt(apportionAmt);
			next.setInputAmt(inputAmt);
			if (apportionAmt.compareTo(BigDecimal.ZERO) <= 0){
				iterator.remove();
			}
		}
		amtApportions.addAll(dbAmtApportions);
		amtApportionDepartments.addAll(collect);
	}

	@Override
	public Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> initApportion(PcxBill bill, List<? extends PcxBillExpBase> baseExpList) {
		List<PcxBillAmtApportion> apportions = new ArrayList<>();
		List<PcxBillAmtApportionDepartment> apportionDepartments = new ArrayList<>();
		if (Objects.isNull(bill) || CollectionUtils.isEmpty(baseExpList) || bill.getInputAmt().compareTo(BigDecimal.ZERO)<=0){
			return Pair.of(apportions, apportionDepartments);
		}
		PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
		amtApportion.setId(IDGenerator.id());
		amtApportion.setFiscal(bill.getFiscal());
		amtApportion.setAgyCode(bill.getAgyCode());
		amtApportion.setMofDivCode(bill.getMofDivCode());
		PcxBillAmtApportionDepartment amtApportionDepartment = new PcxBillAmtApportionDepartment();
		amtApportionDepartment.setFiscal(bill.getFiscal());
		amtApportionDepartment.setAgyCode(bill.getAgyCode());
		amtApportionDepartment.setMofDivCode(bill.getMofDivCode());
		amtApportionDepartment.setId(IDGenerator.id());
		amtApportionDepartment.setApportionId(amtApportion.getId());
		//费用承担部门改成直接用填报人部门
		amtApportionDepartment.setDepartmentCode(bill.getDepartmentCode());
		amtApportionDepartment.setDepartmentName(bill.getDepartmentName());

		BaseDataVo baseDataVo = queryEmpBudDepartment(amtApportionDepartment);
		if (Objects.nonNull(baseDataVo)){
			amtApportionDepartment.setBudDepartmentCode(baseDataVo.getCode());
			amtApportionDepartment.setBudDepartmentName(baseDataVo.getName());
		}

		PcxBillExpBase expBase = baseExpList.get(0);
		amtApportion.setApportionAmt(expBase.getInputAmt());
		amtApportion.setInputAmt(expBase.getInputAmt());
		amtApportion.setExpenseTypeCode(expBase.getExpenseCode());
		amtApportion.setExpenseTypeName(expBase.getExpenseName());
		amtApportionDepartment.setDepartmentAmt(expBase.getInputAmt());
		amtApportionDepartment.setInputAmt(expBase.getInputAmt());
		amtApportionDepartment.setDepartmentRate(new BigDecimal(100));
		amtApportionDepartment.setExpenseTypeCode(expBase.getExpenseCode());
		amtApportionDepartment.setExpenseTypeName(expBase.getExpenseName());
		apportionDepartments.add(amtApportionDepartment);
		apportions.add(amtApportion);
		return Pair.of(apportions, apportionDepartments);
	}

	private void plusTargetCheckAmt(List<PcxBillAmtApportionDepartment> targetTypeDeptList,
									BigDecimal targetCheckAmt,
									List<PcxBillAmtApportionDepartment> sourceTypeDeptList,
									List<PcxBillAmtApportion> dbAmtApportions,
									Integer apportionType,
									Map<String, List<PcxBillAmtApportionDepartment>> expDeptMap,
									String targetTypeCode,
									String targetTypeName) {
		BigDecimal lastAmt = targetCheckAmt;
		if (CollectionUtils.isNotEmpty(targetTypeDeptList)){
			for (int i = 0; i < targetTypeDeptList.size(); i++) {
				PcxBillAmtApportionDepartment  departmentAmt = targetTypeDeptList.get(i);
				boolean last = i == targetTypeDeptList.size() - 1;
				BigDecimal minusAmt = lastAmt;
				if (!last){
					minusAmt = calculateRateAmt(targetCheckAmt, departmentAmt.getDepartmentRate());
				}
				departmentAmt.setDepartmentAmt(departmentAmt.getDepartmentAmt().add(minusAmt));
				lastAmt = lastAmt.subtract(minusAmt);
			}
		}else{
			List<PcxBillAmtApportionDepartment> newDepartmentAmtList = new ArrayList<>();
			for (int i = 0; i < sourceTypeDeptList.size(); i++) {
				PcxBillAmtApportionDepartment newDepartmentAmt = new PcxBillAmtApportionDepartment();
				PcxBillAmtApportionDepartment  departmentAmt = sourceTypeDeptList.get(i);
				BeanUtils.copyProperties(departmentAmt, newDepartmentAmt);
				newDepartmentAmt.setId(IDGenerator.id());

				boolean last = i == sourceTypeDeptList.size() - 1;
				BigDecimal minusAmt = lastAmt;
				if (!last){
					minusAmt = calculateRateAmt(targetCheckAmt, departmentAmt.getDepartmentRate());
				}
				newDepartmentAmt.setDepartmentAmt(minusAmt);
				newDepartmentAmt.setExpenseTypeCode(targetTypeCode);
				newDepartmentAmt.setExpenseTypeName(targetTypeName);
				lastAmt = lastAmt.subtract(minusAmt);
				newDepartmentAmtList.add(newDepartmentAmt);
			}
			//整单分摊的，分摊主表就一条，直接打上他的id
			PcxBillAmtApportion amtApportion = dbAmtApportions.get(0);
			if (Objects.equals(apportionType, ApportionTypeEnum.BILL.getCode())){
				for (PcxBillAmtApportionDepartment amtApportionDepartment : newDepartmentAmtList) {
					amtApportionDepartment.setApportionId(amtApportion.getId());
				}
			}else{
				//如果是费用分摊，并且是新的费用，则创建一个新的分摊主表数据，总金额外面会金额汇总
				PcxBillAmtApportion newApportion = new PcxBillAmtApportion();
				BeanUtils.copyProperties(amtApportion, newApportion);
				newApportion.setId(IDGenerator.id());
				newApportion.setExpenseTypeCode(targetTypeCode);
				newApportion.setExpenseTypeName(targetTypeName);
				dbAmtApportions.add(newApportion);
				for (PcxBillAmtApportionDepartment amtApportionDepartment : newDepartmentAmtList) {
					amtApportionDepartment.setApportionId(newApportion.getId());
				}
			}
			expDeptMap.put(targetTypeCode, newDepartmentAmtList);
		}
	}

	private void minusSourceCheckAmt(List<PcxBillAmtApportionDepartment> sourceTypeDeptList, BigDecimal sourceCheckAmt) {
		BigDecimal lastAmt = sourceCheckAmt;
		for (int i = 0; i < sourceTypeDeptList.size(); i++) {
			PcxBillAmtApportionDepartment  departmentAmt = sourceTypeDeptList.get(i);
			boolean last = i == sourceTypeDeptList.size() - 1;
			BigDecimal minusAmt = lastAmt;
			if (!last){
				minusAmt = calculateRateAmt(sourceCheckAmt, departmentAmt.getDepartmentRate());
			}
			departmentAmt.setDepartmentAmt(departmentAmt.getDepartmentAmt().subtract(minusAmt));
			lastAmt = lastAmt.subtract(minusAmt);
		}
		BigDecimal collect = sourceTypeDeptList.stream().map(PcxBillAmtApportionDepartment::getDepartmentAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
		if (collect.compareTo(BigDecimal.ZERO)<=0){
			sourceTypeDeptList.forEach(a -> a.setDepartmentAmt(BigDecimal.ZERO));
		}
	}

	private CheckMsg toExpenseApportion(SwitchApportionTypeQO qo, PcxBill pcxBill) {

		EcsExpCommonService.EcsRelMsgTemp ecsRelMsgTemp = ecsExpCommonService.ecsRelList(pcxBill, a -> StringUtil.isEmpty(a.getExpenseTypeCode()));
		List<EcsRelVO> ecsRelVOS = ecsRelMsgTemp.getEcsRelVOS();
		SwitchApportionVO vo = new SwitchApportionVO();
		if (CollectionUtils.isNotEmpty(ecsRelVOS)){
			ecsRelVOS.sort(Comparator.comparing(EcsRelVO::getEcsBillId).reversed());
			vo.setEcsRelList(ecsRelVOS);
			return CheckMsg.success(vo);
		}
		pcxBill.setApportionType(qo.getApportionType());
		List<PcxBillExpCommon> pcxBillExpCommons = pcxBillExpCommonDao.selectList(Wrappers.lambdaQuery(PcxBillExpCommon.class)
				.eq(PcxBillExpCommon::getBillId, pcxBill.getId()));
		List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
		List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

		collectExpApportionList(amtApportions, amtApportionDepartments, pcxBill, pcxBillExpCommons);

		ecsExpTransService.updateTravelAmtApportion(amtApportions, amtApportionDepartments, Lists.newArrayList(), pcxBill, true);
		CheckMsg checkMsg = queryCommonAmtApportionList(qo);
		CommonAmtApportionListVO data = (CommonAmtApportionListVO) checkMsg.getData();
		vo.setApportionType(data.getApportionType());
		vo.setApportionList(data.getApportionList());
		vo.setBlockPropertyMap(data.getBlockPropertyMap());
		return CheckMsg.success(vo);
	}

	private void collectExpApportionList(List<PcxBillAmtApportion> amtApportions,
										 List<PcxBillAmtApportionDepartment> amtApportionDepartments,
										 PcxBill pcxBill,
										 List<PcxBillExpCommon> pcxBillExpCommons) {
		int index = 1;
		for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
			if (pcxBillExpCommon.getCheckAmt().compareTo(BigDecimal.ZERO)<=0){
				continue;
			}
			PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
			amtApportion.setFiscal(pcxBill.getFiscal());
			amtApportion.setAgyCode(pcxBill.getAgyCode());
			amtApportion.setMofDivCode(pcxBill.getMofDivCode());
			amtApportion.setId(IDGenerator.id());
			amtApportion.setSeq(index ++);
			amtApportion.setApportionAmt(pcxBillExpCommon.getCheckAmt());
			amtApportion.setInputAmt(pcxBillExpCommon.getCheckAmt());
			amtApportion.setExpenseTypeCode(pcxBillExpCommon.getExpenseCode());
			amtApportion.setExpenseTypeName(pcxBillExpCommon.getExpenseName());
			amtApportion.setBillId(pcxBill.getId());
			amtApportions.add(amtApportion);

			PcxBillAmtApportionDepartment ap = new PcxBillAmtApportionDepartment();
			ap.setFiscal(pcxBill.getFiscal());
			ap.setAgyCode(pcxBill.getAgyCode());
			ap.setMofDivCode(pcxBill.getMofDivCode());
			ap.setDepartmentCode(pcxBill.getDepartmentCode());
			ap.setDepartmentName(pcxBill.getDepartmentName());
			ap.setExpenseTypeCode(pcxBillExpCommon.getExpenseCode());
			ap.setExpenseTypeName(pcxBillExpCommon.getExpenseName());
			ap.setDepartmentRate(new BigDecimal(100));
			ap.setDepartmentAmt(pcxBillExpCommon.getCheckAmt());
			ap.setInputAmt(pcxBillExpCommon.getCheckAmt());
			ap.setApportionId(amtApportion.getId());
			ap.setBillId(pcxBill.getId());
			amtApportionDepartments.add(ap);
		}
	}

	/**
	 * 切换成整单分摊
	 * @param switchApportionTypeQO
	 * @param bill
	 * @return
	 */
	private CheckMsg toBillApportion(SwitchApportionTypeQO switchApportionTypeQO, PcxBill bill) {
		bill.setApportionType(switchApportionTypeQO.getApportionType());
		CommonUpdateApportionQO qo = new CommonUpdateApportionQO();
		qo.setFiscal(bill.getFiscal());
		qo.setAgyCode(bill.getAgyCode());
		qo.setMofDivCode(bill.getMofDivCode());
		qo.setBillId(bill.getId());

		CommonApportionQO amtApportion = new CommonApportionQO();
		qo.setApportionList(Collections.singletonList(amtApportion));
		amtApportion.setApportionAmt(bill.getInputAmt());
		amtApportion.setExpenseTypeCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
		amtApportion.setExpenseTypeName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
		CommonApportionDeptQO amtApportionDept = new CommonApportionDeptQO();
		amtApportionDept.setDepartmentRate(new BigDecimal(100));
		amtApportionDept.setDepartmentAmt(bill.getInputAmt());
		amtApportionDept.setDepartmentCode(bill.getDepartmentCode());
		amtApportionDept.setDepartmentName(bill.getDepartmentName());
		amtApportion.setDeptList(Collections.singletonList(amtApportionDept));

		List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
		List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

		commonBillApportion(bill, qo, amtApportionDepartments, amtApportions, null);

		ecsExpTransService.updateTravelAmtApportion(amtApportions, amtApportionDepartments, Lists.newArrayList(), bill, true);

		SwitchApportionVO vo = new SwitchApportionVO();
		CheckMsg checkMsg = queryCommonAmtApportionList(qo);
		CommonAmtApportionListVO data = (CommonAmtApportionListVO) checkMsg.getData();
		vo.setApportionType(data.getApportionType());
		vo.setApportionList(data.getApportionList());
		vo.setBlockPropertyMap(data.getBlockPropertyMap());
		return CheckMsg.success(vo);
	}

	private CheckMsg validSegment(Set<String> segmentIds, List<PcxBillTripSegment> tripSegments) {
		List<String> amtSegmentIds = tripSegments.stream()
				.filter(item -> item.getInputAmt().compareTo(BigDecimal.ZERO) > 0).map(item -> item.getId()).collect(Collectors.toList());
		if (segmentIds.size() != amtSegmentIds.size()){
			return CheckMsg.fail("分摊行程数量不完全");
		}
		for (String tripSegment : amtSegmentIds) {
			if (!segmentIds.contains(tripSegment)){
				return CheckMsg.fail("分摊行程不匹配");
			}
			segmentIds.remove(tripSegment);
		}
		if (!segmentIds.isEmpty()){
			return CheckMsg.fail("分摊行程不匹配");
		}
		return CheckMsg.success();
	}

	private CheckMsg validDepartAndSegment(TravelUpdateApportionQO qo) {
		Set<String> segmentSet = new HashSet<>();
		for (UpdateApportionQO updateApportionQO : qo.getApportionList()) {
			for (String s : updateApportionQO.getSegmentIds()) {
				if (segmentSet.contains(s)){
					return CheckMsg.fail("分摊行程不能重复");
				}
				segmentSet.add(s);
			}
		}
		return CheckMsg.success(segmentSet);
	}

	private List<TripVO> assembleTripList(List<PcxBillTravelTrip> tripList, List<PcxBillTripSegment> tripSegments) {
		Map<String, List<PcxBillTripSegment>> segmentMap = tripSegments
				.stream()
				.collect(Collectors.groupingBy(PcxBillTripSegment::getTripId, Collectors.toList()));
		List<TripVO> result = new ArrayList<>();
		for (PcxBillTravelTrip travelTrip : tripList) {
			TripVO tripVO = new TripVO();
			BeanUtils.copyProperties(travelTrip, tripVO);
			tripVO.setStartTime(TravelTripProcessor.transDate(travelTrip.getStartTime()));
			tripVO.setFinishTime(TravelTripProcessor.transDate(travelTrip.getEndTime()));
			tripVO.setTripId(travelTrip.getId());
			List<PcxBillTripSegment> segmentList = segmentMap.getOrDefault(travelTrip.getId(), Lists.newArrayList());
			segmentList.sort(Comparator.comparing(PcxBillTripSegment::getSeq));
			for (PcxBillTripSegment pcxBillTripSegment : segmentList) {
				TripNodeVO nodeVO = new TripNodeVO();
				BeanUtils.copyProperties(pcxBillTripSegment, nodeVO);
				nodeVO.setAmt(pcxBillTripSegment.getInputAmt());
				nodeVO.setSegmentId(pcxBillTripSegment.getId());
				tripVO.getTripSegmentList().add(nodeVO);
			}
			result.add(tripVO);
		}
		return result;
	}

	private void fillApportionSegmentIds(List<AmtApportionVO> amtApportionList, List<PcxBillTripSegment> tripSegments) {
		Map<String, List<String>> segmentIdsMap = tripSegments
				.stream()
				.filter(item-> item.getInputAmt().compareTo(BigDecimal.ZERO) > 0)
				.collect(Collectors.groupingBy(PcxBillTripSegment::getApportionId,
						Collectors.mapping(PcxBillTripSegment::getId, Collectors.toList())));
		for (AmtApportionVO vo : amtApportionList) {
			List<String> segmentIds = segmentIdsMap.getOrDefault(vo.getId(), Lists.newArrayList());
			vo.getSegmentIds().addAll(segmentIds);
			vo.setTripSegmentNum(segmentIds.size());
		}
	}

	private List<AmtApportionVO> assembleAmtApportionList(List<PcxBillAmtApportion> amtApportions,
														  List<PcxBillAmtApportionDepartment> amtApportionDepartments) {
		amtApportions.sort(Comparator.comparing(PcxBillAmtApportion::getSeq));
		Map<String, List<PcxBillAmtApportionDepartment>> map = amtApportionDepartments
				.stream()
				.collect(Collectors.groupingBy(PcxBillAmtApportionDepartment::getApportionId));
		List<AmtApportionVO> result = new ArrayList<>();
		for (PcxBillAmtApportion amtApportion : amtApportions) {
			AmtApportionVO vo = new AmtApportionVO();
			BeanUtils.copyProperties(amtApportion, vo);
			List<PcxBillAmtApportionDepartment> deptList = map.get(amtApportion.getId());
			if (CollectionUtils.isNotEmpty(deptList)){
				for (PcxBillAmtApportionDepartment dept : deptList) {
					AmtApportionDeptVO deptVO = new AmtApportionDeptVO();
					BeanUtils.copyProperties(dept, deptVO);
					deptVO.setExpenseTypeCode(amtApportion.getExpenseTypeCode());
					deptVO.setExpenseTypeName(amtApportion.getExpenseTypeName());
					vo.getDeptList().add(deptVO);
				}
				//差旅的分摊部门数据直接挂到分摊主表vo上
				AmtApportionDeptVO amtApportionDeptVO = vo.getDeptList().get(0);
				BeanUtils.copyProperties(amtApportionDeptVO, vo);
				//恢复一下分摊主表的id
				vo.setId(amtApportion.getId());
			}
			result.add(vo);
		}
		return result;
	}

	@Override
	public List<PcxBillAmtApportion> selectByBillId(String billId) {
		return pcxBillAmtApportionDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
				.eq(PcxBillAmtApportion::getBillId, billId));
	}
}


