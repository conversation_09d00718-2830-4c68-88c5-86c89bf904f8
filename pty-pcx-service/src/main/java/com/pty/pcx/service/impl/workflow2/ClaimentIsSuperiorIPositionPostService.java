package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.workflow2.IPositionPostService;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClaimentIsSuperiorIPositionPostService implements IPositionPostService {

    @Override
    public List<String> getPositionIds() {
        return Lists.newArrayList(
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "5"),
                PcxNodeEnum.budget_confirm.getId(),
                PcxNodeEnum.cost_bearing.getId()
        );
    }

    @Autowired
    private BillMainService billMainService;
    @Autowired
    private IPcxMadEmployeeService madEmployeeService;

    @Override
    public boolean postDeal(String billId, String positionId, List<String> userCodes) {
        // 部门领导审批时, 发现填报人是审批人的直接上级, 从userCodes中移除该审批人, userCodes为空时, 不需要审批
        PcxBill view = billMainService.view(billId);
        Assert.notNull(view, "单据不存在 {}", billId);

        AtomicBoolean needApprove = new AtomicBoolean(true);

        List<MadEmployeeDTO> madEmployeeDTOS = madEmployeeService.selectByUserCodes(userCodes, null, Integer.valueOf(view.getFiscal()), view.getMofDivCode());
        if (CollectionUtil.isNotEmpty(madEmployeeDTOS)){
            // 取出审批人的直接上级, 并按领导员工编码进行分组
            Map<String, List<String>> $leaderEmpAndUserCodesRel = madEmployeeDTOS.stream().filter(emp -> StrUtil.isNotBlank(emp.getDirectLeaderUkExFiscal()))
                    .collect(Collectors.groupingBy(MadEmployeeDTO::getDirectLeaderUkExFiscal, Collectors.mapping(MadEmployeeDTO::getUserCode, Collectors.toList())));
            List<String> leaderEmpUkExFiscal = $leaderEmpAndUserCodesRel.keySet().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(leaderEmpUkExFiscal)) {
                List<MadEmployeeDTO> leaderEmpDtos = madEmployeeService.selectByUkExFiscals(leaderEmpUkExFiscal, Integer.valueOf(view.getFiscal()));
                if (CollectionUtil.isNotEmpty(leaderEmpDtos)) {
                    // 取出领导员工编码与用户编码的映射关系
                    Map<String, String> $leaderEmpUkExFiscalWithUserCode = leaderEmpDtos.stream().collect(Collectors.toMap(MadEmployeeDTO::getUkExFiscal, MadEmployeeDTO::getUserCode));
                    $leaderEmpUkExFiscalWithUserCode.forEach((ukExFiscal, userCode)->{
                        if (view.getClaimantUserCode().equals(userCode)) {
                            // 如果领导员工编码和当前填报人员工编码相同, 移除领导对应的下级用户编码
                            userCodes.removeAll($leaderEmpAndUserCodesRel.get(ukExFiscal));
                            needApprove.set(false);
                        }
                    });
                }
            }
        }
        return needApprove.get();
    }
}
