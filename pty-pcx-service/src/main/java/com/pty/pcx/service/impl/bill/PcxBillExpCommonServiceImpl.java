package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.pcx.api.bill.PcxBillExpCommonService;
import com.pty.pcx.dao.bill.PcxBillExpCommonDao;
import com.pty.pcx.entity.bill.PcxBillExpCommon;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 单据费用表_通用(PcxBillExpCommon)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:03
 */
@Service("pcxBillExpCommonService")
@Indexed
public class PcxBillExpCommonServiceImpl implements PcxBillExpCommonService {

    @Resource
    private PcxBillExpCommonDao pcxBillExpCommonDao;

    @Override
    public List<PcxBillExpCommon> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpCommonDao.selectList(new LambdaQueryWrapper<PcxBillExpCommon>()
                .in(PcxBillExpCommon::getBillId, billIds)
                .eq(PcxBillExpCommon::getFiscal, fiscal)
                .eq(PcxBillExpCommon::getMofDivCode, mofDivCode));
    }
}
