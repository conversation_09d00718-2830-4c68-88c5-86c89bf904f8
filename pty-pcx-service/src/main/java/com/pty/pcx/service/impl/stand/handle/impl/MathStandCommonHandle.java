package com.pty.pcx.service.impl.stand.handle.impl;

import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.service.impl.stand.handle.MatchStandFactoryHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 通用要素匹配
 * <AUTHOR>
 * @since 2024/12/04
 */
@Indexed
@Service
@Slf4j
public class MathStandCommonHandle implements MatchStandFactoryHandle {

    @Override
    public List<String> getFactoryValueCode(PcxBaseDTO dto, String expenseCode) {
        return Collections.emptyList();
    }
}
