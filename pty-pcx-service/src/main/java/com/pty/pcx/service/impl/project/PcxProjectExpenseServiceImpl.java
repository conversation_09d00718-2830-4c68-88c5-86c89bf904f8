package com.pty.pcx.service.impl.project;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.pty.fileservice.api.IPubFileService;
import com.pty.fileservice.entity.PaAttach;
import com.pty.mad.entity.MadAtom;
import com.pty.mad.entity.MadProject;
import com.pty.mad.entity.MadProjectAux;
import com.pty.pcx.api.project.IPcxProjectExpensePlusService;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.project.PcxProjectExpenseDao;
import com.pty.pcx.dto.project.ProjectExpenseTypeDTO;
import com.pty.pcx.dto.project.ProjectExpenseTypeImportAttachDTO;
import com.pty.pcx.dto.project.ProjectExpenseTypeImportDTO;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.project.PcxProjectExpense;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.vo.project.MadProjectVO;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.ExcelUtils;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import com.pty.pub.entity.vo.ExcelExportTitleRowVO;
import com.pty.pub.entity.vo.ExcelExportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.pty.mad.api.IMadCommonService;
import org.pty.mad.api.IMadProjectAuxService;
import org.pty.mad.api.IMadProjectService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Slf4j
@Service
public class PcxProjectExpenseServiceImpl extends ServiceImpl<PcxProjectExpenseDao, PcxProjectExpense> implements IPcxProjectExpensePlusService {

    @Autowired
    private IMadProjectService madProjectService;

    @Autowired
    private PcxBasExpTypeDao pcxBasExpTypeDao;

    @Autowired
    @Lazy
    private IPubFileService pubFileService;

    @Autowired
    private IMadProjectService projectService;

    @Autowired
    private IMadCommonService commonService;

    @Resource(name = "prjProjectAuxService")
    IMadProjectAuxService iMadProjectAuxService;

    private static final int IMPROT_START = 3;  // 导入数据行从excel中第几行开始获取

    @Override
    public PageResult<MadProjectVO> selectPage(MadProject cond) {
        cond.setIsEnabled(1);
        PageResult<MadProject> pageResult = madProjectService.selectWithPage(cond);

        List<String> projectIds = pageResult.getResult()
                .stream()
                .map(MadProject::getMadId)
                .collect(Collectors.toList());

        List<MadProjectVO> madProjectVOS = new ArrayList<>();
        if (!projectIds.isEmpty()) {
            List<PcxProjectExpense> pcxProjectExpenses = this.list(
                    Wrappers.lambdaQuery(PcxProjectExpense.class)
                            .in(PcxProjectExpense::getProjectId, projectIds)
            );

            List<String> expTypeCodes = new ArrayList<>();
            List<String> agyCodes = new ArrayList<>();
            Map<String, List<PcxProjectExpense>> pcxProjectExpenseMap = new HashMap<>();
            for (PcxProjectExpense expense : pcxProjectExpenses) {
                expTypeCodes.add(expense.getExpenseCode());
                agyCodes.add(expense.getAgyCode());
                pcxProjectExpenseMap.computeIfAbsent(expense.getProjectId(), k -> new ArrayList<>()).add(expense);
            }
            PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
            pcxBasExpTypeQO.setExpTypeCodes(expTypeCodes);
            pcxBasExpTypeQO.setAgyCodes(agyCodes);
            List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);

            Map<Pair<String, String>, Integer> isEnabledMap = new HashMap<>();
            for (PcxBasExpType pcxBasExpType : pcxBasExpTypes) {
                isEnabledMap.put(Pair.of(pcxBasExpType.getExpenseCode(), pcxBasExpType.getAgyCode()), pcxBasExpType.getIsEnabled());
            }

            madProjectVOS = pageResult.getResult()
                    .stream()
                    .map(madProject -> {
                        MadProjectVO madProjectVO = new MadProjectVO();
                        BeanUtils.copyProperties(madProject, madProjectVO);
                        if (pcxProjectExpenseMap.containsKey(madProject.getMadId())) {
                            List<PcxProjectExpense> groupedPcxProjectExpenses = pcxProjectExpenseMap.get(madProject.getMadId());
                            groupedPcxProjectExpenses = groupedPcxProjectExpenses
                                    .stream()
                                    .filter(pcxProjectExpense -> {
                                        Integer isEnabled = isEnabledMap.get(Pair.of(pcxProjectExpense.getExpenseCode(), pcxProjectExpense.getAgyCode()));
                                        return Objects.equals(1, isEnabled);
                                    })
                                    .collect(Collectors.toList());
                            List<String> expenseCodes = groupedPcxProjectExpenses.stream().map(PcxProjectExpense::getExpenseCode).collect(Collectors.toList());
                            List<String> expenseNames = groupedPcxProjectExpenses.stream().map(PcxProjectExpense::getExpenseName).collect(Collectors.toList());
                            madProjectVO.setExpenseCodes(expenseCodes);
                            madProjectVO.setExpenseNamesStr(String.join("、", expenseNames));
                        }
                        return madProjectVO;
                    })
                    .collect(Collectors.toList());
        }

        PageResult<MadProjectVO> result = new PageResult<>();
        result.setResult(madProjectVOS);
        result.setTotal(pageResult.getTotal());
        result.setTopTotal(pageResult.getTopTotal());

        return result;
    }

    @Override
    public List<MadProject> selectByExpenseAndDepartment(String agyCode,String fiscal,String mofDivCode,String expenseCode) {
        List<PcxProjectExpense> pcxProjectExpenses = this.list(
                Wrappers.lambdaQuery(PcxProjectExpense.class)
                        .eq(PcxProjectExpense::getAgyCode, agyCode)
                        .eq(PcxProjectExpense::getMofDivCode, mofDivCode)
                        .eq(PcxProjectExpense::getFiscal, fiscal)
                        .eq(PcxProjectExpense::getExpenseCode, expenseCode)
        );
        if (pcxProjectExpenses.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> projectIds = pcxProjectExpenses.stream().map(PcxProjectExpense::getProjectId).collect(Collectors.toList());
        MadProject madProject = new MadProject();
        madProject.setMadIds(projectIds);
        return madProjectService.select(madProject);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response saveExpenseType(ProjectExpenseTypeDTO projectExpenseTypeDTO) {
        String projectId = projectExpenseTypeDTO.getProjectId();
//        long count = this.count(
//                Wrappers.<PcxProjectExpense>lambdaQuery()
//                        .eq(PcxProjectExpense::getProjectId, projectId)
//                        .eq(PcxProjectExpense::getIsErpSynced, 1)
//        );
//        if(count > 0) {
//            return Response.fail().setMsg("费用类型由ERP同步的，不允许修改");
//        }
        List<String> expenseCodeList = projectExpenseTypeDTO.getExpenseCodes();
        if (CollectionUtils.isNotEmpty(expenseCodeList)) {
            PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
            pcxBasExpTypeQO.setAgyCode(projectExpenseTypeDTO.getAgyCode());
            pcxBasExpTypeQO.setFiscal(String.valueOf(projectExpenseTypeDTO.getFiscal()));
            pcxBasExpTypeQO.setMofDivCode(projectExpenseTypeDTO.getMofDivCode());
            pcxBasExpTypeQO.setExpTypeCodes(expenseCodeList);
            List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);

            Map<String, String> pcxBasExpTypeMap = new LinkedHashMap<>();
            for (PcxBasExpType pcxBasExpType : pcxBasExpTypes) {
                pcxBasExpTypeMap.put(pcxBasExpType.getExpenseCode(), pcxBasExpType.getExpenseName());
            }

            List<PcxProjectExpense> pcxProjectExpenses = pcxBasExpTypeMap.entrySet()
                    .stream()
                    .map(entry -> {
                        PcxProjectExpense pcxProjectExpense = new PcxProjectExpense();
                        pcxProjectExpense.setProjectId(projectId);
                        pcxProjectExpense.setExpenseCode(entry.getKey());
                        pcxProjectExpense.setExpenseName(entry.getValue());
                        pcxProjectExpense.setAgyCode(projectExpenseTypeDTO.getAgyCode());
                        pcxProjectExpense.setFiscal(projectExpenseTypeDTO.getFiscal());
                        pcxProjectExpense.setMofDivCode(projectExpenseTypeDTO.getMofDivCode());
                        pcxProjectExpense.setBudgetItemCode(entry.getKey());
                        pcxProjectExpense.setIsErpSynced(0);
                        return pcxProjectExpense;
                    })
                    .collect(Collectors.toList());

            this.remove(
                    Wrappers.lambdaQuery(PcxProjectExpense.class)
                            .eq(PcxProjectExpense::getProjectId, projectExpenseTypeDTO.getProjectId())
            );

            this.saveBatch(pcxProjectExpenses);
        } else {
            this.remove(
                    Wrappers.lambdaQuery(PcxProjectExpense.class)
                            .eq(PcxProjectExpense::getProjectId, projectExpenseTypeDTO.getProjectId())
            );
        }
        return Response.success();
    }

    @Override
    public File downloadTemplate() {
        ExcelExportVO excelExportVO = new ExcelExportVO();
        excelExportVO.setAgyName("系统级导入模板");
        excelExportVO.setFileName("项目开支范围导入模板");
        excelExportVO.setTitle("项目开支范围");
        List<Map<String, String>> tileCellList = createTitleCellList();
        List<ExcelExportTitleRowVO> titleRowList = buildExcelExportTitleRow(tileCellList);
        List<List<ExcelExportTitleRowVO>> titleList = Lists.newArrayList();
        titleList.add(titleRowList);
        excelExportVO.setTitleRowVOList(titleList);
        return ExcelUtils.export(excelExportVO, Lists.newArrayList());
    }

    @Override
    public Response importTemplate(ProjectExpenseTypeImportAttachDTO projectExpenseTypeImportAttachDTO) {
        // 1.获取上传的excel
        PaAttach paAttach = pubFileService.selectAttachById(projectExpenseTypeImportAttachDTO.getAttachId());
        if (paAttach == null) {
            return Response.fail().setMsg("获取上传文件失败,请重新上传");
        }
        // 2.解析上传的数据
        byte[] bytes = pubFileService.fileDownload(paAttach);
        List<List<String>> excelData = null;
        if (bytes != null && bytes.length > 0) {
            log.error("mad-atom import download file for the stream.");
            excelData = ExcelUtils.getExcelData(new ByteArrayInputStream(bytes), IMPROT_START);
        } else {
            log.error("mad-atom import download file for the realpath.");
            excelData = ExcelUtils.getExcelData(paAttach.getRelPath(), IMPROT_START);
        }
        if (excelData == null) {
            log.error("mad-atom import download file by PubFileService failed.");
            return Response.fail().setMsg("读取文件服务的上传文件失败，请联系管理员");
        }

        // 3.去除表头等信息
        excelData = excelData.subList(IMPROT_START, excelData.size());

        // 4.过滤全部为空的集合
        filterNull(excelData);

        if (excelData.isEmpty()) {
            return Response.fail().setMsg("模板无数据！");
        }

        // 5.将行数据映射成对象
        List<ProjectExpenseTypeImportDTO> projectExpenseTypeImportDTOList = excelData
                .stream()
                .map(this::projectExpenseTypeImportAttachDTO)
                .collect(Collectors.toList());

        List<String> madCodes = projectExpenseTypeImportDTOList.stream().map(ProjectExpenseTypeImportDTO::getMadCode).collect(Collectors.toList());
        MadProject madProject = new MadProject();
        madProject.setMadCodes(madCodes);
        List<MadProject> madProjectList = projectService.select(madProject);
        if (!madProjectList.isEmpty()) {
            return Response.fail().setMsg("编码: " + madProjectList.stream().map(MadProject::getMadCode).collect(Collectors.joining(", ")) + " 重复");
        }

        List<String> expenseTypes = projectExpenseTypeImportDTOList
                .stream()
                .flatMap(projectExpenseTypeImportDTO -> projectExpenseTypeImportDTO.getExpenseTypes().stream())
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> expenseTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(expenseTypes)) {
            PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
            pcxBasExpTypeQO.setAgyCode(projectExpenseTypeImportAttachDTO.getAgyCode());
            pcxBasExpTypeQO.setFiscal(String.valueOf(projectExpenseTypeImportAttachDTO.getFiscal()));
            pcxBasExpTypeQO.setMofDivCode(projectExpenseTypeImportAttachDTO.getMofDivCode());
            pcxBasExpTypeQO.setExpTypeNames(expenseTypes);
            List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);
            for (PcxBasExpType pcxBasExpType : pcxBasExpTypes) {
                expenseTypeMap.put(pcxBasExpType.getExpenseName(), pcxBasExpType.getExpenseCode());
            }
        }

        List<MadProject> madProjects = new ArrayList<>();
        List<PcxProjectExpense> pcxProjectExpenses = new ArrayList<>();
        for (ProjectExpenseTypeImportDTO projectExpenseTypeImportDTO : projectExpenseTypeImportDTOList) {
            MadProject project = new MadProject();
            BeanUtils.copyProperties(projectExpenseTypeImportDTO, project);
            String madId = IDGenerator.id();
            project.setMadId(madId);

            String table = "mad_project";
            project.setAgyCode(projectExpenseTypeImportAttachDTO.getAgyCode());
            project.setFiscal(projectExpenseTypeImportAttachDTO.getFiscal());
            project.setMofDivCode(projectExpenseTypeImportAttachDTO.getMofDivCode());
            project.setTable(table);
            project.setAtomCode(MadAtom.atomCode(table));
            //
            project.setIsAddsub(1);
            project.setIsEnabled(1);
            project.setIsLeaf(1);
            project.setLevelNo(1);
            project.setIsDeleted(0);
            project.setMadFname(projectExpenseTypeImportDTO.getMadName());
            project.setMadCode01(projectExpenseTypeImportDTO.getMadCode());
            project.setMadId01(madId);
            //
            project.setCategoryCode("01");
            project.setCategoryName("默认项目大类");
            project.setAuditState("0");
            project.setProStatus("1");
            project.setSecrecyLevel("4");
            project.setIsCapitalProject(0);
            madProjects.add(project);

            List<String> expenseTypeNames = projectExpenseTypeImportDTO.getExpenseTypes();
            for (String expenseTypeName : expenseTypeNames) {
                if (expenseTypeMap.containsKey(expenseTypeName)) {
                    String expenseCode = expenseTypeMap.get(expenseTypeName);
                    PcxProjectExpense pcxProjectExpense = new PcxProjectExpense();
                    pcxProjectExpense.setProjectId(madId);
                    pcxProjectExpense.setExpenseCode(expenseCode);
                    pcxProjectExpense.setExpenseName(expenseTypeName);
                    pcxProjectExpense.setAgyCode(projectExpenseTypeImportAttachDTO.getAgyCode());
                    pcxProjectExpense.setFiscal(projectExpenseTypeImportAttachDTO.getFiscal());
                    pcxProjectExpense.setMofDivCode(projectExpenseTypeImportAttachDTO.getMofDivCode());
                    pcxProjectExpenses.add(pcxProjectExpense);
                }
            }
        }

        for (MadProject project : madProjects) {
            projectService.save(project);
            MadProjectAux madProjectAux = new MadProjectAux();
            BeanUtils.copyProperties(project, madProjectAux);
            madProjectAux.setId(StringUtil.getUUID());
            iMadProjectAuxService.insertSelective(madProjectAux);
            commonService.batchUpdateAcitemGal(project.deepClone());//更新凭证及余额表
        }

        this.saveBatch(pcxProjectExpenses);

        return Response.success().setMsg("导入成功");
    }

    /**
     * 将行数据映射成对象
     */
    private ProjectExpenseTypeImportDTO projectExpenseTypeImportAttachDTO(List<String> lineData) {
        ProjectExpenseTypeImportDTO projectExpenseTypeImportDTO = new ProjectExpenseTypeImportDTO();
        projectExpenseTypeImportDTO.setMadCode(lineData.get(0));
        projectExpenseTypeImportDTO.setMadName(lineData.get(1));
        projectExpenseTypeImportDTO.setProManager(lineData.get(2));
        projectExpenseTypeImportDTO.setCountInvest(lineData.get(3));

        String expenseType = lineData.get(4);
        if (expenseType != null) {
            projectExpenseTypeImportDTO.setExpenseTypes(Arrays.asList(expenseType.split(",")));
        }
        return projectExpenseTypeImportDTO;
    }

    /**
     * 过滤内容全部为空的集合
     *
     * @param excelData
     */
    private void filterNull(List<List<String>> excelData) {
        if (org.springframework.util.CollectionUtils.isEmpty(excelData)) {
            return;
        }
        Iterator<List<String>> iterator = excelData.iterator();
        while (iterator.hasNext()) {
            boolean allisNull = true;
            for (String cell : iterator.next()) {
                if (!StringUtil.isEmpty(cell)) {
                    allisNull = false;
                }
            }
            if (allisNull) {
                iterator.remove();
            }
        }
    }

    private List<ExcelExportTitleRowVO> buildExcelExportTitleRow(List<Map<String, String>> tileCellList) {
        List<ExcelExportTitleRowVO> titleRowList = new ArrayList<>();
        tileCellList.forEach(cell -> {
            ExcelExportTitleRowVO excelExportTitleRowVO = new ExcelExportTitleRowVO();
            excelExportTitleRowVO.setName(cell.get("cellName"));
            excelExportTitleRowVO.setCode(cell.get("cellCode"));
            excelExportTitleRowVO.setWidth(Integer.parseInt(cell.get("cellWidth")));
            titleRowList.add(excelExportTitleRowVO);
        });
        return titleRowList;
    }

    private List<Map<String, String>> createTitleCellList() {
        List<Map<String, String>> titleCells = new ArrayList<>();
        titleCells.add(createTitleCell("项目编码"));
        titleCells.add(createTitleCell("项目名称"));
        titleCells.add(createTitleCell("项目负责人"));
        titleCells.add(createTitleCell("项目总投资"));
        titleCells.add(createTitleCell("费用类型"));
        return titleCells;
    }

    private Map<String, String> createTitleCell(String cellName) {
        Map<String, String> titleCell = new HashMap<>();
        titleCell.put("cellName", cellName);
        titleCell.put("cellCode", cellName);
        titleCell.put("cellWidth", "190");
        return titleCell;
    }

}