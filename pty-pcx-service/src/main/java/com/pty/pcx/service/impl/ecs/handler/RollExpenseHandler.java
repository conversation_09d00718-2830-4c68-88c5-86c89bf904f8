package com.pty.pcx.service.impl.ecs.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pty.mad.entity.MadArea;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillExpDetailSourceEnum;
import com.pty.pcx.dto.bill.CityInfo;
import com.pty.pcx.dto.bill.EmpInfo;
import com.pty.pcx.dto.ecs.file.PcxFileHotelDto;
import com.pty.pcx.dto.ecs.file.PcxFileTaxiDto;
import com.pty.pcx.dto.ecs.inv.ExpDetailDTO;
import com.pty.pcx.dto.ecs.inv.PcxGeneralInvoiceDto;
import com.pty.pcx.dto.ecs.inv.PcxRollInvoiceDto;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.service.impl.ecs.ProcessContext;
import com.pty.pcx.service.impl.ecs.trip.TravelTripProcessor;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 增值税普通发票(卷票)
 */
@Indexed
@Component
@Slf4j
public class RollExpenseHandler extends CommonHandler implements Handler<InvoiceDtoWrapper<PcxRollInvoiceDto, PcxBillExpBase, PcxBillExpDetailBase>> {

    @Override
    public void handle(InvoiceDtoWrapper<PcxRollInvoiceDto, PcxBillExpBase, PcxBillExpDetailBase> invoiceDtoWrapper,
                       ProcessContext context) {
        // 跟dto对应的费用/费用类型 进行费用处理
        List<PcxRollInvoiceDto.Detail> detailList = invoiceDtoWrapper.getDto().getDetailList();
        if (detailList.stream().anyMatch(item->CollectionUtils.isEmpty(item.getExpTypeList()))){
            invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
            return;
        }
        PcxRollInvoiceDto dto = invoiceDtoWrapper.getDto();
        List<PcxBasExpType> myExpTypeList = dto.getDetailList().stream()
                .map(PcxRollInvoiceDto.Detail::getExpTypeList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<PcxBasExpType> expTypeList = getMyExpTypeInItemExpTypeList(myExpTypeList, context);

        collectSettlDetails(invoiceDtoWrapper, PcxRollInvoiceDto::getBillSettlList, PcxRollInvoiceDto::getBillId,
                PcxRollInvoiceDto::getBillTypeCode, context);

        if (expTypeList.size()==1 && (expTypeList.get(0).getExpenseCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)
                || expTypeList.get(0).getExpenseCode().equals(PcxConstant.TRAVEL_DETAIL_3021112)) ){
            //ecs总是把增值税票打上城市间交通费的票种，防止报错
            if (expTypeList.get(0).getExpenseCode().equals(PcxConstant.TRAVEL_DETAIL_3021101)){
                invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
                return;
            }
            invoiceDtoWrapper.setMatchExpType(expTypeList.get(0));
            switch (expTypeList.get(0).getExpenseCode()){
                case PcxConstant.TRAVEL_DETAIL_3021102:
                    handleHotelDetail(invoiceDtoWrapper, context);
                    return;
                case PcxConstant.TRAVEL_DETAIL_3021112:
                    handleTraffic(invoiceDtoWrapper, context);
                    return;
            }
        }

        //如果明细中的费用类型和事项的费用类型匹配出不唯一一条，则不能处理费用转换
        for (PcxRollInvoiceDto.Detail detail : detailList) {
            List<PcxBasExpType> detailMatchExpTypeList = getMyExpTypeInItemExpTypeList(detail.getExpTypeList(), context);
            if (detailMatchExpTypeList.size() != 1){
                invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
                return;
            }
        }

        invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());

        for (PcxRollInvoiceDto.Detail detail : detailList) {
            List<PcxBasExpType> detailMatchExpTypeList = getMyExpTypeInItemExpTypeList(detail.getExpTypeList(), context);
            handleOtherExp(invoiceDtoWrapper, context, detail, detailMatchExpTypeList.get(0));
        }
    }

    private void handleTraffic(InvoiceDtoWrapper<PcxRollInvoiceDto, PcxBillExpBase, PcxBillExpDetailBase> invoiceDtoWrapper,
                               ProcessContext context) {
        PcxRollInvoiceDto dto = invoiceDtoWrapper.getDto();
        List<PcxBillExpDetailTravel> travelDetailList = new ArrayList<>();
        List<ExpDetailDTO> billExpDetailList = dto.getBillExpDetailList();
        billExpDetailList = billExpDetailList.stream()
                .filter(item->item.getExpenseTypeCode().equals(PcxConstant.TRAVEL_DETAIL_3021112))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(billExpDetailList)){
            disposeInCityTraffic(travelDetailList, billExpDetailList, context, dto);

        }else if (CollectionUtils.isNotEmpty(dto.getFileList())){
            disposeInCityFile(travelDetailList, dto, context);
        }

        if (CollectionUtils.isEmpty(travelDetailList)){
            //如果行程单和费用明细都没有，则生成一个待确认的明细
            PcxBillExpDetailTravel travel = new PcxBillExpDetailTravel();
            travel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021112);
            travel.setEcsAmt(dto.getTotalAmt());
            travelDetailList.add(travel);
        }

        //把明细放入wrapper即使是不完全的明细
        if (CollectionUtils.isNotEmpty(travelDetailList)){
            for (PcxBillExpDetailTravel travel : travelDetailList) {
                invoiceDtoWrapper.addExpDetail(travel);
            }
        }
        fillDefaultClaimant(travelDetailList, context, dto);
        boolean isConfirm = checkIsConfirm(travelDetailList, context, invoiceDtoWrapper);
        invoiceDtoWrapper.setFlag(isConfirm? InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode() : InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
    }

    private void disposeInCityFile(List<PcxBillExpDetailTravel> travelDetailList, PcxRollInvoiceDto dto, ProcessContext context) {
        List<Object> fileList = dto.getFileList();
        if (CollectionUtils.isNotEmpty(fileList)){
            for (Object o : fileList) {
                JSONObject json = JSON.parseObject(JSON.toJSONString(o));
                String ecsBillType = json.getString(PcxConstant.ECS_BILL_TYPE_CODE);
                if (!PcxConstant.ATTACH_FILE_TAXI.equals(ecsBillType)){
                    continue;
                }
                PcxFileTaxiDto traffic = JSON.parseObject(json.toJSONString(), PcxFileTaxiDto.class);
                String trafficTime = transDateTime(traffic.getStartTime());
                if (CollectionUtils.isEmpty(traffic.getDetailList())){
                    continue;
                }
                for (PcxFileTaxiDto.Detail taxiDetail : traffic.getDetailList()) {
                    PcxBillExpDetailTravel taxi = new PcxBillExpDetailTravel();
                    taxi.setSource(BillExpDetailSourceEnum.ECS.getCode());
                    MadArea madArea = getMadAreaByCityName(taxiDetail.getCityName(), context);
                    taxi.setId(IDGenerator.id());
                    if (Objects.nonNull(madArea)){
                        taxi.setEndCity(madArea.getAreaName());
                        taxi.setEndCityCode(madArea.getAreaCode());
                    }
                    String pickUpTime = transDateTime(taxiDetail.getPickUpTime());
                    taxi.setStartTime(StringUtil.isNotEmpty(pickUpTime) ? pickUpTime: trafficTime);
                    taxi.setStartPlace(taxiDetail.getStartPlace());
                    taxi.setEndPlace(taxiDetail.getEndPlace());
                    taxi.setCarType(taxiDetail.getCarType());
                    taxi.setMileage(taxiDetail.getMileage());

                    taxi.setEcsAmt(taxiDetail.getDetailAmt());
                    taxi.setInputAmt(taxiDetail.getDetailAmt());

                    taxi.setFiscal(context.getFiscal());
                    taxi.setMofDivCode(context.getMofDivCode());
                    taxi.setAgyCode(context.getAgyCode());
                    taxi.setTenantId(context.getTenantId());
                    taxi.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021112);
                    taxi.setEcsRelItemName(dto.getBillDescpt());
                    travelDetailList.add(taxi);
                }
            }
        }
    }

    private void disposeInCityTraffic(List<PcxBillExpDetailTravel> travelDetailList,
                                      List<ExpDetailDTO> billExpDetailList,
                                      ProcessContext context,
                                      PcxRollInvoiceDto dto) {
        int index = 0;
        for (ExpDetailDTO expDetail : billExpDetailList) {
            List<EmpInfo> passengerInfos = getPassengerInfo(expDetail.getPassengerName());

            List<CityInfo> cityInfoList = getCityInfoList(expDetail.getCityName());

            PcxBillExpDetailTravel taxi = new PcxBillExpDetailTravel();
            if (CollectionUtils.isNotEmpty(cityInfoList)){
                CityInfo cityInfo = cityInfoList.get(0);
                taxi.setEndCity(cityInfo.getName());
                taxi.setEndCityCode(cityInfo.getCode());
            }

            if (CollectionUtils.isNotEmpty(passengerInfos)){
                EmpInfo passengerInfo = passengerInfos.get(0);
                if(StringUtil.isNotEmpty(passengerInfo.getEmpName(),passengerInfo.getEmpType())){
                    taxi.setEmpName(passengerInfo.getEmpName());
                    taxi.setEmpCode(passengerInfo.getEmpCode());
                    taxi.setEmpType(passengerInfo.getEmpType());
                    if (passengerInfo.getEmpType().equals(PcxConstant.EMP_TYPE_INNER)){
                        EmpMsg empMsg = queryEmpMsgByEmpCode(passengerInfo.getEmpCode(), passengerInfo.getEmpName(), context);
                        taxi.setEmpType(empMsg.getEmpType());
                        if (StringUtil.isNotEmpty(empMsg.getBudLevel())){
                            taxi.setBudLevel(empMsg.getBudLevel());
                            taxi.setIsSubsidy(empMsg.getIsSubsidy());
                        } else if (Objects.nonNull(context.getDefaultCostControlLevel())) {
                            taxi.setBudLevel(context.getDefaultCostControlLevel().getCostControlCode());
                            taxi.setIsSubsidy(Objects.nonNull(passengerInfo.getIsSubsidy()) ? passengerInfo.getIsSubsidy() : 1);
                        }
                    } else if (Objects.nonNull(context.getDefaultCostControlLevel())) {
                        taxi.setBudLevel(context.getDefaultCostControlLevel().getCostControlCode());
                        taxi.setIsSubsidy(Objects.nonNull(passengerInfo.getIsSubsidy()) ? passengerInfo.getIsSubsidy() : 1);
                    }
                }else{
                    EmpMsg empMsg = queryEmpMsg(passengerInfo.getEmpName(), context);
                    taxi.setEmpName(empMsg.getEmpName());
                    taxi.setEmpCode(empMsg.getEmpCode());
                    taxi.setEmpType(empMsg.getEmpType());
                    taxi.setBudLevel(empMsg.getBudLevel());
                    taxi.setIsSubsidy(empMsg.getIsSubsidy());
                }
            }

            taxi.setId(IDGenerator.id());
            taxi.setStartTime(transDateTime(expDetail.getCheckInTime()));
            taxi.setFinishTime(transDateTime(expDetail.getCheckOutTime()));


            taxi.setEcsAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
            taxi.setInputAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
            taxi.setSource(BillExpDetailSourceEnum.ECS.getCode());

            taxi.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021112);
            taxi.setEcsRelItemName(dto.getBillDescpt());
            taxi.setRemark(expDetail.getRemark());
            taxi.setFiscal(context.getFiscal());
            taxi.setAgyCode(context.getAgyCode());
            taxi.setMofDivCode(context.getMofDivCode());
            taxi.setTenantId(context.getTenantId());
            travelDetailList.add(taxi);
            index ++;
        }
    }


    private void handleHotelDetail(InvoiceDtoWrapper<PcxRollInvoiceDto, PcxBillExpBase,
            PcxBillExpDetailBase> invoiceDtoWrapper, ProcessContext context) {

        PcxRollInvoiceDto dto = invoiceDtoWrapper.getDto();
        List<PcxBillExpDetailTravel> travelDetailList = new ArrayList<>();
        List<ExpDetailDTO> billExpDetailList = dto.getBillExpDetailList();
        PcxRollInvoiceDto.Detail detail = dto.getDetailList().get(0);
        billExpDetailList = billExpDetailList.stream()
                .filter(item->item.getExpenseTypeCode()
                        .equals(PcxConstant.TRAVEL_DETAIL_3021102))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(billExpDetailList)){
            disposeHotelExpDetail(travelDetailList, billExpDetailList, context, dto, detail);
        }else{
            List<Object> fileList = dto.getFileList();
            if (CollectionUtils.isNotEmpty(fileList)){
                disposeHotelFile(travelDetailList, fileList, context, dto);
            }
        }
        if (CollectionUtils.isEmpty(travelDetailList)){
            //如果行程单和费用明细都没有，则生成一个待确认的明细
            PcxBillExpDetailTravel travel = new PcxBillExpDetailTravel();
            travel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021102);
            travel.setEcsAmt(dto.getTotalAmt());
            travelDetailList.add(travel);
        }

        if (CollectionUtils.isNotEmpty(travelDetailList)){
            for (PcxBillExpDetailTravel travel : travelDetailList) {
                invoiceDtoWrapper.addExpDetail(travel);
            }
        }
        fillDefaultClaimant(travelDetailList, context, dto);

        boolean isConfirm = checkIsConfirm(travelDetailList, context, invoiceDtoWrapper);
        if (isConfirm && CollectionUtils.isNotEmpty(travelDetailList)){
            invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
        }else{
            //如果是待确认的票，则把外部人员名称设置成空字符串，因为待确认的票在外面不会生成外部人员编码
            for (PcxBillExpDetailTravel detailTravel : travelDetailList) {
                if (Objects.equals(PcxConstant.EMP_TYPE_OUTER, detailTravel.getEmpType())){
                    detailTravel.setEmpName("");
                }
            }
            invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
        }
    }

    private void disposeHotelFile(List<PcxBillExpDetailTravel> travelDetailList,
                                  List<Object> fileList,
                                  ProcessContext context,
                                  PcxRollInvoiceDto dto) {
        for (Object o : fileList) {
            JSONObject json = JSON.parseObject(JSON.toJSONString(o));
            String ecsBillType = json.getString(PcxConstant.ECS_BILL_TYPE_CODE);
            if (!PcxConstant.ATTACH_FILE_HOTEL.equals(ecsBillType)){
                continue;
            }
            PcxFileHotelDto hotelFile = JSON.parseObject(json.toJSONString(), PcxFileHotelDto.class);
            String[] passArr = new String[]{""};
            if (StringUtil.isNotEmpty(hotelFile.getPassName())){
                passArr = hotelFile.getPassName().split("/");
            }

            String hotelGroup = UUID.randomUUID().toString();
            int index = 0;
            for (String passName : passArr) {
                PcxBillExpDetailTravel hotel = new PcxBillExpDetailTravel();
                MadArea madArea = getMadAreaByCityName(hotelFile.getCityName(), context);
                if (Objects.nonNull(madArea)){
                    hotel.setEndCity(madArea.getAreaName());
                    hotel.setEndCityCode(madArea.getAreaCode());
                }
                hotel.setId(IDGenerator.id());

                String startTime = "";
                String endTime = "";
                if (StringUtil.isNotEmpty(transDateTime(hotelFile.getDeptTime())) && StringUtil.isNotEmpty(transDateTime(hotelFile.getDestTime()))){
                    Pair<String, String> startFinishTime = calculateStartAndFinishTime(hotelFile.getDeptTime(), hotelFile.getDestTime());
                    startTime = startFinishTime.getLeft();
                    endTime = startFinishTime.getRight();
                }
                hotel.setStartTime(startTime);
                hotel.setFinishTime(endTime);
                hotel.setHotelGroup(hotelGroup);
                hotel.setSource(BillExpDetailSourceEnum.ECS.getCode());

                EmpMsg empMsg = queryEmpMsg(passName, context);
                hotel.setEmpCode(empMsg.getEmpCode());
                hotel.setEmpName(empMsg.getEmpName());
                hotel.setEmpType(empMsg.getEmpType());
                hotel.setBudLevel(empMsg.getBudLevel());
                hotel.setIsSubsidy(empMsg.getIsSubsidy());

                hotelFile.setEmpCode(empMsg.getEmpCode());
                hotelFile.setEmpName(empMsg.getEmpName());


                hotel.setEcsAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
                hotel.setInputAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
                index ++;
                long daysDifference = TravelTripProcessor.getDiffDays(hotelFile.getDeptTime(), hotelFile.getDestTime());

                hotel.setTravelDays((double)daysDifference);
                hotel.setFiscal(context.getFiscal());
                hotel.setMofDivCode(context.getMofDivCode());
                hotel.setAgyCode(context.getAgyCode());
                hotel.setTenantId(context.getTenantId());
                hotel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021102);
                hotel.setEcsRelItemName(dto.getBillDescpt());
                travelDetailList.add(hotel);
            }
        }
    }

    private void disposeHotelExpDetail(List<PcxBillExpDetailTravel> travelDetailList,
                                       List<ExpDetailDTO> billExpDetailList,
                                       ProcessContext context,
                                       PcxRollInvoiceDto dto,
                                       PcxRollInvoiceDto.Detail detail) {
        int index = 0;
        int seq = 1;
        for (ExpDetailDTO expDetail : billExpDetailList) {
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");

            List<EmpInfo> passengerInfos = getPassengerInfo(expDetail.getPassengerName());

            List<CityInfo> cityInfoList = getCityInfoList(expDetail.getCityName());

            String startTime = getStartOrFinishTime(transDateTime(expDetail.getCheckInTime()), dto.getBillDate());
            String endTime = getStartOrFinishTime(transDateTime(expDetail.getCheckOutTime()), dto.getBillDate());

            if (CollectionUtils.isNotEmpty(passengerInfos)){
                for (EmpInfo passengerInfo : passengerInfos) {
                    PcxBillExpDetailTravel hotel = new PcxBillExpDetailTravel();
                    hotel.setId(IDGenerator.id());
                    hotel.setStartTime(startTime);
                    hotel.setFinishTime(endTime);
                    if(StringUtil.isNotEmpty(passengerInfo.getEmpName(),passengerInfo.getEmpType())){
                        hotel.setEmpName(passengerInfo.getEmpName());
                        hotel.setEmpCode(passengerInfo.getEmpCode());
                        hotel.setEmpType(passengerInfo.getEmpType());
                        if (passengerInfo.getEmpType().equals(PcxConstant.EMP_TYPE_INNER)){
                            EmpMsg empMsg = queryEmpMsgByEmpCode(passengerInfo.getEmpCode(), passengerInfo.getEmpName(), context);
                            hotel.setEmpType(empMsg.getEmpType());
                            if (StringUtil.isNotEmpty(empMsg.getBudLevel())){
                                hotel.setBudLevel(empMsg.getBudLevel());
                                hotel.setIsSubsidy(empMsg.getIsSubsidy());
                            } else if (Objects.nonNull(context.getDefaultCostControlLevel())) {
                                hotel.setBudLevel(context.getDefaultCostControlLevel().getCostControlCode());
                                hotel.setIsSubsidy(Objects.nonNull(passengerInfo.getIsSubsidy()) ? passengerInfo.getIsSubsidy() : 1);
                            }
                        } else if (Objects.nonNull(context.getDefaultCostControlLevel())) {
                            hotel.setBudLevel(context.getDefaultCostControlLevel().getCostControlCode());
                            hotel.setIsSubsidy(Objects.nonNull(passengerInfo.getIsSubsidy()) ? passengerInfo.getIsSubsidy() : 1);
                        }
                    }else{
                        EmpMsg empMsg = queryEmpMsg(passengerInfo.getEmpName(), context);
                        hotel.setEmpName(empMsg.getEmpName());
                        hotel.setEmpCode(empMsg.getEmpCode());
                        hotel.setEmpType(empMsg.getEmpType());
                        hotel.setBudLevel(empMsg.getBudLevel());
                        hotel.setIsSubsidy(empMsg.getIsSubsidy());
                    }


                    hotel.setHotelGroup(uuid);
                    hotel.setHotelSeq(seq);
                    hotel.setEcsAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
                    hotel.setInputAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
                    hotel.setTaxRate(index == 0 ? detail.getTaxRate() : BigDecimal.ZERO);
                    hotel.setTaxAmt(index == 0 ? detail.getTaxAmt() : BigDecimal.ZERO);
                    hotel.setSource(BillExpDetailSourceEnum.ECS.getCode());

                    long daysDifference = TravelTripProcessor.getDiffDays(hotel.getStartTime(), hotel.getFinishTime());

                    hotel.setTravelDays((double)daysDifference);
                    if (CollectionUtils.isNotEmpty(cityInfoList)){
                        CityInfo cityInfo = cityInfoList.get(0);
                        hotel.setEndCity(cityInfo.getName());
                        hotel.setEndCityCode(cityInfo.getCode());
                    }

                    hotel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021102);
                    hotel.setEcsRelItemName(dto.getBillDescpt());
                    hotel.setRemark(expDetail.getRemark());
                    hotel.setFiscal(context.getFiscal());
                    hotel.setAgyCode(context.getAgyCode());
                    hotel.setMofDivCode(context.getMofDivCode());
                    hotel.setTenantId(context.getTenantId());
                    travelDetailList.add(hotel);
                    index ++;
                }
            }else{
                PcxBillExpDetailTravel hotel = new PcxBillExpDetailTravel();
                hotel.setId(IDGenerator.id());
                hotel.setStartTime(startTime);
                hotel.setFinishTime(endTime);
                hotel.setHotelGroup(uuid);
                hotel.setHotelSeq(seq);
                hotel.setEcsAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
                hotel.setInputAmt(index == 0 ? dto.getTotalAmt(): BigDecimal.ZERO);
                hotel.setTaxRate(index == 0 ? detail.getTaxRate() : BigDecimal.ZERO);
                hotel.setTaxAmt(index == 0 ? dto.getTotalAmt().multiply(detail.getTaxRate()).setScale(2, RoundingMode.HALF_UP): BigDecimal.ZERO);
                hotel.setSource(BillExpDetailSourceEnum.ECS.getCode());

                long daysDifference = TravelTripProcessor.getDiffDays(hotel.getStartTime(), hotel.getFinishTime());

                hotel.setTravelDays((double)daysDifference);
                if (CollectionUtils.isNotEmpty(cityInfoList)){
                    CityInfo cityInfo = cityInfoList.get(0);
                    hotel.setEndCity(cityInfo.getName());
                    hotel.setEndCityCode(cityInfo.getCode());
                }

                hotel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021102);
                hotel.setEcsRelItemName(dto.getBillDescpt());
                hotel.setRemark(expDetail.getRemark());
                hotel.setFiscal(context.getFiscal());
                hotel.setAgyCode(context.getAgyCode());
                hotel.setMofDivCode(context.getMofDivCode());
                hotel.setTenantId(context.getTenantId());
                travelDetailList.add(hotel);
                index ++;
            }

            seq ++;
        }
    }

    /**
     * 默认酒店12点退房，14点入住
     * 如果是同一天的就设置成12点入住，14点退房
     * @param checkInTime
     * @param checkOutTime
     * @return
     */
    private Pair<String, String> calculateStartAndFinishTime(String checkInTime, String checkOutTime) {
        return Pair.of(checkInTime.substring(0,10), checkOutTime.substring(0,10));
    }

    private String getDestTimeHotel(String destTime) {
        if (StringUtil.isNotEmpty(destTime) && destTime.length() == 10){
            return String.format("%s %s", destTime, "12:00");
        }
        return destTime.substring(0, 16);
    }


    public MadArea getMadAreaByCityName(String cityName, ProcessContext context){
        if (Objects.isNull(context.getCityNameMap())){
            return null;
        }
        return context.getCityNameMap().getOrDefault(cityName, context.getCityNameMap().get(cityName+"市"));
    }

    /**
     * 处理明细项
     * @param detail
     */
    private void handleOtherExp(InvoiceDtoWrapper<PcxRollInvoiceDto, PcxBillExpBase, PcxBillExpDetailBase> invoiceDtoWrapper
            , ProcessContext context, PcxRollInvoiceDto.Detail detail, PcxBasExpType basExpType) {
        PcxRollInvoiceDto dto = invoiceDtoWrapper.getDto();
        // 特殊的费用处理
        if (basExpType.getIsRefine() == 1) {
            PcxBillExpDetailBase expDetailCommon = ExpenseBeanUtil.getEntityDetailBean(basExpType.getParentCode());
            expDetailCommon.setId(IDGenerator.id());
            expDetailCommon.setExpDetailCode(basExpType.getExpenseCode());
            expDetailCommon.setEcsAmt(detail.getItemPrice().multiply(new BigDecimal(detail.getItemUnit())));
            expDetailCommon.setInputAmt(detail.getItemPrice().multiply(new BigDecimal(detail.getItemUnit())));
            //新增其他额外的信息
            expDetailCommon.setFiscal(context.getFiscal());
            expDetailCommon.setMofDivCode(context.getMofDivCode());
            expDetailCommon.setAgyCode(context.getAgyCode());
            expDetailCommon.setEcsRelItemName(detail.getItemName());
            invoiceDtoWrapper.addExpDetail(expDetailCommon);
            //数电票，代订飞机票，匹配出城市间交通费，只有金额能用，其他需要补充信息
            if (basExpType.getExpenseCode().equals(PcxConstant.TRAVEL_DETAIL_3021101)){
                invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
            }
        } else { // 不是明细的
            PcxBillExpBase expCommon = ExpenseBeanUtil.getEntityBean(basExpType.getExpenseCode());
            expCommon.setExpenseCode(basExpType.getExpenseCode());
            expCommon.setExpenseName(basExpType.getExpenseName());
            expCommon.setInputAmt(detail.getItemPrice().multiply(new BigDecimal(detail.getItemUnit())));
            expCommon.setFiscal(context.getFiscal());
            expCommon.setMofDivCode(context.getMofDivCode());
            expCommon.setAgyCode(context.getAgyCode());
            expCommon.setEcsRelItemName(detail.getItemName());

            invoiceDtoWrapper.addExpense(expCommon);
        }
    }
}
