package com.pty.pcx.service.impl.positionblock;

import cn.hutool.core.map.MapUtil;
import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.positionblock.IPcxBlockService;
import com.pty.pcx.api.positionblock.IPcxPositionBlockService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.FundPositionEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.enu.block.BlockBeanEnum;
import com.pty.pcx.common.util.BudgetCtrlUtil;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.positionblock.PcxPositionBlockDao;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.qo.positionblock.PcxPositionBlockQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.util.ValidUtil;
import com.pty.pcx.vo.ApprovalProcessDefinitionVO;
import com.pty.pcx.vo.PaOptionVO;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;
import com.pty.pcx.vo.positionblock.PcxPositionBlockVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.SpringUtil;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import com.pty.workflow2.extend.pcx.PcxProcessDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName: PcxPositionBlockServiceImpl
 * @Description: 岗位样式块接口
 * @Date: 2024/11/23  下午3:52
 * @Author: wangbao
 **/
@Service
@Indexed
@Slf4j
public class PcxPositionBlockServiceImpl implements IPcxPositionBlockService {

    @Autowired
    private PcxPositionBlockDao pcxPositionBlockDao;

    @Autowired
    private IPcxBasItemService pcxBasItemService;

    @Autowired
    private IPcxBasExpTypeService pcxBasExpTypeService;

    @Autowired
    private IBusinessRuleOptionService businessRuleOptionService;

    @Autowired
    private IProcessService processService;

    @Autowired
    private BillMainService billMainService;

    @Override
    public CheckMsg<?> select(PcxPositionBlockQO pcxPositionBlockQO)
    {
        List<PcxPositionBlockVO> pcxPositionBlockVOList = pcxPositionBlockDao.select(pcxPositionBlockQO);
        return CheckMsg.success(pcxPositionBlockVOList);
    }
    public static final String DEPARTMENT_CODE = "departmentCode";

    private PcxBasItemVO getPcxBasItemVO(PcxPositionBlockQO pcxPositionBlockQO) {
        PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
        pcxBasItemQO.setItemCode(pcxPositionBlockQO.getItemCode());
        pcxBasItemQO.setMofDivCode(pcxPositionBlockQO.getMofDivCode());
        pcxBasItemQO.setAgyCode(pcxPositionBlockQO.getAgyCode());
        pcxBasItemQO.setFiscal(pcxPositionBlockQO.getFiscal());
        pcxBasItemQO.setTenantId(StringUtil.isEmpty(pcxPositionBlockQO.getTenantId()) ? PtyContext.getTenantId() : pcxPositionBlockQO.getTenantId());
        pcxBasItemQO.setBilltypeCode(pcxPositionBlockQO.getBillFuncCode());
        PcxBasItemVO pcxBasItemVO = pcxBasItemService.selectByItemCode(pcxBasItemQO);
        // 如果是通用类报销
        if(PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxPositionBlockQO.getItemCode())){
            List<String> expenseCodes = CollectionUtil.isEmpty(pcxPositionBlockQO.getExpenseCodes())?new ArrayList<>():pcxPositionBlockQO.getExpenseCodes();
            if(CollectionUtil.isEmpty(pcxPositionBlockQO.getExpenseCodes())){
                expenseCodes.add(PcxConstant.UNIVERSAL_EXPENSE_CODE);
            }
            expenseCodes = expenseCodes.stream().distinct().collect(Collectors.toList());
            pcxBasItemVO.setExpenseCodes(expenseCodes);
        }
        return pcxBasItemVO;
    }

    private List<PcxPositionBlockVO> getPcxPositionBlockVOS(PcxPositionBlockQO pcxPositionBlockQO) {
        PcxPositionBlockQO param = new PcxPositionBlockQO();
        param.setFiscal(pcxPositionBlockQO.getFiscal());
        param.setMofDivCode(pcxPositionBlockQO.getMofDivCode());
        param.setBillFuncCode(pcxPositionBlockQO.getBillFuncCode());
        // todo mashaojie -20241129 先只查询系统级别的
        param.setAgyCode(PubConstant.SYS_DEFAULT_AGY_CODE);
        param.setTenantId(StringUtil.isEmpty(pcxPositionBlockQO.getTenantId()) ? PtyContext.getTenantId() : pcxPositionBlockQO.getTenantId());
        param.setPositionCode(pcxPositionBlockQO.getPositionCode());
        return pcxPositionBlockDao.select(param);
    }

    @Override
    public CheckMsg<?> getBlockInfo(PcxPositionBlockQO pcxPositionBlockQO) {
        ValidUtil.checkEmptyStr(pcxPositionBlockQO.getBillFuncCode(), "单据类型不能为空");
        ValidUtil.checkEmptyStr(pcxPositionBlockQO.getPositionCode(), "岗位编码不能为空");
        ValidUtil.checkEmptyStr(pcxPositionBlockQO.getItemCode(), "事项编码不能为空");

        // 获取事项类型、费用类型信息
        PcxBasItemVO pcxBasItemVO = getPcxBasItemVO(pcxPositionBlockQO);
        if (ObjectUtils.isEmpty(pcxBasItemVO)) {
            return CheckMsg.success(Collections.emptyList());
        }
        // 获取岗位样式块信息
        List<PcxPositionBlockVO> pcxPositionBlockVOS = getPcxPositionBlockVOS(pcxPositionBlockQO);
        // TODO 开发阶段 暂时去掉费用明细专属属性 条件为（费用类型30212 申请单apply）
        // TODO 从 pcxBasItemVO 中的 expenseCodes 判断是否有30212 ，pcxPositionBlockQO.getBillFuncCode() 判断岗位是否为 apply，然后删除pcxPositionBlockVOS 中 classifyCode 等于expenseDetail的数据
        if (CollectionUtil.isNotEmpty(pcxBasItemVO.getExpenseCodes()) && pcxBasItemVO.getExpenseCodes().contains(PcxConstant.ABROAD_EXPENSE_30212)
                && BillFuncCodeEnum.APPLY.getCode().equals(pcxPositionBlockQO.getBillFuncCode())) {
            pcxPositionBlockVOS = pcxPositionBlockVOS.stream().filter(item -> !BlockBeanEnum.EXPENSE_DETAIL.getClassifyCode().equals(item.getClassifyCode())).collect(Collectors.toList());
        }

        if (CollectionUtil.isEmpty(pcxPositionBlockVOS)) {
            return CheckMsg.fail().setMsgInfo("没有岗位样式预置数据,请先预制岗位样式数据");
        }
        // 查询费用类型
        List<String> expenseCodes = pcxBasItemVO.getExpenseCodes();
        Map<String, PcxBasExpType> pcxBasExpTypeMap = getExpenseTypeMap(pcxPositionBlockQO, expenseCodes);
        // 查询费用的费用明细
        Map<String, List<PcxBasExpType>> expenseDetails = getExpenseDetails(pcxPositionBlockQO,expenseCodes);
        // 拼装样式块信息
        List<PcxBlockVO> pcxBlockVOS = buildBlockVOList(pcxPositionBlockQO, pcxPositionBlockVOS, pcxBasExpTypeMap,expenseDetails,pcxBasItemVO);
        // 排序并返回结果
        List<PcxBlockVO> resultList = sortResult(pcxBlockVOS, pcxPositionBlockVOS, new ArrayList<>(pcxBasExpTypeMap.values()));
        return CheckMsg.success(resultList);
    }

    @Override
    public CheckMsg<?> getVersion(PcxPositionBlockQO pcxPositionBlockQO) {
        PcxPositionBlockQO param = new PcxPositionBlockQO();
        BeanUtils.copyProperties(pcxPositionBlockQO, param);
        param.setAgyCode(PubConstant.SYS_DEFAULT_AGY_CODE);
        param.setTenantId(StringUtil.isNotBlank(pcxPositionBlockQO.getTenantId()) ? pcxPositionBlockQO.getTenantId() : PtyContext.getTenantId());
        List<String> version = pcxPositionBlockDao.getVersion(param);
        if(CollectionUtil.isEmpty(version)){
            return CheckMsg.fail().setMsgInfo("没有岗位样式预置数据,请先预制岗位样式数据");
        }
        String result = String.join(".", version);
        // mashaojie-20250117 目前只有通用报销存在需要前端传入费用类型的情况，所以限定在通用报销的情况下；
        // 前端可以存储500m以内的数据
        if(PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxPositionBlockQO.getItemCode()) && CollectionUtil.isNotEmpty(pcxPositionBlockQO.getExpenseCodes())){
            List<String> sortedExpenses = pcxPositionBlockQO.getExpenseCodes().stream().filter(item -> !PcxConstant.UNIVERSAL_EXPENSE_CODE.equals(item)).sorted()
                    .collect(Collectors.toList());
            result = result + "." + String.join(".", sortedExpenses)+"."+PcxConstant.UNIVERSAL_EXPENSE_CODE;
        }
        return CheckMsg.success().setData(result);
    }

    // 查询费用的费用明细
    private Map<String, List<PcxBasExpType>> getExpenseDetails(PcxPositionBlockQO pcxPositionBlockQO, List<String> expenseCodes) {
        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        pcxBasExpTypeQO.setMofDivCode(pcxPositionBlockQO.getMofDivCode());
        pcxBasExpTypeQO.setFiscal(pcxPositionBlockQO.getFiscal());
        pcxBasExpTypeQO.setAgyCode(pcxPositionBlockQO.getAgyCode());
        pcxBasExpTypeQO.setTenantId(StringUtil.isEmpty(pcxPositionBlockQO.getTenantId()) ? PtyContext.getTenantId() : pcxPositionBlockQO.getTenantId());
        //scc 费用类型也会作为一级明细展示，所以去掉条件
        //pcxBasExpTypeQO.setIsRefine(PubConstant.LOGIC_TRUE);
        pcxBasExpTypeQO.setLastCodes(expenseCodes);
        List<PcxBasExpType> pcxBasExpTypeList = pcxBasExpTypeService.selectByQO(pcxBasExpTypeQO);
        if(PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxPositionBlockQO.getItemCode())){
            // 通用费用报销需要填充，一个费用明细
            PcxBasExpType pcxBasExpType = new PcxBasExpType();
            pcxBasExpType.setExpenseCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
            pcxBasExpType.setExpenseName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
            pcxBasExpType.setLastCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
            pcxBasExpTypeList.add(pcxBasExpType);
        }
        return pcxBasExpTypeList.stream()
                .collect(Collectors.groupingBy(PcxBasExpType::getLastCode));
    }

    // 获取费用类型的映射
    private Map<String, PcxBasExpType> getExpenseTypeMap(PcxPositionBlockQO pcxPositionBlockQO, List<String> expenseCodes) {
        PcxBasExpTypeQO params = new PcxBasExpTypeQO();
        params.setMofDivCode(pcxPositionBlockQO.getMofDivCode());
        params.setAgyCode(pcxPositionBlockQO.getAgyCode());
        params.setFiscal(pcxPositionBlockQO.getFiscal());
        params.setTenantId(pcxPositionBlockQO.getTenantId());
        params.setExpTypeCodes(expenseCodes);
        List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeService.selectByQO(params);
        // 用LinkedHashMap 保证顺序
        Map<String, PcxBasExpType> expenseTypeMap = new LinkedHashMap<>();
        Map<String, PcxBasExpType> pcxBasExpTypeMap = pcxBasExpTypes.stream()
                .collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity(), (oldData, newData) -> oldData));
        for (String expenseCode : expenseCodes) {
            if(expenseCode.equals(PcxConstant.UNIVERSAL_EXPENSE_CODE)){
                PcxBasExpType pcxBasExpType = new PcxBasExpType();
                pcxBasExpType.setExpenseCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
                expenseTypeMap.put(PcxConstant.UNIVERSAL_EXPENSE_CODE, pcxBasExpType);
                continue;
            }
            PcxBasExpType pcxBasExpType = pcxBasExpTypeMap.get(expenseCode);
            if (!ObjectUtils.isEmpty(pcxBasExpType)) {
                expenseTypeMap.put(expenseCode, pcxBasExpType);
            }
        }
        return expenseTypeMap;
    }

    // 构建样式块列表
    private List<PcxBlockVO> buildBlockVOList(
            PcxPositionBlockQO pcxPositionBlockQO,
            List<PcxPositionBlockVO> pcxPositionBlockVOS,
            Map<String, PcxBasExpType> pcxBasExpTypeMap,
            Map<String, List<PcxBasExpType>> expenseDetails,
            PcxBasItemVO pcxBasItemVO) {
        List<PcxBlockVO> pcxBlockVOS = new ArrayList<>();
        PcxBlockCondQO pcxBlockCondQO = new PcxBlockCondQO();
        BeanUtils.copyProperties(pcxPositionBlockQO, pcxBlockCondQO);
        pcxBlockCondQO.setExpenseMap(pcxBasExpTypeMap);
        pcxBlockCondQO.setExpenseDetailMap(expenseDetails);
        pcxBlockCondQO.setWfNodes(getWfNodes(pcxPositionBlockQO));
        pcxBlockCondQO.setPcxBasItemVO(pcxBasItemVO);
        assembleBusinessRuleOption(pcxBlockCondQO);
        assembleBillMain(pcxBlockCondQO);
        for (PcxPositionBlockVO pcxPositionBlockVO : pcxPositionBlockVOS) {
            String classifyCode = pcxPositionBlockVO.getClassifyCode();
            if (StringUtil.isEmpty(classifyCode)) {
                continue;
            }
            // 根据事项的is_ctrl_budget控制块儿的显示
            if(!BudgetCtrlUtil.isBudgetCtrlEnabled(pcxBasItemVO.getBudgetCtrl(),pcxPositionBlockQO.getBillFuncCode()) &&
                    (BlockBeanEnum.BUDGET.getClassifyCode().equals(classifyCode)
                            || BlockBeanEnum.FUND_SOURCE.getClassifyCode().equals(classifyCode))){
                log.info("当前事项不控制预算，跳过样式块:{}", classifyCode);
                continue;
            }
            pcxBlockCondQO.setClassifyCode(classifyCode);
            pcxBlockCondQO.setClassifyName(pcxPositionBlockVO.getClassifyName());
            // 根据费用类型控制块儿的显示
            if(!isShowSpecialBlock(pcxBlockCondQO)){
                continue;
            }
            // 根据岗位控制块儿的显示
            if(!isShowPositionBlock(pcxBlockCondQO)){
                continue;
            }
            // 增加根据option控制块儿的显示逻辑
            if(!isAssembleBlock(pcxBlockCondQO)){
                continue;
            }
            BlockBeanEnum blockBeanEnum = BlockBeanEnum.getBlockMap().get(classifyCode);
            if (blockBeanEnum == null) {
                continue;
            }
            IPcxBlockService bean = getBlockServiceBean(blockBeanEnum);
            if (bean == null) {
                log.error("未找到样式块服务Bean:{}", blockBeanEnum.getClassifyCode());
                continue;
            }
            pcxBlockCondQO.setArea(pcxPositionBlockVO.getArea());
            pcxBlockCondQO.setShowName(pcxPositionBlockVO.getShowName());
            pcxBlockCondQO.setPositionContext(pcxPositionBlockVO.getPositionContext());
            List<PcxBlockVO> blockInfo = bean.getBlockInfo(pcxBlockCondQO);
            if (CollectionUtil.isNotEmpty(blockInfo)) {
                //处理特殊的属性
                dealBlockProperties(pcxPositionBlockQO, pcxBasExpTypeMap, classifyCode, blockInfo);
                pcxBlockVOS.addAll(blockInfo);
            }
        }
        return pcxBlockVOS;
    }

    /**
     * 处理特殊属性
     * 如果费用申请设置为无申请费用，移除费用承担部门
     * @param pcxPositionBlockQO
     * @param pcxBasExpTypeMap
     * @param classifyCode
     * @param blockInfo
     */
    private void dealBlockProperties(PcxPositionBlockQO pcxPositionBlockQO, Map<String, PcxBasExpType> pcxBasExpTypeMap, String classifyCode, List<PcxBlockVO> blockInfo) {
        //当前块不是是专属属性跳过
        if( !PositionBlockEnum.SPECIFICITY.getCode().equals(classifyCode) || MapUtil.isEmpty(pcxBasExpTypeMap)) {
            return;
        }
        //获取所有费用类型的申请控制级别
        Set<Integer> applyCtrlLevels = pcxBasExpTypeMap.values().stream().map(PcxBasExpType::getApplyCtrlLevel).collect(Collectors.toSet());
        //如果当前是申请单，且所有费用类型的申请控制级别为无申请费用，移除费用承担部门
        if(BillFuncCodeEnum.APPLY.getCode().equals(pcxPositionBlockQO.getBillFuncCode())
                && applyCtrlLevels.size() == 1 && applyCtrlLevels.contains(0)){
            for (PcxBlockVO pcxBlockVO : blockInfo) {
                List<BlockPropertyVO> properties = pcxBlockVO.getProperties();
                if(CollectionUtil.isNotEmpty(properties)){
                    // 如果当前是费用承担部门，移除
                    properties.removeIf(propertyVO -> DEPARTMENT_CODE.equals(propertyVO.getFieldValue()));
                }
            }
        }

    }

    /**
     * 判断是否显示特殊块儿
     * @param pcxBlockCondQO
     * @return
     * false - 跳出
     * true - 继续后面的逻辑
     */
    private boolean isShowSpecialBlock(PcxBlockCondQO pcxBlockCondQO) {
        if (ObjectUtils.isEmpty(pcxBlockCondQO) || pcxBlockCondQO.getExpenseMap()==null || StringUtil.isEmpty(pcxBlockCondQO.getBillFuncCode())){
            return Boolean.FALSE;
        }
        String classifyCode = pcxBlockCondQO.getClassifyCode();
        // 如果是招待相关的块儿，判断是否有招待30217费用
        if(BlockBeanEnum.LETTER.getClassifyCode().equals(classifyCode) ||
                BlockBeanEnum.ARRANGE.getClassifyCode().equals(classifyCode) ||
                BlockBeanEnum.VISITORS.getClassifyCode().equals(classifyCode) ){
            return pcxBlockCondQO.getExpenseMap().containsKey(PcxConstant.TREAT_EXPENSE_30217);
        }
        // 如果是计划块儿，判断是否有招待30216或30215费用
        if(BlockBeanEnum.PLAN.getClassifyCode().equals(classifyCode)){
            PcxBasExpType meetingExpense = pcxBlockCondQO.getExpenseMap().get(PcxConstant.MEETING_EXPENSE_30215);
            if (meetingExpense != null && PubConstant.LOGIC_TRUE == meetingExpense.getIsNeedPlan()) {
                return true;
            }
            PcxBasExpType trainingExpense = pcxBlockCondQO.getExpenseMap().get(PcxConstant.TRAINING_EXPENSE_30216);
            return trainingExpense != null && PubConstant.LOGIC_TRUE == trainingExpense.getIsNeedPlan();
        }
        // 如果是招待费用类型，判断是否有招待费用类型
        return Boolean.TRUE;
    }

    /****
     * 组装单据信息
     * @param pcxBlockCondQO
     */
    private void assembleBillMain(PcxBlockCondQO pcxBlockCondQO) {
        String billId = pcxBlockCondQO.getBillId();
        if(StringUtil.isEmpty(billId)){
            return;
        }
        pcxBlockCondQO.setPcxBill(billMainService.view(billId));
    }

    /***
     * 根据option控制块儿的显示逻辑
     * @param cond
     * @return
     */
    private boolean isAssembleBlock(PcxBlockCondQO cond) {
        if(StringUtil.isNotBlank(cond.getClassifyCode())
                && !BlockBeanEnum.LOAN.getClassifyCode().equals(cond.getClassifyCode())
                && !BlockBeanEnum.CONTRACT.getClassifyCode().equals(cond.getClassifyCode())){
            return Boolean.TRUE;
        }
        // 检查借款或合同业务规则是否启用
        return isLoanOptionEnabled(cond) || isContractOptionEnabled(cond);
    }

    /**
     * 检查选项映射中是否包含启用借款的业务规则
     * 此方法验证选项映射是否满足以下条件：
     * 1. 选项映射不为空
     * 2. 包含借款业务规则选项键
     * 3. 借款业务规则选项值为逻辑真
     * @param cond 查询条件对象，包含选项映射信息
     * @return 是否包含启用借款业务规则的布尔值
     */
    private boolean isLoanOptionEnabled(PcxBlockCondQO cond) {
        return CollectionUtil.isNotEmpty(cond.getOptionMap())
                && cond.getOptionMap().containsKey(BusinessRuleEnum.BusinessOptionEnum.LOAN.getOptCode())
                && PubConstant.STR_LOGIC_TRUE.equals(
                cond.getOptionMap().get(BusinessRuleEnum.BusinessOptionEnum.LOAN.getOptCode()).getOptValue());
    }

    /**
     * 检查选项映射中是否包含启用合同的业务规则
     * 此方法验证选项映射是否满足以下条件：
     * 1. 选项映射不为空
     * 2. 包含合同业务规则选项键
     * 3. 合同业务规则选项值为逻辑真
     * @param cond 查询条件对象，包含选项映射信息
     * @return 是否包含启用合同业务规则的布尔值
     */
    private boolean isContractOptionEnabled(PcxBlockCondQO cond) {
        Collection<String> expenseCodes = getContractExpenseCodes(cond);

        return CollectionUtil.isNotEmpty(cond.getOptionMap())
                && cond.getOptionMap().containsKey(BusinessRuleEnum.BusinessOptionEnum.CONTRACT.getOptCode())
                && PubConstant.STR_LOGIC_TRUE.equals(
                cond.getOptionMap().get(BusinessRuleEnum.BusinessOptionEnum.CONTRACT.getOptCode()).getOptValue())
                && !expenseCodes.isEmpty();
    }

    private Collection<String> getContractExpenseCodes(PcxBlockCondQO cond) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setFiscal(Integer.parseInt(cond.getFiscal()));
        paOptionQO.setTenantId(StringUtil.getStringValue(cond.getTenantId(), PtyContext.getTenantId()));
        paOptionQO.setMofDivCode(cond.getMofDivCode());
        paOptionQO.setAgyCode(cond.getAgyCode());
        paOptionQO.setOptCode(BusinessRuleEnum.BusinessOptionEnum.CONTRACT_ITEM.getOptCode());

        List<PaOptionVO> agyOpt = businessRuleOptionService.getAgyOpt(paOptionQO);
        List<String> expenseCodes = agyOpt.stream()
                .map(PaOptionVO::getOptValue)
                .filter(Objects::nonNull)
                .flatMap(optValue -> Arrays.stream(optValue.split(",")))
                .collect(Collectors.toList());

        return CollectionUtils.intersection(expenseCodes, cond.getPcxBasItemVO().getExpenseCodes());
    }

    private void assembleBusinessRuleOption(PcxBlockCondQO pcxBlockCondQO) {
        if (ObjectUtils.isEmpty(pcxBlockCondQO)) {
            return;
        }
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setFiscal(Integer.parseInt(pcxBlockCondQO.getFiscal()));
        paOptionQO.setTenantId(StringUtil.getStringValue(pcxBlockCondQO.getTenantId(), PtyContext.getTenantId()));
        paOptionQO.setMofDivCode(pcxBlockCondQO.getMofDivCode());
        paOptionQO.setAgyCode(pcxBlockCondQO.getAgyCode());
        List<PaOptionVO> agyOpt = businessRuleOptionService.getAgyOpt(paOptionQO);
        if (CollectionUtil.isEmpty(agyOpt)) {
            return;
        }
        Map<String, PaOptionVO> optionMap = agyOpt.stream()
                .collect(Collectors.toMap(PaOptionVO::getOptCode, vo -> vo, (oldData, newData) -> oldData));
        pcxBlockCondQO.setOptionMap(optionMap);
    }

    /*****
     * 判断当前岗位是否有需要显示块儿
     * @param pcxBlockCondQO
     * @return
     */
    private boolean isShowPositionBlock(PcxBlockCondQO pcxBlockCondQO) {
        if (ObjectUtils.isEmpty(pcxBlockCondQO) || ObjectUtils.isEmpty(pcxBlockCondQO.getWfNodes())) {
            log.error("参数不正确:{}", pcxBlockCondQO.toString());
            return false;
        }
        String positionCode = getPositionCode(pcxBlockCondQO.getPositionCode());
        String classifyCode = pcxBlockCondQO.getClassifyCode();
        // 财务审核岗/财务总监岗拥有最高权限，所有块儿不做控制
        if(PositionEnum.isFinance(positionCode)){
            return true;
        }
        // mashaojie - 20250409 确定预算审核人拥有经费来源
        if(PcxNodeEnum.budget_confirm.getId().equals(positionCode) && BlockBeanEnum.FUND_SOURCE.getClassifyCode().equals(classifyCode)){
            return true;
        }
        if (!BlockBeanEnum.BUDGET.getClassifyCode().equals(classifyCode)
                && !BlockBeanEnum.FUND_SOURCE.getClassifyCode().equals(classifyCode)
                && !BlockBeanEnum.PAY_DETAIL.getClassifyCode().equals(classifyCode)) {
            return true;
        }
        String optValue = getOptionValue(pcxBlockCondQO, classifyCode);
        if (StringUtil.isEmpty(optValue)) {
            log.error("未设置预算录入岗位");
            return false;
        }
        if(BlockBeanEnum.FUND_SOURCE.getClassifyCode().equals(classifyCode)){
            FundPositionEnum fundPositionEnum = FundPositionEnum.fromCode(optValue);
            if(ObjectUtils.isEmpty(fundPositionEnum)){
                log.error("经费来源录入岗位不存在");
                return false;
            }
            if(PubConstant.STR_LOGIC_FALSE.equals(fundPositionEnum.getIsCarryProject())){
                return false;
            }
            optValue = getPositionCode(fundPositionEnum.getPosition());
        }
        List<ApprovalProcessDefinitionVO.DefinitionRow> wfNodes = pcxBlockCondQO.getWfNodes();
        if (CollectionUtil.isEmpty(wfNodes)) {
            log.error("当前单位未设置岗位流程");
            return false;
        }
        optValue = getPositionCode(optValue);
        if (optValue.equals(positionCode)) {
            return true;
        }
        List<String> wfStopCodes = wfNodes.stream()
                .map(ApprovalProcessDefinitionVO.DefinitionRow::getStepCode)
                .collect(Collectors.toList());

        int optCodeIndex = wfStopCodes.indexOf(optValue);
        if (optCodeIndex == -1) {
            log.error("岗位样式不存在");
            return false;
        }
        int positionCodeIndex = wfStopCodes.indexOf(positionCode);
        return positionCodeIndex > optCodeIndex;
    }
    private String getOptionValue(PcxBlockCondQO pcxBlockCondQO, String classifyCode) {
        Map<String, PaOptionVO> optionMap = pcxBlockCondQO.getOptionMap();
        PaOptionVO paOptionVO = optionMap.get(BusinessRuleEnum.BusinessOptionEnum.POSITION_FUND_SOURCE.getOptCode());
        if (BlockBeanEnum.BUDGET.getClassifyCode().equals(classifyCode)) {
             paOptionVO = optionMap.get(BusinessRuleEnum.BusinessOptionEnum.POSITION_BUDGET.getOptCode());
        }else if ( BlockBeanEnum.PAY_DETAIL.getClassifyCode().equals(classifyCode)){
            paOptionVO = optionMap.get(BusinessRuleEnum.BusinessOptionEnum.POSITION_PAY_DETAIL.getOptCode());
        }
        return paOptionVO.getOptValue();
    }

    private String getPositionCode(String code) {
        return code.replaceAll("_\\d+$", "");
    }

    // 获取样式块服务
    private IPcxBlockService getBlockServiceBean(BlockBeanEnum blockBeanEnum) {
        String beanName = blockBeanEnum.getClassifyCode() + "BlockServiceImpl";
        // mashaojie - 2024-12-28 由于支付明细和结算方式的样式块服务实现类相同，只是classifyCode不同，所以这里需要特殊处理
        if( BlockBeanEnum.PAY_DETAIL.getClassifyCode().equals( blockBeanEnum.getClassifyCode())){
            beanName   =  "settlementBlockServiceImpl";
        }
        if (SpringUtil.getApplicationContext().containsBean(beanName)) {
            return SpringUtil.getBean(beanName, IPcxBlockService.class);
        } else {
            return SpringUtil.getBean("defaultBlockServiceImpl", IPcxBlockService.class);
        }
    }

    // 排序并返回结果
    private List<PcxBlockVO> sortResult(List<PcxBlockVO> pcxBlockVOS, List<PcxPositionBlockVO> pcxPositionBlockVOS, List<PcxBasExpType> pcxBasExpTypes) {
        List<PcxBlockVO> result = new ArrayList<>();
        Map<String, List<PcxBlockVO>> pcxBlockVOGroup = pcxBlockVOS.stream()
                .collect(Collectors.groupingBy(PcxBlockVO::getClassifyCode));
        Map<String, Integer> expTypeOrderMap = new HashMap<>();
        for (int i = 0; i < pcxBasExpTypes.size(); i++) {
            expTypeOrderMap.put(pcxBasExpTypes.get(i).getExpenseCode(), i);
        }
        for (PcxPositionBlockVO pcxPositionBlockVO : pcxPositionBlockVOS) {
            String classifyCode = pcxPositionBlockVO.getClassifyCode();
            List<PcxBlockVO> pcxBlockVOList = pcxBlockVOGroup.get(classifyCode);
            if (pcxBlockVOList != null && pcxBlockVOList.size() > 1) {
                pcxBlockVOList.sort(Comparator.comparingInt(
                        block -> {
                            String blockCode = block.getBlockCode();
                            // 空值或非数字直接排到最后
                            if (blockCode == null || !blockCode.matches("\\d+")) {
                                return Integer.MAX_VALUE;
                            }
                            if (expTypeOrderMap.containsKey(blockCode)) {
                                return expTypeOrderMap.get(blockCode);
                            } else {
                                try {
                                    return Integer.parseInt(blockCode);
                                } catch (NumberFormatException e) {
                                    return Integer.MAX_VALUE;
                                }
                            }
                        }
                ));
            }
            if (pcxBlockVOList != null) {
                result.addAll(pcxBlockVOList);
            }
        }
        return result;
    }

    /****
     * 获单位当前设置的取工作流节点，有序
     * @param qo
     * @return
     */
    private List<ApprovalProcessDefinitionVO.DefinitionRow> getWfNodes(PcxPositionBlockQO qo) {
        CheckMsg<Object> msg = null;
        try {
            msg = processService.queryProcessDefinition(qo.getAgyCode(), qo.getMofDivCode(), qo.getBillFuncCode(), qo.getTenantId());
        } catch (Exception e) {
            log.error("查询工作流失败"+e.getMessage(), e);
            return Collections.emptyList();
        }
        if (ObjectUtils.isEmpty(msg) || !msg.isSuccess()) {
            return Collections.emptyList();
        }
        PcxProcessDefinition def = (PcxProcessDefinition) msg.getData();
        return def.getNodes().stream()
                .filter(node -> !node.getId().equals(PcxNodeEnum.start.getId()) && !node.getId().equals(PcxNodeEnum.end.getId()))
                .map(node -> {
                    ApprovalProcessDefinitionVO.DefinitionRow row = new ApprovalProcessDefinitionVO.DefinitionRow();
                    row.setOrder(node.getOrder());
                    row.setEnabled(!node.isSkip());
                    row.setStepCode(node.getId());
                    row.setStepName(node.getLabel());
                    row.setDesc(node.getDescription());
                    return row;
                }).sorted(Comparator.comparingInt(ApprovalProcessDefinitionVO.DefinitionRow::getOrder))
                .collect(Collectors.toList());
    }
}
