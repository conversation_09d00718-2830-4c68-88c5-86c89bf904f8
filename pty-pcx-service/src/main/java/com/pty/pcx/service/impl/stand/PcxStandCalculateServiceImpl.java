package com.pty.pcx.service.impl.stand;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.bas.PcxBasCityClassifyService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.stand.PcxStandCalculateService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxCityClassifyConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.enu.expense.InlandfeeExpenseTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.PcxDateUtil;
import com.pty.pcx.dao.stand.PcxStandConditionDao;
import com.pty.pcx.dao.stand.PcxStandKeyDao;
import com.pty.pcx.dao.stand.PcxStandValueDao;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailInlandfee;
import com.pty.pcx.entity.bill.PcxBillExpInlandfee;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.entity.stand.PcxStandCondition;
import com.pty.pcx.entity.stand.PcxStandKey;
import com.pty.pcx.entity.stand.PcxStandValue;
import com.pty.pcx.entity.stand.vo.PcxStandConditionVO;
import com.pty.pcx.entity.stand.vo.PcxStandVO;
import com.pty.pcx.entity.stand.vo.PcxStandValueVO;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PeakDateQO;
import com.pty.pcx.qo.bas.QueryCityPeakClassifyQO;
import com.pty.pcx.qo.stand.PcxCalculateCostQO;
import com.pty.pcx.vo.bas.CityPeakVO;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 费用标准计算服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Slf4j
@Service
public class PcxStandCalculateServiceImpl implements PcxStandCalculateService {

    @Autowired
    private PcxStandKeyDao pcxStandKeyDao;

    @Autowired
    private PcxStandValueDao pcxStandValueDao;

    @Autowired
    private PcxStandConditionDao pcxStandConditionDao;

    @Autowired
    private IPcxBasExpTypeService pcxBasExpTypeService;

    @Autowired
    private PcxBillService pcxBillService;

    @Autowired
    private PcxBasCityClassifyService pcxBasCityClassifyService;

    @Override
    public CheckMsg<?> calculateMeetingCost(PcxCalculateCostQO calculateCostQo) {
        //获取费用代码信息
        CheckMsg<?> checkMsg = validSelectStand(calculateCostQo);
        if (!checkMsg.isSuccess()) {
            throw new RuntimeException(checkMsg.getMsgInfo());
        }
        List<PcxBasExpType> byExpTypes = getByExpType(calculateCostQo, PcxConstant.MEETING_EXPENSE_30215);
        if (CollectionUtil.isEmpty(byExpTypes)) {
            return CheckMsg.fail("未查询到符合条件的费用类型");
        }

        List<PcxBasExpType> byRefineExpTypes = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        Integer applyCtrlLevel = byRefineExpTypes.get(0).getApplyCtrlLevel();
        if (null == applyCtrlLevel || applyCtrlLevel > 2) {
            log.info("申请费用明细控制级别为{}，不处理", applyCtrlLevel);
            return CheckMsg.success();
        }

        //获取单据信息(专属属性，以及费用明细)
        CheckMsg<PcxBillVO> view = pcxBillService.view(calculateCostQo.getBillId(), Arrays.asList(PositionBlockEnum.SPECIFICITY.code,  PositionBlockEnum.EXPENSE_DETAIL.code));
        if (!view.isSuccess()) {
            return CheckMsg.fail("未查询到单据信息");
        }
        PcxBillVO data = view.getData();
        if(CollectionUtil.isNotEmpty(data.getSpecificity())){
            List<PcxBillExpBase> specificity = data.getSpecificity();
            //获取getExpenseCode 等于TRAINING_EXPENSE_30216 的数据
            Optional<PcxBillExpBase> first = specificity.stream().filter(item -> item.getExpenseCode().equals(PcxConstant.MEETING_EXPENSE_30215)).findFirst();
            if (first.isPresent()) {
                PcxBillExpBase travel = first.get();
                BeanUtils.copyProperties(travel, calculateCostQo);
            }
        }
        //获取支出标准信息
        List<PcxStandVO> stringList = selectStandForFilter(calculateCostQo, byExpTypes);
        if (CollectionUtil.isEmpty(stringList)) {
            log.info("未查询到符合条件的标准");
            return CheckMsg.fail("未查询到符合条件的标准");
        }
        return CheckMsg.success(calculateMeetingExpStand(calculateCostQo, byExpTypes, stringList, data));
    }

    /**
     * 会议费处理旺季费用标准计算
     * @param calculateCostQo
     * @param byExpTypes
     * @param stringList
     * @param data
     * @return
     */
    private List<PcxBillExpDetailMeeting> calculateMeetingExpStand(PcxCalculateCostQO calculateCostQo, List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxBillVO data) {
        String typeCode = calculateCostQo.getTypeCode();
        String regionCode = calculateCostQo.getRegionCode();
        String startTime = calculateCostQo.getStartTime();
        String finishTime = calculateCostQo.getFinishTime();

        List<String> factors = Lists.newArrayList();
        //城市分类
        PcxBasCityClassify cityClassify = getCityClassify(regionCode, calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(), calculateCostQo.getMofDivCode());
        factors.add(typeCode);
        factors.add(cityClassify != null ? cityClassify.getClassifyCode() : "other");
        //城市淡旺季属性
        CityPeakVO cityType = getCityType(regionCode, startTime, finishTime, calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(), calculateCostQo.getMofDivCode());

        //获取普通标准集合
        List<PcxStandVO> commenStand = stringList.stream().filter(item -> CollectionUtil.isEmpty(item.getConditionList())
                        || (CollectionUtil.isNotEmpty(item.getConditionList()) && item.getConditionList().stream()
                        .allMatch(condition ->
                                StringUtil.isEmpty(condition.getCondKeyCode())
                                        && StringUtil.isEmpty(condition.getCondValueCode())))
                ).collect(Collectors.toList());

        if (cityType == null || cityType.getPeakFlag() == 2 || cityType.getPeakFlag() == 9) {
            return buildMeetingResult(byExpTypes, commenStand, calculateCostQo,  data.getExpenseDetail());
            //全部在旺季
        } else if (cityType.getPeakFlag() == 1) {
            return buildMeetingPeakResult(byExpTypes, stringList, calculateCostQo,  data.getExpenseDetail(),factors);
            //部分在旺季
        } else if (cityType.getPeakFlag() == 3) {
            Set<LocalDate> inRange = cityType.getInRange();
            Integer peakDay = 0;
            Integer commonDay = 0;
            Integer days = calculateCostQo.getDuration();
            if (days == null || days == 0) {
                days = PcxDateUtil.calculateDays(startTime, finishTime);
            }
            if(CollectionUtil.isNotEmpty(inRange)){
                peakDay = inRange.size();
                commonDay = days - peakDay;
            }

            calculateCostQo.setDuration(commonDay);
            List<PcxBillExpDetailMeeting> commenResultList = buildMeetingResult(byExpTypes, commenStand, calculateCostQo, data.getExpenseDetail());
            calculateCostQo.setDuration(peakDay);
            List<PcxBillExpDetailMeeting> peakResultList = buildMeetingPeakResult(byExpTypes, stringList, calculateCostQo, data.getExpenseDetail(),factors);
            //日期恢复原值
            calculateCostQo.setDuration(commonDay + peakDay);


            Map<String, List<PcxBillExpDetailMeeting>> peakMap = Collections.emptyMap();
            if (CollectionUtil.isNotEmpty(peakResultList)){
                peakMap = peakResultList.stream().collect(Collectors.groupingBy(PcxBillExpDetailMeeting::getExpDetailCode));
            }

            //计算结果：
            if(MapUtil.isNotEmpty(peakMap)){
                for (PcxBillExpDetailMeeting commen : commenResultList) {
                    List<PcxBillExpDetailMeeting> details = peakMap.get(commen.getExpDetailCode());
                    BigDecimal quotaStandard = new BigDecimal(commen.getQuotaStandard());

                    for (PcxBillExpDetailMeeting detail : details) {
                        commen.setInputAmt(commen.getInputAmt().add(detail.getInputAmt()));
                    }

                    BigDecimal quotaStandardPeak = new BigDecimal(details.get(0).getQuotaStandard());
                    commen.setQuotaStandard(quotaStandard.multiply(new BigDecimal(commonDay)).add(quotaStandardPeak.multiply(new BigDecimal(peakDay))).divide(BigDecimal.valueOf(calculateCostQo.getDuration()), 2, RoundingMode.HALF_UP).toPlainString());

                }
            }

            return commenResultList;
        }

        return Collections.emptyList();
    }


    @Override
    public CheckMsg<?> calculateTrainingCost(PcxCalculateCostQO calculateCostQo) {
        //获取费用代码信息
        CheckMsg<?> checkMsg = validSelectStand(calculateCostQo);
        if (!checkMsg.isSuccess()) {
            throw new RuntimeException(checkMsg.getMsgInfo());
        }
        List<PcxBasExpType> byExpTypes = getByExpType(calculateCostQo, PcxConstant.TRAINING_EXPENSE_30216);
        if (CollectionUtil.isEmpty(byExpTypes)) {
            return CheckMsg.fail("未查询到符合条件的费用类型");
        }

        List<PcxBasExpType> byRefineExpTypes = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        Integer applyCtrlLevel = byRefineExpTypes.get(0).getApplyCtrlLevel();
        if (null == applyCtrlLevel || applyCtrlLevel > 2) {
            log.info("申请费用明细控制级别为{}，不处理", applyCtrlLevel);
            return CheckMsg.success();
        }
        //获取支出标准信息
        //获取单据信息
        CheckMsg<PcxBillVO> view = pcxBillService.view(calculateCostQo.getBillId(), Arrays.asList(PositionBlockEnum.SPECIFICITY.code, PositionBlockEnum.EXPENSE_DETAIL.code));
        if (!view.isSuccess()) {
            return CheckMsg.fail("未查询到单据信息");
        }
        PcxBillVO data = view.getData();
        if(CollectionUtil.isNotEmpty(data.getSpecificity())){
            List<PcxBillExpBase> specificity = data.getSpecificity();
            //获取getExpenseCode 等于TRAINING_EXPENSE_30216 的数据
            Optional<PcxBillExpBase> first = specificity.stream().filter(item -> item.getExpenseCode().equals(PcxConstant.TRAINING_EXPENSE_30216)).findFirst();
            if (first.isPresent()) {
                PcxBillExpBase travel = first.get();
                BeanUtils.copyProperties(travel, calculateCostQo);
             }
        }
        List<PcxStandVO> stringList = selectStandForFilter(calculateCostQo, byExpTypes);
        if (CollectionUtil.isEmpty(stringList)) {
            log.info("未查询到符合条件的标准");
            return CheckMsg.fail("未查询到符合条件的标准");
        }
        return CheckMsg.success(calculateTrainingExpStand(calculateCostQo, byExpTypes, stringList, data));
    }

    /**
     * 培训费处理旺季费用标准计算
     * @param calculateCostQo
     * @param byExpTypes
     * @param stringList
     * @param data
     * @return
     */
    private List<PcxBillExpDetailTraining> calculateTrainingExpStand(PcxCalculateCostQO calculateCostQo, List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxBillVO data) {
        String typeCode = calculateCostQo.getTypeCode();
        String regionCode = calculateCostQo.getRegionCode();
        String startTime = calculateCostQo.getStartDate();
        String finishTime = calculateCostQo.getFinishDate();

        List<String> factors = Lists.newArrayList();
        //城市分类
        PcxBasCityClassify cityClassify = getCityClassify(regionCode, calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(), calculateCostQo.getMofDivCode());
        factors.add(typeCode);
        factors.add(cityClassify != null ? cityClassify.getClassifyCode() : "other");
        //城市淡旺季属性
        CityPeakVO cityType = getCityType(regionCode, startTime, finishTime, calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(), calculateCostQo.getMofDivCode());

        //区分淡季标准和旺季标准
        //获取普通标准集合
        List<PcxStandVO> commenStand = stringList.stream().filter(item -> CollectionUtil.isEmpty(item.getConditionList())
                || (CollectionUtil.isNotEmpty(item.getConditionList()) && item.getConditionList().stream()
                .allMatch(condition ->
                        StringUtil.isEmpty(condition.getCondKeyCode())
                                && StringUtil.isEmpty(condition.getCondValueCode())))
        ).collect(Collectors.toList());

        if (cityType == null || cityType.getPeakFlag() == 2 || cityType.getPeakFlag() == 9) {
            return buildTrainingResult(byExpTypes, commenStand, calculateCostQo,  data.getExpenseDetail());
        //全部在旺季
        } else if (cityType.getPeakFlag() == 1) {
            return buildTrainingPeakResult(byExpTypes, stringList, calculateCostQo,  data.getExpenseDetail(),factors);
        //部分在旺季
        } else if (cityType.getPeakFlag() == 3) {
            Set<LocalDate> inRange = cityType.getInRange();

            Integer peakDay = 0;
            Integer commonDay = 0;
            Integer days = calculateCostQo.getDuration();
            if (days == null || days == 0) {
                days = PcxDateUtil.calculateDays(startTime, finishTime);
            }
            if(CollectionUtil.isNotEmpty(inRange)){
                peakDay = inRange.size();
                commonDay = days - peakDay;
            }

            calculateCostQo.setDuration(commonDay);
            List<PcxBillExpDetailTraining> commenResultList = buildTrainingResult(byExpTypes, commenStand, calculateCostQo, data.getExpenseDetail());
            calculateCostQo.setDuration(peakDay);
            List<PcxBillExpDetailTraining> peakResultList = buildTrainingPeakResult(byExpTypes, stringList, calculateCostQo, data.getExpenseDetail(),factors);
            //日期恢复原值
            calculateCostQo.setDuration(commonDay + peakDay);
            Map<String, List<PcxBillExpDetailTraining>> peakMap = Collections.emptyMap();
            if (CollectionUtil.isNotEmpty(peakResultList)){
                peakMap = peakResultList.stream().collect(Collectors.groupingBy(PcxBillExpDetailTraining::getExpDetailCode));

            }

            //计算结果：
            if(MapUtil.isNotEmpty(peakMap)){
                for (PcxBillExpDetailTraining commen : commenResultList) {
                    List<PcxBillExpDetailTraining> pcxBillExpDetailTrainingVOS = peakMap.get(commen.getExpDetailCode());

                    BigDecimal quotaStandard = new BigDecimal(commen.getQuotaStandard());

                    for (PcxBillExpDetailTraining pcxBillExpDetailTraining : pcxBillExpDetailTrainingVOS) {
                        commen.setInputAmt(commen.getInputAmt().add(pcxBillExpDetailTraining.getInputAmt()));
                    }

                    BigDecimal quotaStandardPeak = new BigDecimal(pcxBillExpDetailTrainingVOS.get(0).getQuotaStandard());
                    commen.setQuotaStandard(quotaStandard.multiply(new BigDecimal(commonDay)).add(quotaStandardPeak.multiply(new BigDecimal(peakDay))).divide(BigDecimal.valueOf(calculateCostQo.getDuration()), 2, RoundingMode.HALF_UP).toPlainString());
                }
            }
            return commenResultList;
        }

        return Collections.emptyList();
    }


    // 构建支出标准数据
    private List<PcxBillExpDetailTraining> buildTrainingPeakResult(List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxCalculateCostQO calculateCostQo, List<PcxBillExpDetailBase> detailBaseList,List<String> factors) {
        //培训类别
        String typeCode = calculateCostQo.getTypeCode();
        //参培人数 + 工作人员
        Integer participantCount = calculateCostQo.getPeopleNum() + calculateCostQo.getWorkerNum();

        //天数
        Integer days = calculateCostQo.getDuration();
        //开始、结束时间
        String startDate = calculateCostQo.getStartDate();
        String finishDate = calculateCostQo.getFinishDate();
        //如果天数为空
        if (days == null || days == 0) {
            days = PcxDateUtil.calculateDays(startDate, finishDate);
        }
        //是否省内省外
        Integer isInProvince = calculateCostQo.getIsInProvince();
        //获取当前单位属地省份、以及比对regionCode地点编码所在地省份 是否一致
        Map<String, String> stringMap = new HashMap<>();
        stringMap.put("0", "IN_PROVINCE"); // 暂时保留默认值
        stringMap.put("1", "IN_PROVINCE");
        stringMap.put("2", "OUT_PROVINCE");

        //获取普通标准集合
        List<PcxStandVO> commenStand = stringList.stream().filter(item -> CollectionUtil.isEmpty(item.getConditionList())
                || (CollectionUtil.isNotEmpty(item.getConditionList()) && item.getConditionList().stream()
                .allMatch(condition ->
                        StringUtil.isEmpty(condition.getCondKeyCode())
                                && StringUtil.isEmpty(condition.getCondValueCode())))
        ).collect(Collectors.toList());
        Map<String, List<PcxStandVO>> commenStandMap = commenStand.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));

        //获取旺季标准集合
        List<PcxStandVO> peakStand = stringList.stream()
                .filter(item -> {
                    if (CollectionUtil.isNotEmpty(item.getConditionList())) {
                        return item.getConditionList().stream()
                                .anyMatch(condition ->
                                        StringUtil.isNotEmpty(condition.getCondValueCode())
                                                && condition.getCondValueCode().equals(PcxBillProcessConstant.StandCondition.PEAK.getCode()));
                    }
                    return false;
                }).collect(Collectors.toList());
        Map<String, List<PcxStandVO>> peakStandMap = peakStand.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));

        List<ColumnAndRowCode> columnAndRowCodes = getColumnAndRowCode(factors);

        //基于费用类型编码 转成map stringList
        List<PcxBillExpDetailTraining> detailList = new ArrayList<>();
        //TODO 针对单据存在明细的情况下进行处理
        if(CollectionUtil.isNotEmpty(detailBaseList)) {
            for (Object item : detailBaseList) {
                PcxBillExpDetailTraining training = new PcxBillExpDetailTraining();
                BeanUtil.copyProperties(item, training);
                detailList.add(training);
            }
        }
        //找到具体费用。获取据费用是否启用费用明细级次
        PcxBasExpType meetingExpType = byExpTypes.stream().filter(expType -> expType.getExpenseCode().equals(PcxConstant.TRAINING_EXPENSE_30216)).findFirst().orElse(null);
        //等于null或者0的时候 不进行处理明细
        if(meetingExpType == null || meetingExpType.getApplyCtrlLevel() == 0){
            return new ArrayList<>();
        }
        List<PcxBasExpType> collect = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        // 将他转成map：byExpTypes
        Map<String, PcxBasExpType> expTypeMap = collect.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity()));

        for (Map.Entry<String, PcxBasExpType> entry : expTypeMap.entrySet()) {
            String expenseCode = entry.getKey();
            PcxBasExpType expType = entry.getValue();
            // 获取当前费用类型的level
            PcxBasExpType currentExpType = expTypeMap.get(expenseCode);
            if (currentExpType == null) {
                log.warn("费用类型{}未找到对应的基础费用类型信息", expenseCode);
                continue;
            }
            //  不计算师资费以及师资费下级的标准计算
            if (expenseCode.contains("3021602")) {
                continue;
            }
            // 处理applyCtrlLevel为2的情况
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (2 == meetingExpType.getApplyCtrlLevel()  && expType.getParentCode().equals(PcxConstant.TRAINING_EXPENSE_30216)) {
                // 逻辑1：获取当前code的所有下级费用类型
                List<PcxBasExpType> childExpTypes = byExpTypes.stream()
                        .filter(exp -> expenseCode.equals(exp.getParentCode()))
                        .collect(Collectors.toList());

                // 合计下级费用标准
                for (PcxBasExpType childExp : childExpTypes) {
                    List<PcxStandVO> childValue = peakStandMap.get(childExp.getExpenseCode());
                    //没有旺季标准，使用淡季标准
                    if(CollectionUtil.isEmpty(childValue)){
                        //使用淡季标准
                        childValue = commenStandMap.get(childExp.getExpenseCode());
                        totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                    }else{
                        totalAmount = getPeakQuotaStandard(childValue, columnAndRowCodes, totalAmount);
                    }
                }

                // 计算总金额（示例：天数*人数*标准）
                BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(participantCount));
                // 构建明细VO
                boolean isMatched = false;
                for (PcxBillExpDetailTraining detail : detailList) {
                    if (expenseCode.equals(detail.getExpDetailCode())) {
                        // 匹配到现有记录，更新字段
                        detail.setQuotaStandard(totalAmount.toPlainString());
                        detail.setInputAmt(finalAmount);
                        // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) {
                    // 未匹配到，创建新VO并添加
                    PcxBillExpDetailTraining detailVO = new PcxBillExpDetailTraining();
                    detailVO.setExpDetailCode(expenseCode);
                    detailVO.setQuotaStandard(totalAmount.toPlainString());
                    detailVO.setInputAmt(finalAmount);
                    detailList.add(detailVO);
                }
            } else if (3 == meetingExpType.getApplyCtrlLevel() && 1 == expType.getIsLeaf() && !expType.getParentCode().equals(PcxConstant.TRAINING_EXPENSE_30216)) {
                // 逻辑2：直接处理当前费用类型
                List<PcxStandVO> childValue = peakStandMap.get(expenseCode);

                if(CollectionUtil.isEmpty(childValue)){
                    //使用淡季标准
                    childValue = commenStandMap.get(expenseCode);
                    totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                }else{
                    totalAmount = getPeakQuotaStandard(childValue, columnAndRowCodes, totalAmount);
                }

                // 计算金额（示例：天数*人数*标准）
                BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(participantCount));

                // 构建明细VO
                boolean isMatched = false;
                for (PcxBillExpDetailTraining detail : detailList) {
                    if (expenseCode.equals(detail.getExpDetailCode())) {
                        // 匹配到现有记录，更新字段
                        detail.setQuotaStandard(totalAmount.toPlainString());
                        detail.setInputAmt(finalAmount);
                        // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) {
                    // 未匹配到，创建新VO并添加
                    PcxBillExpDetailTraining detailVO = new PcxBillExpDetailTraining();
                    detailVO.setExpDetailCode(expenseCode);
                    detailVO.setQuotaStandard(totalAmount.toPlainString());
                    detailVO.setInputAmt(finalAmount);
                    detailList.add(detailVO);
                }
            }
        }

        return detailList;
    }

    // 构建支出标准数据
    private List<PcxBillExpDetailTraining> buildTrainingResult(List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxCalculateCostQO calculateCostQo, List<PcxBillExpDetailBase> detailBaseList) {
        //培训类别
        String typeCode = calculateCostQo.getTypeCode();
        //参培人数 + 工作人员
        Integer participantCount = calculateCostQo.getPeopleNum() + calculateCostQo.getWorkerNum();
        //是否省内省外
        Integer isInProvince = calculateCostQo.getIsInProvince();
        //天数
        Integer days = calculateCostQo.getDuration();
        //开始、结束时间
        String startDate = calculateCostQo.getStartDate();
        String finishDate = calculateCostQo.getFinishDate();
        //如果天数为空
        if (days == null || days == 0) {
            days = PcxDateUtil.calculateDays(startDate, finishDate);
        }
        //培训地点编码
        String regionCode = calculateCostQo.getRegionCode();
        //获取当前单位属地省份、以及比对regionCode地点编码所在地省份 是否一致
        Map<String, String> stringMap = new HashMap<>();
        stringMap.put("0", "IN_PROVINCE"); // 暂时保留默认值
        stringMap.put("1", "IN_PROVINCE");
        stringMap.put("2", "OUT_PROVINCE");
        //基于费用类型编码 转成map stringList
        List<PcxBillExpDetailTraining> detailList = new ArrayList<>();
        //TODO 针对单据存在明细的情况下进行处理
        if(CollectionUtil.isNotEmpty(detailBaseList)) {
            for (Object item : detailBaseList) {
                if (item instanceof PcxBillExpDetailTraining) {
                    detailList.add((PcxBillExpDetailTraining) item);
                } else {
                    PcxBillExpDetailTraining training = new PcxBillExpDetailTraining();
                    BeanUtil.copyProperties(item, training);
                    detailList.add(training);
                }
            }
        }
        //找到具体费用。获取据费用是否启用费用明细级次
        PcxBasExpType meetingExpType = byExpTypes.stream().filter(expType -> expType.getExpenseCode().equals(PcxConstant.TRAINING_EXPENSE_30216)).findFirst().orElse(null);
        //等于null或者0的时候 不进行处理明细
        if(meetingExpType == null || meetingExpType.getApplyCtrlLevel() == 0){
            return new ArrayList<>();
        }
        List<PcxBasExpType> collect = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        // 将他转成map：byExpTypes
        Map<String, PcxBasExpType> expTypeMap = collect.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity()));
        Map<String, List<PcxStandVO>> standMap = stringList.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));
        // 循环 standMap
        for (Map.Entry<String, PcxBasExpType> entry : expTypeMap.entrySet()) {
            String expenseCode = entry.getKey();
            PcxBasExpType expType = entry.getValue();
            // 获取当前费用类型的level
            PcxBasExpType currentExpType = expTypeMap.get(expenseCode);
            if (currentExpType == null) {
                log.warn("费用类型{}未找到对应的基础费用类型信息", expenseCode);
                continue;
            }
            //  不计算师资费以及师资费下级的标准计算
            if (expenseCode.contains("3021602")) {
                continue;
            }
            // 处理applyCtrlLevel为2的情况
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (2 == meetingExpType.getApplyCtrlLevel()  && expType.getParentCode().equals(PcxConstant.TRAINING_EXPENSE_30216)) {
                // 逻辑1：获取当前code的所有下级费用类型
                List<PcxBasExpType> childExpTypes = byExpTypes.stream()
                        .filter(exp -> expenseCode.equals(exp.getParentCode()))
                        .collect(Collectors.toList());

                // 合计下级费用标准
                for (PcxBasExpType childExp : childExpTypes) {
                    List<PcxStandVO> childValue = standMap.get(childExp.getExpenseCode());
                    if (CollectionUtil.isNotEmpty(childValue)) {
                        totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                    }
                }

                // 计算总金额（示例：天数*人数*标准）
                BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(participantCount));
                // 构建明细VO
                boolean isMatched = false;
                for (PcxBillExpDetailTraining detail : detailList) {
                    if (expenseCode.equals(detail.getExpDetailCode())) {
                        // 匹配到现有记录，更新字段
                        detail.setQuotaStandard(totalAmount.toPlainString());
                        detail.setInputAmt(finalAmount);
                        // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) {
                    // 未匹配到，创建新VO并添加
                    PcxBillExpDetailTraining detailVO = new PcxBillExpDetailTraining();
                    detailVO.setExpDetailCode(expenseCode);
                    detailVO.setQuotaStandard(totalAmount.toPlainString());
                    detailVO.setInputAmt(finalAmount);
                    detailList.add(detailVO);
                }
            } else if (3 == meetingExpType.getApplyCtrlLevel() && 1 == expType.getIsLeaf() && !expType.getParentCode().equals(PcxConstant.TRAINING_EXPENSE_30216)) {
                // 逻辑2：直接处理当前费用类型
                List<PcxStandVO> childValue = standMap.get(expenseCode);
                if (CollectionUtil.isNotEmpty(childValue)) {
                    totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);

                    // 计算金额（示例：天数*人数*标准）
                    BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                            .multiply(BigDecimal.valueOf(participantCount));

                    // 构建明细VO
                    boolean isMatched = false;
                    for (PcxBillExpDetailTraining detail : detailList) {
                        if (expenseCode.equals(detail.getExpDetailCode())) {
                            // 匹配到现有记录，更新字段
                            detail.setQuotaStandard(totalAmount.toPlainString());
                            detail.setInputAmt(finalAmount);
                            // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                            isMatched = true;
                            break;
                        }
                    }
                    if (!isMatched) {
                        // 未匹配到，创建新VO并添加
                        PcxBillExpDetailTraining detailVO = new PcxBillExpDetailTraining();
                        detailVO.setExpDetailCode(expenseCode);
                        detailVO.setQuotaStandard(totalAmount.toPlainString());
                        detailVO.setInputAmt(finalAmount);
                        detailList.add(detailVO);
                    }
                }
            }
        }

        return detailList;
    }
    private List<PcxBillExpDetailMeeting> buildMeetingPeakResult(List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxCalculateCostQO calculateCostQo, List<PcxBillExpDetailBase> detailBaseList,List<String> factors) {
        //培训类别
        String typeCode = calculateCostQo.getTypeCode();
        //天数
        Integer days = calculateCostQo.getDuration();
        //开始、结束时间
        String startTime = calculateCostQo.getStartTime();
        String finishTime = calculateCostQo.getFinishTime();
        //如果天数为空
        if (days == null || days == 0) {
            days = PcxDateUtil.calculateDays(startTime, finishTime);
        }
        //是否省内省外
        Integer isInProvince = calculateCostQo.getIsInProvince();

        //获取当前单位属地省份、以及比对regionCode地点编码所在地省份 是否一致
        Map<String, String> stringMap = new HashMap<>();
        stringMap.put("0", "IN_PROVINCE"); // 暂时保留默认值
        stringMap.put("1", "IN_PROVINCE");
        stringMap.put("2", "OUT_PROVINCE");

        //获取普通标准集合
        List<PcxStandVO> commenStand = stringList.stream().filter(item -> CollectionUtil.isEmpty(item.getConditionList())
                || (CollectionUtil.isNotEmpty(item.getConditionList()) && item.getConditionList().stream()
                .allMatch(condition ->
                        StringUtil.isEmpty(condition.getCondKeyCode())
                                && StringUtil.isEmpty(condition.getCondValueCode())))
        ).collect(Collectors.toList());
        Map<String, List<PcxStandVO>> commenStandMap = commenStand.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));

        //获取旺季标准集合
        List<PcxStandVO> peakStand = stringList.stream()
                .filter(item -> {
                    if (CollectionUtil.isNotEmpty(item.getConditionList())) {
                        return item.getConditionList().stream()
                                .anyMatch(condition ->
                                        StringUtil.isNotEmpty(condition.getCondValueCode())
                                                && condition.getCondValueCode().equals(PcxBillProcessConstant.StandCondition.PEAK.getCode()));
                    }
                    return false;
                }).collect(Collectors.toList());
        Map<String, List<PcxStandVO>> peakStandMap = peakStand.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));

        List<ColumnAndRowCode> columnAndRowCodes = getColumnAndRowCode(factors);

        //基于费用类型编码 转成map stringList
        List<PcxBillExpDetailMeeting> detailList = new ArrayList<>();
        //TODO 针对单据存在明细的情况下进行处理
        if(CollectionUtil.isNotEmpty(detailBaseList)) {
            for (Object item : detailBaseList) {
                PcxBillExpDetailMeeting meeting = new PcxBillExpDetailMeeting();
                BeanUtil.copyProperties(item, meeting);
                detailList.add(meeting);
            }
        }
        //找到具体费用。获取据费用是否启用费用明细级次
        PcxBasExpType meetingExpType = byExpTypes.stream().filter(expType -> expType.getExpenseCode().equals(PcxConstant.MEETING_EXPENSE_30215)).findFirst().orElse(null);
        //等于null或者0的时候 不进行处理明细
        if(meetingExpType == null || meetingExpType.getApplyCtrlLevel() == 0){
            return new ArrayList<>();
        }
        List<PcxBasExpType> collect = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        // 将他转成map：byExpTypes
        Map<String, PcxBasExpType> expTypeMap = collect.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity()));

        for (Map.Entry<String, PcxBasExpType> entry : expTypeMap.entrySet()) {
            String expenseCode = entry.getKey();
            PcxBasExpType expType = entry.getValue();
            // 获取当前费用类型的level
            PcxBasExpType currentExpType = expTypeMap.get(expenseCode);
            if (currentExpType == null) {
                log.warn("费用类型{}未找到对应的基础费用类型信息", expenseCode);
                continue;
            }
            //如果是3021503 暂不计算标准，因为申请时候没有人员专业级别
            if ("3021503".equals(expenseCode)) {
                continue;
            }

            // 处理applyCtrlLevel为2的情况
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (2 == meetingExpType.getApplyCtrlLevel()  && expType.getParentCode().equals(PcxConstant.MEETING_EXPENSE_30215)) {
                // 逻辑1：获取当前code的所有下级费用类型
                List<PcxBasExpType> childExpTypes = byExpTypes.stream()
                        .filter(exp -> expenseCode.equals(exp.getParentCode()))
                        .collect(Collectors.toList());
                // 合计下级费用标准
                for (PcxBasExpType childExp : childExpTypes) {
                    //获取旺季标准
                    List<PcxStandVO> childValue = peakStandMap.get(childExp.getExpenseCode());
                    //没有旺季标准，使用淡季标准
                    if(CollectionUtil.isEmpty(childValue)){
                        //使用淡季标准
                        childValue = commenStandMap.get(childExp.getExpenseCode());
                        totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                    }else{
                        totalAmount = getPeakQuotaStandard(childValue, columnAndRowCodes, totalAmount);
                    }
                }

                // 计算总金额（示例：天数*人数*标准）
                // 计算人数
                Integer propleNum = calculatePeopleNum(expenseCode, calculateCostQo);
                BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(propleNum));
                // 构建明细VO
                boolean isMatched = false;
                for (PcxBillExpDetailMeeting detail : detailList) {
                    if (expenseCode.equals(detail.getExpDetailCode())) {
                        // 匹配到现有记录，更新字段
                        detail.setQuotaStandard(totalAmount.toPlainString());
                        detail.setInputAmt(finalAmount);
                        // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) {
                    // 未匹配到，创建新VO并添加
                    PcxBillExpDetailMeeting detail = new PcxBillExpDetailMeeting();
                    detail.setExpDetailCode(expenseCode);
                    detail.setQuotaStandard(totalAmount.toPlainString());
                    detail.setInputAmt(finalAmount);
                    detailList.add(detail);
                }
            } else if (3 == meetingExpType.getApplyCtrlLevel() && 1 == expType.getIsLeaf() && !expType.getParentCode().equals(PcxConstant.MEETING_EXPENSE_30215)) {
                // 逻辑2：直接处理当前费用类型（level=0不处理level=1的数据）
                List<PcxStandVO> childValue = peakStandMap.get(expenseCode);
                if(CollectionUtil.isEmpty(childValue)){
                    //使用淡季标准
                    childValue = commenStandMap.get(expenseCode);
                    totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                }else{
                    totalAmount = getPeakQuotaStandard(childValue, columnAndRowCodes, totalAmount);
                }

                // 计算金额（示例：天数*人数*标准）
                Integer propleNum = calculatePeopleNum(expenseCode, calculateCostQo);
                BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(propleNum));
                // 构建明细VO
                boolean isMatched = false;
                for (PcxBillExpDetailMeeting detail : detailList) {
                    if (expenseCode.equals(detail.getExpDetailCode())) {
                        // 匹配到现有记录，更新字段
                        detail.setQuotaStandard(totalAmount.toPlainString());
                        detail.setInputAmt(finalAmount);
                        // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) {
                    // 未匹配到，创建新VO并添加
                    PcxBillExpDetailMeeting detailVO = new PcxBillExpDetailMeeting();
                    detailVO.setExpDetailCode(expenseCode);
                    detailVO.setQuotaStandard(totalAmount.toPlainString());
                    detailVO.setInputAmt(finalAmount);
                    detailList.add(detailVO);
                }
            }
        }

        return detailList;
    }

    /**
     * 计算旺季综合定额
     * @param childValue
     * @param columnAndRowCodes
     * @param totalAmount
     * @return
     */
    private BigDecimal getPeakQuotaStandard(List<PcxStandVO> childValue, List<ColumnAndRowCode> columnAndRowCodes, BigDecimal totalAmount) {
        if(CollectionUtil.isEmpty(childValue)){
            return totalAmount;
        }
        for (PcxStandVO childStand : childValue) {
            List<PcxStandValueVO> standValueList = childStand.getStandValueList();
            for (PcxStandValueVO standValue : standValueList) {
                //如果存在 行列 编码和 影响因素相同，
                String rowValueCode = standValue.getRowValueCode();
                String columnValueCode = standValue.getColumnValueCode();
                for (ColumnAndRowCode columnAndRowCode : columnAndRowCodes) {
                    if(columnAndRowCode.getRowCode().equals(rowValueCode) && columnAndRowCode.getColumnCode().equals(columnValueCode)){
                        // 1. 转换String类型的金额为BigDecimal
                        String amountStr = String.valueOf(standValue.getStandardValue());
                        BigDecimal amount;
                        try {
                            amount = new BigDecimal(amountStr);
                        } catch (NumberFormatException e) {
                            // 处理无效金额格式，根据需求决定是否记录日志或抛出异常
                            continue; // 跳过无效金额
                        }
                        // 3. 满足条件时累加金额
                        totalAmount = totalAmount.add(amount);
                    }
                    break;
                }
            }
        }

        return totalAmount;
    }

    // 构建支出标准数据
    private List<PcxBillExpDetailMeeting> buildMeetingResult(List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxCalculateCostQO calculateCostQo, List<PcxBillExpDetailBase> detailBaseList) {
        //培训类别
        String typeCode = calculateCostQo.getTypeCode();
        calculateCostQo.getWorkerNum();
        //是否省内省外
        Integer isInProvince = calculateCostQo.getIsInProvince();
        //天数
        Integer days = calculateCostQo.getDuration();
        //开始、结束时间
        String startTime = calculateCostQo.getStartTime();
        String finishTime = calculateCostQo.getFinishTime();
        //如果天数为空
        if (days == null || days == 0) {
            days = PcxDateUtil.calculateDays(startTime, finishTime);
        }
        //培训地点编码
        String regionCode = calculateCostQo.getRegionCode();
        //获取当前单位属地省份、以及比对regionCode地点编码所在地省份 是否一致
        Map<String, String> stringMap = new HashMap<>();
        stringMap.put("0", "IN_PROVINCE"); // 暂时保留默认值
        stringMap.put("1", "IN_PROVINCE");
        stringMap.put("2", "OUT_PROVINCE");
        //基于费用类型编码 转成map stringList
        List<PcxBillExpDetailMeeting> detailList = new ArrayList<>();
        //TODO 针对单据存在明细的情况下进行处理
        if(CollectionUtil.isNotEmpty(detailBaseList)) {
            for (Object item : detailBaseList) {
                if (item instanceof PcxBillExpDetailMeeting) {
                    detailList.add((PcxBillExpDetailMeeting) item);
                } else {
                    PcxBillExpDetailMeeting meeting = new PcxBillExpDetailMeeting();
                    BeanUtil.copyProperties(item, meeting);
                    detailList.add(meeting);
                }
            }
        }
        //找到具体费用。获取据费用是否启用费用明细级次
        PcxBasExpType meetingExpType = byExpTypes.stream().filter(expType -> expType.getExpenseCode().equals(PcxConstant.MEETING_EXPENSE_30215)).findFirst().orElse(null);
        //等于null或者0的时候 不进行处理明细
        if(meetingExpType == null || meetingExpType.getApplyCtrlLevel() == 0){
            return new ArrayList<>();
        }
        List<PcxBasExpType> collect = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        // 将他转成map：byExpTypes
        Map<String, PcxBasExpType> expTypeMap = collect.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity()));
        Map<String, List<PcxStandVO>> standMap = stringList.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));
        // 循环 standMap

        for (Map.Entry<String, PcxBasExpType> entry : expTypeMap.entrySet()) {
            String expenseCode = entry.getKey();
            PcxBasExpType expType = entry.getValue();
            // 获取当前费用类型的level
            PcxBasExpType currentExpType = expTypeMap.get(expenseCode);
            if (currentExpType == null) {
                log.warn("费用类型{}未找到对应的基础费用类型信息", expenseCode);
                continue;
            }
            //如果是3021503 暂不计算标准，因为申请时候没有人员专业级别
            if ("3021503".equals(expenseCode)) {
                continue;
            }

            // 处理applyCtrlLevel为2的情况
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (2 == meetingExpType.getApplyCtrlLevel()  && expType.getParentCode().equals(PcxConstant.MEETING_EXPENSE_30215)) {
                // 逻辑1：获取当前code的所有下级费用类型
                List<PcxBasExpType> childExpTypes = byExpTypes.stream()
                        .filter(exp -> expenseCode.equals(exp.getParentCode()))
                        .collect(Collectors.toList());
                // 合计下级费用标准
                for (PcxBasExpType childExp : childExpTypes) {
                    List<PcxStandVO> childValue = standMap.get(childExp.getExpenseCode());
                    if (CollectionUtil.isNotEmpty(childValue)) {
                        totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                    }
                }
                // 计算人数
                Integer propleNum = calculatePeopleNum(expenseCode, calculateCostQo);
                // 计算总金额（示例：天数*人数*标准）
                BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(propleNum));
                // 构建明细VO
                boolean isMatched = false;
                for (PcxBillExpDetailMeeting detail : detailList) {
                    if (expenseCode.equals(detail.getExpDetailCode())) {
                        // 匹配到现有记录，更新字段
                        detail.setQuotaStandard(totalAmount.toPlainString());
                        detail.setInputAmt(finalAmount);
                        // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) {
                    // 未匹配到，创建新VO并添加
                    PcxBillExpDetailMeeting detail = new PcxBillExpDetailMeeting();
                    detail.setExpDetailCode(expenseCode);
                    detail.setQuotaStandard(totalAmount.toPlainString());
                    detail.setInputAmt(finalAmount);
                    detailList.add(detail);
                }
            } else if (3 == meetingExpType.getApplyCtrlLevel() && 1 == expType.getIsLeaf() && !expType.getParentCode().equals(PcxConstant.MEETING_EXPENSE_30215)) {
                    // 逻辑2：直接处理当前费用类型（level=0不处理level=1的数据）
                    List<PcxStandVO> childValue = standMap.get(expenseCode);
                    if (CollectionUtil.isNotEmpty(childValue)) {
                        totalAmount = getCommenQuotaStandard(childValue, typeCode, stringMap, isInProvince, totalAmount);
                    }

                    // 计算人数
                    Integer propleNum = calculatePeopleNum(expenseCode, calculateCostQo);
                    // 计算总金额（示例：天数*人数*标准）
                    BigDecimal finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(propleNum));
                    // 构建明细VO
                    boolean isMatched = false;
                    for (PcxBillExpDetailMeeting detail : detailList) {
                        if (expenseCode.equals(detail.getExpDetailCode())) {
                            // 匹配到现有记录，更新字段
                            detail.setQuotaStandard(totalAmount.toPlainString());
                            detail.setInputAmt(finalAmount);
                            // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                            isMatched = true;
                            break;
                        }
                    }
                    if (!isMatched) {
                        // 未匹配到，创建新VO并添加
                        PcxBillExpDetailMeeting detailVO = new PcxBillExpDetailMeeting();
                        detailVO.setExpDetailCode(expenseCode);
                        detailVO.setQuotaStandard(totalAmount.toPlainString());
                        detailVO.setInputAmt(finalAmount);
                        detailList.add(detailVO);
                    }
                }
            }

        return detailList;
    }

    /**
     * 获取淡季标准金额
     * @param childValue
     * @param typeCode
     * @param stringMap
     * @param isInProvince
     * @param totalAmount
     * @return
     */
    private BigDecimal getCommenQuotaStandard(List<PcxStandVO> childValue, String typeCode, Map<String, String> stringMap, Integer isInProvince, BigDecimal totalAmount) {
        if(CollectionUtil.isEmpty(childValue)){
            return totalAmount;
        }
        for (PcxStandVO childStand : childValue) {
            List<PcxStandValueVO> standValueList = childStand.getStandValueList();
            for (PcxStandValueVO standValue : standValueList) {
                // 核心修改点：金额转换 + 省内外判断逻辑
                //TODO 匹配处理人员级别
                if (standValue.getRowValueCode().equals(typeCode)) {
                    // 1. 转换String类型的金额为BigDecimal
                    String amountStr = String.valueOf(standValue.getStandardValue());
                    BigDecimal amount;
                    try {
                        amount = new BigDecimal(amountStr);
                    } catch (NumberFormatException e) {
                        // 处理无效金额格式，根据需求决定是否记录日志或抛出异常
                        continue; // 跳过无效金额
                    }

                    if (standValue.getColumnValueCode().contains("IN_PROVINCE") || standValue.getColumnValueCode().contains("OUT_PROVINCE")) {
                        if (!stringMap.get(String.valueOf(isInProvince)).equals(standValue.getColumnValueCode())) {
                            continue;
                        }
                    }
                    totalAmount = totalAmount.add(amount);
                }
            }
        }
        return totalAmount;
    }

    @Override
    public CheckMsg<?> calculateHospitalityCost(PcxCalculateCostQO calculateCostQo) {
        //获取费用代码信息
        CheckMsg<?> checkMsg = validSelectStand(calculateCostQo);
        if (!checkMsg.isSuccess()) {
            throw new RuntimeException(checkMsg.getMsgInfo());
        }
        List<PcxBasExpType> byExpTypes = getByExpType(calculateCostQo, PcxConstant.TREAT_EXPENSE_30217);
        if (CollectionUtil.isEmpty(byExpTypes)) {
            return CheckMsg.fail("未查询到符合条件的费用类型");
        }

        List<PcxBasExpType> byRefineExpTypes = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        Integer applyCtrlLevel = byRefineExpTypes.get(0).getApplyCtrlLevel();
        if (null == applyCtrlLevel || applyCtrlLevel > 2) {
            log.info("申请费用明细控制级别为{}，不处理", applyCtrlLevel);
            return CheckMsg.success();
        }
        //获取单据信息
        CheckMsg<PcxBillVO> view = pcxBillService.view(calculateCostQo.getBillId(), Arrays.asList(PositionBlockEnum.SPECIFICITY.code,PositionBlockEnum.EXPENSE_DETAIL.code));
        if (!view.isSuccess()) {
            return CheckMsg.fail("未查询到单据信息");
        }
        PcxBillVO data = view.getData();
        if(CollectionUtil.isNotEmpty(data.getSpecificity())){
            List<PcxBillExpBase> specificity = data.getSpecificity();
            //获取getExpenseCode 等于TREAT_EXPENSE_30217 的数据
            Optional<PcxBillExpBase> first = specificity.stream().filter(item -> item.getExpenseCode().equals(PcxConstant.TREAT_EXPENSE_30217)).findFirst();
            if (first.isPresent()) {
                PcxBillExpInlandfee inlandfee = (PcxBillExpInlandfee)first.get();
                BeanUtils.copyProperties(inlandfee, calculateCostQo);
                //赋值开始日期结束日期
                calculateCostQo.setStartTime(inlandfee.getStartTime());
                calculateCostQo.setFinishTime(inlandfee.getEndTime());

                Integer visitPeopleNum = inlandfee.getVisitPeopleNum() == null ? 0 : inlandfee.getVisitPeopleNum();
                Integer peerStaffNum = inlandfee.getPeerStaffNum() == null ? 0 : inlandfee.getPeerStaffNum();
                //来访人员数量
                calculateCostQo.setVisitPeopleNum(visitPeopleNum);
                //人员总数
                calculateCostQo.setPeopleNum(visitPeopleNum + peerStaffNum);
                calculateCostQo.setTypeCode(inlandfee.getInlandfeeLevelCode());

            }
        }
        //获取支出标准信息
        List<PcxStandVO> stringList = selectStandForFilter(calculateCostQo, byExpTypes);
        if (CollectionUtil.isEmpty(stringList)) {
            log.info("未查询到符合条件的标准");
            return CheckMsg.fail("未查询到符合条件的标准");
        }
        return CheckMsg.success(calculateInlandfeeExpStand(calculateCostQo, byExpTypes, stringList, data));
    }

    // 计算人数
    //  线下会议，自动计算规则：(住宿、伙食费仅计算外地代表标准,其他费用计算全部人数的金额,可修改)
    //  线上会议: (其他费用自动计算,住宿、伙食费不计算,
    public Integer calculatePeopleNum(String expDetailCode, PcxCalculateCostQO calculateCostQo) {
        // 参数校验
        if (calculateCostQo == null || StringUtil.isEmpty(expDetailCode)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        //参会人数
        Integer participantCount = ObjectUtil.defaultIfNull(calculateCostQo.getPeopleNum(), 0);
        //工作人员人数
        Integer workerNum = ObjectUtil.defaultIfNull(calculateCostQo.getWorkerNum(), 0);;
        //代表人数
        Integer outsideNum = ObjectUtil.defaultIfNull(calculateCostQo.getOutsideNum(), 0);
        //会议形式
        String meetingFormatCode = calculateCostQo.getMeetingFormatCode();

        // 数值校验
        if (StringUtil.isEmpty(meetingFormatCode)) {
            throw new IllegalArgumentException("会议形式不能为空");
        }
        if (participantCount < 0 || workerNum < 0 || outsideNum < 0) {
            throw new IllegalArgumentException("人数不能为负数");
        }
        if (!PcxConstant.MEETING_FORMAT_1 .equals(meetingFormatCode) && !PcxConstant.MEETING_FORMAT_2.equals(meetingFormatCode)) {
            throw new IllegalArgumentException("无效的会议形式");
        }

        // 计算总人数
        int totalPeople = participantCount + workerNum;

        // 根据费用类型和会议形式计算费用
        if (PcxConstant.MEETING_EXPENSE_302150101.equals(expDetailCode) || PcxConstant.MEETING_EXPENSE_302150102.equals(expDetailCode)) {
            // 住宿费和伙食费计算逻辑
            if (PcxConstant.MEETING_FORMAT_1 .equals(meetingFormatCode)) {
                // 线上会议不计算住宿费和伙食费
                return 0;
            } else {
                // 线下会议仅计算外地代表人数
                return outsideNum;
            }
        } else if (PcxConstant.MEETING_EXPENSE_302150103.equals(expDetailCode)) {
            // 其他费用计算全部人数
            return totalPeople;
        } else {
            return totalPeople;
//            throw new IllegalArgumentException("未知的费用类型");
        }
    }


    /**
     * 招待费处理旺季费用标准计算
     * @param calculateCostQo
     * @param byExpTypes
     * @param stringList
     * @param data
     * @return
     */
    private List<PcxBillExpDetailInlandfee> calculateInlandfeeExpStand(PcxCalculateCostQO calculateCostQo, List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxBillVO data) {
        String typeCode = calculateCostQo.getTypeCode();
        String regionCode = calculateCostQo.getRegionCode();
        String startTime = calculateCostQo.getStartTime();
        String finishTime = calculateCostQo.getFinishTime();

        List<String> factors = Lists.newArrayList();
        //城市分类
        PcxBasCityClassify cityClassify = getCityClassify(regionCode, calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(), calculateCostQo.getMofDivCode());
        factors.add(typeCode);
        factors.add(cityClassify != null ? cityClassify.getClassifyCode() : "other");
        //城市淡旺季属性
        CityPeakVO cityType = getCityType(regionCode, startTime, finishTime, calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(), calculateCostQo.getMofDivCode());

        //获取普通标准集合
        List<PcxStandVO> commenStand = stringList.stream().filter(item -> {
            if (CollectionUtil.isNotEmpty(item.getConditionList())) {
                return item.getConditionList().stream()
                        .allMatch(condition ->
                                StringUtil.isEmpty(condition.getCondKeyCode())
                                        && StringUtil.isEmpty(condition.getCondValueCode()));
            }
            return false;
        }).collect(Collectors.toList());

        //获取旺季标准集合
        List<PcxStandVO> peakStand = stringList.stream()
                .filter(item -> {
                    if (CollectionUtil.isNotEmpty(item.getConditionList())) {
                        return item.getConditionList().stream()
                                .anyMatch(condition ->
                                        StringUtil.isNotEmpty(condition.getCondValueCode())
                                                && condition.getCondValueCode().equals(PcxBillProcessConstant.StandCondition.PEAK.getCode()));
                    }
                    return false;
                }).collect(Collectors.toList());
        //全部淡季
        if (cityType == null || cityType.getPeakFlag() == 2 || cityType.getPeakFlag() == 9) {
            return buildInlandfeeResult(byExpTypes, commenStand, calculateCostQo,  data.getExpenseDetail());
        //全部在旺季
        } else if (cityType.getPeakFlag() == 1) {
            return buildInlandfeePeakResult(byExpTypes, stringList, calculateCostQo,  data.getExpenseDetail(),factors);
        //部分在旺季
        } else if (cityType.getPeakFlag() == 3) {
            Set<LocalDate> inRange = cityType.getInRange();
            Integer peakDay = 0;
            Integer commonDay = 0;
            Integer days = calculateCostQo.getDuration();
            if (days == null || days == 0) {
                days = PcxDateUtil.calculateDays(startTime, finishTime);
            }
            if(CollectionUtil.isNotEmpty(inRange)){
                peakDay = inRange.size();
                commonDay = days - peakDay;
            }

            calculateCostQo.setDuration(commonDay);
            List<PcxBillExpDetailInlandfee> commenResultList = buildInlandfeeResult(byExpTypes, commenStand, calculateCostQo, data.getExpenseDetail());
            calculateCostQo.setDuration(peakDay);
            List<PcxBillExpDetailInlandfee> peakResultList = buildInlandfeePeakResult(byExpTypes, stringList, calculateCostQo, data.getExpenseDetail(),factors);
            //日期恢复原值
            calculateCostQo.setDuration(commonDay + peakDay);
            Map<String, List<PcxBillExpDetailInlandfee>> peakMap = Collections.emptyMap();
            if (CollectionUtil.isNotEmpty(peakResultList)){
                peakMap = peakResultList.stream().collect(Collectors.groupingBy(PcxBillExpDetailInlandfee::getExpDetailCode));

            }

            //计算结果：
            if(MapUtil.isNotEmpty(peakMap)){
                for (PcxBillExpDetailInlandfee commen : commenResultList) {
                    List<PcxBillExpDetailInlandfee> details = peakMap.get(commen.getExpDetailCode());

                    for (PcxBillExpDetailInlandfee detail : details) {
                        commen.setInputAmt(commen.getInputAmt().add(detail.getInputAmt()));
                    }
                }
            }

            return commenResultList;
        }

        return Collections.emptyList();
    }

    /**
     * 构建招待旺季费用标准计算
     *
     * @param byExpTypes
     * @param stringList
     * @return
     */
    private List<PcxBillExpDetailInlandfee> buildInlandfeePeakResult(List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxCalculateCostQO calculateCostQo, List<PcxBillExpDetailBase> detailBaseList, List<String> factors) {
        //类别
        String typeCode = calculateCostQo.getTypeCode();
        //天数
        Integer days = calculateCostQo.getDuration();
        //开始、结束时间
        String startDate = calculateCostQo.getStartDate();
        String finishDate = calculateCostQo.getFinishDate();
        //如果天数为空
        if (days == null || days == 0) {
            days = PcxDateUtil.calculateDays(startDate, finishDate);
        }
        Integer visitPeopleNum = calculateCostQo.getVisitPeopleNum();
        Integer peopleNum = calculateCostQo.getPeopleNum();
        Integer peerStaffNum = calculateCostQo.getPeerStaffNum();

        //获取普通标准集合
        List<PcxStandVO> commenStand = stringList.stream().filter(item -> CollectionUtil.isEmpty(item.getConditionList())
                || (CollectionUtil.isNotEmpty(item.getConditionList()) && item.getConditionList().stream()
                .allMatch(condition ->
                        StringUtil.isEmpty(condition.getCondKeyCode())
                                && StringUtil.isEmpty(condition.getCondValueCode())))
        ).collect(Collectors.toList());
        Map<String, List<PcxStandVO>> commenStandMap = commenStand.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));

        //获取旺季标准集合
        List<PcxStandVO> peakStand = stringList.stream()
                .filter(item -> {
                    if (CollectionUtil.isNotEmpty(item.getConditionList())) {
                        return item.getConditionList().stream()
                                .anyMatch(condition ->
                                        StringUtil.isNotEmpty(condition.getCondValueCode())
                                                && condition.getCondValueCode().equals(PcxBillProcessConstant.StandCondition.PEAK.getCode()));
                    }
                    return false;
                }).collect(Collectors.toList());
        Map<String, List<PcxStandVO>> peakStandMap = peakStand.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));


        List<ColumnAndRowCode> columnAndRowCodes = getColumnAndRowCode(factors);

        //基于费用类型编码 转成map stringList
        List<PcxBillExpDetailInlandfee> detailList = new ArrayList<>();
        //TODO 针对单据存在明细的情况下进行处理
        if(CollectionUtil.isNotEmpty(detailBaseList)) {
            for (Object item : detailBaseList) {
                PcxBillExpDetailInlandfee training = new PcxBillExpDetailInlandfee();
                BeanUtil.copyProperties(item, training);
                detailList.add(training);
            }
        }
        //找到具体费用。获取据费用是否启用费用明细级次
        PcxBasExpType basExpType = byExpTypes.stream().filter(expType -> expType.getExpenseCode().equals(PcxConstant.TREAT_EXPENSE_30217)).findFirst().orElse(null);
        //等于null或者0的时候 不进行处理明细
        if(basExpType == null || basExpType.getApplyCtrlLevel() == 0){
            return new ArrayList<>();
        }
        List<PcxBasExpType> collect = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        // 将他转成map：byExpTypes
        Map<String, PcxBasExpType> expTypeMap = collect.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity()));

        // 循环 standMap
        for (Map.Entry<String, PcxBasExpType> entry : expTypeMap.entrySet()) {
            String expenseCode = entry.getKey();

            BigDecimal totalAmount = BigDecimal.ZERO;

            List<PcxStandVO> childValue = peakStandMap.get(expenseCode);
            if(CollectionUtil.isEmpty(childValue)){
                //使用淡季标准
                childValue = commenStandMap.get(expenseCode);
                totalAmount = getInlandfeeQuotaStandard(childValue, typeCode, totalAmount);
            }else{
                totalAmount = getPeakQuotaStandard(childValue, columnAndRowCodes, totalAmount);
            }

            // 计算总金额（示例：天数*人数*标准）
            BigDecimal finalAmount;

            if(InlandfeeExpenseTypeEnum.EXPENSE_CODE_3021703.getCode().equals(expenseCode)){
                finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(visitPeopleNum));
            }else{
                finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(peopleNum));
            }
            // 构建明细VO
            boolean isMatched = false;
            for (PcxBillExpDetailInlandfee detail : detailList) {
                if (expenseCode.equals(detail.getExpDetailCode())) {
                    // 匹配到现有记录，更新字段
                    detail.setInputAmt(finalAmount);
                    //此处PeopleNum为来访人数
                    detail.setPeopleNum(visitPeopleNum);
                    //配餐人数默认为陪同人数
                    detail.setFoodNum(peerStaffNum);
                    isMatched = true;
                    break;
                }
            }
            if (!isMatched) {
                // 未匹配到，创建新VO并添加
                PcxBillExpDetailInlandfee detailVO = new PcxBillExpDetailInlandfee();
                detailVO.setExpDetailCode(expenseCode);
                detailVO.setInputAmt(finalAmount);
                //此处PeopleNum为来访人数
                detailVO.setPeopleNum(visitPeopleNum);
                //配餐人数默认为陪同人数
                detailVO.setFoodNum(peerStaffNum);
                detailList.add(detailVO);
            }
        }

        return detailList;
    }
    // 构建支出标准数据
    private List<PcxBillExpDetailInlandfee> buildInlandfeeResult(List<PcxBasExpType> byExpTypes, List<PcxStandVO> stringList, PcxCalculateCostQO calculateCostQo, List<PcxBillExpDetailBase> detailBaseList) {
        //类别
        String typeCode = calculateCostQo.getTypeCode();
        //天数
        Integer days = calculateCostQo.getDuration();
        //开始、结束时间
        String startDate = calculateCostQo.getStartDate();
        String finishDate = calculateCostQo.getFinishDate();
        //如果天数为空
        if (days == null || days == 0) {
            days = PcxDateUtil.calculateDays(startDate, finishDate);
        }
        Integer visitPeopleNum = calculateCostQo.getVisitPeopleNum();
        Integer peopleNum = calculateCostQo.getPeopleNum();
        Integer peerStaffNum = calculateCostQo.getPeerStaffNum();

        //基于费用类型编码 转成map stringList
        List<PcxBillExpDetailInlandfee> detailList = new ArrayList<>();
        //TODO 针对单据存在明细的情况下进行处理
        if(CollectionUtil.isNotEmpty(detailBaseList)) {
            for (Object item : detailBaseList) {
                if (item instanceof PcxBillExpDetailInlandfee) {
                    detailList.add((PcxBillExpDetailInlandfee) item);
                } else {
                    PcxBillExpDetailInlandfee training = new PcxBillExpDetailInlandfee();
                    BeanUtil.copyProperties(item, training);
                    detailList.add(training);
                }
            }
        }
        //找到具体费用。获取据费用是否启用费用明细级次
        PcxBasExpType basExpType = byExpTypes.stream().filter(expType -> expType.getExpenseCode().equals(PcxConstant.TREAT_EXPENSE_30217)).findFirst().orElse(null);
        //等于null或者0的时候 不进行处理明细
        if(basExpType == null || basExpType.getApplyCtrlLevel() == 0){
            return new ArrayList<>();
        }
        List<PcxBasExpType> collect = byExpTypes.stream().filter(expType -> expType.getIsRefine() == 1).collect(Collectors.toList());
        // 将他转成map：byExpTypes
        Map<String, PcxBasExpType> expTypeMap = collect.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, Function.identity()));
        Map<String, List<PcxStandVO>> standMap = stringList.stream().collect(Collectors.groupingBy(PcxStandVO::getExpenseTypeCode));

        // 循环 standMap
        for (Map.Entry<String, PcxBasExpType> entry : expTypeMap.entrySet()) {
            String expenseCode = entry.getKey();
            PcxBasExpType expType = entry.getValue();

            BigDecimal totalAmount = BigDecimal.ZERO;

            List<PcxStandVO> childValue = standMap.get(expenseCode);
            if (CollectionUtil.isNotEmpty(childValue)) {
                totalAmount = getInlandfeeQuotaStandard(childValue, typeCode, totalAmount);
            }

            // 计算总金额（示例：天数*人数*标准）
            BigDecimal finalAmount;

            if(InlandfeeExpenseTypeEnum.EXPENSE_CODE_3021703.getCode().equals(expenseCode)){
                finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(visitPeopleNum));
            }else{
                finalAmount = totalAmount.multiply(BigDecimal.valueOf(days))
                        .multiply(BigDecimal.valueOf(peopleNum));
            }
            // 构建明细VO
            boolean isMatched = false;
            for (PcxBillExpDetailInlandfee detail : detailList) {
                if (expenseCode.equals(detail.getExpDetailCode())) {
                    // 匹配到现有记录，更新字段
                    detail.setInputAmt(finalAmount);
                    //此处PeopleNum为来访人数
                    detail.setPeopleNum(visitPeopleNum);
                    //配餐人数默认为陪同人数
                    detail.setFoodNum(peerStaffNum);
                    // 注意：如果需要将更新后的detail转换为VO，这里可能需要额外转换逻辑
                    isMatched = true;
                    break;
                }
            }
            if (!isMatched) {
                // 未匹配到，创建新VO并添加
                PcxBillExpDetailInlandfee detailVO = new PcxBillExpDetailInlandfee();
                detailVO.setExpDetailCode(expenseCode);
                detailVO.setInputAmt(finalAmount);
                //此处PeopleNum为来访人数
                detailVO.setPeopleNum(visitPeopleNum);
                //配餐人数默认为陪同人数
                detailVO.setFoodNum(peerStaffNum);
                detailList.add(detailVO);
            }
        }

        return detailList;
    }

    private BigDecimal getInlandfeeQuotaStandard(List<PcxStandVO> childValue, String typeCode, BigDecimal totalAmount) {
        if(CollectionUtil.isEmpty(childValue)){
            return totalAmount;
        }
        for (PcxStandVO childStand : childValue) {
            List<PcxStandValueVO> standValueList = childStand.getStandValueList();
            for (PcxStandValueVO standValue : standValueList) {

                if (standValue.getRowValueCode().equals(typeCode)) {
                    // 转换String类型的金额为BigDecimal
                    String amountStr = String.valueOf(standValue.getStandardValue());
                    BigDecimal amount;
                    try {
                        amount = new BigDecimal(amountStr);
                    } catch (NumberFormatException e) {
                        continue;
                    }
                    // 3. 满足条件时累加金额
                    totalAmount = totalAmount.add(amount);
                }
            }
        }
        return totalAmount;
    }

    private List<PcxBasExpType> getByExpType(PcxCalculateCostQO calculateCostQo, String expTypeCode) {
        if (calculateCostQo == null || StringUtil.isEmpty(expTypeCode)) {
            return Collections.emptyList();
        }
        PcxBasExpTypeQO pcxBasExpTypeQo = new PcxBasExpTypeQO();
        pcxBasExpTypeQo.setMofDivCode(calculateCostQo.getMofDivCode());
        pcxBasExpTypeQo.setAgyCode(calculateCostQo.getAgyCode());
        pcxBasExpTypeQo.setFiscal(calculateCostQo.getFiscal());
        pcxBasExpTypeQo.setTenantId(StringUtil.isEmpty(calculateCostQo.getTenantId()) ? PtyContext.getTenantId() : calculateCostQo.getTenantId());
        pcxBasExpTypeQo.setLastCode(expTypeCode);
        pcxBasExpTypeQo.setIsRefine(PubConstant.LOGIC_TRUE);
        Response<?> expTypeDetail = pcxBasExpTypeService.getExpTypeDetail(pcxBasExpTypeQo);
        if (expTypeDetail.isSuccess() && expTypeDetail.getData() != null) {
            return (List<PcxBasExpType>) expTypeDetail.getData();
        }
        return Collections.emptyList();
    }

    /**
     * 根据查询条件筛选标准
     * <p>
     * 该方法首先验证查询参数的有效性，然后根据费用类型代码查询相关的标准键，
     * 并按顺序号排序这些标准键如果查询到的标准键不为空，则进一步查询这些标准的值，
     * 并根据查询条件筛选出符合要求的标准值，最后将这些标准值按标准代码分类并返回
     *
     * @param calculateCostQo 查询条件对象，包含查询所需的各种参数
     * @return 返回一个映射，键为标准代码，值为该标准下的标准值列表
     */
    private List<PcxStandVO> selectStandForFilter(PcxCalculateCostQO calculateCostQo, List<PcxBasExpType> expTypes) {
        //校验查询参数
        //从 expTypes中提取费用的expenseCode
        List<String> expenseCodeList = expTypes.stream().map(PcxBasExpType::getExpenseCode).collect(Collectors.toList());
        calculateCostQo.setExpDetailCodes(expenseCodeList);
        //初始化结果容器
        List<PcxStandVO> result = new ArrayList<>();
        //查询标准，条件，值
        List<PcxStandKey> pcxStandKeys = queryStandKeyByExpenseTypeCode(calculateCostQo);
        //对查询到的标准键按顺序号进行排序
        pcxStandKeys = pcxStandKeys.stream().sorted(Comparator.comparingInt(PcxStandKey::getSeq)).collect(Collectors.toList());
        //如果查询到的标准键不为空，则进一步查询标准值
        if (CollectionUtil.isNotEmpty(pcxStandKeys)) {
            //提取所有标准代码
            List<String> standCodeList = pcxStandKeys.stream().map(PcxStandKey::getStandCode).collect(Collectors.toList());
            //查询标准的表单配置数据，组装筛选条件
            List<PcxStandValue> pcxStandValues = pcxStandValueDao.queryStandStandValueList(calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(),
                    calculateCostQo.getMofDivCode(), standCodeList);
            List<PcxStandCondition> pcxStandConditions = pcxStandConditionDao.queryStandConditionList(calculateCostQo.getAgyCode(), calculateCostQo.getFiscal(),
                    calculateCostQo.getMofDivCode(), standCodeList);
            //组装数据
            collectStandQo(pcxStandKeys, pcxStandConditions, pcxStandValues, result);
        }
        //返回筛选后的标准数据
        return result;
    }


    private void collectStandQo(List<PcxStandKey> pcxStandKeys, List<PcxStandCondition> pcxStandConditions,
                                List<PcxStandValue> pcxStandValues, List<PcxStandVO> result) {
        Map<String, List<PcxStandCondition>> conditionMap = pcxStandConditions.stream().collect(Collectors.groupingBy(PcxStandCondition::getStandCode));
        Map<String, List<PcxStandValue>> valueMap = pcxStandValues.stream().collect(Collectors.groupingBy(PcxStandValue::getStandCode));
        for (PcxStandKey pcxStandKey : pcxStandKeys) {
            PcxStandVO standQO = new PcxStandVO();
            result.add(standQO);
            BeanUtils.copyProperties(pcxStandKey, standQO);
            //填充条件和值数据
            List<PcxStandConditionVO> conditionQOList = new ArrayList<>();
            standQO.setConditionList(conditionQOList);
            List<PcxStandValueVO> valueQOList = new ArrayList<>();
            standQO.setStandValueList(valueQOList);

            List<PcxStandCondition> conditionList = conditionMap.get(pcxStandKey.getStandCode());
            if (CollectionUtils.isNotEmpty(conditionList)){
                for (PcxStandCondition pcxStandCondition : conditionList) {
                    PcxStandConditionVO conditionQO = new PcxStandConditionVO();
                    BeanUtils.copyProperties(pcxStandCondition, conditionQO);
                    conditionQOList.add(conditionQO);
                }
            }
            List<PcxStandValue> valueList = valueMap.get(pcxStandKey.getStandCode());
            if (CollectionUtils.isNotEmpty(valueList)) {
                for (PcxStandValue pcxStandValue : valueList) {
                    PcxStandValueVO valueQO = new PcxStandValueVO();
                    BeanUtils.copyProperties(pcxStandValue, valueQO);
                    valueQOList.add(valueQO);
                }
            }
        }
    }

    /**
     * 根据费用类型代码查询标准键
     * <p>
     * 此方法构造一个查询条件对象PcxStandKey，用于查询与给定费用类型代码、机构代码、财政年份和资金分类代码匹配的标准键
     *
     * @param calculateCostQo 包含查询所需信息的查询对象，包括费用类型代码、机构代码、财政年份和资金分类代码
     * @return 返回一个PcxStandKey对象列表，这些对象满足查询条件
     */
    public List<PcxStandKey> queryStandKeyByExpenseTypeCode(PcxCalculateCostQO calculateCostQo) {
        // 参数校验
        if (CollectionUtil.isEmpty(calculateCostQo.getExpDetailCodes())) {
            return Collections.emptyList();
        }

        // 构造查询条件
        return pcxStandKeyDao.selectList(new LambdaQueryWrapper<PcxStandKey>()
                .in(PcxStandKey::getExpenseTypeCode, calculateCostQo.getExpDetailCodes())
                .eq(PcxStandKey::getAgyCode, calculateCostQo.getAgyCode())
                .eq(PcxStandKey::getFiscal, calculateCostQo.getFiscal())
                .eq(PcxStandKey::getMofDivCode, calculateCostQo.getMofDivCode()));
    }

    /**
     * 校验选择定额参数的合法性
     * <p>
     * 本方法用于在进行成本计算前，验证传入的参数是否合法，包括检查参数是否为空，
     * 以及必要的字段是否填写完整这些字段包括单位编码、区划编码、年度和费用类型编码
     * 如果任一条件不满足，则返回相应的错误提示，表示参数验证失败；
     * 如果所有条件都满足，则返回成功提示，表示参数验证通过
     *
     * @param calculateCostQo 成本计算查询对象，包含需要验证的参数
     * @return CheckMsg 参数验证结果，包括是否成功和提示信息
     */
    private CheckMsg<?> validSelectStand(PcxCalculateCostQO calculateCostQo) {
        // 检查参数是否为空
        if (Objects.isNull(calculateCostQo)) {
            return CheckMsg.fail("参数为空");
        }
        // 检查单位编码是否为空
        if (StringUtil.isEmpty(calculateCostQo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        // 检查区划编码是否为空
        if (StringUtil.isEmpty(calculateCostQo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }
        // 检查年度是否为空
        if (StringUtil.isEmpty(calculateCostQo.getFiscal())) {
            return CheckMsg.fail("年度不能为空");
        }
        // 检查年度是否为空
        if (StringUtil.isEmpty(calculateCostQo.getBillId())) {
            return CheckMsg.fail("billId不能为空");
        }
        // 所有检查通过，返回成功
        return CheckMsg.success();
    }

    private PcxBasCityClassify getCityClassify(String cityCode,String agyCode,String fiscal, String mofDivCode) {
        PcxBasCityClassify qo = new PcxBasCityClassify();
        qo.setAgyCode(agyCode);
        qo.setFiscal(fiscal);
        qo.setMofDivCode(mofDivCode);
        qo.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK);
        qo.setDataCode(cityCode);
        List<PcxBasCityClassify> pcxBasCityClassifies = pcxBasCityClassifyService.selectList(qo);
        if (CollectionUtils.isNotEmpty(pcxBasCityClassifies)){
            return pcxBasCityClassifies.get(0);
        }else{
            return null;
        }
    }

    /**
     * 获取城市的淡旺季
     * @param endCityCode
     * @param startTime
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    private CityPeakVO getCityType(String endCityCode, String startTime, String finishTime,String agyCode, String fiscal, String mofDivCode) {
        //获取当前城市是否属于旺季
        QueryCityPeakClassifyQO pcxBasCityPeakQO = new QueryCityPeakClassifyQO();
        pcxBasCityPeakQO.setAgyCode(agyCode);
        pcxBasCityPeakQO.setFiscal(fiscal);
        pcxBasCityPeakQO.setMofDivCode(mofDivCode);
        pcxBasCityPeakQO.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK);
        pcxBasCityPeakQO.setDataCodes(Lists.newArrayList(endCityCode));
        PeakDateQO peakDateQO = new PeakDateQO(startTime.substring(0,10),finishTime.substring(0,10));
        pcxBasCityPeakQO.setPeakDateQO(peakDateQO);
        List<CityPeakVO> cityPeakDate = pcxBasCityClassifyService.getCityPeakDate(pcxBasCityPeakQO);
        if (CollectionUtils.isNotEmpty(cityPeakDate)){
            CityPeakVO cityPeakVO = cityPeakDate.get(0);
            return cityPeakVO;
        }
        return null;
    }

    private List<ColumnAndRowCode> getColumnAndRowCode(List<String> factories){
        List<ColumnAndRowCode> columnAndRowCodes = new ArrayList<>();
        for (int i = 0; i < factories.size(); i++) {
            for (int j = i + 1; j < factories.size(); j++) {
                ColumnAndRowCode columnAndRowCode1 = new ColumnAndRowCode();
                columnAndRowCode1.setColumnCode(factories.get(i));
                columnAndRowCode1.setRowCode(factories.get(j));
                columnAndRowCodes.add(columnAndRowCode1);

                ColumnAndRowCode columnAndRowCode2 = new ColumnAndRowCode();
                columnAndRowCode2.setColumnCode(factories.get(j));
                columnAndRowCode2.setRowCode(factories.get(i));
                columnAndRowCodes.add(columnAndRowCode2);
            }
        }

        return columnAndRowCodes;
    }

    @Data
    static class ColumnAndRowCode{
        String columnCode;
        String rowCode;
    }

}