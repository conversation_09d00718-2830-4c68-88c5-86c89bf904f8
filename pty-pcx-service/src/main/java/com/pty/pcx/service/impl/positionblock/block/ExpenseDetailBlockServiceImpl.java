package com.pty.pcx.service.impl.positionblock.block;

import com.pty.pcx.api.positionblock.IPcxBlockService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.PcxLabourConstants;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.enu.block.BlockBeanEnum;
import com.pty.pcx.common.enu.expense.TrainingExpenseTypeEnum;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class ExpenseDetailBlockServiceImpl extends AbstractBlockService implements IPcxBlockService {

    @Override
    public List<PcxBlockVO> getBlockInfo(PcxBlockCondQO qo) {
        List<PcxBlockVO> result = new ArrayList<>();
        Map<String, FormSettingEnums.FinanceAuditFieldEnum> financeAuditFieldEnumMap = FormSettingEnums.FinanceAuditFieldEnum.getAllValues();
        Boolean isFinanceAudit = PositionEnum.isFinance(qo.getPositionCode());
        for (String expCode : qo.getExpenseMap().keySet()) {
            List<PcxBasExpType> pcxBasExpTypeList = qo.getExpenseDetailMap().get(expCode);
            // 如果费用明细列表为空，直接跳过
            if (CollectionUtil.isEmpty(pcxBasExpTypeList)) {
                continue;
            }
            List<PcxBlockVO> pcxBlockVOS = new ArrayList<>();
            // 判断是否为费用申请
            Boolean isBasExpApply = isBasExpApply(qo,pcxBasExpTypeList);
            if (isBasExpApply) {
                // 处理费用申请逻辑
                pcxBlockVOS = assembleApplyExpTypeList(qo, expCode, pcxBasExpTypeList,
                        financeAuditFieldEnumMap, isFinanceAudit);
            } else {
                //TODO （专属属性、费用明细）处理费用明细层级
                PcxBlockCondQO pcxBlockCondQO = createBlockCondQO(qo, pcxBasExpTypeList);
                List<BlockProperty> blockProperties = getBlockProperties(pcxBlockCondQO);
                if(CollectionUtil.isEmpty(blockProperties)){
                    List<BlockProperty> commonProperties = getCommonProperties(qo);
                    if(CollectionUtil.isEmpty(commonProperties)){
                        continue;
                    }
                    List<BlockPropertyVO> blockPropertyVOS = convertToBlockPropertyVO(commonProperties, financeAuditFieldEnumMap, isFinanceAudit);
                    for (PcxBasExpType pcxBasExpType : pcxBasExpTypeList) {
                        BlockProperty blockProperty = new BlockProperty();
                        blockProperty.setFormCode(pcxBasExpType.getExpenseCode());
                        blockProperty.setFormName(pcxBasExpType.getExpenseName());
                        result.add(createBlockVO(qo, blockProperty,blockPropertyVOS));
                    }
                    return result;
                }
                Map<String, List<BlockProperty>> groupedProperties = groupBlockProperties(blockProperties, pcxBasExpTypeList);
                // 判断当前是否为通用报销，再将通用的属性填入到当前费用明细中
                List<BlockProperty> commonProperties = new ArrayList<>();
                if(!ObjectUtils.isEmpty(qo.getPcxBill()) && ItemBizTypeEnum.COMMON.getCode().equals(qo.getPcxBill().getBizType())){
                     commonProperties = getCommonProperties(qo);
                }
                List<BlockProperty> finalCommonProperties = commonProperties;
                groupedProperties.forEach((formCode, properties) -> {
                    if (CollectionUtil.isNotEmpty(properties)) {
                        BlockProperty blockProperty = properties.get(0);
                        List<BlockPropertyVO> blockPropertyVOS = new ArrayList<>(convertToBlockPropertyVO(properties, financeAuditFieldEnumMap, isFinanceAudit));
                        if(CollectionUtil.isNotEmpty(finalCommonProperties) && !Objects.equals(formCode, PcxConstant.UNIVERSAL_EXPENSE_CODE)){
                            blockPropertyVOS.addAll(convertToBlockPropertyVO(finalCommonProperties, financeAuditFieldEnumMap, isFinanceAudit));
                        }
                        result.add(createBlockVO(qo, blockProperty,blockPropertyVOS));
                    }
                });
                // 非费用申请，直接处理全部费用明细
                pcxBlockVOS = assembleExpTypeList(qo, pcxBasExpTypeList,
                        financeAuditFieldEnumMap, isFinanceAudit);
            }
            if(CollectionUtil.isNotEmpty(pcxBlockVOS)){
                return pcxBlockVOS;
            }
        }
        return result;
    }

    /**
     * 组装费用申请的 PcxBlockVO 列表
     * @param qo 查询条件对象，包含费用映射等信息
     * @param expCode 费用代码
     * @param pcxBasExpTypeList 费用类型明细列表
     * @param financeAuditFieldEnumMap 财务审计字段映射
     * @param isFinanceAudit 是否为财务审计
     * @return 组装好的 PcxBlockVO 列表
     */
    private List<PcxBlockVO> assembleApplyExpTypeList(PcxBlockCondQO qo, String expCode, List<PcxBasExpType> pcxBasExpTypeList,
                                                      Map<String, FormSettingEnums.FinanceAuditFieldEnum> financeAuditFieldEnumMap,
                                                      Boolean isFinanceAudit) {
        // 初始化结果列表
        List<PcxBlockVO> result = new ArrayList<>();
        // 获取费用申请对象
        PcxBasExpType pcxBasExp = qo.getExpenseMap().get(expCode);
        // 校验费用申请对象：如果为空、控制级别为空或为默认级别（事项申请），返回空列表
        if (ObjectUtils.isEmpty(pcxBasExp) || ObjectUtils.isEmpty(pcxBasExp.getApplyCtrlLevel())
                || DEFAULT_CTRL_LEVEL.equals(pcxBasExp.getApplyCtrlLevel())) {
            return new ArrayList<>();
        }
        // 过滤出符合控制级别的费用明细
        List<PcxBasExpType> assembleExpList = ExpenseBeanUtil.getAssembleExpList(pcxBasExpTypeList);
            List<PcxBasExpType> ctrlLevelExpList = assembleExpList.stream()
                .filter(item -> pcxBasExp.getApplyCtrlLevel().equals(item.getDetailLevel()))
                .collect(Collectors.toList());
        //特殊处理三级费用
        specialHandleLevelThree(assembleExpList,pcxBasExp,ctrlLevelExpList);
        // 如果过滤后的费用明细列表不为空，处理并添加到结果
        if (CollectionUtil.isNotEmpty(ctrlLevelExpList)) {
            // 调用方法处理符合控制级别的费用明细，生成 PcxBlockVO 列表
            List<PcxBlockVO> pcxBlockVOS = assembleExpTypeList(qo, ctrlLevelExpList, financeAuditFieldEnumMap, isFinanceAudit);
            // 将生成的 PcxBlockVO 列表添加到结果
            result.addAll(pcxBlockVOS);
        }
        // 返回最终结果列表
        return result;
    }

    private void specialHandleLevelThree(List<PcxBasExpType> assembleExpList,PcxBasExpType pcxBasExp,List<PcxBasExpType> ctrlLevelExpList){
        if (3 != pcxBasExp.getApplyCtrlLevel()) {
            return;
        }
        // 提取所有 parentCode，用于后续判断是否被引用
        Set<String> referencedParentCodes = assembleExpList.stream()
                .map(PcxBasExpType::getParentCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 找出 detailLevel == 2 并且 expenseCode 不在 referencedParentCodes 中的项（即没有子节点）
        List<PcxBasExpType> unReferencedLevel2Items = assembleExpList.stream()
                .filter(exp -> exp.getDetailLevel() == 2)
                .filter(exp -> !referencedParentCodes.contains(exp.getExpenseCode()))
                .collect(Collectors.toList());

        // 这里加到result中key=2的value中
        if (!unReferencedLevel2Items.isEmpty()) {
            ctrlLevelExpList.addAll(unReferencedLevel2Items);
        }
    }

    /**
     * 处理 PcxBasExpType 列表，生成并返回 PcxBlockVO 列表
     * @param qo 查询条件对象
     * @param pcxBasExpTypeList 费用类型列表
     * @param financeAuditFieldEnumMap 财务审计字段映射
     * @param isFinanceAudit 是否为财务审计
     * @return 生成的 PcxBlockVO 列表
     */
    private List<PcxBlockVO> assembleExpTypeList(PcxBlockCondQO qo, List<PcxBasExpType> pcxBasExpTypeList,
                                                      Map<String, FormSettingEnums.FinanceAuditFieldEnum> financeAuditFieldEnumMap,
                                                      Boolean isFinanceAudit) {
        // 初始化结果列表
        List<PcxBlockVO> result = new ArrayList<>();
        // 创建基于查询条件和费用类型列表的 PcxBlockCondQO 对象
        PcxBlockCondQO pcxBlockCondQO = createBlockCondQO(qo, pcxBasExpTypeList);
        // 获取块属性列表
        List<BlockProperty> blockProperties = getBlockProperties(pcxBlockCondQO);
        // 如果块属性列表为空，则处理通用属性
        if (CollectionUtil.isEmpty(blockProperties)) {
            // 获取通用属性
            List<BlockProperty> commonProperties = getCommonProperties(qo);
            // 如果通用属性也为空，直接返回空结果
            if (CollectionUtil.isEmpty(commonProperties)) {
                return result;
            }
            // 将通用属性转换为 BlockPropertyVO 列表，考虑财务审计字段映射和是否为财务审计
            List<BlockPropertyVO> blockPropertyVOS = convertToBlockPropertyVO(commonProperties, financeAuditFieldEnumMap, isFinanceAudit);
            // 遍历费用类型列表，为每种费用类型生成一个 PcxBlockVO
            for (PcxBasExpType pcxBasExpType : pcxBasExpTypeList) {
                BlockProperty blockProperty = new BlockProperty();
                // 设置块属性的表单代码和名称
                blockProperty.setFormCode(pcxBasExpType.getExpenseCode());
                blockProperty.setFormName(pcxBasExpType.getExpenseName());
                // 创建 PcxBlockVO 并添加到结果列表
                result.add(createBlockVO(qo, blockProperty, blockPropertyVOS));
            }
            return result;
        }
        // 如果块属性列表不为空，按表单代码分组
        Map<String, List<BlockProperty>> groupedProperties = groupBlockProperties(blockProperties,pcxBasExpTypeList);
        // 初始化通用属性列表
        List<BlockProperty> commonProperties = new ArrayList<>();
        // 如果查询条件中包含 PcxBill 且业务类型为 COMMON，则获取通用属性
        if (!ObjectUtils.isEmpty(qo.getPcxBill()) && ItemBizTypeEnum.COMMON.getCode().equals(qo.getPcxBill().getBizType())) {
            commonProperties = getCommonProperties(qo);
        }
        // 将通用属性列表声明为 final，以便在 lambda 表达式中使用
        List<BlockProperty> finalCommonProperties = commonProperties;
        // 遍历分组后的块属性
        groupedProperties.forEach((formCode, properties) -> {
            // 如果当前分组的属性列表不为空
            if (CollectionUtil.isNotEmpty(properties)) {
                // 获取分组中的第一个块属性
                BlockProperty blockProperty = properties.get(0);
                // 将当前分组的块属性转换为 BlockPropertyVO 列表
                List<BlockPropertyVO> blockPropertyVOS = new ArrayList<>(convertToBlockPropertyVO(properties, financeAuditFieldEnumMap, isFinanceAudit));
                // 如果通用属性列表不为空，将通用属性也转换为 BlockPropertyVO 并添加到列表
                if (CollectionUtil.isNotEmpty(finalCommonProperties)) {
                    blockPropertyVOS.addAll(convertToBlockPropertyVO(finalCommonProperties, financeAuditFieldEnumMap, isFinanceAudit));
                }
                // 创建 PcxBlockVO 并添加到结果列表
                result.add(createBlockVO(qo, blockProperty, blockPropertyVOS));
            }
        });
        // 返回结果列表
        return result;
    }

    private List<BlockProperty>  getCommonProperties(PcxBlockCondQO qo) {
        PcxBlockCondQO pcxBlockCondQO = new PcxBlockCondQO();
        pcxBlockCondQO.setFiscal(qo.getFiscal());
        pcxBlockCondQO.setMofDivCode(qo.getMofDivCode());
        pcxBlockCondQO.setAgyCode(qo.getAgyCode());
        pcxBlockCondQO.setBillFuncCode(qo.getBillFuncCode());
        Map<String, PcxBasExpType> expenseDetailMap = new HashMap<>();
        expenseDetailMap.put("*", null);
        pcxBlockCondQO.setExpenseMap(expenseDetailMap);
        pcxBlockCondQO.setUserCode(qo.getUserCode());
        pcxBlockCondQO.setBillId(qo.getBillId());
        pcxBlockCondQO.setClassifyCode(qo.getClassifyCode());
        PcxBasItemVO pcxBasItemVO =new PcxBasItemVO();
        pcxBasItemVO.setItemCode(PcxConstant.UNIVERSAL_ITEM_CODE);
        pcxBlockCondQO.setPcxBasItemVO(pcxBasItemVO);
        return getBlockProperties(pcxBlockCondQO);
    }

    private PcxBlockCondQO createBlockCondQO(PcxBlockCondQO qo, List<PcxBasExpType> pcxBasExpTypeList) {
        PcxBlockCondQO pcxBlockCondQO = new PcxBlockCondQO();
        BeanUtils.copyProperties(qo, pcxBlockCondQO);
        if(CollectionUtil.isEmpty(pcxBasExpTypeList)){
            return pcxBlockCondQO;
        }
        Predicate<PcxBasExpType> predicate = expType -> true;
        //todo 临时注释掉这块的代码
        /*if( BillFuncCodeEnum.EXPENSE.getCode().equals(qo.getBillFuncCode())){
            predicate = item -> !ObjectUtils.isEmpty(item.getIsLeaf()) && item.getIsLeaf() == 1;
        }*/
        pcxBlockCondQO.setExpenseMap(pcxBasExpTypeList.stream()
                        .filter(predicate)
                .collect(Collectors.toMap(PcxBasExpType::getExpenseCode, v -> v, (v1, v2) -> v1)));
        return pcxBlockCondQO;
    }

    private Map<String, List<BlockProperty>> groupBlockProperties(List<BlockProperty> blockProperties,List<PcxBasExpType> pcxBasExpTypeList) {
        Map<String, PcxBasExpType> expenseCodeMap = pcxBasExpTypeList.stream()
                .collect(Collectors.toMap(
                        PcxBasExpType::getExpenseCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        return blockProperties.stream()
                .peek(item->{
                    if (expenseCodeMap.containsKey(item.getFormCode()) && !ObjectUtils.isEmpty(expenseCodeMap.get(item.getFormCode()))){
                        item.setFormName(expenseCodeMap.get(item.getFormCode()).getExpenseName());
                    }
                })
                .collect(Collectors.groupingBy(BlockProperty::getFormCode));
    }

    private PcxBlockVO createBlockVO(PcxBlockCondQO qo, BlockProperty blockProperty, List<BlockPropertyVO> properties) {
        return createBlockVO(qo, blockProperty.getFormCode(), blockProperty.getFormName(), properties);
    }

    private PcxBlockVO createBlockVO(PcxBlockCondQO qo,String blockCode, String blockName, List<BlockPropertyVO> properties) {
        PcxBlockVO blockVO = new PcxBlockVO();
        blockVO.setClassifyCode(qo.getClassifyCode());
        // 移动端申请单不显示费用明细标题
        if (!Objects.equals(BillFuncCodeEnum.APPLY.getCode(), qo.getBillFuncCode())){
            blockVO.setClassifyName(qo.getClassifyName());
        }
        blockVO.setArea(qo.getArea());
        blockVO.setBlockTitle(qo.getShowName());
        blockVO.setBlockCode(blockCode);
        blockVO.setBlockName(blockName);

        Map<String, String> blockExtMap = blockVO.getBlockExtMap();
        // todo - 3021503改为常量
        if(TrainingExpenseTypeEnum.EXPENSE_CODE_302160201.getCode().equals(blockCode) || "3021503".equals(blockCode)){
            if(CollectionUtil.isNotEmpty(blockExtMap)){
                blockExtMap = new HashMap<>(blockExtMap);
                blockExtMap.put("isNeedSettlement", PubConstant.STR_LOGIC_TRUE);
            }else{
                Map<String, String> extMap = new HashMap<>();
                extMap.put("isNeedSettlement", PubConstant.STR_LOGIC_TRUE);
                blockExtMap = extMap;
            }
            blockVO.setBlockExtMap(blockExtMap);
        }
        blockVO.setProperties(properties);
        return blockVO;
    }

    protected List<BlockPropertyVO> convertToBlockPropertyVO(List<BlockProperty> blockProperties,Map<String, FormSettingEnums.FinanceAuditFieldEnum> financeAuditFieldEnumMap, Boolean isFinanceAudit) {
        if (CollectionUtil.isEmpty(blockProperties)) {
            return new ArrayList<>();
        }
        List<BlockPropertyVO> result = new ArrayList<>();
        for (BlockProperty blockProperty : blockProperties) {
            if (ObjectUtils.isEmpty(blockProperty.getEditorCode())) {
                continue;
            }
            boolean isFinanceAuditField = financeAuditFieldEnumMap.containsKey(blockProperty.getFieldValue());
            if (!isFinanceAudit && isFinanceAuditField) {
                boolean isTaxAmtField = Objects.equals(FormSettingEnums.FinanceAuditFieldEnum.TAX_AMT.getCode(), blockProperty.getFieldValue());
                boolean isSpecialExpense = Objects.equals(PcxConstant.TRAINING_EXPENSE_302160201, blockProperty.getFormCode())
                        || Objects.equals(PcxConstant.MEETING_EXPENSE_3021503, blockProperty.getFormCode());

                // 如果是税金额字段或者不是特殊费用中劳务(培训/会议)，则跳过
                if (!(isTaxAmtField && isSpecialExpense)) {
                    continue;
                }
            }
            BlockPropertyVO blockPropertyVO = new BlockPropertyVO();
            BeanUtils.copyProperties(blockProperty, blockPropertyVO);
            result.add(blockPropertyVO);
        }
        return result;
    }
}