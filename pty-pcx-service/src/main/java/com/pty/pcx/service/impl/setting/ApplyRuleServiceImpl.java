package com.pty.pcx.service.impl.setting;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pty.mad.common.BizConditionEnum;
import com.pty.mad.common.MadOperator;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.setting.ApplyRuleService;
import com.pty.pcx.common.constant.PtyRuleConstants;
import com.pty.pcx.common.enu.ApplyControlEnum;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dto.rule.PaBizRuleDto;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasFormSetting;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.setting.ApplyRuleQO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.rule.api.IPaBizRuleService;
import com.pty.rule.api.IPtyRuleService;
import com.pty.rule.entity.PaBizRule;
import com.pty.rule.entity.PtyRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class ApplyRuleServiceImpl implements ApplyRuleService {

    @Autowired
    private IPtyRuleService ptyRuleService;

    @Autowired
    private IPaBizRuleService paBizRuleService;

    @Autowired
    private PcxBasExpTypeDao pcxBasExpTypeDao;
    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Override
    @Transactional
    public CheckMsg update(ApplyRuleQO applyRuleQO) {
        log.debug("更新费用申请规则 applyRuleQO {}", applyRuleQO);
        //更新费用信息
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setExpenseCode(applyRuleQO.getExpenseCode());
        qo.setAgyCode(applyRuleQO.getAgyCode());
        qo.setMofDivCode(applyRuleQO.getMofDivCode());
        qo.setFiscal(applyRuleQO.getFiscal());
        PcxBasExpType pcxBasExpType = pcxBasExpTypeDao.getBaseExpByQO(qo);
        if (pcxBasExpType == null){
            return CheckMsg.fail("费用代码不存在");
        }
        pcxBasExpType.setIsNeedApply(applyRuleQO.getIsNeedApply());
        pcxBasExpType.setApplyCtrlCode(applyRuleQO.getApplyCtrlCode());
        pcxBasExpType.setIsNeedApply(applyRuleQO.getIsNeedApply());
        pcxBasExpType.setApplyCtrlName(ApplyControlEnum.getNameByCode(applyRuleQO.getApplyCtrlCode()));
        pcxBasExpType.setIsApplyDetail(applyRuleQO.getIsApplyDetail());
        pcxBasExpType.setIsNeedPlan(applyRuleQO.getIsNeedPlan());
        pcxBasExpType.setApplyCtrlLevel(applyRuleQO.getApplyCtrlLevel());
        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        BeanUtils.copyProperties(pcxBasExpType, pcxBasExpTypeQO);
        pcxBasExpTypeDao.updateByQo(pcxBasExpTypeQO);
        //删除业务规则数据
        PaBizRule bizRule = PaBizRule.builder()
                .agyCode(pcxBasExpType.getAgyCode())
                .fiscal(pcxBasExpType.getFiscal())
                .bizType(BizConditionEnum.APPLY_BIZ.getCode())
                .bizCode(pcxBasExpType.getExpenseCode())
                .mofDivCode(pcxBasExpType.getMofDivCode())
                .build();
        List<PaBizRule> bizRuleDeletes = paBizRuleService.selectList(bizRule);
        if (CollectionUtil.isNotEmpty(bizRuleDeletes)){
            paBizRuleService.batchDelete(bizRuleDeletes.stream().map(PaBizRule::getId).collect(Collectors.toList()));
            ptyRuleService.batchDeleteRuleById(bizRuleDeletes.stream().map(PaBizRule::getPtyRuleId).collect(Collectors.toList()));
        }
        List<PaBizRuleDto> paBizRuleDtos = applyRuleQO.getPaBizRuleDtos();
        if (CollectionUtil.isNotEmpty(paBizRuleDtos)){
            List<PtyRule> ptyRules = Lists.newArrayList();
            List<PaBizRule> paBizRules = Lists.newArrayList();
            paBizRuleDtos.forEach(item->{
                // todo重新添加业务规则数据
                PtyRule rule = new PtyRule();
                rule.setEnabled(1);
                rule.setRuleAct(PtyRuleConstants.PtyRuleActionEnum.ACCESS.name());
                rule.setRuleType("EXPENSE_APPROVE");
                rule.setRuleId(IDGenerator.id());
                rule.setRuleName(com.pty.pub.common.util.DateUtil.getYear(com.pty.pub.common.util.DateUtil.nowDate())+IDGenerator.id());
                rule.setMustCondition("");
                rule.setWhenCondition("");
                rule.setRuleDesc("部门领导审核条件");
                rule.setRuleModule("mad");
                rule.setRuleCreator("");
                rule.setRuleCreatedDate(DateUtil.formatDateTime(new Date()));
                rule.setRuleModifyDate(DateUtil.formatDateTime(new Date()));
                rule.setRuleReference("");
                rule.setRuleCorrect(1);
                rule.setRuleErrorTemplate("");
                rule.setRuleIdentify(0);
                ptyRules.add(rule);
                PaBizRule newBizRule = new PaBizRule();
                //todo 保存业务条件数据
                newBizRule.setId(IDGenerator.id());
                newBizRule.setBizType(BizConditionEnum.APPLY_BIZ.getCode());
                newBizRule.setBizCode(applyRuleQO.getExpenseCode());
                newBizRule.setMofDivCode(pcxBasExpType.getMofDivCode());
                newBizRule.setAgyCode(pcxBasExpType.getAgyCode());
                newBizRule.setFiscal(pcxBasExpType.getFiscal());
                newBizRule.setRemark(ObjectUtils.firstNonNull(bizRule.getRemark(), ""));
                newBizRule.setPtyRuleId(rule.getRuleId());
                newBizRule.setConditionJson(item.getConditionJson());

                newBizRule.setPtyRuleId(rule.getRuleId());
                paBizRules.add(newBizRule);
            });
            ptyRuleService.batchInsertRule(ptyRules);
            paBizRuleService.batchInsert(paBizRules);
        }
        return CheckMsg.success();
    }

    @Override
    public Response view(ApplyRuleQO applyRuleQO) {
        //查看费用申请规则
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setExpenseCode(applyRuleQO.getExpenseCode());
        qo.setAgyCode(applyRuleQO.getAgyCode());
        qo.setMofDivCode(applyRuleQO.getMofDivCode());
        qo.setFiscal(applyRuleQO.getFiscal());
        PcxBasExpType pcxBasExpType = pcxBasExpTypeDao.getBaseExpByQO(qo);
        applyRuleQO.setIsApplyDetail(pcxBasExpType.getIsApplyDetail());
        applyRuleQO.setIsApplyDetail(pcxBasExpType.getIsApplyDetail());
        applyRuleQO.setApplyCtrlName(pcxBasExpType.getApplyCtrlName());
        applyRuleQO.setApplyCtrlCode(pcxBasExpType.getApplyCtrlCode());
        applyRuleQO.setApplyCtrlLevel(pcxBasExpType.getApplyCtrlLevel());
        applyRuleQO.setIsNeedPlan(pcxBasExpType.getIsNeedPlan());
        applyRuleQO.setIsNeedApply(pcxBasExpType.getIsNeedApply());
        //查询业务规则数据
        PaBizRule paBizRule = PaBizRule.builder()
                .bizCode(pcxBasExpType.getExpenseCode())
                .bizType(BizConditionEnum.APPLY_BIZ.getCode())
                .agyCode(pcxBasExpType.getAgyCode())
                .fiscal(pcxBasExpType.getFiscal())
                .mofDivCode(pcxBasExpType.getMofDivCode()).build();
        List<PaBizRule> rules = paBizRuleService.selectList(paBizRule);
        if (CollectionUtil.isNotEmpty(rules)){
            List<PaBizRuleDto> paBizRuleDtos =Lists.newArrayList();
            rules.forEach(item->{
                PaBizRuleDto paBizRuleDto = new PaBizRuleDto();
                BeanUtil.copyProperties(item,paBizRuleDto);
                paBizRuleDtos.add(paBizRuleDto);
            });
            applyRuleQO.setPaBizRuleDtos(paBizRuleDtos);
        }
        return Response.success().setData(applyRuleQO);
    }

    @Override
    public Map<?, ?> selectPcxFormSetting(ApplyRuleQO qo) {
        PcxBasFormSetting setting = new PcxBasFormSetting();
        setting.setAgyCode(qo.getAgyCode());
        setting.setFiscal(qo.getFiscal());
        setting.setMofDivCode(qo.getMofDivCode());
        setting.setFormClassify(FormSettingEnums.FormClassifyEnum.EXPENSE.getCode());
        setting.setFormCode("*");
        setting.setBillFuncCode(FormSettingEnums.BillFuncCodeEnum.APPLY_CONDITION.getBit());
        List<PcxBasFormSetting> pcxBasFormSettings = pcxBasFormSettingService.selectList(setting);
        Map<String,Object> resultMap = Maps.newHashMap();
        //提取 pcxBasFormSettings集合中的 dataTypeCode dataTypeName dataSource 熟悉返回一个list
        List<Map<String,Object>> elements = pcxBasFormSettings.stream().map(item->{
            Map<String,Object> map = Maps.newHashMap();
            map.put("code",item.getFieldValue());
            map.put("name",item.getFieldName());
            map.put("dataSource",item.getDataSourceCode());
            map.put("url",item.getDataSourceCode());
            map.put("dataClassifyCode",item.getDataClassifyCode());
            return map;
        }).collect(Collectors.toList());
        resultMap.put("elements",elements);

        //提取 PcxBasFormSetting中的 集合中的 dataTypeCode 作为Map的 key,PcxBasFormSetting中的 editorCode dataTypeCode 作为value对象
        Map<String,Map<String,Object>> eleValue = pcxBasFormSettings.stream()
                .collect(Collectors
                        .toMap(PcxBasFormSetting::getFieldValue, item->{
                            Map<String,Object> map = Maps.newHashMap();
                            map.put("dataType",item.getDataTypeCode());
                            map.put("editor",item.getEditorCode());
                            if (item.getDataTypeCode().equals("decimal")){
                                map.put("operator", MadOperator.getList().stream().filter(
                                                e -> !MadOperator.BAOHAN.getCode().equals(e.get("code")))
                                        .collect(Collectors.toList()));
                            }else {
                                map.put("operator", MadOperator.getList().stream().filter(
                                                e -> MadOperator.BAOHAN.getCode().equals(e.get("code")))
                                        .collect(Collectors.toList()));
                            }
                            map.put("data",Lists.newArrayList());
                            return map;
                        }));
        resultMap.put("eleValue",eleValue);
        return resultMap;
    }

}
