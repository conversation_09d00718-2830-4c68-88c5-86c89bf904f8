package com.pty.pcx.service.impl.bill.balance;

import cn.hutool.json.JSONUtil;
import com.pty.balance.constants.BalanceConstants;
import com.pty.balance.entity.TipInfoMsg;
import com.pty.balance.exception.BalanceOverException;
import com.pty.balance.vo.BillDTO;
import com.pty.balance.vo.CtrlDTO;
import com.pty.balance.vo.CtrlElementDTO;
import com.pty.pcx.balance.IBalanceExternalService;
import com.pty.pcx.common.enu.BalanceTypeEnum;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.exception.ForbidTipsException;
import com.pty.pcx.common.exception.WarningTipsException;
import com.pty.pcx.dao.bill.PcxBillBalanceDao;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.dto.balance.PcxBillUpdateBalanceDto;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillBalance;
import com.pty.pcx.mad.IMadDepartmentExternalService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.vo.bill.PcxBillRelationVO;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.setting.common.utils.StreamAPIUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指标余额控制服务
 * <AUTHOR>
 * @since 2024/12/09
 */
@Service
@Slf4j
@Indexed
public class PcxBalanceCtrlServiceImpl implements PcxBalanceCtrlService{
    @Autowired
    private PcxBillBalanceDao pcxBillBalanceDao;
    @Autowired
    private IBalanceExternalService balanceExternalService;
    @Autowired
    private BillExpenseCommonService billExpenseCommonService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private IMadDepartmentExternalService madDepartmentExternalService;


    @Override
    public CtrlDTO convertToCtrlDTO(PcxBill contentBill, boolean... finalSubmit) {
    CtrlDTO ctrlDTO = new CtrlDTO(contentBill.getBillFuncCode(), contentBill);
    BillDTO billDto = ctrlDTO.getBillDto();
    billDto.setBillType(contentBill.getBillFuncCode());
    billDto.setBillId(contentBill.getId());
    billDto.setAmt(Optional.ofNullable(contentBill.getCheckAmt()).orElse(BigDecimal.ZERO));
    billDto.setMofDivCode(contentBill.getMofDivCode());
    billDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));

    CtrlElementDTO main = new CtrlElementDTO();
    main.setMofDivCode(contentBill.getMofDivCode());
    main.setAgyCode(contentBill.getAgyCode());
    main.setFiscal(Integer.valueOf(contentBill.getFiscal()));
    billDto.setElementDto(main);

    List<PcxBillBalance> relationList = pcxBillBalanceDao.selectByBillId(contentBill.getId());
    if (CollectionUtils.isNotEmpty(relationList)) {
        relationList.stream()
            .filter(rel -> StringUtil.isNotEmpty(rel.getBalanceId()) && rel.getUsedAmt() != null && rel.getUsedAmt().compareTo(BigDecimal.ZERO) > 0)
            .forEach(relation -> {
                BillDTO relationBillDto = new BillDTO();
                relationBillDto.setMofDivCode(contentBill.getMofDivCode());
                //todo 优化
                //确定传的单位是当前传入部门的单位 加载分摊部门对应的单位信息
                Map<String,String> deptparam = new HashMap<>();
                deptparam.put("fiscal",contentBill.getFiscal());
                deptparam.put("mofDivCode", contentBill.getMofDivCode());
                deptparam.put("madCode", relation.getBudDepartmentCode());
                List<MadDepartmentDTO> depts = madDepartmentExternalService.selectAgyDepartment(deptparam);

                relationBillDto.setAgyCode(depts.get(0).getAgyCode());
                relationBillDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));
                relationBillDto.setBillType(relation.getBalanceType());
                relationBillDto.setBillId(relation.getBalanceId());
                relationBillDto.setAmt(relation.getUsedAmt());
                relationBillDto.setBillNo(relation.getBalanceNo());

                CtrlElementDTO elementDto = new CtrlElementDTO();
                elementDto.setMofDivCode(contentBill.getMofDivCode());
                elementDto.setAgyCode(contentBill.getAgyCode());
                elementDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));
                relationBillDto.setElementDto(elementDto);

                ctrlDTO.getRelationBills().add(relationBillDto);
            });
    }

    if (BillFuncCodeEnum.REPAYMENT.getCode().equals(contentBill.getBillFuncCode())) {
        ctrlDTO.getRelationBills().forEach(dto -> dto.setAmt(dto.getAmt().negate()));
        billDto.setAmt(billDto.getAmt().negate());
        ctrlDTO.setBalanceType(BalanceConstants.BALANCE_TYPE_EXPENSE);
        billDto.setBillType(BalanceConstants.BALANCE_TYPE_EXPENSE);
        return ctrlDTO;
    }

    BigDecimal expenseAmt = contentBill.getCheckAmt();
    BigDecimal loanAmt = BigDecimal.ZERO;
    BigDecimal applyAmt = BigDecimal.ZERO;

    List<PcxBillRelationVO> loanRelation = billExpenseCommonService.getLoanRelation(contentBill);
    if (CollectionUtils.isNotEmpty(loanRelation)) {
        for (PcxBillRelationVO relation : loanRelation) {
            if (relation.getUsedAmt() != null && relation.getUsedAmt().compareTo(BigDecimal.ZERO) > 0) {
                BillDTO relationBillDto = new BillDTO();
                relationBillDto.setMofDivCode(contentBill.getMofDivCode());
                relationBillDto.setAgyCode(contentBill.getAgyCode());
                relationBillDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));
                relationBillDto.setBillType(relation.getRelBillFuncCode());
                relationBillDto.setBillId(relation.getRelBillId());
                relationBillDto.setAmt(relation.getUsedAmt());
                relationBillDto.setBillNo(relation.getRelBillNo());

                CtrlElementDTO elementDto = new CtrlElementDTO();
                elementDto.setMofDivCode(contentBill.getMofDivCode());
                elementDto.setAgyCode(contentBill.getAgyCode());
                elementDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));
                relationBillDto.setElementDto(elementDto);

                ctrlDTO.getRelationBills().add(relationBillDto);
                loanAmt = loanAmt.add(relation.getUsedAmt());
            }
        }
    }

    PcxBillRelationVO applyRelation = billExpenseCommonService.getApplyRelation(contentBill);
    if (Objects.nonNull(applyRelation) && applyRelation.getUsedAmt().compareTo(BigDecimal.ZERO) > 0) {
        BillDTO applyBillDto = new BillDTO();
        applyBillDto.setMofDivCode(contentBill.getMofDivCode());
        applyBillDto.setAgyCode(contentBill.getAgyCode());
        applyBillDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));
        applyBillDto.setBillType(applyRelation.getRelBillFuncCode());
        applyBillDto.setBillId(applyRelation.getRelBillId());
        // todo 金额需要校验
        applyAmt = applyRelation.getUsedAmt().subtract(loanAmt);
        applyBillDto.setAmt(applyAmt);
        applyBillDto.setBillNo(applyRelation.getRelBillNo());

        CtrlElementDTO elementDto = new CtrlElementDTO();
        elementDto.setMofDivCode(contentBill.getMofDivCode());
        elementDto.setAgyCode(contentBill.getAgyCode());
        elementDto.setFiscal(Integer.valueOf(contentBill.getFiscal()));
        applyBillDto.setElementDto(elementDto);

        ctrlDTO.getRelationBills().add(applyBillDto);
    }

    return ctrlDTO;
}



    /**
     * 指标预占接口
     * @param pcxBill
     * @param isValidate
     */
    @Override
    public void saveCtrl(PcxBill pcxBill, Boolean isValidate){
        this.saveCtrl(pcxBill, isValidate,false);
    }

    /**
     * 指标预占接口
     * @param pcxBill
     * @param isValidate
     * @param isFinalSubmit -- 是否办结
     */
    @Override
    public void saveCtrl(PcxBill pcxBill, Boolean isValidate,boolean isFinalSubmit){

        try {
            //从申请发起的借款做还款时不做额度处理 //如果是还款单则取反进行额度归还
            if (isApplyRepay(pcxBill)) return;
            //单据金额为0的时候不处理直接成功
            if (Objects.isNull(pcxBill.getCheckAmt()) || pcxBill.getCheckAmt().compareTo(BigDecimal.ZERO)<=0) return;
            List<PcxBillBalance> relationList = pcxBillBalanceDao.selectByBillId(pcxBill.getId());
            //项目额度未选择完也不允许调用
            if (CollectionUtils.isNotEmpty(relationList)){
                BigDecimal ibalTotalAmt = relationList.stream().filter(item -> Objects.equals(BalanceTypeEnum.IBAL.getCode(), item.getBalanceType())).map(PcxBillBalance::getUsedAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (ibalTotalAmt.compareTo(pcxBill.getCheckAmt()) < 0){
                    log.info("当前单据的项目额度未选择完成不调用额度 {}, {}",pcxBill.getBillNo(), ibalTotalAmt);
                    return;
                }
            }

            //先删除预占 再重新预占
            this.deleteCtrl(pcxBill);
            CtrlDTO ctrlDTO = this.convertToCtrlDTO(pcxBill);
            ctrlDTO.setRemark(this.getClass().getName()+".saveCtrl()");
            ctrlDTO.setCurUser(pcxBill.getCreator());
            if(isValidate ==null){
                //不传默认都过
                ctrlDTO.setIsValidate(0);
                ctrlDTO.setWarnContinued(1);
            }else {
                ctrlDTO.setIsValidate(isValidate?1:0);
                ctrlDTO.setWarnContinued(isValidate?0:1);
            }
            if(isFinalSubmit){
                ctrlDTO.getBillDto().setAmt(null == pcxBill.getLoanAmt()?BigDecimal.ZERO:pcxBill.getLoanAmt());
                ctrlDTO.setRemark(this.getClass().getName()+".resetBalance()");
                ctrlDTO.getBillDto().getNotElementMap().put("isSettle", "1");
                ctrlDTO.getRelationBills().forEach(billDto -> {
//                    billDto.getNotElementMap().put("isSettle", "1");
                    billDto.setAmt(null == pcxBill.getLoanAmt()?BigDecimal.ZERO:pcxBill.getLoanAmt());
                });
            }
            log.info("指标预占 请求 {}",JSONUtil.toJsonStr(ctrlDTO));
            CtrlDTO result = balanceExternalService.saveCtrl(ctrlDTO);
            log.info("指标预占 请求 {} 返回 {}",JSONUtil.toJsonStr(ctrlDTO),JSONUtil.toJsonStr(result));
        } catch (BalanceOverException e) {
            log.error("----saveCtrlDTO异常1：", e);
            if (CollectionUtils.isNotEmpty(e.getForbidMsgs())) {
                throw new ForbidTipsException(balanceBackInfo(e));
            } else {
                throw new WarningTipsException(balanceBackInfo(e));
            }
        } catch (Exception e) {
            log.error("----saveCtrlDTO异常2：", e);
            throw new RuntimeException(e.getMessage());
        }
    }


    /**
     * 删除指标
     * @param pcxBill
     */
    @Override
    public void deleteCtrl(PcxBill pcxBill) {
        try {
            //从申请发起的借款做还款时不做额度处理 //如果是还款单则取反进行额度归还
            if (isApplyRepay(pcxBill)) return;
            //单据金额为0的时候不处理直接成功
            if (Objects.isNull(pcxBill.getCheckAmt()) ||pcxBill.getCheckAmt().compareTo(BigDecimal.ZERO)<=0) return;
            CtrlDTO ctrlDTO = this.convertToCtrlDTO(pcxBill);
            ctrlDTO.setCurUser(PtyContext.getUsername());
            ctrlDTO.setIsValidate(0);//不校验直接删除
            ctrlDTO.setRemark(this.getClass().getName()+".delBalance()");
            log.info("指标删除 请求 {}",JSONUtil.toJsonStr(ctrlDTO));
            CtrlDTO result = balanceExternalService.deleteCtrl(ctrlDTO);
            log.info("指标删除 请求 {} 返回 {}",JSONUtil.toJsonStr(ctrlDTO),JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("----deleteCtrl异常2：", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 终审之后调用
     * @param pcxBill
     */
    @Override
    public void auditCtrl(PcxBill pcxBill) {
        try {
            //从申请发起的借款做还款时不做额度处理
            if (isApplyRepay(pcxBill)) return;
            //单据金额为0的时候不处理直接成功
            if (Objects.isNull(pcxBill.getCheckAmt()) ||pcxBill.getCheckAmt().compareTo(BigDecimal.ZERO)<=0) return;
            CtrlDTO ctrlDTO = convertToCtrlDTO(pcxBill, true);
            ctrlDTO.setRemark(this.getClass().getName() + ".auditCtrl()");
            ctrlDTO.setCurUser(pcxBill.getAuditName());
            log.info("指标终审 请求 {}",JSONUtil.toJsonStr(ctrlDTO));
            CtrlDTO result = balanceExternalService.auditCtrl(ctrlDTO);
            log.info("指标终审 请求 {} 返回 {}",JSONUtil.toJsonStr(ctrlDTO),JSONUtil.toJsonStr(result));

            log.info("更新预算执行金额 {}", pcxBill.getId());
            updateBalanceAmt(pcxBill, false);
        } catch (BalanceOverException e) {
            log.error("----auditCtrl异常1：", e);
            if (CollectionUtils.isNotEmpty(e.getForbidMsgs())) {
                throw new ForbidTipsException(balanceBackInfo(e));
            } else {
                throw new WarningTipsException(balanceBackInfo(e));
            }
        } catch (Exception e) {
            log.error("----auditCtrl异常2：", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 终审撤回之后调用
     * @param pcxBill
     */
    @Override
    public void reStartCtrl(PcxBill pcxBill) {
        try {
            //从申请发起的借款做还款时不做额度处理 //如果是还款单则取反进行额度归还
            if (isApplyRepay(pcxBill)) return;
            //单据金额为0的时候不处理直接成功
            if (Objects.isNull(pcxBill.getCheckAmt()) ||pcxBill.getCheckAmt().compareTo(BigDecimal.ZERO)<=0) return;
            CtrlDTO ctrlDTO = convertToCtrlDTO(pcxBill);
            ctrlDTO.setRemark(this.getClass().getName() + ".reStartCtrl()");
            ctrlDTO.setCurUser(pcxBill.getAuditName());
            CtrlDTO result = balanceExternalService.restartCtrl(ctrlDTO);
            log.info("终审撤回 请求 {} 返回 {}",JSONUtil.toJsonStr(ctrlDTO),JSONUtil.toJsonStr(result));
            updateBalanceAmt(pcxBill, true);
        } catch (BalanceOverException e) {
            log.error("----reStartCtrl异常1：", e);
            if (CollectionUtils.isNotEmpty(e.getForbidMsgs())) {
                throw new ForbidTipsException(balanceBackInfo(e));
            } else {
                throw new WarningTipsException(balanceBackInfo(e));
            }
        } catch (Exception e) {
            log.error("----reStartCtrl异常2：", e);
            throw new RuntimeException(e.getMessage());
        }
    }


    @Override
    public void saveAuditCtrl(PcxBill pcxBill, Boolean isValidate,boolean isFinalSubmit){
        try {
            //从申请发起的借款做还款时不做额度处理 //如果是还款单则取反进行额度归还
            if (isApplyRepay(pcxBill)) return;
            //单据金额为0的时候不处理直接成功
            if (Objects.isNull(pcxBill.getCheckAmt()) || pcxBill.getCheckAmt().compareTo(BigDecimal.ZERO)<=0) return;
            CtrlDTO ctrlDTO = this.convertToCtrlDTO(pcxBill);
            ctrlDTO.setRemark(this.getClass().getName()+".saveAuditCtrl()");
            ctrlDTO.setCurUser(pcxBill.getCreator());
            if(isValidate ==null){
                //不传默认都过
                ctrlDTO.setIsValidate(0);
                ctrlDTO.setWarnContinued(1);
            }else {
                ctrlDTO.setIsValidate(isValidate?1:0);
                ctrlDTO.setWarnContinued(isValidate?0:1);
            }
            if(isFinalSubmit){
                ctrlDTO.getBillDto().setAmt(null == pcxBill.getLoanAmt()?BigDecimal.ZERO:pcxBill.getLoanAmt());
                ctrlDTO.setRemark(this.getClass().getName()+".saveAuditCtrl()");
                ctrlDTO.getBillDto().getNotElementMap().put("isSettle", "1");
                ctrlDTO.getRelationBills().forEach(billDto -> {
//                    billDto.getNotElementMap().put("isSettle", "1");
                    billDto.setAmt(null == pcxBill.getLoanAmt()?BigDecimal.ZERO:pcxBill.getLoanAmt());
                });
            }
            log.info("指标预占加终审 请求 {}",JSONUtil.toJsonStr(ctrlDTO));
            CtrlDTO result = balanceExternalService.saveAuditCtrl(ctrlDTO);
            log.info("指标预占加终审 请求 {} 返回 {}",JSONUtil.toJsonStr(ctrlDTO),JSONUtil.toJsonStr(result));
        } catch (BalanceOverException e) {
            log.error("----saveAuditCtrl异常1：", e);
            if (CollectionUtils.isNotEmpty(e.getForbidMsgs())) {
                throw new ForbidTipsException(balanceBackInfo(e));
            } else {
                throw new WarningTipsException(balanceBackInfo(e));
            }
        } catch (Exception e) {
            log.error("----saveCtrlDTO异常2：", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 更新预算执行金额
     * @param pcxBill
     * @param flag 是否是终审撤回
     */
    @Override
    public void updateBalanceAmt(PcxBill pcxBill, boolean flag) {
        if (!Objects.equals(BillFuncCodeEnum.EXPENSE.getCode(), pcxBill.getBillFunc()) && !Objects.equals(BillFuncCodeEnum.LOAN.getCode(), pcxBill.getBillFunc())) {
            return ;
        }

        // 查询本单据经费来源使用的指标
        List<PcxBillBalance> contentBalanceList = pcxBillBalanceDao.selectByBillId(pcxBill.getId());
        if (CollectionUtils.isEmpty(contentBalanceList)) {
            return;
        }
        List<String> balanceIds = contentBalanceList.stream().map(PcxBillBalance::getBalanceId).collect(Collectors.toList());

        // 查询经费来源上的指标使用情况
        PcxBillUpdateBalanceDto pexBillBalanceQo = new PcxBillUpdateBalanceDto();
        pexBillBalanceQo.setFinalAuditRevoke(flag);
        pexBillBalanceQo.setBillFunc(pcxBill.getBillFuncCode());
        getBalanceUseDetail(pexBillBalanceQo, pcxBill, balanceIds);

        updateBalanceAmounts(pexBillBalanceQo);

    }


    private  String collectMsg(List<TipInfoMsg> msgs) {
        msgs = msgs.stream().filter(StreamAPIUtil.distinctByKey(TipInfoMsg::getTipMsg)).collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        List<String> balanceIds = msgs.stream().map(TipInfoMsg::getBillNo).collect(Collectors.toList());
        List<BudBalanceDTO> balanceByIds = balanceExternalService.getBalanceByIds(balanceIds);
        Map<String, BudBalanceDTO> map = balanceByIds.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity()));
        for (TipInfoMsg msg : msgs) {
            log.info("指标使用情况：{}", JSONUtil.toJsonStr(msg));
            BudBalanceDTO budBalanceDTO = map.get(msg.getBillNo());
            if (budBalanceDTO != null) {
                sb.append(msg.getBalTypeName()+"（"+budBalanceDTO.getDepartmentName()+"-"+budBalanceDTO.getProjectName()+"），");
            }
        }
        sb.append("预算不足！");
        return sb.toString();
    }

    private String balanceBackInfo(BalanceOverException e) {
      if (CollectionUtils.isNotEmpty(e.getForbidMsgs())) {
        return collectMsg(e.getForbidMsgs());
      } else if (CollectionUtils.isNotEmpty(e.getWarnMsgs())) {
        return collectMsg(e.getWarnMsgs());
      } else {
        return "";
      }
    }



    /**
     * 查询指标使用情况：
     * 在报销单单中的使用情况; 在借款单中的使用情况；在冲销借款中的使用情况;
     * @param pexBillBalanceQo
     * @param contentBill
     * @param balanceIds
     */
    private void getBalanceUseDetail(PcxBillUpdateBalanceDto pexBillBalanceQo, PcxBill contentBill, List<String> balanceIds) {
        if (CollectionUtils.isEmpty(balanceIds)) {
            return;
        }

        // 查询指标在报销单和借款单中的使用情况
        List<PcxBillBalance> balanceList = getUsedBalanceList(contentBill, balanceIds);
        // 指标在报销单中使用金额的情况
        List<PcxBillBalance> pexExpenseBillBalances = balanceList.stream().filter(e -> Objects.equals(BillFuncCodeEnum.EXPENSE.getCode(), e.getBillFuncCode())).collect(Collectors.toList());
        // 指标在借款单中使用金额的情况
        List<PcxBillBalance> pexLoanBillBalances = balanceList.stream().filter(e -> Objects.equals(BillFuncCodeEnum.LOAN.getCode(), e.getBillFuncCode())).collect(Collectors.toList());
        // 冲销借款中的使用情况
        List<PcxBillBalance> pexBalanceBackDetails = new ArrayList<>();
        if (pexBillBalanceQo.isFinalAuditRevoke()) {
            // 终审撤回或者退回
            pexBalanceBackDetails = pcxBillBalanceDao.getSumExpenseLoanBackAmtExeCurrBill(contentBill.getId(), balanceIds);
        } else {
            // 终审
            pexBalanceBackDetails = pcxBillBalanceDao.getSumExpenseLoanBackAmt(contentBill.getId(), balanceIds);
        }
        pexBillBalanceQo.setPexExpenseBillBalanceList(pexExpenseBillBalances);
        pexBillBalanceQo.setPexLoanBillBalanceList(pexLoanBillBalances);
        pexBillBalanceQo.setPexBalanceBackDetails(pexBalanceBackDetails);
    }

    private void updateBalanceAmounts(PcxBillUpdateBalanceDto pexBillBalanceQo) {
        // 所有报销单所用指标，每个指标已用的和
        Map<String, BigDecimal> payAmtMap = pexBillBalanceQo.getPexExpenseBillBalanceList().stream().filter(e -> Objects.nonNull(e.getUsedAmt())).collect(Collectors.toMap(PcxBillBalance::getBalanceId, PcxBillBalance::getUsedAmt, (a, b) -> a));
        // 所有借款单所用指标，每个指标已用的和
        Map<String, BigDecimal> loanAmtMap = pexBillBalanceQo.getPexLoanBillBalanceList().stream().filter(e -> Objects.nonNull(e.getUsedAmt())).collect(Collectors.toMap(PcxBillBalance::getBalanceId, PcxBillBalance::getUsedAmt, (a, b) -> a));
        // 报销冲销借款的金额的和
        Map<String, BigDecimal> backAmtMap = pexBillBalanceQo.getPexBalanceBackDetails().stream().filter(e -> Objects.nonNull(e.getUsedAmt())).collect(Collectors.toMap(PcxBillBalance::getBalanceId, PcxBillBalance::getUsedAmt, (a, b) -> a));

        // 是终审撤回和退回
        if (pexBillBalanceQo.isFinalAuditRevoke()) {
            /*
             * 终审撤回和退回的时候，需要将当前单据所用的金额从指标的金额中减去
             * 没有在 sql 中采取 billId != 当前单据id 来排掉金额的方式，是因为这个指标如果是新创建的，第一次使用，
             * 那么 sql 中的这种方式会导致查不出这个新指标，然后更新的时候有问题，所以直接在代码中查询减去金额处理
             */
            List<PcxBillBalance> balances = pcxBillBalanceDao.selectByBillId(pexBillBalanceQo.getBillId());

            if (Objects.equals(BillFuncCodeEnum.LOAN.getCode(), pexBillBalanceQo.getBillFunc())) {
                updateUsedAmt(loanAmtMap, balances);
            } else if (Objects.equals(BillFuncCodeEnum.EXPENSE.getCode(), pexBillBalanceQo.getBillFunc())) {
                updateUsedAmt(payAmtMap, balances);
            }
        }

        Set<String> balanceIdSet = new HashSet<>();
        balanceIdSet.addAll(payAmtMap.keySet());
        balanceIdSet.addAll(loanAmtMap.keySet());
        balanceIdSet.addAll(backAmtMap.keySet());

        balanceIdSet.stream().forEach(balanceId -> {
            BigDecimal expensePayAmt = Objects.isNull(payAmtMap.get(balanceId)) ? BigDecimal.ZERO : payAmtMap.get(balanceId);
            BigDecimal loanPayAmt = Objects.isNull(loanAmtMap.get(balanceId)) ? BigDecimal.ZERO : loanAmtMap.get(balanceId);
            BigDecimal expenseLoanAmt = Objects.isNull(backAmtMap.get(balanceId)) ? BigDecimal.ZERO : backAmtMap.get(balanceId);
            log.info("支付之后更新预算执行情况信息指标id：{}，报销金额：{}，借款金额：{}，冲销借款金额：{}", balanceId, expensePayAmt, loanPayAmt, expenseLoanAmt);
            balanceExternalService.updatePexAmt(balanceId, expensePayAmt, expenseLoanAmt, loanPayAmt);
        });
    }


    /**
     * 终审的时候，将当前单据所用的金额从指标的金额中减去
     * @param balanceUsedAmtMap
     * @param balances
     */
    private void updateUsedAmt(Map<String, BigDecimal> balanceUsedAmtMap, List<PcxBillBalance> balances) {
        balances.stream().forEach(balance -> {
            BigDecimal usedAmt = balance.getUsedAmt();
            BigDecimal oldUsedAmt = balanceUsedAmtMap.get(balance.getBalanceId());
            if (Objects.isNull(usedAmt) || Objects.isNull(oldUsedAmt)) {
                throw new CommonException("终审撤回或退回更新金额失败");
            }
            balanceUsedAmtMap.put(balance.getBalanceId(), oldUsedAmt.subtract(usedAmt));
        });
    }


    /**
     * 查寻本单据上使用的指标在报销中终审单据使用的总金额
     * @param contentBill 本次审批的单据
     * @param balanceIds 用来在这个方法中收集本单据使用的指标id
     * @return
     */
    private List<PcxBillBalance> getUsedBalanceList(PcxBill contentBill, List<String> balanceIds) {
        if (com.pty.pub.common.util.CollectionUtil.isEmpty(balanceIds)) {
            return new ArrayList<>();
        }

        // 查询本单据使用的指标在报销系统终审单据使用的总金额
        List<PcxBillBalance> balanceList = new ArrayList<>();
        if (Objects.equals(BillFuncCodeEnum.EXPENSE.getCode(), contentBill.getBillFunc())) {
            balanceList = pcxBillBalanceDao.getSumPayAmt(contentBill.getId(), BillFuncCodeEnum.EXPENSE.getCode(),BillFuncCodeEnum.LOAN.getCode(), balanceIds);
        } else if (Objects.equals(BillFuncCodeEnum.LOAN.getCode(), contentBill.getBillFunc())) {
            balanceList = pcxBillBalanceDao.getSumPayAmt(contentBill.getId(), BillFuncCodeEnum.LOAN.getCode(), BillFuncCodeEnum.EXPENSE.getCode(), balanceIds);
        }
        return balanceList;
    }


    private boolean isApplyToLoan(PcxBill pcxBill) {
        PcxBillRelationVO applyRelation = billExpenseCommonService.getApplyRelation(pcxBill);
        return Objects.equals(BillFuncCodeEnum.LOAN.getCode(), pcxBill.getBillFunc()) && Objects.nonNull(applyRelation);
    }


    private boolean isApplyRepay(PcxBill pcxBill) {
        if (Objects.equals(BillFuncCodeEnum.REPAYMENT.getCode(), pcxBill.getBillFunc())){
            List<PcxBillRelationVO> loanRelation = billExpenseCommonService.getLoanRelation(pcxBill);
            if (CollectionUtils.isNotEmpty(loanRelation)){
                PcxBillRelationVO relationVO = loanRelation.get(0);
                PcxBill loanBill = billMainService.view(relationVO.getRelBillId());
                if (isApplyToLoan(loanBill)){
                    log.info("当前还款单为申请发起的借款单还款，不做额度处理");
                    return true;
                }
            }
        }
        return false;
    }
}
