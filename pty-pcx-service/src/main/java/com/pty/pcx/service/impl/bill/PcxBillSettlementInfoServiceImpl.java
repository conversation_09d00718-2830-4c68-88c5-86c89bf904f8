package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pty.pcx.api.bill.PcxBillSettlementInfoService;
import com.pty.pcx.dao.bill.PcxBillSettlementInfoDao;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.util.BatchServiceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 单据结算信息(PcxBillSettlementInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-29 11:08:39
 */
@Service("pcxBillSettlementInfoService")
@Indexed
public class PcxBillSettlementInfoServiceImpl implements PcxBillSettlementInfoService {

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private PcxBillSettlementInfoDao pcxBillSettlementInfoDao;

    @Override
    public List<PcxBillSettlement> selectByIds(List<String> ids) {
        return pcxBillSettlementInfoDao.selectBatchIds(ids);
    }

    @Override
    public List<PcxBillSettlement> selectByBillIds(List<String> billIds) {
        return pcxBillSettlementInfoDao.selectList(new LambdaQueryWrapper<PcxBillSettlement>().in(PcxBillSettlement::getBillId, billIds));
    }

    @Override
    public List<PcxBillSettlement> selectByUkMd5(List<String> ukMd5s) {
        return pcxBillSettlementInfoDao.selectList(new LambdaQueryWrapper<PcxBillSettlement>().in(PcxBillSettlement::getSettlementUk, ukMd5s));
    }

    @Override
    public void batchUpdateByUk(List<? extends PcxBillSettlement> settlements) {
        batchServiceUtil.batchProcess(settlements, PcxBillSettlementInfoDao.class, (mapper, settlement) -> {
            mapper.update(settlement, new QueryWrapper<PcxBillSettlement>().lambda().eq(PcxBillSettlement::getSettlementUk, settlement.getSettlementUk()));
        });
    }
}
