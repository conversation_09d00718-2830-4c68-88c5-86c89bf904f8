package com.pty.pcx.service.impl.financeapproval;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.pty.pcx.api.bas.IPcxBasExpExpecoService;
import com.pty.pcx.api.bill.PcxBillSettlementInfoService;
import com.pty.pcx.api.financeapproval.PcxFinanceApprovalService;
import com.pty.pcx.api.setting.IPcxPaFieldSettingService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.PcxApprovalCondFieldEnum;
import com.pty.pcx.common.enu.PcxApprovalPageLabelEnum;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasItemDao;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dto.pa.PaUserDesignateDTO;
import com.pty.pcx.entity.bas.PcxBasExpExpeco;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.entity.financeapproval.FinanceApprovalTaskDTO;
import com.pty.pcx.pa.IPcxUserDesignateService;
import com.pty.pcx.qo.bas.PcxBasExpExpecoQO;
import com.pty.pcx.qo.bill.PcxAnalysisQO;
import com.pty.pcx.qo.bill.analysis.PcxAnalysisBalQO;
import com.pty.pcx.qo.bill.analysis.PcxAnalysisExpenseQO;
import com.pty.pcx.qo.financeapproval.PcxFinanceApprovalBillQO;
import com.pty.pcx.qo.setting.PaFieldSettingQO;
import com.pty.pcx.qo.workflow2.DoneTaskParamQO;
import com.pty.pcx.qo.workflow2.ProcessHistoryQO;
import com.pty.pcx.qo.workflow2.TodoTaskParamQO;
import com.pty.pcx.util.ValidUtil;
import com.pty.pcx.vo.financeapproval.PcxFinanceApprovalBillVO;
import com.pty.pcx.vo.setting.PaFieldSettingVO;
import com.pty.pcx.vo.workflow2.DoneTaskVO;
import com.pty.pcx.vo.workflow2.ProcessHistoryVO;
import com.pty.pcx.vo.workflow2.TodoTaskVO;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.WorkflowConst;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import com.zjtzsw.sso.fastjson.JSON;
import liquibase.util.MD5Util;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 财务审批(PcxFinanceApprovalServiceImpl)表服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
@Indexed
public class PcxFinanceApprovalServiceImpl implements PcxFinanceApprovalService {

    @Autowired
    private IProcessService processService;
    @Autowired
    private PcxBillDao pcxBillDao;
    @Autowired
    private PcxBasItemDao basItemDao;
    @Autowired
    private IPcxBasExpExpecoService pcxBasExpExpecoService;
    @Autowired
    private IPcxUserDesignateService pcxUserDesignateService;
    @Autowired
    private PcxBillSettlementInfoService pcxBillSettlementInfoService;

    @Autowired
    private IPcxPaFieldSettingService iPcxPaFieldSettingService;
    @Autowired
    private CacheManager cacheManager;

    private static final String MENU_URL = "pcx-ops-work-audit";
    private static final Supplier<String> TODO_ORDER_ID_LIST = () -> String.format("NEXT_TODO:LINK:%s", PtyContext.getTenantId());
    private static final Function<PcxFinanceApprovalBillQO, String> TODO_ORDER_KEY =
            (qo) -> String.format("%s:%s", PtyContext.getUsername(), MD5.create().digestHex(JSONObject.toJSONString(qo)));


    /**
     * 查询财务审批页面的单据列表
     */
    @Override
    public CheckMsg<?> selectFinanceApprovalList(PcxFinanceApprovalBillQO qo) {
        // 参数判空
        ValidUtil.checkEmptyObject(qo, "校验参数异常，pcxFinanceApprovalQO 不能为空");
        ValidUtil.checkEmptyStr(qo.getMofDivCode(), "校验参数异常，区划编码不能为空");
        ValidUtil.checkEmptyStr(qo.getFiscal(), "校验参数异常，年度不能为空");
        ValidUtil.checkEmptyStr(qo.getPageLabelCode(), "校验参数异常，pageLabelCode 不能为空");
        ValidUtil.checkEmptyStr(qo.getUserCode(), "校验参数异常，userCode 不能为空");
        handlerItemCodeParam(qo);
        if (StringUtil.isNotEmpty(qo.getTaskEndDate())) {
            //qo.getTaskEndDate()加一天,把结束时间当天的查出来
            qo.setTaskEndDate(DateUtil.formatLocalDate(DateUtil.paseLocalDate(qo.getTaskEndDate()).plusDays(1)));
        }
        // 根据页签编码，查询对应的工作流任务，抽取单据id与工作流任务的关系
        Map<String, FinanceApprovalTaskDTO> financeApprovalTaskMap = getTaskMapByPageLabelCode(qo);
        // 抽取最终需要查询的单据id，集合判空，设置到查询参数qo中
        List<String> finalBillIds = financeApprovalTaskMap.keySet().stream()
                .filter(item -> StringUtil.isNotBlank(qo.getBillId())
                        ? StringUtil.isNotBlank(item) && item.equals(qo.getBillId())
                        : true)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(finalBillIds)){
            return CheckMsg.success(new PageResult<>());
        }
        qo.setIds(finalBillIds);
        LambdaQueryWrapper<PcxBill> queryWrapper = getQueryVouQueryWrapper(qo, financeApprovalTaskMap);
        // 查询待办单据不分页
        if (Objects.equals(qo.getPageLabelCode(), PcxApprovalPageLabelEnum.APPROVE.getCode())) {
            List<PcxBill> pageInfo = pcxBillDao.selectList(queryWrapper);
            return CheckMsg.success(new PageResult<PcxFinanceApprovalBillVO>().setTotal((long)pageInfo.size()).setResult(buildResultList(pageInfo, financeApprovalTaskMap, processService, pcxBillSettlementInfoService, qo.getPageLabelCode())));
        }
        PageInfo<PcxBill> pageInfo = PageHelper.startPage(qo.getPageIndex(), qo.getPageSize())
                .doSelectPageInfo(() -> pcxBillDao.selectList(queryWrapper));
        // 组装财务审批单据VO列表，返回分页结果
        List<PcxFinanceApprovalBillVO> resultList = buildResultList(pageInfo.getList(), financeApprovalTaskMap, processService, pcxBillSettlementInfoService, qo.getPageLabelCode());
        return CheckMsg.success(new PageResult<PcxFinanceApprovalBillVO>().setTotal(pageInfo.getTotal()).setResult(resultList));
    }

    @Override
    public CheckMsg<?> next(PcxFinanceApprovalBillQO qo) {
        String billId = qo.getBillId();
        qo.setBillId(null);
        String prefix = TODO_ORDER_ID_LIST.get();
        String key = TODO_ORDER_KEY.apply(qo);
        log.info("获取下一条任务, billId: {}, cache key: {}:{} qo:{}", billId, prefix, key, JSON.toJSONString(qo));
        // 获取上一次next的全部待办结果
        Cache cache = cacheManager.getCache(prefix);
        List<String> cacheTodoOrderIds = cache.get(key, List.class);
        CheckMsg<?> pageResult = this.selectFinanceApprovalList(qo);
        if (!pageResult.isSuccess()) {
            return CheckMsg.fail(pageResult.getMsgInfo());
        }
        List<String> dbTodoOrderIds = ((PageResult<PcxFinanceApprovalBillVO>)pageResult.getData()).getResult().stream().map(PcxFinanceApprovalBillVO::getId).collect(Collectors.toList());
        // 反转dbTodoOrderIds
        List<String> reverse = new ArrayList<>(Lists.reverse(dbTodoOrderIds));
        dbTodoOrderIds.clear();
        dbTodoOrderIds.addAll(reverse);
        String nextBillId = null;
        cache.put(key, dbTodoOrderIds);
        // 如果存在当前id, 则从缓存中获取下一条
        if (CollectionUtils.isNotEmpty(cacheTodoOrderIds) && cacheTodoOrderIds.contains(billId)) {
            int index = cacheTodoOrderIds.indexOf(billId);
            int ptr = (index + 1) % cacheTodoOrderIds.size();
            while (ptr != index) {
                if (dbTodoOrderIds.contains(cacheTodoOrderIds.get(ptr)))
                    nextBillId = cacheTodoOrderIds.get(ptr);
                ptr = (ptr + 1) % cacheTodoOrderIds.size();
            }
        }
        // 如果缓存中不存在当前id, 直接从数据库记录中获取第一条
        if (CollectionUtils.isNotEmpty(dbTodoOrderIds) && (CollectionUtils.isEmpty(cacheTodoOrderIds) || !cacheTodoOrderIds.contains(billId) || nextBillId == null)) {
            int index = dbTodoOrderIds.indexOf(billId);
            int ptr = (index + 1) % dbTodoOrderIds.size();

            while (ptr != index) {
                nextBillId = dbTodoOrderIds.get(ptr);
                ptr = (ptr + 1) % dbTodoOrderIds.size();
            }
        }
        // 找到当前单据所在
        return CheckMsg.success(nextBillId);
    }

    /**
     * 根据一级事项查询下级事项
     * @param qo
     */
    private void handlerItemCodeParam(PcxFinanceApprovalBillQO qo) {
        if (StringUtil.isEmpty(qo.getOneItemCode())) {
            return;
        }
        PcxBasItem param = new PcxBasItem();
        param.setParentCode(qo.getOneItemCode());
        param.setMofDivCode(qo.getMofDivCode());
        param.setAgyCode(qo.getHostAgyCode());
        param.setFiscal(qo.getFiscal());
        param.setTenantId(qo.getTenantId());
        List<PcxBasItem> basItems = basItemDao.selectList(param);
        if (CollectionUtil.isEmpty(basItems)) {
            qo.setItemCodeList(Collections.singletonList(qo.getOneItemCode()));
        } else {
            qo.setItemCodeList(basItems.stream().map(PcxBasItem::getItemCode).collect(Collectors.toList()));
        }
    }

    @NotNull
    private LambdaQueryWrapper<PcxBill> getQueryVouQueryWrapper(PcxFinanceApprovalBillQO qo, Map<String, FinanceApprovalTaskDTO> financeApprovalTaskMap) {
        QueryWrapper<PcxBill> orgQueryWrapper = Wrappers.query();
        LambdaQueryWrapper<PcxBill> queryWrapper = orgQueryWrapper.lambda();
        if (!CollectionUtil.isEmpty(qo.getIds())) {
            queryWrapper.in(PcxBill::getId, qo.getIds());
        }
        if (StringUtil.isNotEmpty(qo.getMofDivCode())) {
            queryWrapper.eq(PcxBill::getMofDivCode, qo.getMofDivCode());
        }
        if (StringUtil.isNotEmpty(qo.getAgyCode())) {
            queryWrapper.eq(PcxBill::getAgyCode, qo.getAgyCode());
        }
        if (StringUtil.isNotEmpty(qo.getBillFuncCode())) {
            queryWrapper.eq(PcxBill::getBillFuncCode, qo.getBillFuncCode());
        }
        if (StringUtil.isNotEmpty(qo.getBillStatus())) {
            queryWrapper.eq(PcxBill::getBillStatus, qo.getBillStatus());
        }
        if (StringUtil.isNotEmpty(qo.getFiscal())) {
            queryWrapper.eq(PcxBill::getFiscal, qo.getFiscal());
        }
        if (StringUtil.isNotEmpty(qo.getTenantId())) {
            queryWrapper.eq(PcxBill::getTenantId, qo.getTenantId());
        }
        if (StringUtil.isNotEmpty(qo.getApproveStatus())) {
            queryWrapper.eq(PcxBill::getApproveStatus, qo.getApproveStatus());
        }
        if (StringUtil.isNotEmpty(qo.getItemCode())) {
            queryWrapper.eq(PcxBill::getItemCode, qo.getItemCode());
        }
        // 金额CheckAmt大于等于startCheckAmt
        if (Objects.nonNull(qo.getStartCheckAmt())) {
            queryWrapper.ge(PcxBill::getCheckAmt, qo.getStartCheckAmt());
        }
        // 金额CheckAmt小于等于endCheckAmt
        if (Objects.nonNull(qo.getEndCheckAmt())) {
            queryWrapper.le(PcxBill::getCheckAmt, qo.getEndCheckAmt());
        }
        // 时间ModifiedTime大于等于startCheckDate
        if (StringUtil.isNotEmpty(qo.getTaskStartDate())) {
            queryWrapper.ge(PcxBill::getModifiedTime, qo.getTaskStartDate());
        }
        // 时间ModifiedTime大于等于endCheckDate
        if (StringUtil.isNotEmpty(qo.getTaskEndDate())) {
            queryWrapper.le(PcxBill::getModifiedTime, qo.getTaskEndDate());
        }
        if (ObjectUtils.isNotEmpty(qo.getComparedStatus())) {
            queryWrapper.eq(PcxBill::getComparedStatus, qo.getComparedStatus());
        }
        if (StrUtil.isNotBlank(qo.getSettlementType())) {
            queryWrapper.exists("select 1 from pcx_bill_settlement where bill_id = pcx_bill.id and settlement_type = {0}", qo.getSettlementType());
        }
        // 关键字匹配：查询多个字段值是否包含关键字
        String keyword = qo.getKeyword();
        List<String> keywordFields = qo.getKeywordFields();
        if(StringUtil.isNotEmpty(keyword) && CollectionUtil.isNotEmpty(keywordFields)){
            queryWrapper.and(innerWrapper ->
                    innerWrapper.or(keywordFields.contains(PcxApprovalCondFieldEnum.BILL_NO.getCode()), e -> e.like(PcxBill::getBillNo, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.CLAIMANT_NAME.getCode()), e -> e.like(PcxBill::getClaimantName,keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.DEPARTMENT_NAME.getCode()), e -> e.like(PcxBill::getDepartmentName, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.ITEM_NAME.getCode()), e -> e.like(PcxBill::getItemName, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.TRANS_DATE.getCode()), e -> e.like(PcxBill::getTransDate, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.REASON.getCode()), e -> e.like(PcxBill::getReason, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.CHECK_AMT.getCode()), e -> e.like(PcxBill::getCheckAmt, keyword))
            );
        }
        if(CollectionUtil.isNotEmpty(qo.getDepartmentCodeList())){
            queryWrapper.and(innerWrapper -> innerWrapper.in(PcxBill::getDepartmentCode, qo.getDepartmentCodeList()));
        }
        if (CollectionUtil.isNotEmpty(qo.getItemCodeList())) {
            queryWrapper.and(innerWrapper -> innerWrapper.in(PcxBill::getItemCode, qo.getItemCodeList()));
        }
        if(CollectionUtil.isNotEmpty(qo.getExpDepartmentCodeList())){
            queryWrapper.and(innerWrapper ->
                    qo.getExpDepartmentCodeList().forEach(code ->{
                        innerWrapper.or(e->e.like(PcxBill::getExpDepartmentCodes, code));
                    })
            );
        }
        if(CollectionUtil.isNotEmpty(qo.getProjectCodeList())){
            queryWrapper.and(innerWrapper ->
                    qo.getProjectCodeList().forEach(code ->{
                        innerWrapper.or(e->e.like(PcxBill::getProjectCodes, code));
                    })
            );
        }
        getOrderType(qo, orgQueryWrapper, financeApprovalTaskMap);
        return queryWrapper;
    }

    /**
     * 通过显示设置，获取默认排序方式
     *
     * @param qo
     * @param queryWrapper
     * @param financeApprovalTaskMap
     */
    private void getOrderType(PcxFinanceApprovalBillQO qo, QueryWrapper<PcxBill> queryWrapper, Map<String, FinanceApprovalTaskDTO> financeApprovalTaskMap) {
        if (StringUtil.isEmpty(qo.getSortField())) {
            PaFieldSettingQO paFieldSettingQO = new PaFieldSettingQO();
            paFieldSettingQO.setSysId(PcxConstant.SYS_ID);
            paFieldSettingQO.setMenuUrl(MENU_URL +"-"+ qo.getPageLabelCode());
            paFieldSettingQO.setFiscal(qo.getFiscal());
            paFieldSettingQO.setUserCodeOrAll(qo.getUserCode());
            paFieldSettingQO.setMofDivCode(qo.getMofDivCode());
            if (StringUtil.isNotEmpty(qo.getAgyCode())){
                paFieldSettingQO.setAgyCode(qo.getAgyCode());
            }
            try {
                List<PaFieldSettingVO> fieldSettingList = iPcxPaFieldSettingService.getUserSettings(paFieldSettingQO);
                if (!CollectionUtil.isEmpty(fieldSettingList)) {
                    // 直接在流中处理，避免创建中间列表
                    Optional<PaFieldSettingVO> firstFieldSetting = fieldSettingList.stream()
                            .filter(paFieldSettingVO -> StringUtil.isNotEmpty(paFieldSettingVO.getBudSort()))
                            .findFirst();

                    if (firstFieldSetting.isPresent()) {
                        PaFieldSettingVO paFieldSettingVO = firstFieldSetting.get();
                        setSortOrder(queryWrapper, paFieldSettingVO.getColumnCode(),paFieldSettingVO.getBudSort(), financeApprovalTaskMap);
                    }
                } else {
                    queryWrapper.lambda().orderByDesc(PcxBill::getModifiedTime);
                }
            } catch (Exception e) {
                // 异常处理
                log.error("Error occurred while processing user settings", e);
                queryWrapper.lambda().orderByDesc(PcxBill::getModifiedTime);
            }
        }else {
            setSortOrder(queryWrapper, qo.getSortField(), qo.getSortOrder(), financeApprovalTaskMap);
        }
    }

    /**
     * 设置排序字段与方式
     *
     * @param queryWrapper
     * @param sortField
     * @param sortOrder
     * @param financeApprovalTaskMap
     */
    private void setSortOrder(QueryWrapper<PcxBill> queryWrapper, String sortField, String sortOrder, Map<String, FinanceApprovalTaskDTO> financeApprovalTaskMap) {
        if (PcxApprovalCondFieldEnum.TASK_START_TIME.getCode().equals(sortField)) {
            setOrderBy(queryWrapper.lambda(), PcxBill::getModifiedTime, "desc".equalsIgnoreCase(sortOrder));
        } else if (PcxApprovalCondFieldEnum.CHECK_AMT.getCode().equals(sortField)) {
            setOrderBy(queryWrapper.lambda(), PcxBill::getCheckAmt, "desc".equalsIgnoreCase(sortOrder));
        } else if (PcxApprovalCondFieldEnum.TASK_END_TIME.getCode().equals(sortField)) {
            StringBuilder allColWithTaskEndTime = new StringBuilder().append("*, CASE id");
            financeApprovalTaskMap.forEach((key, value) -> {
                allColWithTaskEndTime.append(" WHEN ").append(key).append(" THEN '").append(value.getEndTime()).append("' ");
            });
            allColWithTaskEndTime.append(" ELSE '0' END as allColWithTaskEndTime");
            queryWrapper.select(allColWithTaskEndTime.toString());
            if ("desc".equalsIgnoreCase(sortOrder)) {
                queryWrapper.orderByDesc("allColWithTaskEndTime");
            } else {
                queryWrapper.orderByAsc("allColWithTaskEndTime");
            }
        }
    }
    private void setOrderBy(LambdaQueryWrapper<PcxBill> queryWrapper, SFunction<PcxBill, ?> field, boolean isDesc) {
        if (isDesc) {
            queryWrapper.orderByDesc(field);
        } else {
            queryWrapper.orderByAsc(field);
        }
    }

    /**
     * 根据页签编码，查询对应的工作流任务，抽取单据id与工作流任务的关系
     */
    private Map<String, FinanceApprovalTaskDTO> getTaskMapByPageLabelCode(PcxFinanceApprovalBillQO qo) {
        if(PcxApprovalPageLabelEnum.APPROVE.getCode().equals(qo.getPageLabelCode())){
            return fetchTodoTaskMap(qo);
        }else if(PcxApprovalPageLabelEnum.APPROVED.getCode().equals(qo.getPageLabelCode())){
            return fetchDoneTaskMap(qo);
        }else if(PcxApprovalPageLabelEnum.ALL.getCode().equals(qo.getPageLabelCode())){
            Map<String, FinanceApprovalTaskDTO> financeApprovalTaskMap = new HashMap<>();
            financeApprovalTaskMap.putAll(fetchTodoTaskMap(qo));
            financeApprovalTaskMap.putAll(fetchDoneTaskMap(qo));
            return financeApprovalTaskMap;
        }else{
            log.error("getTaskMapByPageLabelCode ==> 页签编码识别失败, pageLabelCode:{}", qo.getPageLabelCode());
            throw new CommonException("页签编码识别失败");
        }
    }

    /**
     * businessKey相同时，保留endTime最新的（当审批单据发生先驳回后通过的场景的时候，会存在两条已办信息记录，此时需要保留 endTime 最新的那一条记录）
     * @param oldTask 已存在的任务信息
     * @param newTask 新来到的任务信息
     */
    @SneakyThrows
    private FinanceApprovalTaskDTO distinctByEndTime(FinanceApprovalTaskDTO oldTask, FinanceApprovalTaskDTO newTask) {
        if(DateUtil.compareDate(oldTask.getEndTime(), newTask.getEndTime()) > 0){
            return oldTask;
        }
        return newTask;
    }

    /**
     * 组装财务审批单据VO列表
     */
    private static List<PcxFinanceApprovalBillVO> buildResultList(List<PcxBill> billList, Map<String, FinanceApprovalTaskDTO> financeApprovalTaskMap, IProcessService processService, PcxBillSettlementInfoService pcxBillSettlementInfoService, String pageLabelCode) {
        // 集合判空
        if(CollectionUtil.isEmpty(billList)){
            return new ArrayList<>();
        }
        // 
        // 获取结算方式, 优先展示有个人转账和对公转账的, 要补全返回中的结算方式描述
        List<String> topPriorityStypeList = Arrays.asList(SettlementTypeEnum.SETTLE_TRANSFER.getCode(), SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode());
        List<String> secondPriorityStypeList = Arrays.asList(SettlementTypeEnum.SETTLE_CHEQUE.getCode(), SettlementTypeEnum.SETTLE_BUSICARD.getCode(), SettlementTypeEnum.SETTLE_CASH.getCode());
        List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(billList.stream().map(PcxBill::getId).collect(Collectors.toList()));
        Map<String, PcxBillSettlement> topPrioritySettlementRel = settlements
                .stream().filter(settlement -> topPriorityStypeList.contains(settlement.getSettlementType()))
                .collect(Collectors.toMap(PcxBillSettlement::getBillId, Function.identity(), (a, b) -> a));
        Map<String, PcxBillSettlement> secondPrioritySettlementRel = settlements
                .stream().filter(settlement -> secondPriorityStypeList.contains(settlement.getSettlementType()))
                .collect(Collectors.toMap(PcxBillSettlement::getBillId, Function.identity(), (a, b) -> a));
        // 转换VO，并装填工作流任务信息
        List<PcxFinanceApprovalBillVO> resultList = BeanUtil.copyToList(billList,PcxFinanceApprovalBillVO.class);
        resultList.forEach(billVO -> {
            billVO.setIsDone(financeApprovalTaskMap.get(billVO.getId()).getIsDone());
            billVO.setTaskId(financeApprovalTaskMap.get(billVO.getId()).getTaskId());
            billVO.setPositionCode(financeApprovalTaskMap.get(billVO.getId()).getNodeId());
            billVO.setPositionName(financeApprovalTaskMap.get(billVO.getId()).getNodeName());
            billVO.setPositionComment(financeApprovalTaskMap.get(billVO.getId()).getComment());
            billVO.setCanRevoke(financeApprovalTaskMap.get(billVO.getId()).isCanRevoke());
            billVO.setCanFinishedRevoke(financeApprovalTaskMap.get(billVO.getId()).isCanFinishedRevoke());
            ProcessHistoryQO qo = new ProcessHistoryQO();
            qo.setBillId(billVO.getId());
            qo.setBillFuncCode(billVO.getBillFuncCode());
            CheckMsg<ProcessHistoryVO> voCheckMsg = processService.queryLatestApprovedHistory(qo);
            if (voCheckMsg.isSuccess())
                billVO.setTaskStartTime(voCheckMsg.getData().getEndTime());
            billVO.setTaskEndTime(StringUtil.isNotEmpty(financeApprovalTaskMap.get(billVO.getId()).getEndTime())?DateUtil.formatTime(financeApprovalTaskMap.get(billVO.getId()).getEndTime()):null);

            billVO.setSettlementDesc(StrUtil.EMPTY);
            PcxBillSettlement settlement = secondPrioritySettlementRel.get(billVO.getId());
            if (ObjectUtils.isNotEmpty(settlement)) {
                SettlementTypeEnum settlementType = SettlementTypeEnum.getByCode(settlement.getSettlementType());
                String settlementDesc = ObjectUtils.isNotEmpty(settlementType) ? settlementType.getName() : "";
                billVO.setSettlementDesc(settlementDesc);
            }

            settlement = topPrioritySettlementRel.get(billVO.getId());
            if (ObjectUtils.isNotEmpty(settlement)) {
                String settlementDesc = String.format("%s-%s", (settlement.getSettlementType().equals(SettlementTypeEnum.SETTLE_TRANSFER.getCode()) ?
                        SettlementTypeEnum.SETTLE_TRANSFER.getName() : SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getName()), settlement.getPayeeAccName()) ;
                billVO.setSettlementDesc(settlementDesc);
            }
        });
        return resultList;
    }

    /**
     * 模糊查询涉及到的字段枚举
     */
    @Override
    public CheckMsg<List<Map<String, Object>>> selectApprovalCondFieldEnum(PcxFinanceApprovalBillQO qo) {
        ValidUtil.checkEmptyObject(qo, "校验参数异常，请求参数不能为空");
        ValidUtil.checkEmptyStr(qo.getAgyCode(), "校验参数异常，单位编码不能为空");
        ValidUtil.checkEmptyStr(qo.getMofDivCode(), "校验参数异常，区划编码不能为空");
        ValidUtil.checkEmptyStr(qo.getFiscal(), "校验参数异常，年度不能为空");
        List<Map<String, Object>> billCondFieldList = PcxApprovalCondFieldEnum.getList();
        return CheckMsg.success(billCondFieldList);
    }

    @Override
    public CheckMsg<?> analysisExpenseDetail(PcxAnalysisQO qo) {
        if(CollectionUtil.isEmpty(qo.getExpenses())){
            return CheckMsg.fail("请先完成费用明细内容的输入");
        }
        if(CollectionUtil.isEmpty(qo.getBalanceInfo())){
            return CheckMsg.fail("请先选择指标");
        }
        List<PcxAnalysisExpenseQO> expenseList = qo.getExpenses();
        List<PcxAnalysisBalQO> balList = qo.getBalanceInfo();
        Map<String, List<PcxAnalysisExpenseQO>> result = new HashMap<>();
        if(balList.size() == 1 && expenseList.size() == 1){
            result.put(balList.get(0).getBalanceId(), expenseList);
            return CheckMsg.success().setData(result);
        }
        balList = balList.stream().filter(item -> StringUtil.isNotBlank(item.getExpecoCode())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(balList)){
            log.error("指标集合中没有部门经济分类");
            return CheckMsg.success();
        }
        List<String> expeoCodes = balList.stream().map(PcxAnalysisBalQO::getExpecoCode).collect(Collectors.toList());
        PcxBasExpExpecoQO pcxBasExpExpecoQO = new PcxBasExpExpecoQO();
        pcxBasExpExpecoQO.setMofDivCode(qo.getMofDivCode());
        pcxBasExpExpecoQO.setAgyCode(qo.getAgyCode());
        pcxBasExpExpecoQO.setFiscal(qo.getFiscal());
        pcxBasExpExpecoQO.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
        pcxBasExpExpecoQO.setExpecoCodes(expeoCodes);
        List<PcxBasExpExpeco> pcxBasExpExpecos = pcxBasExpExpecoService.selectByExpenseCode(pcxBasExpExpecoQO);
        if(CollectionUtil.isEmpty(pcxBasExpExpecos)){
            log.error("此单位未能找到对应的部门经济分类"+expeoCodes.toString());
            return CheckMsg.success();
        }
        Map<String, PcxBasExpExpeco> pcxBasExpExpecoMap = pcxBasExpExpecos.stream().collect(Collectors.toMap(PcxBasExpExpeco::getExpecoCode, expExpeco -> expExpeco, (existing, replacement) -> existing));
        Map<String, List<PcxAnalysisExpenseQO>> expense = expenseList.stream().collect(Collectors.groupingBy(PcxAnalysisExpenseQO::getExpenseTypeCode));
        for (PcxAnalysisBalQO pcxAnalysisBalQO : balList) {
            String expecoCode = pcxAnalysisBalQO.getExpecoCode();
            if(!pcxBasExpExpecoMap.containsKey(expecoCode)){
                log.error("此单位未能找到对应的部门经济分类"+expeoCodes.toString());
                continue;
            }
            String expenseCode = pcxBasExpExpecoMap.get(expecoCode).getExpenseCode();
            if(expense.containsKey(expenseCode)){
                result.put(pcxAnalysisBalQO.getBalanceId(),expense.get(expenseCode));
            }
        }
        return CheckMsg.success().setData(result);
    }

    /**
     * 根据查询对象获取待办任务列表
     * 此方法主要用于处理待办任务的查询逻辑，根据查询对象的条件决定是否查询待办任务，
     * 并将查询结果返回
     *
     * @param qo 查询对象，包含查询待办任务所需的条件，如标签、部门代码、机构代码和用户代码
     * @return 返回待办任务列表，如果查询失败或没有待办任务，则返回空列表
     */
    private Map<String, FinanceApprovalTaskDTO> fetchTodoTaskMap(PcxFinanceApprovalBillQO qo) {

        List<TodoTaskVO> todoTasks = new ArrayList<>();
        List<PaUserDesignateDTO> paUserDesignateDTOS = pcxUserDesignateService.selectValidAsDesignate(qo.getUserCode());
        log.info("当前委托人{}, 被委托信息{}", qo.getUserCode(), paUserDesignateDTOS);
        Set<String> userCodes = new HashSet<>();
        userCodes.add(qo.getUserCode());
        userCodes.addAll(paUserDesignateDTOS.stream().map(PaUserDesignateDTO::getUserCode).collect(Collectors.toList()));
        // 构建待办任务查询参数，并调用服务方法获取待办任务列表
        CheckMsg<List<TodoTaskVO>> todoMsg = processService.getTodoTaskWithCandidateAndAssigned(TodoTaskParamQO.builder()
                .mofDivCode(qo.getMofDivCode())
//                .agyCode(qo.getHostAgyCode())
//                .candidateUser(qo.getUserCode())
                .candidateUsers(userCodes)
                .build());
        // 如果查询成功，则将查询结果赋值给待办任务列表
        if (todoMsg.isSuccess()) {
            todoTasks = todoMsg.getData();
        }
        // 排除制单岗的单据，这类单据是退回的单据，不在财务审批页面演示
        List<TodoTaskVO> todoTaskVOList = todoTasks.stream().filter(
                todoTask -> !PcxNodeEnum.make_bill.getId().equals(todoTask.getNodeId())
        ).collect(Collectors.toList());
        // 集合判空和转换类型 FinanceApprovalTask
        if(CollectionUtil.isEmpty(todoTaskVOList)){
            return new HashMap<>();
        }
        List<FinanceApprovalTaskDTO> financeApprovalTaskDTOS = JSONObject.parseArray(JSONObject.toJSONString(todoTaskVOList), FinanceApprovalTaskDTO.class);
        financeApprovalTaskDTOS.forEach(financeApprovalTaskDTO -> {
            financeApprovalTaskDTO.setIsDone(0);
            financeApprovalTaskDTO.setStartTime(financeApprovalTaskDTO.getCreateTime());
        });
        return financeApprovalTaskDTOS.stream().collect(Collectors.toMap(FinanceApprovalTaskDTO::getBusinessKey, Function.identity()));
    }

    /**
     * 根据查询对象获取用户审批中的任务列表
     * 此方法专注于处理审批中的任务，特别是当查询标签为null或特定值时
     * 它通过调用流程服务来获取待办任务，旨在为用户提供一个筛选机制，
     * 以便用户可以轻松地获取他们提交审批的任务
     *
     * @param qo 查询对象，包含查询审批中任务所需的参数，如MOF部门代码、机构代码、用户代码和标签
     * @return 返回一个待审批任务的列表如果查询条件不满足或服务调用失败，将返回一个空列表
     */
    private Map<String, FinanceApprovalTaskDTO> fetchDoneTaskMap(PcxFinanceApprovalBillQO qo) {
        List<DoneTaskVO> approvingTasks = new ArrayList<>();
        // 判断查询对象的标签是否为null或特定值，以决定是否需要查询已审批任务
        // 调用流程服务获取待办任务，构建参数时使用查询对象中的相应值
        CheckMsg<List<DoneTaskVO>> approvingMsg = processService.getDoneTaskWithCandidateAndAssigned(DoneTaskParamQO.builder()
                .mofDivCode(qo.getMofDivCode())
//                .agyCode(qo.getHostAgyCode())
                .assignee(qo.getUserCode())
                .build());
        // 如果服务调用成功，则获取并保存待审批任务列表
        if (approvingMsg.isSuccess()) {
            approvingTasks = approvingMsg.getData().stream().filter(task ->
                    !Objects.equals(task.getNodeId(), PcxNodeEnum.make_bill.getId()) && (task.getOperation().equals(WorkflowConst.SUBMIT_TASK_DELETE_REASON) ||
                            task.getOperation().equals(WorkflowConst.ROLLBACK_TASK_DELETE_REASON))).collect(Collectors.toList());
        }
        // 集合判空和转换类型 FinanceApprovalTask
        if(CollectionUtil.isEmpty(approvingTasks)){
            return new HashMap<>();
        }
        List<FinanceApprovalTaskDTO> financeApprovalTaskDTOS = JSONObject.parseArray(JSONObject.toJSONString(approvingTasks), FinanceApprovalTaskDTO.class);
        financeApprovalTaskDTOS.forEach(financeApprovalTaskDTO -> financeApprovalTaskDTO.setIsDone(1));
        return financeApprovalTaskDTOS.stream().collect(Collectors.toMap(FinanceApprovalTaskDTO::getBusinessKey, Function.identity(), this::distinctByEndTime));
    }


}
