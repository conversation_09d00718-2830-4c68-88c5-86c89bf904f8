package com.pty.pcx.service.impl.ecs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.mad.vo.MadExpendVo;
import com.pty.pcx.CodeNameVO;
import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.costcontrollevel.PcxCostControlLevelService;
import com.pty.pcx.api.ecs.EcsProcessService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasFormSettingDao;
import com.pty.pcx.dao.bill.PcxBillExpDetailTravelDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.ecs.EcsModifiedBillDTO;
import com.pty.pcx.dto.ecs.EcsMsgDTO;
import com.pty.pcx.dto.ecs.inv.InvoiceBaseDto;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.ecs.impl.EcsBillExternalServiceImpl;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pcx.entity.costcontrollevel.PcxCostControlLevel;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.bill.PcxBillQO;
import com.pty.pcx.qo.bill.extrasubsidy.UpdateExtraSubsidyQO;
import com.pty.pcx.qo.costcontrollevel.PcxEmployeeWithCostLevelQO;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.*;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.ecs.dto.EcsListExpTypeCombine;
import com.pty.pcx.service.impl.ecs.handler.CommonHandler;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.costcontrollevel.PcxEmployeeWithCostLevelVO;
import com.pty.pcx.vo.ecs.*;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class EcsProcessServiceImpl extends EcsInvoiceService implements EcsProcessService {
    @Resource
    private IPcxBasItemExpService basItemExpService;
    @Resource
    private EcsExpOptService ecsExpOptService;
    @Resource
    private EcsExpCommonService ecsExpCommonService;
    @Resource
    private BillMainService billMainService;
    @Resource
    private PcxBillService pcxBillService;
    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Resource
    private EcsExpTransService ecsExpTransService;
    @Resource
    private PcxBasFormSettingDao pcxBasFormSettingDao;
    @Resource
    private PcxBasFormSettingService pcxBasFormSettingService;
    @Autowired
    private PcxCostControlLevelService pcxCostControlLevelService;
    @Resource
    private IEcsBillExternalService ecsBillExternalService;
    @Resource
    private ChangeItemProcessor changeItemProcessor;

    @Override
    public CheckMsg startExpense(StartExpenseQO expenseQO) {
        log.info("startExpense:{}", JSON.toJSONString(expenseQO));
        if (StringUtil.isEmpty(expenseQO.getTransDate())){
            expenseQO.setTransDate(DateUtil.nowDate());
        }
        MadEmployeeDTO madEmployeeDTO = queryMadEmpDTO(expenseQO);
        if (Objects.isNull(madEmployeeDTO)){
            return CheckMsg.fail("人员信息不存在");
        }
        expenseQO.setMadEmployeeDTO(madEmployeeDTO);
        DefaultClaimantDTO defaultClaimantDTO = queryEmployeeWithCostLevel(expenseQO, madEmployeeDTO);
        expenseQO.setDefaultClaimantDTO(defaultClaimantDTO);


        StopWatch stopWatch = new StopWatch("startExpense");
        InvoiceDtoCollect ecsCollect = new InvoiceDtoCollect();
        if (!CollectionUtils.isEmpty(expenseQO.getBillList())){

            stopWatch.start("doProcessInvoices");
            ecsCollect = this.doProcessInvoices(expenseQO, expenseQO.getItemCode());
            //处理代报人
            disposeDefaultClaimant(ecsCollect, expenseQO);
            stopWatch.stop();
            //没有可以的票，只有无法报销的票
            if (CollectionUtils.isEmpty(ecsCollect.getWrappers())) {
                if (CollectionUtils.isNotEmpty(ecsCollect.getWrongEcs())){
                    log.info("错误的票,无法报销:{}", JSON.toJSONString(ecsCollect.getWrongEcs()));
                    return CheckMsg.fail("所选的票为假票或已报销的票");
                }
                return CheckMsg.fail("没有要处理的票");
            }
        }else{
            expenseQO.setNoEcs(true);
        }
        String billId;
        try {
            //创建单据
            stopWatch.start("saveBillAndExpense");
            billId = ecsExpOptService.saveBillAndExpense(expenseQO, ecsCollect.getWrappers());
        } catch (RuntimeException e) {
            log.error("startExpense异常", e);
            return CheckMsg.fail(e.getMessage());
        }finally {
            stopWatch.stop();
        }
        stopWatch.start("wrapperWrongEcs");
        CheckMsg checkMsg = wrapperWrongEcs(billId, ecsCollect.getWrongEcs(), ecsCollect.getOtherExpTypeEcs());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());

        return checkMsg;
    }

    /**
     * 如果票上有代报人，并且只有一个，并且费用明细中没有其他职员，则这个代报人就是报销单的填报人
     * @param ecsCollect
     * @param expenseQO
     */
    private void disposeDefaultClaimant(InvoiceDtoCollect ecsCollect, StartExpenseQO expenseQO) {
        Map<String, DefaultClaimantDTO> authClaimantMap = ecsCollect.getAuthClaimantMap();
        Map<String, CommonHandler.EmpMsg> innerEmpMsgMap = ecsCollect.getInnerEmpMsgMap();
        //如果没有票的代报人，并且票上也没有整理出职员信息
        if (Objects.isNull(authClaimantMap) || Objects.isNull(innerEmpMsgMap)){
            return;
        }
        //如果票的代报人有多个，或者明细的职员信息有多个
        if (authClaimantMap.size()!=1 || innerEmpMsgMap.size()>1){
            return;
        }
        Iterator<String> iterator = authClaimantMap.keySet().iterator();
        String next = iterator.next();
        DefaultClaimantDTO defaultClaimantDTO = authClaimantMap.get(next);
        if (innerEmpMsgMap.isEmpty()){
            expenseQO.setDefaultClaimant(defaultClaimantDTO);
        }else{
            Iterator<String> it2 = innerEmpMsgMap.keySet().iterator();
            String n2 = it2.next();
            CommonHandler.EmpMsg empMsg = innerEmpMsgMap.get(n2);
            if (Objects.equals(defaultClaimantDTO.getEmpCode(), empMsg.getEmpCode())){
                expenseQO.setDefaultClaimant(defaultClaimantDTO);
            }
        }
    }

    private DefaultClaimantDTO queryEmployeeWithCostLevel(ExpInvoiceQO expenseQO, MadEmployeeDTO madEmployeeDTO){
        PcxEmployeeWithCostLevelQO pcxBaseDTO = new PcxEmployeeWithCostLevelQO();
        pcxBaseDTO.setAgyCode(expenseQO.getAgyCode());
        pcxBaseDTO.setFiscal(Integer.valueOf(expenseQO.getFiscal()));
        pcxBaseDTO.setMofDivCode(expenseQO.getMofDivCode());
        pcxBaseDTO.setMadCodes(Collections.singletonList(madEmployeeDTO.getEmployeeCode()));
        List<PcxEmployeeWithCostLevelVO> result = pcxCostControlLevelService.getEmpsWithCostLevelByCodes(pcxBaseDTO);
        String budLevel = "";
        if (CollectionUtils.isNotEmpty(result)){
            PcxEmployeeWithCostLevelVO employee = result.get(0);

            if (CollectionUtils.isNotEmpty(employee.getPcxCostControlLevels())){
                budLevel = employee.getPcxCostControlLevels().stream().map(PcxCostControlLevel::getCostControlCode).collect(Collectors.joining(","));
            }
            return new DefaultClaimantDTO(employee.getMadCode(), employee.getMadName(), budLevel, employee.getUserCode(), employee.getMadName());
        }
        return new DefaultClaimantDTO(madEmployeeDTO.getEmployeeCode(), madEmployeeDTO.getEmployeeName(), budLevel, madEmployeeDTO.getUserCode(), madEmployeeDTO.getEmployeeName());
    }



    private MadEmployeeDTO queryMadEmpDTO(ExpInvoiceQO expenseQO){
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setFiscal(expenseQO.getFiscal());
        pcxBaseDTO.setAgyCode(expenseQO.getAgyCode());
        pcxBaseDTO.setMofDivCode(expenseQO.getMofDivCode());
        pcxBaseDTO.setTenantId(expenseQO.getTenantId());
        return madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, expenseQO.getUserCode());
    }

    private CheckMsg wrapperWrongEcs(String billId, List<InvoiceBaseDto> wrongEcsList, List<InvoiceBaseDto> otherExpTypeList) {
        CheckMsg<PcxBillVO> view = pcxBillService.view(billId, PositionBlockEnum.ECSEXPMATCH, PositionBlockEnum.SPECIFICITY);
        EcsExpMatchVO vo = view.getData().getEcsExpMatch();
        //错误费用类型的票数量提示
        if (CollectionUtils.isNotEmpty(otherExpTypeList)){
            vo.setTidyEcsHint(String.format(PcxConstant.TIDY_ECS_HINT, otherExpTypeList.size()));
        }
        if (CollectionUtils.isNotEmpty(wrongEcsList)){
            vo.setWrongEcsList(collectWrongEcsList(wrongEcsList));
        }
        return view;
    }

    private List<EcsWrongVO> collectWrongEcsList(List<InvoiceBaseDto> wrongEcsList) {
        List<EcsWrongVO> result = new ArrayList<>();
        for (InvoiceBaseDto invoiceBaseDto : wrongEcsList) {
            EcsWrongVO vo = EcsWrongVO
                    .builder()
                    .attachId(invoiceBaseDto.getAttachId())
                    .fileName(invoiceBaseDto.getBillDescpt())
                    .billDate(invoiceBaseDto.getBillDate())
                    .build();
            if (Objects.equals(invoiceBaseDto.getIsRelExp(),PcxConstant.HAS_EXPENSE)){
                vo.setWrongMessage("重复的票");
            } else{
                vo.setWrongMessage("查验为假");
            }
            result.add(vo);
        }
        return result;
    }


    /**
     * 查询发票信息，转换成报销dto，从dto中解析出费用明细，并取出错误的票和重复的票
     * @param invoiceQO
     * @param itemCode
     * @return
     */
    private InvoiceDtoCollect doProcessInvoices(ExpInvoiceQO invoiceQO, String itemCode) {
        // 按事项查询匹配的费用类型
        PcxBasItemExpQO expQO = new PcxBasItemExpQO();
        expQO.setItemCode(itemCode);
        expQO.setAgyCode(invoiceQO.getAgyCode());
        expQO.setFiscal(invoiceQO.getFiscal());
        expQO.setMofDivCode(invoiceQO.getMofDivCode());
        List<String> expCodeList = basItemExpService.selectByItemCode(expQO).stream().map(
                PcxBasItemExp::getExpenseCode
        ).collect(Collectors.toList());
        log.info("理票选择事项关联的所有费用类型:{}", expCodeList);
        // 1.预处理
        Pair<Map<String, List>, List<InvoiceBaseDto>> mapListPair = queryEcsAndFilterFake(invoiceQO, expCodeList);
        // 2.非假票或者重复的票，但包含不是当前事项对应费用类型的票
        Map<String, List> invoiceMap = mapListPair.getLeft();

        //如果没有可用的票就返回空wrapper列表和错误的票
        List<InvoiceBaseDto> wrongEcsList = mapListPair.getRight();
        if (Objects.isNull(invoiceMap) || invoiceMap.isEmpty()){
            return InvoiceDtoCollect.of(Lists.newArrayList(), wrongEcsList, Lists.newArrayList(), new HashMap<>(), new HashMap<>());
        }

        InvoiceDtoCollect collect = pipelineDisposeEcs(invoiceMap, expCodeList, invoiceQO);

        return InvoiceDtoCollect.of(collect.getWrappers(), wrongEcsList, collect.getOtherExpTypeEcs(), collect.getAuthClaimantMap(), collect.getInnerEmpMsgMap());
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class InvoiceDtoCollect{
        //票wrapper
        List<InvoiceDtoWrapper> wrappers;
        //假票或重复的票
        List<InvoiceBaseDto> wrongEcs;
        //选的票包含的费用类型不符合事项的费用类型
        List<InvoiceBaseDto> otherExpTypeEcs;

        //票授代报人员map
        Map<String, DefaultClaimantDTO> authClaimantMap = new HashMap<>();
        //明细中内部职员map
        Map<String, CommonHandler.EmpMsg> innerEmpMsgMap = new HashMap<>();

        DefaultClaimantDTO defaultClaimant;
        public static InvoiceDtoCollect of(List<InvoiceDtoWrapper> wrappers,
                                           List<InvoiceBaseDto> wrongEcs,
                                           List<InvoiceBaseDto> otherExpTypeEcs,
                                           Map<String, DefaultClaimantDTO> authClaimantMap,
                                           Map<String, CommonHandler.EmpMsg> innerEmpMsgMap){
            return new InvoiceDtoCollect(wrappers, wrongEcs, otherExpTypeEcs, authClaimantMap, innerEmpMsgMap, null);
        }

        public static InvoiceDtoCollect ofWrapperAndOtherExpTypeEcs(List<InvoiceDtoWrapper> wrappers, List<InvoiceBaseDto> otherExpTypeEcs){
            return new InvoiceDtoCollect(wrappers, null, otherExpTypeEcs, null, null, null);
        }
    }

    public List<InvoiceDtoWrapper> analysisEcs(ExpInvoiceQO invoiceQO, String itemCode){
        EcsListExpTypeCombine combine = this.preprocessReal(invoiceQO, Lists.newArrayList());
        Map<String, List> ecsMap = combine.getEcsMap();
        InvoiceDtoCollect collect = pipelineDisposeEcs(ecsMap, Lists.newArrayList(), invoiceQO);
        return collect.getWrappers();
    }

    @Override
    public CheckMsg<?> unMatchEcsReplenishTrip(UnMatchEcsReplenishQO unMatchEcsReplenishQO) {
        log.info("unMatchEcsReplenishTrip, param:{}", JSON.toJSONString(unMatchEcsReplenishQO));
        PcxBill view = billMainService.view(unMatchEcsReplenishQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        StopWatch stopWatch = new StopWatch("unMatchEcsReplenishTrip");

        try {
            ecsExpOptService.unMatchEcsReplenishTrip(view, unMatchEcsReplenishQO);
        }catch (RuntimeException e){
            log.error("unMatchEcsReplenishTrip,error", e);
            return CheckMsg.fail(e.getMessage());
        }
        stopWatch.start("ecsView");
        CheckMsg checkMsg = ecsView(unMatchEcsReplenishQO.getBillId());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return checkMsg;
    }

    @Override
    public CheckMsg<?> updateTripTime(UpdateTripTimeQO updateTripTimeQO) {
        log.info("updateTripTime, param:{}", JSON.toJSONString(updateTripTimeQO));
        PcxBill view = billMainService.view(updateTripTimeQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        StopWatch stopWatch = new StopWatch("updateTripTime");

        try {
            ecsExpOptService.updateTripTime(view, updateTripTimeQO);
        }catch (RuntimeException e){
            log.error("updateTripTime,error", e);
            return CheckMsg.fail(e.getMessage());
        }
        stopWatch.start("ecsView");
        CheckMsg checkMsg = ecsView(updateTripTimeQO.getBillId());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return checkMsg;
    }

    @Override
    public CheckMsg<?> updateAddition(UpdateAdditionQO updateEcsQO) {
        if (Objects.isNull(updateEcsQO.getA8PpPro())){
            updateEcsQO.setA8PpPro("");
        }
        if (Objects.isNull(updateEcsQO.getA8PpProCode())){
            updateEcsQO.setA8PpProCode("");
        }
        if (Objects.isNull(updateEcsQO.getDeferredAssetsCode())){
            updateEcsQO.setDeferredAssetsCode("");
        }
        if (Objects.isNull(updateEcsQO.getDeferredAssets())){
            updateEcsQO.setDeferredAssets("");
        }
        if (Objects.isNull(updateEcsQO.getBiIncomeTypeCode())){
            updateEcsQO.setBiIncomeTypeCode("");
        }
        if (Objects.isNull(updateEcsQO.getBiIncomeType())){
            updateEcsQO.setBiIncomeType("");
        }
        if (Objects.isNull(updateEcsQO.getDevProCode())){
            updateEcsQO.setDevProCode("");
        }
        if (Objects.isNull(updateEcsQO.getDevPro())){
            updateEcsQO.setDevPro("");
        }
        if (Objects.isNull(updateEcsQO.getTenderingProCode())){
            updateEcsQO.setTenderingProCode("");
        }
        if (Objects.isNull(updateEcsQO.getTenderingPro())){
            updateEcsQO.setTenderingPro("");
        }
        if (Objects.isNull(updateEcsQO.getCustomerCode())){
            updateEcsQO.setCustomerCode("");
        }
        if (Objects.isNull(updateEcsQO.getCustomer())){
            updateEcsQO.setCustomer("");
        }
        if (Objects.isNull(updateEcsQO.getAccountingIncomeTypeCode())){
            updateEcsQO.setAccountingIncomeTypeCode("");
        }
        if (Objects.isNull(updateEcsQO.getAccountingIncomeType())){
            updateEcsQO.setAccountingIncomeType("");
        }
        if (Objects.isNull(updateEcsQO.getBudGetItemCode())){
            updateEcsQO.setBudGetItemCode("");
        }
        if (Objects.isNull(updateEcsQO.getBudGetItem())){
            updateEcsQO.setBudGetItem("");
        }
        if (Objects.isNull(updateEcsQO.getAccountingExpenseItemCode())){
            updateEcsQO.setAccountingExpenseItemCode("");
        }
        if (Objects.isNull(updateEcsQO.getAccountingExpenseItem())){
            updateEcsQO.setAccountingExpenseItem("");
        }
        ecsExpTransService.updateAddition(updateEcsQO);
        return CheckMsg.success("ok");
    }

    @Resource
    private PcxBillExpDetailTravelDao pcxBillExpDetailTravelDao;

    @Override
    public CheckMsg<?> queryDetailAddition(QueryDetailAdditionQO queryDetailAdditionQO) {
        DetailAdditionVO vo = new DetailAdditionVO();
        PcxBillExpDetailTravel detailTravel = pcxBillExpDetailTravelDao.selectById(queryDetailAdditionQO.getAdditionQueryId());
        if (Objects.nonNull(detailTravel)){
            BeanUtils.copyProperties(detailTravel, vo);
        }
        return CheckMsg.success(vo);
    }

    @Override
    public int cleanInvalidExpenseBill(long interval) {
        long startTime = System.currentTimeMillis();
        List<String> billIds = pcxBillService.selectInvalidBillIds();
        int num = 0;
        while (!billIds.isEmpty()){
            for (String billId : billIds) {
                PcxBillQO pcxBillQO = new PcxBillQO();
                pcxBillQO.setBillId(billId);
                try {
                    pcxBillService.deleteData(pcxBillQO);
                    num ++;
                } catch (Exception e) {
                    log.error("cleanInvalidExpenseBill->deleteData,error", e);
                }
            }
            if (System.currentTimeMillis() - startTime > interval){
                break;
            }
            billIds = pcxBillService.selectInvalidBillIds();
        }
        log.info("CleanInvalidExpenseBillTask::end,clean {} record", num);
        return num;
    }

    /**
     * 从票解析出费用明细，并表明票是否已匹配
     * @param invoiceMap
     * @param expCodeList
     * @param invoiceQO
     * @return
     */
    private InvoiceDtoCollect pipelineDisposeEcs(Map<String, List> invoiceMap,
                                                       List<String> expCodeList,
                                                       ExpInvoiceQO invoiceQO) {
        // 2.打标识
        InvoiceDtoCollect collect = this.wrapInvoice(invoiceMap, expCodeList);
        List<InvoiceDtoWrapper> wrappers = collect.getWrappers();
        if (CollectionUtils.isNotEmpty(wrappers)){
            // 3.组装处理链的上下文
            ProcessContext context = ProcessContextBuilder.build(wrappers, expCodeList, invoiceQO);
            // 4.处理链执行
            // 可多线程执行
            wrappers.forEach(w -> {
                if (!w.isSkipProcess()) { // 跳过则不进行处理
                    processInvoice(w, context);
                }else{
                    w.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
                }
            });
            collect.setAuthClaimantMap(context.getAuthClaimantMap());
            collect.setInnerEmpMsgMap(context.getEmpMsgMap());
        }
        return collect;
    }

    /**
     * 查询票信息并过滤出不可用的票
     * @param invoiceQO
     * @return
     */
    private Pair<Map<String, List>, List<InvoiceBaseDto>> queryEcsAndFilterFake(ExpInvoiceQO invoiceQO, List<String> itemExpTypeList) {
        // 1.预处理
        EcsListExpTypeCombine combine = this.preprocessReal(invoiceQO, itemExpTypeList);
        Map<String, List> invoiceMap = combine.getEcsMap();
        if (Objects.isNull(invoiceMap) || invoiceMap.isEmpty()) {
            return Pair.of(new HashMap<>(), new ArrayList<>());
        }
        List<InvoiceBaseDto> wrongEcsList = Lists.newArrayList();
        if (!invoiceQO.isChangeItemCode()){
            wrongEcsList = filterDisableEcs(invoiceMap);
        }

        return Pair.of(combine.getEcsMap(), wrongEcsList);
    }

    /**
     * 过滤出不可用的票
     * @param invoiceMap
     * @return
     */
    public List<InvoiceBaseDto> filterDisableEcs(Map<String, List> invoiceMap){
        Iterator<Map.Entry<String, List>> iterator = invoiceMap.entrySet().iterator();
        List<InvoiceBaseDto> wrongEcsList = new ArrayList<>();
        while (iterator.hasNext()){
            Map.Entry<String, List> next = iterator.next();
            Iterator invoiceIt = next.getValue().iterator();
            while (invoiceIt.hasNext()){
                InvoiceBaseDto ecs = (InvoiceBaseDto) invoiceIt.next();
                //合同可以多次报销
                if (!Objects.equals(ecs.getBillTypeCode(), EcsEnum.BillType.FILE_CONTRACT.getCode())
                    && (ecs.getIsRelExp() == PcxConstant.HAS_EXPENSE
                        || PcxConstant.FAKE_ECS.equals(ecs.getIsValid())) ){
                    wrongEcsList.add(ecs);
                    invoiceIt.remove();
                }
            }
            if (CollectionUtils.isEmpty(next.getValue())){
                iterator.remove();
            }
        }
        return wrongEcsList;
    }
    /**
     * 对发票map进行包装
     * @param invoiceMap
     * @param expCodeList
     * @return
     */
    private InvoiceDtoCollect wrapInvoice(Map<String, List> invoiceMap, Collection<String> expCodeList) {

        List<InvoiceDtoWrapper> wrappers = new ArrayList<>();
        List<InvoiceBaseDto> otherExpTypeEcs = new ArrayList<>();
        invoiceMap.forEach((k, v) -> {
            v.forEach(i -> {
                InvoiceBaseDto ecs = (InvoiceBaseDto) i;
                if (ecs.isOtherBizEcs()){
                    otherExpTypeEcs.add(ecs);
                }else {
                    InvoiceDtoWrapper dtoWrapper = InvoiceDtoWrapper.builder()
                            .type(k).dto(i).build();
                    // 按expList对wrapper的flag进行赋值，不在expList内的设置为不用处理
                    dtoWrapper.setSkipProcess(InvoiceDtoUtil.isSkipProcess(dtoWrapper, expCodeList));

                    wrappers.add(dtoWrapper);
                }
            });
        });
        return InvoiceDtoCollect.ofWrapperAndOtherExpTypeEcs(wrappers, otherExpTypeEcs);
    }

    /**
     * 处理单张发票
     * @param wrapper
     * @param context
     */
    private void processInvoice(InvoiceDtoWrapper wrapper, ProcessContext context) {
        InvoiceProcessChain chain = new InvoiceProcessChain(context);
        chain.process(wrapper);
    }

    @Override
    public CheckMsg addEcsBill(AddInvoicesQO invoiceQO) {
        log.info("addEcsBill:{}", JSON.toJSONString(invoiceQO));
        // 2、查询报销单
        PcxBill view = billMainService.view(invoiceQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        StopWatch stopWatch = new StopWatch("addEcsBill");
        stopWatch.start("doProcessInvoices");
        // 1、如果存在ecs的发票ids，进行预处理
        InvoiceDtoCollect collect = this.doProcessInvoices(invoiceQO, invoiceQO.getItemCode());
        stopWatch.stop();
        log.info("addEcsBill wrappers:{}", JSON.toJSONString(collect));
        if (CollectionUtils.isEmpty(collect.getWrappers())
                && CollectionUtils.isEmpty(collect.getWrongEcs())
                && CollectionUtils.isEmpty(collect.getOtherExpTypeEcs())){
            return CheckMsg.fail("未查询到票信息");
        }
        if (CollectionUtils.isNotEmpty(collect.getWrappers())){
            try {
                stopWatch.start("addEcsBills");
                ecsExpOptService.addEcsBills(invoiceQO, collect.getWrappers(), view);
            } catch (RuntimeException e) {
                log.error("addEcsBill异常", e);
                return CheckMsg.fail(e.getMessage());
            }finally {
                stopWatch.stop();
            }
        }
        stopWatch.start("wrapperWrongEcs");
        CheckMsg checkMsg = wrapperWrongEcs(view.getId(), collect.getWrongEcs(), collect.getOtherExpTypeEcs());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());

        return checkMsg;
    }

    @Override
    public CheckMsg ecsView(String billId) {
        return pcxBillService.view(billId, PositionBlockEnum.ECSEXPMATCH, PositionBlockEnum.SPECIFICITY);
    }

    @Override
    public CheckMsg delEcsBillExpense(DelExpenseQO invoiceQO) {
        log.info("delEcsBillExpense:{}", JSON.toJSONString(invoiceQO));
        // 2、查询报销单
        PcxBill view = billMainService.view(invoiceQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        if (StringUtil.isEmpty(invoiceQO.getEcsBillId()) && StringUtil.isEmpty(invoiceQO.getExpenseDetailId())){
            return CheckMsg.fail("票id或者明细id不能同时为空");
        }
        StopWatch stopWatch = new StopWatch("addEcsBill");
        try {
            stopWatch.start("delEcsBillExpense");
            ecsExpOptService.delEcsBillExpense(invoiceQO, view);
        } catch (RuntimeException e) {
            log.error("delEcsBillExpense异常", e);
            return CheckMsg.fail(e.getMessage());
        }finally {
            stopWatch.stop();
        }
        stopWatch.start("ecsView");
        CheckMsg checkMsg = ecsView(invoiceQO.getBillId());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return checkMsg;
    }

    @Override
    public CheckMsg ecsExpView(UpdateEcsDetailAndPaymentQO updateQO) {
        log.info("ecsExpView:{}", JSON.toJSONString(updateQO));
        PcxBill view = billMainService.view(updateQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        return CheckMsg.success(ecsExpOptService.ecsExpView(updateQO, view));
    }

    @Override
    public CheckMsg updateEcs(UpdateEcsQO updateQO) {
        log.info("updateEcs:{}", JSON.toJSONString(updateQO));
        PcxBill view = billMainService.view(updateQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        if (updateQO.getItemExpenseList()
                .stream()
                .allMatch(item->Objects.isNull(item.getExpenseList())
                    || item.getExpenseList().isEmpty())){
            return CheckMsg.fail("费用明细不能为空");
        }
        StopWatch stopWatch = new StopWatch("updateEcs");
        try {
            stopWatch.start("updateEcs");
            ecsExpOptService.updateEcs(updateQO, view);
        } catch (RuntimeException e) {
            log.error("updateEcs", e);
            return CheckMsg.fail(e.getMessage());
        }finally {
            stopWatch.stop();
        }
        stopWatch.start("ecsView");
        CheckMsg checkMsg = ecsView(updateQO.getBillId());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());

        return checkMsg;
    }

    @Override
    public CheckMsg ecsItemExpView(UpdateEcsQO updateQO){
        log.info("ecsItemExpView:{}", JSON.toJSONString(updateQO));
        PcxBill view = billMainService.view(updateQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        StopWatch stopWatch = new StopWatch("ecsItemExpView");
        stopWatch.start("ecsItemExpView");
        EcsExpViewVO vo = ecsExpOptService.ecsItemExpView(updateQO, view);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return CheckMsg.success(vo);
    }

    @Override
    public CheckMsg updateNoEcsDetail(UpdateNoEcsDetailQO updateNoEcsDetailQO) {
        log.info("updateNoEcsDetail:{}", JSON.toJSONString(updateNoEcsDetailQO));
        PcxBill view = billMainService.view(updateNoEcsDetailQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        StopWatch stopWatch = new StopWatch("ecsItemExpView");

        try {
            stopWatch.start("updateNoEcsDetail");
            ecsExpOptService.updateNoEcsDetail(updateNoEcsDetailQO, view);
        } catch (RuntimeException e) {
            log.error("updateNoEcsDetail异常", e);
            return CheckMsg.fail(e.getMessage());
        }finally {
            stopWatch.stop();
        }
        stopWatch.start("ecsView");
        CheckMsg checkMsg = ecsView(updateNoEcsDetailQO.getBillId());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());

        return checkMsg;
    }
    @Override
    public CheckMsg<List<CodeNameVO>> getNoEcsReason(QueryNoEcsQO qo) {
        List<CodeNameVO> result = new ArrayList<>();
        List<NoEcsReasonEnum> byExpenseCode = NoEcsReasonEnum.getByExpenseCode(qo.getExpenseTypeCode());
        if (CollectionUtils.isNotEmpty(byExpenseCode)){
            for (NoEcsReasonEnum noEcsReasonEnum : byExpenseCode) {
                result.add(CodeNameVO.builder().code(noEcsReasonEnum.getCode()).name(noEcsReasonEnum.getName()).build());
            }
        }
        return CheckMsg.success(result);
    }

    @Override
    public CheckMsg addEcsCommonBill(AddInvoicesQO invoiceQO) {
        log.info("addEcsCommonBill:{}", JSON.toJSONString(invoiceQO));
        // 2、查询报销单
        PcxBill view = billMainService.view(invoiceQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        Pair<Map<String, List>, List<InvoiceBaseDto>> mapListPair = queryEcsAndFilterFake(invoiceQO, Lists.newArrayList());
        Map<String, List> ecsMap = mapListPair.getLeft();
        if (Objects.isNull(ecsMap) || ecsMap.isEmpty()) {
            if (CollectionUtils.isNotEmpty(mapListPair.getRight())){
                return CheckMsg.fail("所选票为假票或已报销的票");
            }
            return CheckMsg.fail("没有要处理的票");
        }
        try {
            ecsExpCommonService.addEcsBills(invoiceQO, ecsMap, view);
        } catch (RuntimeException e) {
            log.error("addEcsCommonBill异常", e);
            return CheckMsg.fail(e.getMessage());
        }

        return wrapperHitMessage(invoiceQO.getBillId(), invoiceQO.getNoIncludeEcs());
    }

    @Override
    public CheckMsg startCommonExpense(StartExpenseQO expenseQO) {
        log.info("startCommonExpense:{}", JSON.toJSONString(expenseQO));
        if (StringUtil.isEmpty(expenseQO.getTransDate())){
            expenseQO.setTransDate(DateUtil.nowDate());
        }

        MadEmployeeDTO madEmployeeDTO = queryMadEmpDTO(expenseQO);
        if (Objects.isNull(madEmployeeDTO)){
            return CheckMsg.fail("人员信息不存在");
        }

        // 1.预处理
        Map<String, List> ecsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(expenseQO.getBillList())){
            Pair<Map<String, List>, List<InvoiceBaseDto>> mapListPair = queryEcsAndFilterFake(expenseQO, Lists.newArrayList());
            ecsMap = mapListPair.getLeft();
            if (Objects.isNull(ecsMap) || ecsMap.isEmpty()) {
                if (CollectionUtils.isNotEmpty(mapListPair.getRight())){
                    return CheckMsg.fail("所选票为假票或已报销的票");
                }
                return CheckMsg.fail("没有要处理的票");
            }
        }

        String billId;
        try {
            //创建单据
            billId = ecsExpCommonService.saveBillAndExpense(expenseQO, ecsMap);
        } catch (RuntimeException e) {
            log.error("startCommonExpense异常", e);
            return CheckMsg.fail(e.getMessage());
        }
        return wrapperHitMessage(billId, expenseQO.getNoIncludeEcs(), expenseQO.getTypeArr());
    }

    private CheckMsg wrapperHitMessage(String billId, List<String> noIncludeEcs, String... typeArr) {
        CheckMsg checkMsg = queryBillView(billId, typeArr);
        PcxBillVO pcxBillVO = (PcxBillVO) checkMsg.getData();
        EcsExpMatchVO ecsExpMatch = pcxBillVO.getEcsExpMatch();
        if (Objects.nonNull(ecsExpMatch) && CollectionUtils.isNotEmpty(noIncludeEcs)){
            ecsExpMatch.setTidyEcsHint(String.format(PcxConstant.COMMON_TIDY_ECS_HINT, noIncludeEcs.size()));
        }
        return checkMsg;
    }

    private CheckMsg queryBillView(String billId, String... typeArray){
        Set<String> typeSet = new HashSet<>();
        if (Objects.nonNull(typeArray)){
            typeSet.addAll(Arrays.asList(typeArray));
        }
        typeSet.add(PositionBlockEnum.ECSEXPMATCH.getCode());
        typeSet.add(PositionBlockEnum.CONTRACT.getCode());

        return pcxBillService.view(billId, new ArrayList<>(typeSet));
    }

    @Override
    public CheckMsg<?> delEcsCommonExpense(DelEcsCommonQO qo) {
        log.info("delEcsCommonExpense:{}", JSON.toJSONString(qo));
        // 2、查询报销单
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        try {
            ecsExpCommonService.delEcsBillExpense(qo, view);
        } catch (RuntimeException e) {
            log.error("delEcsCommonExpense异常", e);
            return CheckMsg.fail(e.getMessage());
        }

        return queryBillView(qo.getBillId());
    }

    @Override
    public CheckMsg<?> updateEcsCommon(UpdateEcsCommonQO qo) {
        if (CollectionUtil.isEmpty(qo.getEcsAmtList())){
            return CheckMsg.fail("票据金额不能为空");
        }
        log.info("updateEcsCommon:{}", JSON.toJSONString(qo));
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        try {
            ecsExpCommonService.updateEcsCommon(qo, view);
        } catch (RuntimeException e) {
            log.error("updateEcsCommon", e);
            return CheckMsg.fail(e.getMessage());
        }
        return queryBillView(qo.getBillId());
    }

    @Override
    public CheckMsg updateNoEcsCommon(UpdateNoEcsCommonQO qo) {
        log.info("updateNoEcsCommon:{}", JSON.toJSONString(qo));
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        ecsExpCommonService.updateNoEcsCommon(qo, view);

        return queryBillView(qo.getBillId());
    }

    @Override
    public CheckMsg updateEcsExpType(EcsRelSumQO ecsRelSumQO) {
        log.info("updateEcsExpType:{}", JSON.toJSONString(ecsRelSumQO));
        PcxBill bill = billMainService.view(ecsRelSumQO.getBillId());
        if (Objects.isNull(bill)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!bill.getApproveStatus().equals(BillApproveStatusEnum.APPROVING.getCode())){
            return CheckMsg.fail("报销单当前状态不是审核中状态不可编辑费用类型");
        }
        ecsExpCommonService.updateEcsExpType(ecsRelSumQO, bill);

        return CheckMsg.success(ecsExpCommonService.ecsView(bill));
    }

    @Override
    public CheckMsg updateEcsExpTypeNew(EcsRelSumQO ecsRelSumQO) {
        log.info("updateEcsExpTypeNew:{}", JSON.toJSONString(ecsRelSumQO));
        PcxBill bill = billMainService.view(ecsRelSumQO.getBillId());
        if (Objects.isNull(bill)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!bill.getApproveStatus().equals(BillApproveStatusEnum.APPROVING.getCode())){
            return CheckMsg.fail("报销单当前状态不是审核中状态不可编辑费用类型");
        }
        ecsExpCommonService.updateEcsExpType(ecsRelSumQO, bill);
        CheckMsg checkMsg = queryBillView(ecsRelSumQO.getBillId(), PositionBlockEnum.SPECIFICITY.code);
        PcxBillVO pcxBillVO = (PcxBillVO) checkMsg.getData();
        pcxBillVO.setDataChanged(ecsRelSumQO.isDataChange());
        return checkMsg;
    }
    @Override
    public CheckMsg ecsRelList(QueryEcsRelItemQO itemQO) {
        log.info("ecsRelItem:{}", JSON.toJSONString(itemQO));

        PcxBill bill = billMainService.view(itemQO.getBillId());
        if (Objects.isNull(bill)){
            return CheckMsg.fail("未查询到报销单信息");
        }

        EcsExpCommonService.EcsRelMsgTemp ecsRelMsgTemp = ecsExpCommonService.ecsRelList(bill,a->true);
        return CheckMsg.success(ecsRelMsgTemp.getEcsRelVOS());
    }

    @Override
    public CheckMsg ecsRelItem(QueryEcsRelItemQO itemQO) {
        log.info("ecsRelItem:{}", JSON.toJSONString(itemQO));

        PcxBill bill = billMainService.view(itemQO.getBillId());
        if (Objects.isNull(bill)){
            return CheckMsg.fail("未查询到报销单信息");
        }

        return CheckMsg.success(ecsExpCommonService.ecsRelItemList(itemQO, bill));
    }

    @Override
    public CheckMsg commonBillExpenseTypeList(CommonExpenseTypeQO qo) {
        log.info("commonBillExpenseTypeList:{}", JSON.toJSONString(qo));

        PcxBill bill = billMainService.view(qo.getBillId());
        if (Objects.isNull(bill)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        EcsExpCommonService.MatchEcsRelExpenseTypeDTO ecsRelExpenseType = ecsExpCommonService.getEcsRelExpenseType(Lists.newArrayList(), bill);
        return CheckMsg.success(ecsRelExpenseType.getExpenseTypeList());
    }

    @Override
    public CheckMsg ecsExpCommonView(UpdateEcsCommonQO updateQO) {
        log.info("ecsExpCommonView:{}", JSON.toJSONString(updateQO));
        PcxBill view = billMainService.view(updateQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        return CheckMsg.success(ecsExpCommonService.ecsExpCommonView(updateQO, view));
    }


    @Override
    public CheckMsg queryExpenseTypeAddition(QueryExpenseTypeAdditionQO updateQO) {
        log.info("queryExpenseTypeAddition:{}", JSON.toJSONString(updateQO));
        List<BlockPropertyVO> result = pcxBasFormSettingDao.collectExpenseTypeAdditionField(updateQO);
        if (CollectionUtils.isNotEmpty(result)){
            MadExpendVo costItemData = ecsExpOptService.getCostItemData(updateQO.getFiscal(), updateQO.getAgyCode(), updateQO.getMofDivCode(), updateQO.getExpenseTypeCode());
            if (Objects.nonNull(costItemData)){
                for (BlockPropertyVO blockPropertyVO : result) {
                    if (Objects.equals(blockPropertyVO.getFieldValue(), "budGetItemCode")){
                        blockPropertyVO.setDefVal(costItemData.getBudgetItemCode());
                    } else if (Objects.equals(blockPropertyVO.getFieldValue(),"accountingExpenseItemCode")) {
                        blockPropertyVO.setDefVal(costItemData.getAccountingItemCode());
                    }
                }
            }
            return CheckMsg.success(result);
        }
        return CheckMsg.success(pcxBasFormSettingService.getDefaultExpenseTypeAdditionField());
    }

    @Override
    public CheckMsg<?> calculateTaxAudit(EcsCalculateTaxQO qo) {
        log.info("ecsExpCommonView:{}", JSON.toJSONString(qo));
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        return CheckMsg.success(ecsExpCommonService.calculateTaxAudit(qo));
    }

    @Override
    public CheckMsg<?> bindContract(BindContractQO qo) {
        log.info("bindContract:{}", JSON.toJSONString(qo));
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不是提交状态不可编辑费用");
        }
        Pair<Map<String, List>, List<InvoiceBaseDto>> mapListPair = queryEcsAndFilterFake(qo, Lists.newArrayList());
        Map<String, List> ecsMap = mapListPair.getLeft();
        if (Objects.isNull(ecsMap) || ecsMap.isEmpty()) {
            return CheckMsg.fail("没有查询到合同信息");
        }
        try {
            ecsExpCommonService.bindContract(ecsMap.get(EcsBillExternalServiceImpl.ECS_FLAG_CONTRACT), view, qo);
        } catch (RuntimeException e) {
            log.error("bindContract", e);
            return CheckMsg.fail(e.getMessage());
        }

        return queryBillView(qo.getBillId());
    }

    @Override
    public CheckMsg<?> unbindContract(BindContractQO qo) {
        log.info("unbindContract:{}", JSON.toJSONString(qo));
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不是提交状态不可编辑费用");
        }
        try {
            ecsExpCommonService.unbindContract(view);
        } catch (RuntimeException e) {
            log.error("unbindContract", e);
            return CheckMsg.fail(e.getMessage());
        }

        return queryBillView(qo.getBillId());
    }

    @Override
    public CheckMsg updateEcsAttach(UpdateEcsAttachQO updateEcsAttachQO) {
        log.info("updateEcsAttach:{}", JSON.toJSONString(updateEcsAttachQO));
        PcxBill view = billMainService.view(updateEcsAttachQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        try {
            return CheckMsg.success(ecsExpOptService.updateEcsAttach(updateEcsAttachQO, view));
        } catch (RuntimeException e) {
            log.error("updateEcsAttach", e);
            return CheckMsg.fail(e.getMessage());
        }
    }

    @Override
    public CheckMsg<?> updateExtraSubsidy(UpdateExtraSubsidyQO qo) {
        // 查询报销单
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }

        try {
            ecsExpOptService.updateExtraSubsidy(qo, view);
        } catch (RuntimeException e) {
            log.error("updateExtraSubsidy", e);
            return CheckMsg.fail(e.getMessage());
        }
        return queryBillView(qo.getBillId());
    }


    @Override
    public CheckMsg<?> queryEcsModifyField(QueryEcsModifyFieldQO qo) {
        Pair<Map<String, EcsMsgDTO>, List<JSONObject>> ecsMsgMap = ecsBillExternalService.getEcsMsgMapByEcsIds(Collections.singletonList(qo.getEcsBillId()), qo.getFiscal(), qo.getAgyCode(), qo.getMofDivCode());
        if (Objects.nonNull(ecsMsgMap) && Objects.nonNull(ecsMsgMap.getKey())){
            EcsMsgDTO ecsMsgDTO = ecsMsgMap.getKey().get(qo.getEcsBillId());
            if (Objects.nonNull(ecsMsgDTO)){
                return CheckMsg.success(ecsMsgDTO.getEcsModifiedBillDTO());
            }
        }
        return CheckMsg.success(new EcsModifiedBillDTO());
    }

    @Override
    public CheckMsg<?> queryNoEcsDetail(QueryNoEcsDetailQO qo) {
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        NoEcsDetailVO detailVO = new NoEcsDetailVO();
        try{
            if (Objects.equals(ItemBizTypeEnum.TRAVEL.getCode(), view.getBizType())){
                detailVO = ecsExpOptService.queryNoEcsDetail(qo, view);
            }
            if (Objects.equals(ItemBizTypeEnum.COMMON.getCode(), view.getBizType())){
                detailVO = ecsExpCommonService.queryNoEcsDetail(qo, view);
            }
        }catch (RuntimeException e){
            log.error("queryNoEcsDetail error", e);
            return CheckMsg.fail(e.getMessage());
        }

        return CheckMsg.success(detailVO);
    }

    @Override
    public CheckMsg<?> detailMountTrip(DetailMountTripQO qo) {
        // 查询报销单
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }

        try {
            ecsExpOptService.detailMountTrip(qo, view);
        } catch (RuntimeException e) {
            log.error("detailMountTrip", e);
            return CheckMsg.fail(e.getMessage());
        }
        return queryBillView(qo.getBillId());
    }

    @Override
    public CheckMsg<?> changeItem(ChangeItemQO qo) {
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        qo.setPcxBill(view);
        if (Objects.equals(qo.getItemCode(), view.getItemCode())){
            return CheckMsg.fail("目标事项不能与当前事项一致");
        }

        try {
            return changeItemProcessor.changeItem(qo);
        } catch (RuntimeException e) {
            log.error("changeItem", e);
            return CheckMsg.fail(e.getMessage());
        }
    }
}
