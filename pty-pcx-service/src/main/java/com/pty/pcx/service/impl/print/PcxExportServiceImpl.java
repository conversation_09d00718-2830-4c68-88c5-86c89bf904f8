package com.pty.pcx.service.impl.print;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.pty.fileservice.api.IPubFileService;
import com.pty.fileservice.api.IPubFileStoreService;
import com.pty.fileservice.common.util.FileUploadUtil;
import com.pty.fileservice.entity.PaAttach;
import com.pty.pcx.api.print.IPcxExportService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.dto.pa.PaAttachDTO;
import com.pty.pcx.qo.print.PcxExportForEcsQO;
import com.pty.pcx.trans.PaTransformer;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.ureport2.api.IReportFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

@Slf4j
@Service
public class PcxExportServiceImpl implements IPcxExportService {
    private final BiFunction<PcxExportForEcsQO, Boolean, String> TEMPLATE_NAME_FUNC = (qo, includeAgy) -> String.format("pdf_%s_%s", qo.getBillFunc(), qo.getBillType())
            + (includeAgy ? String.format("_%s_%s", qo.getAgyCode(), qo.getMofDivCode()) : StrUtil.EMPTY) + ".ureport.xml";

    // 模板文件查找优先级, 格式 pdf_billFunc_tenantId_agyCode_mofDivCode_billType.ureport.xml 依次递减, 最少要提供, pdf_apply.ureport.xml 和 pdf_expense.ureport.xml 两个模板文件
    private static final List<Function<PcxExportForEcsQO, String>> TEMPLATE_FUNC_LIST = Arrays.asList(
            (qo) -> String.format("pdf_%s_%s_%s_%s_%s.ureport.xml", qo.getBillFunc(), PtyContext.getTenantId(), qo.getAgyCode(), qo.getBillType(), qo.getMofDivCode()),
            (qo) -> String.format("pdf_%s_%s_%s_%s.ureport.xml", qo.getBillFunc(), PtyContext.getTenantId(), qo.getAgyCode(), qo.getBillType()),
            (qo) -> String.format("pdf_%s_%s_%s.ureport.xml", qo.getBillFunc(), PtyContext.getTenantId(), qo.getAgyCode()),
            (qo) -> String.format("pdf_%s_%s.ureport.xml", qo.getBillFunc(), PtyContext.getTenantId()),
            (qo) -> String.format("pdf_%s.ureport.xml", qo.getBillFunc())
    );

    @Autowired(required = false)
    private IReportFileService reportFileService;
    @Autowired
    @Lazy
    private IPubFileService pubFileService;
    @Value("${pty.pub.upload.basedir:''}")
    private String baseDir;

    @Value("${pty.pcx.pdf.dir:/PCX/PDF/}")
    private String pcxPdfDir;

    @Autowired
    private IPubFileStoreService pubFileStoreService;

    @Override
    public List<PaAttachDTO> exportEcsBizPdf(PcxExportForEcsQO exportQO) {
        String template = StrUtil.EMPTY;
        for (Function<PcxExportForEcsQO, String> func : TEMPLATE_FUNC_LIST) {
            if (reportFileService.fileIsExist(func.apply(exportQO)) == PubConstant.LOGIC_TRUE) {
                template = func.apply(exportQO);
                log.info(">>>生成内生凭证pdf接口找到该ureport文件:{}", template);
                break;
            }
            log.warn(">>>生成内生凭证pdf接口没有找到该ureport文件:{}", func.apply(exportQO));
        }

        Assert.notBlank(template, "没有找到内生凭证对应的模板文件, agy {}, billFunc {}", exportQO.getAgyCode(), exportQO.getBillFunc());
        List<PaAttach> result = new ArrayList<>();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            reportFileService.exportPdf(template, BeanUtil.beanToMap(exportQO), outputStream);
            String fileName = IDGenerator.id();
            PaAttach paAttach = this.upLoadPdf(fileName, outputStream.toByteArray());
            result.add(paAttach);
        } catch (Exception e) {
            log.error("生成PDF文件时发生异常: {}", e.getMessage());
        }
        return PaTransformer.INSTANCE.toAttachDtos(result);
    }


    PaAttach upLoadPdf(String fileName, byte[] content) throws IOException {
        String fullFileName = new StringBuilder(fileName).append(".pdf").toString();
        PaAttach paAttach =
                PaAttach.create(fileName, fullFileName, FileUploadUtil.getFileSize(content.length), content);
        paAttach.setSysId(PcxConstant.SYS_ID);
        //调用公共的附件上传接口
        pubFileService.uploadAttach(paAttach,paAttach,Boolean.FALSE);
        return paAttach;
    }
}
