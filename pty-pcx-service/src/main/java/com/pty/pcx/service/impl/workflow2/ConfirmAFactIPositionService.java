package com.pty.pcx.service.impl.workflow2;

import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Indexed
@Service
public class ConfirmAFactIPositionService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;
    @Override
    public List<String> getPositionIds() {
        return Collections.singletonList(PcxNodeEnum.confirm_afact.getId());
    }

    @Override
    public List<String> findPositionUser(String s) {
        return Collections.emptyList();
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
