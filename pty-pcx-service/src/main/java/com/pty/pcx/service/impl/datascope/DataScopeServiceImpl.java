package com.pty.pcx.service.impl.datascope;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pty.mad.entity.MadDepartment;
import com.pty.mad.entity.PtyDeptUser;
import com.pty.pa.security.api.OrgService;
import com.pty.pa.security.entity.PtyOrg;
import com.pty.pa.security.entity.PtyOrgUser;
import com.pty.pcx.api.bas.IPcxMadDepartmentService;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.datascope.DataScopeService;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.qo.datascope.DataScopeQO;
import com.pty.pcx.vo.datascope.DataScopeVO;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.pty.mad.api.IMadDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class DataScopeServiceImpl implements DataScopeService {

    @Autowired
    private IPcxMadDepartmentService pcxMadDepartmentService;
    @Autowired
    private IMadDepartmentService departmentService;
    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;
    @Autowired
    private CacheManager cacheManager;

    @Autowired(required = false)
    @RestClientReference(microServiceNames = {"pa"})
    private OrgService orgService;

    public static final String DATA_SCOPE_NAME = "dataScopeName";

    // 获取Token的MD5值
    private String getMd5Key() {
        String md5Key = "";
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (ObjectUtil.isNotEmpty(request)) {
            String authorization = request.getHeader("Authorization");
            if (StringUtil.isNotEmpty(authorization)) {
                try {
                    MessageDigest md = MessageDigest.getInstance("MD5");
                    byte[] hashBytes = md.digest(authorization.getBytes());
                    StringBuilder sb = new StringBuilder();
                    for (byte b : hashBytes) {
                        sb.append(String.format("%02x", b));
                    }
                    md5Key = sb.toString();
                } catch (NoSuchAlgorithmException e) {
                    log.error("MD5算法未找到", e);
                }
            }
        }
        return md5Key;
    }

    @Override
    public List<DataScopeVO> getDataScopeTree(DataScopeQO dataScopeQO) {
        //---------------------获取token缓存key------------------------
        String dataScopeCacheKey = getMd5Key();
        if (StringUtil.isEmpty(dataScopeCacheKey)) {
            dataScopeCacheKey = IDGenerator.id();
        }
        dataScopeCacheKey = "token_" + dataScopeCacheKey;
        //---------------------获取数据权限dataScope缓存map------------------------
        JSONObject dataScope = new JSONObject();
        List<DataScopeVO> dataScopeTree = new ArrayList<DataScopeVO>();
        Cache.ValueWrapper cache = cacheManager.getCache(DATA_SCOPE_NAME).get(dataScopeCacheKey);
        if (ObjectUtil.isNotEmpty(cache)) {
            Map<String, Object> map = (HashMap<String, Object>)cache.get();// 获取缓存
            if (CollectionUtil.isNotEmpty(map)) {// 存在缓存
                dataScopeTree = (List<DataScopeVO>)map.get("dataScopeTree");
                if (null != dataScopeTree) {// 从缓存中获取部门权限树，存在则直接返回
                    return dataScopeTree;
                }
                dataScopeTree = new ArrayList<DataScopeVO>();
                dataScope = JSONObject.parseObject(map.get("dataScope").toString());// 从缓存中获取部门权限值
            }
        }
        if (ObjectUtil.isEmpty(dataScope)) {// 从缓存中没有取到部门权限值则重新查询
            dataScope = this.getDataScope(dataScopeQO);// 用户数据权限设置
        }
        //---------------------组装部门权限树------------------------
        if(CollectionUtil.isNotEmpty(dataScope)){// 数据权限处理
            String code = dataScope.getString("code");
            JSONArray scopeArray = dataScope.getJSONArray("scopeList");
            if ("1".equals(code)) {// 仅个人数据权限
            } else if ("2".equals(code)){// 授权部门数据权限
                if (CollectionUtil.isNotEmpty(scopeArray)) {
                    for (int i = 0; i < scopeArray.size(); i++) {
                        JSONObject deptObject = scopeArray.getJSONObject(i);
                        DataScopeVO dataScopeVO = new DataScopeVO();
                        dataScopeVO.setPcode("0");
                        dataScopeVO.setCode(deptObject.getString("code"));
                        dataScopeVO.setName(deptObject.getString("name"));
                        dataScopeTree.add(dataScopeVO);
                    }
                }
            } else if ("3".equals(code)){// 全部数据权限
                PcxMadBaseQO pcxDepartmentQO = new PcxMadBaseQO();
                pcxDepartmentQO.setAgyCode(dataScopeQO.getAgyCode());
                pcxDepartmentQO.setMofDivCode(dataScopeQO.getMofDivCode());
                pcxDepartmentQO.setFiscal(dataScopeQO.getFiscal());
                pcxDepartmentQO.setUserCode(dataScopeQO.getUserCode());
                List<MadDepartmentDTO> pcxMadDepartmentVOList =  pcxMadDepartmentService.select(pcxDepartmentQO);
                List<DataScopeVO> finalDataScopeTree = dataScopeTree;
                pcxMadDepartmentVOList.forEach(madDepartmentDTO -> {
                    DataScopeVO dataScopeVO = new DataScopeVO();
                    dataScopeVO.setPcode(StringUtil.isEmpty(madDepartmentDTO.getParentCode()) ? "0" : madDepartmentDTO.getParentCode());
                    dataScopeVO.setCode(madDepartmentDTO.getDepartmentCode());
                    dataScopeVO.setName(madDepartmentDTO.getDepartmentName());
                    finalDataScopeTree.add(dataScopeVO);
                });
            }
        }
        //---------------------保存数据权限dataScope缓存map------------------------
        Map<String, Object> newMap = new HashMap<>();
        newMap.put("dataScope", dataScope);
        newMap.put("dataScopeTree", dataScopeTree);// 非null设置
        cacheManager.getCache(DATA_SCOPE_NAME).put(dataScopeCacheKey, newMap);// 设置缓存
        return dataScopeTree;
    }

    @Override
    public JSONObject getDataScope(DataScopeQO dataScopeQO) {
        //---------------------获取token缓存key------------------------
        String dataScopeCacheKey = getMd5Key();
        if (StringUtil.isEmpty(dataScopeCacheKey)) {
            dataScopeCacheKey = IDGenerator.id();
        }
        dataScopeCacheKey = "token_" + dataScopeCacheKey;
        //---------------------获取数据权限dataScope缓存map------------------------
        JSONObject dataScope = new JSONObject();
        Cache.ValueWrapper cache = cacheManager.getCache(DATA_SCOPE_NAME).get(dataScopeCacheKey);
        if (ObjectUtil.isNotEmpty(cache)) {
            Map<String, Object> map = (HashMap<String, Object>)cache.get();//获取缓存
            if (CollectionUtil.isNotEmpty(map)) {// 存在缓存
                dataScope = JSONObject.parseObject(map.get("dataScope").toString());
                if (ObjectUtil.isNotEmpty(dataScope)) {// 从缓存中获取部门权限值，存在则直接返回
                    return dataScope;
                }
            }
        }
        //---------------------组装部门权限值------------------------
        if (StringUtil.isEmpty(dataScopeQO.getFiscal())) {
            throw new IllegalArgumentException("年度[fiscal]不能为空");
        }
//        if (StringUtil.isEmpty(dataScopeQO.getAgyCode())) {
//            throw new IllegalArgumentException("单位编码[agyCode]不能为空");
//        }
        if (StringUtil.isEmpty(dataScopeQO.getMofDivCode())) {
            throw new IllegalArgumentException("区划编码[mofDivCode]不能为空");
        }
        if (StringUtil.isEmpty(dataScopeQO.getUserCode())) {
            throw new IllegalArgumentException("用户编码[userCode]不能为空");
        }
        JSONObject dataScopeJson = new JSONObject();
        dataScopeJson.put("code", "1");// 个人权限
        dataScopeJson.put("scope", dataScopeQO.getUserCode());
        PtyOrgUser ptyOrgUser = new PtyOrgUser();
        ptyOrgUser.setUserCode(dataScopeQO.getUserCode());
        ptyOrgUser.setMofDivCode(dataScopeQO.getMofDivCode());
        ptyOrgUser.setFiscal(Integer.parseInt(dataScopeQO.getFiscal()));
        //查询用户已授权机构
        List<PtyOrgUser> ptyOrgUserList = orgService.getOrgAuth(ptyOrgUser);
        if (CollectionUtils.isEmpty(ptyOrgUserList)){
            return dataScopeJson;
        }
        List<String> agyCodes = new ArrayList<>();
        List<String> orgIdList = ptyOrgUserList.stream().map(PtyOrgUser::getOrgId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            List<PtyOrg> ptyOrgs = orgService.getOrgCodeList(orgIdList);
            if (CollectionUtils.isNotEmpty(ptyOrgs)) {
                agyCodes = ptyOrgs.stream().map(PtyOrg::getOrgCode).collect(Collectors.toList());
            } else {
                return dataScopeJson;
            }
        }
        PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
        pcxMadBaseQO.setFiscal(dataScopeQO.getFiscal());
        pcxMadBaseQO.setMofDivCode(dataScopeQO.getMofDivCode());
//        pcxMadBaseQO.setAgyCode(dataScopeQO.getAgyCode());// 授权跨单位，此处不能限制单位编码
        pcxMadBaseQO.setUserCode(dataScopeQO.getUserCode());
        pcxMadBaseQO.setTenantId(StringUtil.isEmpty(dataScopeQO.getTenantId()) ? PtyContext.getTenantId() : dataScopeQO.getTenantId());
        List<MadEmployeeDTO> madEmployeeDTOS = pcxMadEmployeeService.select(pcxMadBaseQO);//查询人员信息
        if (CollectionUtils.isEmpty(madEmployeeDTOS)){
            return dataScopeJson;
        }
        // 正常租户下只会有一个员工编码数据信息。如果租户存在多单位或多部门都有同一个员工，则该员工所属部门中任意一个是财务部门或局领导类别，则该员工即为全部数据权限
        Boolean isAllScope = false;// 是否全部数据权限
        for (MadEmployeeDTO madEmployee : madEmployeeDTOS) {
            MadDepartment madDepartment = new MadDepartment();
            madDepartment.setAgyCode(madEmployee.getAgyCode());
            madDepartment.setMofDivCode(madEmployee.getMofDivCode());
            madDepartment.setFiscal(Integer.parseInt(dataScopeQO.getFiscal()));
            madDepartment.setMadCode(madEmployee.getDepartmentCode());
            List<MadDepartment> madDepartmentList = departmentService.select(madDepartment);//查询归属部门信息
            if (CollectionUtils.isEmpty(madDepartmentList)){
                continue;
            }
            if ("01".equals(madDepartmentList.get(0).getDeptType()) || "03".equals(madDepartmentList.get(0).getDeptType())) {// 财务部或局领导
                isAllScope = true;// 全部数据权限
                break;
            }
        }
        if (isAllScope) {
            dataScopeJson.put("code", "3");// 全部数据权限
            dataScopeJson.put("scope", String.join(",", agyCodes));// 限制授权机构
            return dataScopeJson;
        }
        PtyDeptUser ptyDeptUser = new PtyDeptUser();
        ptyDeptUser.setFiscal(Integer.parseInt(dataScopeQO.getFiscal()));
//        ptyDeptUser.setAgyCode(dataScopeQO.getAgyCode());// 授权跨单位，此处不能限制单位编码
        ptyDeptUser.setMofDivCode(dataScopeQO.getMofDivCode());
        ptyDeptUser.setUserCode(dataScopeQO.getUserCode());
        List<Map> authDepts = departmentService.selectDeptList(ptyDeptUser);// 查询授权部门列表
        if (CollectionUtils.isNotEmpty(authDepts)) {
            List<String> deptCodes = authDepts.stream().map(x -> x.get("code").toString()).collect(Collectors.toList());
            dataScopeJson.put("code", "2");// 授权部门数据权限
            dataScopeJson.put("scope", String.join(",", deptCodes));
            dataScopeJson.put("scopeList", authDepts);
        }
        //---------------------保存数据权限dataScope缓存map------------------------
        Map<String, Object> newMap = new HashMap<>();
        newMap.put("dataScope", dataScopeJson);
        newMap.put("dataScopeTree", null);// null设置
        cacheManager.getCache(DATA_SCOPE_NAME).put(dataScopeCacheKey, newMap);// 设置缓存
        return dataScopeJson;
    }

}
