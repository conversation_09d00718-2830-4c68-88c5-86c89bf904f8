package com.pty.pcx.service.impl.ecs;

import com.alibaba.fastjson.JSON;
import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.ecs.ExpenseStrategy;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.ecs.AddInvoicesQO;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import com.pty.pcx.qo.ecs.StartExpenseQO;
import com.pty.pcx.qo.ecs.common.DelEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateNoEcsCommonQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExpenseStrategyContext {
    @Resource
    private IPcxBasExpTypeService pcxBasExpTypeService;
    @Resource
    private BillMainService billMainService;

    private final Map<String, ExpenseStrategy> strategyMap = new HashMap<>();

    @Autowired
    public ExpenseStrategyContext(
            @Qualifier("trainingExpenseStrategy") ExpenseStrategy trainingStrategy,
            @Qualifier("generalExpenseStrategy") ExpenseStrategy generalStrategy,
            @Qualifier("inlandfeeExpenseStrategy") ExpenseStrategy inlandfeeStrategy,
            @Qualifier("meetingExpenseStrategy") ExpenseStrategy meetingStrategy
    ) {
        strategyMap.put("30216", trainingStrategy);
        strategyMap.put("30215", meetingStrategy);
        strategyMap.put("30217", inlandfeeStrategy);
        strategyMap.put("GENERAL_EXPENSE", generalStrategy);
    }

    public ExpenseStrategy getStrategy(String expenseCode) {
        return strategyMap.getOrDefault(expenseCode, strategyMap.get("GENERAL_EXPENSE"));
    }

    public String getExpenseCodeFromItemCode(ExpInvoiceQO expenseQO) {
        PcxBasExpTypeQO param = new PcxBasExpTypeQO();
        param.setFiscal(expenseQO.getFiscal());
        param.setMofDivCode(expenseQO.getMofDivCode());
        param.setAgyCode(expenseQO.getAgyCode());
        String itemCode = extractItemCode(expenseQO);
        if (StringUtil.isEmpty(itemCode)) {
            throw new CommonException("事项编码不能为空");
        }
        param.setItemCode(itemCode);
        List<PcxBasExpType> pcxBasExpTypeList = pcxBasExpTypeService.selectByItemCode(param);
        if (CollectionUtils.isEmpty(pcxBasExpTypeList)) {
            // 通用没有关联费用
            return "GENERAL_EXPENSE";
        }
        return pcxBasExpTypeList.get(0).getExpenseCode();
    }

    public String getExpenseCodeNoEcs(UpdateNoEcsCommonQO updateNoEcsCommonQO) {
        PcxBasExpTypeQO param = new PcxBasExpTypeQO();
        param.setFiscal(updateNoEcsCommonQO.getFiscal());
        param.setMofDivCode(updateNoEcsCommonQO.getMofDivCode());
        param.setAgyCode(updateNoEcsCommonQO.getAgyCode());
        String itemCode = updateNoEcsCommonQO.getItemCode();
        if (StringUtil.isEmpty(itemCode)) {
            throw new CommonException("事项编码不能为空");
        }
        param.setItemCode(itemCode);
        List<PcxBasExpType> pcxBasExpTypeList = pcxBasExpTypeService.selectByItemCode(param);
        if (CollectionUtils.isEmpty(pcxBasExpTypeList)) {
            // 通用没有关联费用
            return "GENERAL_EXPENSE";
        }
        return pcxBasExpTypeList.get(0).getExpenseCode();
    }

    private String extractItemCode(ExpInvoiceQO expenseQO) {
        if (expenseQO instanceof StartExpenseQO) {
            return ((StartExpenseQO) expenseQO).getItemCode();
        } else if (expenseQO instanceof AddInvoicesQO) {
            return ((AddInvoicesQO) expenseQO).getItemCode();
        } else if (expenseQO instanceof UpdateEcsCommonQO) {
            return ((UpdateEcsCommonQO) expenseQO).getItemCode();
        }
        return null;
    }

    public String getExpenseCodeByBillId(DelEcsCommonQO delEcsCommonQO) {
        PcxBill view = billMainService.view(delEcsCommonQO.getBillId());
        log.info("根据billId获取的主单据信息：{}", JSON.toJSON(view));
        String expenseCodesStr = view.getExpenseCodes();
        if (StringUtil.isEmpty(expenseCodesStr)) {
            throw new CommonException("单据 [" + delEcsCommonQO.getBillId() + "] 未配置费用类型");
        }

        List<String> codes = Arrays.stream(expenseCodesStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 判断是否全是 *
        boolean allStar = codes.stream().allMatch("*"::equals);
        if (CollectionUtils.isEmpty(codes) || allStar) {
            return "GENERAL_EXPENSE";
        }

        // 提取前5位并去重
        List<String> codeList = codes.stream()
                .filter(code -> !"*".equals(code))
                .map(code -> code.length() >= 5 ? code.substring(0, 5) : code)
                .distinct()
                .collect(Collectors.toList());

        if (codeList.size() > 1) {
            throw new CommonException("单据 [" + delEcsCommonQO.getBillId() + "] 包含多个费用类型：" + String.join(",", codeList));
        }
        return codeList.get(0);
    }
}
