package com.pty.pcx.service.impl.ecs;

import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.ecs.EcsItemService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasItemDao;
import com.pty.pcx.dao.bas.PcxBasItemExpDao;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import com.pty.pcx.qo.ecs.QueryAllowChangeItemListQO;
import com.pty.pcx.service.impl.ecs.dto.EcsListExpTypeCombine;
import com.pty.pcx.vo.bas.BasItemVO;
import com.pty.pub.common.util.StringUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

//选票查询票能关联的事项
@Indexed
@Service
public class EcsItemServiceImpl extends EcsInvoiceService implements EcsItemService {

    @Resource
    private IPcxBasItemExpService pcxBasItemExpService;
    @Resource
    private IPcxBasItemService pcxBasItemService;
    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Resource
    private PcxBasItemExpDao pcxBasItemExpDao;
    @Resource
    private PcxBasItemDao pcxBasItemDao;

    private static final BasItemVO BAS_ITEM_VO = new BasItemVO("", PcxConstant.UNIVERSAL_ITEM_CODE,PcxConstant.UNIVERSAL_ITEM_NAME, ItemBizTypeEnum.COMMON.getCode(), ItemBizTypeEnum.COMMON.getName(), 999);
    @Override
    public CheckMsg getItemList(ExpInvoiceQO invoiceQO) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setFiscal(invoiceQO.getFiscal());
        pcxBaseDTO.setMofDivCode(invoiceQO.getMofDivCode());
        pcxBaseDTO.setAgyCode(invoiceQO.getAgyCode());

        MadEmployeeDTO employeeDTO = madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, invoiceQO.getUserCode());
        if (Objects.isNull(employeeDTO)){
            return CheckMsg.fail("用户不存在");
        }
        if (StringUtil.isEmpty(employeeDTO.getDepartmentCode())){
            return CheckMsg.fail("用户没有部门信息");
        }
        BasItemVO commonItem = this.getCommonItem(invoiceQO);
        List<BasItemVO> result = new ArrayList<>();
        // 按当前用户查询允许的事项
        PcxBasItemQO itemQO = new PcxBasItemQO();
        BeanUtils.copyProperties(invoiceQO, itemQO);
        List<BasItemVO> ownItem =  pcxBasItemService.getOwnItem(itemQO);
        Set<String> ownTypeSet = ownItem.stream().map(BasItemVO::getItemCode).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(invoiceQO.getBillList())){
            // 预处理查询
            EcsListExpTypeCombine combine = this.preprocessReal(invoiceQO, Lists.newArrayList());
            //获取票关联的费用类型
            Set<String> typeCodeList = combine.getExpTypes().stream()
                    .map(item->{if (item.getIsRefine()==1){
                        return item.getLastCode();
                    } else {
                        return item.getExpenseCode();
                    }
                    }).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(typeCodeList)){
                result.add(commonItem);
                return CheckMsg.success(result);
            }
            // 按发票的费用类型查询对应的事项
            PcxBasItemExpQO itemExpQO = new PcxBasItemExpQO();
            BeanUtils.copyProperties(invoiceQO, itemExpQO);
            itemExpQO.setExpenseCodes(new ArrayList<>(typeCodeList));
            List<PcxBasItemExp> pcxBasItemExps = pcxBasItemExpService.selectByQO(itemExpQO);
            Set<String> ecsWithItemCode = pcxBasItemExps.stream().map(PcxBasItemExp::getItemCode).collect(Collectors.toSet());
            ownTypeSet.retainAll(ecsWithItemCode);
            List<String> matchExpenseTypeCodes = pcxBasItemExps.stream().filter(item -> ownTypeSet.contains(item.getItemCode())).map(PcxBasItemExp::getExpenseCode).distinct().collect(Collectors.toList());
            result.addAll(ownItem.stream().filter(item->ownTypeSet.contains(item.getItemCode())).collect(Collectors.toList()));
            //如果票的费用类型没匹配到有权限的事项，或者票的费用类型有多个，则添加通用事项
            if (CollectionUtils.isEmpty(result) || matchExpenseTypeCodes.size()>1){
                result.add(commonItem);
            }
        }else{
            if (!ownTypeSet.contains(PcxConstant.UNIVERSAL_ITEM_CODE)){
                result.add(commonItem);
            }
            result.addAll(ownItem);
        }
        Set<String> parentCode = new HashSet<>();
        Set<String> itemCodes = new HashSet<>();
        for (BasItemVO vo : result) {
            if (StringUtil.isNotEmpty(vo.getParentCode())){
                parentCode.add(vo.getParentCode());
            }
            itemCodes.add(vo.getItemCode());
        }
        parentCode.removeAll(itemCodes);
        if (!CollectionUtils.isEmpty(parentCode)){
            //查询出父节点放入result中
            itemQO.setItemCodes(result.stream().map(BasItemVO::getParentCode).collect(Collectors.toList()));
            CheckMsg<List<PcxBasItem>> all = pcxBasItemService.getAll(itemQO);
            result.addAll(transItemVo(all.getData()));
        }
        result = result.stream().peek(item->
        {
            if (Objects.isNull(item.getSeq()) ) {
                item.setSeq(1);
            }
        }).sorted(Comparator.comparingInt(BasItemVO::getSeq)).collect(Collectors.toList());
        return CheckMsg.success(result);
    }

    private BasItemVO getCommonItem(ExpInvoiceQO invoiceQO){
        PcxBasItemExpQO pcxBasItemExpQO = new PcxBasItemExpQO();
        pcxBasItemExpQO.setItemCode(PcxConstant.UNIVERSAL_ITEM_CODE);
        pcxBasItemExpQO.setMofDivCode(invoiceQO.getMofDivCode());
        pcxBasItemExpQO.setAgyCode(invoiceQO.getAgyCode());
        pcxBasItemExpQO.setFiscal(invoiceQO.getFiscal());
        List<PcxBasItemExp> pcxBasItemExps = pcxBasItemExpDao.selectByQO(pcxBasItemExpQO);
        if (CollectionUtils.isEmpty(pcxBasItemExps)){
            return BAS_ITEM_VO;
        }else{
            return new BasItemVO("", pcxBasItemExps.get(0).getItemCode(), pcxBasItemExps.get(0).getItemName(), ItemBizTypeEnum.COMMON.getCode(), ItemBizTypeEnum.COMMON.getName(), pcxBasItemExps.get(0).getSeq());
        }
    }


    private List<BasItemVO> transItemVo(List<PcxBasItem> itemList){
        List<BasItemVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemList)){
            return result;
        }
        return itemList.stream().map(item->{
            BasItemVO itemVO = new BasItemVO();
            BeanUtils.copyProperties(item, itemVO);
            return itemVO;
        }).collect(Collectors.toList());
    }

    @Resource
    private PcxBillDao pcxBillDao;
    @Override
    public CheckMsg<?> allowChangeItemList(QueryAllowChangeItemListQO qo) {
        PcxBill pcxBill = pcxBillDao.selectById(qo.getBillId());
        List<String> haveBizTypeExpenseCodes = PcxConstant.getHaveBizTypeExpenseCodes();
        if (!Objects.equals(PcxConstant.UNIVERSAL_ITEM_CODE, pcxBill.getItemCode())){
            PcxBasItemExpQO basItemExpQO = new PcxBasItemExpQO();
            basItemExpQO.setFiscal(qo.getFiscal());
            basItemExpQO.setAgyCode(qo.getAgyCode());
            basItemExpQO.setMofDivCode(qo.getMofDivCode());
            basItemExpQO.setItemCode(pcxBill.getItemCode());
            List<PcxBasItemExp> pcxBasItemExps = pcxBasItemExpDao.selectByQO(basItemExpQO);
            List<String> expenseCodes = pcxBasItemExps.stream().map(PcxBasItemExp::getExpenseCode).distinct().collect(Collectors.toList());
            expenseCodes.retainAll(haveBizTypeExpenseCodes);
            if (!CollectionUtils.isEmpty(expenseCodes)){
                return CheckMsg.fail("目前只支持通用报销业务的事项切换");
            }
        }
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setFiscal(qo.getFiscal());
        pcxBaseDTO.setMofDivCode(qo.getMofDivCode());
        pcxBaseDTO.setAgyCode(qo.getAgyCode());
        MadEmployeeDTO employeeDTO = madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, qo.getUserCode());
        if (Objects.isNull(employeeDTO)){
            return CheckMsg.fail("用户不存在");
        }
        if (StringUtil.isEmpty(employeeDTO.getDepartmentCode())){
            return CheckMsg.fail("用户没有部门信息");
        }
        List<BasItemVO> result = new ArrayList<>();
        // 按当前用户查询允许的事项
        PcxBasItemQO itemQO = new PcxBasItemQO();
        BeanUtils.copyProperties(qo, itemQO);
        List<BasItemVO> ownItem =  pcxBasItemService.getOwnItem(itemQO);
        Set<String> ownTypeSet = ownItem.stream().map(BasItemVO::getItemCode).collect(Collectors.toSet());

        PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
        pcxBasItemQO.setFiscal(qo.getFiscal());
        pcxBasItemQO.setMofDivCode(qo.getMofDivCode());
        pcxBasItemQO.setAgyCode(qo.getAgyCode());
        pcxBasItemQO.setExpenseCodes(haveBizTypeExpenseCodes);
        List<PcxBasItem> items = pcxBasItemDao.selectCommonItem(pcxBasItemQO);
        items = items.stream().filter(item->ownTypeSet.contains(item.getItemCode())).collect(Collectors.toList());
        Set<String> parentCode = new HashSet<>();
        Set<String> itemCodes = new HashSet<>();
        for (PcxBasItem vo : items) {
            if (StringUtil.isNotEmpty(vo.getParentCode())){
                parentCode.add(vo.getParentCode());
            }
            itemCodes.add(vo.getItemCode());
        }
        parentCode.removeAll(itemCodes);
        if (!CollectionUtils.isEmpty(parentCode)){
            //查询出父节点放入result中
            itemQO.setItemCodes(new ArrayList<>(parentCode));
            itemQO.setItemCode(null);
            CheckMsg<List<PcxBasItem>> all = pcxBasItemService.getAll(itemQO);
            result.addAll(transItemVo(all.getData()));
        }
        result.add(BAS_ITEM_VO);
        for (PcxBasItem item : items) {
            BasItemVO itemVO = new BasItemVO();
            BeanUtils.copyProperties(item, itemVO);
            itemVO.setBizType(ItemBizTypeEnum.COMMON.getCode());
            itemVO.setBizTypeName(ItemBizTypeEnum.COMMON.getName());
            result.add(itemVO);
        }
        result = result.stream()
                .filter(item->!Objects.equals(item.getItemCode(), pcxBill.getItemCode()))
                .peek(item->{
                    if (Objects.isNull(item.getSeq())){
                        item.setSeq(1);
                    }
                })
                .sorted(Comparator.comparingInt(BasItemVO::getSeq)).collect(Collectors.toList());
        return CheckMsg.success(result);
    }
}
