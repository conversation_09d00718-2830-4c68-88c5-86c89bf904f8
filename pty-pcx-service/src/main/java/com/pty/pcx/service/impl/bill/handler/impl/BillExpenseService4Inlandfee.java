package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.StrUtil;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillExpInlandfeeService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.PcxDateUtil;
import com.pty.pcx.dao.bill.PcxBillExpInlandfeeDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpInlandfee;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.vo.bill.PcxBillListVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxConstant.StyleKeyword.BOLD;
import static com.pty.pcx.common.constant.PcxConstant.StyleKeyword.FONT_WEIGHT;
import static com.pty.pcx.common.constant.PcxConstant.UNIVERSAL_ITEM_CODE;


/**
 * 出国费用处理接口接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service("billExpenseService4Inlandfee")
@Slf4j
@Indexed
public class BillExpenseService4Inlandfee  extends BillExpenseCommonService implements BillExpenseService<PcxBillExpInlandfee> {
    @Autowired
    private PcxBillExpInlandfeeDao pcxBillExpInlandfeeDao;

    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Autowired
    private PcxBillExpInlandfeeService pcxBillExpInlandfeeService;

    /**
     * 招待费用校验
     * @param expBase 校验的块信息
     * @param billFuncCode
     * @return
     */
    @Override
    public CheckMsg<Void> validate(PcxBillExpInlandfee expBase, String billFuncCode) {
        if (Objects.isNull(expBase)) {
            return CheckMsg.fail("招待费用信息为空");
        }
        FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum = FormSettingEnums.BillFuncCodeEnum.getByCode(billFuncCode);
        if (Objects.isNull(billFuncCodeEnum)) {
            return CheckMsg.fail("暂不支持单据类型为：[" + billFuncCode + "]的业务操作");
        }
        //查询招待费启用的必填的专属字段
        PcxBasFormSettingQueryQO qo = new PcxBasFormSettingQueryQO(
                FormSettingEnums.FormClassifyEnum.EXPENSE.getCode(),
                billFuncCodeEnum.getBit(),
                expBase.getExpenseCode(),
                FormSettingEnums.FormTypeEnum.EXPENSE_EXCLUSIVE.getCode(),
                expBase.getAgyCode(),
                expBase.getFiscal(),
                expBase.getMofDivCode());
        Response<List<PcxBasFormSettingQO>> response = pcxBasFormSettingService.selectAllFormSetting(qo);
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            //启用且必填
            List<PcxBasFormSettingQO> formSettingQOS = response.getData().stream().filter(item -> item.getIsEnabled() == PubConstant.LOGIC_TRUE
                    & item.getIsNull() == PubConstant.LOGIC_TRUE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(formSettingQOS)) {
                Map<String, Object> stringObjectMap = BeanUtil.beanToMap(expBase);
                List<String> missingFields = new ArrayList<>();

                for (PcxBasFormSettingQO formSettingQO : formSettingQOS) {
                    //todo 费用承担部门不做校验
                    if (formSettingQO.getFieldValue().equals("departmentCode")){
                        continue;
                    }
                    Object o = stringObjectMap.get(formSettingQO.getFieldValue());
                    if (Objects.isNull(o) || StringUtil.isEmpty(o.toString())) {
                        String fieldName = formSettingQO.getFieldName();
                        missingFields.add(fieldName);
                        log.warn("专属属性:[{}]不能为空", fieldName);
                    }
                }

                if (!missingFields.isEmpty()) {
                    String errorMessage = "专属属性:" + String.join("、", missingFields) + "不能为空";
                    return CheckMsg.fail(errorMessage);
                }
            }
        }
        return CheckMsg.success();
    }

    /**
     * 保存或更新费用信息
     * @param pcxBillExpInlandfee 费用信息
     * @param pcxBill 单据
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcxBillExpInlandfee saveOrUpdate(PcxBillExpInlandfee pcxBillExpInlandfee, PcxBill pcxBill) {

        // 查询已有记录
        PcxBillExpInlandfee existingRecord = pcxBillExpInlandfeeDao.selectByUnionKey(
                pcxBill.getId(),
                pcxBillExpInlandfee.getExpenseCode(),
                pcxBill.getAgyCode(),
                pcxBill.getFiscal(),
                pcxBill.getMofDivCode()
        );
        //公共字段赋值
        prepareCommonFields(pcxBillExpInlandfee, pcxBill, existingRecord);

        // 插入或更新逻辑
        if (Objects.isNull(existingRecord)) {
            //招待费用
            pcxBillExpInlandfeeDao.insert(pcxBillExpInlandfee);
        } else {
            pcxBillExpInlandfeeDao.updateById(pcxBillExpInlandfee);
        }
        // 补充公共信息
        saveCommonData(pcxBillExpInlandfee, pcxBill);
        return pcxBillExpInlandfee;
    }

    /**
     * 公共字段赋值
     * @param pcxBillExpInlandfee
     * @param pcxBill
     * @param existingRecord
     */
    private void prepareCommonFields(PcxBillExpInlandfee pcxBillExpInlandfee, PcxBill pcxBill, PcxBillExpInlandfee existingRecord) {
        pcxBillExpInlandfee.setBillId(pcxBill.getId());
        pcxBillExpInlandfee.setAgyCode(pcxBill.getAgyCode());
        pcxBillExpInlandfee.setFiscal(pcxBill.getFiscal());
        pcxBillExpInlandfee.setMofDivCode(pcxBill.getMofDivCode());
        pcxBillExpInlandfee.setTenantId(StringUtil.isEmpty(pcxBill.getTenantId()) ? PtyContext.getTenantId() : pcxBill.getTenantId());
        pcxBillExpInlandfee.setDuration(PcxDateUtil.calculateDays(pcxBillExpInlandfee.getStartTime(),  pcxBillExpInlandfee.getEndTime()));
        String peerStaffCode = pcxBillExpInlandfee.getPeerStaffCode();
        pcxBillExpInlandfee.setPeerStaffNum(StringUtil.isNotEmpty(peerStaffCode) ? peerStaffCode.split(",").length : 0);

        // 如果是新增记录生成ID; 如果是更新记录使用已有ID
        if (Objects.isNull(existingRecord)) {
            //新增记录
            pcxBillExpInlandfee.setId(IDGenerator.id());
            pcxBillExpInlandfee.setCreator(pcxBill.getCreator());
            pcxBillExpInlandfee.setCreatorName(pcxBill.getCreatorName());
            pcxBillExpInlandfee.setCreatedTime(pcxBill.getCreatedTime());
        } else {
            //修改记录
            pcxBillExpInlandfee.setId(existingRecord.getId());
            pcxBillExpInlandfee.setModifier(pcxBill.getModifier());
            pcxBillExpInlandfee.setModifierName(pcxBill.getModifierName());
            pcxBillExpInlandfee.setModifiedTime(pcxBill.getModifiedTime());
        }
    }

    /**
     * 查询招待费用信息
     * @param expenseCode
     * @param pcxBill
     * @return
     */
    @Override
    public PcxBillExpInlandfee view(String expenseCode, PcxBill pcxBill) {
        return pcxBillExpInlandfeeDao.selectByUnionKey(pcxBill.getId(), expenseCode, pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
    }

    /**
     * 删除费用信息
     * @param expenseCode
     * @param pcxBill
     */
    @Override
    public void delete(String expenseCode, PcxBill pcxBill) {
        PcxBillExpInlandfee pcxBillExpInlandfee = pcxBillExpInlandfeeDao.selectByUnionKey(pcxBill.getId(), expenseCode, pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        if (Objects.nonNull(pcxBillExpInlandfee)){
            //删除费用信息
            pcxBillExpInlandfeeDao.deleteById(pcxBillExpInlandfee.getId());
        }
    }

    @Override
    public void dealContent(List<PcxBillListVO> bills) {
        if(CollectionUtil.isEmpty(bills)) return;

        List<String> billIdList = bills.stream().map(PcxBillListVO::getBillId).collect(Collectors.toList());
        Map<String[], List<PcxBillListVO>> afm$bills = bills.stream().collect(Collectors.groupingBy(bill -> new String[]{bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode()}));
        afm$bills.forEach((afm, afmBills) -> {
            String _agyCode = afm[0];
            String _fiscal = afm[1];
            String _mofDivCode = afm[2];
            // 查询费用
            List<PcxBillExpInlandfee> inlandfees = pcxBillExpInlandfeeService.selectList(billIdList, _agyCode, _fiscal, _mofDivCode);

            // 将招待费用信息及其详情按单据ID分组，便于后续处理
            Map<String, List<PcxBillExpInlandfee>> inlandfeeMap = inlandfees.stream().collect(Collectors.groupingBy(PcxBillExpInlandfee::getBillId));

            // 遍历每个单据，准备其对应的差旅信息及其详情
            bills.forEach(bill -> {
                List<PcxBillExpInlandfee> inlandfeeList = inlandfeeMap.get(bill.getBillId());
                // 通用报销不返回content
                if (!bill.getItemCode().equals(UNIVERSAL_ITEM_CODE))
                    // 准备数据
                    prepareContent(bill,inlandfeeList);
            });
        });
    }

    /**
     * 准备接待费首页回显数据
     * @param bill
     * @param inlandfeeList
     */
    private void prepareContent(PcxBillListVO bill, List<PcxBillExpInlandfee> inlandfeeList) {
        if (CollectionUtil.isEmpty(inlandfeeList)) return ;

        Optional<PcxBillExpInlandfee> first = inlandfeeList.stream().filter(item -> item.getExpenseCode().equals(PcxBillProcessConstant.ExpenseProcessBeanEnum.INLANDFEE.getCode())).findFirst();
        if (first.isPresent()) {
            PcxBillExpInlandfee inlandfee = first.get();
            PcxBillListVO.BillContentVO content = new PcxBillListVO.BillContentVO();
            content.setStartDate(inlandfee.getStartTime());
            content.setEndDate(inlandfee.getEndTime());
            content.setDays(
                    StrUtil.isBlank(inlandfee.getStartTime()) || StrUtil.isBlank(inlandfee.getEndTime()) ? 0 :
                            (int) cn.hutool.core.date.DateUtil.between(cn.hutool.core.date.DateUtil.parseDate(inlandfee.getStartTime()), cn.hutool.core.date.DateUtil.parseDate(inlandfee.getEndTime()), DateUnit.DAY) + 1
            );

            List<Map<String, Object>> eventInfos = new ArrayList<>();

            Map<String, Object> styleMap01 = new HashMap<>();
            styleMap01.put(FONT_WEIGHT,  BOLD);

            Map<String, Object> map01 = new HashMap<>();
            map01.put("style",  null);
            map01.put("label",  "来访人员");
            eventInfos.add(map01);

            if (inlandfee.getVisitPeopleNum() != null){
                Map<String, Object> map02 = new HashMap<>();
                map02.put("style",  styleMap01);
                map02.put("label",  inlandfee.getVisitPeopleNum() + "人");
                eventInfos.add(map02);
            }

            if (StringUtil.isNotEmpty(inlandfee.getInlandfeeLevelName())){
                Map<String, Object> map03 = new HashMap<>();
                map03.put("style",  null);
                map03.put("label", inlandfee.getInlandfeeLevelName());
                eventInfos.add(map03);
            }

            content.setEventInfos(eventInfos);

            content.setInputAmt(inlandfee.getInputAmt());
            bill.setContent(content);
        }
    }
}
