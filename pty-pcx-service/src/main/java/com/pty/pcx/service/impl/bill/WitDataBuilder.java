package com.pty.pcx.service.impl.bill;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.pty.mad.common.DaysType;
import com.pty.mad.entity.MadWorkLocations;
import com.pty.pcx.api.bill.PcxBillExpDetailTravelService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.api.costcontrollevel.PcxCostControlLevelService;
import com.pty.pcx.common.enu.BillCompletedStatusEnum;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.dto.pa.PaOrgDTO;
import com.pty.pcx.dto.pa.PaUserInvoiceDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.mad.IHolidaysExternalService;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.mad.IMadWorklocationsExternalService;
import com.pty.pcx.pa.IPcxOrgService;
import com.pty.pcx.pa.IPcxUserService;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.costcontrollevel.PcxEmployeeWithCostLevelQO;
import com.pty.pcx.service.impl.bud.PcxBudBalanceService;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.costcontrollevel.PcxEmployeeWithCostLevelVO;
import com.pty.pcx.vo.ecs.EcsBillTripVO;
import com.pty.pcx.vo.ecs.EcsExpenseVO;
import com.pty.pcx.vo.ecs.EcsRelVO;
import com.pty.pcx.vo.ecs.WitEcsVO;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WitDataBuilder {
    @Autowired
    private PcxBillDao pcxBillDao;
    @Autowired
    private IPcxOrgService pcxOrgService;
    @Autowired
    private IPcxUserService pcxUserService;
    @Autowired
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Autowired
    private IMadWorklocationsExternalService madWorklocationsExternalService;
    @Autowired
    private IHolidaysExternalService madHolidaysExternalService;
    @Autowired
    private PcxCostControlLevelService pcxCostControlLevelService;
    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;
    @Autowired
    private IEcsBillExternalService ecsBillExternalService;
    @Autowired
    private PcxBillExpDetailTravelService pcxBillExpDetailTravelService;
    @Autowired
    private PcxBudBalanceService pcxBudBalanceService;

    /**
     * 前款不清 后款不借
     * @param basicInfo
     * @return
     */
    public PcxBill buildBeforeLoan(PcxBill basicInfo) {
        if (basicInfo == null || basicInfo.getBillFunc() == null) {
            log.warn("BasicInfo or BillFunc is null, cannot build before loan.");
            return null;
        }
        if (basicInfo.getBillFunc().equals(BillFuncCodeEnum.LOAN.getCode())) {
            return pcxBillDao.selectOne(
                    Wrappers.<PcxBill>lambdaQuery()
                            .eq(PcxBill::getBillFuncCode, BillFuncCodeEnum.LOAN.getCode())
                            .eq(PcxBill::getItemCode, basicInfo.getItemCode())
                            .ne(PcxBill::getCompletedStatus, BillCompletedStatusEnum.COMPLETED.getCode())
                            .orderByDesc(PcxBill::getId)
                            .last("LIMIT 1"));
        }
        return null;
    }

    public List<MadDepartmentDTO> buildLeaderDepartment(PcxBill basicInfo) {
        PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
        pcxBalancesQO.setFiscal(basicInfo.getFiscal());
        pcxBalancesQO.setAgyCode(basicInfo.getAgyCode());
        pcxBalancesQO.setMofDivCode(basicInfo.getMofDivCode());
        pcxBalancesQO.setEmployeeCode(basicInfo.getClaimantCode());
        return pcxBudBalanceService.getLeaderDepartment(pcxBalancesQO);
    }

    public BigDecimal buildYearSub(String empCode){
        BigDecimal bigDecimal = pcxBillExpDetailTravelService.sumYearSub(empCode);
        return Objects.nonNull(bigDecimal)? bigDecimal:BigDecimal.ZERO;
    }

    /**
     * 组织机构
     * @param basicInfo
     * @return
     */
    public PaOrgDTO buildPaOrgDTO(PcxBill basicInfo) {
        if (basicInfo == null || basicInfo.getAgyCode() == null || basicInfo.getFiscal() == null || basicInfo.getMofDivCode() == null) {
            log.warn("BasicInfo fields are null, cannot build PaOrgDTO.");
            return null;
        }
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setAgyCode(basicInfo.getAgyCode());
        pcxBaseDTO.setFiscal(basicInfo.getFiscal());
        pcxBaseDTO.setMofDivCode(basicInfo.getMofDivCode());
        PaOrgDTO paOrgDTO = pcxOrgService.selectByOrgCode(pcxBaseDTO);
        if (paOrgDTO != null){
            //重新获取发票信息
            PaUserInvoiceDTO userInvHead = pcxUserService.getUserInvHead(pcxBaseDTO, basicInfo.getClaimantUserCode());
            if (userInvHead != null){
                paOrgDTO.setTitle(userInvHead.getMainOrgName());
                paOrgDTO.setOrgName(userInvHead.getMainOrgName());
                paOrgDTO.setDutyNo(userInvHead.getDutyNo());
                return paOrgDTO;
            }
        }

        return paOrgDTO;
    }

    /**
     * 填报人信息
     * @param basicInfo
     * @return
     */
    public MadEmployeeDTO buildMadEmployeeDTO(PcxBill basicInfo) {
        if (basicInfo == null || basicInfo.getClaimantCode() == null) {
            log.warn("BasicInfo or ClaimantCode is null, cannot build MadEmployeeDTO.");
            return null;
        }
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setAgyCode(basicInfo.getAgyCode());
        pcxBaseDTO.setFiscal(basicInfo.getFiscal());
        pcxBaseDTO.setMofDivCode(basicInfo.getMofDivCode());
        return madEmployeeExternalService.selectEmployeeByEmpCode(pcxBaseDTO, basicInfo.getClaimantCode());
    }

    /**
     * 票信息
     * @param data
     * @return
     */
    public List<WitEcsVO> buildEcsVOList(PcxBillVO data) {
        List<WitEcsVO> ecsVOList = Lists.newArrayList();
        if (data == null || data.getEcsExpMatch() == null) {
            log.warn("Data or EcsExpMatch is null, cannot build ECS VO list.");
            return ecsVOList;
        }
        List<EcsExpenseVO> travelEcsList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(data.getEcsExpMatch().getEcsExpList())){
            travelEcsList.addAll(data.getEcsExpMatch().getEcsExpList());
        }
        if (CollectionUtils.isNotEmpty(data.getEcsExpMatch().getTripList())){
            data.getEcsExpMatch().getTripList().forEach(trip -> {
               if (CollectionUtils.isNotEmpty(trip.getEcsList())){
                   travelEcsList.addAll(trip.getEcsList());
               }
           });
        }
        processEcsData(data.getBasicInfo(),
                data.getExpenseDetail(),
                data.getEcsExpMatch().getEcsRelList(),
                travelEcsList,
                data.getEcsExpMatch().getEcsbills(),
                ecsVOList);
        return ecsVOList;
    }

    private void processEcsData(PcxBill basicInfo, List<PcxBillExpDetailBase> expenseDetail, List<EcsRelVO> ecsRelList,  List<EcsExpenseVO> ecsExpList,List<JSONObject> ecsbills,List<WitEcsVO> ecsVOList) {
        if (CollectionUtils.isNotEmpty(ecsRelList)) {
            //通用报销需要查票信息
            Set<String> list = ecsRelList.stream().map(EcsRelVO::getEcsBillId).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(list)) {

                ecsbills = ecsBillExternalService.getEcsBillList(new ArrayList<>(list), basicInfo.getFiscal(),basicInfo.getAgyCode(), basicInfo.getMofDivCode());
                processEcsRelList(basicInfo,expenseDetail, ecsRelList, ecsbills, ecsVOList);
            }
        }
        if (CollectionUtils.isNotEmpty(ecsExpList)) {
            processEcsExpenseList(basicInfo,expenseDetail, ecsExpList, ecsbills, ecsVOList);
        }
    }

    private void processEcsRelList(PcxBill basicInfo, List<PcxBillExpDetailBase> expenseDetail, List<EcsRelVO> ecsRelList, List<JSONObject> ecsbills, List<WitEcsVO> ecsVOList) {
        Map<String, EcsRelVO> ecsRelVOMap = ecsRelList.stream().filter(item->StringUtil.isNotEmpty(item.getEcsBillId())).collect(Collectors.toMap(EcsRelVO::getEcsBillId, Function.identity()));
        try {
            if (CollectionUtils.isNotEmpty(ecsbills)) {
                ecsbills.forEach(jsonObject -> {
                    String ecsBillId = jsonObject.getString("billId");
                    EcsRelVO ecsRelVO = ecsRelVOMap.get(ecsBillId);
                    if (ecsRelVO != null) {
                        ecsVOList.add(createWitEcsVO(basicInfo,expenseDetail, jsonObject, ecsRelVO));
                    }
                });
            }
            //无票的单据
            List<EcsRelVO> noEcsRel = ecsRelList.stream().filter(item -> StringUtil.isEmpty(item.getEcsBillId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noEcsRel)){
                noEcsRel.forEach(ecsRelVO -> {
                    ecsVOList.add(createWitEcsVO(basicInfo,expenseDetail, null, ecsRelVO));
                });
            }
        } catch (Exception e) {
            log.error("Error processing ECS Rel List. BasicInfo: {}, EcsBills: {}", basicInfo, ecsbills, e);
        }
    }

    private void processEcsExpenseList(PcxBill basicInfo, List<PcxBillExpDetailBase> expenseDetail, List<EcsExpenseVO> ecsExpenseList, List<JSONObject> ecsbills, List<WitEcsVO> ecsVOList) {
        if (CollectionUtils.isEmpty(ecsExpenseList)) return;
        Map<String, EcsExpenseVO> ecsExpenseMap = ecsExpenseList.stream()
                .filter(ecsExpenseVO -> StringUtil.isNotEmpty(ecsExpenseVO.getEcsBillId()))
                .collect(Collectors.toMap(EcsExpenseVO::getEcsBillId, Function.identity(), (key1, key2) -> key1));

        try {
            if (CollectionUtils.isNotEmpty(ecsbills)) {
                ecsbills.forEach(jsonObject -> {
                    String ecsBillId = jsonObject.getString("billId");
                    EcsExpenseVO ecsExpenseVO = ecsExpenseMap.get(ecsBillId);
                    if (ecsExpenseVO != null) {
                        ecsVOList.add(createWitEcsVO(basicInfo, expenseDetail,jsonObject, ecsExpenseVO));
                    }
                });
            }
            //无票的单据
            List<EcsExpenseVO> noEcsExpense = ecsExpenseList.stream().filter(ecsExpenseVO -> StringUtil.isEmpty(ecsExpenseVO.getEcsBillId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noEcsExpense)){
                noEcsExpense.forEach(ecsExpenseVO -> {
                    ecsVOList.add(createWitEcsVO(basicInfo, expenseDetail, null, ecsExpenseVO));
                });
            }
        } catch (Exception e) {
            log.error("Error processing ECS Expense List. BasicInfo: {}, EcsBills: {}", basicInfo, ecsbills, e);
        }
    }

    private WitEcsVO createWitEcsVO(PcxBill basicInfo,List<PcxBillExpDetailBase> expenseDetail,JSONObject jsonObject, Object ecsRelVO) {
        WitEcsVO ecsVO = new WitEcsVO();
        ecsVO.build(jsonObject,expenseDetail);

        if (ecsRelVO instanceof EcsRelVO) {
            populateEcsRelVO((EcsRelVO) ecsRelVO, ecsVO);
        } else if (ecsRelVO instanceof EcsExpenseVO) {
            populateEcsExpenseVO(basicInfo, (EcsExpenseVO) ecsRelVO, ecsVO);
        }


        if(StringUtil.isNotEmpty(ecsVO.getEcsSeller())){
            //统计当前票的开票方的累计开票金额
            DateTime currentDate = DateUtil.date();
            BigDecimal monthEcsAmt = pcxExpDetailEcsRelService.countEcsAmtBySeller(DateUtil.beginOfMonth(currentDate),currentDate,ecsVO.getEcsSeller());
            if (monthEcsAmt == null) {
                monthEcsAmt = BigDecimal.ZERO;
            }
            BigDecimal dayEcsAmt = pcxExpDetailEcsRelService.countEcsAmtBySeller(DateUtil.beginOfDay(currentDate),currentDate,ecsVO.getEcsSeller());
            if (dayEcsAmt == null) {
                dayEcsAmt = BigDecimal.ZERO;
            }
            ecsVO.setMonthEcsAmt(monthEcsAmt);
            ecsVO.setDayEcsAmt(dayEcsAmt);
        }

        return ecsVO;
    }


    private void populateEcsRelVO(EcsRelVO relVO, WitEcsVO ecsVO) {
        ecsVO.setEcsRelId(relVO.getEcsRelId());
        ecsVO.setEcsAmt(relVO.getEcsAmt());
        ecsVO.setInputAmt(relVO.getInputAmt());
        ecsVO.setExpenseTypeCodes(relVO.getExpenseTypeCodes());
        ecsVO.setExpenseTypeNames(relVO.getExpenseTypeNames());
    }

    private void populateEcsExpenseVO(PcxBill basicInfo, EcsExpenseVO expenseVO, WitEcsVO ecsVO) {
        ecsVO.setEcsRelId(expenseVO.getDetailId());
        ecsVO.setEcsAmt(expenseVO.getTicketAmt());
        ecsVO.setInputAmt(expenseVO.getInputAmt());
        ecsVO.setStandReason(expenseVO.getStandOverReason());
        ecsVO.setStandValue(expenseVO.getStandValue());
        ecsVO.setStandValueName(expenseVO.getStandValueName());
        ecsVO.setExpenseTypeCodes(expenseVO.getExpenseTypeCode());
        ecsVO.setExpenseTypeNames(expenseVO.getExpenseTypeName());

        String empCode = expenseVO.getEmpCode();

        if (StringUtil.isEmpty(empCode)) {
            empCode = basicInfo.getClaimantCode();
        }

        if (StringUtil.isNotEmpty(empCode)) {
            populateEmployeeDetails(basicInfo, empCode, ecsVO);
        }

//        if (StringUtil.isNotEmpty(expenseVO.getStartTime())) {
//            populateDaysType(expenseVO, ecsVO);
//        }
    }

    private void populateEmployeeDetails(PcxBill basicInfo, String empCode, WitEcsVO ecsVO) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setAgyCode(basicInfo.getAgyCode());
        pcxBaseDTO.setFiscal(basicInfo.getFiscal());
        pcxBaseDTO.setMofDivCode(basicInfo.getMofDivCode());

        MadEmployeeDTO madEmployeeDTO = madEmployeeExternalService.selectEmployeeByEmpCode(pcxBaseDTO, empCode);
        if (madEmployeeDTO != null) {
            ecsVO.setMadEmployeeDTO(madEmployeeDTO);

            PcxEmployeeWithCostLevelQO costLevelQO = new PcxEmployeeWithCostLevelQO();
            costLevelQO.setAgyCode(basicInfo.getAgyCode());
            costLevelQO.setFiscal(Integer.valueOf(basicInfo.getFiscal()));
            costLevelQO.setMofDivCode(basicInfo.getMofDivCode());
            costLevelQO.setMadCodes(Collections.singletonList(empCode));

            List<PcxEmployeeWithCostLevelVO> empsWithCostLevelByCodes = pcxCostControlLevelService.getEmpsWithCostLevelByCodes(costLevelQO);
            if (CollectionUtils.isNotEmpty(empsWithCostLevelByCodes)) {
                ecsVO.setCostLevelVO(empsWithCostLevelByCodes.get(0));
            }
            if (StringUtil.isNotEmpty(madEmployeeDTO.getWorkLocationId())){
                MadWorkLocations madWorkLocations = madWorklocationsExternalService.selectById(madEmployeeDTO.getWorkLocationId());
                if (Objects.nonNull(madWorkLocations)){
                    madWorkLocations.setAgyCode(basicInfo.getAgyCode());
                }
            }
        }
    }

    private void populateDaysType(EcsExpenseVO expenseVO, WitEcsVO ecsVO) {
        try {
            DaysType daysType = madHolidaysExternalService.getDaysType(LocalDate.parse(expenseVO.getStartTime()));
            if (daysType != null) {
                ecsVO.setDaysType(daysType.getType());
            }
        } catch (Exception e) {
            log.warn("Failed to parse start time for expense: {}", expenseVO, e);
        }
    }
}