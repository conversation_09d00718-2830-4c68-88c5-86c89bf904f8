package com.pty.pcx.service.impl.setting;

import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.setting.IPcxBaseDataService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.setting.BaseDataQO;
import com.pty.pcx.vo.PcxBasExpTypeVO;
import com.pty.pcx.vo.PcxBasItemVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Indexed
@Service
public class PcxBaseDataServiceImpl implements IPcxBaseDataService {

    @Autowired
    private IPcxBasItemService pcxBasItemService;

    @Autowired
    private IPcxBasExpTypeService pcxBasExpTypeService;

    @Override
    public CheckMsg getInfo(BaseDataQO qo) {
        HashMap<String, Object> result = new HashMap<>();
        PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
        pcxBasItemQO.setMofDivCode(qo.getMofDivCode());
        pcxBasItemQO.setAgyCode(qo.getAgyCode());
        pcxBasItemQO.setFiscal(qo.getFiscal());
        pcxBasItemQO.setTenantId(qo.getTenantId());
        CheckMsg checkMsg = pcxBasItemService.getAll(pcxBasItemQO);
        if(checkMsg.isSuccess()){
            List<Map<String,String>> pcxBasItem = new ArrayList<>();
            for (PcxBasItemVO data : (List<PcxBasItemVO>) checkMsg.getData()) {
                pcxBasItem.add( new HashMap<String,String>(){{
                    put("code", data.getItemCode());
                    put("name", data.getItemName());
                }});
            }
            result.put("pcxBasItem",pcxBasItem );
        }
        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        pcxBasExpTypeQO.setMofDivCode(qo.getMofDivCode());
        pcxBasExpTypeQO.setAgyCode(qo.getAgyCode());
        pcxBasExpTypeQO.setFiscal(qo.getFiscal());
        pcxBasExpTypeQO.setTenantId(qo.getTenantId());
        CheckMsg expTypeCheckMsg = pcxBasExpTypeService.getAll(pcxBasExpTypeQO);
        if(expTypeCheckMsg.isSuccess()){
            List<Map<String,String>> pcxBasExpType = new ArrayList<>();
            for (PcxBasExpTypeVO data : (List<PcxBasExpTypeVO>) expTypeCheckMsg.getData()) {
                pcxBasExpType.add( new HashMap<String,String>(){{
                    put("code", data.getExpenseCode());
                    put("name", data.getExpenseName());
                }});
            }
            result.put("pcxBasExpType",pcxBasExpType );
        }
        return CheckMsg.success().setData(result);
    }
}
