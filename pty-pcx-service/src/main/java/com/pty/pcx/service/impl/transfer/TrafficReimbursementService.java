package com.pty.pcx.service.impl.transfer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.mad.entity.MadCurrent;
import com.pty.mad.entity.MadEmployee;
import com.pty.mad.entity.MadExtData;
import com.pty.mad.entity.MadWorkLocations;
import com.pty.openapi.integration.Response;
import com.pty.openapi.integration.dto.kingdee.TravelExpenseReimbursementRequest;
import com.pty.openapi.integration.dto.kingdee.TravelExpenseReimbursementResponse;
import com.pty.openapi.integration.dto.reimbursement.ReimbursementDataDTO;
import com.pty.openapi.integration.dto.reimbursement.travel.TravelExpenseDetailDTO;
import com.pty.openapi.integration.dto.reimbursement.travel.TravelExpenseReimbursementDTO;
import com.pty.openapi.integration.enums.InvoiceType;
import com.pty.openapi.integration.enums.TravelExpenseType;
import com.pty.openapi.sdk.client.kingdee.ErpTravelExpenseReimbursementCilent;
import com.pty.openapi.sdk.client.kingdee.TravelExpenseParamsFactoryClient;
import com.pty.pcx.api.transfer.PcxBillSyncService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.PcxBillSyncStatus;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.dao.bill.PcxBillExpCommonDao;
import com.pty.pcx.dao.bill.PcxBillExpDetailCommonDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.dto.transfer.AllocationItemDTO;
import com.pty.pcx.dto.transfer.ExtendTravelExpenseDetailDTO;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.transfer.PcxBillSync;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pty.mad.api.MadWorkLocationsService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TrafficReimbursementService extends AbstractReimbursementService {

    @Autowired
    private PcxBillSyncService pcxBillSyncService;

    @Autowired
    private ErpTravelExpenseReimbursementCilent erpTravelExpenseReimbursementCilent;

    @Autowired
    private MadWorkLocationsService madWorkLocationsService;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;

    @Autowired
    private PcxBillExpCommonDao pcxBillExpCommonDao;

    @Autowired
    private PcxBillExpDetailCommonDao pcxBillExpDetailCommonDao;

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    @Autowired
    private TravelExpenseParamsFactoryClient travelExpenseParamsFactoryClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TransactionTemplate transactionTemplate;

    public void execute() {
        RLock lock = redissonClient.getLock("TRAFFIC_REIMBURSEMENT_LOCK");
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            transactionTemplate.executeWithoutResult(status -> {
                executeDraft();
                executeBill();
            });
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 执行草稿单据
     */
    private void executeDraft() {
        executeTraffic(false);
        executeTravel(false);
    }

    /**
     * 执行普通单据
     */
    private void executeBill() {
        executeTraffic(true);
        executeTravel(true);
    }

    private void executeTraffic(boolean isFinalReview) {
        Integer syncStatus = isFinalReview ? PcxBillSyncStatus.PENDING_SYNC.getCode() : PcxBillSyncStatus.DRAFT_PENDING_SYNC.getCode();
        List<PcxBill> pcxBills = pcxBillSyncService.getUnSyncedPcxBill(PcxBillSyncServiceImpl.TRAFFIC, syncStatus);
        if (pcxBills.isEmpty()) {
            return;
        }
        execute(pcxBills, PcxBillSyncServiceImpl.TRAFFIC, isFinalReview);
    }

    private void executeTravel(boolean isFinalReview) {
        Integer syncStatus = isFinalReview ? PcxBillSyncStatus.PENDING_SYNC.getCode() : PcxBillSyncStatus.DRAFT_PENDING_SYNC.getCode();
        List<PcxBill> pcxBills = pcxBillSyncService.getUnSyncedPcxBill(PcxBillSyncServiceImpl.TRAVEl, syncStatus);
        if (pcxBills.isEmpty()) {
            return;
        }
        execute(pcxBills, PcxBillSyncServiceImpl.TRAVEl, isFinalReview);
    }

    private void execute(List<PcxBill> pcxBills, Integer bizType, boolean isFinalReview) {
        List<String> billIds = new ArrayList<>();
        List<String> claimantCodes = new ArrayList<>();
        for (PcxBill pcxBill : pcxBills) {
            billIds.add(pcxBill.getId());
            claimantCodes.add(pcxBill.getClaimantUserCode());
        }

        Map<String, List<PcxBillExpCommon>> billExpCommonGroupMap = selectBillExpCommonGroupMap(billIds);

        Map<String, List<PcxBillAmtApportionDepartment>> pcxBillAmtApportionDepartmentsMap = getPcxBillAmtApportionDepartmentsMap(billIds);

        Map<String, MadEmployee> madEmployeeMap = getMadEmployeeMap(claimantCodes);

        List<String> workLocationIds =
                madEmployeeMap.values().stream().map(MadEmployee::getWorkLocationId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> workLocationMap = new HashMap<>();
        if (!workLocationIds.isEmpty()) {
            List<MadWorkLocations> madWorkLocations = madWorkLocationsService.selectByIds(workLocationIds);
            for (MadWorkLocations madWorkLocation : madWorkLocations) {
                workLocationMap.put(madWorkLocation.getId(), madWorkLocation.getCity());
            }
        }

        for (PcxBill pcxBill : pcxBills) {
            PcxBillSync pcxBillSync = new PcxBillSync();
            Integer syncStatus = PcxBillSyncStatus.getSyncedStatus(isFinalReview);
            String syncMessage = null;
            try {
                TravelExpenseReimbursementDTO travelExpenseReimbursementDTO
                        = buildTravelExpenseReimbursementDTO(isFinalReview, pcxBill, bizType, madEmployeeMap, workLocationMap, billExpCommonGroupMap, pcxBillAmtApportionDepartmentsMap);

                Response<ReimbursementDataDTO> response = erpTravelExpenseReimbursementCilent.push(travelExpenseReimbursementDTO);
                if (response.isSuccess()) {
                    ReimbursementDataDTO data = response.getData();
                    pcxBillSync.setExternalBillId(data.getBillId());
                    pcxBillSync.setExternalBillNo(data.getBillNo());
                } else {
                    syncStatus = PcxBillSyncStatus.getSynceFailedStatus(isFinalReview);
                    syncMessage = response.getMsg();
                    // 若草稿状态遇到关账，则需手动处理
                    if (isFinalReview && response.getCode() == -2) {
                        syncStatus = PcxBillSyncStatus.IS_CLOSED.getCode();
                    }
                }

            } catch (Exception e) {
                syncStatus = PcxBillSyncStatus.getSynceFailedStatus(isFinalReview);
                syncMessage = e.getMessage();
            }
            pcxBillSync.setBillId(pcxBill.getId());
            pcxBillSync.setSyncStatus(syncStatus);
            pcxBillSync.setSyncTime(new Date());
            pcxBillSync.setSyncMessage(syncMessage);
            pcxBillSyncService.updateSyncStatus(pcxBillSync);
        }
    }

    private TravelExpenseReimbursementDTO buildTravelExpenseReimbursementDTO(boolean isFinalReview,
                                                                             PcxBill pcxBill,
                                                                             Integer bizType,
                                                                             Map<String, MadEmployee> madEmployeeMap,
                                                                             Map<String, String> workLocationMap,
                                                                             Map<String, List<PcxBillExpCommon>> billExpCommonGroupMap,
                                                                             Map<String, List<PcxBillAmtApportionDepartment>> pcxBillAmtApportionDepartmentsMap) {
        String billId = pcxBill.getId();

        TravelExpenseReimbursementDTO travelExpenseReimbursementDTO = new TravelExpenseReimbursementDTO();
        String id = "";
        String billNo = pcxBill.getBillNo();
        Boolean autoSubmit = Boolean.FALSE;
        if (isFinalReview) {
            PcxBillSync pcxBillSync = pcxBillSyncService.getPcxBillSyncByBillId(billId);
            Assert.notNull(pcxBillSync, "无法查询到终审的同步信息，请检查数据");
            id = pcxBillSync.getExternalBillId();
            autoSubmit = Boolean.TRUE;
        }
        travelExpenseReimbursementDTO.setBillId(id);    // 单据ID    FID
        travelExpenseReimbursementDTO.setBillNo(billNo);   // 单据编号 FBillNo
        travelExpenseReimbursementDTO.setAutoSubmit(autoSubmit.toString());   // 是否自动提交和审核    IsAutoSubmitAndAudit

        travelExpenseReimbursementDTO.setBillType("Reimbursement_Travel");   // 单据类型（差旅报销单）  BILL_TYPE
        travelExpenseReimbursementDTO.setApplyDate(pcxBill.getTransDate());  // 申请日期 FDate
        String userCode = pcxBill.getClaimantUserCode();
        travelExpenseReimbursementDTO.setApplyUserCode(userCode);   // 申请人   FPROPOSER

        // 当前部门
        MadExtData currentDepartment = selectCurrentDepartment(pcxBill.getAgyCode(), pcxBill.getDepartmentCode(), pcxBill.getClaimantUserCode());
        Assert.notNull(currentDepartment, "部门:" + pcxBill.getDepartmentCode() + " 不存在，请检查配置");
        String deptCode = currentDepartment.getMadCode();
        travelExpenseReimbursementDTO.setApplyDept(deptCode);    // 申请部门  FREQUESTDEPT

        // 当前组织
        MadExtData currentOrg = selectCurrentOrg(currentDepartment.getField05());
        Assert.notNull(currentOrg, "ERP 组织:" + currentDepartment.getField05() + " 不存在，请检查配置");
        travelExpenseReimbursementDTO.setApplyOrg(currentOrg.getMadCode()); // 申请组织 APPLY_ORG

        String workLocation = "福州";
        if (madEmployeeMap.containsKey(userCode)) {
            MadEmployee madEmployee = madEmployeeMap.get(userCode);
            travelExpenseReimbursementDTO.setApplyPhone(madEmployee.getPhoneNo()); // 联系电话   FContactPhoneNo

            String workLocationId = madEmployee.getWorkLocationId();
            if (workLocationMap.containsKey(workLocationId)) {
                workLocation = workLocationMap.get(workLocationId);
            }
        }
        final String placeName = workLocation;

        travelExpenseReimbursementDTO.setAssistantName(StringUtils.equals(userCode, pcxBill.getCreator()) ? null : pcxBill.getCreatorName());    // 商务助理 F_KF_Assistant
        travelExpenseReimbursementDTO.setCurrency("PRE001");    // 币别（人民币）   FCURRENCYID
        travelExpenseReimbursementDTO.setSettlementType("JSFS11_SYS");   // 结算方式（网银支付）   PAY_SETTLLETYPE

        String currentType = "BD_Empinfo";
        String currentName = userCode;

        String payeeAccName = null;
        String payeeAccCode = null;
        String payeeAccNo = null;
        if (isFinalReview) {
            PcxBillPayDetail pcxBillPayDetail = selectPcxBillPayDetail(billId);
            if (pcxBillPayDetail != null) {
                if (SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode().equals(pcxBillPayDetail.getSettlementType())) {
                    MadCurrent current = selectMadCurrent(pcxBill, pcxBillPayDetail.getPayeeAccountName());
                    if (current != null) {
                        currentType = "BD_Supplier";    // （供应商）
                        currentName = current.getMadCode();
                    }
                }
                payeeAccName = pcxBillPayDetail.getPayeeAccountName();
                payeeAccCode = pcxBillPayDetail.getPayeeBankName();
                payeeAccNo = pcxBillPayDetail.getPayeeAccountNo();
            }
        } else {
            PcxBillSettlement pcxBillSettlement = selectPcxBillSettlement(billId);
            if (pcxBillSettlement != null) {
                if (SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode().equals(pcxBillSettlement.getSettlementType())) {
                    MadCurrent current = selectMadCurrent(pcxBill, pcxBillSettlement.getPayeeAccName());
                    if (current != null) {
                        currentType = "BD_Supplier";    // （供应商）
                        currentName = current.getMadCode();
                    }
                }
                payeeAccName = pcxBillSettlement.getPayeeAccName();
                payeeAccCode = pcxBillSettlement.getPayeeBankName();
                payeeAccNo = pcxBillSettlement.getPayeeAccNo();
            }
        }
        payeeAccNo = StringUtils.replace(payeeAccNo, " ", "");

        travelExpenseReimbursementDTO.setPayeeAccName(payeeAccName);  // 账户名称 FBankAccountNameT
        travelExpenseReimbursementDTO.setPayeeAccCode(payeeAccCode);  // 开户银行 FBankBranchT
        travelExpenseReimbursementDTO.setPayeeAccNo(payeeAccNo);   // 银行账号    FBankAccountT

        travelExpenseReimbursementDTO.setIsRealPay(Boolean.TRUE.toString()); // 实报实付 FRealPay

        travelExpenseReimbursementDTO.setCurrentType(currentType);    // 往来单位类型  FCONTACTUNITTYPE
        travelExpenseReimbursementDTO.setCurrentName(currentName);    // 往来单位    FCONTACTUNIT

        String reason = StringUtils.isBlank(pcxBill.getReason()) ? "无" : pcxBill.getReason();
        travelExpenseReimbursementDTO.setReason(reason);    // 报销事由 FCAUSA

        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelDao.selectByBillId(billId);

        Map<String, PcxExpDetailEcsRel> pcxExpDetailEcsRelMap = new HashMap<>();
        List<String> ecsBillIds = new ArrayList<>();
        for (PcxExpDetailEcsRel pcxExpDetailEcsRel : pcxExpDetailEcsRels) {
            pcxExpDetailEcsRelMap.put(pcxExpDetailEcsRel.getDetailId(), pcxExpDetailEcsRel);
            ecsBillIds.add(pcxExpDetailEcsRel.getEcsBillId());
        }

        Map<String, PcxExpDetailEcsRel> airExpDetailEcsRelMap = pcxExpDetailEcsRels
                .stream()
                .filter(pcxExpDetailEcsRel -> StringUtils.contains(pcxExpDetailEcsRel.getItemName(), "民航发展基金"))
                .collect(Collectors.toMap(PcxExpDetailEcsRel::getEcsBillId, Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, String> ecsBillEinvMap = selectEcsBillEinvMap(ecsBillIds);

        Map<String, PcxBillExpCommon> pcxBillExpCommonIdMap = new HashMap<>();
        Map<String, PcxBillExpCommon> pcxBillExpCommonBillIdMap = new HashMap<>();
        if (billExpCommonGroupMap.containsKey(billId)) {
            List<PcxBillExpCommon> pcxBillExpCommons = billExpCommonGroupMap.get(billId);
            for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
                pcxBillExpCommonIdMap.put(pcxBillExpCommon.getId(), pcxBillExpCommon);
                pcxBillExpCommonBillIdMap.put(pcxBillExpCommon.getBillId(), pcxBillExpCommon);
            }
        }

        List<PcxBillExpDetailCommon> pcxBillExpDetailCommons = pcxBillExpDetailCommonDao.selectByBillId(billId);

        List<ExtendTravelExpenseDetailDTO> extendExpenseDetailDTOS = pcxBillExpDetailCommons
                .stream()
                .filter(pcxBillExpDetailCommon -> !"replenish".equals(pcxBillExpDetailCommon.getSource()))
                .map(pcxBillExpDetailCommon -> {
                    ExtendTravelExpenseDetailDTO expenseDetail = new ExtendTravelExpenseDetailDTO();

                    String expCode = null;
                    if (Objects.equals(bizType, PcxBillSyncServiceImpl.TRAFFIC)) {
                        expCode = "07.01";
                    } else if (Objects.equals(bizType, PcxBillSyncServiceImpl.TRAVEl)) {
                        expCode = "07.02";
                    }
                    expenseDetail.setExpCode(expCode);   // 费用项目    FExpID

                    TravelExpenseReimbursementRequest request = new TravelExpenseReimbursementRequest();
                    Integer invoiceQty = pcxBillExpDetailCommon.getEcsNum();
                    if (PcxConstant.TRAVEL_DETAIL_3021103.equals(pcxBillExpDetailCommon.getExpDetailCode()) ||
                            PcxConstant.TRAVEL_DETAIL_3021104.equals(pcxBillExpDetailCommon.getExpDetailCode())) {
                        invoiceQty = 0;
                    }
                    String isRealName = "0";
                    if (pcxExpDetailEcsRelMap.containsKey(pcxBillExpDetailCommon.getId())) {
                        PcxExpDetailEcsRel pcxExpDetailEcsRel = pcxExpDetailEcsRelMap.get(pcxBillExpDetailCommon.getId());
                        expenseDetail.setAirFund(pcxExpDetailEcsRel.getCadfAmt().toPlainString()); // 民航发展基金    FCivilAviationFund

                        String ecsBillNo = pcxExpDetailEcsRel.getEcsBillNo();
                        String[] ecsBillNos = ecsBillNo.split("#");
                        expenseDetail.setInvoiceCode(ecsBillNos.length == 2 ? ecsBillNos[1] : "0"); // 发票代码 F_KF_RECINVCODE
                        expenseDetail.setInvoiceNo(getInvoiceNo(ecsBillNo));   // 发票号码   F_KF_RECINVNUMBER
                        request.setEcsBillType(pcxExpDetailEcsRel.getEcsBillType());
                        request.setExpenseTypeCode(pcxExpDetailEcsRel.getExpenseTypeCode());
                        String ecsBillId = pcxExpDetailEcsRel.getEcsBillId();
                        request.setInvSubKind(ecsBillEinvMap.get(ecsBillId));

                        isRealName = String.valueOf(pcxExpDetailEcsRel.getIsRealName());

                        if (StringUtils.isBlank(pcxExpDetailEcsRel.getFileId())) {
                            invoiceQty = 0;
                        }

                        // 补充原始信息
                        expenseDetail.setEcsBillId(pcxExpDetailEcsRel.getEcsBillId());
                    } else {
                        request.setExpenseTypeCode(pcxBillExpDetailCommon.getExpDetailCode());
                    }

                    expenseDetail.setInvoiceAmt(pcxBillExpDetailCommon.getInputAmt());  // 差旅/交通费金额 FExpenseAmount
                    expenseDetail.setRealAmt(pcxBillExpDetailCommon.getCheckAmt().subtract(pcxBillExpDetailCommon.getTaxAmt()).toPlainString()); // 费用金额 FTaxSubmitAmt

                    expenseDetail.setInvoiceQty(invoiceQty);  // 发票张数  F_KF_VOICEQUANTITY
                    request.setInvoiceQty(invoiceQty);

                    // 对于 0702 的费用类型，需要转换
                    if ("0702".equals(request.getExpenseTypeCode())) {
                        request.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
                    }
                    Response<TravelExpenseReimbursementResponse> response = travelExpenseParamsFactoryClient.getTravelExpenseReportParams(request);
                    Assert.isTrue(response.isSuccess(), response.getMsg());
                    TravelExpenseReimbursementResponse reimbursementResponse = response.getData();
                    expenseDetail.setTravelCode(reimbursementResponse.getTravelCode()); // 差旅费类型 FTRAVELTYPE
                    expenseDetail.setInvoiceType(reimbursementResponse.getInvoiceType());  // 发票类型   FINVOICETYPE

                    BigDecimal taxRate = pcxBillExpDetailCommon.getTaxRate();
                    BigDecimal taxAmt = pcxBillExpDetailCommon.getTaxAmt();
                    if (taxRate.compareTo(BigDecimal.ONE) < 0) {
                        taxRate = taxRate.multiply(BigDecimal.valueOf(100));
                    }
                    // 处理 6% 的税率
                    List<String> specialInvoices = Arrays.asList(InvoiceType.VAT_SPECIAL_ELECTRONIC.getCode(), InvoiceType.VAT_SPECIAL.getCode());
                    if (taxRate.compareTo(BigDecimal.valueOf(6)) == 0 && !specialInvoices.contains(reimbursementResponse.getInvoiceType())) {
                        taxRate = BigDecimal.ZERO;
                        taxAmt = BigDecimal.ZERO;
                    }
                    expenseDetail.setRate(taxRate.toPlainString());    // 税率    FTaxRate
                    expenseDetail.setAmt(taxAmt.toPlainString());   // 税额   FTaxAmt

                    expenseDetail.setIsRealName(isRealName);    // 是否实名（0：否；1：是）   FIsRealName

                    String remark = StringUtils.isBlank(pcxBillExpDetailCommon.getNoEcsReason()) ? "无" : pcxBillExpDetailCommon.getNoEcsReason();
                    expenseDetail.setRemark(remark);  // 备注 FREMARK

                    expenseDetail.setApplyExpMoney(pcxBillExpDetailCommon.getInputAmt());   // 申请报销金额   FExpenseAmount
                    expenseDetail.setApplyPayMoney(pcxBillExpDetailCommon.getInputAmt().toPlainString());   // 申请付款金额   FRequestAmount
                    expenseDetail.setCheckExpMoney(pcxBillExpDetailCommon.getCheckAmt().toPlainString());   // 核定报销金额   FExpSubmitAmount
                    expenseDetail.setCheckPayMoney(pcxBillExpDetailCommon.getCheckAmt().toPlainString());   // 核定付款金额   FReqSubmitAmount
                    String budgetExpProject = null;
                    String computeExpProject = null;
                    if (Objects.equals(bizType, PcxBillSyncServiceImpl.TRAFFIC)) {
                        budgetExpProject = "YS.07.01";
                        computeExpProject = "HS.07.01";
                    } else if (Objects.equals(bizType, PcxBillSyncServiceImpl.TRAVEl)) {
                        budgetExpProject = "YS.07.02";
                        computeExpProject = "HS.07.02";
                    }
                    expenseDetail.setBudgetExpProject(budgetExpProject);    // 对应预算费用项目 F_BudgetExpenseItem
                    expenseDetail.setComputeExpProject(computeExpProject);   // 对应核算费用项目 F_CheckExpenseItem

                    expenseDetail.setLoanChargeMoney(null); // 冲借款金额    FBorrowAmount

                    String startTime = pcxBill.getTransDate();
                    String endTime = pcxBill.getTransDate();
                    String startPlace = placeName;
                    String endPlace = placeName;
                    // 差旅类型，需要从专属属性获取出发地、目的地、开始时间和结束时间
                    if (Objects.equals(bizType, PcxBillSyncServiceImpl.TRAVEl)) {
                        PcxBillExpCommon pcxBillExpCommon = null;
                        if (pcxBillExpCommonIdMap.containsKey(pcxBillExpDetailCommon.getExpenseId())) {
                            pcxBillExpCommon = pcxBillExpCommonIdMap.get(pcxBillExpDetailCommon.getExpenseId());
                        } else if (pcxBillExpCommonBillIdMap.containsKey(billId)) {
                            pcxBillExpCommon = pcxBillExpCommonBillIdMap.get(billId);
                        }
                        if (pcxBillExpCommon != null) {
                            String field01 = pcxBillExpCommon.getField01();
                            String field02 = pcxBillExpCommon.getField02();
                            if (StringUtils.isNotBlank(field01) && StringUtils.isNotBlank(field02)) {
                                startPlace = field01;
                                endPlace = field02;
                            }
                            String field03 = pcxBillExpCommon.getField03();
                            String field04 = pcxBillExpCommon.getField04();
                            if (StringUtils.isNotBlank(field03) && StringUtils.isNotBlank(field04)) {
                                startTime = field03;
                                endTime = field04;
                            }
                        }
                    }

                    expenseDetail.setStartTime(startTime);    // 开始日期 FStartDate
                    expenseDetail.setEndTime(endTime);  // 结束日期   FFinishDate
                    expenseDetail.setStartPlaceName(startPlace);   // 出发地  FStartSite
                    expenseDetail.setTravelPlaceName(endPlace);  // 目的地 FEndSite

                    // 补充原始信息
                    expenseDetail.setDetailId(pcxBillExpDetailCommon.getId());
                    expenseDetail.setOriginalDepartmentCode(pcxBillExpDetailCommon.getDepartmentCode());
                    expenseDetail.setExpDetailCode(pcxBillExpDetailCommon.getExpDetailCode());

                    return expenseDetail;
                })
                .collect(Collectors.toList());

        List<ExtendTravelExpenseDetailDTO> expenseDetailDTOS = extendExpenseDetailDTOS
                .stream()
                .filter(pcxBillExpDetailTravel -> {
                    BigDecimal realAmt = new BigDecimal(pcxBillExpDetailTravel.getRealAmt());
                    return realAmt.compareTo(BigDecimal.ZERO) > 0;
                })
                .collect(Collectors.toList());

        BigDecimal filterInputAmt = extendExpenseDetailDTOS
                .stream()
                .filter(pcxBillExpDetailTravel -> {
                    BigDecimal realAmt = new BigDecimal(pcxBillExpDetailTravel.getRealAmt());
                    return realAmt.compareTo(BigDecimal.ZERO) <= 0;
                })
                .map(ExtendTravelExpenseDetailDTO::getApplyExpMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<PcxBillAmtApportionDepartment> pcxBillAmtApportionDepartments = pcxBillAmtApportionDepartmentsMap.getOrDefault(billId, new ArrayList<>());

        // 核定金额为 0 的情况下，需要将部门分摊的申请金额也进行扣减
        subtractAmount(pcxBillAmtApportionDepartments, filterInputAmt);

        List<AllocationItemDTO> allocationItems = getTravelAllocationItems(expenseDetailDTOS, pcxBillAmtApportionDepartments, currentDepartment);
//        // 核定金额为 0 的情况下，需要将部门分摊的申请金额也进行扣减
//        subtractAmount(pcxBillAmtApportionDepartments, filterInputAmt);
//
//        List<BillAllocationUtil.AllocationItem> allocationItems = getTravelAllocationItems(expenseDetailDTOS, pcxBillAmtApportionDepartments, currentDepartment);

        Map<String, ExtendTravelExpenseDetailDTO> extendTravelExpenseDetailDTOMap =
                expenseDetailDTOS.stream().collect(Collectors.toMap(ExtendTravelExpenseDetailDTO::getDetailId, Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, PcxBillAmtApportionDepartment> billAmtApportionDepartmentMap =
                pcxBillAmtApportionDepartments.stream().collect(Collectors.toMap(pcxBillAmtApportionDepartment -> currentDepartment.getMadCode(), Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, Integer> invoiceCountMap = new HashMap<>();
        List<TravelExpenseDetailDTO> dataList = allocationItems
                .stream()
                .filter(allocationItem -> extendTravelExpenseDetailDTOMap.containsKey(allocationItem.getBillId()))
                .map(allocationItem -> {
                    BigDecimal applyAmt = BigDecimal.valueOf(allocationItem.getAllocatedAmount()).divide(BigDecimal.valueOf(100));
                    BigDecimal inputAmt = BigDecimal.valueOf(allocationItem.getAllocatedInputAmount()).divide(BigDecimal.valueOf(100));
                    ExtendTravelExpenseDetailDTO extendTravelExpenseDetailDTO = extendTravelExpenseDetailDTOMap.get(allocationItem.getBillId());
                    String departmentCode = allocationItem.getDepartmentId();
                    extendTravelExpenseDetailDTO.setDepartmentCode(allocationItem.getDepartmentId());    // 费用承担部门 FEXPENSEDEPT
                    extendTravelExpenseDetailDTO.setInvoiceAmt(inputAmt);
                    extendTravelExpenseDetailDTO.setApplyExpMoney(inputAmt);
                    extendTravelExpenseDetailDTO.setApplyPayMoney(inputAmt.toPlainString());
                    extendTravelExpenseDetailDTO.setCheckExpMoney(applyAmt.toPlainString());
                    extendTravelExpenseDetailDTO.setCheckPayMoney(applyAmt.toPlainString());

                    String firstDepartment = selectFirstDepartmentCode(allocationItem.getDepartment(), departmentCode);
                    extendTravelExpenseDetailDTO.setFirstDepartment(firstDepartment); // 一级部门   F_KF_MANAGEDEPTID

                    if (billAmtApportionDepartmentMap.containsKey(departmentCode)) {
                        PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment = billAmtApportionDepartmentMap.get(departmentCode);

                        extendTravelExpenseDetailDTO.setLongExp(pcxBillAmtApportionDepartment.getAcitem09Code()); // 长期待摊费用    F_ZHKF_PrepaidExpenses
                        extendTravelExpenseDetailDTO.setA8ProjectInfo(pcxBillAmtApportionDepartment.getAcitem08Code());   // A8/PP项目信息     F_ZHKF_ProjectInfo
                        extendTravelExpenseDetailDTO.setCustomer(pcxBillAmtApportionDepartment.getAcitem03Code());    // 客户    F_ZHKF_AssignedCustomer
                        extendTravelExpenseDetailDTO.setOpenProject(pcxBillAmtApportionDepartment.getAcitem06Code()); // 招标项目  F_KF_PROJECTNUMBER
                        extendTravelExpenseDetailDTO.setDevProject(pcxBillAmtApportionDepartment.getAcitem07Code());  // 研发项目  F_KF_ResearchProject
                        extendTravelExpenseDetailDTO.setBiIncomeType(pcxBillAmtApportionDepartment.getAcitem04Code());    // BI收入类型    F_BIIncomeType
                        extendTravelExpenseDetailDTO.setComputeType(pcxBillAmtApportionDepartment.getAcitem05Code()); // 核算收入类型    F_CheckIncomeType
                    }

                    String invoiceNo = extendTravelExpenseDetailDTO.getInvoiceNo();
                    if (StringUtils.isNotBlank(invoiceNo)) {
                        invoiceCountMap.put(invoiceNo, invoiceCountMap.getOrDefault(invoiceNo, 0) + 1);
                    }

                    String airFund = getAirFund(extendTravelExpenseDetailDTO, airExpDetailEcsRelMap);
                    extendTravelExpenseDetailDTO.setAirFund(airFund);    // 民航发展基金    FCivilAviationFund

                    TravelExpenseDetailDTO travelExpenseDetailDTO = new TravelExpenseDetailDTO();
                    BeanUtils.copyProperties(extendTravelExpenseDetailDTO, travelExpenseDetailDTO);

                    return travelExpenseDetailDTO;
                })
                .collect(Collectors.toList());

        for (TravelExpenseDetailDTO travelExpenseDetailDTO : dataList) {
            String invoiceNo = travelExpenseDetailDTO.getInvoiceNo();
            String sameInvoice = "0";
            if (StringUtils.isNotBlank(invoiceNo) && invoiceCountMap.get(invoiceNo) > 1) {
                sameInvoice = "1";
            }
            travelExpenseDetailDTO.setSameInvoice(sameInvoice);   // 是否同一发票（0：否；1：是）   F_KF_ComboFP
        }

        boolean isExpDept = dataList.stream().allMatch(kingdeeTravelExpenseDetailDTO -> {
            return StringUtils.equals(deptCode, kingdeeTravelExpenseDetailDTO.getDepartmentCode());
        });

        travelExpenseReimbursementDTO.setDefaultExpDept(isExpDept ? "1" : "0");   //  是否本部门费用（0：否；1：是）    F_KF_Combo1

        travelExpenseReimbursementDTO.setDetailList(dataList);

        return travelExpenseReimbursementDTO;
    }

    private Map<String, String> selectEcsBillEinvMap(List<String> billIds) {
        List<Map<String, String>> ecsBillEinvs = selectEcsBillEinvByBillIds(billIds);
        Map<String, String> ecsBillEinvMap = new HashMap<>();
        for (Map<String, String> ecsBillEinv : ecsBillEinvs) {
            ecsBillEinvMap.put(ecsBillEinv.get("billId"), ecsBillEinv.get("invSubKind"));
        }
        return ecsBillEinvMap;
    }

    /**
     * 查询数电票电子会计凭证,用于判断普票与专票
     */
    private List<Map<String, String>> selectEcsBillEinvByBillIds(List<String> billIds) {
        if (billIds.isEmpty()) {
            return Collections.emptyList();
        }
        return jdbcTemplate.query("select bill_id, inv_sub_kind from ecs_bill_einv where bill_id in(:billIds)",
                new MapSqlParameterSource("billIds", billIds),
                (rs, rowNum) -> {
                    Map<String, String> mapOfColumnValues = new HashMap<>();
                    mapOfColumnValues.put("billId", rs.getString("bill_id"));
                    mapOfColumnValues.put("invSubKind", rs.getString("inv_sub_kind"));
                    return mapOfColumnValues;
                });
    }

    private String getAirFund(ExtendTravelExpenseDetailDTO extendTravelExpenseDetailDTO,
                              Map<String, PcxExpDetailEcsRel> airExpDetailEcsRelMap) {
        String airFund = "50.00";
        if (TravelExpenseType.AIR_TRANSPORT.getCode().equals(extendTravelExpenseDetailDTO.getTravelCode()) &&
                BigDecimal.ZERO.compareTo(new BigDecimal(extendTravelExpenseDetailDTO.getAirFund())) == 0) {
            if ("1".equals(extendTravelExpenseDetailDTO.getIsRealName())) {
                String ecsBillId = extendTravelExpenseDetailDTO.getEcsBillId();
                if (airExpDetailEcsRelMap.containsKey(ecsBillId)) {
                    PcxExpDetailEcsRel pcxExpDetailEcsRel = airExpDetailEcsRelMap.get(ecsBillId);
                    airFund = pcxExpDetailEcsRel.getEcsAmt().toPlainString();
                }
                if (BigDecimal.ZERO.compareTo(new BigDecimal(airFund)) == 0) {
                    airFund = "50.00";
                }
            } else {
                airFund = "0.01";
            }
        }
        // 专票的情况下，民航发展基金是 0.01
        if (InvoiceType.VAT_SPECIAL_ELECTRONIC.getCode().equals(extendTravelExpenseDetailDTO.getInvoiceType())) {
            airFund = "0.01";
        }
        return airFund;
    }

    private Map<String, List<PcxBillExpCommon>> selectBillExpCommonGroupMap(List<String> billIds) {
        List<PcxBillExpCommon> pcxBillExpCommons = pcxBillExpCommonDao.selectList(
                Wrappers.lambdaQuery(PcxBillExpCommon.class)
                        .in(PcxBillExpCommon::getBillId, billIds)
        );

        return pcxBillExpCommons
                .stream()
                .filter(pcxBillExpCommon -> !"*".equals(pcxBillExpCommon.getExpenseCode()))
                .collect(Collectors.groupingBy(PcxBillExpCommon::getBillId));
    }

}
