package com.pty.pcx.service.impl.treasurypay.detail;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pty.ep.entity.ep.EpPayBill;
import com.pty.ep.entity.ep.EpPayBillDetail;
import com.pty.mad.entity.PaValset;
import com.pty.pct.entity.dto.PlanPayStatusDTO;
import com.pty.pcx.api.bas.IPcxBasExpExpecoService;
import com.pty.pcx.api.bas.PcxSettlementRuleService;
import com.pty.pcx.api.bill.PcxBillBalanceService;
import com.pty.pcx.api.bill.PcxBillExpCommonService;
import com.pty.pcx.api.bill.PcxBillSettlementInfoService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.api.contract.PcxBillContractRelService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.treasurypay.change.IPcxChangeBillService;
import com.pty.pcx.balance.IBalanceExternalService;
import com.pty.pcx.common.constant.BillHistoryConstant;
import com.pty.pcx.common.constant.FundPositionEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.common.enu.BillChangeTypeEnum;
import com.pty.pcx.common.enu.BillPayDetailStatusEnum;
import com.pty.pcx.common.enu.BillPayerDetailEnum;
import com.pty.pcx.common.enu.BillStatusEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dao.payTest.PcxBankAccountTestDao;
import com.pty.pcx.dao.treasurypay.detail.PcxBillPayDetailDao;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.dto.mad.MadAgyAccountDTO;
import com.pty.pcx.dto.mad.MadBankNodeDTO;
import com.pty.pcx.dto.mad.MadCurrentDTO;
import com.pty.pcx.dto.mad.MadEmployeeCardDTO;
import com.pty.pcx.entity.bas.PcxSettlementRule;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.payTest.PcxBankAccountTest;
import com.pty.pcx.entity.setting.PaOption;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.ep.IEpPayBillExternalService;
import com.pty.pcx.mad.IMadAgyAccountExternalService;
import com.pty.pcx.mad.IMadBankNodeExternalService;
import com.pty.pcx.mad.IMadCurrentExternalService;
import com.pty.pcx.mad.IMadEmployeeCardExternalService;
import com.pty.pcx.pa.IPcxBillNoService;
import com.pty.pcx.pa.IPcxValSetService;
import com.pty.pcx.pct.IPctExternalService;
import com.pty.pcx.qo.bill.PcxBillListQO;
import com.pty.pcx.qo.bill.PcxBillPaySettlementBalanceQO;
import com.pty.pcx.qo.mad.MadAgyAccountQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.qo.treasurypay.change.PcxChangeBillQO;
import com.pty.pcx.qo.treasurypay.detail.*;
import com.pty.pcx.qo.treasurypay.detail.PcxBillPayDetailQO;
import com.pty.pcx.qo.treasurypay.detail.PcxBillPayItemQO;
import com.pty.pcx.qo.treasurypay.detail.PcxBillPayQO;
import com.pty.pcx.service.impl.bill.balance.PcxBalanceCtrlService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.CallBackService;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillPayDetailPlusService;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillStatusHistoryPlusService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.util.trans.PageTransformer;
import com.pty.pcx.util.trans.PcxBillTransformer;
import com.pty.pcx.vo.treasurypay.change.PcxChangeBillVO;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.treasurypay.detail.*;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pty.pcx.common.enu.BillPayDetailStatusEnum.*;
import static com.pty.pcx.common.enu.PositionBlockEnum.PAY_DETAIL;

/**
 * (PcxBillPayDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-13 18:43:56
 */
@Slf4j
@Indexed
@Service
public class PcxBillPayDetailServiceImpl extends ServiceImpl<PcxBillPayDetailDao, PcxBillPayDetail> implements IPcxBillPayDetailPlusService {

    public static final String SETTLE_LOAN = "settle_loan";
    @Autowired
    private PcxBillPayDetailDao pcxBillPayDetailDao;
    @Autowired
    private PcxBillSettlementInfoService pcxBillSettlementInfoService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private IBalanceExternalService balanceExternalService;
    @Autowired
    private PcxBillBalanceService pcxBillBalanceService;
    @Autowired
    private IPcxChangeBillService pcxChangeBillService;
    @Autowired
    private IPcxBasExpExpecoService pcxBasExpExpecoService;
    @Autowired
    private IPcxBillStatusHistoryPlusService pcxBillStatusHistoryPlusService;
    @Autowired
    private PcxBillStatusHistoryServiceImpl pcxBillStatusHistoryService;
    @Autowired
    private IMadAgyAccountExternalService madAgyAccountExternalService;
    @Autowired
    private IMadBankNodeExternalService madBankNodeExternalService;
    @Autowired
    private IMadEmployeeCardExternalService madEmployeeCardExternalService;
    @Autowired
    private IMadCurrentExternalService madCurrentExternalService;
    @Autowired
    private IPcxBillNoService pcxBillNoService;
    @Autowired
    private IBusinessRuleOptionService businessRuleOptionService;
    @Autowired
    private IEpPayBillExternalService epPayBillExternalService;
    @Autowired
    private IPcxValSetService pcxValSetService;
    @Autowired
    private PcxSettlementRuleService pcxSettlementRuleService;
    @Autowired
    private PcxBillDao pcxBillDao;
    @Autowired
    private PcxBalanceCtrlService pcxBalanceCtrlService;
    @Autowired
    private PcxBillExpCommonService pcxBillExpCommonService;
    @Autowired
    private CallBackService callBackService;
    @Autowired
    private PcxBankAccountTestDao pcxBankAccountTestDao;
    @Autowired
    private IPctExternalService pctExternalService;
    @Autowired
    private PcxBillContractRelService pcxBillContractRelService;
    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;
    @Autowired
    private BillExpenseCommonService billExpenseCommonService;
    @Autowired
    private PayDetailCalculator payDetailCalculator;

    /**
     * 网银支付-发送，发送并支付
     */
    private static final String SEND_AND_PAY = "1";

    /**
     * 生成LambdaQueryWrapper的查询条件
     *
     * @param bean
     * @return
     */
    public LambdaQueryWrapper<PcxBillPayDetail> getQueryWrapper(PcxBillPayDetailQO bean) {
        LambdaQueryWrapper<PcxBillPayDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (bean != null) {
            queryWrapper.eq(PcxBillPayDetail::getIsDeleted, 0);
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getId()), PcxBillPayDetail::getId, bean.getId());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getBillId()), PcxBillPayDetail::getBillId, bean.getBillId());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getExpenseCode()), PcxBillPayDetail::getExpenseCode, bean.getExpenseCode());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getBalanceId()), PcxBillPayDetail::getBalanceId, bean.getBalanceId());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getSettlementUk()), PcxBillPayDetail::getSettlementUk, bean.getSettlementUk());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayAccountNo()), PcxBillPayDetail::getPayAccountNo, bean.getPayAccountNo());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayAccountName()), PcxBillPayDetail::getPayAccountName, bean.getPayAccountName());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayBankName()), PcxBillPayDetail::getPayBankName, bean.getPayBankName());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayeeAccountNo()), PcxBillPayDetail::getPayeeAccountNo, bean.getPayeeAccountNo());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayeeAccountName()), PcxBillPayDetail::getPayeeAccountName, bean.getPayeeAccountName());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayeeBankName()), PcxBillPayDetail::getPayeeBankName, bean.getPayeeBankName());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getSettlementType()), PcxBillPayDetail::getSettlementType, bean.getSettlementType());
            queryWrapper.eq(bean.getCheckAmt() != null, PcxBillPayDetail::getCheckAmt, bean.getCheckAmt());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getSummary()), PcxBillPayDetail::getSummary, bean.getSummary());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getAgyCode()), PcxBillPayDetail::getAgyCode, bean.getAgyCode());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getAgyName()), PcxBillPayDetail::getAgyName, bean.getAgyName());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getFiscal()), PcxBillPayDetail::getFiscal, bean.getFiscal());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getTenantId()), PcxBillPayDetail::getTenantId, bean.getTenantId());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayNo()), PcxBillPayDetail::getPayNo, bean.getPayNo());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getPayStatus()), PcxBillPayDetail::getPayStatus, bean.getPayStatus());
            queryWrapper.eq(StringUtils.isNoneBlank(bean.getMofDivCode()), PcxBillPayDetail::getMofDivCode, bean.getMofDivCode());
            queryWrapper.between(StringUtils.isNotBlank(bean.getPayStartDate()) && StringUtils.isNotBlank(bean.getPayEndDate()), PcxBillPayDetail::getPayingTime, bean.getPayStartDate()+"00:00:00", bean.getPayEndDate()+"23:59:59");
            queryWrapper.between(StringUtils.isNotBlank(bean.getSureStartDate()) && StringUtils.isNotBlank(bean.getSureEndDate()), PcxBillPayDetail::getConfirmTime, bean.getSureStartDate()+"00:00:00", bean.getSureEndDate()+"23:59:59");
            queryWrapper.in(CollectionUtils.isNotEmpty(bean.getBillIdList()), PcxBillPayDetail::getBillId, bean.getBillIdList());
            queryWrapper.in(CollectionUtils.isNotEmpty(bean.getSettlementTypeList()), PcxBillPayDetail::getSettlementType, bean.getSettlementTypeList());
            if (CollectionUtils.isNotEmpty(bean.getEntries())) {
                queryWrapper.and(innerWrapper -> {
                    bean.getEntries().forEach(entry -> {
                        innerWrapper.or(childrenWrapper -> {
                            childrenWrapper.eq(PcxBillPayDetail::getBalanceId, entry.getBalanceId());
                            childrenWrapper.eq(PcxBillPayDetail::getPayeeAccountNo, entry.getPayeeAccountNo());
                            childrenWrapper.eq(PcxBillPayDetail::getSettlementType, entry.getSettlementType());
                        });
                    });
                });
            }
        }
        return queryWrapper;
    }

    public LambdaQueryWrapper<PcxBillPayDetail> getAllQueryWrapper(PcxBillPayDetailQO bean) {
        LambdaQueryWrapper<PcxBillPayDetail> queryWrapper = this.getQueryWrapper(bean);
        if (bean != null) {
            queryWrapper.and(innerWrapper -> innerWrapper.in(CollectionUtils.isNotEmpty(bean.getBalanceIds()), PcxBillPayDetail::getBalanceId, bean.getBalanceIds())
                    .or(key -> key.like(StringUtils.isNoneBlank(bean.getKeyword()), PcxBillPayDetail::getPayeeAccountName, bean.getKeyword())));
        }
        return queryWrapper;
    }


    /**
     * 重写selectList方法，用于查询单据支付详情列表
     *
     * @param qo 查询对象，包含查询条件
     * @return 返回一个包含单据支付详情列表的CheckMsg对象
     */
    @Override
    public CheckMsg<List<PcxBillPayDetailVO>> selectList(PcxBillPayDetailQO qo) {
        // 查询单据支付详情列表
        List<PcxBillPayDetail> payDetails = pcxBillPayDetailDao.selectList(this.getQueryWrapper(qo));
        if (CollectionUtils.isEmpty(payDetails))
            return CheckMsg.success(Collections.emptyList());
        // 将查询结果转换为视图对象列表
        List<PcxBillPayDetailVO> vos = PcxBillTransformer.INSTANCE.toPayDetailVOs(payDetails);
        // 提取并去重得到所有的指标ID
        List<String> balanceIds = vos.stream().map(PcxBillPayDetailVO::getBalanceId).distinct().collect(Collectors.toList());

        // 提取并去重得到所有的单据ID
        List<String> billIds = vos.stream().map(PcxBillPayDetailVO::getBillId).distinct().collect(Collectors.toList());
        // 根据单据ID查询主单据信息
        List<PcxBill> pcxBills = billMainService.selectMainBill(PcxBill.builder().ids(billIds).build());

        // 根据指标ID查询指标信息
        List<BudBalanceDTO> balanceDtoList = balanceExternalService.getBalanceByIds(balanceIds);
        // 将指标信息转换为映射，便于后续关联
        Map<String, BudBalanceDTO> balanceRel = balanceDtoList.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity(), (a, b) -> a));
        // 为每个视图对象关联指标信息
        vos.forEach(vo -> {
            vo.attach(balanceRel.getOrDefault(vo.getBalanceId(), new BudBalanceDTO()));
            vo.setSettlementTypeName(SettlementTypeEnum.getNameByCode(vo.getSettlementType()));
        });

        // 将单据信息转换为映射，便于后续关联
        Map<String, PcxBill> billRel = pcxBills.stream().collect(Collectors.toMap(PcxBill::getId, Function.identity(), (a, b) -> a));
        // 为每个视图对象关联单据信息
        vos.forEach(vo -> {
            vo.sprinkle(billRel.getOrDefault(vo.getBillId(), new PcxBill()));
        });
        // 返回包含单据支付详情列表的响应对象
        return CheckMsg.success(vos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<List<PcxBillPayDetail>> changingPayDetails(PcxBillPayDetailQO param) {
        try {
            List<PcxBillPayDetail> details = this.list(getQueryWrapper(PcxBillTransformer.INSTANCE.toPayDetailQO(param)));
            if (CollectionUtils.isEmpty(details)) {
                log.info("支付明细=>未找到符合条件的支付详情");
                return CheckMsg.success(Collections.emptyList());
            }
            List<PcxBillStatusHistory> histories = new ArrayList<>();
            details.forEach(detail -> {
                detail.setPayStatus(BillPayDetailStatusEnum.CHANGING.getCode());
                histories.add(PcxBillStatusHistory
                        .builder()
                        .id(IDGenerator.id())
                        .billId(detail.getPayNo())
                        .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                        .agyCode(detail.getAgyCode())
                        .mofDivCode(detail.getMofDivCode())
                        .opTime(new Date())
                        .fiscal(detail.getFiscal())
                        .fromStatus(detail.getPayStatus())
                        .toStatus(CHANGING.getCode())
                        .opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_CONFIRM.getCode())
                        .creator(PtyContext.getUsername())
                        .creatorName(PtyContext.getUsernameCn())
                        .createdTime(DateUtil.now())
                        .build());
            });
            this.updateBatchById(details);
            pcxBillStatusHistoryPlusService.saveBatch(histories);
            return CheckMsg.success(details);
        }catch (Exception e) {
            log.error("支付明细调整单开始失败, {}", e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
    }

    @Override
    @Transactional
    public CheckMsg<Void> changingFinished(List<PcxBillPayDetail> details) {
        try {
            if (CollectionUtils.isEmpty(details))
                return CheckMsg.success();
            List<PcxBillStatusHistory> histories = new ArrayList<>();
            List<PcxBillPayDetail> cancelDetails = new ArrayList<>();
            details.forEach(detail -> {
                // 作废原记录
                histories.add(PcxBillStatusHistory
                        .builder()
                        .id(IDGenerator.id())
                        .billId(detail.getPayNo())
                        .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                        .agyCode(detail.getAgyCode())
                        .mofDivCode(detail.getMofDivCode())
                        .opTime(new Date())
                        .fiscal(detail.getFiscal())
                        .fromStatus(detail.getPayStatus())
                        .toStatus(CANCELED.getCode())
                        .opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_CONFIRM.getCode())
                        .creator(PtyContext.getUsername())
                        .creatorName(PtyContext.getUsernameCn())
                        .createdTime(DateUtil.now())
                        .build());
                cancelDetails.add(PcxBillPayDetail.builder()
                        .id(detail.getId())
                        .payStatus(CANCELED.getCode())
                        .isDeleted(detail.getIsDeleted() + 1)
                        .build());
            });
            this.updateBatchById(cancelDetails);
            pcxBillStatusHistoryPlusService.saveBatch(histories);

            // 生成新记录
            details.forEach(detail -> {
                detail.setPayStatus(CONFIRMED.getCode());
                detail.setId(IDGenerator.id());
                detail.setModifier(PtyContext.getUsername());
                detail.setModifierName(PtyContext.getUsernameCn());
                detail.setModifiedTime(DateUtil.now());
                detail.setConfirmTime(DateUtil.now());
                detail.setPayNo(pcxBillNoService.getBillNo(detail.getMofDivCode(), detail.getAgyCode(), PAY_DETAIL.getCode(), null));
            });
            this.saveBatch(details);
            return CheckMsg.success();
        }catch (Exception e) {
            log.error("支付明细调整单完成后处理失败, {}", e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
    }

    /**
     * 根据查询条件和单据支付详情状态，选择单据支付详情列表
     * 此方法用于处理单据支付详情的查询请求，根据传入的查询对象和状态，返回相应的单据支付详情列表
     *
     * @param qo                      单据支付详情查询对象，包含查询条件
     * @param billPayDetailStatusEnum 单据支付详情的状态，用于决定返回的详情对象类型
     * @return 包含单据支付详情列表的检查消息对象，根据查询结果和状态，返回不同类型的单据支付详情
     */
    @Override
    public CheckMsg<PageResult<? extends AbstractPcxBillPayDetailForSomething>> selectListForDetailStatus(PcxBillPayDetailQO qo, BillPayDetailStatusEnum billPayDetailStatusEnum) {
        // 查询单据支付详情列表
        List<PcxBillPayDetail> payDetails = pcxBillPayDetailDao.selectList(this.getQueryWrapper(qo));

        // 初始化结果列表
        List<AbstractPcxBillPayDetailForSomething> result = new ArrayList<>();

        // 如果查询结果为空，直接返回成功消息，包含空列表
        if (CollectionUtils.isEmpty(payDetails)) {
            return CheckMsg.success(new PageResult<>());
        }

        // 按来源单据分页
        PcxBillListQO billParam = new PcxBillListQO();
        billParam.setIds(payDetails.stream().map(PcxBillPayDetail::getBillId).collect(Collectors.toList()));
        billParam.setAgyCode(qo.getAgyCode());
        billParam.setFiscal(qo.getFiscal());
        billParam.setMofDivCode(qo.getMofDivCode());
        billParam.setKeyword(qo.getKeyword());
        billParam.setDepartmentCodeList(qo.getDepartmentCodeList());
        billParam.setBillFuncCodeList(qo.getBillFuncCodeList());
        billParam.setItemCodeList(qo.getItemCodeList());
        billParam.setSettlementTypeList(qo.getSettlementTypeList());
        billParam.setPageSize(qo.getPageSize());
        billParam.setPageIndex(qo.getPageIndex());
        billParam.setStartDate(qo.getStartDate());
        billParam.setEndDate(qo.getEndDate());
        billParam.setFinalAuditStartDate(qo.getFinalAuditStartDate());
        billParam.setFinalAuditEndDate(qo.getFinalAuditEndDate());
        billParam.setPayStartDate(qo.getPayStartDate());
        billParam.setPayEndDate(qo.getPayEndDate());
        billParam.setBillStatus(qo.getBillStatus());
        // 此处默认不查询为变更状态的单据
        billParam.setNeBillStatus(BillStatusEnum.CHANGE.getCode());
        // 查询与支付详情相关的主单据信息
        Page<PcxBill> pcxBills = billMainService.selectParamIndexPage(billParam);

        if (CollectionUtils.isEmpty(pcxBills.getRecords())) {
            return CheckMsg.success(new PageResult<>());
        }
        // 构建支付详情的映射关系，以便后续处理
        Map<String, List<PcxBillPayDetail>> payDetailRel = payDetails.stream().collect(Collectors.groupingBy(PcxBillPayDetail::getBillId));

        // 遍历主单据列表，根据单据支付详情状态，创建并添加相应的单据支付详情对象到结果列表
        pcxBills.getRecords().forEach(bill -> {
            List<PcxBillPayDetail> payDetailList = payDetailRel.getOrDefault(bill.getId(), Collections.emptyList());

            // 查询与支付详情相关的预算指标信息
            List<BudBalanceDTO> balanceDtoList = balanceExternalService.getBalanceByIds(payDetailList.stream()
                    .map(PcxBillPayDetail::getBalanceId).collect(Collectors.toList()));
            // 查询与支付详情相关的结算信息
            List<PcxBillSettlement> settlementInfos = pcxBillSettlementInfoService.selectByUkMd5(payDetailList.stream().map(PcxBillPayDetail::getSettlementUk).collect(Collectors.toList()));
            Map<String, BudBalanceDTO> balanceRel = balanceDtoList.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity(), (a, b) -> a));
            Map<String, PcxBillSettlement> settlementRel = settlementInfos.stream().collect(Collectors.toMap(PcxBillSettlement::getSettlementUk, Function.identity(), (a, b) -> a));

            // 根据单据支付详情状态，执行不同的逻辑
            switch (billPayDetailStatusEnum) {
                // 待确认
                case CONFIRM:
                    result.add(new PcxBillPayDetailForConfirmVO(bill, payDetailList, balanceRel, settlementRel));
                    break;
                // 已确认
                case CONFIRMED:
                    result.add(new PcxBillPayDetailForPayVO(bill, payDetailList, balanceRel, settlementRel));
                    break;
                // 支付完成
                case PAID:
                    result.add(new PcxBillPayDetailForPaySuccessVO(bill, payDetailList, balanceRel, settlementRel));
                    break;
                default:
                    break;
            }
        });

        //分页转换
        Page<AbstractPcxBillPayDetailForSomething> resultPage = new Page<AbstractPcxBillPayDetailForSomething>(qo.getPageIndex(), qo.getPageSize(), pcxBills.getTotal()).setRecords(result);

        // 返回包含单据支付详情列表的成功消息
        return CheckMsg.success(PageTransformer.INSTANCE.plus2result(resultPage));
    }

    @Override
    public CheckMsg<Map<String, Object>> selectListForChangeBillStatus(PcxBillPayDetailQO qo) {
        // 查询单据支付详情列表
        List<PcxBillPayDetail> payDetails = pcxBillPayDetailDao.selectList(this.getQueryWrapper(qo));

        // 初始化结果列表
        List<PcxBillPayDetailForChangeVO> result = new ArrayList<>();

        // 如果查询结果为空，直接返回成功消息，包含空列表
        if (CollectionUtils.isEmpty(payDetails)) {
            return CheckMsg.success();
        }

        // 按来源单据分页
        PcxBillListQO billParam = new PcxBillListQO();
        billParam.setIds(payDetails.stream().map(PcxBillPayDetail::getBillId).collect(Collectors.toList()));
        billParam.setAgyCode(qo.getAgyCode());
        billParam.setFiscal(qo.getFiscal());
        billParam.setMofDivCode(qo.getMofDivCode());
        billParam.setKeyword(qo.getKeyword());
        billParam.setDepartmentCodeList(qo.getDepartmentCodeList());
        billParam.setBillFuncCodeList(qo.getBillFuncCodeList());
        billParam.setSettlementTypeList(qo.getSettlementTypeList());
        billParam.setPageSize(qo.getPageSize());
        billParam.setPageIndex(qo.getPageIndex());
        billParam.setStartDate(qo.getStartDate());
        billParam.setEndDate(qo.getEndDate());
        billParam.setFinalAuditStartDate(qo.getFinalAuditStartDate());
        billParam.setFinalAuditEndDate(qo.getFinalAuditEndDate());
        billParam.setPayStartDate(qo.getPayStartDate());
        billParam.setPayEndDate(qo.getPayEndDate());
        billParam.setBillStatus(BillStatusEnum.CHANGE.getCode());

        // 查询与支付详情相关的主单据信息
        Page<PcxBill> pcxBills = billMainService.selectParamIndexPage(billParam);

        if (CollectionUtils.isEmpty(pcxBills.getRecords())) {
            return CheckMsg.success();
        }
        Map<String, PcxBill> billEntityMap = pcxBills.getRecords().stream().collect(Collectors.toMap(PcxBill::getId, Function.identity(), (a, b) -> a));

        List<String> billIds = pcxBills.getRecords().stream().filter(pcxBill -> BillStatusEnum.CHANGE.getCode().equals(pcxBill.getBillStatus())).map(PcxBill::getId).collect(Collectors.toList());
        //获取申请单信息
        PcxChangeBillQO changeBillQO = new PcxChangeBillQO();
        changeBillQO.setChangeBillIds(billIds);
        changeBillQO.setChangeBillStatus(qo.getChangeStatus());
        changeBillQO.setChangeTypeList(qo.getChangeTypeList());
        List<PcxChangeBillVO> changeBillByBillIds = pcxChangeBillService.getChangeBillByBillIds(changeBillQO);
        if (CollectionUtils.isEmpty(changeBillByBillIds)) {
            return CheckMsg.success();
        }

        Map<String, List<PcxChangeBillVO>> changeBillRel = changeBillByBillIds.stream().collect(Collectors.groupingBy(PcxChangeBillVO::getBillId));
        // 构建支付详情的映射关系，以便后续处理
        Map<String, List<PcxBillPayDetail>> payDetailRel = payDetails.stream().collect(Collectors.groupingBy(PcxBillPayDetail::getBillId));
        //查询默认结算方式信息

        //查询启用的结算方式
        PcxSettlementRule settlementRule = new PcxSettlementRule();
        settlementRule.setAgyCode(qo.getAgyCode());
        settlementRule.setFiscal(qo.getFiscal());
        settlementRule.setMofDivCode(qo.getMofDivCode());
        List<BaseDataVo> baseDataVos = pcxSettlementRuleService.selectEnable(settlementRule);
        if (CollectionUtils.isEmpty(baseDataVos)) {
            return CheckMsg.fail("当前单位未配置结算方式");
        }
        //结算方信息
        Map<String, String> codeNameMap = baseDataVos.stream()
                .collect(Collectors.toMap(BaseDataVo::getCode, BaseDataVo::getName, (existing, replacement) -> existing));

        // 遍历主单据列表，根据单据支付详情状态，创建并添加相应的单据支付详情对象到结果列表
        billIds.forEach(billId -> {
            List<PcxBillPayDetail> payDetailList = payDetailRel.getOrDefault(billId, Collections.emptyList());
            // 查询与支付详情相关的预算指标信息
            List<BudBalanceDTO> balanceDtoList = balanceExternalService.getBalanceByIds(payDetailList.stream()
                    .map(PcxBillPayDetail::getBalanceId).collect(Collectors.toList()));
            // 查询与支付详情相关的结算信息
            List<PcxBillSettlement> settlementInfos = pcxBillSettlementInfoService.selectByUkMd5(payDetailList.stream().map(PcxBillPayDetail::getSettlementUk).collect(Collectors.toList()));
            Map<String, BudBalanceDTO> balanceRel = balanceDtoList.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity(), (a, b) -> a));
            Map<String, PcxBillSettlement> settlementRel = settlementInfos.stream().collect(Collectors.toMap(PcxBillSettlement::getSettlementUk, Function.identity(), (a, b) -> a));

            List<PcxChangeBillVO> orDefault = changeBillRel.getOrDefault(billId, Collections.emptyList());
            if (CollectionUtils.isNotEmpty(orDefault)) {
                result.add(new PcxBillPayDetailForChangeVO(billEntityMap.get(billId), payDetailList, balanceRel, settlementRel, orDefault, codeNameMap));
            }
        });
        Map<String, Object> resultPage = new HashMap();
        resultPage.put("total", result.size());
        resultPage.put("result", result);
        resultPage.put("submittedNum", result.stream()
                .filter(vo -> BillChangeStatusEnum.SUBMITTED.getCode().equals(vo.getChangeBillStatus())).count());
        resultPage.put("pendingSubmissionNum", result.stream()
                .filter(vo -> BillChangeStatusEnum.PENDING_SUBMISSION.getCode().equals(vo.getChangeBillStatus())).count());
        // 返回包含单据支付详情列表的成功消息
        return CheckMsg.success(resultPage);
    }

    /**
     * 查询出纳支付列表
     *
     * @param qo
     * @param status
     * @return
     */
    @Override
    public CheckMsg<PageResult<? extends AbstractPcxBillPayDetailForSomething>> selectListForPayerDetail(PcxBillPayDetailQO qo, BillPayerDetailEnum status) {
        // 查询单据支付详情列表
        List<PcxBillPayDetail> payDetails = pcxBillPayDetailDao.selectList(this.getQueryWrapper(qo));

        // 如果查询结果为空，直接返回成功消息，包含空列表
        if (CollectionUtils.isEmpty(payDetails)) {
            return CheckMsg.success(new PageResult<AbstractPcxBillPayDetailForSomething>().setTotal(0L));
        }

        // 查询与支付详情相关的过滤信息
        List<String> otherBillIds = new ArrayList<>();
        if (StringUtils.isNotBlank(qo.getKeyword())) {
            List<String> balanceSourceIds = payDetails.stream().map(PcxBillPayDetail::getBalanceId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(balanceSourceIds)) {
                List<BudBalanceDTO> balanceDList = balanceExternalService.getBalanceByIds(balanceSourceIds);
                List<String> balanceIds = balanceDList.stream().filter(balanceDTO -> balanceDTO.getBalanceNo().contains(qo.getKeyword())).map(BudBalanceDTO::getBalanceId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(balanceIds)) {
                    qo.setBalanceIds(balanceIds);
                    List<PcxBillPayDetail> balancePayDetails = pcxBillPayDetailDao.selectList(this.getAllQueryWrapper(qo));
                    otherBillIds = balancePayDetails.stream().map(PcxBillPayDetail::getBillId).collect(Collectors.toList());
                }
            }

            // 查询与支付详情相关的收款方名称信息
            List<String> payeeANameBillIds = payDetails.stream().filter(payDetail -> payDetail.getPayeeAccountName().contains(qo.getKeyword())).map(PcxBillPayDetail::getBillId).collect(Collectors.toList());
            otherBillIds.addAll(payeeANameBillIds);
        }

        PcxBillListQO billParam = new PcxBillListQO();
        billParam.setIds(payDetails.stream().map(PcxBillPayDetail::getBillId).collect(Collectors.toList()));
        billParam.setOtherBillIds(otherBillIds);
        billParam.setAgyCode(qo.getAgyCode());
        billParam.setFiscal(qo.getFiscal());
        billParam.setMofDivCode(qo.getMofDivCode());
        billParam.setKeyword(qo.getKeyword());
        billParam.setDepartmentCodeList(qo.getDepartmentCodeList());
        billParam.setBillFuncCodeList(qo.getBillFuncCodeList());
        billParam.setSettlementTypeList(qo.getSettlementTypeList());
        billParam.setPageSize(qo.getPageSize());
        billParam.setPageIndex(qo.getPageIndex());
        billParam.setSureStartDate(qo.getSureStartDate());
        billParam.setSureEndDate(qo.getSureEndDate());
        billParam.setFinalAuditStartDate(qo.getFinalAuditStartDate());
        billParam.setFinalAuditEndDate(qo.getFinalAuditEndDate());
        billParam.setPayStartDate(qo.getPayStartDate());
        billParam.setPayEndDate(qo.getPayEndDate());
        billParam.setBillStatus(qo.getBillStatus());
        // 查询与支付详情相关的主单据信息
        Page<PcxBill> pcxBills = billMainService.selectPayerParamIndexPage(billParam);

        if (CollectionUtils.isEmpty(pcxBills.getRecords())) {
            return CheckMsg.success(new PageResult<>());
        }

        // 构建支付详情的映射关系，以便后续处理
        Map<String, List<PcxBillPayDetail>> payDetailRel = payDetails.stream().collect(Collectors.groupingBy(PcxBillPayDetail::getBillId));
        // 查询与支付详情相关的预算指标信息
        List<BudBalanceDTO> balanceDtoList = balanceExternalService.getBalanceByIds(payDetails.stream()
                .map(PcxBillPayDetail::getBalanceId).collect(Collectors.toList()));
        // 查询与支付详情相关的结算信息
        Map<String, BudBalanceDTO> balanceRel = balanceDtoList.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity(), (a, b) -> a));

        // 过滤筛选后的明细
        List<String> billIds = pcxBills.getRecords().stream().map(PcxBill::getId).collect(Collectors.toList());
        payDetails = payDetails.stream().filter(payDetail -> billIds.contains(payDetail.getBillId())).collect(Collectors.toList());

        List result = new ArrayList<>();
        long totalRecord = payDetails.size();
        // 根据单据支付详情状态，执行不同的逻辑
        switch (status) {
            // 待支付
            case PAY_PREPARE:
                // 遍历主单据列表，根据单据支付详情状态，创建并添加相应的单据支付详情对象到结果列表
                pcxBills.getRecords().forEach(bill -> {
                    List<PcxBillPayDetail> payDetailList = payDetailRel.getOrDefault(bill.getId(), Collections.emptyList());
                    // 初始化结果列表
                    result.add(new PcxBillPayDetailForPayerVO(bill, payDetailList, balanceRel));
                });
                totalRecord = pcxBills.getTotal();
                break;
            // 在途
            case PAY_WAY:
                payDetails.forEach(payDetail -> {
                    PcxBill bill = pcxBills.getRecords().stream().filter(b -> b.getId().equals(payDetail.getBillId())).collect(Collectors.toList()).get(0);
                    result.add(new PcxBillPayDetailPayerWayVO(bill, payDetail, balanceRel.get(payDetail.getBalanceId())));
                });
                break;
            // 支付异常
            case PAY_EXCEPTION:
                payDetails.forEach(payDetail -> {
                    PcxBill bill = pcxBills.getRecords().stream().filter(b -> b.getId().equals(payDetail.getBillId())).collect(Collectors.toList()).get(0);
                    PcxBillPayDetailPayerWayVO pcxBillPayDetailPayerWayVO = new PcxBillPayDetailPayerWayVO(bill, payDetail, balanceRel.get(payDetail.getBalanceId()));
                    PcxBillStatusHistory pcxBillStatusHistory = pcxBillStatusHistoryPlusService.getOne(new LambdaQueryWrapper<PcxBillStatusHistory>()
                            .eq(StringUtils.isNotBlank(payDetail.getPayNo()), PcxBillStatusHistory::getBillId, payDetail.getPayNo())
                            .eq(PcxBillStatusHistory::getOpType, BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_ERROR.getCode())
                            .eq(PcxBillStatusHistory::getBillType, BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                            .orderBy(true, false, PcxBillStatusHistory::getCreatedTime)
                            // 获取最新的一条支付异常记录
                            .last("limit 1")
                    );
                    pcxBillPayDetailPayerWayVO.setReason(pcxBillStatusHistory.getRemark());
                    result.add(pcxBillPayDetailPayerWayVO);
                });
                break;
            // 支付完成
            case PAY_COMPLETE:
                payDetails.forEach(payDetail -> {
                    PcxBill bill = pcxBills.getRecords().stream().filter(b -> b.getId().equals(payDetail.getBillId())).collect(Collectors.toList()).get(0);
                    PcxBillPayDetailPayerWayVO pcxBillPayDetailPayerWayVO = new PcxBillPayDetailPayerWayVO(bill, payDetail, balanceRel.get(payDetail.getBalanceId()));
                    pcxBillPayDetailPayerWayVO.setBankReceipt(payDetail.getBankReceipt());
                    result.add(pcxBillPayDetailPayerWayVO);
                });
                break;
            default:
                break;
        }

        // 分页转换
        Page resultPage = new Page<>(qo.getPageIndex(), qo.getPageSize(), totalRecord).setRecords(result);
        // 返回包含单据支付详情列表的成功消息
        return CheckMsg.success(PageTransformer.INSTANCE.plus2result(resultPage));
    }

    /**
     * 发起变更时，需要分别触发如下动作：
     * 1.若当前单据存在关联的付款申请单，则需要将支付状态为【异常】的付款申请单作废；
     * 2.变更内容为收款账号时，需要将所选收款账号关联的【待支付】的付款申请单作废；
     * 3.变更内容为预算指标时，需要将所选预算指标关联的【待支付】的付款申请单作废；
     * 4.变更内容为事由时，需要将所有【待支付】的付款申请单作废；
     *
     * @param billId
     * @param changeType
     * @return
     */
    @Override
    @Transactional
    public CheckMsg<List<String>> disable(String billId, BillChangeTypeEnum changeType) {
        PcxBillPayDetailQO detail = new PcxBillPayDetailQO();
        detail.setBillId(billId);
        detail.setStatusList(Arrays.asList(CONFIRM.getCode(),
                CONFIRMED.getCode(),
                PAY_ERROR.getCode()));
        List<PcxBillPayDetail> payDetails = this.pcxBillPayDetailDao.selectList(this.getQueryWrapper(detail));
        if (CollectionUtils.isNotEmpty(payDetails)) {
            List<String> ids = new ArrayList<>();
            switch (changeType) {
                case BASIC_INFO:
                case SETTLEMENT_TYPE:
                case BUDGET_INDEX:
                    ids = disableByDetailIds(payDetails.stream().filter(_detail -> _detail.getPayStatus().equals(CONFIRMED.getCode()))
                            .map(PcxBillPayDetail::getId).collect(Collectors.toList()));
                    break;
                default:
                    break;
            }
            return CheckMsg.success(ids);
        }
        return CheckMsg.success(Collections.emptyList());
    }

    @Override
    public CheckMsg<PcxBillPayDetailSelVO> selectDetail(PcxBillPayDetailSelQO qo) {
        PcxBillPayDetailQO detailQO = new PcxBillPayDetailQO();
        BeanUtil.copyProperties(qo, detailQO);
        CheckMsg<List<PcxBillPayDetailVO>> listCheckMsg = this.selectList(detailQO);
        if (!listCheckMsg.isSuccess()) {
            return CheckMsg.fail(listCheckMsg.getMsgInfo());
        }
        if (CollectionUtils.isEmpty(listCheckMsg.getData())) {
            return CheckMsg.fail(String.format("单据{%s}--支付明细{%s}不存在", qo.getBillId(), qo.getPayNo()));
        }
        PcxBillPayDetailVO pcxBillPayDetailVO = listCheckMsg.getData().get(0);
        PcxBillPayDetailSelVO pcxBillPayDetailSelVO = new PcxBillPayDetailSelVO();
        BeanUtil.copyProperties(pcxBillPayDetailVO, pcxBillPayDetailSelVO);
        pcxBillPayDetailSelVO.setPayTypeCode(BillPayTypeEnum.BANK_TRANSFER.getCode());
        pcxBillPayDetailSelVO.setPayTypeName(BillPayTypeEnum.BANK_TRANSFER.getName());
        pcxBillPayDetailSelVO.setPayDate(pcxBillPayDetailVO.getPayingTime());
        return CheckMsg.success(pcxBillPayDetailSelVO);
    }

    @Override
    @Transactional
    public CheckMsg<Void> confirm(PcxBillPayDetailQO qo) {
        List<PcxBillPayDetail> details = pcxBillPayDetailDao.selectList(new LambdaQueryWrapper<PcxBillPayDetail>()
                .in(PcxBillPayDetail::getBillId, qo.getBillIdList())).stream().filter(detail -> CONFIRM.getCode().equals(detail.getPayStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(details))
            return CheckMsg.success();

        details.forEach(detail -> {
            Integer current = detail.getVer();
            detail.setConfirmTime(DateUtil.now());
            detail.setPayStatus(CONFIRMED.getCode());
            detail.setModifier(PtyContext.getUsername());
            detail.setModifierName(PtyContext.getUsernameCn());
            detail.setModifiedTime(DateUtil.now());
            detail.setVer(current + 1);
            detail.setPayNo(pcxBillNoService.getBillNo(detail.getMofDivCode(), detail.getAgyCode(), PAY_DETAIL.getCode(), null));
            boolean updated = this.update(detail, new LambdaQueryWrapper<PcxBillPayDetail>()
                    .eq(PcxBillPayDetail::getId, detail.getId())
                    .eq(PcxBillPayDetail::getVer, current));
            Assert.state(updated, "支付明细{}并发修改异常", detail.getId());
        });

        List<PcxBillStatusHistory> histories = new ArrayList<>();
        details.forEach(detail -> {
            histories.add(PcxBillStatusHistory
                    .builder()
                    .id(IDGenerator.id())
                    .billId(detail.getPayNo())
                    .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                    .agyCode(detail.getAgyCode())
                    .mofDivCode(detail.getMofDivCode())
                    .opTime(new Date())
                    .fiscal(detail.getFiscal())
                    .fromStatus(CONFIRM.getCode())
                    .toStatus(CONFIRMED.getCode())
                    .opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_CONFIRM.getCode())
                    .creator(PtyContext.getUsername())
                    .creatorName(PtyContext.getUsernameCn())
                    .createdTime(DateUtil.now())
                    .build());
        });
        pcxBillStatusHistoryPlusService.saveBatch(histories);
        return CheckMsg.success();
    }

    @Override
    @Transactional
    @Deprecated
    public CheckMsg<Void> saveDetail(PcxBillPaySettlementBalanceQO qo, PcxBill pcxBill) {
        List<PcxBillPayDetail> details = new ArrayList<>();

        if (Objects.nonNull(qo)) {

//            PaOptionQO optionQO = new PaOptionQO();
//            optionQO.setOptCode(FundPositionEnum.ENABLE_BALANCE.getCode());
//            optionQO.setAgyCode(pcxBill.getAgyCode());
//            optionQO.setFiscal(Integer.parseInt(pcxBill.getFiscal()));
//            optionQO.setMofDivCode(pcxBill.getMofDivCode());
//            Response<?> response = businessRuleOptionService.selectByQO(optionQO);
//
//            Assert.state(response.isSuccess(), "获取指标是否启用失败");
//            PaOption data = (PaOption) response.getData();
//            Assert.state(Objects.nonNull(data), "单位指标是否启用未配置,请前往业务规则设置操作后重试");
//            boolean disableBalance = data.getOptValue().equals("0");
            // 如果启用了指标, 则支付明细对应指标不能全空
//            Assert.state(disableBalance ||
//                    (qo.getSettle_transfer().stream().map(PcxBillPaySettlementBalanceQO.SettlementExtQO::getBalanceList).flatMap(Collection::stream).findAny().isPresent()
//                            || qo.getSettle_busicard().stream().map(PcxBillPaySettlementBalanceQO.SettlementExtQO::getBalanceList).flatMap(Collection::stream).findAny().isPresent()
//                            || qo.getSettle_cash().stream().map(PcxBillPaySettlementBalanceQO.SettlementExtQO::getBalanceList).flatMap(Collection::stream).findAny().isPresent()
//                            || qo.getSettle_cheque().stream().map(PcxBillPaySettlementBalanceQO.SettlementExtQO::getBalanceList).flatMap(Collection::stream).findAny().isPresent()
//                            || qo.getSettle_loan().stream().map(PcxBillPaySettlementBalanceQO.SettlementExtQO::getBalanceList).flatMap(Collection::stream).findAny().isPresent())
//                    , "指标启用,支付明细指标参数均为空");

            List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(pcxBill.getId()));

            List<PcxBillPaySettlementBalanceQO.SettlementExtQO> settlementExtVOS = new ArrayList<>();
            settlementExtVOS.addAll(qo.getSettle_transfer());
            settlementExtVOS.addAll(qo.getSettle_busicard());
            boolean allHasPayAccountNo = settlementExtVOS.stream().flatMap(settlementExt -> settlementExt.getBalanceList().stream()).allMatch(balance -> StringUtils.isNotBlank(balance.getPayAccountNo()));
            Assert.state(allHasPayAccountNo, "指标对应付款账号有空值");
            settlementExtVOS.addAll(qo.getSettle_cheque());
            settlementExtVOS.addAll(qo.getSettle_cash());

            List<PcxBillPaySettlementBalanceQO.BalanceExtQO> balanceList = settlementExtVOS.stream().map(PcxBillPaySettlementBalanceQO.SettlementExtQO::getBalanceList).flatMap(Collection::stream).collect(Collectors.toList());
            balanceEnhance(balanceList, pcxBill);
            settlementExtVOS.addAll(qo.getSettle_loan());

            settlementExtVOS.forEach(settlement -> {
                Optional<PcxBillSettlement> opSettlement = settlements.stream().filter(settlementExt -> settlementExt.getSettlementType().equals(settlement.getSettlementType()) &&
                        StringUtil.getStringValue(settlementExt.getPayeeAccNo()).equals(StringUtil.getStringValue(settlement.getPayeeAccNo()))).findFirst();
                if (opSettlement.isPresent()) {
                    PcxBillSettlement dbSettlement = opSettlement.get();
                    settlement.setId(dbSettlement.getId());
                    settlement.getBalanceList().forEach(balance -> {
//                    MadAgyAccountDTO account = getMadAgyAccountDTO(Boolean.FALSE, pcxBill);
                        settlementEnhance(pcxBill, settlement, Boolean.TRUE);
                        detailGenerate(settlement, balance, BigDecimal.valueOf(Long.MAX_VALUE), details, pcxBill, Boolean.TRUE);
                    });
                    if (settlement.getCheckAmt() == null || settlement.getCheckAmt().compareTo(BigDecimal.ZERO) == 0) {
                        settlement.setCheckAmt(settlement.getInputAmt());
                    }
                } else {
                    settlement.getBalanceList().forEach(balance -> {
                        detailGenerateLoan(pcxBill, balance, details);
                    });
                    log.error("支付明细=>支付结算信息不存在bill {}, settlement type {}, payee account no {}", pcxBill.getId(), settlement.getSettlementType(), settlement.getPayeeAccNo());
                }
            });
            if (CollectionUtils.isNotEmpty(settlementExtVOS)) {
                pcxBillSettlementInfoService.batchUpdateByUk(settlementExtVOS);
            }
        }

        LambdaUpdateWrapper<PcxBillPayDetail> loanUpdateWrapper = new LambdaUpdateWrapper<PcxBillPayDetail>()
                .set(PcxBillPayDetail::getBillId, pcxBill.getId())
                .set(PcxBillPayDetail::getModifier, PtyContext.getUsername())
                .set(PcxBillPayDetail::getModifiedTime, DateUtil.now())
                .set(PcxBillPayDetail::getPayStatus, STASH.getCode())
                .eq(PcxBillPayDetail::getBillId, pcxBill.getId())
                .eq(PcxBillPayDetail::getSettlementType, SETTLE_LOAN)
                .eq(PcxBillPayDetail::getIsDeleted, 0);

        String[] expenseCodes = StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",");
        if (expenseCodes.length == 1) {
            details.forEach(detail -> {
                detail.setExpenseCode(StrUtil.firstNonBlank(detail.getExpenseCode(), expenseCodes[0]));
            });
            loanUpdateWrapper.set(PcxBillPayDetail::getExpenseCode, expenseCodes[0]);
        }

        // 将qos中的所有结算信息聚合到一个集合中
        Assert.isTrue(details.stream()
                .noneMatch(detail -> BillFuncCodeEnum.EXPENSE.getCode().equals(pcxBill.getBillFuncCode()) && StringUtils.isBlank(detail.getExpenseCode())), "存在未细化费用类型的指标");

        // 删除单据下所有待确认的
        this.remove(new LambdaQueryWrapper<PcxBillPayDetail>()
                .eq(PcxBillPayDetail::getBillId, pcxBill.getId())
                .in(PcxBillPayDetail::getPayStatus, Arrays.asList(CONFIRM.getCode(), STASH.getCode(), STASH.getCode())));
        log.info("支付明细=>新增支付明细: {}", JSON.toJSONString(details));
        details.forEach(detail -> {
            detail.setPayStatus(STASH.getCode());
            // 唯一键冲突进行更新, 否则新增
            this.saveOrUpdate(detail, new LambdaQueryWrapper<PcxBillPayDetail>()
                    .eq(PcxBillPayDetail::getBillId, detail.getBillId())
                    .eq(PcxBillPayDetail::getExpenseCode, detail.getExpenseCode())
                    .eq(PcxBillPayDetail::getBalanceId, detail.getBalanceId())
                    .eq(PcxBillPayDetail::getSettlementUk, detail.getSettlementUk()));
        });
        this.update(loanUpdateWrapper);
        return CheckMsg.success();
    }

    /**
     * 结算增强处理方法
     * 该方法用于在结算过程中，根据账户信息和结算类型，增强结算对象的详细信息
     * 主要包括银行名称、银行代码、城市代码等信息的设置
     *
     * @param pcxBill PcxBill对象，代表某个账单
     * @param settlement Settlement对象，包含结算相关信息
     * @param mustSatisfy 一个布尔值，表示是否必须满足条件
     */
    private void settlementEnhance(PcxBill pcxBill, PcxBillPaySettlementBalanceQO.SettlementExtQO settlement, Boolean mustSatisfy) {
//        // 设置付款银行名称
//        settlement.setPayBankName(account.getBankName());
//        // 设置付款银行代码
//        settlement.setPayBankCode(account.getBankCode());
//        // 设置付款账户所在城市代码
//        settlement.setPayAccCity(account.getCityCode());
        // 当结算类型不是现金结算也不是支票结算时，执行以下逻辑
        if (!settlement.getSettlementType().equals(SettlementTypeEnum.SETTLE_CASH.getCode()) &&
                !settlement.getSettlementType().equals(SettlementTypeEnum.SETTLE_CHEQUE.getCode())) {

            // 获取MadBankNodeDTO对象，用于获取银行节点相关信息
            MadBankNodeDTO madBankNodeDTO = getMadBankNodeDTO(settlement, pcxBill);
            // 断言结算账户存在，如果mustSatisfy为真且madBankNodeDTO为空，则抛出异常
//            Assert.state(!mustSatisfy || Objects.nonNull(madBankNodeDTO), "收款账户{}不存在", settlement.getPayeeAccNo());

            // 使用默认的MadBankNodeDTO对象，以备madBankNodeDTO为空时使用
            MadBankNodeDTO _default = new MadBankNodeDTO();
            // 设置收款账户代码，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeAccCode(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getBankNodeNo());
            // 设置收款银行代码，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeBankCode(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getBankCode());
            // 设置收款银行名称，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeBankName(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getBankName());
            // 设置收款银行节点代码，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeBankNodeCode(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getBankNodeCode());
            // 设置收款银行节点名称，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeBankNodeName(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getBankNodeName());
            // 设置收款账户所在城市代码，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeAccCity(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getCityCode());
            // 设置收款银行节点代码，如果madBankNodeDTO为空，则使用默认值
            settlement.setPayeeBankNodeNo(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getBankNodeNo());
            settlement.setPayeeAccountType(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getAccountType());
            settlement.setPayeeAccountTypeName(ObjectUtils.firstNonNull(madBankNodeDTO, _default).getAccountTypeName());
            settlement.setSettlementTypeName(SettlementTypeEnum.getNameByCode(settlement.getSettlementType()));
        }
        log.info("支付明细=>enhance settlement: {}", JSON.toJSONString(settlement));
    }

    @Override
    @Transactional
    @Deprecated
    public CheckMsg<Void> batchSaveDetail(PcxBillPaySettlementBalanceQO qos, PcxBill pcxBill) {
        //
        if (Objects.nonNull(qos) && !pcxBill.getBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode())) {
            this.saveDetail(qos, pcxBill);
        }
        return CheckMsg.success();
    }

    /**
     * bill detail支付
     *
     * @param pcxBillPayQO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CheckMsg<?> normalPay(PcxBillPayQO pcxBillPayQO) {

        // 检查支付密码
        CheckMsg<?> checkMsg = epPayBillExternalService.checkEpPayPwd(pcxBillPayQO);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }

        List<EpPayBill> payBillList = new ArrayList<>();

        Map<String, List<PcxBillPayItemQO>> billId$payItem = pcxBillPayQO.getPayItemList().stream().collect(Collectors.groupingBy(PcxBillPayItemQO::getBillId));
        billId$payItem.forEach((billId, payItemList) -> {
            // 主单据信息
            EpPayBill payBill = new EpPayBill();
            payBill.setBillId(StringUtil.getUUID());
            payBill.setFromId(billId);
            payBill.setAgyCode(pcxBillPayQO.getAgyCode());
            payBill.setFiscal(pcxBillPayQO.getFiscal());
            payBill.setMofDivCode(pcxBillPayQO.getMofDivCode());
            // 暂时用uuid
            payBill.setFromNo("BNO" + billId);
            payBill.setCreator(PtyContext.getUsername());
            payBill.setCreatorName(String.valueOf(PtyContext.getValue(PtyContext.CURRENT_USER_NAME)));
            payBill.setInputTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            payBill.setFromModule("PCX");
            payBill.setTransType("2001");

            List<EpPayBillDetail> billDetailList = new ArrayList();
            payItemList.forEach(payItem -> {
                // 子单据信息
                EpPayBillDetail payBillDetail = new EpPayBillDetail();
                payBillDetail.setBillId(payBill.getBillId());
                payBillDetail.setRefSetId(payItem.getPayNo());
                billDetailList.add(payBillDetail);
            });
            payBill.setBillDetailList(billDetailList);
            payBillList.add(payBill);
        });

        List<EpPayBill> donePayBillList = new ArrayList<>();
        List<String> successList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        List<String> exceptList = new ArrayList<>();

        // 查询当前单据状态、组装支付数据
        for (EpPayBill epPayBill : payBillList) {
            PcxBillPayDetail billPayDetail = null;
            EpPayBill doneEpPayBill = new EpPayBill();
            BeanUtils.copyProperties(epPayBill, doneEpPayBill);
            doneEpPayBill.setBillDetailList(new ArrayList<>());
            for (int i = 0;i<epPayBill.getBillDetailList().size();i++) {
                EpPayBillDetail epPayBillDetail = epPayBill.getBillDetailList().get(i);
                billPayDetail = getOne(
                        new LambdaQueryWrapper<PcxBillPayDetail>()
                                .eq(StringUtils.isNoneBlank(epPayBill.getFromId()), PcxBillPayDetail::getBillId, epPayBill.getFromId())
                                .eq(StringUtils.isNoneBlank(epPayBillDetail.getRefSetId()), PcxBillPayDetail::getPayNo, epPayBillDetail.getRefSetId())
                                .eq(StringUtils.isNoneBlank(epPayBill.getAgyCode()), PcxBillPayDetail::getAgyCode, epPayBill.getAgyCode())
                                .eq(StringUtils.isNoneBlank(epPayBill.getFiscal()), PcxBillPayDetail::getFiscal, epPayBill.getFiscal())
                                .eq(StringUtils.isNoneBlank(epPayBill.getMofDivCode()), PcxBillPayDetail::getMofDivCode, epPayBill.getMofDivCode()));

                checkMsg = checkPayDetail(epPayBill.getFromId(), epPayBillDetail.getRefSetId(), billPayDetail);
                if (!checkMsg.isSuccess()) {
                    return checkMsg;
                }

                // 现金、支票支付直接成功
                if (billPayDetail.getSettlementType().equals(SettlementTypeEnum.SETTLE_CASH.getCode()) || billPayDetail.getSettlementType().equals(SettlementTypeEnum.SETTLE_CHEQUE.getCode())){
                    cashPaySuccess(pcxBillPayQO, billPayDetail);
                    successList.add("1");
                    continue;
                }

                doneEpPayBill.getBillDetailList().add(epPayBillDetail);

                // 填充交易信息

                if(getPaPayStatus("BILL_PAY_TEST_STATUS").equals("1")){
                    // 测试数据
                    PcxBankAccountTest agyTestAccount = new PcxBankAccountTest();
                    List<PcxBankAccountTest> userTestAccountList = new ArrayList<>();

                    getTestAccount(pcxBillPayQO, agyTestAccount, userTestAccountList, i>1);
                    epPayBill.setRemark("this is a billid test remark");
                    epPayBill.setPayBankCode(agyTestAccount.getBankCode());
                    epPayBill.setPayAccNo(agyTestAccount.getAccountNo());
                    epPayBill.setPayAccName(agyTestAccount.getAccountName());

                    PcxBankAccountTest userTestAccount = i > 0 ? userTestAccountList.get(1):userTestAccountList.get(0);
                    epPayBillDetail.setRemark("this is a payno test remark");
                    epPayBillDetail.setPayAccNo(agyTestAccount.getAccountNo());
                    epPayBillDetail.setPayAccName(agyTestAccount.getAccountName());
                    epPayBillDetail.setPayBankName(agyTestAccount.getBankName());
                    epPayBillDetail.setPayBankCode(agyTestAccount.getBankCode());
                    epPayBillDetail.setRecAccNo(userTestAccount.getAccountNo());
                    epPayBillDetail.setRecAccName(userTestAccount.getAccountName());
                    epPayBillDetail.setRecBankName(userTestAccount.getBankName());
                    epPayBillDetail.setRecBankNodeName(userTestAccount.getBankNodeName());
                    epPayBillDetail.setRecBankNodeNo(userTestAccount.getBankNodeNo());
                    epPayBillDetail.setRecBankCode(userTestAccount.getBankCode());
                    epPayBillDetail.setRecAccCity(userTestAccount.getCityName());
                    if (StringUtil.isNotEmpty(billPayDetail.getPayBankCode()) && StringUtil.isNotEmpty(billPayDetail.getPayeeBankCode())) {
                        epPayBillDetail.setIsSameBank(String.valueOf(epPayBillDetail.getPayBankCode().equals(epPayBillDetail.getRecBankCode()) ? 1 : 0));
                    } else {
                        epPayBillDetail.setIsSameBank("0");
                    }
                    epPayBillDetail.setAmt(new BigDecimal(0.1));
                    epPayBillDetail.setPayType(userTestAccount.getCardType());
                }else{
                    // 正式数据
                    epPayBillDetail.setPayAccNo(billPayDetail.getPayAccountNo());
                    epPayBillDetail.setPayAccName(billPayDetail.getPayAccountName());
                    epPayBillDetail.setPayBankName(billPayDetail.getPayBankName());
                    epPayBillDetail.setPayBankCode(billPayDetail.getPayBankCode());
                    epPayBillDetail.setPayAccCity(billPayDetail.getPayAccountCity());
                    epPayBillDetail.setRecAccNo(billPayDetail.getPayeeAccountNo());
                    epPayBillDetail.setRecAccName(billPayDetail.getPayeeAccountName());
                    epPayBillDetail.setRecBankName(billPayDetail.getPayeeBankName());
                    epPayBillDetail.setRecBankNodeName(billPayDetail.getPayeeBankNodeName());
                    epPayBillDetail.setRecBankNodeNo(billPayDetail.getPayeeBankNodeNo());
                    epPayBillDetail.setRecAccCity(billPayDetail.getPayeeAccountCity());
                    epPayBillDetail.setRecBankCode(billPayDetail.getPayeeBankCode());
                    epPayBillDetail.setPayType(billPayDetail.getPayType());
                    if (StringUtil.isNotEmpty(billPayDetail.getPayAccountCity()) && StringUtil.isNotEmpty(billPayDetail.getPayeeAccountCity())) {
                        epPayBillDetail.setIsSameCity(String.valueOf(billPayDetail.getPayAccountCity().equals(billPayDetail.getPayeeAccountCity()) ? 1 : 0));
                    } else {
                        epPayBillDetail.setIsSameCity("0");
                    }
                    if (StringUtil.isNotEmpty(billPayDetail.getPayBankCode()) && StringUtil.isNotEmpty(billPayDetail.getPayeeBankCode())) {
                        epPayBillDetail.setIsSameBank(String.valueOf(billPayDetail.getPayBankCode().equals(billPayDetail.getPayeeBankCode()) ? 1 : 0));
                    } else {
                        epPayBillDetail.setIsSameBank("0");
                    }
                    epPayBillDetail.setAmt(billPayDetail.getCheckAmt());
                    epPayBillDetail.setRemark("this is a payno remark");

                    epPayBill.setPayAccNo(billPayDetail.getPayAccountNo());
                    epPayBill.setPayAccName(billPayDetail.getPayAccountName());
                    epPayBill.setPayBankCode(billPayDetail.getPayBankCode());
                    epPayBill.setRemark("this is a billid remark");
                }

                epPayBill.setAgyName(billPayDetail.getAgyName());
                epPayBill.setBillCount(epPayBill.getBillDetailList().size());
                epPayBill.setBillAmt(epPayBill.getBillDetailList().stream().map(EpPayBillDetail::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                epPayBill.setSettlementType(SettlementTypeEnum.getBKSettlementByCode(billPayDetail.getSettlementType()));

                if(getPaPayStatus("BILL_PAY_MONEY_TEST_STATUS").equals("1")){
                    // 测试金额1.68
                    epPayBillDetail.setAmt(new BigDecimal(1.68));
                }
            }

            if(CollectionUtils.isNotEmpty(doneEpPayBill.getBillDetailList())){
                donePayBillList.add(doneEpPayBill);
            }

        }

        if(CollectionUtils.isNotEmpty(donePayBillList)){
            // 发送并批量支付
            checkMsg = epPayBillExternalService.startEpPay(donePayBillList);
            log.info("发送并支付" + JSON.toJSONString(checkMsg.getData()));
            log.info("发送并支付" + checkMsg.getMsgInfo());

            // 发送返回数据
            List<EpPayBill> epPayBills = JSONArray.parseArray(JSON.toJSONString(checkMsg.getData()), EpPayBill.class);

            // 修改单据流转状态
            for (EpPayBill epPayBill : epPayBills) {
                for (EpPayBillDetail epPayBillDetail : epPayBill.getBillDetailList()){
                    // 支付后处理
                    try {
                        doAfterPay(epPayBill, epPayBillDetail);
                    } catch (Exception e) {
                        log.info("支付后处理（不回滚）" , e);
                    }
                }
            }
        }

        // 存在支付异常
        if(!errorList.isEmpty()){
            return CheckMsg.fail(String.format("%d笔支付成功，%d笔支付失败",successList.size(),errorList.size()));
        }

        // 全部支付成功
        return CheckMsg.success();
    }

    /**
     * 获取Pa支付参数测试状态
     * @return
     */
    private String getPaPayStatus(String optCode){
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setAgyCode("*");
        paOptionQO.setFiscal(Integer.parseInt(com.pty.pub.common.util.DateUtil.getYear(com.pty.pub.common.util.DateUtil.nowDate())));
        paOptionQO.setMofDivCode("87");
        paOptionQO.setSysId("PCX");
        paOptionQO.setOptCode(optCode);
        PaOption paOption = (PaOption) businessRuleOptionService.selectByQO(paOptionQO).getData();
        return paOption.getOptValue();
    }

    /**
     * 校验支付单状态
     * @param billId
     * @param payNo
     * @param billPayDetail
     * @return
     */
    CheckMsg checkPayDetail(String billId, String payNo, PcxBillPayDetail billPayDetail) {
        if (billPayDetail == null) {
            log.error(String.format("来源单据{%s}不存在支付单{%s}", billId, payNo));
            return CheckMsg.fail(String.format("来源单据{%s}不存在支付单{%s}", billId, payNo));
        }
        if (billPayDetail.getPayStatus().equals(PAYING.getCode())) {
            log.error(String.format("支付单{%s}正在支付中，请勿重复支付", payNo));
            return CheckMsg.fail(String.format("支付单{%s}正在支付中，请勿重复支付", payNo));
        }
        if (billPayDetail.getPayStatus().equals(PAID.getCode())) {
            log.error(String.format("支付单{%s}已支付，无需再次支付", payNo));
            return CheckMsg.fail(String.format("支付单{%s}已支付，无需再次支付", payNo));
        }
        if(!billPayDetail.getPayStatus().equals(CONFIRMED.getCode())){
            log.error(String.format("单据状态异常{%s}",billPayDetail.getPayStatus()));
            return CheckMsg.fail(String.format("单据状态异常{%s}",billPayDetail.getPayStatus()));
        }

        return CheckMsg.success();
    }

    void cashPaySuccess(PcxBillPayQO pcxBillPayQO, PcxBillPayDetail billPayDetail){
        // 新增支付完成单据流转状态
        PcxBillStatusHistory billStatusHistory = PcxBillStatusHistory.builder()
                .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                .billId(billPayDetail.getPayNo())
                .opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_SUCCESS.getCode())
                .opTime(new Date())
                .fromStatus(billPayDetail.getPayStatus())
                .toStatus(PAID.getCode())
                .remark("")
                .creator(pcxBillPayQO.getUserCode())
                .creatorName(pcxBillPayQO.getUserName())
                .createdTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                .agyCode(pcxBillPayQO.getAgyCode())
                .fiscal(pcxBillPayQO.getFiscal())
                .mofDivCode(pcxBillPayQO.getMofDivCode())
                .build();
        billPayDetail.setPayStatus(PAID.getCode());
        billPayDetail.setPayingTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        billPayDetail.setPaidTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        // 修改单据状态
        operationAfterPay(billPayDetail, billStatusHistory);
    }

    /**
     * bill detail平铺支付
     *
     * @param pcxBillPayQO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CheckMsg<?> normalFlatPay(PcxBillPayQO pcxBillPayQO) {

        // 检查支付密码
        CheckMsg<?> checkMsg = epPayBillExternalService.checkEpPayPwd(pcxBillPayQO);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }

        List<EpPayBill> payBillList = new ArrayList<>();
        for (int i = 0; i<pcxBillPayQO.getPayItemList().size(); i++) {
            PcxBillPayItemQO payItem = pcxBillPayQO.getPayItemList().get(i);
            PcxBillPayDetail billPayDetail = getOne(
                    new LambdaQueryWrapper<PcxBillPayDetail>()
                            .eq(StringUtils.isNoneBlank(payItem.getBillId()), PcxBillPayDetail::getBillId, payItem.getBillId())
                            .eq(StringUtils.isNoneBlank(payItem.getPayNo()), PcxBillPayDetail::getPayNo, payItem.getPayNo())
                            .eq(StringUtils.isNoneBlank(pcxBillPayQO.getAgyCode()), PcxBillPayDetail::getAgyCode, pcxBillPayQO.getAgyCode())
                            .eq(StringUtils.isNoneBlank(pcxBillPayQO.getFiscal()), PcxBillPayDetail::getFiscal, pcxBillPayQO.getFiscal())
                            .eq(StringUtils.isNoneBlank(pcxBillPayQO.getMofDivCode()), PcxBillPayDetail::getMofDivCode, pcxBillPayQO.getMofDivCode()));

            checkMsg = checkPayDetail(payItem.getBillId(), payItem.getPayNo(), billPayDetail);
            if (!checkMsg.isSuccess()) {
                return checkMsg;
            }

            // 现金、支票支付直接成功
            if (billPayDetail.getSettlementType().equals(SettlementTypeEnum.SETTLE_CASH.getCode()) || billPayDetail.getSettlementType().equals(SettlementTypeEnum.SETTLE_CHEQUE.getCode())){
                cashPaySuccess(pcxBillPayQO, billPayDetail);
                continue;
            }

            // 主单据信息
            EpPayBill payBill = new EpPayBill();
            payBill.setBillId(StringUtil.getUUID());
            payBill.setFromId(payItem.getBillId());
            // 暂时用uuid
            payBill.setFromNo("BNO"+payItem.getBillId());
            payBill.setAgyCode(pcxBillPayQO.getAgyCode());
            payBill.setFiscal(pcxBillPayQO.getFiscal());
            payBill.setMofDivCode(pcxBillPayQO.getMofDivCode());
            payBill.setAgyName(billPayDetail.getAgyName());
            payBill.setSettlementType(SettlementTypeEnum.getBKSettlementByCode(billPayDetail.getSettlementType()));
            payBill.setCreator(PtyContext.getUsername());
            payBill.setCreatorName(String.valueOf(PtyContext.getValue(PtyContext.CURRENT_USER_NAME)));
            payBill.setInputTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            payBill.setFromModule("PCX");
            payBill.setTransType("2001");
            List<EpPayBillDetail> billDetailList = new ArrayList();
            // 子单据信息
            EpPayBillDetail payBillDetail = new EpPayBillDetail();
            payBillDetail.setBillId(payBill.getBillId());
            payBillDetail.setRefSetId(payItem.getPayNo());
            billDetailList.add(payBillDetail);
            payBill.setBillDetailList(billDetailList);
            payBillList.add(payBill);

            // 填充交易信息
            if(getPaPayStatus("BILL_PAY_TEST_STATUS").equals("1")){
                // 测试数据
                PcxBankAccountTest agyTestAccount = new PcxBankAccountTest();
                List<PcxBankAccountTest> userTestAccountList = new ArrayList<>();

                getTestAccount(pcxBillPayQO, agyTestAccount, userTestAccountList, i>1);
                payBill.setRemark("this is a billid test remark");
                payBill.setPayBankCode(agyTestAccount.getBankCode());
                payBill.setPayAccNo(agyTestAccount.getAccountNo());
                payBill.setPayAccName(agyTestAccount.getAccountName());

                PcxBankAccountTest userTestAccount = userTestAccountList.get(i);
                payBillDetail.setRemark("this is a payno test remark");
                payBillDetail.setPayAccNo(agyTestAccount.getAccountNo());
                payBillDetail.setPayAccName(agyTestAccount.getAccountName());
                payBillDetail.setPayBankName(agyTestAccount.getBankName());
                payBillDetail.setPayBankCode(agyTestAccount.getBankCode());
                payBillDetail.setRecAccNo(userTestAccount.getAccountNo());
                payBillDetail.setRecAccName(userTestAccount.getAccountName());
                payBillDetail.setRecBankName(userTestAccount.getBankName());
                payBillDetail.setRecBankNodeName(userTestAccount.getBankNodeName());
                payBillDetail.setRecBankNodeNo(userTestAccount.getBankNodeNo());
                payBillDetail.setRecBankCode(userTestAccount.getBankCode());
                payBillDetail.setRecAccCity(userTestAccount.getCityName());
                if (StringUtil.isNotEmpty(billPayDetail.getPayBankCode()) && StringUtil.isNotEmpty(billPayDetail.getPayeeBankCode())) {
                    payBillDetail.setIsSameBank(String.valueOf(payBillDetail.getPayBankCode().equals(payBillDetail.getRecBankCode()) ? 1 : 0));
                } else {
                    payBillDetail.setIsSameBank("0");
                }
                if(getPaPayStatus("BILL_PAY_MONEY_TEST_STATUS").equals("1")){
                    // 测试金额1.68
                    payBillDetail.setAmt(new BigDecimal(1.68));
                }
                payBillDetail.setPayType(userTestAccount.getCardType());
            }else{
                // 正式数据
                payBillDetail.setPayAccNo(billPayDetail.getPayAccountNo());
                payBillDetail.setPayAccName(billPayDetail.getPayAccountName());
                payBillDetail.setPayBankName(billPayDetail.getPayBankName());
                payBillDetail.setPayBankCode(billPayDetail.getPayBankCode());
                payBillDetail.setPayAccCity(billPayDetail.getPayAccountCity());
                payBillDetail.setRecAccNo(billPayDetail.getPayeeAccountNo());
                payBillDetail.setRecAccName(billPayDetail.getPayeeAccountName());
                payBillDetail.setRecBankName(billPayDetail.getPayeeBankName());
                payBillDetail.setRecBankNodeName(billPayDetail.getPayeeBankNodeName());
                payBillDetail.setRecBankNodeNo(billPayDetail.getPayeeBankNodeNo());
                payBillDetail.setRecAccCity(billPayDetail.getPayeeAccountCity());
                payBillDetail.setRecBankCode(billPayDetail.getPayeeBankCode());
                payBillDetail.setPayType(billPayDetail.getPayType());
                if(StringUtil.isNotEmpty(billPayDetail.getPayAccountCity()) && StringUtil.isNotEmpty(billPayDetail.getPayeeAccountCity())){
                    payBillDetail.setIsSameCity(String.valueOf(billPayDetail.getPayAccountCity().equals(billPayDetail.getPayeeAccountCity()) ? 1: 0));
                }else{
                    payBillDetail.setIsSameCity("0");
                }
                if(StringUtil.isNotEmpty(billPayDetail.getPayBankCode()) && StringUtil.isNotEmpty(billPayDetail.getPayeeBankCode())){
                    payBillDetail.setIsSameBank(String.valueOf(billPayDetail.getPayBankCode().equals(billPayDetail.getPayeeBankCode()) ? 1: 0));
                }else{
                    payBillDetail.setIsSameBank("0");
                }
                payBillDetail.setAmt(billPayDetail.getCheckAmt());
                payBillDetail.setRemark("this is a payno remark");

                payBill.setPayAccNo(billPayDetail.getPayAccountNo());
                payBill.setPayAccName(billPayDetail.getPayAccountName());
                payBill.setPayBankCode(billPayDetail.getPayBankCode());
                payBill.setRemark("this is a billid remark");
            }

            payBill.setBillCount(payBill.getBillDetailList().size());
            payBill.setBillAmt(payBill.getBillDetailList().stream().map(EpPayBillDetail::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        if(CollectionUtils.isNotEmpty(payBillList)){
            // 发送并批量支付
            checkMsg = epPayBillExternalService.startEpPay(payBillList);
            log.info("发送并支付" + JSON.toJSONString(checkMsg.getData()));
            log.info("发送并支付" + checkMsg.getMsgInfo());

            // 发送返回数据
            List<EpPayBill> epPayBills = JSONArray.parseArray(JSON.toJSONString(checkMsg.getData()), EpPayBill.class);

            // 修改单据流转状态
            for (EpPayBill epPayBill : epPayBills) {
                for (EpPayBillDetail epPayBillDetail : epPayBill.getBillDetailList()){
                    // 支付后处理
                    try {
                        doAfterPay(epPayBill, epPayBillDetail);
                    } catch (Exception e) {
                        log.info("支付后处理（不回滚）" , e);
                    }
                }
            }
        }

        // 全部支付成功
        return CheckMsg.successStr("发送成功");
    }

    /**
     * 支付后处理
     */
    public void doAfterPay(EpPayBill epPayBill, EpPayBillDetail epPayBillDetail){
        // 查询单据状态
        PcxBillPayDetail billPayDetail = getOne(
                new LambdaQueryWrapper<PcxBillPayDetail>()
                        .eq(StringUtils.isNoneBlank(epPayBill.getFromId()), PcxBillPayDetail::getBillId, epPayBill.getFromId())
                        .eq(StringUtils.isNoneBlank(epPayBillDetail.getRefSetId()), PcxBillPayDetail::getPayNo, epPayBillDetail.getRefSetId())
                        .eq(StringUtils.isNoneBlank(epPayBill.getAgyCode()), PcxBillPayDetail::getAgyCode, epPayBill.getAgyCode())
                        .eq(StringUtils.isNoneBlank(epPayBill.getFiscal()), PcxBillPayDetail::getFiscal, epPayBill.getFiscal())
                        .eq(StringUtils.isNoneBlank(epPayBill.getMofDivCode()), PcxBillPayDetail::getMofDivCode, epPayBill.getMofDivCode()));
        // 新增支付中单据流转状态
        PcxBillStatusHistory billStatusHistory = PcxBillStatusHistory.builder()
                .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                .billId(billPayDetail.getPayNo())
                .opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
                .opTime(new Date())
                .fromStatus(billPayDetail.getPayStatus())
                .toStatus(PAYING.getCode())
                .remark("")
                .creator(epPayBill.getCreator())
                .creatorName(epPayBill.getCreatorName())
                .createdTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                .agyCode(epPayBill.getAgyCode())
                .fiscal(epPayBill.getFiscal())
                .mofDivCode(epPayBill.getMofDivCode())
                .build();
        pcxBillStatusHistoryPlusService.save(billStatusHistory);
        // 修改当前单据状态
        PcxBillPayDetail updateBillPayDetail = new PcxBillPayDetail();
        updateBillPayDetail.setId(billPayDetail.getId());
        updateBillPayDetail.setPayStatus(PAYING.getCode());
        updateBillPayDetail.setPayingTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        updateById(updateBillPayDetail);
    }

    /**
     * 支付后处理bill状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void operationAfterPay(PcxBillPayDetail updateBillPayDetail, PcxBillStatusHistory billStatusHistory){

        // 更新支付状态
        pcxBillStatusHistoryPlusService.save(billStatusHistory);
        updateById(updateBillPayDetail);

        // 保证只有完成和作废状态、且有支付完单据
        List<PcxBillPayDetail> pcxBillPayDetailList = list(new LambdaQueryWrapper<PcxBillPayDetail>()
                .eq(PcxBillPayDetail::getBillId, updateBillPayDetail.getBillId())
                .ne(PcxBillPayDetail::getPayStatus, CANCELED.getCode())
                .ne(PcxBillPayDetail::getPayStatus, PAID.getCode()));
        List<PcxBillPayDetail> paidPayDetailList = list(new LambdaQueryWrapper<PcxBillPayDetail>()
                .eq(PcxBillPayDetail::getBillId, updateBillPayDetail.getBillId())
                .eq(PcxBillPayDetail::getPayStatus, PAID.getCode()));

        // 合同单据特殊处理
        // 查询关联的付款计划
        List<String> planIdList = pcxBillContractRelService.getBillPlanIdList(updateBillPayDetail.getBillId());
        if (!CollectionUtils.isEmpty(planIdList)){
            // 查询是否有票
            boolean hasEcs = pcxExpDetailEcsRelService.selectCountByBillId(updateBillPayDetail.getBillId()) > 0;
            List<PlanPayStatusDTO> planPayStatusDTOs = new ArrayList<>();
            for (String planId : planIdList) {
                PlanPayStatusDTO planPayStatusDTO = new PlanPayStatusDTO();
                planPayStatusDTO.setPlanId(planId);
                planPayStatusDTO.setHasTicket(hasEcs);
                planPayStatusDTOs.add(planPayStatusDTO);
            }
            pctExternalService.updateContractPayStatus(planPayStatusDTOs);

            return;
        }

        // 报销单据处理
        // 增加结算金额
        PcxBill pcxBill = pcxBillDao.selectById(updateBillPayDetail.getBillId());
        pcxBill.setSettlementAmt(pcxBill.getSettlementAmt().add(updateBillPayDetail.getCheckAmt()));
        pcxBillDao.updateById(pcxBill);
        log.info("支付单支付 billId {}",pcxBill.getId());
        // 全部支付成功后处理
        if (CollectionUtils.isEmpty(pcxBillPayDetailList) && CollectionUtils.isNotEmpty(paidPayDetailList)){
            pcxBill.setPayTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            pcxBill.setPayStatus(PAID.getCode());
            pcxBillDao.updateById(pcxBill);
        }
    }

    /**
     * 获取测试账号
     * @param pcxBillPayQO：请求QO
     * @param agyTestAccount：单位测试账户
     * @param userTestAccountList：用户测试账户列表
     * @param isMulti：是否需要多个用户测试账户
     */
    public void getTestAccount(PcxBillPayQO pcxBillPayQO, PcxBankAccountTest agyTestAccount, List<PcxBankAccountTest> userTestAccountList, Boolean isMulti){
        List<PcxBankAccountTest> agyBankAccountTestList = pcxBankAccountTestDao.selectList(new LambdaQueryWrapper<PcxBankAccountTest>()
                .eq(PcxBankAccountTest::getBankType, "cmb")
                .eq(PcxBankAccountTest::getIsAgyAccount, "1")
                .eq(StringUtils.isNoneBlank(pcxBillPayQO.getAgyCode()), PcxBankAccountTest::getAgyCode, pcxBillPayQO.getAgyCode())
                .eq(StringUtils.isNoneBlank(pcxBillPayQO.getFiscal()), PcxBankAccountTest::getFiscal, pcxBillPayQO.getFiscal())
                .eq(StringUtils.isNoneBlank(pcxBillPayQO.getMofDivCode()), PcxBankAccountTest::getMofDivCode, pcxBillPayQO.getMofDivCode()));
        Assert.isTrue(CollectionUtils.isNotEmpty(agyBankAccountTestList), "未找到单位测试账户");
        BeanUtils.copyProperties(agyBankAccountTestList.get(0), agyTestAccount);

        List<PcxBankAccountTest> userBankAccountTestList = pcxBankAccountTestDao.selectList(new LambdaQueryWrapper<PcxBankAccountTest>()
                .eq(PcxBankAccountTest::getBankType, "cmb")
                .eq(PcxBankAccountTest::getIsAgyAccount, "0")
                .eq(StringUtils.isNoneBlank(pcxBillPayQO.getAgyCode()), PcxBankAccountTest::getAgyCode, pcxBillPayQO.getAgyCode())
                .eq(StringUtils.isNoneBlank(pcxBillPayQO.getFiscal()), PcxBankAccountTest::getFiscal, pcxBillPayQO.getFiscal())
                .eq(StringUtils.isNoneBlank(pcxBillPayQO.getMofDivCode()), PcxBankAccountTest::getMofDivCode, pcxBillPayQO.getMofDivCode()));
        Assert.isTrue(!isMulti ? CollectionUtils.isNotEmpty(userBankAccountTestList) : userBankAccountTestList.size()>1, !isMulti ? "未找到用户测试账户" : "未找到多个用户测试账户");
        userTestAccountList.addAll(userBankAccountTestList);
    }

    /**
     * 是否批量支付弹窗
     * @param pcxBillPayQO
     * @return
     */
    @Override
    public CheckMsg<?> canBatchPay(PcxBillPayQO pcxBillPayQO) {
//        List<PcxBillPayItemQO> payItemList = pcxBillPayQO.getPayItemList();
//        // 来源单据
//        List<String> billIdList = payItemList.stream().map(PcxBillPayItemQO::getBillId).distinct().collect(Collectors.toList());
//        Set<String> balanceIdSet = new HashSet<>();
//        Set<String> accountNoSet = new HashSet<>();
//        Set<String> settlementTypeSet = new HashSet<>();
//        for (PcxBillPayItemQO pcxBillPayItemQO : payItemList) {
//            PcxBillPayDetail pcxBillPayDetail = getOne(new LambdaQueryWrapper<PcxBillPayDetail>().eq(PcxBillPayDetail::getBillId, pcxBillPayItemQO.getBillId()));
//            balanceIdSet.add(pcxBillPayDetail.getBalanceId());
//            accountNoSet.add(pcxBillPayDetail.getPayAccountNo());
//            settlementTypeSet.add(pcxBillPayDetail.getSettlementType());
//        }
//        if(billIdList.size()==1 && balanceIdSet.size() == 1 && accountNoSet.size() == 1 && settlementTypeSet.size() == 1 && settlementTypeSet.toArray()[0].equals(SettlementTypeEnum.SETTLE_TRANSFER.getCode())){
//            return CheckMsg.success().setData("true");
//        }
        return CheckMsg.success().setData("false");
    }

    @Override
    public void cancelPay(String billId) {
        this.pcxBillPayDetailDao.update(null, Wrappers.<PcxBillPayDetail>lambdaUpdate()
                .set(PcxBillPayDetail::getPayStatus, STASH.getCode())
                        .set(PcxBillPayDetail::getPayingTime, StrUtil.EMPTY)
                        .set(PcxBillPayDetail::getModifier, PtyContext.getUsername())
                        .set(PcxBillPayDetail::getModifiedTime, PtyContext.getUsernameCn())
                        .set(PcxBillPayDetail::getModifiedTime, DateUtil.now())
                .eq(PcxBillPayDetail::getBillId, billId));
    }

    /**
     * 批量查询支付状态
     *
     * @param pcxBillRefreshPayQO
     * @return
     */
    @Override
    public CheckMsg<List<PcxBillRefreshPayVO>> refreshPayStatus(PcxBillRefreshPayQO pcxBillRefreshPayQO) {

        pcxBillStatusHistoryService.refreshAllPayingStatus();

        List<PcxBillRefreshPayVO> pcxBillRefreshPayVOList = new ArrayList<>();
        pcxBillRefreshPayQO.getPayItemList().forEach(item -> {
            PcxBillPayDetail billPayDetail = getOne(new LambdaQueryWrapper<PcxBillPayDetail>()
                    .eq(StringUtils.isNoneBlank(item.getBillId()), PcxBillPayDetail::getBillId, item.getBillId())
                    .eq(StringUtils.isNoneBlank(item.getPayNo()), PcxBillPayDetail::getPayNo, item.getPayNo()));
            if (billPayDetail == null) {
                CheckMsg.fail(String.format("单据{%s}-支付单{%s}信息有误", item.getBillId(), item.getPayNo()));
                return;
            }
            PcxBillRefreshPayVO pcxBillRefreshPayVO = new PcxBillRefreshPayVO();
            pcxBillRefreshPayVO.setBillId(billPayDetail.getBillId());
            pcxBillRefreshPayVO.setPayNo(billPayDetail.getPayNo());
            pcxBillRefreshPayVO.setPayStatusCode(billPayDetail.getPayStatus());
            pcxBillRefreshPayVO.setPayStatusName(BillPayerDetailEnum.getByCode(billPayDetail.getPayStatus()).getName());
            pcxBillRefreshPayVOList.add(pcxBillRefreshPayVO);
        });

        return CheckMsg.success(pcxBillRefreshPayVOList);
    }

    /**
     * 分配结算余额到支付明细
     *
     * 本方法主要用于根据结算信息自动计算并生成支付明细它通过获取结算信息、
     * 校验数据有效性、计算未分配的结算金额，并将这些金额分配到支付明细中
     *
     * @param qo 包含结算分配请求信息的对象，包括单据ID和结算信息列表
     * @return 返回一个CheckMsg对象，包含生成的支付明细信息或错误信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<PaySettlementBalanceVO> allocateSettlementBalance(AllocateBalanceQO qo) {
        try {
            // 参数有效性校验
            Assert.notNull(qo, "自动计算支付明细参数不能为空");
            Assert.notNull(qo.getBillId(), "单据Id不能为空");
            String billId = qo.getBillId();

            // 提取冲销借款信息
            List<PcxBillPaySettlementBalanceQO.BalanceExtQO> loanBalanceList = qo.getBalanceList().stream()
                    .filter(ext -> ext.getBalanceType().equals(BalanceTypeEnum.BUD.getCode()))
                    .filter(ext -> Objects.nonNull(ext.getUsedAmt()))
                    .filter(ext -> Objects.nonNull(ext.getBalanceSource()) && ext.getBalanceSource().equals("loan"))
                    .collect(Collectors.toList());

            // 过滤出预算类型的余额信息
            List<PcxBillPaySettlementBalanceQO.BalanceExtQO> balanceList = qo.getBalanceList().stream()
                    .filter(ext -> ext.getBalanceType().equals(BalanceTypeEnum.BUD.getCode()))
                    .filter(ext -> Objects.nonNull(ext.getUsedAmt()))
                    .filter(ext -> Objects.isNull(ext.getBalanceSource()) || !ext.getBalanceSource().equals("loan"))
                    .filter(ext -> StringUtils.isNotBlank(ext.getPayAccountNo())).collect(Collectors.toList());

            // 使用LinkedHashMap分组, 保证相同数据遍历顺序一致
            List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(billId));
            if (CollectionUtils.isEmpty(settlements))
                return CheckMsg.success(PaySettlementBalanceVO.builder().build());

            // 将结算信息转换为扩展对象列表
            List<PcxBillPaySettlementBalanceQO.SettlementExtQO> settlementExtList = settlements.stream().map(settlement -> {
                PcxBillPaySettlementBalanceQO.SettlementExtQO ext = PcxBillPaySettlementBalanceQO.SettlementExtQO.builder().build();
                BeanUtils.copyProperties(settlement, ext);
                ext.setSettlementUk(null);
                return ext;
            }).collect(Collectors.toList());

            // 创建结算信息的Map，以便快速查找
            Map<String, PcxBillPaySettlementBalanceQO.SettlementExtQO> uk$settlement = settlementExtList.stream().collect(Collectors.toMap(PcxBillPaySettlementBalanceQO.SettlementExtQO::getSettlementUk, Function.identity(), (a, b) -> a));

            // 取出主单据
            PcxBill pcxBill = billMainService.view(billId);

            // 只有一个费用类型就直接赋值
            if (StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",").length == 1) {
                balanceList.forEach(ext -> {
                    ext.setExpenseCode(ObjectUtils.firstNonNull(ext.getExpenseCode(), pcxBill.getExpenseCodes()));;
                });
                loanBalanceList.forEach(ext -> {
                    ext.setExpenseCode(ObjectUtils.firstNonNull(ext.getExpenseCode(), pcxBill.getExpenseCodes()));
                });
            }

            BiConsumer<List<PcxBillPayDetail>, List<PcxBillPayDetail>> rebuildStashDetail = (newRecords, stashRecords) -> {
                if (CollectionUtils.isNotEmpty(stashRecords))
                    this.removeBatchByIds(stashRecords.stream().map(PcxBillPayDetail::getId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(newRecords))
                    this.saveBatch(newRecords);
            };

            // 单据有效性校验
            Assert.notNull(pcxBill, "单据不存在,{}", billId);

            List<PcxBillPayDetail> details = buildDetails(qo, pcxBill, settlementExtList, balanceList, loanBalanceList, rebuildStashDetail);

            // 回填汇总数据
            PaySettlementBalanceVO balanceVO = PaySettlementBalanceVO.builder().build();

            List<String> balanceIds = details.stream().map(PcxBillPayDetail::getBalanceId).collect(Collectors.toList());
            Map<String, BudBalanceDTO> id$balance = new HashMap<>();
            if (CollectionUtils.isNotEmpty(balanceIds)) {
                List<BudBalanceDTO> balanceDTOList = balanceExternalService.getBalanceByIds(balanceIds);
                id$balance = balanceDTOList.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity(), (a, b) -> a));
            }
            List<PcxBillExpCommon> commons = pcxBillExpCommonService.selectList(Collections.singletonList(billId), pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
            Map<String, String> exp$rel = commons.stream().collect(Collectors.toMap(PcxBillExpCommon::getExpenseCode, PcxBillExpCommon::getExpenseName, (a, b) -> a));
            assemblePsbVOS(details, billId, uk$settlement, balanceVO, id$balance, exp$rel);
            balanceEnhanceForVO(details.stream().map(PcxBillPayDetail::getBalanceExtQO).filter(Objects::nonNull).collect(Collectors.toList()), pcxBill);
            return CheckMsg.success(balanceVO);
        } catch (Exception e) {
            log.error(e.getMessage(), "自动计算支付明细异常:", e);
            return CheckMsg.fail("自动计算支付明细异常:" + e.getMessage());
        }
    }

    @Override
    public CheckMsg<List<PcxBillPayDetail>> allocatePayDetails(AllocateBalanceQO qo) {
        try {
            // 参数有效性校验
            Assert.notNull(qo, "自动计算支付明细参数不能为空");
            Assert.notNull(qo.getBillId(), "单据Id不能为空");
            String billId = qo.getBillId();
            qo.setFromView(Boolean.TRUE);

            // 提取冲销借款信息
            List<PcxBillPaySettlementBalanceQO.BalanceExtQO> loanBalanceList = qo.getBalanceList().stream()
                    .filter(ext -> ext.getBalanceType().equals(BalanceTypeEnum.BUD.getCode()))
                    .filter(ext -> Objects.nonNull(ext.getUsedAmt()))
                    .filter(ext -> Objects.nonNull(ext.getBalanceSource()) && ext.getBalanceSource().equals("loan"))
                    .collect(Collectors.toList());

            // 过滤出预算类型的余额信息
            List<PcxBillPaySettlementBalanceQO.BalanceExtQO> balanceList = qo.getBalanceList().stream()
                    .filter(ext -> ext.getBalanceType().equals(BalanceTypeEnum.BUD.getCode()))
                    .filter(ext -> Objects.nonNull(ext.getUsedAmt()))
                    .filter(ext -> Objects.isNull(ext.getBalanceSource()) || !ext.getBalanceSource().equals("loan"))
                    .filter(ext -> StringUtils.isNotBlank(ext.getPayAccountNo())).collect(Collectors.toList());

            // 使用LinkedHashMap分组, 保证相同数据遍历顺序一致
            List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(billId));
            if (CollectionUtils.isEmpty(settlements))
                return CheckMsg.success(Collections.emptyList());

            // 将结算信息转换为扩展对象列表
            List<PcxBillPaySettlementBalanceQO.SettlementExtQO> settlementExtList = settlements.stream().map(settlement -> {
                PcxBillPaySettlementBalanceQO.SettlementExtQO ext = PcxBillPaySettlementBalanceQO.SettlementExtQO.builder().build();
                BeanUtils.copyProperties(settlement, ext);
                ext.setSettlementUk(null);
                return ext;
            }).collect(Collectors.toList());

            // 取出主单据
            PcxBill pcxBill = billMainService.view(billId);

            BiConsumer<List<PcxBillPayDetail>, List<PcxBillPayDetail>> rebuildStashDetail = (newRecords, stashRecords) -> {};

            // 单据有效性校验
            Assert.notNull(pcxBill, "单据不存在,{}", billId);
            List<PcxBillPayDetail> details = buildDetails(qo, pcxBill, settlementExtList, balanceList, loanBalanceList, rebuildStashDetail);
            return CheckMsg.success(details);
        } catch (Exception e) {
            log.error(e.getMessage(), "自动计算支付明细异常:", e);
            return CheckMsg.fail("自动计算支付明细异常:" + e.getMessage());
        }
    }

    /**
     * 只计算不保存
     * @param qo
     * @param dbFirst 是否优先从数据库中取数据
     * @return
     */
    @Override
    public CheckMsg<PayDetailVO> calculatePayDetails(PayDetailQO qo, boolean dbFirst) {
        if (dbFirst) {
            List<PcxBillPayDetail> list = this.list(new LambdaQueryWrapper<PcxBillPayDetail>().eq(PcxBillPayDetail::getBillId, qo.getBillId()));
            return payDetailCalculator.restoreFromPayDetails(qo.getBillId(), list);
        }
        return payDetailCalculator.calculatePayDetails(qo);
    }

    @Override
    @Transactional
    public CheckMsg<Boolean> savePayDetails(PayDetailSaveQO qo) {
        // 检查是否已有终审后的记录
        List<PcxBillPayDetail> existsRecords = pcxBillPayDetailDao.selectList(Wrappers.<PcxBillPayDetail>lambdaQuery().eq(PcxBillPayDetail::getBillId, qo.getBillId()));
        Assert.state(CollectionUtils.isEmpty(existsRecords) ||
                existsRecords.stream().noneMatch(record -> record.getPayStatus().equals(CONFIRM.getCode())), "单据已终审,不能修改支付明细");
        // 移除单据下所有支付明细
        pcxBillPayDetailDao.delete(Wrappers.<PcxBillPayDetail>lambdaQuery().eq(PcxBillPayDetail::getBillId, qo.getBillId()));
        List<PcxBillPayDetail> buildPayDetails = payDetailCalculator.buildPayDetails(qo);
        if (CollectionUtils.isEmpty(buildPayDetails))
            return CheckMsg.success(true);
        this.saveBatch(buildPayDetails);
        // 汇总结算方式核定金额
        Map<String, List<PcxBillPayDetail>> uk$settlement = buildPayDetails.stream().collect(Collectors.groupingBy(PcxBillPayDetail::getSettlementUk));

        pcxBillSettlementInfoService.batchUpdateByUk(uk$settlement.entrySet().stream().map(entry -> {
            PcxBillSettlement settlement = new PcxBillSettlement();
            settlement.setSettlementUk(entry.getKey());
            settlement.setCheckAmt(entry.getValue().stream().map(PcxBillPayDetail::getCheckAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            settlement.setPayeeAccNo(entry.getValue().get(0).getPayeeAccountNo());
            settlement.setPayeeAccName(entry.getValue().get(0).getPayeeAccountName());
            settlement.setPayeeBankName(entry.getValue().get(0).getPayeeBankName());
            log.info("更新支付结算信息:{}", settlement);
            return settlement;
        }).collect(Collectors.toList()));
        return CheckMsg.success(true);
    }


    private @NotNull List<PcxBillPayDetail> buildDetails(AllocateBalanceQO qo,
                                                         PcxBill pcxBill,
                                                         List<PcxBillPaySettlementBalanceQO.SettlementExtQO> settlementExtList,
                                                         List<PcxBillPaySettlementBalanceQO.BalanceExtQO> balanceList,
                                                         List<PcxBillPaySettlementBalanceQO.BalanceExtQO> loanBalanceList,
                                                         BiConsumer<List<PcxBillPayDetail>, List<PcxBillPayDetail>> rebuildStashDetail) {
        // 根据结算方式id取出所有结算明细,
        List<PcxBillPayDetail> existsRecords = this.list(new LambdaQueryWrapper<PcxBillPayDetail>()
                .eq(PcxBillPayDetail::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillPayDetail::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillPayDetail::getIsDeleted, 0)
                .eq(PcxBillPayDetail::getMofDivCode, pcxBill.getMofDivCode())
                .eq(PcxBillPayDetail::getBillId, pcxBill.getId()));

        // 分离待处理和已处理的记录
        List<PcxBillPayDetail> stashRecords = existsRecords.stream().filter(record -> Arrays.asList(STASH.getCode(), STASH.getCode()).contains(record.getPayStatus())).collect(Collectors.toList());
        List<PcxBillPayDetail> releaseRecords = existsRecords.stream().filter(record -> !Arrays.asList(STASH.getCode(), STASH.getCode()).contains(record.getPayStatus())).collect(Collectors.toList());

        // 一切满足的情况下, 提取结算扩展转换为支付明细保存
        List<PcxBillPayDetail> details = new ArrayList<>();
        // 已生成支付明细(送审), 无法再变更, 变更流程需要走变更流程
        // Assert.state(releaseRecords.isEmpty(), "单据{}已生成支付明细, 无法再变更, 变更流程需要走变更流程", billId);
        if (CollectionUtils.isNotEmpty(releaseRecords)) {
            details.addAll(releaseRecords);
        }else if (CollectionUtils.isNotEmpty(stashRecords) && qo.isFromView()){
            details.addAll(stashRecords);
        }else {
            // 按金额排序，保证每次相同输入时匹配结果一致
            settlementExtList.sort(Comparator.comparing(PcxBillSettlement::getSettlementType).thenComparing(PcxBillSettlement::getCheckAmt));
            // 取出付款账号, 企业默认单位基本户, 一个的情况下默认带出来, 多个不赋值
            boolean fromView = qo.isFromView();
//            MadAgyAccountDTO account = getMadAgyAccountDTO(fromView, pcxBill);
            balanceEnhance(balanceList, pcxBill);
            // 获取费用类型对应的指标
            settlementExtList.forEach(settlement -> {
                settlementEnhance(pcxBill, settlement, !fromView);

                // 结算方式需要金额
                BigDecimal settleAmt = settlement.getCheckAmt().subtract(settlement.getAllocatedAmt());
                if (CollectionUtils.isEmpty(balanceList))
                    reduceSettleAmt(settlement, balanceList, settleAmt, details, pcxBill);
                else
                    while (settleAmt.compareTo(BigDecimal.ZERO) > 0 && !balanceList.stream().allMatch(ext -> ext.getUsedAmt().equals(ext.getAllocatedAmt()))) {
                        settleAmt = reduceSettleAmt(settlement, balanceList, settleAmt, details, pcxBill);
                    }
            });
            // 生成冲借款支付明细
            loanBalanceList.forEach(ext -> {
                detailGenerateLoan(pcxBill, ext, details);
            });
            if (!fromView) {
                rebuildStashDetail.accept(details, stashRecords);
            }
        }

        return details;
    }

    private static void detailGenerateLoan(PcxBill pcxBill, PcxBillPaySettlementBalanceQO.BalanceExtQO ext, List<PcxBillPayDetail> details) {
        PcxBillPayDetail detail = detailGenerate(PcxBillPaySettlementBalanceQO.SettlementExtQO.builder().build(), ext, ext.getUsedAmt(), details, pcxBill, false);
        detail.setSettlementType(SETTLE_LOAN);
        detail.setSettlementTypeName("冲销借款");
        detail.setLoanBillId(ext.getRelBillId());
        detail.setLoanBillNo(ext.getRelBillNo());
        detail.setSettlementUk(ext.getRelBillNo());
    }

    private void balanceEnhanceForVO(List<PcxBillPaySettlementBalanceQO.BalanceExtQO> balanceList, PcxBill pcxBill) {
        List<String> accountNos = balanceList.stream().map(PcxBillPaySettlementBalanceQO.BalanceExtQO::getPayAccountNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountNos)) {
            MadAgyAccountQO query = MadAgyAccountQO.builder()
                    .agyCode(pcxBill.getAgyCode())
                    .mofDivCode(pcxBill.getMofDivCode())
                    .fiscal(pcxBill.getFiscal())
                    .build();
            List<JSONObject> accountCodes = madAgyAccountExternalService.getAgyAccountCodeAndDesc(query);
            Map<String, String> payAccountNameRel = accountCodes.stream().collect(Collectors.toMap(account -> account.get("code").toString(), account -> account.get("name").toString(), (a, b) -> a));
            balanceList.forEach(ext -> {
                ext.setPayAccountInfo(payAccountNameRel.getOrDefault(ext.getPayAccountNo(), StringUtils.EMPTY));
            });
        }
    }

    private void balanceEnhance(List<PcxBillPaySettlementBalanceQO.BalanceExtQO> balanceList, PcxBill pcxBill) {
        List<String> accountNos = balanceList.stream().map(PcxBillPaySettlementBalanceQO.BalanceExtQO::getPayAccountNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountNos)) {
            MadAgyAccountQO query = MadAgyAccountQO.builder().agyCode(pcxBill.getAgyCode())
                    .mofDivCode(pcxBill.getMofDivCode())
                    .build();
            query.setAccountCodes(accountNos);
            List<MadAgyAccountDTO> accountList = madAgyAccountExternalService.selectAgyAccountList(MadAgyAccountQO.builder()
                    .agyCode(pcxBill.getAgyCode())
                    .mofDivCode(pcxBill.getMofDivCode())
                    .accountCodes(accountNos)
                    .build());

            PaValset paValset = new PaValset();
            paValset.setValsetCode(PcxConstant.ValueSetCode.ACCOUNT_TYPE);
            paValset.setAgyCode("*");
            paValset.setFiscal(Integer.valueOf(pcxBill.getFiscal()));
            paValset.setMofDivCode(pcxBill.getMofDivCode());

            List<PaValset> accountTypes = pcxValSetService.selectByValsetCode(paValset);
            Map<String, String> accountTypeRel = accountTypes.stream().collect(Collectors.toMap(PaValset::getValCode, PaValset::getVal, (a, b) -> a));

            Map<String, MadAgyAccountDTO> accountMap = accountList.stream().collect(Collectors.toMap(MadAgyAccountDTO::getAccountCode, Function.identity(), (a, b) -> a));
            balanceList.stream().filter(ext -> !"loan".equals(ext.getBalanceSource())).forEach(ext -> {
                MadAgyAccountDTO account = accountMap.get(ext.getPayAccountNo());
                Assert.notNull(account, "账户信息不存在,账户编号:{}", ext.getPayAccountNo());
                ext.setPayAccountName(account.getAccountName());
                ext.setPayAccCity(account.getCityCode());
                ext.setPayAccountType(account.getAccountType());
                ext.setPayAccountTypeName(accountTypeRel.getOrDefault(ext.getPayAccountType(), StringUtils.EMPTY));
                ext.setPayBankName(account.getBankName());
                ext.setPayBankCode(account.getBankCode());
                ext.setPayAccountNo(account.getAccountCode());
            });
        }
    }

    /**
     * 根据结算信息和账单信息获取银行节点信息
     *
     * 本方法通过调用外部服务查询员工卡信息，并进一步查询银行节点信息
     * 如果查询过程中遇到错误，会记录错误日志并返回null
     *
     * @param settlement 结算信息，包含代理代码、财政年份等
     * @param pcxBill 账单信息，包含申请人代码等
     * @return 可能返回null或一个填充了银行节点信息的MadBankNodeDTO对象
     */
    private @Nullable MadBankNodeDTO getMadBankNodeDTO(PcxBillSettlement settlement, PcxBill pcxBill) {
        MadEmployeeCardDTO madEmployeeCardDTO = null;
        MadBankNodeDTO madBankNodeDTO = null;
        try {
            // 对公
            if (PaymentType.CORPORATE_ACCOUNT.getValue().equals(settlement.getPayType())) {
                MadCurrentDTO madCurrentDTO = madCurrentExternalService.selectByAccountNo(settlement.getPayeeAccNo(), settlement.getAgyCode(), settlement.getFiscal(), settlement.getMofDivCode());
                Assert.state(Objects.nonNull(madCurrentDTO), "单位{}部门{}人员{}对公收款账号{}信息维护有误", pcxBill.getAgyCode(), pcxBill.getMofDivCode(), pcxBill.getClaimantCode(), settlement.getPayeeAccNo());
                if (StrUtil.isNotBlank(madCurrentDTO.getBanknodeNo())) {
                    madBankNodeDTO = madBankNodeExternalService.selectByBankNodeNo(madCurrentDTO.getBanknodeNo());
                    // 断言银行节点信息查询成功，否则抛出异常
                    Assert.state(Objects.nonNull(madBankNodeDTO), "单位{}部门{}对公收款账号{}信息维护有误", pcxBill.getAgyCode(), pcxBill.getMofDivCode(), settlement.getPayeeAccNo());
                    madBankNodeDTO.setAccountType(PaymentType.CORPORATE_ACCOUNT.getUnionType());
                    madBankNodeDTO.setAccountTypeName(PaymentType.CORPORATE_ACCOUNT.getName());
                }
            } else {
                // 根据唯一键查询员工卡信息
                CheckMsg<List<MadEmployeeCardDTO>> msg = madEmployeeCardExternalService.selectEmployeeCardByUniqueKey(settlement.getAgyCode(), settlement.getFiscal(),
                        settlement.getMofDivCode(),
                        null,
                        settlement.getPayeeAccNo(),
                        pcxBill.getTenantId());
                // 断言员工卡信息查询成功，否则抛出异常
                Assert.state(msg.isSuccess() && CollectionUtils.isNotEmpty(msg.getData()), "单位{}部门{}人员{}个人收款账号{}信息维护有误", pcxBill.getAgyCode(), pcxBill.getMofDivCode(), pcxBill.getClaimantCode(), settlement.getPayeeAccNo());

                madEmployeeCardDTO = msg.getData().get(0);
                // 1个人卡,2公务卡
                PaymentType accountType = PaymentType.PERSON_CARD.getUnionType().equals(madEmployeeCardDTO.getAccountType()) ?
                        PaymentType.PERSON_CARD : PaymentType.BUSINESS_CARD.getCode().equals(madEmployeeCardDTO.getAccountType()) ?
                        PaymentType.BUSINESS_CARD : null;
                // 根据银行代码和银行节点代码查询银行节点信息
                if (StrUtil.isNotBlank(madEmployeeCardDTO.getBankCode()) && StrUtil.isNotBlank(madEmployeeCardDTO.getBankNodeCode())) {
                    madBankNodeDTO = madBankNodeExternalService.selectByBankCodeAndBankNodeCode(madEmployeeCardDTO.getBankCode(), madEmployeeCardDTO.getBankNodeCode());
                    // 断言银行节点信息查询成功，否则抛出异常
                    Assert.state(Objects.nonNull(madBankNodeDTO), "单位{}部门{}个人收款账号{}信息维护有误", pcxBill.getAgyCode(), pcxBill.getMofDivCode(), settlement.getPayeeAccNo());
                    if (Objects.nonNull(accountType)) {
                        madBankNodeDTO.setAccountType(accountType.getUnionType());
                        madBankNodeDTO.setAccountTypeName(accountType.getName());
                    }
                }
            }
        }catch (Exception e){
            // 捕获异常并记录错误日志
            log.error(e.getMessage(), e);
        }
        // 返回查询到的银行节点信息，可能为null
        return madBankNodeDTO;
    }

    @Override
    @Transactional
    public void submit(String billId) {
        try {
            PcxBill pcxBill = billMainService.view(billId);
            Assert.notNull(pcxBill, "单据不存在,{}", billId);
            if (Arrays.asList(BillFuncCodeEnum.EXPENSE.getCode(),
                    BillFuncCodeEnum.LOAN.getCode()).contains(pcxBill.getBillFuncCode())) {
                if (pcxBill.getCheckAmt().compareTo(pcxBill.getLoanAmt()) == 0) {
                    log.info("单据{}已核定金额与冲销金额一致,无需生成支付明细", billId);
                    return;
                }
                // 获取待确认单据明细
                List<PcxBillPayDetail> details = this.list(new LambdaQueryWrapper<PcxBillPayDetail>()
                        .eq(PcxBillPayDetail::getAgyCode, pcxBill.getAgyCode())
                        .eq(PcxBillPayDetail::getFiscal, pcxBill.getFiscal())
                        .eq(PcxBillPayDetail::getIsDeleted, 0)
                        .eq(PcxBillPayDetail::getMofDivCode, pcxBill.getMofDivCode())
                        .in(PcxBillPayDetail::getPayStatus, Arrays.asList(CONFIRM.getCode(), STASH.getCode()))
                        .eq(PcxBillPayDetail::getBillId, billId)
                );
                Assert.notEmpty(details, "单据{}不存在待确认明细", billId);
                // 退回的,已经产生过待确认单据, 需要将待确认单据改为暂存
                if (details.stream().anyMatch(detail -> detail.getPayStatus().equals(CONFIRM.getCode()))) {
                    details.forEach(detail -> {
                        detail.setPayStatus(STASH.getCode());
                        detail.setModifier(PtyContext.getUsername());
                        detail.setModifierName(PtyContext.getUsernameCn());
                        detail.setModifiedTime(DateUtil.now());
                    });
                } else {
                    // 正向审批通过, 需要将暂存单据改为待确认
                    details.forEach(detail -> {
                        detail.setPayStatus(CONFIRM.getCode());
                        detail.setModifier(PtyContext.getUsername());
                        detail.setModifierName(PtyContext.getUsernameCn());
                        detail.setModifiedTime(DateUtil.now());
                    });
                }
                this.saveOrUpdateBatch(details);
            }else {
                log.info("支付明细=>无支付明细的单据类型{}.{}", pcxBill.getBillFuncCode(), billId);
            }
        }catch (Exception e) {
            log.error(e.getMessage(), "初审岗变更明细状态异常:", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 组装支付结算信息VO
     * 该方法用于根据账单详情和结算信息，组装支付结算余额VO
     * 主要进行的工作包括：
     * 1. 根据结算类型和结算ID对详情进行分组
     * 2. 为每个详情项关联余额信息
     * 3. 根据结算类型将结算信息添加到相应的列表中
     *
     * @param details       账单支付详情列表
     * @param billId        账单ID
     * @param uk$settlement 结算ID与结算扩展信息的映射
     * @param balanceVO     支付结算余额VO
     * @param id$balance    结算ID与余额信息的映射
     * @param exp$rel
     */
    private static void assemblePsbVOS(List<PcxBillPayDetail> details, String billId, Map<String, PcxBillPaySettlementBalanceQO.SettlementExtQO> uk$settlement, PaySettlementBalanceVO balanceVO, Map<String, BudBalanceDTO> id$balance, Map<String, String> exp$rel) {
        // 剔除借款结算类型
//        details = details.stream().filter(detail -> !detail.getSettlementType().equals(SETTLE_LOAN)).collect(Collectors.toList());
        // 根据结算类型和结算ID对详情进行分组
        details.stream().collect(Collectors.groupingBy(PcxBillPayDetail::getSettlementType,
                Collectors.groupingBy(PcxBillPayDetail::getSettlementUk))).forEach((settlementType, idRelatedList) -> {
            // 遍历每个结算ID相关的详情列表
            idRelatedList.forEach((getSettlementUk, detailList) -> {
                // 遍历每个详情项，关联余额信息
                AtomicReference<PcxBillPaySettlementBalanceQO.SettlementExtQO> atomicReference = new AtomicReference<>(new PcxBillPaySettlementBalanceQO.SettlementExtQO());
                detailList.forEach(detail -> {
                    detail.assembleBalance();
                    detail.assembleSettlement();
                    atomicReference.set(detail.getSettlementExtQO());
                    if (Objects.nonNull(detail.getBalanceExtQO())) {
                        BudBalanceDTO balanceDTO = id$balance.getOrDefault(detail.getBalanceId(), new BudBalanceDTO());
                        detail.getBalanceExtQO().setBalanceAmt(balanceDTO.getBalanceAmt());
                        detail.getBalanceExtQO().setTotalAmt(balanceDTO.getTotalAmt());
                        detail.getBalanceExtQO().setProject(balanceDTO.getProjectName());
                        detail.getBalanceExtQO().setExpeco(balanceDTO.getExpecoName());
                        detail.getBalanceExtQO().setDepartment(balanceDTO.getDepartmentName());
                        detail.getBalanceExtQO().setExpenseCode(detail.getExpenseCode());
                        detail.getBalanceExtQO().setExpenseName(exp$rel.getOrDefault(detail.getExpenseCode(), StringUtils.EMPTY));
                        detail.getBalanceExtQO().setSpanId(SecureUtil.md5(String.format("%s/%s", balanceDTO.getBalanceId(), detail.getExpenseCode())));
                        detail.getBalanceExtQO().setPayAccountNo(detail.getPayAccountNo());
                    }
                });
                // 获取当前结算ID对应的结算扩展信息，并设置余额列表和是否满足条件
                PcxBillPaySettlementBalanceQO.SettlementExtQO settlementExtQO = uk$settlement.getOrDefault(getSettlementUk, PcxBillPaySettlementBalanceQO.SettlementExtQO.builder().build());
                if (Objects.nonNull(atomicReference.get())) {
                    BeanUtil.copyProperties(atomicReference.get(), settlementExtQO, CopyOptions.create().ignoreNullValue());
                }
                settlementExtQO.setBalanceList(detailList.stream().map(PcxBillPayDetail::getBalanceExtQO).filter(Objects::nonNull).collect(Collectors.toList()));
                settlementExtQO.setIsSatisfied(ObjectUtils.firstNonNull(settlementExtQO.getCheckAmt(), BigDecimal.ZERO).compareTo(detailList.stream().map(PcxBillPayDetail::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add)) == 0);
                settlementExtQO.setCheckAmt(detailList.stream().map(PcxBillPayDetail::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add));

                if (SETTLE_LOAN.equals(settlementType)) {
                    balanceVO.getSettle_loan().add(settlementExtQO);
                }else {
                    // 根据结算类型进行进一步处理
                    SettlementTypeEnum settlementTypeEnum = SettlementTypeEnum.getByCode(settlementType);
                    switch (Objects.requireNonNull(settlementTypeEnum)) {
                        case SETTLE_CASH:
                            balanceVO.getSettle_cash().add(settlementExtQO);
                            break;
                        case SETTLE_TRANSFER:
                            balanceVO.getSettle_transfer().add(settlementExtQO);
                            break;
                        case SETTLE_BUSICARD:
                            balanceVO.getSettle_busicard().add(settlementExtQO);
                            break;
                        case SETTLE_CHEQUE:
                            balanceVO.getSettle_cheque().add(settlementExtQO);
                            break;
                    }
                }
            });
        });
    }



    /**
     * 减少结算金额
     * 根据提供的结算扩展信息、预期余额列表、结算金额、详情列表和账单信息来减少结算金额
     *
     * @param settlementExtQO 结算扩展信息，包含结算相关的额外信息
     * @param expBalanceList  预期余额列表，用于结算的余额信息
     * @param settleAmt       结算金额，即需要结算的总金额
     * @param details         详情列表，保存结算详情的对象列表
     * @param pcxBill         账单对象，包含账单的详细信息
     * @return 减少后的结算金额
     */
    private static @NotNull BigDecimal reduceSettleAmt(PcxBillPaySettlementBalanceQO.SettlementExtQO settlementExtQO,
                                                       List<PcxBillPaySettlementBalanceQO.BalanceExtQO> expBalanceList,
                                                       BigDecimal settleAmt,
                                                       List<PcxBillPayDetail> details, PcxBill pcxBill) {
        // 匹配到一个
        if (expBalanceList.size() == 1) {
            PcxBillPaySettlementBalanceQO.BalanceExtQO balanceExtQO = expBalanceList.get(0);
            detailGenerate(settlementExtQO, balanceExtQO, settleAmt, details, pcxBill, Boolean.TRUE);
            // 匹配多个
        } else if (expBalanceList.size() > 1) {
            // 存在指标剩余金额与所需金额相等的,生成一条支付明细
            PcxBillPaySettlementBalanceQO.BalanceExtQO matchBalance = expBalanceList.stream().filter(balanceExtQO ->
                    balanceExtQO.getUsedAmt().subtract(balanceExtQO.getAllocatedAmt()).compareTo(settleAmt) == 0).findAny().orElse(null);
            if (Objects.nonNull(matchBalance)) {
                detailGenerate(settlementExtQO, matchBalance, settleAmt, details, pcxBill, Boolean.TRUE);
            } else {
                // 若不存在,取剩余可用金额最小的指标
                matchBalance = expBalanceList.stream().filter(balance -> balance.getUsedAmt().compareTo(balance.getAllocatedAmt()) != 0)
                        .min(Comparator.comparing(balance -> balance.getUsedAmt().subtract(balance.getAllocatedAmt()))).orElse(null);
                // 根据收款账号的收款金额形成一条支付明细
                // 指标可提供金额
                if (Objects.nonNull(matchBalance)) {
                    detailGenerate(settlementExtQO, matchBalance, settleAmt, details, pcxBill, Boolean.TRUE);
                }
                // 指标为空说明已有指标无法满足当前结算方式
            }
            // 一个指标没有
        } else {
            // 为使用方法结构虚拟一个指标, 后面做新增的时候, 需要将detail中指标为空的记录剔除
            detailGenerate(settlementExtQO, PcxBillPaySettlementBalanceQO.BalanceExtQO
                    .builder()
                    .allocatedAmt(BigDecimal.ZERO)
                    .usedAmt(BigDecimal.ZERO)
                    .build(), settleAmt, details, pcxBill, Boolean.TRUE);
            settlementExtQO.setAllocatedAmt(settlementExtQO.getCheckAmt());
        }
        // 重新计算结算方式需求金额
        return settlementExtQO.getCheckAmt().subtract(settlementExtQO.getAllocatedAmt());
    }


    /**
     * 根据结算扩展信息和指标扩展信息生成支付明细
     * 支付明细展平支付明细和指标
     *
     * @param settlementExtQO    结算扩展信息对象，包含结算相关数据
     * @param balanceExtQO       指标扩展信息对象，包含指标相关数据
     * @param settleAmt          结算金额，用于比较和分配
     * @param details            支付明细列表，将新生成的明细添加到此列表中
     * @param pcxBill
     * @param updateAllocatedAmt
     */
    private static PcxBillPayDetail detailGenerate(PcxBillPaySettlementBalanceQO.SettlementExtQO settlementExtQO,
                                       PcxBillPaySettlementBalanceQO.BalanceExtQO balanceExtQO,
                                       BigDecimal settleAmt,
                                       List<PcxBillPayDetail> details,
                                       PcxBill pcxBill, Boolean updateAllocatedAmt) {
        // 根据收款账号的收款金额形成一条支付明细
        // 指标可提供金额
        BigDecimal balanceAmt = balanceExtQO.getUsedAmt().subtract(balanceExtQO.getAllocatedAmt());
        // settleAmt balanceAmt 取小的
        BigDecimal minAlloc = settleAmt.compareTo(balanceAmt) > 0 ? balanceAmt : settleAmt;
        if (updateAllocatedAmt) {
            // 更新结算和余额的已分配金额
            settlementExtQO.setAllocatedAmt(settlementExtQO.getAllocatedAmt().add(minAlloc));
            balanceExtQO.setAllocatedAmt(balanceExtQO.getAllocatedAmt().add(minAlloc));
        }

        // 生成一条支付明细
        PcxBillPayDetail detail = new PcxBillPayDetail();
        detail.setCheckAmt(minAlloc);
        // 将新生成的支付明细添加到明细列表中
        details.add(detail.sprinkle(pcxBill).sprinkle(settlementExtQO).sprinkle(balanceExtQO));
        return detail;
    }


    private List<String> disableByDetailIds(List<String> ids) {
        LambdaUpdateWrapper<PcxBillPayDetail> in = new LambdaUpdateWrapper<PcxBillPayDetail>()
                .set(PcxBillPayDetail::getPayStatus, CANCELED.getCode())
                .in(PcxBillPayDetail::getBillId, ids);
        pcxBillPayDetailDao.update(null, in);
        return ids;
    }
}

