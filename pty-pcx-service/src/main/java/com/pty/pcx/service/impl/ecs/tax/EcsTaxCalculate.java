package com.pty.pcx.service.impl.ecs.tax;

import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;

import java.util.Objects;

/**
 * 票税计算
 */
public interface EcsTaxCalculate {
    default void calculate(PcxExpDetailEcsRel ecsRel, PcxBillExpDetailBase detail){
        if (!isMatch(ecsRel, detail) && Objects.nonNull(next())){
            next().calculate(ecsRel, detail);
        }
    }
    boolean isMatch(PcxExpDetailEcsRel ecsRel, PcxBillExpDetailBase detail);
    EcsTaxCalculate next();
    void setNext(EcsTaxCalculate next);
}
