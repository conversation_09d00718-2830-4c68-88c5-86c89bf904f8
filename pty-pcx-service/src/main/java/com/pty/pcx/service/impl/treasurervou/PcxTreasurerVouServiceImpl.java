package com.pty.pcx.service.impl.treasurervou;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pty.pcx.api.treasurervou.PcxTreasurerVouService;
import com.pty.pcx.common.enu.BillApproveStatusEnum;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.PcxApprovalCondFieldEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasItemDao;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dao.treasurypay.detail.PcxBillPayDetailDao;
import com.pty.pcx.dto.treasurervou.PcxGenVouDTO;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.gip.IPcxGipVouService;
import com.pty.pcx.qo.treasurervou.PcxTreasurerVouQO;
import com.pty.pcx.qo.treasurervou.PcxTreasurerVouQueryQO;
import com.pty.pcx.api.mybatisplus.bill.IPcxBillPlusService;
import com.pty.pcx.util.ValidUtil;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class PcxTreasurerVouServiceImpl implements PcxTreasurerVouService {

    @Autowired
    private PcxBillDao pcxBillDao;
    @Autowired
    private PcxBasItemDao basItemDao;
    @Autowired
    private IPcxBillPlusService pcxBillPlusService;
    @Autowired
    private PcxBillPayDetailDao pcxBillPayDetailDao;
    @Autowired
    private IPcxGipVouService pcxGipVouService;

    @Override
    public CheckMsg<?> queryTreasurerVouList(PcxTreasurerVouQueryQO qo) {
        // 必传参数判空
        checkQueryVouParam(qo);
        handlerItemCodeParam(qo);
        LambdaQueryWrapper<PcxBill> queryWrapper = getQueryVouQueryWrapper(qo);
        PageInfo<PcxBill> pageInfo = PageHelper.startPage(qo.getPageIndex(), qo.getPageSize())
                .doSelectPageInfo(() -> pcxBillDao.selectList(queryWrapper));
        return CheckMsg.success(new PageResult<PcxBill>().setTotal(pageInfo.getTotal()).setResult(pageInfo.getList()));
    }

    /**
     * 根据一级事项查询下级事项
     * @param qo
     */
    private void handlerItemCodeParam(PcxTreasurerVouQueryQO qo) {
        if (StringUtil.isEmpty(qo.getOneItemCode())) {
            return;
        }
        PcxBasItem param = new PcxBasItem();
        param.setParentCode(qo.getOneItemCode());
        param.setMofDivCode(qo.getMofDivCode());
        param.setAgyCode(qo.getAgyCode());
        param.setFiscal(qo.getFiscal());
        param.setTenantId(qo.getTenantId());
        List<PcxBasItem> basItems = basItemDao.selectList(param);
        if (CollectionUtil.isEmpty(basItems)) {
            qo.setItemCodeList(Collections.singletonList(qo.getOneItemCode()));
        } else {
            qo.setItemCodeList(basItems.stream().map(PcxBasItem::getItemCode).collect(Collectors.toList()));
        }
    }

    @NotNull
    private static LambdaQueryWrapper<PcxBill> getQueryVouQueryWrapper(PcxTreasurerVouQueryQO qo) {
        LambdaQueryWrapper<PcxBill> queryWrapper = Wrappers.lambdaQuery(PcxBill.class);
        if (Objects.nonNull(qo.getIsVou())) {
            queryWrapper.eq(PcxBill::getIsVou, qo.getIsVou());
        }
        if (!CollectionUtil.isEmpty(qo.getIds())) {
            queryWrapper.in(PcxBill::getId, qo.getId());
        }
        if (StringUtil.isNotEmpty(qo.getMofDivCode())) {
            queryWrapper.eq(PcxBill::getMofDivCode, qo.getMofDivCode());
        }
        if (StringUtil.isNotEmpty(qo.getAgyCode())) {
            queryWrapper.eq(PcxBill::getAgyCode, qo.getAgyCode());
        }
        if (StringUtil.isNotEmpty(qo.getDepartmentCode())) {
            queryWrapper.eq(PcxBill::getDepartmentCode, qo.getDepartmentCode());
        }
        if (StringUtil.isNotEmpty(qo.getBillFuncCode())) {
            queryWrapper.eq(PcxBill::getBillFuncCode, qo.getBillFuncCode());
        }
        if (StringUtil.isNotEmpty(qo.getFiscal())) {
            queryWrapper.eq(PcxBill::getFiscal, qo.getFiscal());
        }
        if (StringUtil.isNotEmpty(qo.getTenantId())) {
            queryWrapper.eq(PcxBill::getTenantId, qo.getTenantId());
        }
        if (StringUtil.isNotEmpty(qo.getItemCode())) {
            queryWrapper.eq(PcxBill::getItemCode, qo.getItemCode());
        }
        // ItemCode以一级事项oneItemCode开头
        if (StringUtil.isNotEmpty(qo.getOneItemCode())) {
            queryWrapper.like(PcxBill::getItemCode, qo.getOneItemCode());
        }
        // 金额CheckAmt大于等于startCheckAmt
        if (Objects.nonNull(qo.getStartCheckAmt())) {
            queryWrapper.ge(PcxBill::getCheckAmt, qo.getStartCheckAmt());
        }
        // 金额CheckAmt小于等于endCheckAmt
        if (Objects.nonNull(qo.getEndCheckAmt())) {
            queryWrapper.le(PcxBill::getCheckAmt, qo.getEndCheckAmt());
        }
        queryWrapper.ne(PcxBill::getBillFuncCode, BillFuncCodeEnum.APPLY.getCode());
        queryWrapper.eq(PcxBill::getApproveStatus, BillApproveStatusEnum.APPROVED.getCode());
        // 关键字匹配：查询多个字段值是否包含关键字
        String keyword = qo.getKeyword();
        List<String> keywordFields = qo.getKeywordFields();
        if(StringUtil.isNotEmpty(keyword) && CollectionUtil.isNotEmpty(keywordFields)){
            queryWrapper.and(innerWrapper ->
                    innerWrapper.or(keywordFields.contains(PcxApprovalCondFieldEnum.BILL_NO.getCode()), e -> e.like(PcxBill::getBillNo, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.CLAIMANT_NAME.getCode()), e -> e.like(PcxBill::getClaimantName,keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.DEPARTMENT_NAME.getCode()), e -> e.like(PcxBill::getDepartmentName, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.ITEM_NAME.getCode()), e -> e.like(PcxBill::getItemName, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.TRANS_DATE.getCode()), e -> e.like(PcxBill::getTransDate, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.REASON.getCode()), e -> e.like(PcxBill::getReason, keyword))
                            .or(keywordFields.contains(PcxApprovalCondFieldEnum.CHECK_AMT.getCode()), e -> e.like(PcxBill::getCheckAmt, keyword))
            );
        }
        if(CollectionUtil.isNotEmpty(qo.getDepartmentCodeList())){
            queryWrapper.and(innerWrapper -> innerWrapper.in(PcxBill::getDepartmentCode, qo.getDepartmentCodeList()));
        }
        if (CollectionUtil.isNotEmpty(qo.getItemCodeList())) {
            queryWrapper.and(innerWrapper -> innerWrapper.in(PcxBill::getItemCode, qo.getItemCodeList()));
        }
        if(CollectionUtil.isNotEmpty(qo.getExpDepartmentCodeList())){
            queryWrapper.and(innerWrapper ->
                    qo.getExpDepartmentCodeList().forEach(code ->{
                        innerWrapper.or(e->e.like(PcxBill::getExpDepartmentCodes, code));
                    })
            );
        }
        if(CollectionUtil.isNotEmpty(qo.getProjectCodeList())){
            queryWrapper.and(innerWrapper ->
                    qo.getProjectCodeList().forEach(code ->{
                        innerWrapper.or(e->e.like(PcxBill::getProjectCodes, code));
                    })
            );
        }
        queryWrapper.orderByDesc(PcxBill::getModifiedTime);
        return queryWrapper;
    }

    private static void checkQueryVouParam(PcxTreasurerVouQueryQO qo) {
        ValidUtil.checkEmptyObject(qo, "校验参数异常，PcxTreasurerVouQueryQO 不能为空");
        ValidUtil.checkEmptyStr(qo.getAgyCode(), "校验参数异常，单位编码不能为空");
        ValidUtil.checkEmptyStr(qo.getMofDivCode(), "校验参数异常，区划编码不能为空");
        ValidUtil.checkEmptyStr(qo.getFiscal(), "校验参数异常，年度不能为空");
        ValidUtil.checkEmptyObject(qo.getIsVou(), "校验参数异常，是否以记账is_vou不能为空");
        ValidUtil.checkEmptyStr(qo.getUserCode(), "校验参数异常，用户编码userCode不能为空");
        if (CollectionUtil.isEmpty(qo.getKeywordFields())) {
            qo.setKeywordFields(PcxApprovalCondFieldEnum.getAllCode());
        }
    }

    @Override
    public CheckMsg<?> treasurerVou(PcxTreasurerVouQO qo) {
        checkTreasurerVouParam(qo);
        PcxGenVouDTO pcxGenVouDTO = getPcxGenVouDTO(qo);// 查询记账所需要的单据数据
        CheckMsg<PcxGenVouDTO> checkMsg = pcxGipVouService.handleTreasurerVou(pcxGenVouDTO);
        if (checkMsg.isSuccess()) {
            LambdaUpdateWrapper<PcxBill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.in(PcxBill::getId, qo.getBillIds());
            lambdaUpdateWrapper.set(PcxBill::getIsVou, 1);
            lambdaUpdateWrapper.set(PcxBill::getVouId, checkMsg.getData().getVouId());
            lambdaUpdateWrapper.set(PcxBill::getVouNo, checkMsg.getData().getVouNo());
            lambdaUpdateWrapper.set(PcxBill::getVouDate, checkMsg.getData().getVouDate());
            pcxBillPlusService.update(lambdaUpdateWrapper);
            return CheckMsg.success(checkMsg.getData());
        }
        return checkMsg;
    }

    private void checkTreasurerVouParam(PcxTreasurerVouQO qo) {
        ValidUtil.checkEmptyObject(qo, "校验参数异常，PcxTreasurerVouQO 不能为空");
        if (ObjectUtil.isEmpty(qo.getIsSumVou()) || qo.getIsSumVou() == 0) {
            ValidUtil.checkEmptyStr(qo.getBillId(), "校验参数异常，billId 不能为空");
        } else {
            ValidUtil.checkEmptyCollection(qo.getBillIds(), "校验参数异常，billIds 不能为空");
        }
        ValidUtil.checkEmptyStr(qo.getUserCode(), "校验参数异常，userCode 不能为空");
        ValidUtil.checkEmptyStr(qo.getLoginDate(), "校验参数异常，loginDate 不能为空");
        ValidUtil.checkEmptyStr(qo.getAgyCode(), "校验参数异常，agyCode 不能为空");
        ValidUtil.checkEmptyObject(qo.getFiscal(), "校验参数异常，fiscal 不能为空");
        ValidUtil.checkEmptyStr(qo.getMofDivCode(), "校验参数异常，mofDivCode 不能为空");
//        ValidUtil.checkEmptyStr(qo.getTenantId(), "校验参数异常，tenantId 不能为空");
    }

    /**
     * 查询记账所需要的单据数据，如还需要其他数据在该方法里增加
     */
    private PcxGenVouDTO getPcxGenVouDTO(PcxTreasurerVouQO qo) {
        if (ObjectUtil.isEmpty(qo.getIsSumVou()) || qo.getIsSumVou() == 0) {
            qo.setBillIds(Collections.singletonList(qo.getBillId()));
        }
        LambdaQueryWrapper<PcxBill> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(PcxBill::getId, qo.getBillIds());
        List<PcxBill> pcxBills = pcxBillDao.selectList(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(pcxBills)) {
            throw new CommonException("查询PcxBill无数据导致异常,单据ids:"+String.join(",", qo.getBillIds()));
        }
        // 检查数据是不是同一个单位的，若不是则提示无法汇总记账
        if (CollectionUtil.isNotEmpty(pcxBills) && pcxBills.stream().map(PcxBill::getAgyCode).distinct().count() > 1) {
            throw new CommonException("无法汇总记账，请选择同一单位单据");
        }
        // 检查数据是不是同一个单位的，若不是则提示无法汇总记账
        if (CollectionUtil.isNotEmpty(pcxBills) && pcxBills.stream().map(PcxBill::getBillFuncCode).distinct().count() > 1) {
            throw new CommonException("无法汇总记账，请选择同一单据类型");
        }
        List<PcxBillPayDetail> details = pcxBillPayDetailDao.selectList(new LambdaQueryWrapper<PcxBillPayDetail>()
                .in(PcxBillPayDetail::getBillId, qo.getBillIds()));
        if (CollectionUtil.isEmpty(details)) {
            throw new CommonException("查询PcxBillPayDetail无数据导致异常,单据ids:"+String.join(",", qo.getBillIds()));
        }
        return PcxGenVouDTO.builder()
                .pcxBills(pcxBills)
                .pcxBillPayDetailList(details)
                .pcxTreasurerVouQO(qo)
                .build();
    }

    @Override
    public CheckMsg<String> previewVou(PcxTreasurerVouQO qo) {
        PcxGenVouDTO pcxGenVouDTO = getPcxGenVouDTO(qo);// 查询记账所需要的单据数据
        return pcxGipVouService.handlePreviewVou(pcxGenVouDTO);
    }
}
