package com.pty.pcx.service.impl.plan;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.api.plan.PcxPlanService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;

import com.pty.pcx.common.util.PcxDateUtil;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.meeting.PcxBillExpMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.pa.impl.PcxBillNoService;
import com.pty.pcx.qo.plan.PcxPlanQO;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class PcxPlanServiceImpl implements PcxPlanService {

    @Autowired
    private PcxBillDao pcxBillDao;
    @Autowired
    private PcxBillExpTrainingDao pcxBillExpTrainingDao;
    @Autowired
    private PcxBillExpMeetingDao pcxBillExpMeetingDao;
    @Autowired
    private PcxBillExpAbroadDao pcxBillExpAbroadDao;
    @Resource
    private PcxBillNoService pcxBillNoService;
    @Autowired
    private PcxBillRelationDao pcxBillRelationDao;

    // 映射业务类型到对应的实体类
    private static final Map<String, Class<?>> TYPE_MAP = new HashMap<String, Class<?>>() {{
        put(PcxPlanCommonEnum.BusinessTypeEnum.TRAINING.getCode(), PcxBillExpTraining.class);
        put(PcxPlanCommonEnum.BusinessTypeEnum.ABROAD.getCode(), PcxBillExpAbroad.class);
        put(PcxPlanCommonEnum.BusinessTypeEnum.MEETING.getCode(), PcxBillExpMeeting.class);
    }};

    // 映射业务类型到对应的费用代码
    private static final Map<String, String> EXPENSE_CODE_MAP = new HashMap<String, String>() {{
        put(PcxPlanCommonEnum.BusinessTypeEnum.TRAINING.getCode(), PcxConstant.TRAINING_EXPENSE_30216);
        put(PcxPlanCommonEnum.BusinessTypeEnum.ABROAD.getCode(), PcxConstant.ABROAD_EXPENSE_30212);
        put(PcxPlanCommonEnum.BusinessTypeEnum.MEETING.getCode(), PcxConstant.MEETING_EXPENSE_30215);
    }};

    // 映射业务类型到对应的费用名称
    private static final Map<String, String> EXPENSE_NAME_MAP = new HashMap<String, String>() {{
        put(PcxPlanCommonEnum.BusinessTypeEnum.TRAINING.getCode(), PcxConstant.TRAINING_EXPENSE_30216_NAME);
        put(PcxPlanCommonEnum.BusinessTypeEnum.ABROAD.getCode(), PcxConstant.ABROAD_EXPENSE_30212_NAME);
        put(PcxPlanCommonEnum.BusinessTypeEnum.MEETING.getCode(), PcxConstant.MEETING_EXPENSE_30215_NAME);
    }};

    @Override
    public CheckMsg<?> getList(PcxPlanQO qo) {
        // 参数校验（精准提示）
        String missingField = validateDeleteParams(qo, false);
        if (missingField != null) {
            return CheckMsg.fail("参数错误：" + missingField + "不能为空");
        }

        PcxPlanCommonEnum.BusinessTypeEnum planTypeEnum = PcxPlanCommonEnum.BusinessTypeEnum.getByCode(qo.getPlanType());
        // 处理未知业务类型
        if (null == planTypeEnum) {
            return CheckMsg.fail("未知的计划类型：" + qo.getPlanType());
        }

        List<PcxBill> pcxBills = getPcxBills(qo);

        if (CollectionUtil.isEmpty(pcxBills)) {
            return CheckMsg.success(Collections.emptyList());
        }
        List<String> billIds = pcxBills.stream().map(PcxBill::getId).collect(Collectors.toList());
        return getPlanByPlanType(qo, planTypeEnum, billIds, pcxBills);

    }

    /**
     * 根据单据ID获取计划信息
     *
     * @param qo 查询条件对象，包含账单ID等信息
     * @return CheckMsg<?> 返回结果消息对象，包含以下情况：
     *         1. 如果查询失败，直接返回失败消息
     *         2. 如果查询结果为空，返回空结果
     *         3. 如果数据类型不合法，返回失败消息
     *         4. 如果结果列表为空，返回空列表
     *         5. 成功时返回结果列表中的第一个元素
     */
    @Override
    public CheckMsg<?> getPlanByBillId(PcxPlanQO qo) {
        // 获取查询结果列表
        CheckMsg<?> list = getList(qo);

        // 处理查询失败或数据为空的情况
        if (!list.isSuccess()) {
            return list;
        }

        if (list.getData() == null) {
            return list;
        }

        // 验证数据类型是否为List
        if (!(list.getData() instanceof List)) {
            return CheckMsg.fail("Invalid data type");
        }

        List<?> dataList = (List<?>) list.getData();

        // 处理空列表情况
        if (CollectionUtil.isEmpty(dataList)) {
            return CheckMsg.success(Collections.emptyList());
        }

        // 返回列表中的第一个元素
        return CheckMsg.success(dataList.get(0));
    }


    /**
     * 查询计划单
     * @param qo
     * @return
     */
    private List<PcxBill> getPcxBills(PcxPlanQO qo) {
        // 查询PcxBill表
        LambdaQueryWrapper<PcxBill> queryWrapper = Wrappers.lambdaQuery(PcxBill.class)
                .eq(PcxBill::getFiscal, qo.getFiscal())
                .eq(PcxBill::getMofDivCode, qo.getMofDivCode())
                .eq(PcxBill::getAgyCode, qo.getAgyCode())
                .eq(PcxBill::getBillFuncCode, BillFuncCodeEnum.PLAN.getCode())
                .eq(PcxBill::getApproveStatus, BillApproveStatusEnum.APPROVED.getCode());

        // 动态添加billId条件，如果qo.getBillId()不为空
        if (StringUtil.isNotEmpty(qo.getBillId())) {
            queryWrapper.eq(PcxBill::getId, qo.getBillId());
        }
        return pcxBillDao.selectList(queryWrapper);
    }

    private CheckMsg<?> getPlanByPlanType(PcxPlanQO qo, PcxPlanCommonEnum.BusinessTypeEnum planTypeEnum, List<String> billIds, List<PcxBill> pcxBills) {
        switch (planTypeEnum) {
            case TRAINING:
                List<PcxBillExpTraining> billExpTrainingList = pcxBillExpTrainingDao.selectList(Wrappers.lambdaQuery(PcxBillExpTraining.class)
                        .eq(PcxBillExpTraining::getAgyCode, qo.getAgyCode())
                        .eq(PcxBillExpTraining::getFiscal, qo.getFiscal())
                        .eq(PcxBillExpTraining::getMofDivCode, qo.getMofDivCode())
                        .eq(StringUtil.isNotBlank(qo.getTenantId()), PcxBillExpTraining::getTenantId, qo.getTenantId())
                        .eq(StringUtil.isNotBlank(qo.getPlanCode()), PcxBillExpTraining::getPlanCode, qo.getPlanCode())
                        .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpTraining::getBillId, billIds)
                );
                return CheckMsg.success(buildResultPlanDate(billExpTrainingList, pcxBills));
            case MEETING:
                List<PcxBillExpMeeting> expMeetings = pcxBillExpMeetingDao.selectList(Wrappers.lambdaQuery(PcxBillExpMeeting.class)
                        .eq(PcxBillExpMeeting::getAgyCode, qo.getAgyCode())
                        .eq(PcxBillExpMeeting::getFiscal, qo.getFiscal())
                        .eq(PcxBillExpMeeting::getMofDivCode, qo.getMofDivCode())
                        .eq(StringUtil.isNotBlank(qo.getTenantId()), PcxBillExpMeeting::getTenantId, qo.getTenantId())
                        .eq(StringUtil.isNotBlank(qo.getPlanCode()), PcxBillExpMeeting::getPlanCode, qo.getPlanCode())
                        .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpMeeting::getBillId, billIds)
                );
                return CheckMsg.success(buildResultPlanDate(expMeetings, pcxBills));
            case ABROAD:
                List<PcxBillExpAbroad> expAbroadList = pcxBillExpAbroadDao.selectList(Wrappers.lambdaQuery(PcxBillExpAbroad.class)
                        .eq(PcxBillExpAbroad::getAgyCode, qo.getAgyCode())
                        .eq(PcxBillExpAbroad::getFiscal, qo.getFiscal())
                        .eq(PcxBillExpAbroad::getMofDivCode, qo.getMofDivCode())
                        .eq(StringUtil.isNotBlank(qo.getTenantId()), PcxBillExpAbroad::getTenantId, qo.getTenantId())
                        .eq(StringUtil.isNotBlank(qo.getPlanCode()), PcxBillExpAbroad::getPlanCode, qo.getPlanCode())
                        .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpAbroad::getBillId, billIds)
                );
                return CheckMsg.success(buildResultPlanDate(expAbroadList, pcxBills));
            default:
                return CheckMsg.fail("未知的计划类型");
        }
    }


    @Override
    public CheckMsg<?> queryUnusedPlan(PcxPlanQO qo) {
        // 参数校验（精准提示）
        String missingField = validateDeleteParams(qo, false);
        if (missingField != null) {
            return CheckMsg.fail("参数错误：" + missingField + "不能为空");
        }

        PcxPlanCommonEnum.BusinessTypeEnum planTypeEnum = PcxPlanCommonEnum.BusinessTypeEnum.getByCode(qo.getPlanType());
        // 处理未知业务类型
        if (null == planTypeEnum) {
            return CheckMsg.fail("未知的计划类型：" + qo.getPlanType());
        }

        // 查询PcxBill表
        List<PcxBill> pcxBills = getPcxBills(qo);

        if (CollectionUtil.isEmpty(pcxBills)) {
            return CheckMsg.success(Collections.emptyList());
        }

        //过滤掉已经使用的计划
        pcxBills = filterPcxBills(qo, pcxBills);

        List<String> billIds = pcxBills.stream().map(PcxBill::getId).collect(Collectors.toList());

        return getPlanByPlanType(qo, planTypeEnum, billIds, pcxBills);
    }

    /**
     * 过滤掉已经使用的计划
     * @param qo
     * @param pcxBills
     * @return
     */
    private List<PcxBill> filterPcxBills(PcxPlanQO qo, List<PcxBill> pcxBills) {
        //查询已经使用过的计划
        List<String> relBillIds = pcxBillRelationDao.selectObjs(
                        new LambdaQueryWrapper<PcxBillRelation>()
                                .eq(PcxBillRelation::getAgyCode, qo.getAgyCode())
                                .eq(PcxBillRelation::getFiscal, qo.getFiscal())
                                .eq(PcxBillRelation::getMofDivCode, qo.getMofDivCode())
                                .eq(StringUtil.isNotBlank(qo.getTenantId()), PcxBillRelation::getTenantId, qo.getTenantId())
                                .eq(PcxBillRelation::getRelBillFuncCode, BillFuncCodeEnum.PLAN.getCode())
                                .select(PcxBillRelation::getRelBillId) // 只查询 relBillId 字段
                ).stream()
                .map(obj -> (String) obj) // 转换为 String 类型
                .collect(Collectors.toList());

        if(CollectionUtil.isNotEmpty(relBillIds)){
            pcxBills = pcxBills.stream()
                    .filter(pcxBill -> !relBillIds.contains(pcxBill.getId()))
                    .collect(Collectors.toList());
        }
        return pcxBills;
    }


    /**
     * 基于计划类型去获取dao实现
     * @param planType
     * @return
     */
    private BaseMapper<?> getDaoByPlanType(String planType) {
        switch (Objects.requireNonNull(PcxPlanCommonEnum.BusinessTypeEnum.getByCode(planType))) {
            case TRAINING: return pcxBillExpTrainingDao;
            case MEETING: return pcxBillExpMeetingDao;
            case ABROAD: return pcxBillExpAbroadDao;
            default: throw new IllegalArgumentException("未知的计划类型：" + planType);
        }
    }

    /**
     * 更新计划信息
     *
     * @param qo 包含更新信息的查询对象
     * @return 更新结果消息
     * <p>
     * 该方法主要用于根据不同的业务类型更新相应的计划信息，并处理相关的业务逻辑
     * 包括参数校验、数据查询、根据不同业务类型调用不同的DAO进行更新操作等
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> updateByQo(PcxPlanQO qo) {
        // 参数校验（精准提示）
        String missingField = validateDeleteParams(qo, true);
        if (missingField != null) {
            return CheckMsg.fail("参数错误：" + missingField + "不能为空");
        }

        PcxPlanCommonEnum.BusinessTypeEnum planTypeEnum = PcxPlanCommonEnum.BusinessTypeEnum.getByCode(qo.getPlanType());
        // 处理未知业务类型
        if (null == planTypeEnum) {
            return CheckMsg.fail("未知的计划类型：" + qo.getPlanType());
        }

        // 获取自定义字段数据，用于后续的更新操作
        Map<String, Object> customFieldsData = qo.getCustomFieldsData();
        if (null == customFieldsData || customFieldsData.isEmpty()) {
            return CheckMsg.fail("参数错误：原始数据列表不能为空");
        }

        // 查询PcxBill表，获取单据信息
        PcxBill pcxBill = pcxBillDao.selectById(qo.getBillId());
        if (pcxBill == null) {
            return CheckMsg.fail("单据不存在");
        }

        // 根据业务类型更新具体计划
        getDaoByPlanType(qo.getPlanType()).updateById(convertToEntity(customFieldsData, qo, pcxBill));
        return CheckMsg.success().setMsgInfo("更新成功");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> save(PcxPlanQO qo) {
        // 参数校验（精准提示）
        String missingField = validateDeleteParams(qo, false);
        if (missingField != null) {
            return CheckMsg.fail("参数错误：" + missingField + "不能为空");
        }
        PcxPlanCommonEnum.BusinessTypeEnum planTypeEnum = PcxPlanCommonEnum.BusinessTypeEnum.getByCode(qo.getPlanType());
        // 处理未知业务类型
        if (null == planTypeEnum) {
            return CheckMsg.fail("未知的计划类型：" + qo.getPlanType());
        }

        Map<String, Object> customFieldsData = qo.getCustomFieldsData();
        if (customFieldsData.isEmpty()) {
            return CheckMsg.fail("参数错误：原始数据列表不能为空");
        }
        //构建并存储计划单独
        PcxBill pcxBill = new PcxBill();
        buildPlanBillEntity(qo, pcxBill, customFieldsData);
        getDaoByPlanType(qo.getPlanType()).insert(convertToEntity(customFieldsData, qo, pcxBill));
        pcxBill.setReason("1");
        pcxBillDao.insert(pcxBill);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> deleteByQo(PcxPlanQO qo) {
        // 参数校验（精准提示）
        String missingField = validateDeleteParams(qo, true);
        if (missingField != null) {
            return CheckMsg.fail("参数错误：" + missingField + "不能为空");
        }

        PcxPlanCommonEnum.BusinessTypeEnum planTypeEnum = PcxPlanCommonEnum.BusinessTypeEnum.getByCode(qo.getPlanType());
        // 处理未知业务类型
        if (null == planTypeEnum) {
            return CheckMsg.fail("未知的计划类型：" + qo.getPlanType());
        }

        // 查询验证单据存在性
        PcxBill pcxBill = pcxBillDao.selectOne(Wrappers.lambdaQuery(PcxBill.class)
                .eq(PcxBill::getId, qo.getBillId())
                .eq(PcxBill::getAgyCode, qo.getAgyCode())
                .eq(PcxBill::getFiscal, qo.getFiscal())
                .eq(PcxBill::getMofDivCode, qo.getMofDivCode()));
        if (ObjectUtils.isEmpty(pcxBill)) {
            return CheckMsg.fail("计划单据不存在");
        }

        // 执行删除操作
        switch (planTypeEnum) {
            case TRAINING:
                pcxBillExpTrainingDao.delete(Wrappers.lambdaQuery(PcxBillExpTraining.class)
                        .eq(PcxBillExpTraining::getBillId, qo.getBillId()));
                break;
            case MEETING:
                pcxBillExpMeetingDao.delete(Wrappers.lambdaQuery(PcxBillExpMeeting.class)
                        .eq(PcxBillExpMeeting::getBillId, qo.getBillId()));
                break;
            case ABROAD:
                pcxBillExpAbroadDao.delete(Wrappers.lambdaQuery(PcxBillExpAbroad.class)
                        .eq(PcxBillExpAbroad::getBillId, qo.getBillId()));
                break;
            default:
                break;
        }
        // 仅在成功删除明细时删除主表
        pcxBillDao.deleteById(qo.getBillId());
        return CheckMsg.success().setMsgInfo("删除计划成功");
    }


    /**
     * 验证删除操作的参数是否合法
     *
     * @param qo            删除操作的查询对象，包含需要验证的参数
     * @param isCheckBillId 是否检查单据ID，用于控制是否需要验证单据ID的非空性
     * @return 如果参数不合法，则返回相应的错误信息；如果参数合法，则返回null
     */
    private String validateDeleteParams(PcxPlanQO qo, boolean isCheckBillId) {
        // 检查qo对象是否为null，确保请求参数的完整性
        if (null == qo) {
            return "请求参数不允许为null";
        }
        // 根据isCheckBillId参数决定是否检查单据ID，确保单据ID在需要时非空
        if (StringUtil.isEmpty(qo.getBillId()) && isCheckBillId) {
            return "单据ID";
        }
        // 检查单位编码是否为空，确保单位信息的完整性
        if (StringUtil.isEmpty(qo.getAgyCode())) {
            return "单位编码";
        }
        // 检查年度是否为空，确保时间信息的完整性
        if (StringUtil.isEmpty(qo.getFiscal())) {
            return "年度";
        }
        // 检查区划编码是否为空，确保地域信息的完整性
        if (StringUtil.isEmpty(qo.getMofDivCode())) {
            return "区划编码";
        }
        // 检查计划类型是否为空，确保计划信息的完整性
        if (StringUtil.isEmpty(qo.getPlanType())) {
            return "计划类型";
        }
        // 所有参数验证通过，返回null表示参数合法
        return null;
    }

    /**
     * 将给定的源列表转换为指定类型的实体对象
     * 此方法主要用于将从数据库或其他来源获取的数据列表转换为具有特定业务逻辑的实体对象
     *
     * @param source  原始数据列表，期望是一个包含多个属性映射的列表，每个映射代表一个数据记录
     * @param qo      查询对象，包含需要转换的数据的计划类型信息
     * @param pcxBill PcxBill对象，提供转换过程中需要的额外信息，如账单ID
     * @param <T>     转换后的实体对象类型，由调用者指定
     * @return 转换后的实体对象，具体类型由调用者指定
     * @throws IllegalArgumentException 如果源列表为空、计划类型未知或不支持转换为实体对象
     * @throws RuntimeException         如果数据转换过程中发生任何错误
     */
    private <T> T convertToEntity(Map<String, Object> source, PcxPlanQO qo, PcxBill pcxBill) {
        try {
            // 根据业务类型获取对应的实体类
            Class<?> clazz = TYPE_MAP.get(qo.getPlanType());
            if (clazz == null) {
                throw new IllegalArgumentException("不支持的计划类型：" + qo.getPlanType());
            }
            // 将源列表转换为指定类型的实体对象
            Object entity = JSONObject.parseObject(JSONObject.toJSONString(source), clazz);
            if (entity instanceof PcxBillExpBase) {
                // 对转换后的实体对象进行进一步处理
                PcxBillExpBase baseEntity = (PcxBillExpBase) entity;
                baseEntity.setId(StringUtil.isEmpty(baseEntity.getId()) ? IDGenerator.id() : baseEntity.getId());
                baseEntity.setBillId(pcxBill.getId());
                baseEntity.setExpenseCode(EXPENSE_CODE_MAP.get(qo.getPlanType()));
                baseEntity.setExpenseName(EXPENSE_NAME_MAP.get(qo.getPlanType()));
                baseEntity.setAgyCode(qo.getAgyCode());
                baseEntity.setFiscal(qo.getFiscal());
                baseEntity.setMofDivCode(qo.getMofDivCode());
                baseEntity.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
                if(entity instanceof PcxBillExpMeeting){
                    PcxBillExpMeeting meeting = (PcxBillExpMeeting) baseEntity;
                    meeting.setDuration(PcxDateUtil.calculateDays(meeting.getStartTime(),  meeting.getFinishTime()));
                }
                return (T) baseEntity;
            } else {
                // 如果转换后的对象类型不符合预期，抛出类型转换异常
                throw new ClassCastException("对象类型转换失败，预期PcxBillExpBase类型");
            }
        } catch (Exception e) {
            // 捕获并重新抛出转换过程中发生的任何异常
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 构建预算计划或预算账单实体对象
     *
     * @param qo           预算计划查询对象，包含需要构建实体的基本信息
     * @param pcxBill      预算账单对象，通过此方法进行属性赋值
     * @param customFields 自定义字段映射，用于处理特定的字段赋值
     * @return 返回构建完成的预算账单对象
     */
    private PcxBill buildPlanBillEntity(PcxPlanQO qo, PcxBill pcxBill, Map<String, Object> customFields) {
        pcxBill.setId(IDGenerator.id());
        pcxBill.setAgyCode(qo.getAgyCode());
        pcxBill.setAgyName(qo.getAgyName());
        pcxBill.setFiscal(qo.getFiscal());
        pcxBill.setMofDivCode(qo.getMofDivCode());

        //TODO 初步思考事项类型由前端传，如果最终设计不是由前端传则根据PcxPlanCommonEnum.BusinessTypeEnum 基于计划类型进行匹配（存在一对多）
        pcxBill.setItemCode(qo.getItemCode());
        pcxBill.setItemName(qo.getItemName());

        pcxBill.setBillFuncCode(FormSettingEnums.FormTypeEnum.PLAN.getCode());
        pcxBill.setBillStatus(BillStatusEnum.SAVE.getCode());
        pcxBill.setApproveStatus(BillApproveStatusEnum.APPROVED.getCode());
        pcxBill.setBillFuncName(BillFuncCodeEnum.PLAN.getName());
        pcxBill.setExpenseCodes(EXPENSE_CODE_MAP.get(qo.getPlanType()));
        pcxBill.setExpenseNames(EXPENSE_NAME_MAP.get(qo.getPlanType()));
        pcxBill.setTransDate(DateUtil.getCurDate());
        pcxBill.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
        // 生成并设置账单编号
        pcxBill.setBillNo(pcxBillNoService.getBillNo(qo.getMofDivCode(), qo.getAgyCode(), FormSettingEnums.FormTypeEnum.PLAN.getCode(), BeanUtil.beanToMap(pcxBill)));
        pcxBill.setCreator(qo.getUserCode());
        pcxBill.setCreatorName(qo.getUserName());
        pcxBill.setCreatedTime(DateUtil.nowTime());
        //设置为当前日期
        pcxBill.setTransDate(DateUtil.getCurDate());
        // 判断是否存在项目（存在则组装赋值）  字段目前固定
        if (customFields.containsKey("projectCodes") && customFields.containsKey("projectNames")) {
            pcxBill.setProjectCodes((String) customFields.get("projectCodes"));
            pcxBill.setProjectNames((String) customFields.get("projectNames"));
        }
        // 返回构建完成的单据对象
        return pcxBill;
    }


    /** TODO 暂时保留
     * 根据明细数据和主表数据构建包含主表字段的最终结果集
     * 此方法的目的是将明细数据与主表数据进行关联，补充明细数据中缺失的主表关键字段
     *
     * @param result   明细数据列表，包含了需要补充主表字段的明细信息
     * @param pcxBills 主表数据列表，包含了主表的关键信息
     * @return 补充了主表关键字段的明细数据列表
     */
    private List<Map<String, Object>> buildResultPlanDate(List<?> result, List<PcxBill> pcxBills) {

        // 将主表数据转为billId为key的Map，以便后续快速查找
        Map<String, PcxBill> billMap = pcxBills.stream()
                .collect(Collectors.toMap(PcxBill::getId, bill -> bill));

        // 将结果列表转换为Map格式
        List<Map<String, Object>> finalResult = new ArrayList<>();

        for (Object item : result) {
            // 将实体对象转换为Map
            Map<String, Object> itemMap = BeanUtil.beanToMap(item);
            // 获取billId
            String billId = StringUtil.getStringValue(itemMap.get("billId"));
            // 检查billId是否存在且在主表Map中找到对应的主表记录
            if (StringUtil.isNotEmpty(billId) && billMap.containsKey(billId)) {
                PcxBill bill = billMap.get(billId);
                // 补充单据主表关键字段
                itemMap.put("billNo", bill.getBillNo());
                itemMap.put("itemCode", bill.getItemCode());
                itemMap.put("itemName", bill.getItemName());
                itemMap.put("billStatus", bill.getBillStatus());
            }
            finalResult.add(itemMap);
        }
        return finalResult;

    }
}
