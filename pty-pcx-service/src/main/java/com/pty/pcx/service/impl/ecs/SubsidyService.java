package com.pty.pcx.service.impl.ecs;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.pty.mad.common.DaysType;
import com.pty.mad.entity.MadArea;
import com.pty.pcx.api.bill.PcxBillExtraSubsidyService;
import com.pty.pcx.api.calculationrule.ICalculationRuleService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillExpDetailSourceEnum;
import com.pty.pcx.common.enu.ExtraSubsidyTypeEnum;
import com.pty.pcx.common.enu.NoEcsReasonEnum;
import com.pty.pcx.dao.bill.PcxBillExtraAttachDao;
import com.pty.pcx.dao.bill.PcxBillExtraSubsidyDao;
import com.pty.pcx.dao.bill.PcxBillTripCityDayDao;
import com.pty.pcx.dto.bill.SubsidyExtraDTO;
import com.pty.pcx.dto.calculationrule.CalculationRuleDTO;
import com.pty.pcx.dto.calculationrule.CalculationRuleReturnDTO;
import com.pty.pcx.dto.calculationrule.CalculationRuleWrap;
import com.pty.pcx.dto.calculationrule.MealAllowanceDTO;
import com.pty.pcx.dto.ecs.CalculateSubsidyResult;
import com.pty.pcx.dto.ecs.TripEmpCityDateDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.mad.IHolidaysExternalService;
import com.pty.pcx.mad.IMadAreaExternalService;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.service.impl.bill.PcxBillExtraSubsidyServiceImpl;
import com.pty.pcx.service.impl.ecs.dto.CityDateVO;
import com.pty.pcx.service.impl.ecs.dto.CollectTripDto;
import com.pty.pcx.service.impl.ecs.dto.DateVO;
import com.pty.pcx.service.impl.ecs.dto.EcsExpTripDTO;
import com.pty.pcx.service.impl.ecs.trip.TravelTripProcessor;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SubsidyService {

    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Resource
    private IMadAreaExternalService madAreaExternalService;
    @Resource
    private TravelTripProcessor travelTripProcessor;
    @Resource
    private ICalculationRuleService calculationRuleService;
    @Resource
    private PcxBillTripCityDayDao pcxBillTripCityDayDao;
    @Resource
    private IHolidaysExternalService iHolidaysExternalService;
    @Resource
    private PcxBillExtraSubsidyDao pcxBillExtraSubsidyDao;
    @Resource
    private PcxBillExtraAttachDao pcxBillExtraAttachDao;
    @Resource
    private PcxBillExtraSubsidyService pcxBillExtraSubsidyService;

    /**
     * 查询工作日，如果是返程，还要把最后一天算上
     * @param startTime
     * @param finishTime
     * @param includeLastDay
     * @return
     */
    public Pair<Integer, Integer> queryWorkDays(String startTime, String finishTime, boolean includeLastDay){
        LocalDate startDate = LocalDate.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate finishDate = LocalDate.parse(finishTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<DaysType> daysTypeByRange = iHolidaysExternalService.getDaysTypeByRange(startDate, finishDate);

        if (daysTypeByRange.size()>1 && !includeLastDay){
            daysTypeByRange.remove(daysTypeByRange.size()-1);
        }
        Integer days = 0;
        Integer workDays = 0;
        for (DaysType daysType : daysTypeByRange) {
            if (daysType.equals(DaysType.WEEKDAYS)){
                workDays++;
            }
            days++;
        }
        return Pair.of(days, workDays);
    }
    /**
     * 计算伙食补助，如果有行程数据，则取行程中的城市驻留天数去计算补助
     */
    public CalculateSubsidyResult calculateSubsidyByHotel(
                                                                PcxBill pcxBill,
                                                                CollectTripDto tripDto,
                                                                 List<PcxBillExpDetailBase> allExpDetailBase,
                                                                 List<PcxBillExpStandResult> standResultListCollect,
                                                                 String claimantCode,
                                                                 List<PcxBillTripCityDay> tripCityDaysCollect) {

        //如果有行程，则从行程对应城市驻留天数
        //调接口算补助
        List<PcxBillExpDetailTravel> result = new ArrayList<>();

        List<PcxBillExtraSubsidy> delExtraList = new ArrayList<>();
        List<PcxBillExtraAttach> delExtraAttachList = new ArrayList<>();
        if (CollectionUtils.isEmpty(allExpDetailBase)){
            return CalculateSubsidyResult.of(result, delExtraList, delExtraAttachList);
        }
        //查询出其他补助设置，需要把其他补助天数分配进补助的行程中
        List<PcxBillExtraSubsidy> pcxBillExtraSubsidies = pcxBillExtraSubsidyDao.selectList(Wrappers.lambdaQuery(PcxBillExtraSubsidy.class)
                .eq(PcxBillExtraSubsidy::getBillId, pcxBill.getId()));
        List<PcxBillExtraAttach> pcxBillExtraAttaches = pcxBillExtraAttachDao.selectList(Wrappers.lambdaQuery(PcxBillExtraAttach.class)
                .eq(PcxBillExtraAttach::getBillId, pcxBill.getId()));

        if (CollectionUtils.isNotEmpty(tripDto.getTripDTOList())){
            List<String> tripDetailIds = tripDto.getTripDTOList().stream().flatMap(trip -> trip.getDetailIds().stream()).collect(Collectors.toList());
            //找出城市间交通费，以第一个作为工作地
            List<PcxBillExpDetailTravel> travelDetailList = EcsExpOptService.getTravelDetailList(allExpDetailBase);
            Map<String, PcxBillExpDetailTravel> userEarlyTripMap = new HashMap<>();
            for (PcxBillExpDetailTravel travel : travelDetailList) {
                if (EcsExpOptService.isIntercityTraffic(travel) && tripDetailIds.contains(travel.getId())){
                    PcxBillExpDetailTravel earlyTraffic = userEarlyTripMap.get(travel.getEmpCode());
                    if (Objects.isNull(earlyTraffic) || travel.getStartTime().compareTo(earlyTraffic.getStartTime()) < 0){
                        userEarlyTripMap.put(travel.getEmpCode(), travel);
                    }
                }
            }
            List<TripEmpCityDateDTO> cityDateResult = getCitySubsidyDate(tripDto.getTripDTOList(), userEarlyTripMap);
            if (CollectionUtils.isNotEmpty(cityDateResult)){
                //填充额外天数
                fillExtraDaysAndCollectDelExtra(cityDateResult, pcxBillExtraSubsidies, pcxBillExtraAttaches, delExtraList, delExtraAttachList);
                //转换收集城市驻留天数
                collectTripCityDay(tripCityDaysCollect, cityDateResult);
                log.info("calculateSubsidy::cityDateResult::{}", JSON.toJSONString(cityDateResult));
                cityDateResult = cityDateResult.stream()
                        .filter(item->Objects.equals(item.getEmpType(), PcxConstant.EMP_TYPE_INNER)
                                || Objects.equals(item.getIsSubsidy(), 1)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cityDateResult)){
                    result.addAll(calculateSubsidyByEmpCityDateVO(pcxBill, cityDateResult, travelDetailList.get(0), standResultListCollect, claimantCode, userEarlyTripMap));
                }
            }else{
                //如果没有计算补助的城市驻留天数，则把额外天数，和附件信息都删除
                delExtraList.addAll(pcxBillExtraSubsidies);
                delExtraAttachList.addAll(pcxBillExtraAttaches);
            }
        }else{
            //如果没有计算补助的城市驻留天数，则把额外天数，和附件信息都删除
            delExtraList.addAll(pcxBillExtraSubsidies);
            delExtraAttachList.addAll(pcxBillExtraAttaches);
        }
        return CalculateSubsidyResult.of(result, delExtraList, delExtraAttachList);
    }


    private void fillExtraDaysAndCollectDelExtra(List<TripEmpCityDateDTO> cityDateResult,
                                                 List<PcxBillExtraSubsidy> pcxBillExtraSubsidies,
                                                 List<PcxBillExtraAttach> pcxBillExtraAttaches,
                                                 List<PcxBillExtraSubsidy> delExtraList,
                                                 List<PcxBillExtraAttach> delExtraAttachList) {
        if (CollectionUtils.isEmpty(pcxBillExtraSubsidies)){
            return;
        }
        //校验其他补助的日期，是否包含在行程内，如果不包含，就把其他补助删除
        List<PcxBillExtraSubsidy> delExtraSubsidies = pcxBillExtraSubsidyService.checkExtraSubsidyDate(cityDateResult, pcxBillExtraSubsidies);
        delExtraList.addAll(delExtraSubsidies);
        pcxBillExtraSubsidies.removeAll(delExtraSubsidies);
        
        //如果还有剩余的额外天数，则保留它们能匹配的额外附件
        if (CollectionUtils.isNotEmpty(pcxBillExtraSubsidies)){
            Map<String, List<TripEmpCityDateDTO>> empCityMap = cityDateResult.stream().collect(Collectors.groupingBy(TripEmpCityDateDTO::getEmpCode));
            //把其他补助天数分配进行程中
            for (PcxBillExtraSubsidy extraSubsidy : pcxBillExtraSubsidies) {
                ExtraSubsidyTypeEnum extraEnum = ExtraSubsidyTypeEnum.getEnumByCode(extraSubsidy.getExtraType());
                Set<String> dateSet = PcxBillExtraSubsidyServiceImpl.transExtraDate(extraSubsidy.getExtraDate(), extraEnum);
                Iterator<String> iterator = dateSet.iterator();
                List<TripEmpCityDateDTO> empCityDateList = empCityMap.get(extraSubsidy.getEmpCode());
                if (CollectionUtils.isEmpty(empCityDateList)){
                    continue;
                }
                while (iterator.hasNext()) {
                    String date = iterator.next();
                    for (TripEmpCityDateDTO newCityDay : empCityDateList) {
                        if (newCityDay.getStartTime().compareTo(date) <= 0 && newCityDay.getFinishTime().compareTo(date) >= 0) {
                            switch (extraEnum) {
                                case EXTRA_WORK:
                                    newCityDay.setWorkExtraDays(newCityDay.getWorkExtraDays() + 1);
                                    break;
                                case MANAGE:
                                    newCityDay.setManagerDays(newCityDay.getManagerDays() + 1);
                                    break;
                            }
                            iterator.remove();
                            break;
                        }
                    }
                }
            }
            List<String> extraAttachKeys =pcxBillExtraSubsidies.stream().map(item-> String.format("%s-%s", item.getEmpCode(), item.getExtraType())).collect(Collectors.toList());
            delExtraAttachList.addAll(pcxBillExtraAttaches.stream().filter(item->!extraAttachKeys.contains(String.format("%s-%s", item.getEmpCode(), item.getExtraType()))).collect(Collectors.toList()));
        }else{
            delExtraAttachList.addAll(pcxBillExtraAttaches);
        }
    }

    private void collectTripCityDay(List<PcxBillTripCityDay> tripCityDaysCollect, List<TripEmpCityDateDTO> cityDateResult) {
        for (TripEmpCityDateDTO vo : cityDateResult) {
            PcxBillTripCityDay cityDay = new PcxBillTripCityDay();
            cityDay.setId(IDGenerator.id());
            cityDay.setCityCode(vo.getCityCode());
            cityDay.setEmpCode(vo.getEmpCode());
            cityDay.setCityDays(vo.getTravelDays());
            cityDay.setWorkDays(vo.getWorkDays());
            cityDay.setDormDays(vo.getDormDays());
            cityDay.setWorkDormDays(vo.getWorkDormDays());
            cityDay.setTripId(vo.getTripId());
            cityDay.setStartTime(vo.getStartTime());
            cityDay.setFinishTime(vo.getFinishTime());
            cityDay.setWorkExtraDays(vo.getWorkExtraDays());
            cityDay.setManagerDays(vo.getManagerDays());
            cityDay.setHotelDays(vo.getHotelDays());
            cityDay.setWorkHotelDays(vo.getWorkHotelDays());
            cityDay.setWorkHotelDays(vo.getWorkHotelDays());
            tripCityDaysCollect.add(cityDay);
        }
    }

    private List<TripEmpCityDateDTO> getCitySubsidyDate(List<EcsExpTripDTO> tripDTOList, Map<String, PcxBillExpDetailTravel> empMap) {
        List<TripEmpCityDateDTO> result = new ArrayList<>();
        for (EcsExpTripDTO tripDTO : tripDTOList) {
            String tripId = tripDTO.getId();
            //行程中，人员的城市驻留时间，住宿时间
            Map<String, Map<String, List<DateVO>>> empCityHotelDateMap = tripDTO.getEmpCityHotelDateMap();
            if (Objects.nonNull(tripDTO.getEmpCityDateMap()) && !tripDTO.getEmpCityDateMap().isEmpty()){
                tripDTO.getEmpCityDateMap().forEach((empCode, cityDateVOList) -> {
                            Map<String, List<DateVO>> empCityHotelMap = empCityHotelDateMap.get(empCode);
                            //收集城市驻留时间，包含出差天数，工作日天数，住宿舍天数，住酒店天数
                            collectTripEmpCityDateDTO(empCode, cityDateVOList, empCityHotelMap, result, tripId);
                        }
                );
            }
        }
        Map<String, TripEmpCityDateDTO> tripMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(result)){
            for (TripEmpCityDateDTO vo : result) {
                String key = vo.getKey();
                PcxBillExpDetailTravel travel = empMap.get(vo.getEmpCode());
                if (Objects.nonNull(travel)){
                    vo.setBudLevel(travel.getBudLevel());
                    vo.setIsSubsidy(Objects.isNull(travel.getIsSubsidy()) ? 1 : travel.getIsSubsidy());
                    //内部员工，默认为有补助
                    if (Objects.equals(travel.getEmpType(), PcxConstant.EMP_TYPE_INNER)){
                        vo.setIsSubsidy(1);
                    }
                    vo.setEmpType(travel.getEmpType());
                    vo.setEmpName(travel.getEmpName());
                }
                tripMap.put(key, vo);
            }
        }
        return new ArrayList<>(tripMap.values());
    }

    private void collectTripEmpCityDateDTO(String empCode,
                                          List<CityDateVO> cityDateVOList,
                                          Map<String, List<DateVO>> empCityHotelMap,
                                          List<TripEmpCityDateDTO> result,
                                          String tripId){
        //先反向遍历人员在城市的驻留日期，如果存在相同开始时间+结束时间,则去掉前面的，目的就是在同一天在两个地方出现，取最后出现的城市计算补助
        Collections.reverse(cityDateVOList);
        Set<String> dateKey = new HashSet<>();
        Iterator<CityDateVO> iterator = cityDateVOList.iterator();
        while (iterator.hasNext()){
            CityDateVO cityDateVO = iterator.next();
            String key = String.format("%s-%s", cityDateVO.getStartDate(), cityDateVO.getEndDate());
            if (dateKey.contains(key)){
                iterator.remove();
            }else{
                dateKey.add(key);
            }
        }
        Collections.reverse(cityDateVOList);

        boolean includeLastDay = false;
        for (int i = 0; i < cityDateVOList.size(); i++) {
            TripEmpCityDateDTO vo = new TripEmpCityDateDTO();
            CityDateVO cityDateVO = cityDateVOList.get(i);
            String endDate = cityDateVO.getEndDate();
            //如果是返程，则结束时间为返程的结束时间，其他情况结束时间是T+1的出发时间
            boolean isLast = i == cityDateVOList.size()-1;
            if (isLast){
                endDate = cityDateVO.getEndFinishDate();
            }
            //最后一段驻留，天数+1，如果只有一个驻留还是当天往返，则不用+1
            if ((cityDateVOList.size() > 1 || !cityDateVO.getStartDate().equals(endDate)) && isLast){
                includeLastDay = true;
            }

            Pair<Integer, Integer> dayAndWorkDay = queryWorkDays(cityDateVO.getStartDate(), endDate, includeLastDay);
            long travelDays = dayAndWorkDay.getLeft();
            long workDays = dayAndWorkDay.getRight();
            List<DateVO> dateVOS = empCityHotelMap.getOrDefault(cityDateVO.getCityCode(), Lists.newArrayList());
            List<DateVO> dormDateVOS = dateVOS.stream().filter(item-> NoEcsReasonEnum.DORMITORY.getCode().equals(item.getNoEcsReason())).collect(Collectors.toList());
            List<DateVO> cov = TravelTripProcessor.getCoverDateList(cityDateVO, dormDateVOS);
            long dormDays = 0;
            long workDormDays = 0;
            cov.sort(Comparator.comparing(DateVO::getStartDate));
            int hotelIndex= 0;
            for (DateVO dateVO : cov) {
                //如果是最后一段驻留时间，并且住宿舍天结束时间就是离开城市的时间，则住宿舍+1
                includeLastDay = includeLastDay && hotelIndex == cov.size()-1 && Objects.equals(dateVO.getEndDate(), cityDateVO.getEndDate());
                Pair<Integer, Integer> dormDayPair = queryWorkDays(dateVO.getStartDate(), dateVO.getEndDate(), includeLastDay);
                dormDays += dormDayPair.getLeft();
                workDormDays += dormDayPair.getRight();
                hotelIndex ++;
            }

            List<DateVO> hotelDateVOS = dateVOS.stream().filter(item-> Objects.equals(item.getSource(), BillExpDetailSourceEnum.ECS.getCode())
                                            && !NoEcsReasonEnum.DORMITORY.getCode().equals(item.getNoEcsReason())).collect(Collectors.toList());
            List<DateVO> hotelCoverDates = TravelTripProcessor.getCoverDateList(cityDateVO, hotelDateVOS);
            long hotelDays = 0;
            long workHotelDays = 0;
            int hotelI= 0;
            hotelCoverDates.sort(Comparator.comparing(DateVO::getStartDate));
            for (DateVO dateVO : hotelCoverDates) {
//                includeLastDay = includeLastDay && hotelI == cov.size()-1 && Objects.equals(dateVO.getEndDate(), cityDateVO.getEndDate());
                Pair<Integer, Integer> dormDayPair = queryWorkDays(dateVO.getStartDate(), dateVO.getEndDate(), false);
                hotelDays += dormDayPair.getLeft();
                workHotelDays += dormDayPair.getRight();
//                hotelI ++;
            }

            //如果在城市驻留的天数全都是住宿舍，则住宿舍也+1一下
            vo.setEmpCode(empCode);
            vo.setCityCode(cityDateVO.getCityCode());
            vo.setTravelDays(travelDays);
            vo.setWorkDays(workDays);
            vo.setDormDays(dormDays);
            vo.setWorkDormDays(workDormDays);
            vo.setTripId(tripId);
            vo.setStartTime(cityDateVO.getStartDate());
            vo.setFinishTime(cityDateVO.getEndDate());
            vo.setHotelDays(hotelDays);
            vo.setWorkHotelDays(workHotelDays);
            result.add(vo);
        }

    }

    /**
     * 算补助
     * @param cityDateResult
     * @param oneDetail
     * @param standResultListCollect
     * @return
     */
    private List<PcxBillExpDetailTravel> calculateSubsidyByEmpCityDateVO(
                                                                         PcxBill bill,
                                                                         List<TripEmpCityDateDTO> cityDateResult,
                                                                         PcxBillExpDetailBase oneDetail,
                                                                         List<PcxBillExpStandResult> standResultListCollect,
                                                                         String claimantCode,
                                                                         Map<String, PcxBillExpDetailTravel> userEarlyTripMap) {
        //找出行程人员，行程中最后一个住宿费明细，计算补助时，最后的住宿天数+1
        List<PcxBillExpDetailTravel> result = new ArrayList<>();
        Set<String> empCodeSet = new HashSet<>();
        List<String> cityCodeList = cityDateResult.stream()
                .peek(item->empCodeSet.add(item.getEmpCode()))
                .map(TripEmpCityDateDTO::getCityCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, MadArea> cityMap = getCityMap(cityCodeList);

        Map<String, MadArea> empWorkCityMap = getEmpWorkCityMap(empCodeSet,claimantCode, userEarlyTripMap, oneDetail.getMofDivCode(), oneDetail.getFiscal(), oneDetail.getAgyCode());
        Map<String, MadEmployeeDTO> empMap = getEmpMap(Lists.newArrayList(empCodeSet), oneDetail.getMofDivCode(), oneDetail.getFiscal(), oneDetail.getAgyCode());
        List<CalculationRuleDTO> queryList = new ArrayList<>();
        Map<String, TripEmpCityDateDTO> tripEmpKeyMap = new HashMap<>();
        Map<String, CalculationRuleWrap> calculateWrapMap = new HashMap<>();
        Map<String, List<TripEmpCityDateDTO>> tripEmpMap = cityDateResult
                .stream().collect(Collectors.groupingBy(item -> String.format("%s-%s", item.getTripId(), item.getEmpCode())));
        for (Map.Entry<String, List<TripEmpCityDateDTO>> entry : tripEmpMap.entrySet()) {
            List<TripEmpCityDateDTO> value = entry.getValue();
            value.sort(Comparator.comparing(TripEmpCityDateDTO::getStartTime));
            List<MealAllowanceDTO> mealAllowanceDTOS = new ArrayList<>();
            for (int i = 0; i < value.size(); i++) {
                TripEmpCityDateDTO vo = value.get(i);
                MadArea workArea = empWorkCityMap.get(vo.getEmpCode());
                MadArea travelArea = cityMap.get(vo.getCityCode());
                if (Objects.isNull(workArea) || Objects.isNull(travelArea)){
                    log.info("工作地城市信息存在{},出差城市信息存在{},不能计算补助", Objects.nonNull(workArea), Objects.nonNull(travelArea));
                    continue;
                }
                CalculationRuleDTO dto = new CalculationRuleDTO();
                dto.setAgyCode(oneDetail.getAgyCode());
                dto.setFiscal(oneDetail.getFiscal());
                dto.setMofDivCode(oneDetail.getMofDivCode());
                dto.setExpenseType(PcxConstant.TRAVEL_DETAIL_3021103);
                dto.setKey(vo.getKey());
                tripEmpKeyMap.put(vo.getKey(), vo);
                MealAllowanceDTO mealAllowanceDTO = new MealAllowanceDTO();
                mealAllowanceDTOS.add(mealAllowanceDTO);
                dto.setMealAllowanceDTO(mealAllowanceDTO);
                mealAllowanceDTO.setDepartureCity(travelArea.getAreaCode());
                mealAllowanceDTO.setDepartureProvince(getProvinceCode(travelArea.getParentCode()));
                mealAllowanceDTO.setWorkplaceCity(workArea.getAreaCode());
                mealAllowanceDTO.setWorkplaceProvince(getProvinceCode(workArea.getParentCode()));
                mealAllowanceDTO.setTravelDays(vo.getTravelDays()+"");
                mealAllowanceDTO.setDormDays(vo.getDormDays()+"");
                mealAllowanceDTO.setWorkDays(vo.getWorkDays()+"");
                mealAllowanceDTO.setWorkDormDays(vo.getWorkDormDays()+"");
                mealAllowanceDTO.setStayType("0");
                mealAllowanceDTO.setBudLevel(vo.getBudLevel());
                mealAllowanceDTO.setExtraSubsidy("1");
                mealAllowanceDTO.setHolidayWorkDays(vo.getWorkExtraDays()+"");
                mealAllowanceDTO.setPmDays("0");
                mealAllowanceDTO.setTravelType("0");
                MadEmployeeDTO madEmployeeDTO = empMap.get(vo.getEmpCode());
                if (Objects.nonNull(madEmployeeDTO)){
                    mealAllowanceDTO.setDepartmentCode(madEmployeeDTO.getDepartmentCode());
                    mealAllowanceDTO.setJobLevel(madEmployeeDTO.getJobLevel());
                }
                CalculationRuleWrap wrap = new CalculationRuleWrap();
                BeanUtils.copyProperties(dto, wrap);
                calculateWrapMap.put(wrap.getKey(), wrap);
                queryList.add(dto);
            }
            //最后一个省外行程减0.5天
            for (int i = mealAllowanceDTOS.size() - 1; i >= 0; i--) {
                MealAllowanceDTO mealAllowanceDTO = mealAllowanceDTOS.get(i);
                if (!Objects.equals(mealAllowanceDTO.getWorkplaceProvince(), mealAllowanceDTO.getDepartureProvince())){
                    mealAllowanceDTO.setTravelType("1");
                    break;
                }
            }
        }
        //补助的标准去重一下
        Set<String> standCodeSet = new HashSet<>();
        Map<String, SubsidyExtraDTO> subsidyExtraMap = new HashMap<>();
        //第一次查所有补助和出差节假日加班补助
        querySubsidy(queryList, calculateWrapMap, null, standCodeSet, standResultListCollect, subsidyExtraMap);
        //第二次查所有补助和项目经理补助
        querySubsidy(queryList, calculateWrapMap, tripEmpKeyMap, standCodeSet, standResultListCollect, subsidyExtraMap);

        collect3021103(result, subsidyExtraMap, calculateWrapMap, oneDetail, bill, tripEmpKeyMap, cityMap);

        for (CalculationRuleDTO calculationRuleDTO : queryList) {
            calculationRuleDTO.setExpenseType(PcxConstant.TRAVEL_DETAIL_3021104);
            for (Map.Entry<String, CalculationRuleWrap> entry : calculateWrapMap.entrySet()) {
                CalculationRuleWrap value = entry.getValue();
                value.setBaseAmt(BigDecimal.ZERO);
                value.setExtraWorkAmt(BigDecimal.ZERO);
                value.setManagerAmt(BigDecimal.ZERO);
                value.setDormAmt(BigDecimal.ZERO);
            }
        }

        subsidyExtraMap.clear();
        querySubsidy(queryList, calculateWrapMap, null, standCodeSet, standResultListCollect, subsidyExtraMap);

        collect3021104(result, subsidyExtraMap, calculateWrapMap, oneDetail, bill, tripEmpKeyMap, cityMap);

        return result;
    }

    /**
     * 根据给定的地区代码获取对应的省份代码
     * 省份代码是通过截取地区代码的前两位得到的
     * 如果输入的地区代码为空，则返回空字符串
     *
     * @param parentCode 地区代码
     * @return 省份代码如果输入为空，则返回空字符串
     */
    private String getProvinceCode(String parentCode) {
        if (StringUtil.isEmpty(parentCode)){
            return "";
        }
        return parentCode.substring(0, 2);
    }

    private void collect3021104(List<PcxBillExpDetailTravel> result,
                                Map<String, SubsidyExtraDTO> subsidyExtraMap,
                                Map<String, CalculationRuleWrap> calculateWrapMap,
                                PcxBillExpDetailBase oneDetail,
                                PcxBill bill,
                                Map<String, TripEmpCityDateDTO> tripEmpKeyMap,
                                Map<String, MadArea> cityMap) {
        for (Map.Entry<String, CalculationRuleWrap> entry : calculateWrapMap.entrySet()) {
            String key = entry.getKey();
            SubsidyExtraDTO subsidyExtra = subsidyExtraMap.get(key);
            CalculationRuleWrap value = entry.getValue();
            BigDecimal baseAmt = value.getBaseAmt();
            if(baseAmt.compareTo(BigDecimal.ZERO) <= 0){
                continue;
            }
            PcxBillExpDetailTravel travel = new PcxBillExpDetailTravel();
            travel.setId(IDGenerator.id());
            travel.setFiscal(oneDetail.getFiscal());
            travel.setMofDivCode(oneDetail.getMofDivCode());
            travel.setAgyCode(oneDetail.getAgyCode());
            travel.setTenantId(oneDetail.getTenantId());
            travel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021104);
            travel.setDepartmentCode(bill.getDepartmentCode());
            travel.setDepartmentName(bill.getDepartmentName());
            TripEmpCityDateDTO vo = tripEmpKeyMap.get(key);
            travel.setTravelDays(vo.getTravelDays());
            travel.setDormDays(vo.getDormDays());
            travel.setWorkDays(vo.getWorkDays());
            travel.setWorkDormDays(vo.getWorkDormDays());
            travel.setInputAmt(baseAmt);
            travel.setCheckAmt(baseAmt);
            travel.setEcsAmt(baseAmt);
            travel.setBaseAmt(baseAmt);
            travel.setStartTime(vo.getStartTime());
            travel.setFinishTime(vo.getFinishTime());
            //记录周末加班天数，项目经理天数，周末加班补助，项目经理补助，总的额外补助，住宿舍补助
            travel.setTripId(vo.getTripId());
            travel.setHotelDays(vo.getHotelDays());
            travel.setWorkHotelDays(vo.getWorkHotelDays());
            MadArea madArea = cityMap.get(vo.getCityCode());
            if (Objects.nonNull(madArea)){
                travel.setEndCity(madArea.getAreaName());
            }

            if (Objects.nonNull(subsidyExtra)){
                travel.setSubsidyExtra(JSON.toJSONString(subsidyExtra));
            }
            travel.setEndCityCode(vo.getCityCode());
            travel.setEmpName(vo.getEmpName());
            travel.setEmpCode(vo.getEmpCode());
            travel.setEmpType(vo.getEmpType());
            travel.setBudLevel(vo.getBudLevel());
            travel.setExpenseId(oneDetail.getExpenseId());
            travel.setBillId(oneDetail.getBillId());
            travel.setSource(BillExpDetailSourceEnum.ECS.getCode());

            result.add(travel);
        }
    }

    private void collect3021103(List<PcxBillExpDetailTravel> result,
                                Map<String, SubsidyExtraDTO> subsidyExtraMap,
                                Map<String, CalculationRuleWrap> calculateWrapMap,
                                PcxBillExpDetailBase oneDetail,
                                PcxBill bill,
                                Map<String, TripEmpCityDateDTO> tripEmpKeyMap,
                                Map<String, MadArea> cityMap) {
        for (Map.Entry<String, CalculationRuleWrap> entry : calculateWrapMap.entrySet()) {
            String key = entry.getKey();
            SubsidyExtraDTO subsidyExtra = subsidyExtraMap.get(key);
            CalculationRuleWrap value = entry.getValue();
            BigDecimal baseAmt = value.getBaseAmt();
            BigDecimal extraWorkAmt = value.getExtraWorkAmt();
            BigDecimal managerAmt = value.getManagerAmt();
            BigDecimal dormAmt = value.getDormAmt();
            BigDecimal inputAmt = baseAmt.add(extraWorkAmt).add(managerAmt).add(dormAmt);
            PcxBillExpDetailTravel travel = new PcxBillExpDetailTravel();
            travel.setId(IDGenerator.id());
            travel.setFiscal(oneDetail.getFiscal());
            travel.setMofDivCode(oneDetail.getMofDivCode());
            travel.setAgyCode(oneDetail.getAgyCode());
            travel.setTenantId(oneDetail.getTenantId());
            travel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021103);
            travel.setDepartmentCode(bill.getDepartmentCode());
            travel.setDepartmentName(bill.getDepartmentName());
            TripEmpCityDateDTO vo = tripEmpKeyMap.get(key);
            travel.setTravelDays(vo.getTravelDays());
            travel.setDormDays(vo.getDormDays());
            travel.setWorkDays(vo.getWorkDays());
            travel.setWorkDormDays(vo.getWorkDormDays());
            travel.setInputAmt(inputAmt);
            travel.setCheckAmt(inputAmt);
            travel.setEcsAmt(inputAmt);
            travel.setWorkExtraAmt(extraWorkAmt);
            travel.setManagerAmt(managerAmt);
            travel.setExtraAmt(extraWorkAmt.add(managerAmt));
            travel.setBaseAmt(baseAmt);
            travel.setDormAmt(dormAmt);
            travel.setStartTime(vo.getStartTime());
            travel.setFinishTime(vo.getFinishTime());
            //记录周末加班天数，项目经理天数，周末加班补助，项目经理补助，总的额外补助，住宿舍补助
            travel.setTripId(vo.getTripId());
            travel.setWorkExtraDays(vo.getWorkExtraDays());
            travel.setManagerDays(vo.getManagerDays());
            travel.setHotelDays(vo.getHotelDays());
            travel.setWorkHotelDays(vo.getWorkHotelDays());
            MadArea madArea = cityMap.get(vo.getCityCode());
            if (Objects.nonNull(madArea)){
                travel.setEndCity(madArea.getAreaName());
            }
            travel.setEndCityCode(vo.getCityCode());
            travel.setEmpName(vo.getEmpName());
            travel.setEmpCode(vo.getEmpCode());
            travel.setEmpType(vo.getEmpType());
            travel.setBudLevel(vo.getBudLevel());
            travel.setExpenseId(oneDetail.getExpenseId());
            travel.setBillId(oneDetail.getBillId());
            if (Objects.nonNull(subsidyExtra)){
                travel.setSubsidyExtra(JSON.toJSONString(subsidyExtra));
            }
            travel.setSource(BillExpDetailSourceEnum.ECS.getCode());

            result.add(travel);
        }
    }

    private void querySubsidy(List<CalculationRuleDTO> queryList,
                              Map<String, CalculationRuleWrap> calculateWrapMap,
                              Map<String, TripEmpCityDateDTO> tripEmpKeyMap,
                              Set<String> standCodeSet,
                              List<PcxBillExpStandResult> standResultListCollect,
                              Map<String, SubsidyExtraDTO> subsidyExtraMap) {
        if (Objects.isNull(tripEmpKeyMap) || tripEmpKeyMap.isEmpty()){
            log.info("calculateSubsidy::queryList::{}", JSON.toJSONString(queryList));
            List<CalculationRuleReturnDTO> calculationRule = calculationRuleService.getCalculationRule(queryList);
            log.info("calculateSubsidy::calculationRule::{}", JSON.toJSONString(calculationRule));
            for (CalculationRuleReturnDTO dto : calculationRule) {
                if (CollectionUtils.isNotEmpty(dto.getStandResultList())){
                    for (PcxBillExpStandResult standResult : dto.getStandResultList()) {
                        if (!standCodeSet.contains(standResult.getStandCode())){
                            standResultListCollect.add(standResult);
                            standCodeSet.add(standResult.getStandCode());
                        }
                    }
                }
                CalculationRuleWrap wrap = calculateWrapMap.get(dto.getKey());
                SubsidyExtraDTO subsidyExtraDTO = subsidyExtraMap.computeIfAbsent(dto.getKey(), k -> new SubsidyExtraDTO());
                disposeSubsidyExtra(subsidyExtraDTO, dto);
                if (Objects.nonNull(wrap)){
                    if (Objects.nonNull(dto.getBaseSubsidy())){
                        BigDecimal baseAmt = new BigDecimal(dto.getBaseSubsidy().toString());
                        //基础补助大于0才记录，一天周末还是返程的，会计算出负的基础补助
                        if (baseAmt.compareTo(BigDecimal.ZERO)>0){
                            wrap.setBaseAmt(baseAmt);
                        }

                    }
                    if (Objects.nonNull(dto.getDormSubsidy())){
                        BigDecimal dormAmt = new BigDecimal(dto.getDormSubsidy().toString());
                        wrap.setDormAmt(dormAmt);
                    }
                    if (Objects.nonNull(dto.getHolidayWorkSubsidy())){
                        BigDecimal holidayAmt = new BigDecimal(dto.getHolidayWorkSubsidy().toString());
                        wrap.setExtraWorkAmt(holidayAmt);
                    }

                }
            }
        }else{
            for (CalculationRuleDTO calculationRuleDTO : queryList) {
                TripEmpCityDateDTO tripEmpCityDateDTO = tripEmpKeyMap.get(calculationRuleDTO.getKey());
                if (Objects.nonNull(tripEmpCityDateDTO)) {
                    calculationRuleDTO.getMealAllowanceDTO().setPmDays(tripEmpCityDateDTO.getManagerDays() + "");
                } else {
                    calculationRuleDTO.getMealAllowanceDTO().setPmDays("0");
                }
                calculationRuleDTO.getMealAllowanceDTO().setExtraSubsidy("2");
            }
            log.info("calculateSubsidy::queryList::{}", JSON.toJSONString(queryList));
            List<CalculationRuleReturnDTO> calculationRule = calculationRuleService.getCalculationRule(queryList);
            log.info("calculateSubsidy::calculationRule::{}", JSON.toJSONString(calculationRule));
            for (CalculationRuleReturnDTO dto : calculationRule) {
                if (CollectionUtils.isNotEmpty(dto.getStandResultList())){
                    for (PcxBillExpStandResult standResult : dto.getStandResultList()) {
                        if (!standCodeSet.contains(standResult.getStandCode())){
                            standResultListCollect.add(standResult);
                            standCodeSet.add(standResult.getStandCode());
                        }
                    }
                }
                CalculationRuleWrap wrap = calculateWrapMap.get(dto.getKey());
                if (Objects.nonNull(wrap)){
                    if (Objects.nonNull(dto.getPmSubsidy())){
                        BigDecimal pmAmt = new BigDecimal(dto.getPmSubsidy().toString());
                        wrap.setManagerAmt(pmAmt);
                    }
                }
            }
        }
    }

    private void disposeSubsidyExtra(SubsidyExtraDTO subsidyExtraDTO, CalculationRuleReturnDTO dto) {
        //处理基础补助计算信息
        String salesCalculationResult = dto.getSalesCalculationResult();
        if (salesCalculationResult.contains(PcxConstant.SUBSIDY_DAY_TYPE_WORK_DAY)){
            subsidyExtraDTO.setDayType(PcxConstant.SUBSIDY_DAY_TYPE_WORK_DAY);
        }else{
            subsidyExtraDTO.setDayType(PcxConstant.SUBSIDY_DAY_TYPE_TRAVEL_DAY);
        }

        if (salesCalculationResult.contains(PcxConstant.SUBSIDY_DORM_DAY_TYPE_DORM_DAY)){
            subsidyExtraDTO.setDormType(PcxConstant.SUBSIDY_DORM_DAY_TYPE_DORM_DAY);
        }else{
            subsidyExtraDTO.setDormType(PcxConstant.SUBSIDY_DORM_DAY_TYPE_WORK_DORM_DAY);
        }
        subsidyExtraDTO.setBaseSubsidy(dto.getBaseSubsidyAmt());
        subsidyExtraDTO.setDormSubsidy(dto.getDormSubsidyAmt());
        subsidyExtraDTO.setHolidaySubsidy(dto.getHolidayWorkSubsidyAmt());
        subsidyExtraDTO.setManagerSubsidy(dto.getPmSubsidyAmt());
    }

    private Map<String, MadArea> getCityMap(List<String> cityCodeList) {
        List<MadArea> madAreas = madAreaExternalService.selectByCodes(cityCodeList);
        return madAreas.stream().collect(Collectors.toMap(MadArea::getAreaCode, Function.identity()));
    }

    private Map<String,MadEmployeeDTO> getEmpMap(List<String> empCodeList, String mofDivCode, String fiscal, String agyCode){
        List<MadEmployeeDTO> madEmployeeDTOS = madEmployeeExternalService.selectByMadCodes(empCodeList, agyCode, Integer.valueOf(fiscal),mofDivCode);
        return madEmployeeDTOS.stream().collect(Collectors.toMap(MadEmployeeDTO::getEmployeeCode, Function.identity()));
    }
    private Map<String, MadArea> getEmpWorkCityMap(Set<String> empCodeList,
                                                   String claimantCode,
                                                   Map<String, PcxBillExpDetailTravel> userEarlyTripMap,
                                                   String mofDivCode, String fiscal, String agyCode) {
        return travelTripProcessor.queryEmpWorkCityMap(fiscal, agyCode, mofDivCode, userEarlyTripMap, empCodeList, claimantCode);
    }

    public List<PcxBillExpDetailTravel> generateSubsidy(List<PcxBillOuterEmp> subsidyOuter, PcxBill bill,
                                                        List<PcxBillExpDetailBase> outerTravelDetail,
                                                        List<PcxBillExpStandResult> standResultList) {
        List<String> empCodeList = subsidyOuter.stream().map(PcxBillOuterEmp::getEmpCode).collect(Collectors.toList());
        List<PcxBillTripCityDay> tripCityDays = pcxBillTripCityDayDao.selectList(new LambdaQueryWrapper<PcxBillTripCityDay>()
                .eq(PcxBillTripCityDay::getBillId, bill.getId())
                .in(PcxBillTripCityDay::getEmpCode, empCodeList));
        List<PcxBillExpDetailTravel> subsidy = new ArrayList<>();
        if (CollectionUtils.isEmpty(outerTravelDetail)){
            return subsidy;
        }
        List<PcxBillExpDetailTravel> travelDetailList = EcsExpOptService.getTravelDetailList(outerTravelDetail);
        Map<String, PcxBillExpDetailTravel> userEarlyTripMap = new HashMap<>();
        for (PcxBillExpDetailTravel travel : travelDetailList) {
            if (EcsExpOptService.isIntercityTraffic(travel) && StringUtil.isNotEmpty(travel.getTripId())){
                PcxBillExpDetailTravel earlyTraffic = userEarlyTripMap.get(travel.getEmpCode());
                if (Objects.isNull(earlyTraffic) || travel.getStartTime().compareTo(earlyTraffic.getStartTime()) < 0){
                    userEarlyTripMap.put(travel.getEmpCode(), travel);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(tripCityDays)){
            Map<String, PcxBillOuterEmp> nameMap = subsidyOuter.stream()
                    .collect(Collectors.toMap(PcxBillOuterEmp::getEmpCode, Function.identity(), (k, v) -> k));
            List<TripEmpCityDateDTO> cityDateResult = transCityDate(tripCityDays, nameMap);
            subsidy.addAll(calculateSubsidyByEmpCityDateVO(bill, cityDateResult, travelDetailList.get(0), standResultList, bill.getClaimantCode(), userEarlyTripMap));
        }
        return subsidy;
    }

    public List<PcxBillExpDetailTravel> generateSubsidyByExtra(PcxBill bill,
                                                               List<PcxBillExpDetailTravel> travelDetail,
                                                               List<PcxBillExpStandResult> standResultList,
                                                               List<PcxBillTripCityDay> tripCityDays) {
        List<PcxBillExpDetailTravel> subsidy = new ArrayList<>();
        if (CollectionUtils.isEmpty(travelDetail)){
            return subsidy;
        }
        Map<String, PcxBillExpDetailTravel> userEarlyTripMap = new HashMap<>();
        for (PcxBillExpDetailTravel travel : travelDetail) {
            if (EcsExpOptService.isIntercityTraffic(travel) && StringUtil.isNotEmpty(travel.getTripId())){
                PcxBillExpDetailTravel earlyTraffic = userEarlyTripMap.get(travel.getEmpCode());
                if (Objects.isNull(earlyTraffic) || travel.getStartTime().compareTo(earlyTraffic.getStartTime()) < 0){
                    userEarlyTripMap.put(travel.getEmpCode(), travel);
                }
            }
        }

        List<TripEmpCityDateDTO> cityDateResult = transCityDateByExtra(tripCityDays, travelDetail.get(0));
        subsidy.addAll(calculateSubsidyByEmpCityDateVO(bill, cityDateResult, travelDetail.get(0), standResultList, bill.getClaimantCode(), userEarlyTripMap));
        return subsidy;
    }

    private List<TripEmpCityDateDTO> transCityDate(List<PcxBillTripCityDay> tripCityDays, Map<String, PcxBillOuterEmp> nameMap) {
        List<TripEmpCityDateDTO> cityDateResult = new ArrayList<>();
        for (PcxBillTripCityDay tripCityDay : tripCityDays) {
            PcxBillOuterEmp outerEmp = nameMap.get(tripCityDay.getEmpCode());
            TripEmpCityDateDTO cityDateVO = new TripEmpCityDateDTO();
            cityDateVO.setEmpCode(tripCityDay.getEmpCode());
            cityDateVO.setEmpName(outerEmp.getEmpName());
            cityDateVO.setIsSubsidy(outerEmp.getIsSubsidy());
            cityDateVO.setCityCode(tripCityDay.getCityCode());
            cityDateVO.setEmpType(PcxConstant.EMP_TYPE_OUTER);
            cityDateVO.setBudLevel(outerEmp.getBudLevel());
            cityDateVO.setTravelDays(tripCityDay.getCityDays());
            cityDateVO.setDormDays(tripCityDay.getDormDays());
            cityDateVO.setWorkDays(tripCityDay.getWorkDays());
            cityDateVO.setWorkDormDays(tripCityDay.getWorkDormDays());
            cityDateVO.setWorkExtraDays(tripCityDay.getWorkExtraDays());
            cityDateVO.setManagerDays(tripCityDay.getManagerDays());
            cityDateVO.setTripId(tripCityDay.getTripId());
            cityDateVO.setStartTime(tripCityDay.getStartTime());
            cityDateVO.setFinishTime(tripCityDay.getFinishTime());
            cityDateResult.add(cityDateVO);
        }
        return cityDateResult;
    }

    private List<TripEmpCityDateDTO> transCityDateByExtra(List<PcxBillTripCityDay> tripCityDays, PcxBillExpDetailTravel travel) {
        List<TripEmpCityDateDTO> cityDateResult = new ArrayList<>();
        for (PcxBillTripCityDay tripCityDay : tripCityDays) {
            TripEmpCityDateDTO cityDateVO = new TripEmpCityDateDTO();
            cityDateVO.setEmpCode(tripCityDay.getEmpCode());
            cityDateVO.setEmpName(travel.getEmpName());
            cityDateVO.setIsSubsidy(travel.getIsSubsidy());
            cityDateVO.setCityCode(tripCityDay.getCityCode());
            cityDateVO.setEmpType(travel.getEmpType());
            cityDateVO.setBudLevel(travel.getBudLevel());
            cityDateVO.setTravelDays(tripCityDay.getCityDays());
            cityDateVO.setDormDays(tripCityDay.getDormDays());
            cityDateVO.setWorkDays(tripCityDay.getWorkDays());
            cityDateVO.setWorkDormDays(tripCityDay.getWorkDormDays());
            cityDateVO.setWorkExtraDays(tripCityDay.getWorkExtraDays());
            cityDateVO.setManagerDays(tripCityDay.getManagerDays());
            cityDateVO.setTripId(tripCityDay.getTripId());
            cityDateVO.setStartTime(tripCityDay.getStartTime());
            cityDateVO.setFinishTime(tripCityDay.getFinishTime());
            cityDateResult.add(cityDateVO);
        }
        return cityDateResult;
    }

}
