package com.pty.pcx.service.impl.bas;

import com.pty.pcx.api.bas.IPcxBasItemAuthService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasItemAuthDao;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bas.PcxBasItemAuth;
import com.pty.pcx.qo.bas.PcxBasItemAuthQO;
import com.pty.pcx.util.BatchServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Indexed
@Service
public class PcxBasItemAuthServiceImpl implements IPcxBasItemAuthService {

    @Autowired
    private PcxBasItemAuthDao pcxBasItemAuthDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    //1:人员；2:部门
    public static final String DEPARTMENT = "2";
    public static final String EMPLOYEE = "1";

    @Override
    public CheckMsg<?> saveBatch(List<PcxBasItemAuth> pcxBasItemAuthList) {
        batchServiceUtil.batchProcess(pcxBasItemAuthList, PcxBasItemAuthDao.class, PcxBasItemAuthDao::insertSelective);
        return CheckMsg.success().setMsgInfo("保存成功");

    }

    public CheckMsg<?> deleteByItemCode(PcxBasItemAuthQO pcxBasItemAuthQO) {
        pcxBasItemAuthDao.deleteByItemCode(pcxBasItemAuthQO);
        return CheckMsg.success().setMsgInfo("删除成功");
    }

    @Override
    public List<PcxBasItemAuth> selectByQO(PcxBasItemAuthQO qo) {
        return pcxBasItemAuthDao.selectByQO(qo);
    }

    public List<PcxBasItem> getOwnItem(PcxBasItemAuthQO qo) {
        return pcxBasItemAuthDao.getOwnItem(qo);
    }
}
