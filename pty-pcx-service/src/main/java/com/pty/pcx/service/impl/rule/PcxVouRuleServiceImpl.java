package com.pty.pcx.service.impl.rule;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.pty.pcx.api.rule.IPcxVouRuleService;
import com.pty.pcx.common.constant.PcxVouRuleConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.rule.VouSummaryEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.PcxUtil;
import com.pty.pcx.dao.rule.PcxVouRuleDao;
import com.pty.pcx.ecs.IEcsBillTypeExternalService;
import com.pty.pcx.entity.rule.PcxVouRule;
import com.pty.pcx.qo.rule.PcxVouRuleQO;
import com.pty.pcx.qo.rule.PcxVouSummaryAcoQO;
import com.pty.pcx.vo.rule.PcxVouRuleVO;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 财务审批(PcxFinanceApprovalServiceImpl)表服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
@Indexed
public class PcxVouRuleServiceImpl implements IPcxVouRuleService {

    @Autowired
    private PcxVouRuleDao pcxVouRuleDao;

    public static final String SPLIT_CODE = "-";

    @Autowired
    private IEcsBillTypeExternalService iEcsBillTypeExternalService;


    @Override
    public Map<String,Object> vouRuleList(PcxVouRuleQO pcxVouRuleQO) {
        Map<String,Object> resMap = new HashMap<>();
        List<PcxVouRuleVO> list = pcxVouRuleDao.select(pcxVouRuleQO);
        if (CollectionUtil.isEmpty(list)){
            return resMap;
        }
        Map<String, List<PcxVouRuleVO>> ruleMap = list.stream().collect(Collectors.groupingBy(a -> a.getDrType()+ SPLIT_CODE +a.getOrd() + SPLIT_CODE + a.getAccountAco()));
        List<PcxVouRuleVO> ruleList = new ArrayList<>();
        ruleMap.forEach((drType, rules) -> {
            PcxVouRuleVO rule = assembleAtomShowContent(rules);
            ruleList.add(rule);
        });
        resMap = ruleList.stream()
                .collect(Collectors.groupingBy(PcxVouRule::getDrType,
                        Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOrd()))),
                                ArrayList::new)));
        return resMap;
    }

    /**
     * 组装要素展示内容
     * 直接给前端拼装好要素名称，允许多选的要素，将名称放在一个集合内
     * 如："expenseTypeContent":["伙食补助费","城市间交通费","住宿费"]
     * @param rules
     * @return
     */
    private PcxVouRuleVO assembleAtomShowContent(List<PcxVouRuleVO> rules) {
        List<String> expenseDetailCodeList = new ArrayList<>();
        List<String> settlementTypeList = new ArrayList<>();
        List<String> payAccountTypeCodeList = new ArrayList<>();
        List<String> ecsBizTypeCodeList = new ArrayList<>();
        for(PcxVouRuleVO rule : rules){
            if (StringUtil.isNotEmpty(rule.getExpenseDetailCode()) && !expenseDetailCodeList.contains(rule.getExpenseDetailCode())){
                expenseDetailCodeList.add(rule.getExpenseDetailCode());
            }
            if (StringUtil.isNotEmpty(rule.getSettlementType()) && !settlementTypeList.contains(rule.getSettlementType())){
                settlementTypeList.add(rule.getSettlementType());
            }
            if (StringUtil.isNotEmpty(rule.getPayAccountTypeCode()) && !payAccountTypeCodeList.contains(rule.getPayAccountTypeCode())){
                payAccountTypeCodeList.add(rule.getPayAccountTypeCode());
            }
            if (StringUtil.isNotEmpty(rule.getEcsBizTypeCode())&& !ecsBizTypeCodeList.contains(rule.getEcsBizTypeCode())){
                ecsBizTypeCodeList.add(rule.getEcsBizTypeCode());
            }
        }
        PcxVouRuleVO rule = rules.get(0);
        rule.setExpenseTypeContent(expenseDetailCodeList);
        rule.setSettlementContent(settlementTypeList);
        rule.setPayAccountContent(payAccountTypeCodeList);
        rule.setEcsBizTypeContent(ecsBizTypeCodeList);
        rule.setExpenseDetailCode("");
        rule.setExpenseDetailName("");
        rule.setSettlementType("");
        rule.setSettlementName("");
        rule.setEcsBizTypeCode("");
        rule.setEcsBizTypeName("");
        rule.setPayAccountTypeCode("");
        rule.setPayAccountTypeName("");
        return rule;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg batchSave(PcxVouRuleQO ruleQO) {
        CheckMsg checkMsg = new CheckMsg();
        if (ObjectUtils.isEmpty(ruleQO)){
            return checkMsg.fail().setMsgInfo("请求参数为空");
        }
        try {
            PcxVouRuleQO param = new PcxVouRuleQO();
            param.setMofDivCode(ruleQO.getMofDivCode());
            param.setFiscal(ruleQO.getFiscal());
            param.setAgyCode(ruleQO.getAgyCode());
            param.setAcbCode(ruleQO.getAcbCode());
            param.setBillFuncCode(ruleQO.getBillFuncCode());
            param.setDrCr(ruleQO.getDrCr());
            param.setExpenseCode(ruleQO.getExpenseCode());
            param.setTenantId(ruleQO.getTenantId());
            pcxVouRuleDao.delByQO(param);
            if (CollectionUtil.isEmpty(ruleQO.getVouRuleList())) {
                return checkMsg.success().setMsgInfo("保存成功");
            }
            List<PcxVouRuleQO> saveAllList = new ArrayList<>();
            for (PcxVouRuleQO rule : ruleQO.getVouRuleList()) {
                String expenseTypeContent = rule.getExpenseDetailContent();
                String ecsBizTypeContent = rule.getEcsBizTypeContent();
                String settlementTypeContent = rule.getSettlementTypeContent();
                String payAccountTypeContent = rule.getPayAccountTypeContent();

                if (ruleQO.getDrCr().equals(PcxVouRuleConstant.DR)){
                    List<PcxVouRule> expenseDetails = parseContents(expenseTypeContent);
                    List<PcxVouRule> ecsBizTypes = parseContents(ecsBizTypeContent);
                    if (!expenseDetails.isEmpty() && !ecsBizTypes.isEmpty()) {
                        // 两个字段都不为空，计算笛卡尔积
                        saveAllList.addAll(generateDrCartesianProduct(expenseDetails, ecsBizTypes,rule));
                    } else if (!expenseDetails.isEmpty()) {
                        // 只有 费用明细 不为空
                        saveAllList.addAll(generateSingleFieldRules(expenseDetails, null,null,null,rule));
                    } else if (!ecsBizTypes.isEmpty()) {
                        // 只有 电子凭证类型 不为空
                        saveAllList.addAll(generateSingleFieldRules(null, ecsBizTypes,null,null,rule));
                    }else {
                        // 都为空
                        saveAllList.add(rule);
                    }
                }else {
                    List<PcxVouRule> settlementTypeDetails = parseContents(settlementTypeContent);
                    List<PcxVouRule> payAccountTypes = parseContents(payAccountTypeContent);
                    if (!settlementTypeDetails.isEmpty() && !payAccountTypes.isEmpty()) {
                        // 两个字段都不为空，计算笛卡尔积
                        saveAllList.addAll(generateCrCartesianProduct(settlementTypeDetails, payAccountTypes,rule));
                    } else if (!settlementTypeDetails.isEmpty()) {
                        // 只有结算方式不为空
                        saveAllList.addAll(generateSingleFieldRules(null,null,settlementTypeDetails, null,rule));
                    } else if (!payAccountTypes.isEmpty()) {
                        // 只有付款类型不为空
                        saveAllList.addAll(generateSingleFieldRules(null,null,null, payAccountTypes,rule));
                    }else {
                        // 都为空
                        saveAllList.add(rule);
                    }
                }

            }
            assembleSaveList(ruleQO, saveAllList);

            PcxUtil.getPartitionBatchInsertList(saveAllList).forEach(o->pcxVouRuleDao.batchInsert(o));
            checkMsg.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("PcxVouRuleServiceImpl->batchSave", e);
            return checkMsg.fail().setMsgInfo(e.getMessage());
        }
        return checkMsg;
    }

    private void assembleSaveList(PcxVouRuleQO rule, List<PcxVouRuleQO> saveAllList) {
        for (PcxVouRuleQO saveAll : saveAllList) {
            saveAll.setId(IDGenerator.UUID());
            saveAll.setFiscal(rule.getFiscal());
            saveAll.setAgyCode(rule.getAgyCode());
            saveAll.setTenantId(StringUtil.isEmpty(rule.getTenantId()) ? PtyContext.getTenantId() : rule.getTenantId());
            saveAll.setExpenseCode(rule.getExpenseCode());
            saveAll.setExpenseName(rule.getExpenseName());
            saveAll.setBillFuncCode(rule.getBillFuncCode());
            saveAll.setBillFuncName(rule.getBillFuncName());
            saveAll.setDrCr(rule.getDrCr());
            saveAll.setAcbCode(rule.getAcbCode());
            saveAll.setAcbName(rule.getAcbName());
            saveAll.setAcsCode(rule.getAcsCode());
            saveAll.setMofDivCode(rule.getMofDivCode());
            //todo scc 科目体系与是否合并逻辑，待产品确认后完善。现阶段设置为默认值
            saveAll.setAcaCode(StringUtil.isEmpty(rule.getAcaCode()) ? "fac" : rule.getAcaCode());
            saveAll.setIsMergeByFacDr(ObjectUtil.isNull(rule.getIsMergeByFacDr()) ? 1 : rule.getIsMergeByFacDr());
            saveAll.setIsMergeByFacCr(ObjectUtil.isNull(rule.getIsMergeByFacCr()) ? 1 : rule.getIsMergeByFacCr());
            saveAll.setIsMergeByBacDr(ObjectUtil.isNull(rule.getIsMergeByBacDr()) ? 1 : rule.getIsMergeByBacDr());
            saveAll.setIsMergeByBacCr(ObjectUtil.isNull(rule.getIsMergeByBacCr()) ? 1 : rule.getIsMergeByBacCr());
            saveAll.setIsMergeBySummary(ObjectUtil.isNull(rule.getIsMergeBySummary()) ? 1 : rule.getIsMergeBySummary());
        }
    }

    /**
     * 前端传入的json
     * @param content
     * @return
     */
    private List<PcxVouRule> parseContents(String content) {
        if (StringUtil.isEmpty(content)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(content, PcxVouRule.class);
    }


    /**
     * 生成笛卡尔积
     * @param expenseDetails
     * @param ecsBizTypes
     * @param sourceRule
     * @return
     */
    private List<PcxVouRuleQO> generateDrCartesianProduct(List<PcxVouRule> expenseDetails, List<PcxVouRule> ecsBizTypes, PcxVouRule sourceRule) {
        return expenseDetails.stream()
                .flatMap(expenseDetail -> ecsBizTypes.stream()
                        .map(ecsBizType -> createPcxVouRuleQO(expenseDetail, ecsBizType,null,null,sourceRule)))
                .collect(Collectors.toList());
    }

    private List<PcxVouRuleQO> generateCrCartesianProduct(List<PcxVouRule> settlementTypes, List<PcxVouRule> payAccountTypes, PcxVouRule sourceRule) {
        return settlementTypes.stream()
                .flatMap(settlementType -> payAccountTypes.stream()
                        .map(payAccountType -> createPcxVouRuleQO(null, null,settlementType,payAccountType,sourceRule)))
                .collect(Collectors.toList());
    }

    private List<PcxVouRuleQO> generateSingleFieldRules(List<PcxVouRule> expenseDetails, List<PcxVouRule> ecsBizTypes, List<PcxVouRule> settlementTypes, List<PcxVouRule> payAccountTypes, PcxVouRule sourceRule) {
        List<PcxVouRuleQO> result = new ArrayList<>();
        if (expenseDetails != null) {
            for (PcxVouRule expenseDetail : expenseDetails) {
                result.add(createPcxVouRuleQO(expenseDetail, null,null,null,sourceRule));
            }
        }
        if (ecsBizTypes != null) {
            for (PcxVouRule ecsBizType : ecsBizTypes) {
                result.add(createPcxVouRuleQO(null, ecsBizType,null,null,sourceRule));
            }
        }
        if (settlementTypes != null) {
            for (PcxVouRule settlementType : settlementTypes) {
                result.add(createPcxVouRuleQO(null, null,settlementType,null,sourceRule));
            }
        }
        if (payAccountTypes != null) {
            for (PcxVouRule payAccountType : payAccountTypes) {
                result.add(createPcxVouRuleQO(null, null,null,payAccountType,sourceRule));
            }
        }
        return result;
    }

    /**
     * 笛卡尔积创建出新的对象，并赋值
     * @param expenseDetail
     * @param ecsBizType
     * @param settlementType
     * @param payAccountType
     * @param sourceRule
     * @return
     */
    private PcxVouRuleQO createPcxVouRuleQO(PcxVouRule expenseDetail, PcxVouRule ecsBizType, PcxVouRule settlementType, PcxVouRule payAccountType, PcxVouRule sourceRule) {
        PcxVouRuleQO newRule = new PcxVouRuleQO();
        if (expenseDetail != null) {
            newRule.setExpenseDetailCode(expenseDetail.getExpenseDetailCode());
            newRule.setExpenseDetailName(expenseDetail.getExpenseDetailName());
        }
        if (ecsBizType != null) {
            newRule.setEcsBizTypeCode(ecsBizType.getEcsBizTypeCode());
            newRule.setEcsBizTypeName(ecsBizType.getEcsBizTypeName());
        }
        if (settlementType != null) {
            newRule.setSettlementType(settlementType.getSettlementType());
            newRule.setSettlementName(settlementType.getSettlementName());
        }
        if (payAccountType != null) {
            newRule.setPayAccountTypeCode(payAccountType.getPayAccountTypeCode());
            newRule.setPayAccountTypeName(payAccountType.getPayAccountTypeName());
        }
        newRule.setAccountAco(sourceRule.getAccountAco());
        newRule.setAccountAcoName(sourceRule.getAccountAcoName());
        newRule.setDrType(sourceRule.getDrType());
        newRule.setOrd(sourceRule.getOrd());
        newRule.setSummaryRuleContent(sourceRule.getSummaryRuleContent());
        return newRule;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(String id) {
        if (StringUtil.isEmpty(id)){
            throw new RuntimeException("请求参数不能为空");
        }
        pcxVouRuleDao.deleteById(id);
    }

    @Override
    public CheckMsg selectVouSummaryAndAco(List<PcxVouSummaryAcoQO> qoList){
        CheckMsg msg = new CheckMsg();
        if (CollectionUtil.isEmpty(qoList)){
            return msg.fail().setMsgInfo("请求信息为空");
        }
        PcxVouRuleQO ruleQO = new PcxVouRuleQO();
        PcxVouSummaryAcoQO vouSummaryAcoQO = qoList.get(0);
        ruleQO.setFiscal(vouSummaryAcoQO.getFiscal());
        ruleQO.setAgyCode(vouSummaryAcoQO.getAgyCode());
        ruleQO.setAcbCode(vouSummaryAcoQO.getAcbCode());
        ruleQO.setMofDivCode(vouSummaryAcoQO.getMofDivCode());
        ruleQO.setTenantId(vouSummaryAcoQO.getTenantId());
        List<PcxVouRuleVO> vouRuleList = pcxVouRuleDao.select(ruleQO); //按照借贷方分组
        if (CollectionUtil.isEmpty(vouRuleList)) {
            return msg.fail().setMsgInfo("未查询到记账规则设置，请设置对应规则");
        }
        Map<Integer, List<PcxVouRuleVO>> ruleMapByDrcr = vouRuleList.stream().collect(Collectors.groupingBy(a -> a.getDrCr()));
        List<Map<String, Object>> resultList = new ArrayList<>();
        try {
            //循环请求的报销单明细
            for (PcxVouSummaryAcoQO qo : qoList){
                Map<String, Object> resultMap = new HashMap<>();
                List<PcxVouRuleVO> drRuleList = ruleMapByDrcr.get(PcxVouRuleConstant.DR);
                List<PcxVouRuleVO> crRuleList = ruleMapByDrcr.get(PcxVouRuleConstant.CR);
                if (StringUtil.isEmpty(qo.getTableName())){
                    throw new RuntimeException("单据ID"+qo.getDetailId()+"未确认单据类型（tableName）" );
                }

                //费用单匹配借方；支付单匹配贷方
                if (qo.getTableName().equals(PcxVouRuleConstant.TABLE_NAME_EXPENSE)){
                    //费用类单据  todo 待兼容拓展要素acitem01-10
                    if (qo.getBillFuncCode().equals(BillFuncCodeEnum.EXPENSE.getCode())){
                        if (Objects.equals(PcxVouRuleConstant.DR_TYPE_TAXAMOUNT,qo.getDrType())){
                            List<PcxVouRuleVO> taxAmountRule = drRuleList.stream().filter(a -> a.getDrType().equals(PcxVouRuleConstant.DR_TYPE_TAXAMOUNT)).collect(Collectors.toList());
                            if (StringUtil.isEmpty(qo.getExpenseCode()) || StringUtil.isEmpty(qo.getEcsBizTypeCode())){
                                throw new RuntimeException("单据中缺少费用明细或电子凭证类型，请检查报销单数据内容");
                            }
                            String mapKey = qo.getExpenseCode() + SPLIT_CODE + qo.getEcsBizTypeCode();
                            Map<String, List<PcxVouRuleVO>> ruleMap = taxAmountRule.stream()
                                    .collect(Collectors.groupingBy(a -> {
                                                String expenseDetailCode = Optional.ofNullable(a.getExpenseDetailCode()).orElse(PcxVouRuleConstant.DEFAULT_GROUP);
                                                String ecsBizTypeCode = Optional.ofNullable(a.getEcsBizTypeCode()).orElse(PcxVouRuleConstant.DEFAULT_GROUP);
                                                return expenseDetailCode + SPLIT_CODE + ecsBizTypeCode;
                                            }, Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOrd()))), ArrayList::new)));
                            getResultMapList(qo, ruleMap, mapKey, resultMap, resultList,PcxVouRuleConstant.DR);
                        }else {
                            List<PcxVouRuleVO> drRule = drRuleList.stream().filter(a -> a.getDrType().equals(PcxVouRuleConstant.DR_TYPE)).collect(Collectors.toList());
                            String mapKey = qo.getExpenseCode();
                            if (StringUtil.isEmpty(qo.getExpenseCode())){
                                throw new RuntimeException("单据中缺少费用明细，请检查报销单数据内容");
                            }
                            Map<String, List<PcxVouRuleVO>> ruleMap = drRule.stream()
                                    .collect(Collectors.groupingBy(a -> Optional.ofNullable(a.getExpenseDetailCode()).orElse(PcxVouRuleConstant.DEFAULT_GROUP),
                                            Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOrd()))),
                                                    ArrayList::new)));
                            getResultMapList(qo, ruleMap, mapKey, resultMap, resultList,PcxVouRuleConstant.DR);
                        }
                    }
                    //借款单 todo 借款单后续有没有要素条件，待定
                    if (qo.getBillFuncCode().equals(BillFuncCodeEnum.LOAN.getCode()) || qo.getBillFuncCode().equals(BillFuncCodeEnum.REPAYMENT.getCode())){
                        drRuleList.sort(Comparator.comparingInt(PcxVouRuleVO::getOrd));
                        PcxVouRuleVO drVouRuleVO = drRuleList.get(0);
                        assmbaleResult(qo, resultMap, resultList, drVouRuleVO,PcxVouRuleConstant.DR);
                    }
                }else {
                    if (StringUtil.isEmpty(qo.getSettlementType()) || StringUtil.isEmpty(qo.getPayAccountTypeCode())){
                        throw new RuntimeException("单据中缺少结算方式或付款账户类型，请检查报销单数据内容");
                    }
                    String mapKey = qo.getSettlementType() + SPLIT_CODE + qo.getPayAccountTypeCode();
                    Map<String, List<PcxVouRuleVO>> ruleMap = crRuleList.stream()
                            .collect(Collectors.groupingBy(a -> {
                                        String settlementType = Optional.ofNullable(a.getSettlementType()).orElse(PcxVouRuleConstant.DEFAULT_GROUP);
                                        String payAccountTypeCode = Optional.ofNullable(a.getPayAccountTypeCode()).orElse(PcxVouRuleConstant.DEFAULT_GROUP);
                                        return settlementType + SPLIT_CODE + payAccountTypeCode;
                                    }, Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getOrd()))),ArrayList::new)));

                    getResultMapList(qo, ruleMap, mapKey, resultMap, resultList,PcxVouRuleConstant.CR);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("匹配摘要与科目失败",e);
            return msg.fail().setMsgInfo(e.getMessage());
        }
        return msg.success().setData(resultList);
    }

    @Override
    public CheckMsg<?> selectEcsBillTypeList(PcxVouRuleQO pcxVouRuleQO) {
        return iEcsBillTypeExternalService.select(pcxVouRuleQO);
    }


    private static void getResultMapList(PcxVouSummaryAcoQO qo, Map<String, List<PcxVouRuleVO>> ruleMap, String mapKey, Map<String, Object> resultMap, List<Map<String, Object>> resultList, int drCr) {
        List<PcxVouRuleVO> vouRuleVOS = new ArrayList<>();
        //通过设置的要素条件，直接匹配通过
        matchRuleByKey(ruleMap, mapKey, vouRuleVOS);
        vouRuleVOS.sort(Comparator.comparingInt(PcxVouRuleVO::getOrd));
        PcxVouRuleVO vouRuleVO = vouRuleVOS.get(0);//已排序，取第一条
        assmbaleResult(qo, resultMap, resultList,vouRuleVO,drCr);
    }

    /**
     * 单据中的要素信息，与规则设置的要素条件的key值匹配
     * 规则设置中可以设置为：不选择，默认匹配全部。对于此中情况使用默认defaultGroup分组
     * @param ruleMap
     * @param mapKey
     * @param vouRuleVOS
     */
    private static void matchRuleByKey(Map<String, List<PcxVouRuleVO>> ruleMap, String mapKey, List<PcxVouRuleVO> vouRuleVOS) {
        if (ruleMap.get(mapKey) != null ) {
            vouRuleVOS.addAll(ruleMap.get(mapKey));
        }
        //多条件时，使用“-”拼接，处理各个条件不选择，默认全部的情况
        if (mapKey.contains(SPLIT_CODE)){
            String[] parts = mapKey.split(SPLIT_CODE);
            //选了第一个条件，第二个条件默认匹配
            String firstParam = parts[0];
            if (ruleMap.get(firstParam +SPLIT_CODE+ PcxVouRuleConstant.DEFAULT_GROUP) != null ) {
                vouRuleVOS.addAll(ruleMap.get(firstParam +SPLIT_CODE+ PcxVouRuleConstant.DEFAULT_GROUP));
            }
            //选了第二个条件，第一个条件默认匹配
            String secondParam = parts[1];
            if (ruleMap.get(PcxVouRuleConstant.DEFAULT_GROUP + SPLIT_CODE+ secondParam) != null ) {
                vouRuleVOS.addAll(ruleMap.get(PcxVouRuleConstant.DEFAULT_GROUP + SPLIT_CODE+ secondParam));
            }
            if (firstParam.equals(PcxVouRuleConstant.DEFAULT_GROUP) && secondParam.equals(PcxVouRuleConstant.DEFAULT_GROUP)){
                vouRuleVOS.addAll(ruleMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
            }
        }else {
            //单条件时，如果有未选择条件的行，则全部添加到匹配列表中
            vouRuleVOS.addAll(ruleMap.get(PcxVouRuleConstant.DEFAULT_GROUP));
        }
        if (CollectionUtil.isEmpty(vouRuleVOS)){
            throw new RuntimeException("未匹配到记账规则，请检查规则配置与单据信息");
        }
    }

    /**
     * 组装返回结果
     * 其中：科目与摘要的key值，需区分财务/预算；借方/贷方
     */
    private static void assmbaleResult(PcxVouSummaryAcoQO qo, Map<String, Object> resultMap, List<Map<String, Object>> resultList, PcxVouRuleVO vouRuleVO, int drCr) {
        resultMap.put("detailId", qo.getDetailId());
        String drCrCode = drCr == PcxVouRuleConstant.DR ? "Dr" : "Cr";
        String acoKey = vouRuleVO.getAcaCode() + drCrCode + "AcoCode";
        String summaryKey = vouRuleVO.getAcaCode() + drCrCode + "Summary";
        resultMap.put(acoKey, vouRuleVO.getAccountAco());
        resultMap.put(summaryKey,getVouSummery(qo, vouRuleVO));

        resultMap.put("isMergeByFacDr", ObjectUtils.isEmpty(vouRuleVO.getIsMergeByFacDr()) ? 1 : vouRuleVO.getIsMergeByFacDr());
        resultMap.put("isMergeByFacCr", ObjectUtils.isEmpty(vouRuleVO.getIsMergeByFacCr()) ? 1 : vouRuleVO.getIsMergeByFacCr());
        resultMap.put("isMergeByBacDr", ObjectUtils.isEmpty(vouRuleVO.getIsMergeByBacDr()) ? 1 : vouRuleVO.getIsMergeByBacDr());
        resultMap.put("isMergeByBacCr", ObjectUtils.isEmpty(vouRuleVO.getIsMergeByBacCr()) ? 1 : vouRuleVO.getIsMergeByBacCr());
        resultMap.put("isMergeBySummary", ObjectUtils.isEmpty(vouRuleVO.getIsMergeBySummary()) ? 1 : vouRuleVO.getIsMergeBySummary());
        resultMap.put("isMergeByAcitem", ObjectUtils.isEmpty(vouRuleVO.getIsMergeByAcitem()) ? 1 : vouRuleVO.getIsMergeByAcitem());
        resultList.add(resultMap);
    }

    private static String getVouSummery(PcxVouSummaryAcoQO qo, PcxVouRuleVO pcxVouRuleVO) {
        String summaryRuleContent = pcxVouRuleVO.getSummaryRuleContent();
        StringBuffer sb = new StringBuffer();
        Map<String, Object> content = JSON.parseObject(summaryRuleContent, new TypeReference<Map<String, Object>>() {},Feature.OrderedField);
        List<String> keys = new ArrayList<>();
        keys.addAll(content.keySet());
        for (String key : keys) {
            Field field;
            try {//先匹配父类中的公共属性
                if(key.startsWith(VouSummaryEnum.ANY_STRING.getCode())){
                    sb.append(content.get(key));
                }else {
                    field = qo.getClass().getDeclaredField(key);
                    field.setAccessible(true);
                    sb.append(field.get(qo));
                }
            } catch (Exception exception) {
                log.error("获取摘要参数错误");
            }
        }
        return sb.toString();
    }


}
