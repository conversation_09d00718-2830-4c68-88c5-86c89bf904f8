package com.pty.pcx.service.impl.treasurypay.change;

import com.google.common.collect.Lists;
import com.pty.pcx.api.treasurypay.change.IPcxChangeBillPayDetailService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.treasurypay.change.PcxChangeBillPayDetailDao;
import com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail;
import com.pty.pcx.qo.treasurypay.change.PcxChangeBillPayDetailQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.treasurypay.change.PcxChangeBillPayDetailVO;
import com.pty.pub.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Indexed
@Service
public class PcxChangeBillPayDetailServiceImpl implements IPcxChangeBillPayDetailService {

    @Autowired
    private PcxChangeBillPayDetailDao changeBillPayDetailDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Override
    public List<PcxChangeBillPayDetailVO> select(PcxChangeBillPayDetailQO changeBillPayDetailQo) {
        return changeBillPayDetailDao.select(changeBillPayDetailQo);
    }

    @Override
    public List<PcxChangeBillPayDetailVO> selectByBillIds(List<String> billIds) {
        if (CollectionUtil.isEmpty(billIds)) {
            return Collections.emptyList();

        }
        List<PcxChangeBillPayDetailVO> result = new ArrayList<>();
        List<List<String>> partition = Lists.partition(billIds, 50);
        partition.forEach(part -> {
            result.addAll(changeBillPayDetailDao.selectByBillIds(part));
        });
        return result;
    }

    @Override
    public List<PcxChangeBillPayDetailVO> selectByChangeIds(List<String> changeIds) {
        List<PcxChangeBillPayDetailVO> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(changeIds)) {
            return result;
        }

        List<List<String>> partition = Lists.partition(changeIds, 50);
        partition.forEach(part -> {
            result.addAll(changeBillPayDetailDao.selectByChangeIds(part));
        });
        return result;
    }

    @Override
    public CheckMsg save(PcxChangeBillPayDetailQO changeBillPayDetailQo) {
        changeBillPayDetailDao.insertSelective(changeBillPayDetailQo);
        return CheckMsg.success("保存成功");
    }


    @Override
    public CheckMsg batchSave(List<PcxChangeBillPayDetail> changeBillPayDetails) {
        batchServiceUtil.batchProcess(changeBillPayDetails, PcxChangeBillPayDetailDao.class, PcxChangeBillPayDetailDao::insertSelective);
        return CheckMsg.success("保存成功");
    }

    @Override
    public int update(PcxChangeBillPayDetailQO changeBillPayDetailQo) {
        return changeBillPayDetailDao.updateById(changeBillPayDetailQo);
    }

    @Override
    public void batchUpdate(List<PcxChangeBillPayDetail> changeBillPayDetails) {
        batchServiceUtil.batchProcess(changeBillPayDetails, PcxChangeBillPayDetailDao.class, PcxChangeBillPayDetailDao::updateById);
    }
}
