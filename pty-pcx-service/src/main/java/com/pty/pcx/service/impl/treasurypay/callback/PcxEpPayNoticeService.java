package com.pty.pcx.service.impl.treasurypay.callback;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.ep.api.IEPayNoticeService;
import com.pty.ep.entity.vo.EPayResult;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillPayDetailPlusService;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillStatusHistoryPlusService;
import com.pty.pcx.common.constant.BillHistoryConstant;
import com.pty.pcx.common.enu.BillPayStatusEnum;
import com.pty.pcx.entity.bill.PcxBillStatusHistory;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.pty.pcx.common.enu.BillPayDetailStatusEnum.*;

@Slf4j
@Indexed
@Service
public class PcxEpPayNoticeService implements IEPayNoticeService {
    /**
     * 网银成功回写报销单修改支付状态
     * @param payResultList
     */

    @Autowired
    private IPcxBillPayDetailPlusService pcxBillPayDetailPlusService;

    @Autowired
    private IPcxBillStatusHistoryPlusService pcxBillStatusHistoryPlusService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payResultNotice(List<EPayResult> payResultList) {
        for (EPayResult ePayResult : payResultList) {
            // 查询支付中流程状态
            List<PcxBillStatusHistory> payingHistoryList= pcxBillStatusHistoryPlusService.list(
                    new LambdaQueryWrapper<PcxBillStatusHistory>()
                            .eq(PcxBillStatusHistory::getBillType, BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                            .eq(PcxBillStatusHistory::getOpType, BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
                            .eq(StringUtils.isNoneBlank(ePayResult.getSettlementId()), PcxBillStatusHistory::getBillId, ePayResult.getSettlementId())
                            .eq(StringUtils.isNoneBlank(ePayResult.getAgyCode()), PcxBillStatusHistory::getAgyCode, ePayResult.getAgyCode())
                            .eq(StringUtils.isNoneBlank(ePayResult.getFiscal()), PcxBillStatusHistory::getFiscal, ePayResult.getFiscal())
                            .eq(StringUtils.isNoneBlank(ePayResult.getMofDivCode()), PcxBillStatusHistory::getMofDivCode, ePayResult.getMofDivCode()));
            // 不存在要新增状态
            if (CollectionUtils.isEmpty(payingHistoryList)){
                // 查询单据状态
                PcxBillPayDetail billPayDetail = pcxBillPayDetailPlusService.getOne(
                        new LambdaQueryWrapper<PcxBillPayDetail>()
                                .eq(StringUtils.isNoneBlank(ePayResult.getBillId()), PcxBillPayDetail::getBillId, ePayResult.getBillId())
                                .eq(StringUtils.isNoneBlank(ePayResult.getSettlementId()), PcxBillPayDetail::getPayNo, ePayResult.getSettlementId())
                                .eq(StringUtils.isNoneBlank(ePayResult.getAgyCode()), PcxBillPayDetail::getAgyCode, ePayResult.getAgyCode())
                                .eq(StringUtils.isNoneBlank(ePayResult.getFiscal()), PcxBillPayDetail::getFiscal, ePayResult.getFiscal())
                                .eq(StringUtils.isNoneBlank(ePayResult.getMofDivCode()), PcxBillPayDetail::getMofDivCode, ePayResult.getMofDivCode()));
                // 新增支付中单据流转状态
                PcxBillStatusHistory billStatusHistory = PcxBillStatusHistory.builder()
                        .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                        .billId(billPayDetail.getPayNo())
                        .opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
                        .opTime(new Date())
                        .fromStatus(billPayDetail.getPayStatus())
                        .toStatus(PAYING.getCode())
                        .remark("")
                        .creator(PtyContext.getUsername())
                        .creatorName(String.valueOf(PtyContext.getValue(PtyContext.CURRENT_USER_NAME)))
                        .createdTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                        .agyCode(ePayResult.getAgyCode())
                        .fiscal(ePayResult.getFiscal())
                        .mofDivCode(ePayResult.getMofDivCode())
                        .build();
                pcxBillStatusHistoryPlusService.save(billStatusHistory);
                // 修改当前单据状态
                PcxBillPayDetail updateBillPayDetail = new PcxBillPayDetail();
                updateBillPayDetail.setId(billPayDetail.getId());
                updateBillPayDetail.setPayStatus(PAYING.getCode());
                updateBillPayDetail.setPayingTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                pcxBillPayDetailPlusService.updateById(updateBillPayDetail);
            }

            // 状态不明的不处理
            if (ePayResult.getStatus() == 2){
                continue;
            }

            // 查询单据状态
            PcxBillPayDetail billPayDetail = pcxBillPayDetailPlusService.getOne(
                    new LambdaQueryWrapper<PcxBillPayDetail>()
                            .eq(StringUtils.isNoneBlank(ePayResult.getBillId()), PcxBillPayDetail::getBillId, ePayResult.getBillId())
                            .eq(StringUtils.isNoneBlank(ePayResult.getSettlementId()), PcxBillPayDetail::getPayNo, ePayResult.getSettlementId())
                            .eq(StringUtils.isNoneBlank(ePayResult.getAgyCode()), PcxBillPayDetail::getAgyCode, ePayResult.getAgyCode())
                            .eq(StringUtils.isNoneBlank(ePayResult.getFiscal()), PcxBillPayDetail::getFiscal, ePayResult.getFiscal())
                            .eq(StringUtils.isNoneBlank(ePayResult.getMofDivCode()), PcxBillPayDetail::getMofDivCode, ePayResult.getMofDivCode()));

            String detailPayStatus = ePayResult.getStatus() == 1 ? PAID.getCode() : PAY_ERROR.getCode();
            String historyPayStatus = ePayResult.getStatus() == 1 ? BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_SUCCESS.getCode() : BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_ERROR.getCode();
            // 新增支付单据流转状态
            PcxBillStatusHistory billStatusHistory = PcxBillStatusHistory.builder()
                    .billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
                    .billId(billPayDetail.getPayNo())
                    .opType(historyPayStatus)
                    .opTime(new Date())
                    .fromStatus(billPayDetail.getPayStatus())
                    .toStatus(detailPayStatus)
                    .remark("")
                    .creator(PtyContext.getUsername())
                    .creatorName(String.valueOf(PtyContext.getValue(PtyContext.CURRENT_USER_NAME)))
                    .createdTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                    .agyCode(billPayDetail.getAgyCode())
                    .fiscal(billPayDetail.getFiscal())
                    .mofDivCode(billPayDetail.getMofDivCode())
                    .build();

            // 修改当前单据状态
            PcxBillPayDetail updateBillPayDetail = new PcxBillPayDetail();
            updateBillPayDetail.setId(billPayDetail.getId());
            updateBillPayDetail.setPayStatus(detailPayStatus);

            if(billStatusHistory.getOpType().equals(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_ERROR.getCode())){
//                List<String> payTrnDescList = Arrays.asList(epPayBillInfo.getPayTrnDesc().split("===="));
//                if(CollectionUtils.isNotEmpty(payTrnDescList) && payTrnDescList.size() > 1){
//                    String errorMsg = payTrnDescList.get(1);
//                    if(errorMsg.contains("您输入账（卡）号不存在") || errorMsg.contains("收款账号不是兴业银行账号")){
                        // 用户信息错误导致失败，支付异常
//                        billStatusHistory.setRemark(payTrnDescList.get(1));
                        billStatusHistory.setRemark(ePayResult.getMsg());
                        pcxBillStatusHistoryPlusService.save(billStatusHistory);
//                    }else{
//                        // 网络或其他原因导致失败，删除支付中状态，重新支付
//                        pcxBillStatusHistoryPlusService.remove(new LambdaQueryWrapper<PcxBillStatusHistory>()
//                                .eq(StringUtils.isNoneBlank(billStatusHistory.getBillId()), PcxBillStatusHistory::getBillId, billStatusHistory.getBillId())
//                                .eq(StringUtils.isNoneBlank(billStatusHistory.getOpType()), PcxBillStatusHistory::getOpType, BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
//                                .eq(StringUtils.isNoneBlank(billStatusHistory.getBillType()), PcxBillStatusHistory::getBillType, billStatusHistory.getBillType()));
//                        updateBillPayDetail.setPayStatus(BillPayStatusEnum.UNPAID.getCode());
//                        errorList.add("1");
//                    }
//                }else{
//                    // 网络或其他原因导致失败，删除支付中状态，重新支付
//                    pcxBillStatusHistoryPlusService.remove(new LambdaQueryWrapper<PcxBillStatusHistory>()
//                            .eq(StringUtils.isNoneBlank(billStatusHistory.getBillId()), PcxBillStatusHistory::getBillId, billStatusHistory.getBillId())
//                            .eq(StringUtils.isNoneBlank(billStatusHistory.getOpType()), PcxBillStatusHistory::getOpType, BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
//                            .eq(StringUtils.isNoneBlank(billStatusHistory.getBillType()), PcxBillStatusHistory::getBillType, billStatusHistory.getBillType()));
//                    updateBillPayDetail.setPayStatus(BillPayStatusEnum.UNPAID.getCode());
//                    errorList.add("1");
//                }

                // 更新支付单状态
                pcxBillPayDetailPlusService.updateById(updateBillPayDetail);
            }
            // 支付成功后处理bill和指标
            if(billStatusHistory.getOpType().equals(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_SUCCESS.getCode())){
                updateBillPayDetail.setBillId(billPayDetail.getBillId());
                updateBillPayDetail.setPaidTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                updateBillPayDetail.setCheckAmt(billPayDetail.getCheckAmt());
                pcxBillPayDetailPlusService.operationAfterPay(updateBillPayDetail, billStatusHistory);
            }
        }
    }
}
