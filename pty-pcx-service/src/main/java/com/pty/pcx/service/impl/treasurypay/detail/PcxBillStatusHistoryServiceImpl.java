package com.pty.pcx.service.impl.treasurypay.detail;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pty.ep.entity.ep.EpPayBill;
import com.pty.ep.entity.ep.EpPayBillInfo;
import com.pty.pcx.common.constant.BillHistoryConstant;
import com.pty.pcx.common.enu.BillPayDetailStatusEnum;
import com.pty.pcx.common.enu.BillPayStatusEnum;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.treasurypay.detail.PcxBillStatusHistoryDao;
import com.pty.pcx.entity.bill.PcxBillStatusHistory;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.ep.IEpPayBillExternalService;
import com.pty.pcx.qo.treasurypay.detail.PcxBillStatusHistoryQO;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillPayDetailPlusService;
import com.pty.pcx.api.mybatisplus.treasurypay.IPcxBillStatusHistoryPlusService;
import com.pty.pcx.vo.treasurypay.detail.PcxBillStatusHistoryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.pty.pcx.common.enu.BillPayDetailStatusEnum.PAID;

/**
 * 单据流转历史表(PcxBillStatusHistory)表服务实现类
 * <AUTHOR>
 * @since 2024-12-20 09:06:15
 */
@Slf4j
@Indexed
@Service
public class PcxBillStatusHistoryServiceImpl extends ServiceImpl<PcxBillStatusHistoryDao, PcxBillStatusHistory> implements IPcxBillStatusHistoryPlusService {

	@Autowired
	private IPcxBillPayDetailPlusService pcxBillPayDetailPlusService;

	@Autowired
	private PcxBillStatusHistoryDao pcxBillStatusHistoryDao;

	@Autowired
	private IEpPayBillExternalService epPayBillExternalService;

	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxBillStatusHistory selectById(String id) {
		return pcxBillStatusHistoryDao.selectById(id);
	}

	/**
	 * 查询单据流转历史列表
	 * @param qo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public CheckMsg<List<PcxBillStatusHistoryVO>> selectBillStatusHistory(PcxBillStatusHistoryQO qo) {

		List<PcxBillStatusHistory> list = pcxBillStatusHistoryDao.selectList(new LambdaQueryWrapper<PcxBillStatusHistory>()
				.eq(StringUtils.isNotBlank(qo.getPayNo()), PcxBillStatusHistory::getBillId, qo.getPayNo())
				.eq(PcxBillStatusHistory::getBillType, BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
				.orderByAsc(PcxBillStatusHistory::getOpTime));
		List<PcxBillStatusHistoryVO> voList = list.stream().map(item->{
			PcxBillStatusHistoryVO vo = new PcxBillStatusHistoryVO();
			vo.setOpTypeCode(item.getOpType());
			vo.setOpTypeName(BillHistoryConstant.BillOpTypeEnum.getBillOpTypeNameByCode(item.getOpType()));
			vo.setCreatorName(item.getCreatorName());
			vo.setOpTime(DateUtil.format(item.getOpTime(), "yyyy-MM-dd HH:mm:ss"));
			vo.setRemark(item.getRemark());
			return vo;
		}).collect(Collectors.toList());
		// 发起支付时加上支付中记录
		if (list.size() == 2 && list.get(1).getOpType().equals(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())) {
			PcxBillStatusHistoryVO vo = new PcxBillStatusHistoryVO();
			vo.setOpTypeCode(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAYING.getCode());
			vo.setOpTypeName(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAYING.getName());
			vo.setCreatorName(list.get(1).getCreatorName());
			vo.setOpTime(DateUtil.format(list.get(1).getOpTime(), "yyyy-MM-dd HH:mm:ss"));
			vo.setRemark(list.get(1).getRemark());
			voList.add(vo);
		}
		return CheckMsg.success(voList);
	}

	// 支付单同步更新ep状态
	public void refreshAllPayingStatus() {
		// 查询所有支付中支付单
		List<PcxBillPayDetail> pcxBillPayDetailList = pcxBillPayDetailPlusService.list(new LambdaQueryWrapper<PcxBillPayDetail>()
				.eq(PcxBillPayDetail::getPayStatus, BillPayDetailStatusEnum.PAYING.getCode()));

		log.info("本次刷新以下支付单{}",pcxBillPayDetailList.stream().map(PcxBillPayDetail::getPayNo).collect(Collectors.toList()));

		for (PcxBillPayDetail pcxBillPayDetail : pcxBillPayDetailList) {

			// 现金、支票支付直接成功
			if (pcxBillPayDetail.getSettlementType().equals(SettlementTypeEnum.SETTLE_CASH.getCode()) || pcxBillPayDetail.getSettlementType().equals(SettlementTypeEnum.SETTLE_CHEQUE.getCode())){
				// 新增支付完成单据流转状态
				PcxBillStatusHistory billStatusHistory = PcxBillStatusHistory.builder()
						.billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
						.billId(pcxBillPayDetail.getPayNo())
						.opType(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_SUCCESS.getCode())
						.opTime(new Date())
						.fromStatus(pcxBillPayDetail.getPayStatus())
						.toStatus(PAID.getCode())
						.remark("")
						.creator("0")
						.creatorName("task")
						.createdTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
						.agyCode(pcxBillPayDetail.getAgyCode())
						.fiscal(pcxBillPayDetail.getFiscal())
						.mofDivCode(pcxBillPayDetail.getMofDivCode())
						.build();
				pcxBillPayDetail.setPayStatus(PAID.getCode());
				pcxBillPayDetail.setPayingTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
				pcxBillPayDetail.setPaidTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
				// 修改单据状态
				pcxBillPayDetailPlusService.operationAfterPay(pcxBillPayDetail, billStatusHistory);
				continue;
			}

			// 查询ep支付单状态
			List<EpPayBillInfo> epPayBillInfoList = epPayBillExternalService.getEpPayStatus(pcxBillPayDetail);

			if (epPayBillInfoList.size() > 0){
				EpPayBillInfo epPayBillInfo = epPayBillInfoList.get(0);
				EpPayBill epPayBill = new EpPayBill();
				epPayBill.setBillId(epPayBillInfo.getBillId());

				try {
					// 确认发送请求，不回滚
					epPayBillExternalService.checkEpPayStatus(epPayBill);
				} catch (Exception e) {
					log.error(String.format("支付单{%s}--确认请求发送失败",pcxBillPayDetail.getPayNo()));
				}
			}

			epPayBillInfoList = epPayBillExternalService.getEpPayStatus(pcxBillPayDetail);

			if (epPayBillInfoList.size() > 0) {
				EpPayBillInfo epPayBillInfo = epPayBillInfoList.get(0);
				if (epPayBillInfo.getPayTrnFlag().equals("0") || epPayBillInfo.getPayTrnFlag().equals("1") || epPayBillInfo.getPayTrnFlag().equals("1001") || epPayBillInfo.getPayTrnFlag().equals("9999")){
					// 修改支付单状态
					addAndSavePayDetail(epPayBillInfo, pcxBillPayDetail);
				}
			}
		}
	}

	// 修改支付单状态
	@Transactional(rollbackFor = Exception.class)
	public void addAndSavePayDetail(EpPayBillInfo epPayBillInfo, PcxBillPayDetail pcxBillPayDetail) {
		String detailPayStatus = epPayBillInfo.getPayTrnFlag().equals("0") ? BillPayDetailStatusEnum.PAID.getCode() : BillPayDetailStatusEnum.PAY_ERROR.getCode();
		String historyPayStatus = epPayBillInfo.getPayTrnFlag().equals("0") ? BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_SUCCESS.getCode() : BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_ERROR.getCode();
		// 新增支付单据流转状态
		PcxBillStatusHistory billStatusHistory = PcxBillStatusHistory.builder()
				.billType(BillHistoryConstant.BillTypeEnum.PAY_DETAIL.getCode())
				.billId(pcxBillPayDetail.getPayNo())
				.opType(historyPayStatus)
				.opTime(new Date())
				.fromStatus(pcxBillPayDetail.getPayStatus())
				.toStatus(detailPayStatus)
				.remark("")
				.creator("0")
				.creatorName("task")
				.createdTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
				.agyCode(pcxBillPayDetail.getAgyCode())
				.fiscal(pcxBillPayDetail.getFiscal())
				.mofDivCode(pcxBillPayDetail.getMofDivCode())
				.build();

		pcxBillPayDetail.setPayStatus(detailPayStatus);
		if(billStatusHistory.getOpType().equals(BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY_ERROR.getCode())){
//			List<String> payTrnDescList = Arrays.asList(epPayBillInfo.getPayTrnDesc().split("===="));
//			if(CollectionUtils.isNotEmpty(payTrnDescList) && payTrnDescList.size() > 1){
//				String errorMsg = payTrnDescList.get(1);
//				if(errorMsg.contains("您输入账（卡）号不存在") || errorMsg.contains("收款账号不是兴业银行账号")){
					// 用户信息错误导致失败，支付异常
//					billStatusHistory.setRemark(payTrnDescList.get(1));
					billStatusHistory.setRemark(epPayBillInfo.getPayTrnDesc());
					save(billStatusHistory);
//				}else{
//					// 网络或其他原因导致失败，删除支付中状态，重新支付
//					remove(new LambdaQueryWrapper<PcxBillStatusHistory>()
//							.eq(StringUtils.isNoneBlank(billStatusHistory.getBillId()), PcxBillStatusHistory::getBillId, billStatusHistory.getBillId())
//							.eq(StringUtils.isNoneBlank(billStatusHistory.getOpType()), PcxBillStatusHistory::getOpType, BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
//							.eq(StringUtils.isNoneBlank(billStatusHistory.getBillType()), PcxBillStatusHistory::getBillType, billStatusHistory.getBillType()));
//					pcxBillPayDetail.setPayStatus(BillPayStatusEnum.UNPAID.getCode());
//				}
//			}else{
//				// 网络或其他原因导致失败，删除支付中状态，重新支付
//				remove(new LambdaQueryWrapper<PcxBillStatusHistory>()
//						.eq(StringUtils.isNoneBlank(billStatusHistory.getBillId()), PcxBillStatusHistory::getBillId, billStatusHistory.getBillId())
//						.eq(StringUtils.isNoneBlank(billStatusHistory.getOpType()), PcxBillStatusHistory::getOpType, BillHistoryConstant.BillOpTypeEnum.ACCOUNTING_PAY.getCode())
//						.eq(StringUtils.isNoneBlank(billStatusHistory.getBillType()), PcxBillStatusHistory::getBillType, billStatusHistory.getBillType()));
//				pcxBillPayDetail.setPayStatus(BillPayStatusEnum.UNPAID.getCode());
//			}

			// 更新支付单状态
			pcxBillPayDetailPlusService.updateById(pcxBillPayDetail);
		}

		// 支付成功后处理bill状态
		if(epPayBillInfo.getPayTrnFlag().equals("0")){
			pcxBillPayDetail.setPaidTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
			pcxBillPayDetailPlusService.operationAfterPay(pcxBillPayDetail, billStatusHistory);
		}
	}


}

