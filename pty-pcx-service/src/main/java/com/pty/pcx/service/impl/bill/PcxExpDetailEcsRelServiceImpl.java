package com.pty.pcx.service.impl.bill;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pty.openapi.integration.enums.InvoiceType;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.qo.bill.PcxExpDetailEcsRelQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报销单费用明细与票附件关联表，存储费用明细与票务附件的关系信息(PcxExpDetailEcsRel)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-27 19:48:00
 */
@Slf4j
@Indexed
@Service
public class PcxExpDetailEcsRelServiceImpl implements PcxExpDetailEcsRelService {

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PcxExpDetailEcsRel selectById(String id) {
        return pcxExpDetailEcsRelDao.selectById(id);
    }


    /**
     * 通过主键id删除数据
     *
     * @param id 主键
     */
    @Override
    public int deleteById(String id) {
        return pcxExpDetailEcsRelDao.deleteById(id);
    }

    @Override
    public List<PcxExpDetailEcsRel> selectByBillId(String billId) {
        return pcxExpDetailEcsRelDao.selectList(new LambdaQueryWrapper<PcxExpDetailEcsRel>().eq(PcxExpDetailEcsRel::getBillId, billId));
    }

    @Override
    public List<PcxExpDetailEcsRel> selectByQo(PcxExpDetailEcsRelQO param) {
        return pcxExpDetailEcsRelDao.selectList(new LambdaQueryWrapper<PcxExpDetailEcsRel>()
                .eq(PcxExpDetailEcsRel::getBillId, param.getBillId())
                .eq(PcxExpDetailEcsRel::getFiscal, param.getFiscal())
                .eq(PcxExpDetailEcsRel::getMofDivCode, param.getMofDivCode())
                .eq(PcxExpDetailEcsRel::getAgyCode, param.getAgyCode())
                .eq(PcxExpDetailEcsRel::getTenantId, param.getTenantId()));
    }


    @Override
    public List<PcxExpDetailEcsRel> selectByBillIds(List<String> billIds) {
        return pcxExpDetailEcsRelDao.selectList(new LambdaQueryWrapper<PcxExpDetailEcsRel>().in(PcxExpDetailEcsRel::getBillId, billIds));
    }

    @Override
    public Integer countEcs(String billId) {
        return pcxExpDetailEcsRelDao.countEcs(billId);
    }

    @Override
    public int updateById(PcxExpDetailEcsRel pcxExpDetailEcsRel) {
        return pcxExpDetailEcsRelDao.updateById(pcxExpDetailEcsRel);
    }

    /**
     * 根据billId查询是否有票
     *
     * @param billId
     * @return
     */
    @Override
    public long selectCountByBillId(String billId) {
        return pcxExpDetailEcsRelDao.selectCount(new LambdaQueryWrapper<PcxExpDetailEcsRel>()
                .eq(PcxExpDetailEcsRel::getBillId, billId)
                .isNotNull(PcxExpDetailEcsRel::getEcsBillId).and(i -> i.ne(PcxExpDetailEcsRel::getEcsBillId, ""))
        );
    }

    @Override
    public List<PcxExpDetailEcsRel> selectAgyConfirmEcsList(PcxExpDetailEcsRel pcxExpDetailEcsRel, List<String> billNos) {
        return pcxExpDetailEcsRelDao.selectList(new LambdaQueryWrapper<PcxExpDetailEcsRel>()
//				.eq(PcxExpDetailEcsRel::getAgyCode, pcxExpDetailEcsRel.getAgyCode())
                .eq(PcxExpDetailEcsRel::getFiscal, pcxExpDetailEcsRel.getFiscal())
                .eq(PcxExpDetailEcsRel::getMofDivCode, pcxExpDetailEcsRel.getMofDivCode())
                .eq(PcxExpDetailEcsRel::getIsConfirm, PubConstant.LOGIC_TRUE)
                .in(PcxExpDetailEcsRel::getEcsBillNo, billNos));
    }

    @Override
    public void updateCheckStatusByInvoiceNos(List<String> invoiceNos, Integer checkStatus) {
        if (CollectionUtil.isEmpty(invoiceNos)) {
            return;
        }
        pcxExpDetailEcsRelDao.update(null, new LambdaUpdateWrapper<PcxExpDetailEcsRel>()
                .in(PcxExpDetailEcsRel::getEcsBillNo, invoiceNos)
                .set(PcxExpDetailEcsRel::getEcsCheckStatus, checkStatus));
    }

    @Override
    public PcxExpDetailEcsRel selectByDetailId(String detailId) {
        return pcxExpDetailEcsRelDao.selectOne(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getDetailId, detailId).last("limit 1"));
    }

    @Override
    public List<PcxExpDetailEcsRel> selectByDetailIds(List<String> detailIds) {
        if (CollectionUtil.isEmpty(detailIds)) {
            return Collections.emptyList();
        }
        return pcxExpDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .in(PcxExpDetailEcsRel::getDetailId, detailIds));
    }

    @Override
    public void initEcsRealName() {
        LambdaQueryWrapper<PcxExpDetailEcsRel> queryWrapper = Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getIsRealName, -1);
        Page<PcxExpDetailEcsRel> page = pcxExpDetailEcsRelDao.selectPage(new Page<>(1, 100), queryWrapper);
        List<PcxExpDetailEcsRel> records = page.getRecords();

        List<String> ecsBillIds =
                records.stream().map(PcxExpDetailEcsRel::getEcsBillId).collect(Collectors.toList());
        Map<String, String> ecsExtHeadMap = selectEcsExtHeadMap(ecsBillIds);
        Map<String, String> ecsBillRaiMap = selectEcsBillRaiMap(ecsBillIds);
        Map<String, String> ecsBillEinvMap = selectEcsBillEinvMap(ecsBillIds);

        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = records.stream()
                .map(record -> {
                    int isRealName = 0;
                    String ecsBillType = record.getEcsBillType();
                    if ("atr".equals(ecsBillType)) {
                        isRealName = 1;
                    } else if ("bus".equals(ecsBillType)) {
                        String passenger = ecsExtHeadMap.get(record.getEcsBillId());
                        if (StringUtils.isNotBlank(passenger)) {
                            isRealName = 1;
                        }
                    } else if ("einv".equals(ecsBillType)) {
                        String expenseTypeCode = record.getExpenseTypeCode();
                        if (PcxConstant.TRAVEL_DETAIL_3021101.equals(expenseTypeCode)) {
                            String billDesc = record.getEcsBillDesc();
                            if (!StringUtils.contains(billDesc, "代理")) {
                                isRealName = 1;
                            }

                            // 对于专用发票，为非实名
                            String invoiceType = ecsBillEinvMap.get(record.getEcsBillId());
                            if (InvoiceType.VAT_SPECIAL_ELECTRONIC.getCode().equals(invoiceType)) {
                                isRealName = 0;
                            }

                        } else if (PcxConstant.TRAVEL_DETAIL_3021112.equals(expenseTypeCode)) {
                            String passenger = ecsExtHeadMap.get(record.getEcsBillId());
                            if (StringUtils.isNotBlank(passenger)) {
                                isRealName = 1;
                            }
                        }
                    } else if ("rai".equals(ecsBillType)) {
                        String raiBizType = ecsBillRaiMap.get(record.getEcsBillId());
                        if (!"退".equals(raiBizType) && !"改".equals(raiBizType) && !"补".equals(raiBizType)) {
                            isRealName = 1;
                        }
                    }
                    record.setIsRealName(isRealName);
                    return record;
                })
                .collect(Collectors.toList());

        batchServiceUtil.batchProcess(pcxExpDetailEcsRels, PcxExpDetailEcsRelDao.class, PcxExpDetailEcsRelDao::updateById);
    }

    @Override
    public BigDecimal countEcsAmtBySeller(DateTime startDate, DateTime endDate, String ecsSeller) {
        return pcxExpDetailEcsRelDao.countEcsAmt(DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), ecsSeller);
    }

    private Map<String, String> selectEcsBillEinvMap(List<String> billIds) {
        List<Map<String, String>> ecsBillEinvs = selectEcsBillEinvByBillIds(billIds);
        Map<String, String> ecsBillEinvMap = new HashMap<>();
        for (Map<String, String> ecsBillEinv : ecsBillEinvs) {
            ecsBillEinvMap.put(ecsBillEinv.get("billId"), ecsBillEinv.get("invSubKind"));
        }
        return ecsBillEinvMap;
    }

    /**
     * 查询数电票电子会计凭证,用于判断普票与专票
     */
    private List<Map<String, String>> selectEcsBillEinvByBillIds(List<String> billIds) {
        if (billIds.isEmpty()) {
            return Collections.emptyList();
        }
        return jdbcTemplate.query("select bill_id, inv_sub_kind from ecs_bill_einv where bill_id in(:billIds)",
                new MapSqlParameterSource("billIds", billIds),
                (rs, rowNum) -> {
                    Map<String, String> mapOfColumnValues = new HashMap<>();
                    mapOfColumnValues.put("billId", rs.getString("bill_id"));
                    mapOfColumnValues.put("invSubKind", rs.getString("inv_sub_kind"));
                    return mapOfColumnValues;
                });
    }

    private Map<String, String> selectEcsExtHeadMap(List<String> billIds) {
        List<Map<String, String>> ecsExtHeads = selectEcsExtHeadByBillIds(billIds);
        Map<String, String> ecsExtHeadMap = new HashMap<>();
        for (Map<String, String> ecsExtHead : ecsExtHeads) {
            ecsExtHeadMap.put(ecsExtHead.get("bill_id"), ecsExtHead.get("passenger"));
        }
        return ecsExtHeadMap;
    }

    private List<Map<String, String>> selectEcsExtHeadByBillIds(List<String> billIds) {
        if (billIds.isEmpty()) {
            return Collections.emptyList();
        }
        return jdbcTemplate.query("select bill_id, field03 from ecs_ext_head where bill_id in(:billIds)",
                new MapSqlParameterSource("billIds", billIds),
                (rs, rowNum) -> {
                    Map<String, String> mapOfColumnValues = new HashMap<>();
                    mapOfColumnValues.put("billId", rs.getString("bill_id"));
                    mapOfColumnValues.put("passenger", rs.getString("field03"));
                    return mapOfColumnValues;
                });
    }

    private Map<String, String> selectEcsBillRaiMap(List<String> billIds) {
        List<Map<String, String>> ecsBillEinvs = selectEcsBillRaisByBillIds(billIds);
        Map<String, String> ecsBillRaiMap = new HashMap<>();
        for (Map<String, String> ecsBillRai : ecsBillEinvs) {
            ecsBillRaiMap.put(ecsBillRai.get("billId"), ecsBillRai.get("invSubKind"));
        }
        return ecsBillRaiMap;
    }

    private List<Map<String, String>> selectEcsBillRaisByBillIds(List<String> billIds) {
        if (billIds.isEmpty()) {
            return Collections.emptyList();
        }
        return jdbcTemplate.query("select bill_id, rai_biz_type from ecs_bill_rai where bill_id in(:billIds)",
                new MapSqlParameterSource("billIds", billIds),
                (rs, rowNum) -> {
                    Map<String, String> mapOfColumnValues = new HashMap<>();
                    mapOfColumnValues.put("billId", rs.getString("bill_id"));
                    mapOfColumnValues.put("raiBizType", rs.getString("rai_biz_type"));
                    return mapOfColumnValues;
                });
    }

}


