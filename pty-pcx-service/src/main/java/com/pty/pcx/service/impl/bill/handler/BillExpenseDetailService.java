package com.pty.pcx.service.impl.bill.handler;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;

import java.util.List;

public interface BillExpenseDetailService<T extends PcxBillExpDetailBase,B extends PcxBillExpBase> {


    /**
     * 校验逻辑（校验明细属性明细项）
     * @param expDetail
     * @return
     */
    CheckMsg<Void> validate(List<T> expDetail,String billFuncCode);


    /**
     * 明细信息保存
     * @param expense
     * @param detail
     * @param pcxBill
     */
    List<T>  saveOrUpdate(B expense,List<T> detail, PcxBill pcxBill);


    /**
     * 费用类型代码查询
     * 住宿费回合并展示，明细会少
     * 所有明细使用listByExpenseCode方法
     * @param expenseCode
     * @param pcxBill
     * @return·
     */
    List<T> viewByExpenseCode(String expenseCode,PcxBill pcxBill);

    /**
     * 费用类型代码查询
     * 所有明细
     * @param expenseCode
     * @param pcxBill
     * @return·
     */
    List<T> listByExpenseCode(String expenseCode,PcxBill pcxBill);

    /**
     * 根据费用明细代码查询
     * @param expenseDetailCode
     * @param pcxBill
     * @return
     */
    List<T> view(String expenseDetailCode,PcxBill pcxBill);

    /**
     * 删除
     * @param expenseCode
     * @param pcxBill
     */
    void deleteByExpenseCode(String expenseCode,PcxBill pcxBill);

    /**
     * 根据费用明细删除
     * @param expenseDetailCode
     * @param pcxBill
     */
    void delete(String expenseDetailCode,PcxBill pcxBill);


    void deleteByIds(List<String> ids);
}
