package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.CollectionUtils;
import com.beust.jcommander.internal.Lists;
import com.pty.mad.common.qo.MadWorkflowEmployeeQo;
import com.pty.mad.entity.MadDepartment;
import com.pty.mad.tree.DepartmentTreeNode;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pub.common.bean.TreeNode;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.pty.mad.api.IMadDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class DeptSuperiorAuditIPositionService extends AbstractRuleDomainService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;
    @Resource
    private PcxBillService pcxBillService;

    @Resource
    private IMadDepartmentService departmentService;
    @Override
    public List<String> getPositionIds() {
        return Lists.newArrayList(
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.dept_superior_audit.getId(), "5")
        );
    }

    @Override
    public List<String> findPositionUser(String billNo) {

        CheckMsg<PcxBillVO> msg = pcxBillService.view(billNo, PositionBlockEnum.FUND_SOURCE, PositionBlockEnum.SPECIFICITY);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());

        PcxBillVO vo = msg.getData();
        String departmentCode = vo.getBasicInfo().getDepartmentCode();
        String agyCode = vo.getBasicInfo().getAgyCode();
        String fiscal = vo.getBasicInfo().getFiscal();
        String mofDivCode = vo.getBasicInfo().getMofDivCode();
        MadDepartment department =  new MadDepartment();
        department.setAgyCode(agyCode);
        department.setFiscal(Integer.valueOf(fiscal));
        department.setMofDivCode(mofDivCode);
        department.setIsEnabled(1);
        List<DepartmentTreeNode> tree = departmentService.getTree(department);
        Map<String, TreeNode> code$rel = mapAllDepartmentCodeTree(tree);
        Map<String, TreeNode> id$rel = mapAllDepartmentIdTree(tree);
        TreeNode departmentTreeNode = code$rel.get(departmentCode);
        Assert.state(Objects.nonNull(departmentTreeNode), "未找到部门信息{}", departmentCode);
        String positionId = ThreadLocalUtil.get();
        DepartmentTreeNode node = (DepartmentTreeNode) expenditureReview(positionId, departmentTreeNode, id$rel);
        if (Objects.isNull(node))
            return Collections.emptyList();
        MadWorkflowEmployeeQo ruleDomain = getRuleDomain(billNo, vo, vo.getBasicInfo());
        ruleDomain.setDepartmentCode(node.getCode());
        List<String> validApprovals = departmentService.isValidApprovals(ruleDomain);
        return getUserIds(vo.getBasicInfo(), validApprovals);
    }

    private static @NotNull Map<String, TreeNode> mapAllDepartmentIdTree(List<? extends TreeNode> tree) {
        Map<String, TreeNode> result = new HashMap<>();
        result.putAll(tree.stream().collect(Collectors.toMap(TreeNode::getId, Function.identity(), (a, b) -> a)));
        List<TreeNode> children = tree.stream().flatMap(node -> node.getChildren().stream()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(children))
            result.putAll(mapAllDepartmentIdTree(children));
        return result;
    }

    private static @NotNull Map<String, TreeNode> mapAllDepartmentCodeTree(List<? extends TreeNode> tree) {
        Map<String, TreeNode> result = new HashMap<>();
        result.putAll(tree.stream().collect(Collectors.toMap(TreeNode::getCode, Function.identity(), (a, b) -> a)));
        List<TreeNode> children = tree.stream().flatMap(node -> node.getChildren().stream()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(children))
            result.putAll(mapAllDepartmentCodeTree(children));
        return result;
    }

    /**
     * 查询上级部门
     * @param positionId
     * @param departmentTreeNode
     * @param id$rel
     * @return
     */
    private TreeNode expenditureReview(String positionId, TreeNode departmentTreeNode, Map<String, TreeNode> id$rel) {
        if (Objects.isNull(departmentTreeNode))
            return null;
        if (positionId.endsWith("1"))
            return departmentTreeNode;
        Integer index = Integer.valueOf(positionId.substring(positionId.length() - 1));
        positionId = positionId.replace(String.valueOf(index), String.valueOf(index - 1));
        return expenditureReview(positionId, id$rel.get(departmentTreeNode.getPid()), id$rel);
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
