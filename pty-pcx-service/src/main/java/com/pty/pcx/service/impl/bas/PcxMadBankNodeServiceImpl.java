package com.pty.pcx.service.impl.bas;


import com.pty.pcx.api.bas.IPcxMadBankNodeService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.dto.mad.MadBankNodeDTO;
import com.pty.pcx.mad.IMadBankNodeExternalService;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Indexed
@Service
public class PcxMadBankNodeServiceImpl implements IPcxMadBankNodeService {

    @Autowired
    private Map<String, IMadBankNodeExternalService> madBankNodeExternalServiceMap;

    @Autowired
    private IBusinessRuleOptionService businessRuleOptionService;

    @Override
    public List<MadBankNodeDTO> select(PcxMadBaseQO pcxMadBaseQO) {
        PaOptionQO paOptionQo = createPaOptionQO(pcxMadBaseQO);
        String madDepartmentOptValue = businessRuleOptionService.getOptionValueByOptionCode(paOptionQo, BusinessRuleEnum.BusinessOptionEnum.MAD_BANK_NODE_SERVICE_BEAN.getOptCode());
        IMadBankNodeExternalService madBankNodeExternalService = madBankNodeExternalServiceMap.get(madDepartmentOptValue);
        Map<String, Object> param = createQueryParam(pcxMadBaseQO);
        return madBankNodeExternalService.select(param);
    }

    private Map<String, Object> createQueryParam(PcxMadBaseQO pcxMadBaseQO) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("bankNodeName", pcxMadBaseQO.getBankNodeName());
        param.put("bankNodeCode", pcxMadBaseQO.getBankNodeCode());
        param.put("bankNodeNameList",pcxMadBaseQO.getBankNodeNameList());
        return param;
    }

    private PaOptionQO createPaOptionQO(PcxMadBaseQO pcxDepartmentQO) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxDepartmentQO.getMofDivCode());
        paOptionQO.setAgyCode(pcxDepartmentQO.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxDepartmentQO.getFiscal()));
        paOptionQO.setTenantId(pcxDepartmentQO.getTenantId());
        return paOptionQO;
    }

}
