package com.pty.pcx.service.impl.calculationrule;

import com.pty.pcx.api.calculationrule.ParamEncapsuleStrategy;
import com.pty.pcx.dto.calculationrule.CalculationRuleDTO;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.pub.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 封装计算公式参数上下文类
 */
@Service
@Indexed
@Slf4j
public class ParamEncapsuleContext {
    private final Map<String, ParamEncapsuleStrategy> strategies;

    @Autowired
    public ParamEncapsuleContext(Map<String, ParamEncapsuleStrategy> strategies) {
        this.strategies = strategies;
    }

    /**
     * 封装参数入口
     *
     * @param calculationRuleDTO
     */
    public Map<String,Object> encapsulateParameters(CalculationRuleDTO calculationRuleDTO, Set<String> standCodeList) {
        Map<String, Object> result = new HashMap<>();
        String expenseType = calculationRuleDTO.getExpenseType();
        ParamEncapsuleStrategy strategy = strategies.get("paramEncapsule" + expenseType + "Strategy");
        if (strategy != null
                && CollectionUtil.isNotEmpty(standCodeList)
                && calculationRuleDTO.getMealAllowanceDTO() != null) {
            List<PcxBillExpStandResult> standResultList = new ArrayList<>();
            for (String s : standCodeList) {
                calculationRuleDTO.getMealAllowanceDTO().setStandardCode(s);
                PcxBillExpStandResult pcxBillExpStandResult = strategy.encapsulateParameters(calculationRuleDTO);
                if (pcxBillExpStandResult != null) {
                    standResultList.add(pcxBillExpStandResult);
                    result.put(s, pcxBillExpStandResult.getStandValue());
                } else {
                    //如果职级没有配标准，则返回0
                    result.put(s, "0");
                    log.debug("未查询到标准值，expenseType:{},standCode：{}", expenseType, s);
                }
            }
            calculationRuleDTO.setStandResultList(standResultList);
        }
        return result;
    }
}
