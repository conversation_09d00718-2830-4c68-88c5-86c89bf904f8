package com.pty.pcx.service.impl.block;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.block.IBaseBlockService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.enu.block.BlockBeanEnum;
import com.pty.pcx.dao.bas.PcxBasFormSettingDao;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.block.BlockPropertyQO;
import com.pty.pub.common.constant.PubConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@Indexed
public class FormSettingBlockServiceImpl implements IBaseBlockService {
    @Autowired
    private PcxBasFormSettingDao pcxBasFormSettingDao;
    @Override
    public List<BlockProperty> getBlockProperties(BlockPropertyQO blockPropertyQO) {
        log.info("查询表单元素：{}", JSON.toJSONString(blockPropertyQO));
        PcxBasFormSettingQO query = new PcxBasFormSettingQO();
        query.setFormClassify(FormSettingEnums.FormClassifyEnum.EXPENSE.getCode());
        query.setFiscal(blockPropertyQO.getFiscal());
        query.setMofDivCode(blockPropertyQO.getMofDivCode());
        query.setAgyCode(blockPropertyQO.getAgyCode());
        query.setBillFuncCode(blockPropertyQO.getBillFuncCodeEnum().getBit());
        query.setFormCodeList(blockPropertyQO.getBasExpTypeCodes());
        if (CollectionUtils.isEmpty(blockPropertyQO.getBasExpTypeCodes())){
            return Lists.newArrayList();
        }
        if (blockPropertyQO.getClassifyCode().equals(BlockBeanEnum.SPECIFICITY.getClassifyCode())){
            query.setFormType(FormSettingEnums.FormTypeEnum.EXPENSE_EXCLUSIVE.getCode());
        }
        if(BlockBeanEnum.EXPENSE_DETAIL.getClassifyCode().equals(blockPropertyQO.getClassifyCode())){
            query.setFormType(FormSettingEnums.FormTypeEnum.EXPENSE_DETAIL.getCode());
        }
        return pcxBasFormSettingDao.getBlockProperties(query);
    }
}
