package com.pty.pcx.service.impl.workflow2;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.pty.mad.entity.MadEmployee;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.IMadEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Indexed
@Service
public class SuperiorAuditIPositionService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;


    @Resource
    private PcxBillService pcxBillService;

    @Resource
    private IMadEmployeeService employeeService;
    @Override
    public List<String> getPositionIds() {
        return Collections.singletonList(PcxNodeEnum.superior_audit.getId());
    }

    @Override
    public List<String> findPositionUser(String billNo) {

        CheckMsg<PcxBillVO> msg = pcxBillService.view(billNo);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());

        PcxBillVO vo = msg.getData();
        String claimantCode = vo.getBasicInfo().getClaimantCode();
        String fiscal = vo.getBasicInfo().getFiscal();
        String mofDivCode = vo.getBasicInfo().getMofDivCode();
        List<MadEmployee> madEmployees = employeeService.listByMadCodes(Collections.singletonList(claimantCode), null, Integer.valueOf(fiscal), mofDivCode);
        Assert.state(!CollectionUtils.isEmpty(madEmployees), "未找到填报人信息{}", claimantCode);
        Assert.state(madEmployees.size() == 1, "人员信息{}有误,找到多个填报人", claimantCode);
        MadEmployee employee = madEmployees.get(0);
        if (StrUtil.isBlank(employee.getDirectLeaderUkExFiscal()))
            return Collections.emptyList();
        MadEmployee cond = new MadEmployee();
        cond.setUkExFiscal(employee.getDirectLeaderUkExFiscal());
        cond.setFiscal(Integer.valueOf(fiscal));
        List<MadEmployee> employees = employeeService.select(cond);
        if (CollectionUtils.isEmpty(employees))
            return Collections.emptyList();

        return Collections.singletonList(employees.get(0).getUserCode());
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
