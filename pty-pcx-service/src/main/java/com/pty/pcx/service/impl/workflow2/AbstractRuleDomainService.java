package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.CollectionUtils;
import com.pty.mad.common.qo.MadWorkflowEmployeeQo;
import com.pty.mad.entity.MadProject;
import com.pty.pcx.api.wit.IWitRuleService;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.dao.wit.WitRuleResultDetailDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpTravel;
import com.pty.pcx.entity.wit.WitRuleResultDetail;
import com.pty.pcx.qo.wit.PcxAuditRuleQO;
import com.pty.pcx.vo.bill.PcxBillBalanceVO;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pub.common.util.StringUtil;
import com.pty.rule.entity.PtyRule;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.pty.mad.api.IMadProjectService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 涉及规则的查询
 */
@Service
public abstract class AbstractRuleDomainService {

    @Resource
    private IMadProjectService madProjectService;

    @Resource
    private WitRuleResultDetailDao witRuleResultDetailDao;

    @Resource
    private IWitRuleService witRuleService;

    protected @NotNull MadWorkflowEmployeeQo getRuleDomain(String billId, PcxBillVO vo, PcxBill basicInfo) {
        Assert.state(vo != null, "无法扎到单据信息{}", billId);
        List<PcxBillExpBase> specificity = vo.getSpecificity();
        List<PcxBillBalanceVO> fundSource = ObjectUtils.firstNonNull(vo.getBudget(), Collections.emptyList());
        List<String> fellowerList = new ArrayList<>();
        specificity.forEach(spec -> {
            if (spec instanceof PcxBillExpTravel) {
                PcxBillExpTravel travel = (PcxBillExpTravel) spec;
                if (!CollectionUtils.isEmpty(travel.getSelectFollower()))
                    travel.getSelectFollower().forEach(follower -> fellowerList.add(follower.getEmpCode()));
            }
        });
        MadWorkflowEmployeeQo mwq = new MadWorkflowEmployeeQo();
        mwq.setBillId(basicInfo.getId());
        mwq.setAgyCode(basicInfo.getAgyCode());
        mwq.setFiscal(basicInfo.getFiscal());
        mwq.setInputAmt(basicInfo.getInputAmt().doubleValue());
        mwq.setClaimantCode(basicInfo.getClaimantCode());
        mwq.setMofDivCode(basicInfo.getMofDivCode());
        mwq.setTenantId(basicInfo.getTenantId());
        mwq.setDepartmentCode(basicInfo.getDepartmentCode());
        mwq.setFellowEmpCodes(fellowerList);
        mwq.setPositionId(ThreadLocalUtil.get());
        mwq.setExpenseCodes(StringUtil.isEmpty(basicInfo.getExpenseCodes()) ? Collections.emptyList() : Arrays.asList(basicInfo.getExpenseCodes().split(",")));

        appendUnPassRuleIds(basicInfo, mwq);
        appendProjectAndCategories(billId, basicInfo, fundSource, mwq);

        return mwq;
    }

    private void appendProjectAndCategories(String billId, PcxBill basicInfo, List<PcxBillBalanceVO> fundSource, MadWorkflowEmployeeQo mwq) {
        List<String> projectCodes = fundSource.stream().map(PcxBillBalanceVO::getProjectCode).distinct().collect(Collectors.toList());
        // 获取项目类型
        if (!CollectionUtils.isEmpty(projectCodes)) {
            MadProject cond = new MadProject();
            cond.setAgyCode(basicInfo.getAgyCode());
            cond.setMadCodes(projectCodes);
            cond.setFiscal(Integer.valueOf(basicInfo.getFiscal()));
            cond.setMofDivCode(basicInfo.getMofDivCode());
            cond.setIsEnabled(1);
            cond.setIsDeleted(0);

            List<MadProject> projects = madProjectService.select(cond);

            mwq.setProjectCodes(projectCodes);
            mwq.setProtypeCodes(projects.stream().map(MadProject::getProTypeCode).collect(Collectors.toList()));
        }
    }

    private void appendUnPassRuleIds(PcxBill basicInfo, MadWorkflowEmployeeQo mwq) {
        PcxAuditRuleQO ruleParam = new PcxAuditRuleQO();
        // 领导加签
        ruleParam.setField1("4");
        ruleParam.setAgyCode(basicInfo.getAgyCode());
        ruleParam.setFiscal(basicInfo.getFiscal());
        ruleParam.setMofDivCode(basicInfo.getMofDivCode());
        List<? extends PtyRule> rules = witRuleService.queryRuleList(ruleParam);
        if (!CollectionUtils.isEmpty(rules)) {
            WitRuleResultDetail ruleDetailParam = new WitRuleResultDetail();
            ruleDetailParam.setBillId(basicInfo.getId());
            ruleDetailParam.setFiscal(basicInfo.getFiscal());
            // 查询未处理的稽核失败记录
            ruleDetailParam.setStatus("2");
            ruleDetailParam.setRuleIds(rules.stream().map(PtyRule::getRuleId).collect(Collectors.toList()));
            List<WitRuleResultDetail> details = witRuleResultDetailDao.select(ruleDetailParam);
            mwq.setUnPassRuleIds(details.stream().map(WitRuleResultDetail::getRuleId).collect(Collectors.toList()));
        }
    }
}
