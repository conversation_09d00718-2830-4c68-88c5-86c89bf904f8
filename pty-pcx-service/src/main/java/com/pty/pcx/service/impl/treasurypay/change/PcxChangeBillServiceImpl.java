package com.pty.pcx.service.impl.treasurypay.change;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.treasurypay.change.IPcxChangeBillPayDetailService;
import com.pty.pcx.api.treasurypay.change.IPcxChangeBillService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillPayDetailService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.balance.IBalanceExternalService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.treasurypay.change.PcxChangeBillDao;
import com.pty.pcx.dao.treasurypay.change.PcxChangeBillPayDetailDao;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.dto.pa.PaUserDTO;
import com.pty.pcx.entity.treasurypay.change.PcxChangeBill;
import com.pty.pcx.entity.treasurypay.change.PcxChangeBillPayDetail;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.qo.treasurypay.change.PcxChangeBillQO;
import com.pty.pcx.qo.treasurypay.detail.PcxBillPayDetailQO;
import com.pty.pcx.qo.workflow2.ProcessHistoryQO;
import com.pty.pcx.qo.workflow2.SubmitQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.treasurypay.change.PcxChangeBillPayDetailVO;
import com.pty.pcx.vo.treasurypay.change.PcxChangeBillVO;
import com.pty.pcx.vo.workflow2.ProcessHistoryVO;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class PcxChangeBillServiceImpl implements IPcxChangeBillService {

    @Autowired
    private PcxChangeBillDao changeBillDao;
    @Autowired
    private IPcxChangeBillPayDetailService pcxChangeBillPayDetailService;
    @Autowired
    private IPcxMadEmployeeService madEmployeeService;
    @Autowired
    private IPcxBillPayDetailService billPayDetailService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private PcxBillService pcxBillService;
    @Autowired
    private BatchServiceUtil batchServiceUtil;
    @Autowired
    private IProcessService processService;
    @Autowired
    private IBalanceExternalService balanceExternalService;

    @Override
    public List<PcxChangeBill> select(PcxChangeBillQO changeBillQo) {
        return changeBillDao.select(changeBillQo);
    }

    @Override
    public List<PcxChangeBillVO> getChangeBillByBillIds(PcxChangeBillQO changeBillQo) {
        // 检查传入的账单ID列表是否为空，如果为空则直接返回空列表
        if (CollectionUtil.isEmpty(changeBillQo.getChangeBillIds())) {
            return Collections.emptyList();
        }
        List<PcxChangeBillVO> result = new ArrayList<>();
        // 将账单ID列表分割为多个子列表，每个子列表包含50个ID，以避免一次性查询过多数据
        List<List<String>> partition = Lists.partition(changeBillQo.getChangeBillIds(), 50);
        // 遍历每个子列表，查询数据库，并将结果添加到结果列表中
        partition.forEach(part -> {
            result.addAll(changeBillDao.selectByBillIds(part, changeBillQo.getChangeBillStatus()));
        });

        // 如果查询结果为空，则直接返回结果列表
        if (CollectionUtil.isEmpty(result)) {
            return result;
        }
        List<PcxChangeBillVO> filterResult = new ArrayList<>();
        filterResult = result.stream()
                .filter(item -> containsAnyKeyword(item.getChangeType(), changeBillQo.getChangeTypeList()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(filterResult)) {
            return filterResult;
        }
        // 查询每个变更账单对应的支付详情
        List<PcxChangeBillPayDetailVO> changeBillPayDetailList = pcxChangeBillPayDetailService.selectByChangeIds(filterResult.stream().map(PcxChangeBillVO::getId).collect(Collectors.toList()));
        // 使用Stream流实现按BillId分组
        Map<String, List<PcxChangeBillPayDetailVO>> idToPayDetailMap = changeBillPayDetailList.stream()
                .collect(Collectors.groupingBy(PcxChangeBillPayDetailVO::getBillId));

        // 将支付详情列表与变更账单列表进行关联
        filterResult.forEach(changeBillVO -> {
            List<PcxChangeBillPayDetailVO> payDetailVOList = idToPayDetailMap.getOrDefault(changeBillVO.getBillId(), new ArrayList<>());
            changeBillVO.setAllBillPayDetailVOList(payDetailVOList);
        });
        // 返回包含所有查询到的账单及其支付详情的列表
        return filterResult;
    }

    // 定义一个方法来检查字符串是否包含任何关键字
    private static boolean containsAnyKeyword(String item, List<String> keywords) {
        //如果为空则默认为true
        if (CollectionUtil.isEmpty(keywords)) {
            return true;
        }
        // 遍历每个关键字，检查是否存在于字符串中
        for (String keyword : keywords) {
            if (item.contains(keyword)) {
                return true; // 如果包含关键字，则返回true
            }
        }
        return false; // 如果没有包含任何关键字，则返回false
    }

    @Override
    public CheckMsg save(PcxChangeBillQO changeBillQo) {
        PcxChangeBill changeBill = new PcxChangeBill();
        BeanUtils.copyProperties(changeBillQo, changeBill);
        changeBillDao.insert(changeBill);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    public CheckMsg batchSave(List<PcxChangeBill> changeBillList) {
        if (CollectionUtil.isEmpty(changeBillList)) {
            return CheckMsg.fail("保存的数据为空，无法进行操作，请检查 [pcxBasOfficialCardList] 参数是否正确。");
        }
        batchServiceUtil.batchProcess(changeBillList, PcxChangeBillDao.class, PcxChangeBillDao::insert);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg submitChangeBill(PcxChangeBillQO changeBillQo) {
        // 校验保存必填参数项
        CheckMsg checkMsg = validateChangeBill(changeBillQo);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }

        // 校验并获取变更类型
        CheckMsg changeTypeMsg = validateChangeTypes(changeBillQo.getChangeType());
        if (!changeTypeMsg.isSuccess()) {
            return changeTypeMsg;
        }

        // 校验单据是否已存在变更（变更中状态）
        List<PcxChangeBillVO> pcxChangeBillVOS = changeBillDao.selectByBillIds(Arrays.asList(changeBillQo.getBillId()), null);
        if (CollectionUtil.isNotEmpty(pcxChangeBillVOS)) {
            return CheckMsg.fail("该单据已变更在途,不允许重复发起");
        }

        // 构建并保存变更单对象
        PcxChangeBill changeBill = buildPcxChangeBillFromQO(changeBillQo);

        // 获取接收用户人员编码
        CheckMsg<List<Map<String, String>>> changeBillFlowPos = getChangeBillFlowPos(changeBillQo);
        if (!changeBillFlowPos.isSuccess()) {
            return CheckMsg.fail("发起变更失败:" + changeBillFlowPos.getMsgInfo());
        }

        // 设置接收用户
        List<Map<String, String>> data = changeBillFlowPos.getData();
        changeBill.setReceiver(CollectionUtil.isNotEmpty(data) ? data.get(0).get("claimantCode") : null);

        // 保存变更单
        changeBillDao.insert(changeBill);

        // 处理支付明细并保存
        handleChangePayDetails(changeBillQo, changeBill);

        BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                .billId(changeBillQo.getBillId())
                .billStatus(BillStatusEnum.CHANGE.getCode())
                .build();
        // 修改报销单据状态为变更状态
        billMainService.updateStatus(updateStatusDTO);

        // 发起变更单不启用工作流
        return CheckMsg.success("提交成功");
    }

    /**
     * 处理变更支付明细
     * 当变更类型包含结算方式或预算指标时，需要处理相关的支付明细
     *
     * @param changeBillQo    变更账单查询对象，包含查询支付明细所需的参数
     * @param changeBill      变更账单对象，包含变更账单的详细信息
     */
    private void handleChangePayDetails(PcxChangeBillQO changeBillQo, PcxChangeBill changeBill) {
        //TODO 250310去掉判断是否包含结算方式或预算指标变更类型
        // 获取已作废的支付明细
        PcxBillPayDetailQO pcxBillPayDetailQO = buildPayDetailQuery(changeBillQo);

        // 获取支付明细列表
        CheckMsg<List<PcxBillPayDetail>> resultPayDetailList = billPayDetailService.changingPayDetails(pcxBillPayDetailQO);
        List<PcxBillPayDetail> sourcePayDetails = resultPayDetailList.getData();

        if (CollectionUtil.isNotEmpty(sourcePayDetails)) {
            List<PcxChangeBillPayDetail> changeBillPayDetails = new ArrayList<>();
            sourcePayDetails.forEach(sourceDetail -> {
                PcxChangeBillPayDetail changePayDetail = new PcxChangeBillPayDetail();
                BeanUtil.copyProperties(sourceDetail, changePayDetail);
                changePayDetail.setId(StringUtil.getUUID());
                changePayDetail.setChangeBillId(changeBill.getId());
                changePayDetail.setInfoSource(PcxConstant.BEFORE_CHANGE);
                changePayDetail.setInfoSourceId(sourceDetail.getId());
                changeBillPayDetails.add(changePayDetail);
            });
            pcxChangeBillPayDetailService.batchSave(changeBillPayDetails);
        }
    }

    /**
     * 构建支付明细查询对象
     *
     * @param changeBillQo 变更账单查询对象
     * @return PcxBillPayDetailQO 查询对象
     */
    private PcxBillPayDetailQO buildPayDetailQuery(PcxChangeBillQO changeBillQo) {
        PcxBillPayDetailQO pcxBillPayDetailQO = new PcxBillPayDetailQO();
        pcxBillPayDetailQO.setAgyCode(changeBillQo.getAgyCode());
        pcxBillPayDetailQO.setFiscal(changeBillQo.getFiscal());
        pcxBillPayDetailQO.setMofDivCode(changeBillQo.getMofDivCode());
        pcxBillPayDetailQO.setBillId(changeBillQo.getBillId());
        pcxBillPayDetailQO.setTenantId(changeBillQo.getTenantId());

        // 构建支付明细条目
        List<PcxBillPayDetailQO.ChangingDetailEntry> params = new ArrayList<>();
        addDetailEntries(params, changeBillQo.getChangeAccountNos(), null);
        addDetailEntries(params, changeBillQo.getChangeBalanceIds(), null);
        pcxBillPayDetailQO.setEntries(params);

        return pcxBillPayDetailQO;
    }

    /**
     * 添加支付明细条目
     *
     * @param params     明细条目列表
     * @param accountNos 或余额id集合
     * @param balanceId  余额id
     */
    private void addDetailEntries(List<PcxBillPayDetailQO.ChangingDetailEntry> params, List<String> accountNos, String balanceId) {
        if (CollectionUtil.isNotEmpty(accountNos)) {
            accountNos.forEach(account -> {
                PcxBillPayDetailQO.ChangingDetailEntry entity = PcxBillPayDetailQO.ChangingDetailEntry.builder()
                        .balanceId(balanceId)
                        .settlementType(null)
                        .payeeAccountNo(account)
                        .build();
                params.add(entity);
            });
        }
    }


    @Override
    public CheckMsg<List<Map<String, String>>> getChangeBillFlowPos(PcxChangeBillQO changeBillQo) {
        List<Map<String, String>> result = new ArrayList<>();
        // 校验并获取变更类型
        CheckMsg<List<BillChangeTypeEnum>> changeTypeMsg = validateChangeTypes(changeBillQo.getChangeType());
        if (!changeTypeMsg.isSuccess()) {
            return CheckMsg.fail(changeTypeMsg.getMsgInfo());
        }
        CheckMsg<PcxBillVO> view = pcxBillService.view(changeBillQo.getBillId(), Arrays.asList(PositionBlockEnum.BASIC_INFO.code));
        if (!view.isSuccess()) {
            return CheckMsg.success(result);
        }
        PcxBillVO data = view.getData();
        //获取单据审批流程中终审的用户，以及对应的部门、岗位
        List<BillChangeTypeEnum> changeTypesList = changeTypeMsg.getData();
        if (CollectionUtil.isEmpty(changeTypesList)) {
            return CheckMsg.fail("变更类型不允许为空");
        }
        Map<String, String> isData = new HashMap<>();

        if (changeTypesList.contains(BillChangeTypeEnum.BUDGET_INDEX)) {
            ProcessHistoryQO qo = new ProcessHistoryQO();
            qo.setAgyCode(data.getBasicInfo().getAgyCode());
            qo.setMofDivCode(data.getBasicInfo().getMofDivCode());
            qo.setBillId(changeBillQo.getBillId());
            qo.setBillFuncCode(data.getBasicInfo().getBillFuncCode());
            qo.setTenantId(changeBillQo.getTenantId());
            CheckMsg<ProcessHistoryVO> processHistoryVOCheckMsg = processService.queryNearestApproveUser(qo, "finance_audit_1");
            if (!processHistoryVOCheckMsg.isSuccess()) {
                return CheckMsg.fail("获取变更接收岗位信息失败," + processHistoryVOCheckMsg.getMsgInfo());
            }
            ProcessHistoryVO processHistory = processHistoryVOCheckMsg.getData();
            List<PaUserDTO> userCodes = processHistory.getUserCodes();

            if (userCodes == null || userCodes.isEmpty()) {
                return CheckMsg.fail("未找到审核用户信息");
            }

            // 获取审核用户部门信息
            PcxMadBaseQO madBaseQo = new PcxMadBaseQO();
            madBaseQo.setUserCode(userCodes.get(0).getUserCode());
            madBaseQo.setAgyCode(data.getBasicInfo().getAgyCode());
            madBaseQo.setFiscal(data.getBasicInfo().getFiscal());
            madBaseQo.setMofDivCode(data.getBasicInfo().getMofDivCode());
            madBaseQo.setTenantId(changeBillQo.getTenantId());
            List<MadEmployeeDTO> empData = madEmployeeService.select(madBaseQo);

            isData.put("executionStep", processHistory.getPositionName());
            isData.put("claimantName", userCodes.get(0).getUserName());
            isData.put("claimantCode", CollectionUtil.isNotEmpty(empData) ? empData.get(0).getEmployeeCode() : "");
            isData.put("departmentName", CollectionUtil.isNotEmpty(empData) ? empData.get(0).getDepartmentName() : "");
        } else {
            isData.put("executionStep", "报销人");
            isData.put("claimantName", data.getBasicInfo().getClaimantName());
            isData.put("departmentName", data.getBasicInfo().getDepartmentName());
            isData.put("claimantCode", data.getBasicInfo().getClaimantCode());
        }

        // 默认值
        isData.put("submitOpinions", "/");
        isData.put("auditorStatus", "提交");

        // 目前是单条，后续可能多条
        result.add(isData);
        return CheckMsg.success(result);
    }


    @Override
    @Transactional
    public CheckMsg<?> saveChangeBillInfo(PcxChangeBillQO changeBillQo) {
        // 调用 validateRequiredFields 方法进行验证
        CheckMsg<?> validationResult = validateRequiredFields(changeBillQo);
        if (!validationResult.isSuccess()) {
            return validationResult;  // 如果验证失败，直接返回错误信息
        }

        //1如果只是事由则存主表即可
        // 1.1 事由部分需要校验源事由和现在事由是否相同，如果相同则进行提示
        List<String> changeTypeEnum = changeBillQo.getChangeTypeList();
        if (changeTypeEnum.contains(BillChangeTypeEnum.BASIC_INFO.getCode())) {
            CheckMsg<?> reasonMsg = validateChangeReason(changeBillQo);
            if (!reasonMsg.isSuccess()) {
                return reasonMsg;
            }
        }

        // 校验银行信息变更
        if (changeTypeEnum.contains(BillChangeTypeEnum.SETTLEMENT_TYPE.getCode())) {
            CheckMsg<String> bankValidationMsg = validateBankInfo(changeBillQo.getSourceBillCardList(), changeBillQo.getChangeBillCardList());
            if (!bankValidationMsg.isSuccess()) {
                return bankValidationMsg;
            }
        }

        // 校验支付明细
        if (changeTypeEnum.contains(BillChangeTypeEnum.BUDGET_INDEX.getCode())) {
            //2.2根据 支付明细来校验
            CheckMsg checkMsg = validateBillChange(changeBillQo);
            if (!checkMsg.isSuccess()) {
                return checkMsg;
            }
        }
        //处理单据保存以及明细计算
        SpringUtil.getBean(PcxChangeBillServiceImpl.class).updateChangeBillInfo(changeBillQo, changeTypeEnum);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Transactional(rollbackFor = Exception.class) //保存更新单据信息，以及明细单
    public void updateChangeBillInfo(PcxChangeBillQO changeBillQo, List<String> changeTypeEnum) {
        //判断是否开启工作流
        if (changeTypeEnum.contains(BillChangeTypeEnum.BUDGET_INDEX)) {
            SubmitQO startParam = new SubmitQO();
            startParam.setBillFuncCode(IProcessService.BILL_TYPE_PAY_DETAIL_CHANGE);
            startParam.setBillId(changeBillQo.getBillId());
            startParam.setSubmitter(changeBillQo.getUserCode());
            startParam.setSubmitterName(changeBillQo.getUserName());
            startParam.setAgyCode(changeBillQo.getAgyCode());
            startParam.setMofDivCode(changeBillQo.getMofDivCode());
            startParam.setComment(changeBillQo.getCorrectionDesc());
            CheckMsg<Void> voidCheckMsg = processService.submitTask(startParam);
            if (!voidCheckMsg.isSuccess()) {
                throw new CommonException("发起变更流程失败:" + voidCheckMsg.getMsgInfo());
            }
        }
        List<PcxChangeBillPayDetailVO> changeBillPayDetailList = pcxChangeBillPayDetailService.selectByChangeIds(Arrays.asList(changeBillQo.getId()));
        List<PcxChangeBillPayDetailVO> changeBillPayDetailVOList = filterByChangeInfoSource(changeBillPayDetailList, PcxConstant.AFTER_CHANGE);

        if (changeTypeEnum.contains(BillChangeTypeEnum.SETTLEMENT_TYPE.getCode()) || changeTypeEnum.contains(BillChangeTypeEnum.BUDGET_INDEX.getCode())) {
            Map<String, PcxChangeBillVO.ChangeBillCardInfo> resultMap = new HashMap<>();
            changeBillQo.getChangeBillCardList().forEach(payee ->
                    payee.getIds().forEach(id -> resultMap.put(id, payee))
            );

            if (CollectionUtil.isNotEmpty(changeBillPayDetailVOList)) {
                List<PcxChangeBillPayDetail> changeBillPayDetails = updatePayDetails(changeBillPayDetailVOList, resultMap);// 调用公共方法
                batchServiceUtil.batchProcess(changeBillPayDetails, PcxChangeBillPayDetailDao.class, PcxChangeBillPayDetailDao::updateById);
            } else {
                List<PcxChangeBillPayDetail> newPayDetails = createNewPayDetails(changeBillPayDetailList, resultMap);// 调用公共方法
                pcxChangeBillPayDetailService.batchSave(newPayDetails);
            }
        }else{
            if(CollectionUtil.isEmpty(changeBillPayDetailVOList)){
                for(PcxChangeBillPayDetailVO pcxChangeBillPayDetailVO:changeBillPayDetailList){
                    pcxChangeBillPayDetailVO.setInfoSource(PcxConstant.AFTER_CHANGE);
                    pcxChangeBillPayDetailVO.setId(StringUtil.getUUID());
                }
                List<PcxChangeBillPayDetail> payDetails=BeanUtil.copyToList(changeBillPayDetailList,PcxChangeBillPayDetail.class);
                pcxChangeBillPayDetailService.batchSave(payDetails);
            }
        }

        PcxChangeBill changeBill = new PcxChangeBill();
        changeBill.setId(changeBillQo.getId());
        changeBill.setChangeBillStatus(changeBillQo.getChangeTypeList().contains(BillChangeTypeEnum.BUDGET_INDEX.getCode()) ?
                BillChangeStatusEnum.SUBMITTED.getCode() : BillChangeStatusEnum.AWAIT_CONFIRMATION.getCode());
        changeBill.setChangeReason(changeBillQo.getChangeReason());
        changeBill.setModifier(changeBillQo.getUserCode());
        changeBill.setModifierName(changeBillQo.getUserName());
        changeBill.setModifiedTime(DateUtil.nowTime());
        changeBillDao.updateById(changeBill);
    }

    /**
     * 更新支付详情列表
     * 根据提供的变更账单支付详情VO列表和结果映射，更新支付详情的收件人账户信息
     *
     * @param changeBillPayDetailList 变更账单支付详情VO列表，用于更新支付详情
     * @param resultMap 包含支付详情信息源ID与变更账单卡片信息的映射，用于获取更新后的账户信息
     * @return 返回一个更新后的支付详情列表
     */
    private  List<PcxChangeBillPayDetail>  updatePayDetails(List<PcxChangeBillPayDetailVO> changeBillPayDetailList, Map<String, PcxChangeBillVO.ChangeBillCardInfo> resultMap) {
        List<PcxChangeBillPayDetail> upPayDetails = new ArrayList<>();
        for (PcxChangeBillPayDetailVO payDetail : changeBillPayDetailList) {
            if (null == resultMap.get(payDetail.getInfoSourceId())) {
                continue;
            }
            PcxChangeBillVO.ChangeBillCardInfo changeBillCardInfo = resultMap.get(payDetail.getInfoSourceId());
            payDetail.setPayeeAccountName(changeBillCardInfo.getPayeeAccountName());
            payDetail.setPayeeBankCode(changeBillCardInfo.getPayeeBankCode());
            payDetail.setPayeeBankName(changeBillCardInfo.getPayeeBankName());
            payDetail.setPayeeAccountNo(changeBillCardInfo.getPayeeAccountNo());
            payDetail.setPayeeAccountCity(changeBillCardInfo.getPayeeAccountCity());
            payDetail.setPayeeBankNodeNo(changeBillCardInfo.getPayeeBankNodeNo());
            payDetail.setPayeeBankNodeName(changeBillCardInfo.getPayeeBankNodeName());
            payDetail.setPayeeAccountTypeCode(changeBillCardInfo.getPayeeAccountTypeCode());
            payDetail.setPayeeAccountTypeName(changeBillCardInfo.getPayeeAccountTypeName());
            upPayDetails.add(payDetail);
        }
        return upPayDetails;
    }

    /**
     * 创建新的支付详情列表
     * 根据变更账单支付详情VO列表和结果映射，创建并返回一个新的支付详情列表
     * 此方法主要负责将支付详情VO转换为支付详情对象，并根据结果映射更新支付信息
     *
     * @param changeBillPayDetailList 变更账单支付详情VO列表，用于创建新的支付详情
     * @param resultMap 包含支付信息的映射，键为信息来源ID，值为变更账单卡片信息
     * @return 返回新创建的支付详情列表
     */
    private  List<PcxChangeBillPayDetail> createNewPayDetails(List<PcxChangeBillPayDetailVO> changeBillPayDetailList, Map<String, PcxChangeBillVO.ChangeBillCardInfo> resultMap) {
        List<PcxChangeBillPayDetail> newPayDetails = new ArrayList<>();
        for (PcxChangeBillPayDetailVO payDetail : changeBillPayDetailList) {
            if (null == resultMap.get(payDetail.getInfoSourceId())) {
                continue;
            }
            PcxChangeBillPayDetail billPayDetail = new PcxChangeBillPayDetail();
            BeanUtils.copyProperties(payDetail, billPayDetail);
            PcxChangeBillVO.ChangeBillCardInfo changeBillCardInfo = resultMap.get(payDetail.getInfoSourceId());
            billPayDetail.setPayeeAccountName(changeBillCardInfo.getPayeeAccountName());
            billPayDetail.setPayeeBankCode(changeBillCardInfo.getPayeeBankCode());
            billPayDetail.setPayeeBankName(changeBillCardInfo.getPayeeBankName());
            billPayDetail.setPayeeAccountNo(changeBillCardInfo.getPayeeAccountNo());
            billPayDetail.setPayeeAccountCity(changeBillCardInfo.getPayeeAccountCity());
            billPayDetail.setPayeeBankNodeNo(changeBillCardInfo.getPayeeBankNodeNo());
            billPayDetail.setPayeeBankNodeName(changeBillCardInfo.getPayeeBankNodeName());
            billPayDetail.setPayeeAccountTypeCode(changeBillCardInfo.getPayeeAccountTypeCode());
            billPayDetail.setPayeeAccountTypeName(changeBillCardInfo.getPayeeAccountTypeName());
            billPayDetail.setInfoSource(PcxConstant.AFTER_CHANGE);
            billPayDetail.setId(StringUtil.getUUID());
            newPayDetails.add(billPayDetail);
        }
        return newPayDetails;
    }

    /**
     * 校验变更后事由
     * @param changeBillQo
     * @return
     */
    private CheckMsg<?> validateChangeReason(PcxChangeBillQO changeBillQo) {
        if (StringUtil.isEmpty(changeBillQo.getChangeReason())) {
            return CheckMsg.fail("变更后事由不能为空");
        }
        if (changeBillQo.getSourceReason().equals(changeBillQo.getChangeReason())) {
            return CheckMsg.fail("变更后事由不能和原事由一样");
        }
        return CheckMsg.success();
    }

    private CheckMsg<String> validateBankInfo(List<PcxChangeBillVO.ChangeBillCardInfo> sourceBillCardList, List<PcxChangeBillVO.ChangeBillCardInfo> changeBillCardList) {
        // 如果 SourceBillCardList 或 ChangeBillCardList 为 null，返回错误信息
        if (CollectionUtil.isEmpty(sourceBillCardList) || CollectionUtil.isEmpty(changeBillCardList)) {
            return CheckMsg.fail("变更后收款银行账户信息不能为空");
        }

        // 创建一个集合用于存储变更前银行卡的唯一值（卡号 + 开户行 + 名称）
        Set<String> sourceCardSet = new HashSet<>();

        // 遍历变更前银行卡信息，生成唯一值并存入集合
        for (PcxChangeBillVO.ChangeBillCardInfo cardInfo : sourceBillCardList) {
            String uniqueValue = generateUniqueValue(cardInfo);
            sourceCardSet.add(uniqueValue);
        }

        // 遍历变更后的银行卡信息，检查是否有不同的唯一值
        for (PcxChangeBillVO.ChangeBillCardInfo cardInfo : changeBillCardList) {
            String uniqueValue = generateUniqueValue(cardInfo);
            if (sourceCardSet.contains(uniqueValue)) {
                return CheckMsg.fail("收款银行账户信息未改变");
            }
        }

        // 如果没有发现变更，返回银行卡信息一致的提示
        return CheckMsg.success();
    }

    // 生成银行卡信息的唯一值，基于卡号 + 开户行 + 银行名称
    private String generateUniqueValue(PcxChangeBillVO.ChangeBillCardInfo cardInfo) {
        return cardInfo.getPayeeAccountNo() + cardInfo.getPayeeAccountName() + cardInfo.getPayeeBankName();
    }

    @Override
    public CheckMsg<PcxChangeBillVO> getChangeBillInfo(PcxChangeBillQO changeBillQo) {
        // 1：（变更单据）创建一个PcxChangeBillVO对象，用于封装查询结果
        PcxChangeBillVO changeBillVO = new PcxChangeBillVO();
        // 2：校验数据：判断单据号billId校验请求参数
        if (StringUtil.isEmpty(changeBillQo.getBillId())) {
            return CheckMsg.fail("单据ID不能为空");
        }
        //查询变更单单据 根据单据id ，在根据单据状态(不查询已确认状态的数据)
        List<PcxChangeBillVO> pcxChangeBillVOS = changeBillDao.selectByBillIds(Arrays.asList(changeBillQo.getBillId()), null);
        if (CollectionUtil.isEmpty(pcxChangeBillVOS)) {
            return CheckMsg.fail("未查询到变更单单据信息");
        }
        //获取变更类型
        changeBillVO = pcxChangeBillVOS.get(0);
        CheckMsg<List<BillChangeTypeEnum>> changeTypeMsg = validateChangeTypes(changeBillVO.getChangeType());
        if (!changeTypeMsg.isSuccess()) {
            return CheckMsg.fail(changeTypeMsg.getMsgInfo());
        }
        List<BillChangeTypeEnum> changeTypeList = changeTypeMsg.getData();
        //setChangeTypeList 类型是 List<String>
        changeBillVO.setChangeTypeList(changeTypeList.stream().map(BillChangeTypeEnum::getCode).collect(Collectors.toList()));
        // 判断类型处理数据，基于数据分组出
        if (changeTypeList.contains(BillChangeTypeEnum.SETTLEMENT_TYPE) || changeTypeList.contains(BillChangeTypeEnum.BUDGET_INDEX)) {
            // 查询变更支付明细
            List<PcxChangeBillPayDetailVO> changeBillPayDetailList = pcxChangeBillPayDetailService.selectByChangeIds(
                    pcxChangeBillVOS.stream().map(PcxChangeBillVO::getId).collect(Collectors.toList())
            );
            // 处理变更前的银行卡信息
            List<PcxChangeBillPayDetailVO> sourcePayDetailList = filterByChangeInfoSource(changeBillPayDetailList, PcxConstant.BEFORE_CHANGE);
            changeBillVO.setSourceBillCardList(processBillCardInfo(sourcePayDetailList));

            // 处理变更后的银行卡信息
            List<PcxChangeBillPayDetailVO> changePayDetailList = filterByChangeInfoSource(changeBillPayDetailList, PcxConstant.AFTER_CHANGE);
            changeBillVO.setChangeBillCardList(processBillCardInfo(changePayDetailList));

            //针对指标的数据处理逻辑后续补充
        }
        return CheckMsg.success(changeBillVO);
    }


    // 过滤变更信息来源的方法：根据infoSource筛选出相应的支付明细
    private List<PcxChangeBillPayDetailVO> filterByChangeInfoSource(List<PcxChangeBillPayDetailVO> payDetailList, String infoSource) {
        return payDetailList.stream()
                .filter(detail -> infoSource.equals(detail.getInfoSource()))  // 根据infoSource过滤
                .collect(Collectors.toList());  // 收集成列表返回
    }

    /**
     * 处理变更单的银行卡信息
     * 该方法对支付明细列表进行处理，按银行卡账号分组，收集每个账号的支付详情ID，并计算总金额
     *
     * @param payDetailList 支付明细列表，包含各个支付账号的支付详情
     * @return 返回一个列表，包含每个银行卡账号对应的变更单信息对象
     */
    private List<PcxChangeBillVO.ChangeBillCardInfo> processBillCardInfo(List<PcxChangeBillPayDetailVO> payDetailList) {
        if (payDetailList == null || payDetailList.isEmpty()) {
            return Collections.emptyList();
        }

        return payDetailList.stream()
                .collect(Collectors.groupingBy(PcxChangeBillPayDetailVO::getPayeeAccountNo))  // 按银行卡账号分组
                .entrySet().stream()
                .map(entry -> {
                    List<PcxChangeBillPayDetailVO> details = entry.getValue();
                    PcxChangeBillVO.ChangeBillCardInfo cardInfo = new PcxChangeBillVO.ChangeBillCardInfo();
                    //一定存在值
                    PcxChangeBillPayDetailVO firstDetail = details.get(0);  // 获取该组的第一个明细
                    // 设置银行卡信息
                    cardInfo.setPayeeAccountName(firstDetail.getPayeeAccountName());
                    cardInfo.setPayeeBankCode(firstDetail.getPayeeBankCode());
                    cardInfo.setPayeeBankName(firstDetail.getPayeeBankName());
                    cardInfo.setPayeeAccountNo(firstDetail.getPayeeAccountNo());
                    cardInfo.setPayeeAccountCity(firstDetail.getPayeeAccountCity());
                    cardInfo.setPayeeBankNodeNo(firstDetail.getPayeeBankNodeNo());
                    cardInfo.setPayeeBankNodeName(firstDetail.getPayeeBankNodeName());
                    cardInfo.setPayeeAccountTypeCode(firstDetail.getPayeeAccountTypeCode());
                    cardInfo.setPayeeAccountTypeName(firstDetail.getPayeeAccountTypeName());
                    // 收集该组的ID并计算总金额
                    BigDecimal totalCheckAmt = BigDecimal.ZERO;
                    List<String> ids = new ArrayList<>();
                    for (PcxChangeBillPayDetailVO detail : details) {
                        ids.add(detail.getInfoSourceId());
                        BigDecimal checkAmt = Optional.ofNullable(detail.getCheckAmt()).orElse(BigDecimal.ZERO);
                        totalCheckAmt = totalCheckAmt.add(checkAmt);
                    }

                    cardInfo.setIds(ids);
                    cardInfo.setCheckAmt(totalCheckAmt);
                    return cardInfo;  // 返回银行卡信息对象
                })
                .collect(Collectors.toList());  // 收集成列表返回
    }

    @Override
    public CheckMsg<List<String>> getLastChangeNotes(PcxChangeBillQO changeBillQo) {
        //最大只查询5条更正说明信息返回
        if (StringUtil.isEmpty(changeBillQo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        // 校验区划
        if (StringUtil.isEmpty(changeBillQo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }      // 校验用户名称
        if (StringUtil.isEmpty(changeBillQo.getUserCode())) {
            return CheckMsg.fail("操作人编码不能为空");
        }
        PageInfo<String> pageInfo = PageHelper
                .startPage(1, 5).doSelectPageInfo(() -> {
                    changeBillDao.getCorrectionDesc(changeBillQo.getUserCode(), changeBillQo.getAgyCode(), changeBillQo.getMofDivCode());
                });
        return CheckMsg.success(pageInfo.getList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> confirmChangeBill(PcxChangeBillQO changeBillQo) {
        // 调用 validateRequiredFields 方法进行验证
        CheckMsg<?> validationResult = validateRequiredFields(changeBillQo);
        if (!validationResult.isSuccess()) {
            return validationResult;  // 如果验证失败，直接返回错误信息
        }
        //查询主单据 获取报销单据状态，
        PcxChangeBill changeBill = changeBillDao.selectById(changeBillQo.getId());
        if (ObjectUtils.isEmpty(changeBill)) {
            return CheckMsg.fail("变更单不存在");
        }
        String changeReason = null;
        //判断是否变更事由
        if (changeBillQo.getChangeTypeList().contains(BillChangeTypeEnum.BASIC_INFO.getCode())) {
            changeReason = changeBill.getChangeReason();
        }
        //TODO 250310 去掉根据类型判断是否需要拿变更单据明细
        List<PcxChangeBillPayDetailVO> changeBillPayDetailList = pcxChangeBillPayDetailService.selectByChangeIds(Arrays.asList(changeBill.getId()));
        if (CollectionUtil.isNotEmpty(changeBillPayDetailList)) {
            billPayDetailService.changingFinished(buildPayDetailEntity(filterByChangeInfoSource(changeBillPayDetailList, PcxConstant.AFTER_CHANGE)));
        }
        changeBill.setChangeBillStatus(BillChangeStatusEnum.CONFIRMED.getCode());
        changeBill.setChangeReason(changeBillQo.getChangeReason());
        changeBill.setModifier(changeBillQo.getUserCode());
        changeBill.setModifierName(changeBillQo.getUserName());
        changeBill.setModifiedTime(DateUtil.nowTime());
        //修改变更单主表状态
        changeBillDao.updateById(changeBill);
        BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                .billId(changeBill.getBillId())
                .billStatus(changeBill.getBillStatus())
                .reason(changeReason)
                .build();
        //还原报销单单据状态并基于判断更新事由
        billMainService.updateStatus(updateStatusDTO);
        return CheckMsg.success().setMsgInfo("确认变更成功");
    }

    @Override
    public CheckMsg<?> revokeApproval(PcxChangeBillQO changeBillQO) {
        //校验单据id
        if(StringUtil.isEmpty(changeBillQO.getId())){
            return CheckMsg.fail("变更单据id不能为空");
        }
        if(StringUtil.isEmpty(changeBillQO.getCorrectionDesc())){
            return CheckMsg.fail("更正说明不能为空");
        }
        //校验单据变更类型
        if(StringUtil.isEmpty(changeBillQO.getChangeTypeList())){
            return CheckMsg.fail("变更类型集合不能为空");
        }
        //todo 校验单据变更状态(此处是处理工作流的待补充 到指标变更时候在开发完善),需要判断流程岗位,以此来判断接收岗位，以及对应状态
        /*if(changeBillQO.getChangeTypeList().contains(BillChangeTypeEnum.BUDGET_INDEX.getCode())){

        }*/
        //目前暂时未记录这个单子的历史变更说明
        PcxChangeBill changeBill = new PcxChangeBill();
        changeBill.setId(changeBillQO.getId());
        changeBill.setChangeBillStatus(BillChangeStatusEnum.PENDING_SUBMISSION.getCode());
        changeBill.setCorrectionDesc(changeBillQO.getCorrectionDesc());
        changeBill.setModifier(changeBillQO.getUserCode());
        changeBill.setModifierName(changeBillQO.getUserName());
        changeBill.setModifiedTime(DateUtil.nowTime());
        changeBillDao.updateById(changeBill);
        return CheckMsg.success().setMsgInfo("驳回成功");
    }

    private List<PcxBillPayDetail> buildPayDetailEntity(List<PcxChangeBillPayDetailVO> changeBillPayDetailVOList) {
             //将PcxChangeBillPayDetailVO 转为 PcxBillPayDetail
        List<PcxBillPayDetail> newPayDetails = new ArrayList<>();
        for (PcxChangeBillPayDetailVO payDetail : changeBillPayDetailVOList) {
            PcxBillPayDetail newPayDetail = new PcxBillPayDetail();
            BeanUtils.copyProperties(payDetail, newPayDetail); // 复制已有属性
            // 手动赋值需要修改的字段
            newPayDetail.setId(payDetail.getInfoSourceId());
            newPayDetails.add(newPayDetail);
        }
        return newPayDetails;
    }

    private CheckMsg<List<BillChangeTypeEnum>> validateChangeTypes(String changeTypes) {
        // 校验空值
        if (StringUtil.isEmpty(changeTypes)) {
            return CheckMsg.fail("变更类型不能为空");
        }
        // 拆分并处理变更类型
        String[] changeTypeArray = changeTypes.split(",");
        if (changeTypeArray.length == 0) {
            return CheckMsg.fail("变更类型不能为空");
        }

        List<BillChangeTypeEnum> changeTypesList = new ArrayList<>();
        for (String changeTypeStr : changeTypeArray) {
            changeTypeStr = changeTypeStr.trim();
            if (changeTypeStr.isEmpty()) {
                continue; // 或者抛出异常
            }
            // 校验变更类型是否有效
            BillChangeTypeEnum status = BillChangeTypeEnum.getByCode(changeTypeStr);
            if (null == status) {
                return CheckMsg.fail("变更类型不存在");
            }
            // 添加有效的变更类型到列表中
            changeTypesList.add(status);
        }

        return CheckMsg.success(changeTypesList);
    }

    public CheckMsg<String> validateChangeBill(PcxChangeBillQO changeBillQo) {
        // 校验单位
        if (StringUtil.isEmpty(changeBillQo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        // 校验区划
        if (StringUtil.isEmpty(changeBillQo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }
        // 校验年度
        if (StringUtil.isEmpty(changeBillQo.getFiscal())) {
            return CheckMsg.fail("年度不能为空");
        }
        // 校验单据ID
        if (StringUtil.isEmpty(changeBillQo.getBillId())) {
            return CheckMsg.fail("单据ID不能为空");
        }
        // 校验单据ID
        if (StringUtil.isEmpty(changeBillQo.getBillStatus())) {
            return CheckMsg.fail("单据状态不能为空");
        }
        // 校验变更说明
        if (StringUtil.isEmpty(changeBillQo.getCorrectionDesc())) {
            return CheckMsg.fail("更正说明不能为空");
        }
        // 校验用户名称
        if (StringUtil.isEmpty(changeBillQo.getUserName())) {
            return CheckMsg.fail("操作人名称不能为空");
        }
        // 校验用户编码
        if (StringUtil.isEmpty(changeBillQo.getUserCode())) {
            return CheckMsg.fail("操作人编码不能为空");
        }
        // 校验变更类型
        if (StringUtil.isEmpty(changeBillQo.getChangeType())) {
            return CheckMsg.fail("变更类型不能为空");
        }
        // 所有校验通过
        return CheckMsg.success();
    }

    public PcxChangeBill buildPcxChangeBillFromQO(PcxChangeBillQO pcxChangeBillQO) {
        PcxChangeBill bill = new PcxChangeBill();
        // 映射所有字段
        bill.setId(StringUtil.getUUID());
        bill.setChangeBillNo(StringUtil.getUUID());
        bill.setChangeType(pcxChangeBillQO.getChangeType());
        bill.setChangeDate(DateUtil.getCurDate());
        //状态为:变更内容待提交
        bill.setChangeBillStatus(BillChangeStatusEnum.PENDING_SUBMISSION.getCode());
        bill.setBillId(pcxChangeBillQO.getBillId());
        //默认状态：变更中
        bill.setBillStatus(pcxChangeBillQO.getBillStatus());
        bill.setCorrectionDesc(pcxChangeBillQO.getCorrectionDesc());
        bill.setSourceReason(pcxChangeBillQO.getSourceReason());
        bill.setChangeReason(pcxChangeBillQO.getChangeReason());
        bill.setAgyCode(pcxChangeBillQO.getAgyCode());
        bill.setFiscal(pcxChangeBillQO.getFiscal());
        bill.setMofDivCode(pcxChangeBillQO.getMofDivCode());
        bill.setModifier(pcxChangeBillQO.getUserCode());
        bill.setModifierName(pcxChangeBillQO.getUserName());
        bill.setModifiedTime(DateUtil.getCurDate());
        bill.setCreatorCode(pcxChangeBillQO.getUserCode());
        bill.setCreatorName(pcxChangeBillQO.getUserName());
        //初次构建直接获取getModifiedTime
        bill.setCreatedTime(bill.getModifiedTime());
        bill.setTenantId(StringUtil.isEmpty(pcxChangeBillQO.getTenantId()) ? PtyContext.getTenantId() : pcxChangeBillQO.getTenantId());
        return bill;
    }

    // 校验变更后的单据信息 (待调整入参，确定不同检测的数据来源)
    public CheckMsg validateBillChange(PcxChangeBillQO changeBillQO) {
        // 1. 校验变更后的经费来源金额  （实际就是历史的支付明细金额和本次变更的支付明细 总金额比较）
        if (!validateFundSource(changeBillQO)) {
            return CheckMsg.fail("变更前的指标使用金额与变更后的指标使用金额不一致");
        }
        // 2. 校验用款计划余额是否足够
        CheckMsg checkMsg = validateBudgetBalance(changeBillQO);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }
        return CheckMsg.success("所有校验通过");
    }

    //
    // 校验变更后的经费来源金额不得低于当前付款申请单中该指标的使用金额
    private boolean validateFundSource(PcxChangeBillQO changeBillQO) {
        //变更前核定总金额
        BigDecimal afterBigDecimal = changeBillQO.getSourceBillPayDetailVOList().stream().map(PcxChangeBillPayDetail::getCheckAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //变更后的核定总金额
        BigDecimal beforeBigDecimal = changeBillQO.getChangeBillPayDetailVOList().stream().map(PcxChangeBillPayDetail::getCheckAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 比较afterBigDecimal 和 beforeBigDecimal金额大小 是否相等
        return afterBigDecimal.compareTo(beforeBigDecimal) == 0;
    }

    /**
     * 校验所选指标的用款计划余额是否足够
     *
     * @param changeBillQO
     * @return
     */
    private CheckMsg validateBudgetBalance(PcxChangeBillQO changeBillQO) {
        CheckMsg checkMsg = CheckMsg.success();
        List<PcxChangeBillPayDetailVO> changeBillPayDetailVOList = changeBillQO.getChangeBillPayDetailVOList();
        if (CollectionUtil.isEmpty(changeBillPayDetailVOList)) {
            return checkMsg;
        }
        Map<String, BigDecimal> repayMap = changeBillPayDetailVOList.stream()
                .filter(b -> Objects.nonNull(b.getCheckAmt()))
                .collect(Collectors.toMap(PcxChangeBillPayDetailVO::getBalanceId, PcxChangeBillPayDetailVO::getCheckAmt));
        List<String> balanceIds = new ArrayList<>(repayMap.keySet());
        if (CollectionUtil.isEmpty(balanceIds)) {
            return checkMsg;
        }
        // 根据指标ID查询指标信息
        List<BudBalanceDTO> balanceDtoList = balanceExternalService.getBalanceByIds(balanceIds);
        for (BudBalanceDTO balance : balanceDtoList) {
            BigDecimal totalAmt = Objects.isNull(balance.getTotalAmt()) ? BigDecimal.ZERO : balance.getTotalAmt();
            BigDecimal usedAmt = Objects.isNull(balance.getUsedAmt()) ? BigDecimal.ZERO : balance.getUsedAmt();
            // 指标剩余额度
            BigDecimal resAmt = totalAmt.subtract(usedAmt);
            // 还款金额
            BigDecimal repayAmt = repayMap.get(balance.getBalanceId());
            if (resAmt.subtract(repayAmt).signum() < 0) {
                return CheckMsg.fail(balance.getBalanceNo() + "指标剩余额度不足，无法继续操作！");
            }
        }
        return checkMsg;
    }

    public CheckMsg<?> validateRequiredFields(PcxChangeBillQO changeBillQo) {
        // 校验保存必填参数项
        if (StringUtil.isEmpty(changeBillQo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        // 校验区划
        if (StringUtil.isEmpty(changeBillQo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }
        // 校验年度
        if (StringUtil.isEmpty(changeBillQo.getFiscal())) {
            return CheckMsg.fail("年度不能为空");
        }
        // 校验单据ID
        if (StringUtil.isEmpty(changeBillQo.getBillId())) {
            return CheckMsg.fail("单据ID不能为空");
        }
        // 校验用户名称
        if (StringUtil.isEmpty(changeBillQo.getUserName())) {
            return CheckMsg.fail("操作人名称不能为空");
        }
        // 校验用户编码
        if (StringUtil.isEmpty(changeBillQo.getUserCode())) {
            return CheckMsg.fail("操作人编码不能为空");
        }
        // 校验变更类型
        if (CollectionUtil.isEmpty(changeBillQo.getChangeTypeList())) {
            return CheckMsg.fail("变更类型不能为空");
        }
        // 校验变更单ID
        if (StringUtil.isEmpty(changeBillQo.getId())) {
            return CheckMsg.fail("变更单ID不能为空");
        }

        // 所有必填字段验证通过
        return CheckMsg.success();
    }

}
