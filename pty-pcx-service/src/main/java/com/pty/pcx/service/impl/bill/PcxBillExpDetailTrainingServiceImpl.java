package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.pcx.api.bill.PcxBillExpDetailTrainingService;
import com.pty.pcx.dao.bill.PcxBillExpDetailTrainingDao;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pub.common.util.CollectionUtil;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Indexed
public class PcxBillExpDetailTrainingServiceImpl implements PcxBillExpDetailTrainingService {

    @Resource
    private PcxBillExpDetailTrainingDao pcxBillExpDetailTrainingDao;

    @Override
    public List<PcxBillExpDetailTraining> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpDetailTrainingDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailTraining>()
                .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpDetailTraining::getBillId, billIds)
                .eq(PcxBillExpDetailTraining::getAgyCode, agyCode)
                .eq(PcxBillExpDetailTraining::getFiscal, fiscal)
                .eq(PcxBillExpDetailTraining::getMofDivCode, mofDivCode));
    }

    @Override
    public List<PcxBillExpDetailTraining> selectBatchIds(List<String> collect) {
        return pcxBillExpDetailTrainingDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailTraining>()
                .in(CollectionUtil.isNotEmpty(collect), PcxBillExpDetailTraining::getId, collect));
    }

    @Override
    public void deleteBatchIds(List<String> collect) {
        pcxBillExpDetailTrainingDao.deleteBatchIds(collect);
    }

}