package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.pty.mad.entity.MadDepartment;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillAmtApportionDepartmentService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillBalanceDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillAmtApportionDepartment;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pty.mad.api.IMadDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 费用承担部门
 */
@Slf4j
@Service
public class CostBearingIPositionService implements IPositionService<String> {

    @Autowired
    private IMadDepartmentService departmentService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private PcxBillAmtApportionDepartmentService pcxBillAmtApportionDepartmentService;
    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Override
    public List<String> getPositionIds() {
        return Collections.singletonList(PcxNodeEnum.cost_bearing.getId());
    }

    @Override
    public List<String> findPositionUser(String billNo) {
        PcxBill basicInfo = billMainService.view(billNo);
        Assert.notNull(basicInfo, "未找到单据信息 {}", billNo);
        List<String> approvals = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> departments = pcxBillAmtApportionDepartmentService.selectByBillId(basicInfo.getId());
        if (CollUtil.isNotEmpty(departments)) {
            MadDepartment department = new MadDepartment();
            List<String> departmentMadCodes = departments.stream().map(PcxBillAmtApportionDepartment::getDepartmentCode).distinct().collect(Collectors.toList());
            department.setMadCodes(departmentMadCodes);
            department.setMofDivCode(basicInfo.getMofDivCode());
//            department.setAgyCode(basicInfo.getAgyCode());
            department.setFiscal(Integer.valueOf(basicInfo.getFiscal()));
            List<MadDepartment> madDepartments = departmentService.select(department);
            Assert.isTrue(!CollectionUtils.isEmpty(madDepartments), "未找到分摊部门信息");
            Assert.isTrue(madDepartments.size() == departmentMadCodes.size(), "分摊部门{}信息不存在", departmentMadCodes.removeAll(madDepartments.stream().map(MadDepartment::getMadCode).collect(Collectors.toList())));
            List<MadDepartment> noneLeaderDepartments = madDepartments.stream().filter(d -> StringUtils.isBlank(d.getDeptLeaderCode())).collect(Collectors.toList());
            Assert.isTrue(CollUtil.isEmpty(noneLeaderDepartments), "请设置确定预算分摊部门{}负责人", noneLeaderDepartments.stream().map(MadDepartment::getMadName).collect(Collectors.toList()));
            approvals.addAll(madDepartments.stream().map(dept -> {
                if (StrUtil.isNotBlank(dept.getDeptLeaderCode())) {
                    // 如果部门负责人和填报人一致，并且部门有支出审核人，则审批人员为有支出审核人，否则为部门负责人
                    if (dept.getDeptLeaderCode().equals(basicInfo.getClaimantCode()) && StrUtil.isNotBlank(dept.getApprovePersonnelCodes()))
                        return dept.getApprovePersonnelCodes();
                    return dept.getDeptLeaderCode();
                }
                return StrUtil.EMPTY;
            }).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        }
        return getUserIds(null, basicInfo.getMofDivCode(), Integer.valueOf(basicInfo.getFiscal()), approvals);
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
