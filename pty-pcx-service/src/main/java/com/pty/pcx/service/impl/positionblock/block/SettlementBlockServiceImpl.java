package com.pty.pcx.service.impl.positionblock.block;

import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bas.PcxSettlementRuleService;
import com.pty.pcx.api.positionblock.IPcxBlockService;
import com.pty.pcx.common.enu.PcxApprovalCondFieldEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.util.BudgetCtrlUtil;
import com.pty.pcx.entity.bas.PcxBasFormSetting;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.qo.bas.PcxSettlementRuleQO;
import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class SettlementBlockServiceImpl extends AbstractBlockService implements IPcxBlockService {

    private static final Set<String> FINANCE_FIELD_SET = new HashSet<>(Arrays.asList("balanceUK"));

    private static final Set<String> BUDGET_FIELD_SET = new HashSet<>(Arrays.asList("inputAmt","balanceUK"));

    private static final Set<String> CASHIER_FIELD_SET = new HashSet<>(Arrays.asList("receiveAccountName","receiveAccountNo","receiveBank"));

    @Autowired
    private PcxSettlementRuleService pcxSettlementRuleService;

    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Override
    public List<PcxBlockVO> getBlockInfo(PcxBlockCondQO qo) {
        if (ObjectUtils.isEmpty(qo)) {
            return new ArrayList<>();
        }
        // 获取启用的结算方式
        List<BaseDataVo> baseDataVos = getEnabledSettlementTypes(qo);
        // 获取并分组表单设置
        Map<String, List<PcxBasFormSetting>> groupedByFormType = getGroupedFormSettings(qo, baseDataVos);
        // 构建块信息
        return baseDataVos.stream()
                .map(baseDataVo -> createBlockVO(qo, baseDataVo, groupedByFormType.get(baseDataVo.getCode())))
                .collect(Collectors.toList());
    }

    private List<BaseDataVo> getEnabledSettlementTypes(PcxBlockCondQO qo) {
        if(ObjectUtils.isEmpty(qo)){
            return new ArrayList<>();
        }
        String tenantId = StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId();
        PcxSettlementRuleQO param = new PcxSettlementRuleQO();
        param.setAgyCode(qo.getAgyCode());
        param.setFiscal(qo.getFiscal());
        param.setMofDivCode(qo.getMofDivCode());
        param.setTenantId(tenantId);
        param.setExpenseCodes(new ArrayList<>(qo.getExpenseMap().keySet()));
        return pcxSettlementRuleService.selectSettlementByExpenses(param);
    }

    private Map<String, List<PcxBasFormSetting>> getGroupedFormSettings(PcxBlockCondQO qo, List<BaseDataVo> baseDataVos) {
        List<String> settlementTypes = baseDataVos.stream().map(BaseDataVo::getCode).collect(Collectors.toList());
        PcxBasFormSettingQueryQO pcxBasFormSettingQueryQO = new PcxBasFormSettingQueryQO();
        pcxBasFormSettingQueryQO.setFiscal(qo.getFiscal());
        pcxBasFormSettingQueryQO.setMofDivCode(qo.getMofDivCode());
        pcxBasFormSettingQueryQO.setAgyCode(qo.getAgyCode());
        pcxBasFormSettingQueryQO.setFormTypes(settlementTypes);
        // 跟报销的保持一致 todo -mashaojie 2024-12-28;
//        pcxBasFormSettingQueryQO.setFormClassify(FormSettingEnums.BillFuncCodeEnum.getByCode(qo.getBillFuncCode()).getCode());
        pcxBasFormSettingQueryQO.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        List<PcxBasFormSetting> pcxBasFormSettings = pcxBasFormSettingService.selectByQO(pcxBasFormSettingQueryQO);
        PcxBasItemVO pcxBasItemVO = qo.getPcxBasItemVO();
        if(!ObjectUtils.isEmpty(pcxBasItemVO) && !BudgetCtrlUtil.isBudgetCtrlEnabled(pcxBasItemVO.getBudgetCtrl(), qo.getBillFuncCode())){
            pcxBasFormSettings = pcxBasFormSettings.stream()
                    .filter(item -> !BUDGET_FIELD_SET.contains(item.getFieldValue()))
                    .collect(Collectors.toList());
        }
        if(!PositionEnum.isFinance(qo.getPositionCode())){
            return pcxBasFormSettings.stream().filter(item-> !PcxApprovalCondFieldEnum.CHECK_AMT.getCode().equals(item.getFieldValue())
                            && !FINANCE_FIELD_SET.contains(item.getFieldValue()))
                    .collect(Collectors.groupingBy(PcxBasFormSetting::getFormType));
        }
        return pcxBasFormSettings.stream()
                .peek(item -> {
                    if (PcxApprovalCondFieldEnum.CHECK_AMT.getCode().equals(item.getFieldValue())) {
                        item.setIsEdit(PubConstant.LOGIC_TRUE);
                    } else {
                        item.setIsEnabled(PubConstant.LOGIC_FALSE);
                    }
                })
                .collect(Collectors.groupingBy(PcxBasFormSetting::getFormType));
    }

    private PcxBlockVO createBlockVO(PcxBlockCondQO qo, BaseDataVo baseDataVo, List<PcxBasFormSetting> pcxBasFormSettings) {
        PcxBlockVO pcxBlockVO = new PcxBlockVO();
        pcxBlockVO.setClassifyCode(qo.getClassifyCode());
        pcxBlockVO.setClassifyName(qo.getClassifyName());
        pcxBlockVO.setBlockCode(baseDataVo.getCode());
        pcxBlockVO.setBlockName(baseDataVo.getName());
        pcxBlockVO.setBlockTitle(qo.getShowName());
        pcxBlockVO.setProperties(formSettingConvertToBlockProperty(pcxBasFormSettings,qo));
        pcxBlockVO.setArea(qo.getArea());
        return pcxBlockVO;
    }

    private List<BlockPropertyVO> formSettingConvertToBlockProperty(List<PcxBasFormSetting> pcxBasFormSettings,PcxBlockCondQO qo) {
        if (CollectionUtil.isEmpty(pcxBasFormSettings)) {
            return new ArrayList<>();
        }
        // 如果是出纳
        Boolean isCashier = PcxNodeEnum.cashier.getId().equals(qo.getPositionCode());
        return pcxBasFormSettings.stream().map(pcxBasFormSetting -> {
            BlockPropertyVO blockPropertyVO = new BlockPropertyVO();
            blockPropertyVO.setFieldLabel(pcxBasFormSetting.getFieldLabel());
            blockPropertyVO.setFieldTitle(pcxBasFormSetting.getFieldTitle());
            blockPropertyVO.setIsRequired(pcxBasFormSetting.getIsNull());
            blockPropertyVO.setIsEdit(pcxBasFormSetting.getIsEdit());
            if(isCashier){
                if( CASHIER_FIELD_SET.contains(pcxBasFormSetting.getFieldValue())){
                    blockPropertyVO.setIsEdit(PubConstant.LOGIC_TRUE);
                }else{
                    blockPropertyVO.setIsEdit(PubConstant.LOGIC_FALSE);
                }
            }
            blockPropertyVO.setFieldName(pcxBasFormSetting.getFieldName());
            blockPropertyVO.setShowType(pcxBasFormSetting.getShowType());
            blockPropertyVO.setNotes(pcxBasFormSetting.getNotes());
            blockPropertyVO.setRemarks(pcxBasFormSetting.getRemarks());
            blockPropertyVO.setEditorCode(pcxBasFormSetting.getEditorCode());
            blockPropertyVO.setDataTypeCode(pcxBasFormSetting.getDataTypeCode());
            blockPropertyVO.setDataClassifyCode(pcxBasFormSetting.getDataClassifyCode());
            blockPropertyVO.setDataSourceCode(pcxBasFormSetting.getDataSourceCode());
            blockPropertyVO.setFieldValue(pcxBasFormSetting.getFieldValue());
            return blockPropertyVO;
        }).collect(Collectors.toList());
    }
}