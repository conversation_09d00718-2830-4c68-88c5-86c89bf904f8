package com.pty.pcx.service.impl.labour;


import com.pty.mad.entity.*;
import com.pty.pcx.api.labour.ILabourDeclareTaxService;
import com.pty.pcx.dao.mad.PcxMadEmpTypeDao;
import com.pty.pcx.qo.labour.PcxLabourInfoQO;
import com.pty.pub.common.constant.PubConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;

/**
 * 劳务报酬报税
 */
@Service
@SuppressWarnings("all")
@Indexed
@Slf4j
public class LabourDeclareTaxService implements ILabourDeclareTaxService {


  @Autowired
  private PcxMadEmpTypeDao pcxMadEmpTypeDao;


  @Override
  public List<Map<String, Object>> userType(PcxLabourInfoQO param) {
    MadEmpType madEmpType = new MadEmpType();
    madEmpType.setFiscal(Integer.parseInt(param.getFiscal()));
    madEmpType.setAgyCode(param.getAgyCode());
    madEmpType.setMofDivCode(param.getMofDivCode());
    madEmpType.setIsEnabled(PubConstant.LOGIC_TRUE);
    List<MadEmpType> userTypes = pcxMadEmpTypeDao.select(madEmpType);
    List<Map<String, Object>> list = new ArrayList<>();
    if (!CollectionUtils.isEmpty(userTypes)) {
      for (MadEmpType type : userTypes) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", type.getTypeCode());
        map.put("name", type.getTypeName());
        map.put("code", type.getTypeCode());
        list.add(map);
      }
    }
    return list;
  }

}
