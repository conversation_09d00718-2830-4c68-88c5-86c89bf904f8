package com.pty.pcx.service.impl.transfer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.mad.entity.MadCurrent;
import com.pty.mad.entity.MadEmployee;
import com.pty.mad.entity.MadExtData;
import com.pty.mad.qo.MadExtDataQO;
import com.pty.openapi.integration.Response;
import com.pty.openapi.integration.dto.kingdee.TravelExpenseReimbursementRequest;
import com.pty.openapi.integration.dto.kingdee.TravelExpenseReimbursementResponse;
import com.pty.openapi.integration.dto.reimbursement.ReimbursementDataDTO;
import com.pty.openapi.integration.dto.reimbursement.common.ExpenseDetailDTO;
import com.pty.openapi.integration.dto.reimbursement.common.ExpenseReimbursementDTO;
import com.pty.openapi.integration.enums.InvoiceType;
import com.pty.openapi.sdk.client.kingdee.ErpExpenseReimbursementClient;
import com.pty.openapi.sdk.client.kingdee.TravelExpenseParamsFactoryClient;
import com.pty.pcx.api.transfer.PcxBillSyncService;
import com.pty.pcx.common.enu.PcxBillSyncStatus;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.dao.bill.PcxBillBalanceDao;
import com.pty.pcx.dao.bill.PcxBillExpCommonDao;
import com.pty.pcx.dao.bill.PcxBillExpDetailCommonDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.dao.project.PcxProjectExpenseDao;
import com.pty.pcx.dto.transfer.AllocationItemDTO;
import com.pty.pcx.dto.transfer.ExtendExpenseDetailDTO;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.project.PcxProjectExpense;
import com.pty.pcx.entity.transfer.PcxBillSync;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pub.common.rest.RestClientReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pty.mad.api.IMadExtDataService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用报销
 */
@Slf4j
@Service
public class ExpenseReimbursementService extends AbstractReimbursementService {

    @Autowired
    private PcxBillSyncService pcxBillSyncService;

    @Autowired
    private ErpExpenseReimbursementClient expenseReimbursementClient;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;

    @Autowired
    private PcxBillExpCommonDao pcxBillExpCommonDao;

    @Autowired
    private PcxBillExpDetailCommonDao pcxBillExpDetailCommonDao;

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IMadExtDataService madExtDataService;

    @Autowired
    private PcxProjectExpenseDao pcxProjectExpenseDao;

    @Autowired
    private PcxBillBalanceDao pcxBillBalanceDao;

    @Autowired
    private TravelExpenseParamsFactoryClient travelExpenseParamsFactoryClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TransactionTemplate transactionTemplate;

    public void execute() {
        RLock lock = redissonClient.getLock("EXPENSE_REIMBURSEMENT_LOCK");
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            transactionTemplate.executeWithoutResult(status -> {
                executeDraft();
                executeBill();
            });
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 执行草稿单据
     */
    private void executeDraft() {
        executeCommon(false);
        executeOther(false);
    }

    /**
     * 执行普通单据
     */
    private void executeBill() {
        executeCommon(true);
        executeOther(true);
    }

    private void executeCommon(boolean isFinalReview) {
        Integer syncStatus = isFinalReview ? PcxBillSyncStatus.PENDING_SYNC.getCode() : PcxBillSyncStatus.DRAFT_PENDING_SYNC.getCode();
        List<PcxBill> pcxBills = pcxBillSyncService.getUnSyncedPcxBill(PcxBillSyncServiceImpl.COMMON, syncStatus);
        if (pcxBills.isEmpty()) {
            return;
        }
        execute(pcxBills, PcxBillSyncServiceImpl.COMMON, isFinalReview);
    }

    private void executeOther(boolean isFinalReview) {
        Integer syncStatus = isFinalReview ? PcxBillSyncStatus.PENDING_SYNC.getCode() : PcxBillSyncStatus.DRAFT_PENDING_SYNC.getCode();
        List<PcxBill> pcxBills = pcxBillSyncService.getUnSyncedPcxBill(PcxBillSyncServiceImpl.OTHER, syncStatus);
        if (pcxBills.isEmpty()) {
            return;
        }
        execute(pcxBills, PcxBillSyncServiceImpl.OTHER, isFinalReview);
    }

    private void execute(List<PcxBill> pcxBills, Integer bizType, boolean isFinalReview) {
        List<String> billIds = new ArrayList<>();
        List<String> claimantCodes = new ArrayList<>();
        for (PcxBill pcxBill : pcxBills) {
            billIds.add(pcxBill.getId());
            claimantCodes.add(pcxBill.getClaimantUserCode());
        }

        Map<String, List<PcxBillExpCommon>> billExpCommonGroupMap = selectBillExpCommonGroupMap(billIds);

        Map<String, List<PcxBillAmtApportionDepartment>> pcxBillAmtApportionDepartmentsMap = getPcxBillAmtApportionDepartmentsMap(billIds);

        Map<String, MadEmployee> madEmployeeMap = getMadEmployeeMap(claimantCodes);

        for (PcxBill pcxBill : pcxBills) {
            PcxBillSync pcxBillSync = new PcxBillSync();
            Integer syncStatus = PcxBillSyncStatus.getSyncedStatus(isFinalReview);
            String syncMessage = null;
            try {
                ExpenseReimbursementDTO expenseReimbursementDTO
                        = buildExpenseReimbursementDTO(isFinalReview, pcxBill, bizType, madEmployeeMap, billExpCommonGroupMap, pcxBillAmtApportionDepartmentsMap);

                Response<ReimbursementDataDTO> response = expenseReimbursementClient.push(expenseReimbursementDTO);
                if (response.isSuccess()) {
                    ReimbursementDataDTO data = response.getData();
                    pcxBillSync.setExternalBillId(data.getBillId());
                    pcxBillSync.setExternalBillNo(data.getBillNo());
                } else {
                    syncStatus = PcxBillSyncStatus.getSynceFailedStatus(isFinalReview);
                    syncMessage = response.getMsg();
                    // 若草稿状态遇到关账，则需手动处理
                    if (isFinalReview && response.getCode() == -2) {
                        syncStatus = PcxBillSyncStatus.IS_CLOSED.getCode();
                    }
                }

            } catch (Exception e) {
                syncStatus = PcxBillSyncStatus.getSynceFailedStatus(isFinalReview);
                syncMessage = e.getMessage();
            }
            pcxBillSync.setBillId(pcxBill.getId());
            pcxBillSync.setSyncStatus(syncStatus);
            pcxBillSync.setSyncTime(new Date());
            pcxBillSync.setSyncMessage(syncMessage);
            pcxBillSyncService.updateSyncStatus(pcxBillSync);
        }
    }

    private ExpenseReimbursementDTO buildExpenseReimbursementDTO(boolean isFinalReview,
                                                                 PcxBill pcxBill,
                                                                 Integer bizType,
                                                                 Map<String, MadEmployee> madEmployeeMap,
                                                                 Map<String, List<PcxBillExpCommon>> billExpCommonGroupMap,
                                                                 Map<String, List<PcxBillAmtApportionDepartment>> pcxBillAmtApportionDepartmentsMap) {
        String billId = pcxBill.getId();

        ExpenseReimbursementDTO expenseReimbursementDTO = new ExpenseReimbursementDTO();
        String id = "";
        String billNo = pcxBill.getBillNo();
        Boolean autoSubmit = Boolean.FALSE;
        if (isFinalReview) {
            PcxBillSync pcxBillSync = pcxBillSyncService.getPcxBillSyncByBillId(billId);
            Assert.notNull(pcxBillSync, "无法查询到终审的同步信息，请检查数据");
            id = pcxBillSync.getExternalBillId();
            autoSubmit = Boolean.TRUE;
        }
        expenseReimbursementDTO.setBillId(id);    // 单据ID    FID
        expenseReimbursementDTO.setBillNo(billNo);   // 单据编号 FBillNo
        expenseReimbursementDTO.setAutoSubmit(autoSubmit.toString());   // 是否自动提交和审核    IsAutoSubmitAndAudit

        expenseReimbursementDTO.setApplyDate(pcxBill.getTransDate());  // 申请日期 FDate
        String userCode = pcxBill.getClaimantUserCode();
        expenseReimbursementDTO.setApplyUserCode(userCode);   // 申请人   FPROPOSER

        // 当前部门
        MadExtData currentDepartment = selectCurrentDepartment(pcxBill.getAgyCode(), pcxBill.getDepartmentCode(), pcxBill.getClaimantUserCode());
        Assert.notNull(currentDepartment, "部门:" + pcxBill.getDepartmentCode() + " 不存在，请检查配置");
        String deptCode = currentDepartment.getMadCode();
        expenseReimbursementDTO.setApplyDept(deptCode);    // 申请部门  FREQUESTDEPT

        // 当前组织
        MadExtData currentOrg = selectCurrentOrg(currentDepartment.getField05());
        Assert.notNull(currentOrg, "ERP 组织:" + currentDepartment.getField05() + " 不存在，请检查配置");
        expenseReimbursementDTO.setApplyOrg(currentOrg.getMadCode()); // 申请组织 APPLY_ORG

        if (madEmployeeMap.containsKey(userCode)) {
            MadEmployee madEmployee = madEmployeeMap.get(userCode);
            expenseReimbursementDTO.setApplyPhone(madEmployee.getPhoneNo()); // 联系电话   FContactPhoneNo
        }
        expenseReimbursementDTO.setAssistantName(StringUtils.equals(userCode, pcxBill.getCreator()) ? null : pcxBill.getCreatorName());    // 商务助理 F_KF_Assistant
        expenseReimbursementDTO.setCurrency("PRE001");    // 币别（人民币）   FCURRENCYID

        expenseReimbursementDTO.setSettlementType("JSFS11_SYS");   // 结算方式（网银支付）   PAY_SETTLLETYPE

        String billType = null;
        String currentType = "BD_Empinfo";
        String currentName = userCode;
        String payeeAccName = null;
        String payeeAccCode = null;
        String payeeAccNo = null;
        Boolean isRealPay = Boolean.TRUE;
        if (isFinalReview) {
            PcxBillPayDetail pcxBillPayDetail = selectPcxBillPayDetail(billId);
            if (pcxBillPayDetail != null) {
                if (Objects.equals(bizType, PcxBillSyncServiceImpl.COMMON)) {
                    if (SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode().equals(pcxBillPayDetail.getSettlementType())) {
                        // 无合同对公报销单
                        billType = "ExpReimbursement";
                        MadCurrent current = selectMadCurrent(pcxBill, pcxBillPayDetail.getPayeeAccountName());
                        if (current != null) {
                            currentType = "BD_Supplier";    // （供应商）
                            currentName = current.getMadCode();
                        }
                    } else {
                        // 费用报销单
                        billType = "Reimbursement";
                    }
                } else if (Objects.equals(bizType, PcxBillSyncServiceImpl.OTHER)) {
                    // 其他支付单
                    billType = "OtherReimbursement";
                }
                payeeAccName = pcxBillPayDetail.getPayeeAccountName();
                payeeAccCode = pcxBillPayDetail.getPayeeBankName();
                payeeAccNo = pcxBillPayDetail.getPayeeAccountNo();
            }
        } else {
            PcxBillSettlement pcxBillSettlement = selectPcxBillSettlement(billId);
            if (pcxBillSettlement != null) {
                if (Objects.equals(bizType, PcxBillSyncServiceImpl.COMMON)) {
                    if (SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode().equals(pcxBillSettlement.getSettlementType())) {
                        // 无合同对公报销单
                        billType = "ExpReimbursement";
                        MadCurrent current = selectMadCurrent(pcxBill, pcxBillSettlement.getPayeeAccName());
                        if (current != null) {
                            currentType = "BD_Supplier";    // （供应商）
                            currentName = current.getMadCode();
                        }
                    } else {
                        // 费用报销单
                        billType = "Reimbursement";
                    }
                } else if (Objects.equals(bizType, PcxBillSyncServiceImpl.OTHER)) {
                    // 其他支付单
                    billType = "OtherReimbursement";
                }
                payeeAccName = pcxBillSettlement.getPayeeAccName();
                payeeAccCode = pcxBillSettlement.getPayeeBankName();
                payeeAccNo = pcxBillSettlement.getPayeeAccNo();
            }
        }
        payeeAccNo = StringUtils.replace(payeeAccNo, " ", "");

        expenseReimbursementDTO.setPayeeAccName(payeeAccName);  // 账户名称 FBankAccountNameT
        expenseReimbursementDTO.setPayeeAccCode(payeeAccCode);  // 开户银行 FBankBranchT
        expenseReimbursementDTO.setPayeeAccNo(payeeAccNo);   // 银行账号    FBankAccountT

        expenseReimbursementDTO.setIsRealPay(isRealPay.toString()); // 实报实付 FRealPay

        expenseReimbursementDTO.setBillType(billType);   // 单据类型（费用报销单）  BILL_TYPE
        expenseReimbursementDTO.setCurrentType(currentType);    // 往来单位类型  FCONTACTUNITTYPE
        expenseReimbursementDTO.setCurrentName(currentName);    // 往来单位    FCONTACTUNIT

        String reason = StringUtils.isBlank(pcxBill.getReason()) ? "无" : pcxBill.getReason();
        expenseReimbursementDTO.setReason(reason);    // 报销事由 FCAUSA

        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelDao.selectByBillId(billId);

        Map<String, PcxExpDetailEcsRel> pcxExpDetailEcsRelMap = new HashMap<>();
        for (PcxExpDetailEcsRel pcxExpDetailEcsRel : pcxExpDetailEcsRels) {
            pcxExpDetailEcsRelMap.put(pcxExpDetailEcsRel.getDetailId(), pcxExpDetailEcsRel);
        }

        Map<String, PcxBillExpCommon> pcxBillExpCommonIdMap = new HashMap<>();
        Map<String, PcxBillExpCommon> pcxBillExpCommonBillIdMap = new HashMap<>();
        List<String> expenseCodes = new ArrayList<>();
        if (billExpCommonGroupMap.containsKey(billId)) {
            List<PcxBillExpCommon> pcxBillExpCommons = billExpCommonGroupMap.get(billId);
            for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
                pcxBillExpCommonIdMap.put(pcxBillExpCommon.getId(), pcxBillExpCommon);
                pcxBillExpCommonBillIdMap.put(pcxBillExpCommon.getBillId(), pcxBillExpCommon);
                if (!"*".equals(pcxBillExpCommon.getExpenseCode())) {
                    expenseCodes.add(pcxBillExpCommon.getExpenseCode());
                }
            }
        }

        List<PcxBillExpDetailCommon> pcxBillExpDetailCommons = pcxBillExpDetailCommonDao.selectByBillId(billId);

        Map<String, PcxProjectExpense> pcxProjectExpenseMap = getPcxProjectExpenseMap(pcxBill.getAgyCode(), expenseCodes, billId, billExpCommonGroupMap);

        List<ExtendExpenseDetailDTO> extendExpenseDetailDTOS = pcxBillExpDetailCommons
                .stream()
                .filter(pcxBillExpDetailCommon -> !"replenish".equals(pcxBillExpDetailCommon.getSource()))
                .map(pcxBillExpDetailCommon -> {
                    ExtendExpenseDetailDTO expenseDetail = new ExtendExpenseDetailDTO();

                    PcxBillExpCommon pcxBillExpCommon = null;
                    if (pcxBillExpCommonIdMap.containsKey(pcxBillExpDetailCommon.getExpenseId())) {
                        pcxBillExpCommon = pcxBillExpCommonIdMap.get(pcxBillExpDetailCommon.getExpenseId());
                    } else if (pcxBillExpCommonBillIdMap.containsKey(billId)) {
                        pcxBillExpCommon = pcxBillExpCommonBillIdMap.get(billId);
                    }

                    String expCode = "15";
                    if (pcxBillExpCommon != null) {
                        Map<String, String> costItemMap = selectCostItemExtDataMap(pcxBillExpCommon.getExpenseCode(), pcxBill.getAgyCode());
                        expCode = costItemMap.getOrDefault(pcxBillExpCommon.getExpenseCode(), "15");
                    }
                    expenseDetail.setExpCode(expCode);   // 费用项目   FExpID

                    TravelExpenseReimbursementRequest request = new TravelExpenseReimbursementRequest();
                    Integer invoiceQty = pcxBillExpDetailCommon.getEcsNum();
                    if (pcxExpDetailEcsRelMap.containsKey(pcxBillExpDetailCommon.getId())) {
                        PcxExpDetailEcsRel pcxExpDetailEcsRel = pcxExpDetailEcsRelMap.get(pcxBillExpDetailCommon.getId());

                        String ecsBillNo = pcxExpDetailEcsRel.getEcsBillNo();
                        String[] ecsBillNos = ecsBillNo.split("#");
                        expenseDetail.setInvoiceCode(ecsBillNos.length == 2 ? ecsBillNos[1] : "0"); // 发票代码 F_KF_RECINVCODE
                        expenseDetail.setInvoiceNo(getInvoiceNo(ecsBillNo));   // 发票号码   F_KF_RECINVNUMBER
                        request.setEcsBillType(pcxExpDetailEcsRel.getEcsBillType());
                        request.setExpenseTypeCode(pcxExpDetailEcsRel.getExpenseTypeCode());

                        if (StringUtils.isBlank(pcxExpDetailEcsRel.getFileId())) {
                            invoiceQty = 0;
                        }
                    } else {
                        request.setExpenseTypeCode(pcxBillExpDetailCommon.getExpDetailCode());
                    }

                    expenseDetail.setInvoiceQty(invoiceQty);  // 发票张数  F_KF_VOICEQUANTITY
                    request.setInvoiceQty(invoiceQty);

                    Response<TravelExpenseReimbursementResponse> response = travelExpenseParamsFactoryClient.getTravelExpenseReportParams(request);
                    Assert.isTrue(response.isSuccess(), response.getMsg());
                    TravelExpenseReimbursementResponse reimbursementResponse = response.getData();
                    String invoiceType = reimbursementResponse.getInvoiceType();
                    expenseDetail.setInvoiceType(invoiceType);   // 发票类型   FINVOICETYPE

                    BigDecimal taxRate = pcxBillExpDetailCommon.getTaxRate();
                    BigDecimal taxAmt = pcxBillExpDetailCommon.getTaxAmt();
                    if (taxRate.compareTo(BigDecimal.ONE) < 0) {
                        taxRate = taxRate.multiply(BigDecimal.valueOf(100));
                    }
                    List<String> rateInvoiceTypes = Arrays.asList(
                            InvoiceType.VAT_GENERAL_ELECTRONIC.getCode(),
                            InvoiceType.VAT_SPECIAL_ELECTRONIC.getCode(),
                            InvoiceType.VAT_SPECIAL.getCode()
                    );
                    if (!rateInvoiceTypes.contains(invoiceType)) {
                        taxRate = BigDecimal.ZERO;
                        taxAmt = BigDecimal.ZERO;
                    }
                    // 处理 6% 的税率
                    List<String> specialInvoices = Arrays.asList(InvoiceType.VAT_SPECIAL_ELECTRONIC.getCode(), InvoiceType.VAT_SPECIAL.getCode());
                    if (taxRate.compareTo(BigDecimal.valueOf(6)) == 0 && !specialInvoices.contains(reimbursementResponse.getInvoiceType())) {
                        taxRate = BigDecimal.ZERO;
                        taxAmt = BigDecimal.ZERO;
                    }
                    expenseDetail.setRate(taxRate.toPlainString());    // 税率    FTaxRate
                    expenseDetail.setAmt(taxAmt.toPlainString());   // 税额   FTaxAmt
                    expenseDetail.setRealAmt(pcxBillExpDetailCommon.getCheckAmt().subtract(pcxBillExpDetailCommon.getTaxAmt()).toPlainString()); // 费用金额 FTaxSubmitAmt

                    String remark = StringUtils.isBlank(pcxBillExpDetailCommon.getRemark()) ? "无" : pcxBillExpDetailCommon.getRemark();
                    expenseDetail.setRemark(remark);  // 备注 FREMARK

                    expenseDetail.setApplyExpMoney(pcxBillExpDetailCommon.getInputAmt());   // 申请报销金额   FExpenseAmount
                    expenseDetail.setApplyPayMoney(pcxBillExpDetailCommon.getInputAmt().toPlainString());   // 申请付款金额   FRequestAmount
                    expenseDetail.setCheckExpMoney(pcxBillExpDetailCommon.getCheckAmt().toPlainString());   // 核定报销金额   FExpSubmitAmount
                    expenseDetail.setCheckPayMoney(pcxBillExpDetailCommon.getCheckAmt().toPlainString());   // 核定付款金额   FReqSubmitAmount

                    String budgetExpProject = getBudgetExpProject(billExpCommonGroupMap, pcxBill, pcxBillExpDetailCommon);
                    expenseDetail.setBudgetExpProject(budgetExpProject);    // 对应预算费用项目 F_BudgetExpenseItem
                    String computeExpProject = getComputeExpProject(pcxProjectExpenseMap, pcxBillExpDetailCommon, pcxBill.getAgyCode());
                    expenseDetail.setComputeExpProject(computeExpProject);   // 对应核算费用项目 F_CheckExpenseItem
                    expenseDetail.setLoanChargeMoney(null); // 冲借款金额    FBorrowAmount

                    // 补充原始信息
                    expenseDetail.setDetailId(pcxBillExpDetailCommon.getId());
                    expenseDetail.setOriginalDepartmentCode(pcxBillExpDetailCommon.getDepartmentCode());

                    return expenseDetail;
                })
                .collect(Collectors.toList());

        List<ExtendExpenseDetailDTO> expenseDetailDTOS = extendExpenseDetailDTOS
                .stream()
                .filter(pcxBillExpDetailCommon -> {
                    BigDecimal realAmt = new BigDecimal(pcxBillExpDetailCommon.getRealAmt());
                    return realAmt.compareTo(BigDecimal.ZERO) > 0;
                })
                .collect(Collectors.toList());

        BigDecimal filterInputAmt = extendExpenseDetailDTOS
                .stream()
                .filter(pcxBillExpDetailTravel -> {
                    BigDecimal realAmt = new BigDecimal(pcxBillExpDetailTravel.getRealAmt());
                    return realAmt.compareTo(BigDecimal.ZERO) <= 0;
                })
                .map(ExtendExpenseDetailDTO::getApplyExpMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<PcxBillAmtApportionDepartment> pcxBillAmtApportionDepartments = pcxBillAmtApportionDepartmentsMap.getOrDefault(billId, new ArrayList<>());

        // 核定金额为 0 的情况下，需要将部门分摊的申请金额也进行扣减
        subtractAmount(pcxBillAmtApportionDepartments, filterInputAmt);

        List<AllocationItemDTO> allocationItems = getAllocationItems(expenseDetailDTOS, pcxBillAmtApportionDepartments, currentDepartment);

        Map<String, ExtendExpenseDetailDTO> extendExpenseDetailDTOMap =
                expenseDetailDTOS.stream().collect(Collectors.toMap(ExtendExpenseDetailDTO::getDetailId, Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, PcxBillAmtApportionDepartment> billAmtApportionDepartmentMap =
                pcxBillAmtApportionDepartments.stream().collect(Collectors.toMap(pcxBillAmtApportionDepartment -> currentDepartment.getMadCode(), Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, Integer> invoiceCountMap = new HashMap<>();
        Map<String, BigDecimal> invoiceMap = new HashMap<>();
        List<ExpenseDetailDTO> dataList = allocationItems
                .stream()
                .filter(allocationItem -> extendExpenseDetailDTOMap.containsKey(allocationItem.getBillId()))
                .map(allocationItem -> {
                    BigDecimal applyAmt = BigDecimal.valueOf(allocationItem.getAllocatedAmount()).divide(BigDecimal.valueOf(100));
                    BigDecimal inputAmt = BigDecimal.valueOf(allocationItem.getAllocatedInputAmount()).divide(BigDecimal.valueOf(100));
                    ExtendExpenseDetailDTO extendExpenseDetailDTO = extendExpenseDetailDTOMap.get(allocationItem.getBillId());
                    String departmentCode = allocationItem.getDepartmentId();
                    extendExpenseDetailDTO.setDepartmentCode(allocationItem.getDepartmentId());    // 费用承担部门 FEXPENSEDEPT
                    extendExpenseDetailDTO.setApplyExpMoney(inputAmt);
                    extendExpenseDetailDTO.setApplyPayMoney(inputAmt.toPlainString());
                    extendExpenseDetailDTO.setCheckExpMoney(applyAmt.toPlainString());
                    extendExpenseDetailDTO.setCheckPayMoney(applyAmt.toPlainString());

                    String firstDepartment = selectFirstDepartmentCode(allocationItem.getDepartment(), departmentCode);
                    extendExpenseDetailDTO.setFirstDepartment(firstDepartment); // 一级部门   F_KF_MANAGEDEPTID

                    String invoiceNo = extendExpenseDetailDTO.getInvoiceNo();
                    BigDecimal taxAmt = new BigDecimal(extendExpenseDetailDTO.getAmt());
                    BigDecimal amt = BigDecimal.ZERO;
                    BigDecimal realAmt = applyAmt;
                    // 处理税率、税额
                    if (taxAmt.compareTo(BigDecimal.ZERO) != 0 && !invoiceMap.containsKey(invoiceNo)) {
                        amt = taxAmt;
                        realAmt = applyAmt.subtract(amt);
                        if (realAmt.compareTo(BigDecimal.ZERO) > 0) {
                            invoiceMap.put(extendExpenseDetailDTO.getInvoiceNo(), new BigDecimal(extendExpenseDetailDTO.getAmt()));
                        }
                    }

                    extendExpenseDetailDTO.setAmt(amt.toPlainString());   // 税额   FTaxAmt
                    extendExpenseDetailDTO.setRealAmt(realAmt.toPlainString()); // 费用金额 FTaxSubmitAmt

                    if (billAmtApportionDepartmentMap.containsKey(departmentCode)) {
                        PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment = billAmtApportionDepartmentMap.get(departmentCode);

                        extendExpenseDetailDTO.setLongExp(pcxBillAmtApportionDepartment.getAcitem09Code()); // 长期待摊费用    F_ZHKF_PrepaidExpenses
                        extendExpenseDetailDTO.setA8ProjectInfo(pcxBillAmtApportionDepartment.getAcitem08Code());   // A8/PP项目信息     F_ZHKF_ProjectInfo
                        extendExpenseDetailDTO.setCustomer(pcxBillAmtApportionDepartment.getAcitem03Code());    // 客户    F_ZHKF_AssignedCustomer
                        extendExpenseDetailDTO.setOpenProject(pcxBillAmtApportionDepartment.getAcitem06Code()); // 招标项目  F_KF_PROJECTNUMBER
                        extendExpenseDetailDTO.setDevProject(pcxBillAmtApportionDepartment.getAcitem07Code());  // 研发项目  F_KF_ResearchProject
                        extendExpenseDetailDTO.setBiIncomeType(pcxBillAmtApportionDepartment.getAcitem04Code());    // BI收入类型    F_BIIncomeType
                        extendExpenseDetailDTO.setComputeType(pcxBillAmtApportionDepartment.getAcitem05Code()); // 核算收入类型    F_CheckIncomeType
                    }

                    ExpenseDetailDTO expenseDetailDTO = new ExpenseDetailDTO();
                    BeanUtils.copyProperties(extendExpenseDetailDTO, expenseDetailDTO);

                    if (StringUtils.isNotBlank(invoiceNo)) {
                        invoiceCountMap.put(invoiceNo, invoiceCountMap.getOrDefault(invoiceNo, 0) + 1);
                    }

                    return expenseDetailDTO;
                })
                .collect(Collectors.toList());

        for (ExpenseDetailDTO expenseDetailDTO : dataList) {
            String invoiceNo = expenseDetailDTO.getInvoiceNo();
            String sameInvoice = "0";
            if (StringUtils.isNotBlank(invoiceNo) && invoiceCountMap.get(invoiceNo) > 1) {
                sameInvoice = "1";
            }
            expenseDetailDTO.setSameInvoice(sameInvoice);   // 是否同一发票（0：否；1：是）   F_KF_ComboFP
        }

        boolean isExpDept = dataList.stream().allMatch(kingdeeTravelExpenseDetailDTO -> {
            return StringUtils.equals(deptCode, kingdeeTravelExpenseDetailDTO.getDepartmentCode());
        });

        expenseReimbursementDTO.setDefaultExpDept(isExpDept ? "1" : "0");   //  是否本部门费用（0：否；1：是）    F_KF_Combo1

        expenseReimbursementDTO.setDetailList(dataList);

        return expenseReimbursementDTO;
    }

    private String getBudgetExpProject(Map<String, List<PcxBillExpCommon>> billExpCommonGroupMap,
                                       PcxBill pcxBill,
                                       PcxBillExpDetailCommon pcxBillExpDetailCommon) {
        String billId = pcxBill.getId();

        String[] projectCodes = StringUtils.split(pcxBill.getProjectCodes(), ",");
        List<PcxBillBalance> pcxBillBalances = pcxBillBalanceDao.selectByBillId(billId);
        Map<String, PcxBillBalance> pcxBillBalanceMap =
                pcxBillBalances.stream().collect(Collectors.toMap(PcxBillBalance::getProjectCode, Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, PcxBillBalance> billBalanceExpenseCodeMap = new HashMap<>();
        for (String projectCode : projectCodes) {
            if (pcxBillBalanceMap.containsKey(projectCode)) {
                PcxBillBalance pcxBillBalance = pcxBillBalanceMap.get(projectCode);
                billBalanceExpenseCodeMap.put(pcxBillBalance.getExpenseCode(), pcxBillBalance);
            }
        }

        Map<String, PcxBillBalance> billBalanceMap = new HashMap<>();
        if (billExpCommonGroupMap.containsKey(billId)) {
            List<PcxBillExpCommon> pcxBillExpCommons = billExpCommonGroupMap.get(billId);
            for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
                if (billBalanceExpenseCodeMap.containsKey(pcxBillExpCommon.getExpenseCode())) {
                    PcxBillBalance pcxBillBalance = billBalanceExpenseCodeMap.get(pcxBillExpCommon.getExpenseCode());
                    billBalanceMap.put(pcxBillExpCommon.getId(), pcxBillBalance);
                }
            }
        }

        String budgetExpProject = null;
        if (billBalanceMap.containsKey(pcxBillExpDetailCommon.getExpenseId())) {
            PcxBillBalance pcxBillBalance = billBalanceMap.get(pcxBillExpDetailCommon.getExpenseId());
            String expenseCode = pcxBillBalance.getProjectCode();
            Map<String, String> map = selectCostItemExtDataMap(expenseCode, pcxBill.getAgyCode());
            budgetExpProject = map.get(expenseCode);
        }
        return budgetExpProject;
    }

    private String getComputeExpProject(Map<String, PcxProjectExpense> pcxProjectExpenseMap,
                                        PcxBillExpDetailCommon pcxBillExpDetailCommon,
                                        String agyCode) {
        String computeExpProject = null;
        if (pcxProjectExpenseMap.containsKey(pcxBillExpDetailCommon.getExpenseId())) {
            PcxProjectExpense pcxProjectExpense = pcxProjectExpenseMap.get(pcxBillExpDetailCommon.getExpenseId());
            computeExpProject = selectCostItemExtDataByExpenseCode(pcxProjectExpense.getAccountingItemCode(), agyCode);
        }
        return computeExpProject;
    }

    private String selectCostItemExtDataByExpenseCode(String expenseCode, String agyCode) {
        Map<String, String> map = selectCostItemExtDataMap(expenseCode, agyCode);
        return map.get(expenseCode);
    }

    private Map<String, String> selectCostItemExtDataMap(String expenseCode, String agyCode) {
        MadExtDataQO madExtDataQO = new MadExtDataQO();
        madExtDataQO.setIsEnabled(1);
        madExtDataQO.setAtomCode("KINGDEE_ERP_COST_ITEM");
        madExtDataQO.setLikeCommaField04(expenseCode);
        madExtDataQO.setAgyCode(agyCode);
        List<MadExtData> madExtDataList = madExtDataService.selectByErpIds(madExtDataQO);

        return madExtDataList.stream()
                .flatMap(extData -> {
                    String[] codes = extData.getField04().split(",");
                    return Arrays.stream(codes)
                            .map(code -> new AbstractMap.SimpleEntry<>(code.trim(), extData.getMadCode()));
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> newVal
                ));
    }


    private Map<String, List<PcxBillExpCommon>> selectBillExpCommonGroupMap(List<String> billIds) {
        List<PcxBillExpCommon> pcxBillExpCommons = pcxBillExpCommonDao.selectList(
                Wrappers.lambdaQuery(PcxBillExpCommon.class)
                        .in(PcxBillExpCommon::getBillId, billIds)
        );

        return pcxBillExpCommons
                .stream()
                .filter(pcxBillExpCommon -> !"*".equals(pcxBillExpCommon.getExpenseCode()))
                .collect(Collectors.groupingBy(PcxBillExpCommon::getBillId));
    }

    private Map<String, PcxProjectExpense> getPcxProjectExpenseMap(String agyCode,
                                                                   List<String> expenseCode,
                                                                   String billId,
                                                                   Map<String, List<PcxBillExpCommon>> billExpCommonGroupMap) {
        if (expenseCode.isEmpty()) {
            return Collections.emptyMap();
        }
        List<PcxProjectExpense> pcxProjectExpenses = pcxProjectExpenseDao.selectList(
                Wrappers.<PcxProjectExpense>lambdaQuery()
                        .eq(PcxProjectExpense::getAgyCode, agyCode)
                        .in(PcxProjectExpense::getExpenseCode, expenseCode)
        );
        Map<String, PcxProjectExpense> expenseCodeMap =
                pcxProjectExpenses.stream().collect(Collectors.toMap(PcxProjectExpense::getExpenseCode, Function.identity(), (oldVal, newVal) -> newVal));

        Map<String, PcxProjectExpense> projectExpenseMap = new HashMap<>();
        if (billExpCommonGroupMap.containsKey(billId)) {
            List<PcxBillExpCommon> pcxBillExpCommons = billExpCommonGroupMap.get(billId);
            for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
                if (expenseCodeMap.containsKey(pcxBillExpCommon.getExpenseCode())) {
                    PcxProjectExpense pcxProjectExpense = expenseCodeMap.get(pcxBillExpCommon.getExpenseCode());
                    projectExpenseMap.put(pcxBillExpCommon.getId(), pcxProjectExpense);
                }
            }
        }
        return projectExpenseMap;
    }

}
