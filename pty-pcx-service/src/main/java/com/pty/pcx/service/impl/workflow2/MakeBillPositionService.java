package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Indexed
@Service
public class MakeBillPositionService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Resource
    private PcxBillService pcxBillService;
    @Autowired
    private BillMainService billMainService;
    @Override
    public List<String> getPositionIds() {
        return Collections.singletonList(PcxNodeEnum.make_bill.getId());
    }

    @Override
    public List<String> findPositionUser(String billId) {
        PcxBill msg = billMainService.view(billId);
        Assert.state(msg != null, "调用单据异常");
        return Collections.singletonList(msg.getCreator());
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
