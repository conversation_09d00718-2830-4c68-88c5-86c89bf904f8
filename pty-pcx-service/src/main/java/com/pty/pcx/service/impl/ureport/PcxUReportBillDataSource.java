package com.pty.pcx.service.impl.ureport;

import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.entity.print.PcxPrintResult;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.MapUtil;
import com.pty.pub.common.util.StringUtil;
import com.pty.ureport2.api.IUreportPcxBillDataSource;
import com.pty.ureport2.entity.vo.pcx.PcxBill;
import com.pty.ureport2.entity.vo.pcx.PcxBillExpDetail;
import com.pty.ureport2.entity.vo.pcx.PcxProcessNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@org.springframework.stereotype.Indexed
@Service("pcxBillDataSource")
public class PcxUReportBillDataSource implements IUreportPcxBillDataSource {

    public static final String PCX_CHECK_KEY = "PCX_CHECK";

    public static final String DEFAULT_BILL_KIND = "无需发票";

    public static final String SUBSIDY = "补助";

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private IPcxBasExpTypeService pcxBasExpTypeService;

    @Autowired
    private PcxExpDetailEcsRelService PcxExpDetailEcsRelService;

    @Override
    public List<PcxBill> getBillPrintData(Map<String, Object> param) throws Exception {
        PcxPrintResult data = (PcxPrintResult) cacheManager.getCache(PCX_CHECK_KEY).get(param.get("id")).get();
        if(ObjectUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        List<Map<String,Object>> rows = data.getRows();
        List<PcxBill> bills = MapUtil.convertListMap2ListBean(rows, PcxBill.class);
        assemblePaPerTotalAmt(bills, param);
        return bills;
    }

    private void assemblePaPerTotalAmt(List<PcxBill> bills, Map<String, Object> param) throws Exception {
        List<Map<String, Object>> specificities = getExpDetailsFromCache(param);
        if (CollectionUtil.isNotEmpty(specificities)) {
            List<PcxBillExpDetail> expDetails = MapUtil.convertListMap2ListBean(specificities, PcxBillExpDetail.class);
            if (CollectionUtil.isNotEmpty(expDetails)) {
                Map<String, BigDecimal> groupedByBillIdSum = expDetails.stream()
                        // mashaojie 20250630 - 后补票（定额发票）不属于纸质票，进行计算时需要过滤掉
                        .filter(item->StringUtil.isNotBlank(item.getEcsBillId()))
                        .collect(Collectors.groupingBy(
                                PcxBillExpDetail::getBillId,
                                Collectors.mapping(
                                        PcxBillExpDetail::getInputAmt,
                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                                )
                        ));
                for (PcxBill bill : bills) {
                    BigDecimal totalAmt = groupedByBillIdSum.get(bill.getId());
                    if (!ObjectUtils.isEmpty(totalAmt)) {
                        bill.setPaperTotalAmt(StringUtil.getStringValue(totalAmt));
                    }
                }
            }
        }
    }

    @Override
    public List<PcxProcessNode> getBillProcess(Map<String, Object> param) throws Exception {
        PcxPrintResult data = (PcxPrintResult) cacheManager.getCache(PCX_CHECK_KEY).get(param.get("id")).get();
        if(ObjectUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        List<Map<String, Object>> dataProcessNode = data.getProcessNode();
        List<PcxProcessNode> relust = MapUtil.convertListMap2ListBean(dataProcessNode, PcxProcessNode.class);
        return relust;
    }

    @Override
    public List<PcxBillExpDetail> getBillExpDetail(Map<String, Object> param) throws Exception {
        // 获取并验证缓存数据
        List<Map<String, Object>> specificities = getExpDetailsFromCache(param);
        // 将Map列表转换为Bean列表
        List<PcxBillExpDetail> expDetails = MapUtil.convertListMap2ListBean(specificities, PcxBillExpDetail.class);
        if (CollectionUtil.isEmpty(expDetails)) {
            return Collections.emptyList();
        }
        // 使用流提取唯一编码和ID
        Set<String> expDetailCodes = extractNonEmptySet(expDetails, PcxBillExpDetail::getExpDetailCode);
        Set<String> expDetailIds = extractNonEmptySet(expDetails, PcxBillExpDetail::getId);

        // 获取并映射关联数据
        Map<String, PcxExpDetailEcsRel> ecsRelMap = PcxExpDetailEcsRelService.selectByDetailIds(new ArrayList<>(expDetailIds))
                .stream()
                .filter(e -> e.getDetailId() != null)
                .collect(Collectors.toMap(
                        PcxExpDetailEcsRel::getDetailId,
                        Function.identity(),
                        (oldData, newData) -> oldData
                ));

        // 准备查询对象并获取费用类型
        PcxBillExpDetail firstDetail = expDetails.get(0);
        PcxBasExpTypeQO qo = buildExpTypeQuery(firstDetail, expDetailCodes);
        Map<String, PcxBasExpType> expTypeMap = pcxBasExpTypeService.selectByQO(qo)
                .stream()
                .filter(e -> e.getExpenseCode() != null)
                .collect(Collectors.toMap(
                        PcxBasExpType::getExpenseCode,
                        Function.identity(),
                        (oldData, newData) -> oldData
                ));

        // 处理费用明细
        processExpDetails(expDetails, expTypeMap, ecsRelMap);
        return expDetails;
    }

    private List<Map<String, Object>> getExpDetailsFromCache( Map<String, Object> param) {
        Object cacheObj = Objects.requireNonNull(cacheManager.getCache(PCX_CHECK_KEY)
                        .get(param.get("id")))
                .get();
        if (ObjectUtils.isEmpty(cacheObj)) {
            return Collections.emptyList();
        }
        PcxPrintResult data = (PcxPrintResult) cacheObj;
        List<Map<String, Object>> specificities = data.getSpecificity();
        if (CollectionUtil.isEmpty(specificities)) {
            return Collections.emptyList();
        }
       return specificities;
    }

    /**
     * 辅助方法：提取非空值到Set集合
     */
    private <T> Set<String> extractNonEmptySet(List<T> items, Function<T, String> mapper) {
        return items.stream()
                .map(mapper)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.toSet());
    }

    /**
     * 辅助方法：构建费用类型查询对象
     */
    private PcxBasExpTypeQO buildExpTypeQuery(PcxBillExpDetail detail, Set<String> codes) {
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setExpTypeCodes(new ArrayList<>(codes));
        qo.setMofDivCode(detail.getMofDivCode());
        qo.setAgyCode(detail.getAgyCode());
        qo.setFiscal(detail.getFiscal());
        return qo;
    }

    /**
     * 辅助方法：处理费用明细数据
     */
    private void processExpDetails(List<PcxBillExpDetail> details,
                                   Map<String, PcxBasExpType> expTypeMap,
                                   Map<String, PcxExpDetailEcsRel> ecsRelMap) {
        for (int i = 0; i < details.size(); i++) {
            PcxBillExpDetail item = details.get(i);
            item.setSeq(i + 1);

            // 处理票据类型
//            item.setEcsBillKind(getBillKindName(item.getEcsBillKind()));
            // 处理票种
            item.setEcsBillType(getBillTypeName(item.getEcsBillType()));
            // 设置费用明细名称
            if (CollectionUtil.isNotEmpty(expTypeMap)) {
                PcxBasExpType expType = expTypeMap.get(item.getExpDetailCode());
                if (expType != null) {
                    item.setExpDetailName(expType.getExpenseName());
                }
            }
            if (StringUtil.isNotBlank(item.getExpDetailName()) && item.getExpDetailName().contains(SUBSIDY)) {
                item.setEcsNum(0);
            }
            // 设置摘要信息
            if (CollectionUtil.isNotEmpty(ecsRelMap)) {
                PcxExpDetailEcsRel ecsRel = ecsRelMap.get(item.getId());
                if (ecsRel != null) {
                    item.setSummary(ecsRel.getEcsBillDesc());
                }
            }
        }
    }

    private String getBillTypeName(String ecsBillType) {
        for (EcsEnum.BillType billType : EcsEnum.BillType.values()) {
            if (!billType.getCode().equals(ecsBillType)) {
                continue;
            }
            return billType.getName();
        }
        return DEFAULT_BILL_KIND;
    }

    private String getBillKindName(String ecsBillKindCode) {
        for (EcsEnum.BillKind billKind : EcsEnum.BillKind.values()) {
            if (!billKind.getCode().equals(ecsBillKindCode)) {
                continue;
            }
            return billKind.getName();
        }
        return DEFAULT_BILL_KIND;
    }
}
