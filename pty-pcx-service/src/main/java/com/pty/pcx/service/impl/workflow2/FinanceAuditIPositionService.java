package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.beust.jcommander.internal.Lists;
import com.pty.mad.entity.PaDeptFinanceSetting;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.mad.IMadDepartmentExternalService;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.PaDeptFinanceSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class FinanceAuditIPositionService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Resource
    private PcxBillService pcxBillService;

    @Resource
    private PaDeptFinanceSettingService paDeptFinanceSettingService;

    @Autowired(required = false)
    IMadDepartmentExternalService madDepartmentExternalService;

    @Override
    public List<String> getPositionIds() {
        return Lists.newArrayList(
                String.format("%s_%s", PcxNodeEnum.finance_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.finance_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.finance_audit.getId(), "3")
        );
    }

    @Override
    public List<String> findPositionUser(String billNo) {
        CheckMsg<PcxBillVO> msg = pcxBillService.view(billNo);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());
        PcxBillVO vo = msg.getData();
        PcxBill basicInfo = vo.getBasicInfo();
        Assert.state(vo != null, "无法查到单据信息{}", billNo);

        PaDeptFinanceSetting paDeptFinanceSetting = new PaDeptFinanceSetting();
        paDeptFinanceSetting.setAgyCode(basicInfo.getAgyCode());
        paDeptFinanceSetting.setMofDivCode(basicInfo.getMofDivCode());
        paDeptFinanceSetting.setFiscal(basicInfo.getFiscal());
        paDeptFinanceSetting.setTenantId(basicInfo.getTenantId());
        String positionId = ThreadLocalUtil.get();
        if(positionId.endsWith("1")){
            paDeptFinanceSetting.setFirstApprove(1);
        }
        if(positionId.endsWith("2")){
            paDeptFinanceSetting.setReviewApprove(1);
        }
        if(positionId.endsWith("3")){
            paDeptFinanceSetting.setFinanceLeader(1);
        }

        List<PaDeptFinanceSetting> paDeptFinanceSettingList = paDeptFinanceSettingService.selectList(paDeptFinanceSetting);
        if (CollectionUtils.isEmpty(paDeptFinanceSettingList)) {
            return Lists.newArrayList();
        }
        List<MadDepartmentDTO> departments = madDepartmentExternalService.selectAgyDepartment(MapUtil.<String, String>builder()
                .put("mofDivCode", basicInfo.getMofDivCode())
                .put("fiscal", basicInfo.getFiscal())
                .build());
        Map<String, MadDepartmentDTO> code$department$rel = departments.stream().collect(Collectors.toMap(dept -> String.join("|",dept.getAgyCode(), dept.getDepartmentCode()),
                item -> item, (k1, k2) -> k1));
        paDeptFinanceSettingList = paDeptFinanceSettingList.stream().filter(item ->
                {
                    Integer isEnabled = code$department$rel.getOrDefault(String.join("|", StrUtil.firstNonBlank(item.getSourceAgyCode(), item.getAgyCode()), item.getDeptCode()), new MadDepartmentDTO()).getIsEnabled();
                    return Objects.nonNull(isEnabled) && 1 == isEnabled;
                })
                .collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        Map<String, List<String>> foreignEmployee = paDeptFinanceSettingList.stream()
                .filter(item -> !item.getAgyCode().equals(item.getSourceAgyCode()))
                .collect(Collectors.groupingBy(PaDeptFinanceSetting::getSourceAgyCode, Collectors.mapping(PaDeptFinanceSetting::getFinanceEmpCode, Collectors.toList())));
        foreignEmployee.forEach((agyCode, empCodes)->{
            result.addAll(getUserIds(agyCode, basicInfo.getMofDivCode(), Integer.valueOf(basicInfo.getFiscal()), empCodes));
        });
        result.addAll(getUserIds(basicInfo,  paDeptFinanceSettingList.stream().filter(item -> item.getAgyCode().equals(item.getSourceAgyCode())).map(PaDeptFinanceSetting::getFinanceEmpCode).collect(Collectors.toList())));
        return result;
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
