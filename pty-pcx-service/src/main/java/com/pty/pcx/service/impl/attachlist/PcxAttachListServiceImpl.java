package com.pty.pcx.service.impl.attachlist;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.pty.fileservice.entity.PaAttach;
import com.pty.pcx.api.attachlist.IPcxAttachListService;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.block.IBaseBlockService;
import com.pty.pcx.common.constant.FieldDisptypeEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.attachlist.PcxAttachListDao;
import com.pty.pcx.dao.attachlist.PcxAttachListRelationDao;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.entity.attachlist.PcxAttachListRelation;
import com.pty.pcx.pa.IPcxAttachService;
import com.pty.pcx.qo.attachlist.PcxAttachListQO;
import com.pty.pcx.qo.attachlist.PcxAttachListRelationQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.block.BlockPropertyQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ValidUtil;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.attachlist.PcxAttachListVO;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.SpringUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ClassName: PcxAttachListServiceImpl
 * Description: 附件清单：新增、保存、同步到报销模块
 * Date: 2024/11/6  下午5:19
 * Author: wangbao
 **/
@Service
@Indexed
@Slf4j
public class PcxAttachListServiceImpl implements IPcxAttachListService, IBaseBlockService {

    @Autowired
    private PcxAttachListDao pcxAttachListDao;
    @Autowired
    private PcxAttachListRelationDao pcxAttachListRelationDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private IPcxAttachService pcxAttachService;

    @Autowired
    private IPcxBasItemService pcxBasItemService;

    /**
     * 新增或修改附件清单
     */
    @Override
    @Transactional
    public CheckMsg<?> saveOrUpdate(List<PcxAttachListQO> paAttachListQOList) {
        CheckMsg<?> paramCheckMsg = checkSaveOrUpdateParam(paAttachListQOList);
        if(!paramCheckMsg.isSuccess()){
            return paramCheckMsg;
        }
        for (int i = 0; i < paAttachListQOList.size(); i++) {
            PcxAttachListQO paAttachListQO = paAttachListQOList.get(i);
            paAttachListQO.setSeq(i + 1);
        }
        SpringUtil.getBean(BatchServiceUtil.class).batchProcess(paAttachListQOList, PcxAttachListDao.class, PcxAttachListServiceImpl::saveAttachList);
        return CheckMsg.success("保存成功");
    }

    /**
     * 校验参数：新增或修改
     */
    private CheckMsg<?> checkSaveOrUpdateParam(List<PcxAttachListQO> paAttachListQOList) {
        ValidUtil.checkEmptyCollection(paAttachListQOList, "查询失败，paAttachListQOList 不能为空");
        for(PcxAttachListQO paAttachListQO : paAttachListQOList){
            validBaseInfo(paAttachListQO);
            ValidUtil.checkEmptyStr(paAttachListQO.getFuncType(), "查询失败，功能类型不能为空");
            ValidUtil.checkEmptyStr(paAttachListQO.getFuncCode(), "查询失败，功能编码 不能为空");
            ValidUtil.checkEmptyStr(paAttachListQO.getBilltypeCode(), "查询失败，单据类型 不能为空");
            paAttachListQO.setTenantId(StringUtil.isEmpty(paAttachListQO.getTenantId()) ? PtyContext.getTenantId() : paAttachListQO.getTenantId());
            paAttachListQO.setAttachTypeId(StringUtil.isEmpty(paAttachListQO.getAttachTypeId()) ? PcxConstant.SYS_ID : paAttachListQO.getAttachTypeId());
        }
        return CheckMsg.success();
    }

    /**
     * 单条新增或保存: attachListId 主键为空则新增，主键不为空则修改
     */
    private static void saveAttachList(PcxAttachListDao pcxAttachListDao, PcxAttachListQO paAttachListQO) {
        handleUploadType(paAttachListQO);
        if (StringUtil.isEmpty(paAttachListQO.getAttachListId())) {
            paAttachListQO.setAttachListId(StringUtil.getUUID());
            pcxAttachListDao.insertSelective(paAttachListQO);
        } else {
            pcxAttachListDao.updateById(paAttachListQO);
        }
    }

    /**
     * 处理上传类型: 多类型场景，使用逗号拼接，为空则提供空串
     */
    private static void handleUploadType(PcxAttachListQO paAttachListQO) {
        List<String> uploadTypeList = paAttachListQO.getUploadTypes();
        if (StringUtil.isNotEmpty(uploadTypeList)) {
            String uploadTypeJoinStr = uploadTypeList.stream().map(StringUtil::getStringValue).collect(Collectors.joining(","));
            paAttachListQO.setUploadType(uploadTypeJoinStr);
        }else{
            paAttachListQO.setUploadType(StringUtil.EMPTY);
        }
    }

    /**
     * 删除附件清单：已经使用的 id 不能删除
     */
    @Override
    public CheckMsg<?> deleteByAttachListId(PcxAttachListQO paAttachListQO) {
        // 删除前校验参数
        CheckMsg<?> paramCheckMsg = checkDeleteByAttachListIdParam(paAttachListQO);
        if(!paramCheckMsg.isSuccess()){
            return paramCheckMsg;
        }
        // 查询已使用的附件清单id
        List<String> attachListIds = paAttachListQO.getAttachListIds();
        List<String> usedIdList = selectUsedAttachListId(attachListIds);
        attachListIds.removeAll(usedIdList);
        // 删除未使用的附件清单id
        if(CollectionUtil.isNotEmpty(attachListIds)){
            SpringUtil.getBean(BatchServiceUtil.class).batchProcess(attachListIds, PcxAttachListDao.class, PcxAttachListServiceImpl::singleDeleteByAttachListId);
        }
        // 返回删除失败的附件清单id
        return buildDeleteResultMsg(usedIdList, attachListIds);
    }

    /**
     * 校验参数：批量删除
     */
    private CheckMsg<?> checkDeleteByAttachListIdParam(PcxAttachListQO paAttachListQO) {
        validBaseInfo(paAttachListQO);
        ValidUtil.checkContainsEmptyStr(paAttachListQO.getAttachListIds(), "查询失败，attachListIds 中不能含有空元素");
        return CheckMsg.success();
    }

    /**
     * 查询已使用的附件清单id
     */
    private List<String> selectUsedAttachListId(List<String> attachListIds) {
        List<PcxAttachListRelation> pcxAttachRelList = new ArrayList<>();
        List<List<String>> idList = Lists.partition(attachListIds, 800);
        for (List<String> ids : idList) {
            PcxAttachListRelationQO paAttachListRelationQO = new PcxAttachListRelationQO();
            paAttachListRelationQO.setAttachListIds(ids);
            List<PcxAttachListRelation> paAttachListRelations = pcxAttachListRelationDao.selectByAttachListIds(paAttachListRelationQO);
            pcxAttachRelList.addAll(paAttachListRelations);
        }
        return pcxAttachRelList.stream()
                .map(PcxAttachListRelation::getAttachListId)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 单条删除
     */
    private static void singleDeleteByAttachListId(PcxAttachListDao pcxAttachListDao, String attachListId) {
        if(StringUtil.isEmpty(attachListId)){
            return;
        }
        PcxAttachListQO paAttachListQO = new PcxAttachListQO();
        paAttachListQO.setAttachListId(attachListId);
        pcxAttachListDao.deleteById(paAttachListQO);
    }

    /**
     * 删除结果提示：成功 {} 条 , 失败 {} 条, 失败详情：附件清单id {}, 已经被使用, 删除失败;
     * @param failedIdList 删除失败的附件清单id
     * @param successIdList 删除成功的附件清单id
     */
    private CheckMsg<?> buildDeleteResultMsg(List<String> failedIdList, List<String> successIdList) {
        StringBuilder msgBuilder = new StringBuilder();
        msgBuilder.append("删除成功 ").append(successIdList.size()).append(" 条 ");
        // 全部删除成功
        if(failedIdList.isEmpty()){
            return CheckMsg.success(msgBuilder.toString()).setMsgInfo("删除附件清单成功");
        }
        // 部分删除失败
        msgBuilder.append(", 失败 ").append(failedIdList.size()).append(" 条").append(", 失败详情：");
        for (String failedId : failedIdList) {
            msgBuilder.append("附件清单id ").append(failedId).append(" 已经被使用, 删除失败;");
        }
        return CheckMsg.fail(msgBuilder.toString()).setData(failedIdList);
    }

    /**
     * 查询附件清单：主线索为功能编码
     */
    @Override
    public CheckMsg<?> selectByFuncCode(PcxAttachListQO paAttachListQO) {
        CheckMsg<?> paramCheckMsg = checkSelectByFuncCodeParam(paAttachListQO);
        if(!paramCheckMsg.isSuccess()){
            return paramCheckMsg;
        }
        List<PcxAttachListVO> pcxAttachListVOList = pcxAttachListDao.select(paAttachListQO);
        handlePcxAttachListVOList(pcxAttachListVOList);
        return CheckMsg.success(pcxAttachListVOList);
    }

    @Override
    public List<PcxAttachListVO> selectByQO(PcxAttachListQO pcxAttachListQO) {
        if(ObjectUtils.isEmpty(pcxAttachListQO)){
            return new ArrayList<>();
        }
        return pcxAttachListDao.selectByQO(pcxAttachListQO);
    }

    /**
     * 校验购汇单单据关联关系参数
     */
    private CheckMsg<?> validateForexReceiptRelation(List<PcxAttachListRelationQO> relationQOList) {
        for (PcxAttachListRelationQO qo : relationQOList) {
            if (StringUtil.isEmpty(qo.getBillId()) || StringUtil.isEmpty(qo.getAttachId())) {
                log.error("购汇单单据关联关系参数不完整，billId: {}, attachId: {}", qo.getBillId(), qo.getAttachId());
                return CheckMsg.fail("购汇单单据关联关系参数不完整");
            }
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> saveForexReceiptRel(List<PcxAttachListRelationQO> relationQOList) {
        if (CollectionUtil.isEmpty(relationQOList)) {
            log.error("无法保存购汇单单据关联关系，relationQOList入参集合为空");
            return CheckMsg.success(Collections.emptyList());
        }
        CheckMsg<?> checkMsg = validateForexReceiptRelation(relationQOList);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }
        try {
            // 查询附件信息
            Map<String, PaAttach> attachMap = getAttachMap(relationQOList);

            // 构建关联关系
            List<PcxAttachListRelation> relations = buildRelations(relationQOList);
            if (CollectionUtil.isEmpty(relations)){
                return CheckMsg.fail().setMsgInfo("购汇单附件信息为空");
            }
            //基于单据id以及 attachId删除 确定是购汇单文件
            List<String> attachIds = relationQOList.stream()
                    .map(PcxAttachListRelationQO::getAttachId)
                    .collect(Collectors.toList());
            List<String> billIds = relationQOList.stream()
                    .map(PcxAttachListRelationQO::getBillId)
                    .collect(Collectors.toList());
            pcxAttachListRelationDao.removeByBillIdsAndAttachIds(billIds, attachIds);

            batchServiceUtil.batchProcess(relations, PcxAttachListRelationDao.class, PcxAttachListRelationDao::insertSelective);
            // 将 relations 转换为 VO
            List<PcxBillAttachRelationVO> voList = convertToVoList(relations, attachMap);
            log.info("成功保存购汇单单据关联关系，共{}条记录", relations.size());
            return CheckMsg.success().setData(voList);
        } catch (Exception e) {
            log.error("保存购汇单单据关联关系失败", e);
            throw new RuntimeException("保存购汇单单据关联关系失败");
        }
    }

    /**
     * 获取附件信息 Map
     */
    private Map<String, PaAttach> getAttachMap(List<PcxAttachListRelationQO> relationQOList) {
        List<String> attachIds = relationQOList.stream()
                .map(PcxAttachListRelationQO::getAttachId)
                .collect(Collectors.toList());

        List<PaAttach> listByIds = pcxAttachService.getListByIds(attachIds);
        return listByIds.stream().collect(
                Collectors.toMap(PaAttach::getAttachId, Function.identity(), (a, b) -> a));
    }

    /**
     * 构建关联关系
     */
    private List<PcxAttachListRelation> buildRelations(List<PcxAttachListRelationQO> relationQOList) {
        List<PcxAttachListRelation> relations = Lists.newArrayList();
        relationQOList.forEach(qo -> {
            PcxAttachListRelation relation = new PcxAttachListRelation();
            relation.setId(IDGenerator.id());
            relation.setBillId(qo.getBillId());
            relation.setAttachId(qo.getAttachId());
            relation.setAttachListId(qo.getAttachListId());
            relation.setAttachTypeId(qo.getAttachTypeId());
            if (StringUtil.isEmpty(qo.getAttachTypeId())){
                relation.setAttachTypeId(PcxConstant.ATTACH_TYPE_FOREX_RECEIPT);
            }
            if (StringUtil.isEmpty(qo.getAttachListId())){
                //todo 暂时forexReceipt作为AttachListId
                relation.setAttachListId(PcxConstant.ATTACH_TYPE_FOREX_RECEIPT);
            }
            relation.setUploadWay(1);
            relation.setAttachListName(qo.getAttachListName());
            relations.add(relation);
        });
        return relations;
    }

    /**
     * 将关联关系转换为 VO
     */
    private List<PcxBillAttachRelationVO> convertToVoList(List<PcxAttachListRelation> relations, Map<String, PaAttach> attachMap) {
        return relations.stream().map(relation -> {
            PcxBillAttachRelationVO vo = new PcxBillAttachRelationVO();
            vo.setId(relation.getId());
            vo.setBillId(relation.getBillId());
            vo.setAttachId(relation.getAttachId());
            vo.setAttachTypeId(relation.getAttachTypeId());
            vo.setAttachListId(relation.getAttachListId());
            vo.setCreator(PtyContext.getUsername());
            vo.setUploadWay(relation.getUploadWay());
            vo.setAttachListName(relation.getAttachListName());
            // 从 attachMap 中获取文件名称
            PaAttach attach = attachMap.get(relation.getAttachId());
            if (attach != null) {
                vo.setFileName(attach.getFileName());
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public CheckMsg<?> deleteForexReceiptRel(List<PcxAttachListRelationQO> relationQOList) {
        if (CollectionUtil.isEmpty(relationQOList)) {
            return CheckMsg.success("入参集合为空,暂无可删除的附件");
        }

        // 参数校验
        CheckMsg<?> checkMsg = validateForexReceiptRelation(relationQOList);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }
        List<String> attachIds = relationQOList.stream()
                .map(PcxAttachListRelationQO::getAttachId)
                .collect(Collectors.toList());
        List<String> billIds = relationQOList.stream()
                .map(PcxAttachListRelationQO::getBillId)
                .collect(Collectors.toList());

        try {
            pcxAttachListRelationDao.removeByBillIdsAndAttachIds(billIds, attachIds);
            log.info("成功删除购汇单关联附件，共{}条记录，attachIds:{}, billIds:{}", attachIds.size(), attachIds, billIds);
            return CheckMsg.success("删除附件成功");
        } catch (Exception e) {
            log.error("删除购汇单关联附件失败", e);
            return CheckMsg.fail("删除购汇单关联附件失败");
        }
    }

    /**
     * 校验参数：查询附件清单
     */
    private CheckMsg<?> checkSelectByFuncCodeParam(PcxAttachListQO paAttachListQO) {
        validBaseInfo(paAttachListQO);
        ValidUtil.checkEmptyStr(paAttachListQO.getFuncType(), "查询失败，功能类型 不能为空");
        ValidUtil.checkEmptyStr(paAttachListQO.getFuncCode(), "查询失败，功能编码 不能为空");
        return CheckMsg.success();
    }

    /**
     * 对附件清单查询结果进行解析处理
     */
    private void handlePcxAttachListVOList(List<PcxAttachListVO> pcxAttachListVOList) {
        if(CollectionUtil.isEmpty(pcxAttachListVOList)){
            return;
        }
        for (PcxAttachListVO paAttachListVO : pcxAttachListVOList){
            //分割上传文件类型
            String uploadTypeStr = paAttachListVO.getUploadType();
            if(StringUtil.isEmpty(uploadTypeStr)){
                return;
            }
            paAttachListVO.setUploadTypes(Arrays.asList(uploadTypeStr.split(",")));
        }
    }

    /**
     * 基本参数校验：mofDivCode、fiscal、agyCode
     */
    private void validBaseInfo(PcxAttachListQO paAttachListQO) {
        ValidUtil.checkEmptyObject(paAttachListQO, "查询失败，paAttachListQO不能为空");
        ValidUtil.checkEmptyStr(paAttachListQO.getMofDivCode(), "查询失败，单位编码不能为空");
        ValidUtil.checkEmptyStr(paAttachListQO.getAgyCode(), "查询失败，区划编码不能为空");
        ValidUtil.checkEmptyObject(paAttachListQO.getFiscal(), "查询失败，年度不能为空");
    }

    @Override
    public List<BlockProperty> getBlockProperties(BlockPropertyQO blockPropertyQO) {
        PcxAttachListQO pcxAttachListQO = createPcxAttachListQO(blockPropertyQO);
        List<PcxAttachListVO> pcxAttachListVOList = pcxAttachListDao.selectByQO(pcxAttachListQO);
        String billFuncCode = blockPropertyQO.getBillFuncCodeEnum().getCode();
        Map<String, List<PcxAttachListVO>> filteredList = filterAttachListByBillTypeCode(pcxAttachListVOList, billFuncCode);
        List<BlockProperty> result = new ArrayList<>();
        List<PcxAttachListVO> pcxAttachListVOS = new ArrayList<>();
        for (Map.Entry<String, List<PcxAttachListVO>> entry : filteredList.entrySet()) {
            List<PcxAttachListVO> pcxAttachListVOs = entry.getValue();
            if (pcxAttachListVOs.isEmpty()) {
                continue;
            }
            // 获取通用费用项
            List<PcxAttachListVO> universalAttach = pcxAttachListVOs.stream()
                    .filter(item -> PcxConstant.UNIVERSAL_EXPENSE_CODE.equals(item.getFuncCode()))
                    .collect(Collectors.toList());
            // 选择最终的 PcxAttachListVO
            PcxAttachListVO selectedAttach = universalAttach.size() > 1
                    ? pcxAttachListVOs.stream()
                    .filter(item -> !PcxConstant.UNIVERSAL_EXPENSE_CODE.equals(item.getFuncCode()))
                    .min(Comparator.comparing(PcxAttachListVO::getFuncCode))
                    .orElse(pcxAttachListVOs.get(0))
                    : pcxAttachListVOs.get(0);
            pcxAttachListVOS.add(selectedAttach);
        }
        pcxAttachListVOS.sort(Comparator.comparing(PcxAttachListVO::getSeq));
        for (PcxAttachListVO pcxAttachListVO : pcxAttachListVOS) {
            BlockProperty blockProperty = new BlockProperty();
            blockProperty.setFieldValue(pcxAttachListVO.getAttachListId());
            blockProperty.setFieldName(pcxAttachListVO.getAttachListName());
            blockProperty.setEditorCode(FieldDisptypeEnum.FILE.getCode());
            blockProperty.setFormCode(pcxAttachListVO.getFuncCode());
            blockProperty.setFormName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
            blockProperty.setIsRequired(isRequired(pcxAttachListVO, billFuncCode));
            result.add(blockProperty);
        }
        assembleBaseAttachList(result,blockPropertyQO);
        return result;
    }

    /***
     * 默认每个费用都有一个附件清单属性，并且在最后的位置上
     */
    private void assembleBaseAttachList(List<BlockProperty> result,BlockPropertyQO blockPropertyQO) {
        BlockProperty blockProperty = new BlockProperty();
        blockProperty.setEditorCode(FieldDisptypeEnum.FILE.getCode());
        blockProperty.setFieldName("附件");
        blockProperty.setFieldValue(PcxConstant.SYS_ID);
        if(CollectionUtil.isEmpty(result)){
            //查询事项的费用
            PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
            pcxBasItemQO.setItemCode(blockPropertyQO.getItemCode());
            pcxBasItemQO.setMofDivCode(blockPropertyQO.getMofDivCode());
            pcxBasItemQO.setAgyCode(blockPropertyQO.getAgyCode());
            pcxBasItemQO.setFiscal(blockPropertyQO.getFiscal());
            pcxBasItemQO.setBilltypeCode(blockPropertyQO.getBillFuncCodeEnum().getCode());
            PcxBasItemVO pcxBasItemVO = pcxBasItemService.selectByItemCode(pcxBasItemQO);
            // 如果是通用类报销
            if(PcxConstant.UNIVERSAL_ITEM_CODE.equals(blockPropertyQO.getItemCode())){
                blockProperty.setFormCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
            }else if (CollectionUtil.isNotEmpty(pcxBasItemVO.getExpenseCodes())){
                blockProperty.setFormCode(pcxBasItemVO.getExpenseCodes().get(0));
            }else {
                blockProperty.setFormCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
            }
        }else{
            blockProperty.setFormCode(result.get(0).getFormCode());
        }
        blockProperty.setFormName("默认附件清单");
        result.add(blockProperty);
    }

    private PcxAttachListQO createPcxAttachListQO(BlockPropertyQO blockPropertyQO) {
        PcxAttachListQO pcxAttachListQO = new PcxAttachListQO();
        if(BillFuncCodeEnum.LOAN.getCode().equals(blockPropertyQO.getBillFuncCodeEnum().getCode())){
            pcxAttachListQO.setFuncType(BillFuncCodeEnum.LOAN.getFuncCode());
            pcxAttachListQO.setFuncCode(blockPropertyQO.getItemCode());
        }else{
            pcxAttachListQO.setFuncType(BillFuncCodeEnum.EXPENSE.getFuncCode());
            pcxAttachListQO.setFuncCodes(blockPropertyQO.getBasExpTypeCodes());
        }
        pcxAttachListQO.setMofDivCode(blockPropertyQO.getMofDivCode());
        pcxAttachListQO.setAgyCode(blockPropertyQO.getAgyCode());
        pcxAttachListQO.setFiscal(Integer.valueOf(blockPropertyQO.getFiscal()));
        pcxAttachListQO.setTenantId(blockPropertyQO.getTenantId());
        return pcxAttachListQO;
    }

    private int isRequired(PcxAttachListVO pcxAttachListVO, String billFuncCode) {
        if (StringUtil.isEmpty(pcxAttachListVO.getIsNecessary())) {
            return PubConstant.LOGIC_FALSE;
        }
        String[] billTypeCodes = pcxAttachListVO.getIsNecessary().split(",");
        return Arrays.asList(billTypeCodes).contains(billFuncCode) ? PubConstant.LOGIC_TRUE : PubConstant.LOGIC_FALSE;
    }

    private Map<String,List<PcxAttachListVO>> filterAttachListByBillTypeCode(List<PcxAttachListVO> pcxAttachListVOList, String billFuncCode) {
        return pcxAttachListVOList.stream()
                .filter(pcxAttachListVO -> {
                    String[] billTypeCodes = StringUtil.nullToEmpty(pcxAttachListVO.getBilltypeCode()).split(",");
                    return Arrays.asList(billTypeCodes).contains(billFuncCode);
                })
                .collect(Collectors.groupingBy(PcxAttachListVO::getAttachListName));
    }
}
