package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import com.pty.mad.entity.PaDeptFinanceSetting;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.mad.IMadDepartmentExternalService;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.PaDeptFinanceSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class CasherIPositionService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Resource
    private PcxBillService pcxBillService;

    @Resource
    private PaDeptFinanceSettingService paDeptFinanceSettingService;

    @Autowired
    private IMadDepartmentExternalService madDepartmentExternalService;

    @Override
    public List<String> getPositionIds() {
        return Collections.singletonList(PcxNodeEnum.cashier.getId());
    }

    @Override
    public List<String> findPositionUser(String billNo) {
        CheckMsg<PcxBillVO> msg = pcxBillService.view(billNo);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());
        PcxBillVO vo = msg.getData();
        PcxBill basicInfo = vo.getBasicInfo();
        Assert.state(vo != null, "无法扎到单据信息{}", billNo);

        PaDeptFinanceSetting paDeptFinanceSetting = new PaDeptFinanceSetting();
        paDeptFinanceSetting.setAgyCode(basicInfo.getAgyCode());
        paDeptFinanceSetting.setMofDivCode(basicInfo.getMofDivCode());
        paDeptFinanceSetting.setFiscal(basicInfo.getFiscal());
        paDeptFinanceSetting.setTenantId(basicInfo.getTenantId());
        paDeptFinanceSetting.setCashier(1);

        List<PaDeptFinanceSetting> paDeptFinanceSettingList = paDeptFinanceSettingService.selectList(paDeptFinanceSetting);

        List<MadDepartmentDTO> departments = madDepartmentExternalService.selectAgyDepartment(MapUtil.<String, String>builder()
                .put("agyCode", basicInfo.getAgyCode())
                .put("mofDivCode", basicInfo.getMofDivCode())
                .put("fiscal", basicInfo.getFiscal())
                .build());
        Map<String, MadDepartmentDTO> code$department$rel = departments.stream().collect(Collectors.toMap(MadDepartmentDTO::getDepartmentCode, item -> item, (k1, k2) -> k1));
        paDeptFinanceSettingList = paDeptFinanceSettingList.stream().filter(item ->
                {
                    Integer isEnabled = code$department$rel.getOrDefault(item.getDeptCode(), new MadDepartmentDTO()).getIsEnabled();
                    return Objects.nonNull(isEnabled) && 1 == isEnabled;
                })
                .collect(Collectors.toList());
        return getUserIds(basicInfo, paDeptFinanceSettingList.stream().map(PaDeptFinanceSetting::getFinanceEmpCode).collect(Collectors.toList()));
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
