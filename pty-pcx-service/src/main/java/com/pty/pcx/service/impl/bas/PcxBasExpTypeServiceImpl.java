package com.pty.pcx.service.impl.bas;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pty.pcx.api.bas.IPcxBasExpExpecoService;
import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.common.constant.OperationCNConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.ResponseErrorCode;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dto.mad.MadExpcoDTO;
import com.pty.pcx.entity.bas.PcxBasExpExpeco;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.qo.bas.PcxBasExpExpecoQO;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.PcxBasExpTypeVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class PcxBasExpTypeServiceImpl implements IPcxBasExpTypeService {

    @Autowired
    private PcxBasExpTypeDao pcxBasExpTypeDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private IPcxBasExpExpecoService pcxBasExpExpecoService;

    @Autowired
    private IPcxBasItemExpService pcxBasItemExpService;

    @Override
    public Response<?> selectWithPage(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> valided = isValided(pcxBasExpTypeQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        PageInfo<PcxBasItem> pageInfo = PageHelper.startPage(pcxBasExpTypeQO.getPageIndex(), pcxBasExpTypeQO.getPageSize())
                .doSelectPageInfo(() -> {
                    pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);
                });
        return Response.success(pageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> save(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> valided = isValided(pcxBasExpTypeQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        if(StringUtil.isEmpty(pcxBasExpTypeQO.getExpenseCode()) || StringUtil.isEmpty(pcxBasExpTypeQO.getExpenseName())){
            return Response.fail().setMsg("费用类型编码或名称不能为空");
        }
        // 检查费用类类型/名称是否已存在
        CheckMsg<?> checkMsgExists = checkExpenseTypeExists(pcxBasExpTypeQO);
        if (!checkMsgExists.isSuccess()) {
            return Response.fail().setMsg(checkMsgExists.getMsgInfo());
        }
        CheckMsg<?> modifiedMsg = modifyParentBasExp(pcxBasExpTypeQO);
        if (!modifiedMsg.isSuccess()) {
            return Response.fail().setMsg(modifiedMsg.getMsgInfo());
        }
        // 新增的费用类型默认为非系统
        pcxBasExpTypeQO.setIsSystem(PubConstant.LOGIC_FALSE);
        // 新增的费用类型默认为叶子节点
        pcxBasExpTypeQO.setIsLeaf(PubConstant.LOGIC_TRUE);
        pcxBasExpTypeQO.setId(StringUtil.getUUID());
        pcxBasExpTypeDao.insertSelective(pcxBasExpTypeQO);
        CheckMsg<?> checkMsg = saveOrUpdateExpExpecoReleation(pcxBasExpTypeQO, Boolean.TRUE);
        if(!checkMsg.isSuccess()){
            throw new RuntimeException(checkMsg.getMsgInfo());
        }
        return Response.commonResponse("保存成功");
    }

    /****
     * 当新增费用类型时，修改父级费用类型的isLeaf字段
     * @param pcxBasExpTypeQO
     * @return
     */
    private CheckMsg<?> modifyParentBasExp(PcxBasExpTypeQO pcxBasExpTypeQO) {
        if (StringUtil.isEmpty(pcxBasExpTypeQO.getParentCode())) {
            return CheckMsg.success();
        }
        PcxBasExpTypeQO parentParams = new PcxBasExpTypeQO();
        parentParams.setMofDivCode(pcxBasExpTypeQO.getMofDivCode());
        parentParams.setAgyCode(pcxBasExpTypeQO.getAgyCode());
        parentParams.setFiscal(pcxBasExpTypeQO.getFiscal());
        parentParams.setExpenseCode(pcxBasExpTypeQO.getParentCode());
        parentParams.setTenantId(pcxBasExpTypeQO.getTenantId());
        PcxBasExpType parentExp = pcxBasExpTypeDao.getBaseExpByQO(parentParams);
        if (ObjectUtils.isEmpty(parentExp)) {
            return CheckMsg.fail().setMsgInfo("费用类型的父级编码不存在");
        }
        if (parentExp.getIsLeaf() == PubConstant.LOGIC_TRUE) {
            BeanUtils.copyProperties(parentExp, pcxBasExpTypeQO);
            pcxBasExpTypeQO.setIsLeaf(PubConstant.LOGIC_FALSE);
            pcxBasExpTypeDao.updateByQo(pcxBasExpTypeQO);
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> updateById(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> valided = isValided(pcxBasExpTypeQO);
        if(!valided.isSuccess()) {
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        if(StringUtil.isEmpty(pcxBasExpTypeQO.getId())){
            return Response.fail().setMsg("请选择需要修改的费用类型");
        }
        pcxBasExpTypeQO.setModifier(pcxBasExpTypeQO.getUserCode());
        pcxBasExpTypeQO.setModifiedTime(DateUtil.getCurDate());
        pcxBasExpTypeQO.setModifierName(pcxBasExpTypeQO.getUserName());
        pcxBasExpTypeDao.updateByQo(pcxBasExpTypeQO);
        CheckMsg<?> checkMsg = saveOrUpdateExpExpecoReleation(pcxBasExpTypeQO, Boolean.FALSE);
        if(!checkMsg.isSuccess()){
            throw new RuntimeException(checkMsg.getMsgInfo());
        }
        return Response.commonResponse("修改成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> deleteByQO(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> checkMsg = checkParams(pcxBasExpTypeQO, OperationCNConstant.DELETE);
        if(!checkMsg.isSuccess()){
            return Response.fail().setMsg(checkMsg.getMsgInfo());
        }
        pcxBasExpTypeDao.deleteByQO(pcxBasExpTypeQO);
        deleteBasExpExpecoReleation(pcxBasExpTypeQO);
        return Response.commonResponse("删除成功");
    }

    @Override
    public Response<?> selectById(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> valided = isValided(pcxBasExpTypeQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        if(StringUtil.isEmpty(pcxBasExpTypeQO.getId())){
            return Response.fail().setMsg("请选择需要查询的费用类型");
        }
        PcxBasExpType pcxBasExpType = pcxBasExpTypeDao.getBaseExpByQO(pcxBasExpTypeQO);
        if(ObjectUtils.isEmpty(pcxBasExpType)){
            return Response.fail().setMsg("费用类型不存在");
        }
        PcxBasExpTypeVO pcxBasExpTypeVO = new PcxBasExpTypeVO();
        BeanUtils.copyProperties(pcxBasExpType,pcxBasExpTypeVO);
        //填充部门经济分类的信息
        assembleMadExpecoCodes(pcxBasExpTypeVO);
        return Response.success(pcxBasExpTypeVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> disEnableOrEnable(PcxBasExpTypeQO pcxBasExpTypeQO, Integer isEnable) {
        String action = PubConstant.LOGIC_TRUE==isEnable  ? OperationCNConstant.ENABLE : OperationCNConstant.DISABLE;
        CheckMsg<?> checkFlag = checkParams(pcxBasExpTypeQO,action);
        if(!checkFlag.isSuccess()){
            return Response.fail().setMsg(checkFlag.getMsgInfo());
        }
        PcxBasExpType basExpType = pcxBasExpTypeDao.getBaseExpByQO(pcxBasExpTypeQO);
        if(ObjectUtils.isEmpty(basExpType)){
            return Response.fail().setMsg("费用类型不存在");
        }
        List<PcxBasExpType> childList = getChildList(basExpType);
        // 如果为停用操作,需要有一层交互确认
        if(PubConstant.LOGIC_FALSE == isEnable){
            CheckMsg<?> isNeedConfirm = isNeedConfirm(pcxBasExpTypeQO, childList, action);
            if (!isNeedConfirm.isSuccess()) {
                return Response.fail().setCode(ResponseErrorCode.STATUS_BIZ_NEED_CONFIRM).setMsg(isNeedConfirm.getMsgInfo());
            }
        }
        //        if (PubConstant.LOGIC_FALSE == isEnable){
        // 如果为停用操作，判断当前费用类型是否有关联数据，如果有则不能停用
        // 下级数据也需要判断是否有关联数据，如果有则不能停用
//            List<PcxBasExpType> relatedData = pcxBasExpTypeDao.selectRelatedData(pcxBasExpTypeQO);
//            if (CollectionUtil.isNotEmpty(relatedData)) {
//                return Response.fail().setMsg("当前费用类型有关联数据，不能停用");
//            }
//        }
        List<PcxBasExpType> updateData = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(childList)){
            updateData.addAll(childList);
        }
        // 当是启用时，启用下级需要把上级启用了
        if(PubConstant.LOGIC_TRUE==isEnable && StringUtil.isNotEmpty(basExpType.getParentCode())){
            PcxBasExpTypeQO parentQO = new PcxBasExpTypeQO();
            parentQO.setExpenseCode(basExpType.getParentCode());
            parentQO.setMofDivCode(basExpType.getMofDivCode());
            parentQO.setAgyCode(basExpType.getAgyCode());
            parentQO.setFiscal(basExpType.getFiscal());
            parentQO.setTenantId(basExpType.getTenantId());
            PcxBasExpType parentExp = pcxBasExpTypeDao.getBaseExpByQO(parentQO);
            updateData.add(parentExp);
        }
        updateData.add(pcxBasExpTypeQO);
        updateData.forEach(item->{
            item.setIsEnabled(isEnable);
            item.setModifier(pcxBasExpTypeQO.getUserCode());
            item.setModifiedTime(DateUtil.getCurDate());
            item.setModifierName(pcxBasExpTypeQO.getUserName());
        });
        batchServiceUtil.batchProcess(updateData,PcxBasExpTypeDao.class,PcxBasExpTypeDao::disEnableOrEnableById);
        return Response.commonResponse(String.format("费用类型%s成功",action));
    }

    private List<PcxBasExpType> getChildList(PcxBasExpType basExpType) {
        PcxBasExpTypeQO param = new PcxBasExpTypeQO();
        param.setParentCode(basExpType.getExpenseCode());
        param.setMofDivCode(basExpType.getMofDivCode());
        param.setAgyCode(basExpType.getAgyCode());
        param.setFiscal(basExpType.getFiscal());
        param.setTenantId(basExpType.getTenantId());
        param.setIsRefine(PubConstant.LOGIC_FALSE);
        return pcxBasExpTypeDao.selectSimpleList(param);
    }

    private CheckMsg<?> isNeedConfirm(PcxBasExpTypeQO pcxBasExpTypeQO, List<PcxBasExpType> pcxBasExpTypeList, String action) {
        if (ObjectUtils.isEmpty(pcxBasExpTypeQO.getIsConfirm()) ||
                (PubConstant.LOGIC_FALSE == pcxBasExpTypeQO.getIsConfirm() && CollectionUtil.isNotEmpty(pcxBasExpTypeList))) {
            return CheckMsg.fail().setErrorCode(CheckMsg.STATUS_BIZ_NEED_CONFIRM).setMsgInfo(String.format("此操作会%s当前费用类型的下级数据，是否同意", action));
        }
        return CheckMsg.success();
    }

    @Override
    public CheckMsg<?> getAll(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> valided = isValided(pcxBasExpTypeQO);
        if(!valided.isSuccess()){
            return valided;
        }
        List<PcxBasExpTypeVO> pcxBasExpTypes = pcxBasExpTypeDao.getTreeData(pcxBasExpTypeQO);
        return CheckMsg.success().setData(pcxBasExpTypes).setMsgInfo("查询成功");
    }

    @Override
    public Response<?> getExpTypeDetail(PcxBasExpTypeQO pcxBasExpTypeQO) {
        CheckMsg<?> valided = isValided(pcxBasExpTypeQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        if(StringUtil.isEmpty(pcxBasExpTypeQO.getLastCode())){
            return Response.fail().setMsg("请选择需要查询费用明细的费用类型");
        }
        PcxBasExpTypeQO param = new PcxBasExpTypeQO();
        param.setMofDivCode(pcxBasExpTypeQO.getMofDivCode());
        param.setAgyCode(pcxBasExpTypeQO.getAgyCode());
        param.setFiscal(pcxBasExpTypeQO.getFiscal());
        param.setTenantId(pcxBasExpTypeQO.getTenantId());
        param.setExpenseCode(pcxBasExpTypeQO.getLastCode());
        PcxBasExpType pcxBasExpType = pcxBasExpTypeDao.getBaseExpByQO(param);
        if(ObjectUtils.isEmpty(pcxBasExpType)){
            return Response.fail().setMsg("费用类型不存在");
        }
        // 查询出明细字段
        pcxBasExpTypeQO.setIsRefine(PubConstant.LOGIC_TRUE);
        List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);
        pcxBasExpTypes.add(pcxBasExpType);
        return Response.success(pcxBasExpTypes);
    }

    @Override
    public List<PcxBasExpType> selectByQO(PcxBasExpTypeQO pcxBasExpTypeQO) {
        if(ObjectUtils.isEmpty(pcxBasExpTypeQO)){
            return new ArrayList<>();
        }
        return pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);
    }

    @Override
    public List<BaseDataVo> queryExpense(PcxBasExpTypeQO pcxBasExpTypeQO) {
        List<PcxBasExpTypeVO> pcxBasExpTypes = pcxBasExpTypeDao.getTreeData(pcxBasExpTypeQO);
        List<BaseDataVo> result = new ArrayList<>();
        pcxBasExpTypes.forEach(item->{
            BaseDataVo baseDataVo = new BaseDataVo();
            baseDataVo.setCode(item.getExpenseCode());
            baseDataVo.setName(item.getExpenseName());
            baseDataVo.setPcode(item.getParentCode());
            baseDataVo.setDisabled(item.getIsEnabled() == 1?0:1);
            result.add(baseDataVo);
        });
        return result;
    }

    @Override
    public Integer getEnbaleApplyBill(PcxMadBaseQO qo) {
        PcxBasExpTypeQO param = new PcxBasExpTypeQO();
        param.setFiscal(qo.getFiscal());
        param.setMofDivCode(qo.getMofDivCode());
        param.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        param.setAgyCode(qo.getAgyCode());
        param.setIsNeedApply(PubConstant.LOGIC_TRUE);
        List<PcxBasExpType> pcxBasExpTypes = this.selectByQO(param);
        if (CollectionUtils.isEmpty(pcxBasExpTypes)) {
            return PubConstant.LOGIC_FALSE; // 没有申请
        }
        List<String> expenseList = pcxBasExpTypes.stream().map(PcxBasExpType::getExpenseCode).collect(Collectors.toList());
        // 查询关联关系表
        PcxBasItemExpQO itemExpQO = new PcxBasItemExpQO();
        itemExpQO.setFiscal(qo.getFiscal());
        itemExpQO.setMofDivCode(qo.getMofDivCode());
        itemExpQO.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        itemExpQO.setAgyCode(qo.getAgyCode());
        itemExpQO.setExpenseCodes(expenseList);
        List<PcxBasItemExp> pcxBasItemExps = pcxBasItemExpService.selectByQO(itemExpQO);
        if (CollectionUtils.isEmpty(pcxBasItemExps)) {
            return PubConstant.LOGIC_FALSE;// 没有申请
        }
        return PubConstant.LOGIC_TRUE;
    }

    @Override
    public List<PcxBasExpType> selectByItemCode(PcxBasExpTypeQO param) {
        if(ObjectUtils.isEmpty(param)){
            return new ArrayList<>();
        }
        PcxBasItemExpQO pcxBasItemExpQO = new PcxBasItemExpQO();
        pcxBasItemExpQO.setMofDivCode(param.getMofDivCode());
        pcxBasItemExpQO.setFiscal(param.getFiscal());
        pcxBasItemExpQO.setAgyCode(param.getAgyCode());
        pcxBasItemExpQO.setItemCode(param.getItemCode());
        List<PcxBasItemExp> pcxBasItemExps = pcxBasItemExpService.selectByQO(pcxBasItemExpQO);
        if (CollectionUtil.isEmpty(pcxBasItemExps)) {
            return new ArrayList<>();
        }
        List<String> expenseList = pcxBasItemExps.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.toList());
        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        pcxBasExpTypeQO.setMofDivCode(param.getMofDivCode());
        pcxBasExpTypeQO.setAgyCode(param.getAgyCode());
        pcxBasExpTypeQO.setFiscal(param.getFiscal());
        pcxBasExpTypeQO.setTenantId(StringUtil.isEmpty(param.getTenantId()) ? PtyContext.getTenantId() : param.getTenantId());
        pcxBasExpTypeQO.setExpTypeCodes(expenseList);
        return this.selectByQO(pcxBasExpTypeQO);
    }

    @Override
    public Map<Integer, String> getExpDetailLevel(PcxBasExpTypeQO qo) {
        Map<Integer, String> expDetailLevel = new HashMap<>();
        if(ObjectUtils.isEmpty(qo)){
            return expDetailLevel;
        }
        PcxBasExpTypeQO params = new PcxBasExpTypeQO();
        params.setMofDivCode(qo.getMofDivCode());
        params.setAgyCode(qo.getAgyCode());
        params.setFiscal(qo.getFiscal());
        params.setIsRefine(PubConstant.LOGIC_TRUE);
        params.setLastCode(qo.getExpenseCode());
        List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectSimpleList(params);
        if(CollectionUtil.isEmpty(pcxBasExpTypes)){
            return expDetailLevel;
        }
        List<PcxBasExpType> assembleExpList = ExpenseBeanUtil.getAssembleExpList(pcxBasExpTypes);
        Map<Integer, String> result = assembleExpList.stream()
                .collect(Collectors.toMap(
                        PcxBasExpType::getDetailLevel,
                        PcxBasExpType::getExpenseName,
                        (existing, replacement) -> existing + "," + replacement
                ));
        //如果三级不为空，需要把其他二级的子数据为空 的加到三级中
        specialHandleLevelThree(assembleExpList,result);
        //查询一级费用名称
        params = new PcxBasExpTypeQO();
        params.setMofDivCode(qo.getMofDivCode());
        params.setAgyCode(qo.getAgyCode());
        params.setFiscal(qo.getFiscal());
        params.setIsRefine(PubConstant.LOGIC_FALSE);
        params.setExpenseCode(qo.getExpenseCode());
        List<PcxBasExpType> pcxBasExpTypeLevelFirst = pcxBasExpTypeDao.selectSimpleList(params);
        if(CollectionUtil.isNotEmpty(pcxBasExpTypeLevelFirst)){
            result.put(1,pcxBasExpTypeLevelFirst.get(0).getExpenseName());
        }
        result.put(0,"无费用申请");
        appendExpLevelName(result);
        return result;
    }

    private void specialHandleLevelThree(List<PcxBasExpType> assembleExpList,Map<Integer, String> result){
        if(!result.containsKey(3)){
            return;
        }
        // 提取所有 parentCode，用于后续判断是否被引用
        Set<String> referencedParentCodes = assembleExpList.stream()
                .map(PcxBasExpType::getParentCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 找出 detailLevel == 2 并且 expenseCode 不在 referencedParentCodes 中的项（即没有子节点）
        List<PcxBasExpType> unReferencedLevel2Items = assembleExpList.stream()
                .filter(exp -> exp.getDetailLevel() == 2)
                .filter(exp -> !referencedParentCodes.contains(exp.getExpenseCode()))
                .collect(Collectors.toList());

        // 这里加到result中key=2的value中
        if (!unReferencedLevel2Items.isEmpty()) {
            String combinedNames = unReferencedLevel2Items.stream()
                    .map(PcxBasExpType::getExpenseName)
                    .distinct()
                    .collect(Collectors.joining(","));

                String existingValue = result.get(3);
                result.put(3, existingValue + "," + combinedNames);
        }
    }


    private void appendExpLevelName(Map<Integer, String> result){
        StringBuilder nameBuilder;
        for (Integer level : result.keySet()) {
            nameBuilder = new StringBuilder();
            switch (level) {
                case 1:
                    nameBuilder.append("一级费用申请（").append(result.get(level)).append("）");
                    break;
                case 2:
                    nameBuilder.append("二级费用申请（").append(result.get(level)).append("）");
                    break;
                case 3:
                    nameBuilder.append("三级费用申请（").append(result.get(level)).append("）");
                    break;
                default:
                    continue;
             }
            result.put(level, nameBuilder.toString());
        }
    }

    /*****
     * 校验费用类型code或name是否存在
     * 逻辑：code或者name任意一个，在同一单位、区划、年度、租户下不能重复；
     * @param pcxBasExpTypeQO
     * @return
     */
    private CheckMsg<?> checkExpenseTypeExists(PcxBasExpTypeQO pcxBasExpTypeQO) {
        PcxBasExpTypeQO params = new PcxBasExpTypeQO();
        params.setMofDivCode(pcxBasExpTypeQO.getMofDivCode());
        params.setAgyCode(pcxBasExpTypeQO.getAgyCode());
        params.setFiscal(pcxBasExpTypeQO.getFiscal());
        params.setTenantId(pcxBasExpTypeQO.getTenantId());
        List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectSimpleList(params);
        if (CollectionUtil.isNotEmpty(pcxBasExpTypes)) {
            boolean exists = pcxBasExpTypes.stream()
                    .anyMatch(item -> item.getExpenseCode().equals(pcxBasExpTypeQO.getExpenseCode()) ||
                            item.getExpenseName().equals(pcxBasExpTypeQO.getExpenseName()));
            if (exists) {
                return CheckMsg.fail().setMsgInfo("费用类型编码或名称已存在");
            }
        }
        return CheckMsg.success();
    }

    private CheckMsg<?> saveOrUpdateExpExpecoReleation(PcxBasExpTypeQO qo, Boolean isSave) {
        CheckMsg<?> checkMsg = CheckMsg.success();
        List<PcxBasExpExpecoQO>  pcxBasExpExpecoQOList = new ArrayList<>();
        for (MadExpcoDTO madExpcoDTO : qo.getMadExpcoDTOList()) {
            PcxBasExpExpecoQO pcxBasExpExpecoQO = createPcxBasExpExpecoQO(qo, madExpcoDTO);
            pcxBasExpExpecoQOList.add(pcxBasExpExpecoQO);
        }
        if(isSave){
            if (CollectionUtil.isNotEmpty(pcxBasExpExpecoQOList)){
                checkMsg = pcxBasExpExpecoService.batchSave(pcxBasExpExpecoQOList);
            }
        }else{
            checkMsg = updateBasExpExpecoReleation(qo, pcxBasExpExpecoQOList);
        }
        checkMsg.setMsgInfo(String.format("%s成功" ,isSave ? OperationCNConstant.SAVE : OperationCNConstant.UPDATE));
        return checkMsg;
    }

    private PcxBasExpExpecoQO createPcxBasExpExpecoQO(PcxBasExpTypeQO qo, MadExpcoDTO madExpcoDTO) {
        PcxBasExpExpecoQO pcxBasExpExpecoQO = new PcxBasExpExpecoQO();
        pcxBasExpExpecoQO.setAgyCode(qo.getAgyCode());
        pcxBasExpExpecoQO.setMofDivCode(qo.getMofDivCode());
        pcxBasExpExpecoQO.setFiscal(qo.getFiscal());
        pcxBasExpExpecoQO.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        pcxBasExpExpecoQO.setExpenseCode(qo.getExpenseCode());
        pcxBasExpExpecoQO.setExpenseName(qo.getExpenseName());
        pcxBasExpExpecoQO.setExpecoCode(madExpcoDTO.getExpcoCode());
        pcxBasExpExpecoQO.setExpecoName(madExpcoDTO.getExpcoName());
        pcxBasExpExpecoQO.setId(StringUtil.getUUID());
        return pcxBasExpExpecoQO;
    }

    private void assembleMadExpecoCodes(PcxBasExpTypeVO pcxBasExpTypeVO) {
        PcxBasExpExpecoQO pcxBasExpExpecoQO = new PcxBasExpExpecoQO();
        pcxBasExpExpecoQO.setMofDivCode(pcxBasExpTypeVO.getMofDivCode());
        pcxBasExpExpecoQO.setAgyCode(pcxBasExpTypeVO.getAgyCode());
        pcxBasExpExpecoQO.setFiscal(pcxBasExpTypeVO.getFiscal());
        pcxBasExpExpecoQO.setTenantId(pcxBasExpTypeVO.getTenantId());
        pcxBasExpExpecoQO.setExpenseCode(pcxBasExpTypeVO.getExpenseCode());
        List<PcxBasExpExpeco> pcxBasExpExpecos = pcxBasExpExpecoService.selectByExpenseCode(pcxBasExpExpecoQO);
        if (CollectionUtil.isNotEmpty(pcxBasExpExpecos)) {
            List<String> madExpecoCodes = pcxBasExpExpecos.stream().map(PcxBasExpExpeco::getExpecoCode).collect(Collectors.toList());
            pcxBasExpTypeVO.setMdaExpcoCodes(madExpecoCodes);
        }
    }

    private CheckMsg<?> updateBasExpExpecoReleation(PcxBasExpTypeQO qo, List<PcxBasExpExpecoQO> pcxBasExpExpecoQOList) {
        CheckMsg<?> checkMsg =deleteBasExpExpecoReleation(qo);
        if (checkMsg.isSuccess()) {
            if (CollectionUtil.isNotEmpty(pcxBasExpExpecoQOList)){
                checkMsg = pcxBasExpExpecoService.batchSave(pcxBasExpExpecoQOList);
            }
        } else {
            throw new RuntimeException(checkMsg.getMsgInfo());
        }
        return checkMsg;
    }

    private CheckMsg<?> deleteBasExpExpecoReleation(PcxBasExpTypeQO qo) {
        PcxBasExpExpecoQO deleteParam = new PcxBasExpExpecoQO();
        deleteParam.setExpenseCode(qo.getExpenseCode());
        deleteParam.setMofDivCode(qo.getMofDivCode());
        deleteParam.setAgyCode(qo.getAgyCode());
        deleteParam.setFiscal(qo.getFiscal());
        deleteParam.setTenantId(qo.getTenantId());
        return pcxBasExpExpecoService.deleteByExpense(deleteParam);
    }

    private CheckMsg<?> isValided(PcxBasExpTypeQO qo) {
        if(StringUtil.isEmpty(qo.getAgyCode())){
            return CheckMsg.fail("单位编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getMofDivCode())){
            return CheckMsg.fail("区划编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getFiscal())){
            return CheckMsg.fail("年度不能为空");
        }
        return CheckMsg.success();
    }

    private CheckMsg<?> checkParams(PcxBasExpTypeQO qo,String action) {
        if(StringUtil.isEmpty(qo.getId())){
            return CheckMsg.fail(String.format("请勾选需要%s的数据",action));
        }
        CheckMsg<?> valided = isValided(qo);
        if(!valided.isSuccess()){
            return valided;
        }
        return CheckMsg.success();
    }
}
