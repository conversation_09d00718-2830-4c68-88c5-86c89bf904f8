package com.pty.pcx.service.impl.calculationrule;

import com.pty.pcx.api.calculationrule.ParamEncapsuleStrategy;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.dto.calculationrule.CalculationRuleDTO;
import com.pty.pcx.dto.calculationrule.MealAllowanceDTO;
import com.pty.pcx.dto.ecs.StandMatchSubDTO;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.pcx.service.impl.ecs.StandMatchProcessor;
import com.pty.pcx.service.impl.ecs.handler.StandMatchAbstractHandler;
import com.pty.pub.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 伙食补助参数封装
 */
@Service
@Indexed
public class ParamEncapsule3021103Strategy extends StandMatchAbstractHandler implements ParamEncapsuleStrategy {

    @Autowired
    private StandMatchProcessor standMatchProcessor;

    /**
     * 封装伙食补助的补贴金额
     * @param calculationRuleDTO
     */
    @Override
    public PcxBillExpStandResult encapsulateParameters(CalculationRuleDTO calculationRuleDTO) {
        MealAllowanceDTO mealAllowanceDTO = calculationRuleDTO.getMealAllowanceDTO();
        if (mealAllowanceDTO != null) {
            String departureProvince = mealAllowanceDTO.getDepartureProvince();
            String workplaceProvince = mealAllowanceDTO.getWorkplaceProvince();
            StandMatchSubDTO standMatchSubDTO = new StandMatchSubDTO();
            standMatchSubDTO.setExpenseCode(calculationRuleDTO.getExpenseType());
            standMatchSubDTO.setFiscal(calculationRuleDTO.getFiscal());
            standMatchSubDTO.setAgyCode(calculationRuleDTO.getAgyCode());
            standMatchSubDTO.setMofDivCode(calculationRuleDTO.getMofDivCode());
            List<String> factory = new ArrayList<>();
            factory.add(PcxConstant.STANDARD_FIELD_CODE);
            //标准要素为字符串1
            factory.add("1");
            factory.add(StringUtil.isEmpty(departureProvince) || departureProvince.equals(workplaceProvince) ? PcxConstant.IN_PROVINCE : PcxConstant.OUT_PROVINCE);
            //费用控制级别
            if (StringUtil.isNotEmpty(mealAllowanceDTO.getBudLevel())){
                factory.addAll(Arrays.asList(mealAllowanceDTO.getBudLevel().split(",")));
            }
            //职级
            if (StringUtil.isNotEmpty(mealAllowanceDTO.getJobLevel())){
                factory.add(mealAllowanceDTO.getJobLevel());
            }
            factory.add(mealAllowanceDTO.getStandardCode());
            factory.add(mealAllowanceDTO.getDepartureCity());

            // 2. 城市分类
            PcxBasCityClassify cityClassify = getCityClassifyExpense(mealAllowanceDTO.getDepartureCity(), calculationRuleDTO.getAgyCode(), calculationRuleDTO.getFiscal(),
                    calculationRuleDTO.getMofDivCode(),PcxConstant.TRAVEL_DETAIL_3021103);
            factory.add(cityClassify != null ? cityClassify.getClassifyCode() : "other");
            List<String> condition = new ArrayList<>();
            //增加部门条件
            if (StringUtil.isNotBlank(mealAllowanceDTO.getDepartmentCode())){
                condition.add(mealAllowanceDTO.getDepartmentCode());
            }
            //职级条件
            if (StringUtil.isNotBlank(mealAllowanceDTO.getJobLevel())){
                condition.add(mealAllowanceDTO.getJobLevel());
            }
            condition.add(StringUtil.isEmpty(departureProvince) || departureProvince.equals(workplaceProvince) ? PcxConstant.IN_PROVINCE : PcxConstant.OUT_PROVINCE);

            standMatchSubDTO.setFactory(factory);
            standMatchSubDTO.setCondition(condition);
            return standMatchProcessor.matchByFactory(standMatchSubDTO);
        }
        return null;
    }
}
