package com.pty.pcx.service.impl.transfer;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.mad.entity.MadCurrent;
import com.pty.mad.entity.MadEmployee;
import com.pty.mad.entity.MadExtData;
import com.pty.mad.qo.MadExtDataQO;
import com.pty.pcx.dao.bill.PcxBillAmtApportionDepartmentDao;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dao.bill.PcxBillSettlementInfoDao;
import com.pty.pcx.dao.treasurypay.detail.PcxBillPayDetailDao;
import com.pty.pcx.dto.transfer.AllocationItemDTO;
import com.pty.pcx.dto.transfer.ExtendExpenseDetailDTO;
import com.pty.pcx.dto.transfer.ExtendTravelExpenseDetailDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillAmtApportionDepartment;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.util.BillAllocationUtil;
import com.pty.pub.common.rest.RestClientReference;
import org.apache.commons.lang3.StringUtils;
import org.pty.mad.api.IMadCurrentService;
import org.pty.mad.api.IMadEmployeeService;
import org.pty.mad.api.IMadExtDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class AbstractReimbursementService {

    @Autowired
    private PcxBillAmtApportionDepartmentDao pcxBillAmtApportionDepartmentDao;

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    protected IMadEmployeeService madEmployeeService;

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IMadExtDataService madExtDataService;

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IMadCurrentService madCurrentService;

    @Autowired
    private PcxBillDao pcxBillDao;

    @Autowired
    private PcxBillPayDetailDao pcxBillPayDetailDao;

    @Autowired
    private PcxBillSettlementInfoDao pcxBillSettlementInfoDao;

    protected Map<String, List<PcxBillAmtApportionDepartment>> getPcxBillAmtApportionDepartmentsMap(List<String> billIds) {
        List<PcxBillAmtApportionDepartment> pcxBillAmtApportionDepartments = pcxBillAmtApportionDepartmentDao.selectList(
                Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
                        .in(PcxBillAmtApportionDepartment::getBillId, billIds)
        );
        return pcxBillAmtApportionDepartments.stream().collect(Collectors.groupingBy(PcxBillAmtApportionDepartment::getBillId));
    }

    protected Map<String, MadEmployee> getMadEmployeeMap(List<String> claimantCodes) {
        MadEmployee madEmployee = new MadEmployee();
        madEmployee.setMadCodes(claimantCodes);
        List<MadEmployee> madEmployees = madEmployeeService.select(madEmployee);
        return madEmployees.stream().collect(Collectors.toMap(MadEmployee::getMadCode, Function.identity(), (oldVal, newVal) -> newVal));
    }

    protected MadExtData selectCurrentOrg(String orgId) {
        MadExtDataQO madExtDataQO = new MadExtDataQO();
        madExtDataQO.setIsEnabled(1);
        madExtDataQO.setAtomCode("KINGDEE_ERP_ORGANIZATION");
        madExtDataQO.setField01(orgId);
        List<MadExtData> madExtDataList = madExtDataService.selectByErpIds(madExtDataQO);
        return getCurrentExtData(madExtDataList);
    }

    private MadExtData selectCurrentEmployee(String employeeCode) {
        MadExtDataQO madExtDataQO = new MadExtDataQO();
        madExtDataQO.setIsEnabled(1);
        madExtDataQO.setAtomCode("KINGDEE_ERP_EMPLOYEE");
        madExtDataQO.setMadCode(employeeCode);
        List<MadExtData> madExtDataList = madExtDataService.selectByErpIds(madExtDataQO);
        return getCurrentExtData(madExtDataList);
    }

    protected String selectFirstDepartmentCode(MadExtData department, String departmentCode) {
        if (department != null) {
            MadExtData madExtData = selectCurrentDepartmentByDepartmentId(department.getField02(), department.getAgyCode());
            if (madExtData != null) {
                return madExtData.getMadCode();
            }
        }
        String[] departmentCodes = StringUtils.split(departmentCode, ".");
        if (departmentCodes.length <= 2) {
            return departmentCode;
        }
        return departmentCodes[0] + "." + departmentCodes[1];
    }

    protected MadExtData selectCurrentDepartment(String agyCode, String departmentCode, String userCode) {
        MadExtData currentDepartment = selectCurrentDepartmentByDepartmentCode(departmentCode, agyCode);
        if (currentDepartment == null && userCode != null) {
            MadExtData currentEmployee = selectCurrentEmployee(userCode);
            if (currentEmployee == null) {
                return null;
            }
            return selectCurrentDepartmentByDepartmentId(currentEmployee.getField02(), agyCode);
        }
        return currentDepartment;
    }

    protected MadExtData selectCurrentDepartment(String agyCode, String departmentCode) {
        return selectCurrentDepartment(agyCode, departmentCode, null);
    }

    private MadExtData getCurrentExtData(List<MadExtData> extDataList) {
        if (extDataList.isEmpty()) {
            return null;
        }
        return extDataList.get(0);
    }

    private MadExtData selectCurrentDepartmentByDepartmentId(String departmentCode, String agyCode) {
        MadExtDataQO madExtDataQO = new MadExtDataQO();
        madExtDataQO.setIsEnabled(1);
        madExtDataQO.setAtomCode("KINGDEE_ERP_DEPARTMENT");
        madExtDataQO.setField01(departmentCode);
        madExtDataQO.setAgyCode(agyCode);
        List<MadExtData> madExtDataList = madExtDataService.selectByErpIds(madExtDataQO);
        return getCurrentExtData(madExtDataList);
    }

    private MadExtData selectCurrentDepartmentByDepartmentCode(String departmentCode, String agyCode) {
        MadExtDataQO madExtDataQO = new MadExtDataQO();
        madExtDataQO.setIsEnabled(1);
        madExtDataQO.setAtomCode("KINGDEE_ERP_DEPARTMENT");
        madExtDataQO.setLikeCommaField04(departmentCode);
        madExtDataQO.setAgyCode(agyCode);
        List<MadExtData> madExtDataList = madExtDataService.selectByErpIds(madExtDataQO);
        return getCurrentExtData(madExtDataList);
    }

    protected String getInvoiceNo(String ecsBillNo) {
        String[] ecsBillNos = ecsBillNo.split("#");
        return ecsBillNos.length == 2 ? ecsBillNos[0] : ecsBillNo;
    }

    @Deprecated
    protected void updatePayStatus(PcxBill pcxBill) {
        pcxBill.setSettlementAmt(pcxBill.getCheckAmt());
        pcxBill.setPayTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        pcxBill.setPayStatus("2");
        pcxBillDao.updateById(pcxBill);
    }

    protected List<AllocationItemDTO> getAllocationItems(List<ExtendExpenseDetailDTO> expenseDetailDTOS,
                                                         List<PcxBillAmtApportionDepartment> pcxBillAmtApportionDepartments,
                                                         MadExtData currentDepartment) {
        List<BillAllocationUtil.Bill> utilBills = expenseDetailDTOS
                .stream()
                .map(travelExpenseDetailDTO -> {
                    BigDecimal checkExpMoney = new BigDecimal(travelExpenseDetailDTO.getCheckExpMoney());
                    long checkAmount = BigDecimal.valueOf(100).multiply(checkExpMoney).longValue();

                    BigDecimal applyExpMoney = travelExpenseDetailDTO.getApplyExpMoney();
                    long applyAmount = BigDecimal.valueOf(100).multiply(applyExpMoney).longValue();

                    return new BillAllocationUtil.Bill(travelExpenseDetailDTO.getDetailId(), checkAmount, applyAmount);
                })
                .collect(Collectors.toList());

        List<BillAllocationUtil.Department> utilDepartments = new ArrayList<>();
        Map<String, MadExtData> departmenMap = new HashMap<>();
        for (PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment : pcxBillAmtApportionDepartments) {
            MadExtData department = selectCurrentDepartment(pcxBillAmtApportionDepartment.getAgyCode(), pcxBillAmtApportionDepartment.getDepartmentCode());
            if (department == null) {
                department = currentDepartment;
            }
            String departmentCode = department.getMadCode();

            long requiredAmount = BigDecimal.valueOf(100).multiply(pcxBillAmtApportionDepartment.getDepartmentAmt()).longValue();
            long inputAmount = BigDecimal.valueOf(100).multiply(pcxBillAmtApportionDepartment.getInputAmt()).longValue();

            utilDepartments.add(new BillAllocationUtil.Department(departmentCode, departmentCode, requiredAmount, inputAmount));

            departmenMap.put(departmentCode, department);
        }

        List<BillAllocationUtil.AllocationItem> allocationItems = BillAllocationUtil.allocateBills(utilBills, utilDepartments);

        return convertToAllocationItemDTO(allocationItems, departmenMap);
    }

    protected List<AllocationItemDTO> getTravelAllocationItems(List<ExtendTravelExpenseDetailDTO> expenseDetailDTOS,
                                                               List<PcxBillAmtApportionDepartment> pcxBillAmtApportionDepartments,
                                                               MadExtData currentDepartment) {
        List<BillAllocationUtil.Bill> utilBills = expenseDetailDTOS
                .stream()
                .map(travelExpenseDetailDTO -> {
                    BigDecimal checkExpMoney = new BigDecimal(travelExpenseDetailDTO.getCheckExpMoney());
                    long checkAmount = BigDecimal.valueOf(100).multiply(checkExpMoney).longValue();

                    BigDecimal applyExpMoney = travelExpenseDetailDTO.getApplyExpMoney();
                    long applyAmount = BigDecimal.valueOf(100).multiply(applyExpMoney).longValue();

                    return new BillAllocationUtil.Bill(travelExpenseDetailDTO.getDetailId(), checkAmount, applyAmount);
                })
                .collect(Collectors.toList());

        List<BillAllocationUtil.Department> utilDepartments = new ArrayList<>();
        Map<String, MadExtData> departmenMap = new HashMap<>();
        for (PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment : pcxBillAmtApportionDepartments) {
            MadExtData department = selectCurrentDepartment(pcxBillAmtApportionDepartment.getAgyCode(), pcxBillAmtApportionDepartment.getDepartmentCode());
            if (department == null) {
                department = currentDepartment;
            }
            String departmentCode = department.getMadCode();

            long requiredAmount = BigDecimal.valueOf(100).multiply(pcxBillAmtApportionDepartment.getDepartmentAmt()).longValue();
            long inputAmount = BigDecimal.valueOf(100).multiply(pcxBillAmtApportionDepartment.getInputAmt()).longValue();

            utilDepartments.add(new BillAllocationUtil.Department(departmentCode, departmentCode, requiredAmount, inputAmount));

            departmenMap.put(departmentCode, department);
        }

        List<BillAllocationUtil.AllocationItem> allocationItems = BillAllocationUtil.allocateBills(utilBills, utilDepartments);

        return convertToAllocationItemDTO(allocationItems, departmenMap);
    }

    private List<AllocationItemDTO> convertToAllocationItemDTO(List<BillAllocationUtil.AllocationItem> allocationItems,
                                                               Map<String, MadExtData> departmenMap) {
        return allocationItems
                .stream()
                .map(allocationItem -> {
                    AllocationItemDTO allocationItemDTO = new AllocationItemDTO();
                    BeanUtils.copyProperties(allocationItem, allocationItemDTO);

                    MadExtData madExtData = departmenMap.get(allocationItem.getDepartmentId());
                    allocationItemDTO.setDepartment(madExtData);
                    return allocationItemDTO;
                })
                .collect(Collectors.toList());
    }

    @Deprecated
    protected MadCurrent selectMadCurrent(PcxBill pcxBill, PcxBillPayDetail pcxBillPayDetail) {
        MadCurrent madCurrent = new MadCurrent();
        madCurrent.setMadFname(pcxBillPayDetail.getPayeeAccountName());
        madCurrent.setMofDivCode("87");
        madCurrent.setFiscal(LocalDate.now().getYear());
        madCurrent.setAgyCode(pcxBill.getAgyCode());
        List<MadCurrent> madCurrents = madCurrentService.select(madCurrent);
        if (madCurrents.isEmpty()) {
            return null;
        }
        return madCurrents.get(0);
    }

    protected MadCurrent selectMadCurrent(PcxBill pcxBill, String payeeAccName) {
        MadCurrent madCurrent = new MadCurrent();
        madCurrent.setMadFname(payeeAccName);
        madCurrent.setMofDivCode("87");
        madCurrent.setFiscal(LocalDate.now().getYear());
        madCurrent.setAgyCode(pcxBill.getAgyCode());
        List<MadCurrent> madCurrents = madCurrentService.select(madCurrent);
        if (madCurrents.isEmpty()) {
            return null;
        }
        return madCurrents.get(0);
    }

    /**
     * 查询单据结算信息
     */
    protected PcxBillSettlement selectPcxBillSettlement(String billId) {
        List<PcxBillSettlement> pcxBillSettlements = pcxBillSettlementInfoDao.selectList(
                Wrappers.<PcxBillSettlement>lambdaQuery()
                        .eq(PcxBillSettlement::getBillId, billId)
        );
        if (pcxBillSettlements.isEmpty()) {
            return null;
        }
        return pcxBillSettlements.get(0);
    }

    /**
     * 查询支付明细
     */
    protected PcxBillPayDetail selectPcxBillPayDetail(String billId) {
        List<PcxBillPayDetail> pcxBillPayDetails = pcxBillPayDetailDao.selectList(
                Wrappers.<PcxBillPayDetail>lambdaQuery()
                        .eq(PcxBillPayDetail::getBillId, billId)
                        .ne(PcxBillPayDetail::getCheckAmt, BigDecimal.ZERO)
        );
        if (pcxBillPayDetails.isEmpty()) {
            return null;
        }
        return pcxBillPayDetails.get(0);
    }

    protected void subtractAmount(List<PcxBillAmtApportionDepartment> pcxBillAmtApportionDepartments,
                                  BigDecimal filterInputAmt) {
        BigDecimal remainingAmount = filterInputAmt;

        for (PcxBillAmtApportionDepartment department : pcxBillAmtApportionDepartments) {
            BigDecimal currentInputAmt = department.getInputAmt();

            if (currentInputAmt.compareTo(remainingAmount) >= 0) {
                department.setInputAmt(currentInputAmt.subtract(remainingAmount));
                break;
            } else {
                department.setInputAmt(BigDecimal.ZERO);
                remainingAmount = remainingAmount.subtract(currentInputAmt);
            }
        }
    }

}
