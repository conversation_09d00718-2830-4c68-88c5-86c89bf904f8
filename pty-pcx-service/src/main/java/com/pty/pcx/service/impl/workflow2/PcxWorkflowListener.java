package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.pty.mad.entity.PaOption;
import com.pty.pcx.api.workflow2.IPositionPostService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.common.enu.BillStatusEnum;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.dto.pa.PaUserDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.pa.IPcxUserService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.msg.event.PcxMsgApprovalToDoEvent;
import com.pty.pcx.service.impl.msg.service.ApprovalMessageService;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.api.IPtyWfDeployService;
import com.pty.workflow2.api.proxy.ExecutionContext;
import com.pty.workflow2.api.proxy.PtyWfExecutionInterceptor;
import com.pty.workflow2.api.proxy.PtyWorkflowListener;
import com.pty.workflow2.api.proxy.TaskContext;
import com.pty.workflow2.extend.pcx.PcxNode;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import com.pty.workflow2.extend.pcx.PcxProcessDefinition;
import com.pty.workflow2.vo.ExecuteInfo;
import com.pty.workflow2.vo.IdentityInfo;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.IPaOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.pty.pcx.common.constant.PcxConstant.OPTION_CODE_POSITION_MUST_APPROVE;

@Slf4j
@Indexed
@Service
public class PcxWorkflowListener implements PtyWorkflowListener, PtyWfExecutionInterceptor {
    @Autowired
    @Qualifier("PtyWfDeployService2")
    private IPtyWfDeployService ptyWfDeployService;

    @Autowired
    private IPaOptionService paOptionService;

    @Autowired
    private BillMainService billMainService;

    @Autowired
    private IProcessService processService;

    @Autowired
    private PositionServiceManager positionServiceManager;



    @Autowired
    private ApprovalMessageService approvalMessageService;

    @Override
    public void processStart(ExecutionContext executionContext) {
        log.info("workflow instance start {}", executionContext.getBusinessKey());
    }

    @Override
    public void processEnd(ExecutionContext executionContext) {
        log.info("workflow instance end {}", executionContext.getBusinessKey());
        processService.processEnd(executionContext.getBusinessKey());

    }

    @Override
    public void taskStart(TaskContext taskContext) {
        log.info("workflow task start {}.{}", taskContext.getTaskNodeId(), taskContext.getBusinessId());

        if (taskContext.getTaskNodeId().equals(PcxNodeEnum.make_bill.getId())) {
            log.info("make bill {} task start", taskContext.getBusinessId());
            Object action = taskContext.getVariable("action");
            // 正向为空
            processService.makeBillStart(taskContext.getBusinessId(), null == action ? StrUtil.EMPTY : action.toString());
        }

        if (taskContext.getSendMessage()) {
            // 发送消息通知
            approvalMessageService.workflowSendMessage(taskContext);
        } else {
            log.info("bill {} at position {} no need send message", taskContext.getBusinessId(), taskContext.getTaskNodeId());
        }
    }

    @Override
    public void taskEnd(TaskContext taskContext) {
        log.info("workflow task end {}.{}", taskContext.getTaskNodeId(), taskContext.getBusinessId());
        if (taskContext.getTaskNodeId().equals(PcxNodeEnum.finance_audit.getId() + "_1")) {
            log.info("finance audit bill {}", taskContext.getBusinessId());
            processService.finance1AuditEnd(taskContext.getBusinessId());
        }
    }

    public Pair<Boolean, IdentityInfo> calculateAssignOrGroups(ExecutionContext executionContext) {
        // 是否需要审核
        boolean needApprove = true;
        log.info("pcx workflow {} find assign users", executionContext.getCurrentNodeId());
        IPositionService<String> service = positionServiceManager.getPositionService(executionContext.getCurrentNodeId());

        IdentityInfo info = new IdentityInfo();
        try {
            ThreadLocalUtil.set(executionContext.getCurrentNodeId());
            Assert.state(service != null, "未找到岗位服务{}", executionContext.getCurrentNodeId());
            List<String> positionUser = service.findPositionUser(executionContext.getBusinessKey());
            // 部门人员
            IPositionPostService positionPostService = positionServiceManager.getPositionPostService(executionContext.getCurrentNodeId());
            if (positionPostService != null) {
                needApprove = positionPostService.postDeal(executionContext.getBusinessKey(), executionContext.getCurrentNodeId(), positionUser);
            }
            positionUser.forEach(info::addAssignee);
        } catch (Exception e) {
            log.error("获取流程定义异常:" + e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            ThreadLocalUtil.remove();
        }
        return Pair.of(needApprove, info);
    }

    public boolean skipInfo(ExecutionContext executionContext) {
        PcxProcessDefinition def = null;
        try {
            def = (PcxProcessDefinition)ptyWfDeployService.getPcxProcessDefinition(executionContext.getProcessDefinitionId());
            Assert.state(def != null, "未找到流程定义{}", executionContext.getProcessDefinitionId());
            PcxNode node = def.findNode(executionContext.getCurrentNodeId());
            return node.isSkip();
        } catch (Exception e) {
            log.error("获取流程参数异常:" + e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public ExecuteInfo executeInfo(ExecutionContext executionContext) {
        boolean skipInfo = this.skipInfo(executionContext);
        ExecuteInfo executeInfo = new ExecuteInfo();
        executeInfo.setSkip(skipInfo);
        executeInfo.setMustApproval(false);
        executeInfo.setIdentityInfo(new IdentityInfo());
        if (skipInfo || !executionContext.needCalculateExecutor())
            return executeInfo;
        Pair<Boolean, IdentityInfo> needApproveAndIdentityInfo = this.calculateAssignOrGroups(executionContext);
        Boolean needApprove = needApproveAndIdentityInfo.getKey();
        IdentityInfo identityInfo = needApproveAndIdentityInfo.getValue();
        executeInfo.setIdentityInfo(identityInfo);

        if (needApprove) {
            PcxBill bill = billMainService.view(executionContext.getBusinessKey());
            PaOption paOption = new PaOption();
            paOption.setAgyCode(bill.getAgyCode());
            paOption.setOptCode(OPTION_CODE_POSITION_MUST_APPROVE);
            paOption.setFiscal(DateUtil.thisYear());
            List<PaOption> paOptions = paOptionService.select(paOption);
            if (CollectionUtil.isEmpty(paOptions)) {
                paOption.setAgyCode("*");
                paOptions = paOptionService.select(paOption);
            }
            if (!paOptions.isEmpty()) {
                paOption = paOptions.get(0);
                String[] values = paOption.getOptValue().split(",");
                for (String value : values) {
                    if (executionContext.getCurrentNodeId().startsWith(value)) {
                        executeInfo.setMustApproval(true);
                        break;
                    }
                }
            }
        }
        return executeInfo;
    }
}
