package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.CollectionUtils;
import com.beust.jcommander.internal.Lists;
import com.pty.mad.common.qo.MadWorkflowEmployeeQo;
import com.pty.mad.entity.MadProject;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.vo.bill.PcxBillBalanceVO;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.IMadDepartmentService;
import org.pty.mad.api.IMadProjectService;
import org.pty.mad.api.PaDeptApproveSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class RelevanceAuditIPositionService extends AbstractRuleDomainService implements IPositionService<String> {

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Resource
    private IMadDepartmentService departmentService;
    @Resource
    private PaDeptApproveSettingService paDeptApproveSettingService;
    @Resource
    private PcxBillService pcxBillService;
    @Resource
    private IMadProjectService madProjectService;
    @Override
    public List<String> getPositionIds() {
        return Lists.newArrayList(
                PcxNodeEnum.relevance_audit.getId(),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "5"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "6"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "7"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "8"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "9"),
                String.format("%s_%s", PcxNodeEnum.relevance_audit.getId(), "10")
        );
    }

    @Override
    public List<String> findPositionUser(String billNo) {

        CheckMsg<PcxBillVO> msg = pcxBillService.view(billNo, PositionBlockEnum.FUND_SOURCE, PositionBlockEnum.SPECIFICITY);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());

        PcxBillVO vo = msg.getData();
        PcxBill basicInfo = vo.getBasicInfo();
        MadWorkflowEmployeeQo ruleDomain = super.getRuleDomain(billNo, vo, basicInfo);
        return getUserIds(basicInfo, departmentService.selectApprovalsByDeptCode(ruleDomain));
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
