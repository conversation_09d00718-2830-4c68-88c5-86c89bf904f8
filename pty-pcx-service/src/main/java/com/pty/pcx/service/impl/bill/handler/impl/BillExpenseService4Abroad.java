package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.YesOrNoEnum;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.enu.abroad.AbroadRateEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillAbroadTripDao;
import com.pty.pcx.dao.bill.PcxBillExpAbroadDao;
import com.pty.pcx.dao.bill.PcxBillExpDetailAbroadDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillAbroadTrip;
import com.pty.pcx.entity.bill.PcxBillExpAbroad;
import com.pty.pcx.entity.bill.PcxBillExpDetailAbroad;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.bill.PcxBillExpAbroadVO;
import com.pty.pcx.vo.bill.PcxBillListVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 出国费用处理接口接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service("billExpenseService4Abroad")
@Slf4j
@Indexed
public class BillExpenseService4Abroad extends BillExpenseCommonService implements BillExpenseService<PcxBillExpAbroad> {
    @Autowired
    private PcxBillExpAbroadDao pcxBillExpAbroadDao;
    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;
    @Autowired
    private PcxBillExpDetailAbroadDao pcxBillExpDetailAbroadDao;
    @Autowired
    private PcxBillAbroadTripDao pcxBillAbroadTripDao;
    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcxBillExpAbroad saveOrUpdate(PcxBillExpAbroad pcxBillExpAbroad, PcxBill pcxBill) {
        // 调用 DAO 层的通用查询方法
        PcxBillExpAbroad existingRecord = pcxBillExpAbroadDao.selectByUnionKey(
                pcxBill.getId(),
                pcxBillExpAbroad.getExpenseCode(),
                pcxBill.getAgyCode(),
                pcxBill.getFiscal(),
                pcxBill.getMofDivCode()
        );

        // 设置公共字段
        prepareCommonFields(pcxBillExpAbroad, pcxBill, existingRecord);

        // 插入或更新逻辑
        if (Objects.isNull(existingRecord)) {
            log.info("插入数据库, billId: {}", pcxBill.getId());
            pcxBillExpAbroadDao.insert(pcxBillExpAbroad);
            batchServiceUtil.batchProcess(pcxBillExpAbroad.getPcxBillAbroadTrip(), PcxBillAbroadTripDao.class, PcxBillAbroadTripDao::insert);
        } else {
            log.info("更新数据库, id: {}", existingRecord.getId());
            pcxBillExpAbroadDao.updateById(pcxBillExpAbroad);
            batchServiceUtil.batchProcess(pcxBillExpAbroad.getPcxBillAbroadTrip(), PcxBillAbroadTripDao.class, PcxBillAbroadTripDao::updateById);
        }

        // 补充公共信息
        saveCommonData(pcxBillExpAbroad, pcxBill);
        return pcxBillExpAbroad;
    }

    /**
     * 设置公共字段
     *
     * @param pcxBillExpAbroad 当前操作的实体对象
     * @param pcxBill          单据对象
     * @param existingRecord   数据库中已存在的记录
     */
    private void prepareCommonFields(PcxBillExpAbroad pcxBillExpAbroad, PcxBill pcxBill, PcxBillExpAbroad existingRecord) {
        // 设置 BillId
        pcxBillExpAbroad.setBillId(pcxBill.getId());
        pcxBillExpAbroad.setAgyCode(pcxBill.getAgyCode());
        pcxBillExpAbroad.setFiscal(pcxBill.getFiscal());
        pcxBillExpAbroad.setMofDivCode(pcxBill.getMofDivCode());
        pcxBillExpAbroad.setTenantId(StringUtil.isEmpty(pcxBill.getTenantId()) ? PtyContext.getTenantId() : pcxBill.getTenantId());

        // 如果是新增记录生成ID; 如果是更新记录使用已有ID
        if (Objects.isNull(existingRecord)) {
            pcxBillExpAbroad.setId(IDGenerator.id());
        } else {
            pcxBillExpAbroad.setId(existingRecord.getId());
        }
        // todo scc trip子表后续修改为，先删除再插入。
        // 组装行程数据
        List<PcxBillAbroadTrip> pcxBillAbroadTrips = pcxBillExpAbroad.getPcxBillAbroadTrip();
        if (CollectionUtils.isNotEmpty(pcxBillAbroadTrips)) {
            pcxBillExpAbroad.getPcxBillAbroadTrip().forEach(trip -> {
                trip.setAgyCode(pcxBill.getAgyCode());
                trip.setFiscal(pcxBill.getFiscal());
                trip.setMofDivCode(pcxBill.getMofDivCode());
            });
            if (Objects.nonNull(existingRecord)) {
                // 根据单据编号查询现有的行程数据
                List<PcxBillAbroadTrip> existingTrips = pcxBillAbroadTripDao.selectList(
                        Wrappers.lambdaQuery(PcxBillAbroadTrip.class)
                                .eq(PcxBillAbroadTrip::getBillId, existingRecord.getBillId())
                );

                // 创建一个 Map 用于快速查找现有行程数据
                Map<String, PcxBillAbroadTrip> existingTripMap = existingTrips.stream()
                        .collect(Collectors.toMap(PcxBillAbroadTrip::getId, trip -> trip));

                pcxBillAbroadTrips.forEach(trip -> {
                    // 如果行程数据不存在, 使用新生成ID
                    if (!existingTripMap.containsKey(trip.getId())) {
                        trip.setId(IDGenerator.id());trip.setId(IDGenerator.id());
                    }
                });
            } else {
                // 如果是新增记录，生成新ID
                pcxBillAbroadTrips.forEach(trip -> {
                    trip.setId(IDGenerator.id());
                    trip.setBillId(pcxBill.getId());
                });
            }
        } else {
            log.debug("行程数据为空，无需设置 ID 和 BillId");
        }
    }

    /**
     * 查看出国费信息
     *
     * @param expenseCode 费用编码，用于查询特定的出国费用记录
     * @param pcxBill     出国费主表对象，包含查询所需的多个关键字段
     * @return 返回出国费详情DTO对象，包含出国费主表信息、行程信息和费用明细信息
     */
    @Override
    public PcxBillExpAbroadVO view(String expenseCode, PcxBill pcxBill) {
        // 查询出国费主表信息
        PcxBillExpAbroad billExpAbroad = Optional.ofNullable(pcxBillExpAbroadDao.selectByUnionKey(
                pcxBill.getId(),
                expenseCode,
                pcxBill.getAgyCode(),
                pcxBill.getFiscal(),
                pcxBill.getMofDivCode()
        )).orElseGet(() -> {
            log.warn("未查询到对应费用记录，expenseCode: {}, billId: {}", expenseCode, pcxBill.getId());
            return null;
        });

        if (billExpAbroad == null) {
            return null;
        }
        PcxBillExpAbroadVO pcxBillExpAbroadVO = new PcxBillExpAbroadVO();
        // 初始化 DTO 对象
        BeanUtils.copyProperties(billExpAbroad, pcxBillExpAbroadVO);

        // 查询行程信息
        List<PcxBillAbroadTrip> pcxBillAbroadTrips = pcxBillAbroadTripDao.selectList(
                Wrappers.lambdaQuery(PcxBillAbroadTrip.class)
                        .eq(PcxBillAbroadTrip::getBillId, billExpAbroad.getBillId())
                        .eq(PcxBillAbroadTrip::getAgyCode, billExpAbroad.getAgyCode())
                        .eq(PcxBillAbroadTrip::getFiscal, billExpAbroad.getFiscal())
                        .eq(PcxBillAbroadTrip::getMofDivCode, billExpAbroad.getMofDivCode())
        );

        if (CollectionUtil.isEmpty(pcxBillAbroadTrips)) {
            return pcxBillExpAbroadVO;
        }

        // 转换 provideFood 和 provideTool 的值
        pcxBillAbroadTrips.forEach(trip -> {
            trip.setProvideFoodName(YesOrNoEnum.getDescByCode(trip.getProvideFood()));
            trip.setProvideToolName(YesOrNoEnum.getDescByCode(trip.getProvideTool()));
            if (StringUtil.isNotEmpty(trip.getCurrency())) {
                trip.setCurrencyName(AbroadRateEnum.getByCode(trip.getCurrency()).getName());
            }
        });
        pcxBillExpAbroadVO.setPcxBillAbroadTrip(pcxBillAbroadTrips);

        // 查询并处理费用明细
        List<PcxBillExpDetailAbroad> abroadDetail = pcxBillExpDetailAbroadDao.selectList(
                Wrappers.lambdaQuery(PcxBillExpDetailAbroad.class)
                        .eq(PcxBillExpDetailAbroad::getBillId, billExpAbroad.getBillId())
                        .eq(PcxBillExpDetailAbroad::getAgyCode, billExpAbroad.getAgyCode())
                        .eq(PcxBillExpDetailAbroad::getFiscal, billExpAbroad.getFiscal())
                        .eq(PcxBillExpDetailAbroad::getMofDivCode, billExpAbroad.getMofDivCode())
        );

        // 转换费用明细中的币种代码为名称
        abroadDetail.forEach(detail -> {
            if (StringUtil.isNotEmpty(detail.getCurrency())) {
                detail.setCurrency(AbroadRateEnum.getByCode(detail.getCurrency()).getName());
            }
        });
        Map<Integer, List<PcxBillExpDetailAbroad>> detailAbroadMap = abroadDetail.stream()
                .collect(Collectors.groupingBy(PcxBillExpDetailAbroad::getExpenseType));

        Map<Integer, Object> detailAbroadInfo = processBudgetDetails(detailAbroadMap, pcxBillAbroadTrips, pcxBillExpAbroadVO);
        //放入处理后的人民币、外币预算明细信息
        pcxBillExpAbroadVO.setPcxBillExpDetailAbroad(detailAbroadInfo);
        return pcxBillExpAbroadVO;
    }

    /**
     * 处理预算详情信息
     * 该方法主要负责根据预算类型处理并整合预算详情，针对人民币预算和外币预算采取不同的处理策略
     *
     * @param detailAbroadMap 预算详情映射，键为预算类型，值为对应类型的预算详情列表
     * @param trips           出国行程列表，用于外币预算处理时参考
     * @return 返回处理后的预算详情映射，形式与输入相同，但内部结构可能经过处理
     */
    private Map<Integer, Object> processBudgetDetails(Map<Integer, List<PcxBillExpDetailAbroad>> detailAbroadMap,
                                                      List<PcxBillAbroadTrip> trips, PcxBillExpAbroadVO pcxBillExpAbroadVO) {
        //初始化结果映射，用于存储处理后的预算详情
        Map<Integer, Object> result = new HashMap<>();

        //遍历输入的预算详情映射，根据预算类型决定处理方式
        detailAbroadMap.forEach((type, details) -> {
            //如果预算类型为人民币，则直接将详情信息放入结果中
            if (PcxConstant.BUDGET_TYPE_RMB == type) {
                result.put(type, details);
            } else if (PcxConstant.BUDGET_TYPE_FOREIGN == type) {
                //处理国外预算，调用专门的方法进行处理
                result.put(type, pcxBillExpAbroadVO.pcxBillExpDetailAbroadVO(details,trips));
            }
        });

        //返回处理后的预算详情映射
        return result;
    }

    /**
     * 处理国外预算的逻辑
     * 该方法将根据国外行程单和费用明细进行匹配，返回一个映射，其中每个行程单对应一个费用明细列表
     *
     * @param details 费用明细列表，包含各项国外支出的详细信息
     * @param trips   行程单列表，包含国外行程的相关信息
     * @return 返回一个映射，键为行程单对象，值为该行程单对应的费用明细列表
     */
    private Map<PcxBillAbroadTrip, List<PcxBillExpDetailAbroad>> processForeignBudget(
            List<PcxBillExpDetailAbroad> details, List<PcxBillAbroadTrip> trips) {
        // 将费用明细按行程单ID分组，以便后续匹配行程单
        Map<String, List<PcxBillExpDetailAbroad>> groupedByTripId = details.stream()
                .collect(Collectors.groupingBy(PcxBillExpDetailAbroad::getTripExpenseId));

        // 过滤出有费用明细的行程单，并将每个行程单与其费用明细列表匹配，生成映射
        return trips.stream()
                .filter(trip -> groupedByTripId.containsKey(trip.getTripExpenseId()))
                .collect(Collectors.toMap(
                        trip -> trip,
                        trip -> groupedByTripId.get(trip.getTripExpenseId())
                ));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String expenseCode, PcxBill pcxBill) {
        // 查询出国费主表信息
        PcxBillExpAbroad billExpAbroad = Optional.ofNullable(pcxBillExpAbroadDao.selectByUnionKey(
                pcxBill.getId(),
                expenseCode,
                pcxBill.getAgyCode(),
                pcxBill.getFiscal(),
                pcxBill.getMofDivCode()
        )).orElseGet(() -> {
            log.warn("未查询到对应费用记录，expenseCode: {}, billId: {}", expenseCode, pcxBill.getId());
            return null;
        });

        if (Objects.nonNull(billExpAbroad)) {
            //删除费用主表信息
            pcxBillExpAbroadDao.deleteById(billExpAbroad.getId());
            //删除行程信息
            pcxBillAbroadTripDao.delete(Wrappers.lambdaQuery(PcxBillAbroadTrip.class)
                    .eq(PcxBillAbroadTrip::getBillId, billExpAbroad.getBillId()));
            //出国费在此处同时删除费用明细数据
            pcxBillExpDetailAbroadDao.delete(Wrappers.lambdaQuery(PcxBillExpDetailAbroad.class)
                    .eq(PcxBillExpDetailAbroad::getBillId, billExpAbroad.getBillId()));
        }

    }

    @Override
    public void dealContent(List<PcxBillListVO> value) {

    }

    @Override
    public CheckMsg<Void> validate(PcxBillExpAbroad expBase, String billFuncCode) {
        if (Objects.isNull(expBase)) {
            return CheckMsg.fail("因公出国(境)费用信息为空");
        }
        FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum = FormSettingEnums.BillFuncCodeEnum.getByCode(billFuncCode);
        if (Objects.isNull(billFuncCodeEnum)) {
            return CheckMsg.fail("暂不支持单据类型为：[" + billFuncCode + "]的业务操作");
        }
        //查询差旅费启用的必填的专属字段
        PcxBasFormSettingQueryQO qo = new PcxBasFormSettingQueryQO(
                FormSettingEnums.FormClassifyEnum.EXPENSE.getCode(),
                billFuncCodeEnum.getBit(),
                expBase.getExpenseCode(),
                FormSettingEnums.FormTypeEnum.EXPENSE_EXCLUSIVE.getCode(),
                expBase.getAgyCode(),
                expBase.getFiscal(),
                expBase.getMofDivCode());
        Response<List<PcxBasFormSettingQO>> response = pcxBasFormSettingService.selectAllFormSetting(qo);
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            //启用且必填
            List<PcxBasFormSettingQO> formSettingQOS = response.getData().stream().filter(item -> item.getIsEnabled() == PubConstant.LOGIC_TRUE
                    & item.getIsNull() == PubConstant.LOGIC_TRUE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(formSettingQOS)) {
                Map<String, Object> stringObjectMap = BeanUtil.beanToMap(expBase);
                List<String> missingFields = new ArrayList<>();

                for (PcxBasFormSettingQO formSettingQO : formSettingQOS) {
                    //todo 费用承担部门不做校验
                    if (formSettingQO.getFieldValue().equals("departmentCode")){
                        continue;
                    }
                    Object o = stringObjectMap.get(formSettingQO.getFieldValue());
                    if (Objects.isNull(o) || StringUtil.isEmpty(o.toString())) {
                        String fieldName = formSettingQO.getFieldName();
                        missingFields.add(fieldName);
                        log.warn("专属属性:[{}]不能为空", fieldName);
                    }
                }

                if (!missingFields.isEmpty()) {
                    String errorMessage = "专属属性:" + String.join("、", missingFields) + "不能为空";
                    return CheckMsg.fail(errorMessage);
                }
            }
        }
        return CheckMsg.success();
    }
}
