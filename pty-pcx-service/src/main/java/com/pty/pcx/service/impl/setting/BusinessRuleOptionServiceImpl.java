package com.pty.pcx.service.impl.setting;

import com.alibaba.fastjson.JSONObject;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.OperationCNConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.setting.PcxPaOptionDao;
import com.pty.pcx.entity.setting.PaOption;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.util.OptionCacheUtil;
import com.pty.pcx.vo.PaOptionVO;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class BusinessRuleOptionServiceImpl implements IBusinessRuleOptionService {

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private PcxPaOptionDao pcxPaOptionDao;

    @Override
    public Response selectWithPage(PaOptionQO qo){
        CheckMsg valided = isValided(qo);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        List<PaOptionVO> agyOptions = getAgyOptions(qo);
        if(CollectionUtil.isEmpty(agyOptions)){
            return Response.success().setData(new PageResult<PaOption>().setTotal(0L).setResult(new ArrayList<>()));
        }
        int pageIndex = qo.getPageIndex();
        int pageSize = qo.getPageSize();
        int fromIndex = (pageIndex - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, agyOptions.size());
        List<PaOptionVO> paginatedOptions = agyOptions.subList(fromIndex, toIndex);
        PageResult<PaOptionVO> pageResult = new PageResult<PaOptionVO>().setResult(paginatedOptions).setTotal(Long.valueOf(agyOptions.size()));
        return Response.success().setData(pageResult);
    }

    @Override
    public String getOptionValueByOptionCode(PaOptionQO qo,String optionCode) {
        String result = StringUtil.EMPTY;
        BusinessRuleEnum.BusinessOptionEnum optionByCode = BusinessRuleEnum.BusinessOptionEnum.getOptionByCode(optionCode);
        if (optionByCode != null) {
            result = optionByCode.getOptValue();
        }
        List<PaOptionVO> agyOptions = getAgyOptions(qo);
        if (CollectionUtil.isNotEmpty(agyOptions)) {
            return agyOptions.stream()
                    .filter(item -> optionCode.equals(item.getOptCode()))
                    .map(PaOption::getOptValue)
                    .findFirst()
                    .orElse(result);
        }
        return result;
    }

    @Override
    public Response save(PaOptionQO qo) {
        CheckMsg valided = isValided(qo);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        if(StringUtil.isEmpty(qo.getOptCode())){
            return Response.fail().setMsg("业务规则编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getOptValue())){
            return Response.fail().setMsg("业务规则值不能为空");
        }
        qo.setOptId(StringUtil.getUUID());
        qo.setSysId(StringUtil.isEmpty(qo.getSysId())?PcxConstant.SYS_ID:qo.getSysId());
        qo.setTenantId(StringUtil.getStringValue(qo.getTenantId(), PtyContext.getTenantId()));
        qo.setIsEdit(ObjectUtils.isEmpty(qo.getIsEdit())?PubConstant.LOGIC_FALSE:qo.getIsEdit());
        qo.setIsVisible((ObjectUtils.isEmpty(qo.getIsVisible())?PubConstant.LOGIC_FALSE: qo.getIsVisible()));
        qo.setAcbCode(StringUtil.isEmpty(qo.getAcbCode())?PubConstant.SYS_DEFAULT_CODE:qo.getAcbCode());
        qo.setTenantId(StringUtil.isNotBlank(qo.getTenantId())?qo.getTenantId():PtyContext.getTenantId());
        pcxPaOptionDao.insert(qo);
        return Response.success().setData("新增业务规则成功");
    }

    @Override
    public Response selectById(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        if(StringUtil.isEmpty(paOptionQO.getOptId())){
            return Response.fail().setMsg("请选择需要查看的业务规则");
        }
        List<PaOption> paOptions = pcxPaOptionDao.selectByQO(paOptionQO);
        if(CollectionUtil.isEmpty(paOptions)){
            return Response.fail().setMsg("未查询到相关业务规则");
        }
        return Response.success().setData(paOptions.get(0));
    }

    @Override
    public Response selectByQO(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        List<PaOption> paOptions = pcxPaOptionDao.selectByQO(paOptionQO);
        if(CollectionUtil.isEmpty(paOptions)){
            return Response.fail().setMsg("未查询到相关业务规则");
        }
        return Response.success().setData(paOptions.get(0));
    }

    @Override
    public Response<?> selectListByQO(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        List<PaOption> paOptions = pcxPaOptionDao.selectByQO(paOptionQO);
        return Response.success(paOptions);
    }

    @Override
    public Response remove(PaOptionQO paOptionQO) {
        CheckMsg checkMsg = checkParams(paOptionQO, OperationCNConstant.DELETE);
        if(!checkMsg.isSuccess()){
            return Response.fail().setMsg(checkMsg.getMsgInfo());
        }
        pcxPaOptionDao.deleteByQO(paOptionQO);
        return Response.success();
    }

    @Override
    public Response updateById(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if (!valided.isSuccess()) {
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        PaOptionQO param = new PaOptionQO();
        BeanUtils.copyProperties(paOptionQO, param);
        if (StringUtil.isEmpty(paOptionQO.getOptId()) || PubConstant.SYS_DEFAULT_CODE.equals(paOptionQO.getOptId())) {
            Response saveResponse = this.save(param);
            if (!saveResponse.isSuccess()) {
                return Response.fail().setMsg("修改业务规则失败");
            }
        } else {
            pcxPaOptionDao.updateById(param);
        }
        Cache cache = OptionCacheUtil.getCache(cacheManager);
        String key = OptionCacheUtil.key(paOptionQO.getAgyCode(), StringUtil.isNotBlank(paOptionQO.getTenantId())? paOptionQO.getTenantId() : PtyContext.getTenantId(), StringUtil.getStringValue(paOptionQO.getFiscal()), paOptionQO.getMofDivCode());
        cache.evict(key);
        return Response.success().setData("修改业务规则成功");
    }

    @Override
    public Response getAll(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if(!valided.isSuccess()){
            return Response.fail().setMsg(valided.getMsgInfo());
        }
        List<PaOptionVO> agyOptions = getAgyOptions(paOptionQO);
        Predicate<PaOption> predicate = buildPredicate(paOptionQO);
        agyOptions = agyOptions.stream().filter(predicate).collect(Collectors.toList());
        return Response.success().setData(agyOptions);
    }

    @Override
    public Response getGroupList(PaOptionQO paOptionQO) {
        List<Map<String, String>> result = BusinessRuleEnum.BusinessGroupNameEnum.getAllGroups();
        if (!ObjectUtils.isEmpty(paOptionQO) && !ObjectUtils.isEmpty(paOptionQO.getShowPage())) {
            result = result.stream()
                    .filter(group -> paOptionQO.getShowPage().equals(group.get("showPage")))
                    .collect(Collectors.toList());
        }
        return Response.success().setData(result);
    }

    @Override
    public CheckMsg getRuleMap(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if(!valided.isSuccess()){
            return CheckMsg.fail().setMsgInfo(valided.getMsgInfo());
        }
        List<PaOptionVO> agyOptions = getAgyOptions(paOptionQO);
        Map<String, String> resultMap = agyOptions.stream()
                .collect(Collectors.toMap(
                        PaOptionVO::getOptCode,
                        PaOptionVO::getOptValue,
                        (oldValue, newValue) -> oldValue
                ));
        return CheckMsg.success().setData(resultMap);
    }

    @Override
    public List<PaOptionVO> getAgyOpt(PaOptionQO paOptionQO) {
        CheckMsg valided = isValided(paOptionQO);
        if(!valided.isSuccess()){
            return Collections.emptyList();
        }
        List<PaOptionVO> agyOptions = getAgyOptions(paOptionQO);
        return agyOptions;
    }

    private Predicate<PaOption> buildPredicate(PaOptionQO paOptionQO) {
        Predicate<PaOption> predicate = option -> Boolean.TRUE;
        if (StringUtil.isNotEmpty(paOptionQO.getGroupName())) {
            predicate = predicate.and(option -> paOptionQO.getGroupName().equals(option.getGroupName()));
        }
        if (!ObjectUtils.isEmpty(paOptionQO.getIsVisible())) {
            predicate = predicate.and(option -> paOptionQO.getIsVisible().equals(option.getIsVisible()));
        }
        if (!ObjectUtils.isEmpty(paOptionQO.getOptCode())) {
            predicate = predicate.and(option -> paOptionQO.getOptCode().equals(option.getOptCode()));
        }
        if (!ObjectUtils.isEmpty(paOptionQO.getOptName())) {
            predicate = predicate.and(option -> paOptionQO.getOptName().equals(option.getOptName()));
        }
        return predicate;
    }

    private List<PaOptionVO> getAgyOptions(PaOptionQO qo) {
        // 检查必要参数
        if (StringUtil.isEmpty(qo.getMofDivCode()) || StringUtil.isEmpty(qo.getAgyCode()) || StringUtil.isEmpty(qo.getFiscal())) {
            return new ArrayList<>();
        }
        // 获取缓存和键值
        Cache cache = OptionCacheUtil.getCache(cacheManager);
        String key = OptionCacheUtil.key(qo.getAgyCode(), StringUtil.isNotBlank(qo.getTenantId())? qo.getTenantId() : PtyContext.getTenantId(), StringUtil.getStringValue(qo.getFiscal()), qo.getMofDivCode());
        // 从缓存中获取数据
        List<PaOptionVO> paOptions = getCachedOptions(cache, key);
        if (CollectionUtil.isNotEmpty(paOptions)) {
            return paOptions;
        }
        synchronized (this) {
            // Double-check 获取缓存数据
            paOptions = getCachedOptions(cache, key);
            if (CollectionUtil.isNotEmpty(paOptions)) {
                return paOptions;
            }
            // 数据库查询及处理
            Map<String, PaOptionVO> agyOptionsMap = queryAndProcessOptions(qo);
            // 添加默认配置项
            mergeDefaultOptions(agyOptionsMap);
            // 缓存数据
            if (CollectionUtil.isNotEmpty(agyOptionsMap)) {
                cache.put(key, JSONObject.toJSONString(agyOptionsMap.values()));
            }

            // 返回结果
            return new ArrayList<>(agyOptionsMap.values());
        }
    }

    // 从缓存中读取数据
    private List<PaOptionVO> getCachedOptions(Cache cache, String key) {
        if (ObjectUtils.isEmpty(cache)) {
            return new ArrayList<>();
        }
        Cache.ValueWrapper valueWrapper = cache.get(key);
        if (valueWrapper != null) {
            Object cacheValue = valueWrapper.get();
            if (cacheValue instanceof String) {
                return JSONObject.parseArray(cacheValue.toString(), PaOptionVO.class);
            }
        }
        return new ArrayList<>();
    }

    // 查询数据库并处理结果
    private Map<String, PaOptionVO> queryAndProcessOptions(PaOptionQO qo) {
        PaOptionQO param = new PaOptionQO();
        param.setAgyCode(qo.getAgyCode());
        param.setTenantId(qo.getTenantId());
        param.setFiscal(qo.getFiscal());
        param.setSysId(PcxConstant.SYS_ID);
        param.setMofDivCode(qo.getMofDivCode());
        List<PaOption> paOptionList = pcxPaOptionDao.selectByQO(param);
        return paOptionList.stream().collect(Collectors.toMap(
                PaOption::getOptCode,
                option -> {
                    PaOptionVO vo = new PaOptionVO();
                    vo.setFiscal(option.getFiscal());
                    vo.setOptId(option.getOptId());
                    vo.setOptCode(option.getOptCode());
                    vo.setOptName(option.getOptName());
                    vo.setAcbCode(StringUtil.isEmpty(option.getAcbCode()) ? PubConstant.SYS_DEFAULT_CODE : option.getAcbCode());
                    vo.setOptValue(StringUtil.isEmpty(option.getOptValue()) ? "" : option.getOptValue());
                    vo.setOptDesc(option.getOptDesc());
                    vo.setIsVisible(option.getIsVisible());
                    vo.setAtomCode(option.getAtomCode());
                    vo.setFieldDisptype(option.getFieldDisptype());
                    vo.setGroupName(option.getGroupName());
                    vo.setFieldValuesetCode(option.getFieldValuesetCode());
                    vo.setAdmCode(option.getAdmCode());
                    vo.setOrdSeq(option.getOrdSeq());
                    vo.setIsEnableSetting(option.getIsEnableSetting());
                    vo.setIsSystem(PubConstant.LOGIC_FALSE);
                    vo.setSysId(PcxConstant.SYS_ID);
                    return vo;
                },
                (oldValue, newValue) -> oldValue
        ));
    }

    // 合并默认配置项
    private void mergeDefaultOptions(Map<String, PaOptionVO> agyOptionsMap) {
        Map<String, BusinessRuleEnum.BusinessOptionEnum> allOptions = BusinessRuleEnum.BusinessOptionEnum.getAllOptionMap();
        for (Map.Entry<String, BusinessRuleEnum.BusinessOptionEnum> entry : allOptions.entrySet()) {
            agyOptionsMap.putIfAbsent(entry.getKey(), createDefaultOption(entry));
        }
    }

    // 创建默认配置项
    private PaOptionVO createDefaultOption(Map.Entry<String, BusinessRuleEnum.BusinessOptionEnum> entry) {
        BusinessRuleEnum.BusinessOptionEnum option = entry.getValue();
        PaOptionVO vo = new PaOptionVO();
        vo.setGroupName(option.getGroupName());
        vo.setOptId(PubConstant.SYS_DEFAULT_CODE);
        vo.setOptCode(entry.getKey());
        vo.setOptName(option.getOptName());
        vo.setOptValue(option.getOptValue());
        vo.setFieldDisptype(option.getFieldDisptype());
        vo.setAtomCode(option.getAtomCode());
        vo.setAcbCode(PubConstant.SYS_DEFAULT_CODE);
        vo.setFieldValuesetCode(option.getFieldValuesetCode());
        vo.setOrdSeq(option.getOrdSeq());
        vo.setIsVisible(option.getIsVisible());
        vo.setOptDesc(option.getOptDesc());
        vo.setSysId(PcxConstant.SYS_ID);
        vo.setIsSystem(PubConstant.LOGIC_TRUE);
        return vo;
    }

    private CheckMsg isValided(PaOptionQO qo) {
        if(StringUtil.isEmpty(qo.getAgyCode())){
            return CheckMsg.fail("单位编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getMofDivCode())){
            return CheckMsg.fail("区划编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getFiscal())){
            return CheckMsg.fail("年度不能为空");
        }
        return CheckMsg.success();
    }

    private CheckMsg checkParams(PaOptionQO qo, String action) {
        if(CollectionUtil.isEmpty(qo.getOptIds())|| StringUtil.isEmpty(qo.getOptId())){
            return CheckMsg.fail(String.format("请勾选需要{}的数据",action));
        }
        CheckMsg valided = isValided(qo);
        if(!valided.isSuccess()){
            return valided;
        }
        return CheckMsg.success();
    }

    @Override
    public boolean isEnableExpenseApportion(PaOptionQO paOptionQO) {
        // 查询费用承担部门分摊配置
        String value = getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.IS_ENABLE_EXPENSE_APPORTION.getOptCode());
        return Objects.equals("1", value);
    }
}
