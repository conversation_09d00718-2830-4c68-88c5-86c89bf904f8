package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.fileservice.entity.PaAttach;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.bill.PcxBillAmtApportionDepartmentService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillPayDetailService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.balance.IBalanceExternalService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.ecs.IsValidEnum;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.attachlist.PcxAttachListDao;
import com.pty.pcx.dao.attachlist.PcxAttachListRelationDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dao.ecs.PcxEcsComparedResultDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.dto.bill.PcxBillBalanceDTO;
import com.pty.pcx.dto.ecs.EcsMsgDTO;
import com.pty.pcx.dto.mad.MadProjectDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.entity.attachlist.PcxAttachList;
import com.pty.pcx.entity.attachlist.PcxAttachListRelation;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.ecscompared.PcxEcsComparedResult;
import com.pty.pcx.mad.IMadProjectExternalService;
import com.pty.pcx.pa.IPcxAttachService;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.bill.*;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailSaveQO;
import com.pty.pcx.qo.workflow2.SubmitQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bud.PcxBudBalanceService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pcx.vo.bill.PcxBillBalanceVO;
import com.pty.pcx.vo.bill.PcxBillRelationVO;
import com.pty.pcx.vo.bill.PcxBillSettlementVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;



/**
 * 费用公共处理类
 * <AUTHOR>
 * @since 2024/11/28
 */
@Service
@Slf4j
@Indexed
public class BillExpenseCommonService {

    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;
    @Autowired
    private PcxBillExpCommonDao pcxBillExpCommonDao;
    @Autowired
    private PcxAttachListDao pcxAttachListDao;
    @Autowired
    private PcxAttachListRelationDao pcxAttachListRelationDao;
    @Autowired
    private BatchServiceUtil batchServiceUtil;
    @Autowired
    private PcxBillBalanceDao pcxBillBalanceDao;
    @Autowired
    private IPcxAttachService pcxAttachService;
    @Autowired
    private IProcessService processService;
    @Autowired
    private PcxBillSettlementInfoDao pcxBillSettlementInfoDao;
    @Autowired
    private PcxBillRelationDao pcxBillRelationDao;
    @Autowired
    private IBalanceExternalService balanceExternalService;
    @Autowired
    private IPcxBillPayDetailService pcxBillPayDetailService;
    @Autowired
    private IPcxBasItemService pcxBasItemService;

    @Autowired
    private IMadProjectExternalService madProjectExternalService;
    @Autowired
    private PcxBillAmtApportionDepartmentService pcxBillAmtApportionDepartmentService;
    @Autowired
    private PcxBillAmtApportionDao pcxBillAmtApportionDao;
    @Autowired
    private BillMainService billMainService;
    @Resource
    private PcxEcsComparedResultDao pcxEcsComparedResultDao;

    @Resource
    private IBusinessRuleOptionService businessRuleOptionService;

    @Autowired
    private IEcsBillExternalService ecsBillExternalService;

    public Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMap(List<String> ecsBillIds, PcxBill view) {
        return ecsBillExternalService.getEcsMsgMapByEcsIds(ecsBillIds, view.getFiscal(), view.getAgyCode(), view.getMofDivCode());
    }

    public Integer disposeIsValid(EcsMsgDTO ecsMsgDTO) {
        if (Objects.isNull(ecsMsgDTO)){
            return IsValidEnum.NO_ECS.getCode();
        }
        if (Objects.equals(ecsMsgDTO.getIsValid(), 1)){
            return IsValidEnum.CHECK_REAL.getCode();
        }
        if (Objects.equals(ecsMsgDTO.getIsValid(), 2)){
            return IsValidEnum.CHECK_FAKE.getCode();
        }
        if (Objects.equals(ecsMsgDTO.getIsValid(), 0) || Objects.equals(ecsMsgDTO.getIsValid(), 3)){
            if (Objects.equals(ecsMsgDTO.getIsBillEdited(), 1)){
                return IsValidEnum.MANUAL_MODIFY.getCode();
            }else {
                return IsValidEnum.UN_MODIFY.getCode();
            }
        }
        return IsValidEnum.NO_ECS.getCode();
    }


    @Getter
    public static enum CheckStatus{
        UNCHECKED(-1,"未比对"),
        CHECKED(1,"对比通过"),
        REJECTED(2,"对比异常"),
        NO_NEED_CHECK(3,"无需比对");
        private Integer code;
        private String name;
        CheckStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    public static  String disposeEcsBillType(PcxExpDetailEcsRel rel) {
        if (StringUtil.isEmpty(rel.getEcsBillId()) && StringUtil.isEmpty(rel.getEcsBillType())){
            return PcxConstant.NO_NEED_ECS;
        }
        return rel.getEcsBillType();
    }

    public static Integer disposeCheckStatus(Integer ecsCheckStatus, String ecsBillKind, boolean paperBill) {
        if (Objects.isNull(ecsCheckStatus) || ecsCheckStatus == ComparedResultStatus.UNCHECKED.getCode()){
            //未必对的票，如果打印模式是粘贴单模式，并且是电子票，则无需比对
            if (paperBill && !Objects.equals(ecsBillKind, EcsEnum.BillKind.PAPER.getCode())){
                return CheckStatus.NO_NEED_CHECK.code;
            }
            return CheckStatus.UNCHECKED.code;
        }
        if (ecsCheckStatus == CheckStatus.REJECTED.code){
            return ComparedResultStatus.REJECTED.getCode();
        }
        if (ecsCheckStatus == CheckStatus.NO_NEED_CHECK.code){
            return ComparedResultStatus.NO_NEED_CHECK.getCode();
        }
        return ComparedResultStatus.CHECKED.getCode();
    }



    public void fillComparedResult(PcxBillExpDetailBase detailTravel, PcxEcsComparedResult comparedResult) {
        if (Objects.nonNull(comparedResult)){
            detailTravel.setCompareResultId(comparedResult.getId());
            detailTravel.setCompareEcsBillType(comparedResult.getEcsBillType());
        }
    }
    public boolean isPaperBill(PcxBill pcxBill){
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxBill.getMofDivCode());
        paOptionQO.setAgyCode(pcxBill.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxBill.getFiscal()));
        String value = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.PRINT_BILL.getOptCode());
        return Objects.equals(PcxConstant.PrintBillType.PASTE_BILL, value);
    }

    public boolean isEnableExpenseApportion(PcxBill pcxBill){
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxBill.getMofDivCode());
        paOptionQO.setAgyCode(pcxBill.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxBill.getFiscal()));
        String value = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.IS_ENABLE_EXPENSE_APPORTION.getOptCode());
        return Objects.equals("1", value);
    }


    public List<PcxExpDetailEcsRel> queryEcsRelList(PcxBill bill){
        //更新票信息
        return pcxExpDetailEcsRelService.selectByBillId(bill.getId());
    }

    public List<PcxEcsComparedResult> getBillEcsComparedResult(String billId){
        LambdaQueryWrapper<PcxEcsComparedResult> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PcxEcsComparedResult::getId,
                PcxEcsComparedResult::getEcsId,
                PcxEcsComparedResult::getEcsBillNo,
                PcxEcsComparedResult::getSourceAttachId,
                PcxEcsComparedResult::getEcsBillType);
        lambdaQueryWrapper.eq(PcxEcsComparedResult::getPcxBillId, billId);
        return pcxEcsComparedResultDao.selectList(lambdaQueryWrapper);
    }
    /**
     * 处理费用公共信息
     * @param expBase
     * @param pcxBill
     */
    @Transactional
    public PcxBillExpCommon saveCommonData(PcxBillExpBase expBase, PcxBill pcxBill){
        //查询
        PcxBillExpCommon result = pcxBillExpCommonDao.selectByUnionKey(pcxBill.getId(), expBase.getExpenseCode(),
                pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        if (Objects.isNull(result)){
            //重新补充数据
            PcxBillExpCommon common = new PcxBillExpCommon();
            BeanUtils.copyProperties(expBase,common);
            common.setId(IDGenerator.id());
            common.setBillId(pcxBill.getId());
            common.setAgyCode(pcxBill.getAgyCode());
            common.setFiscal(pcxBill.getFiscal());
            common.setMofDivCode(pcxBill.getMofDivCode());
            //补充金额信息
            if (common.getCheckAmt() == null ||common.getCheckAmt().compareTo(BigDecimal.ZERO) == 0){
                common.setCheckAmt(common.getInputAmt());
            }
            pcxBillExpCommonDao.insert(common);
            return common;
        }else {
            PcxBillExpCommon common = new PcxBillExpCommon();
            BeanUtils.copyProperties(expBase,common);
            common.setId(IDGenerator.id());
            common.setBillId(pcxBill.getId());
            common.setAgyCode(pcxBill.getAgyCode());
            common.setFiscal(pcxBill.getFiscal());
            common.setMofDivCode(pcxBill.getMofDivCode());
            common.setId(result.getId());
            pcxBillExpCommonDao.updateById(common);
            return result;
        }
    }

    @Resource
    private PcxBillAmtApportionDepartmentDao pcxBillAmtApportionDepartmentDao;
    @Resource
    private PcxBudBalanceService pcxBudBalanceService;

    /**
     * 处理费用信息
     * @param qo
     * @param pcxBill
     */
    @Transactional
    public void processExpense(PcxBillQO qo, PcxBill pcxBill) {
        List<PcxBillExpBase> expenseList = qo.getExpenseList();
        //更新费用承担部门
        updateApportionDepartment(pcxBill, expenseList);
        for (PcxBillExpBase expBase : expenseList) {
            BillExpenseService<PcxBillExpBase> billExpenseService = ExpenseBeanUtil.getBean(expBase.getExpenseCode(), pcxBill.getBizType());
            if (billExpenseService != null) {
                //填报岗不更新金额
                if (PositionEnum.MAKE_BILL.getCode().equals(qo.getPositionCode())){
                    expBase.setInputAmt(null);
                    expBase.setCheckAmt(null);
                }
                PcxBillExpBase pcxBillExpBase = billExpenseService.saveOrUpdate(expBase, pcxBill);
                BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> billExpenseDetailService = ExpenseBeanUtil.getDetailBean(expBase.getExpenseCode());
                List<PcxBillExpDetailBase> filteredDetails = qo.getExpenseDetailList().stream().filter(detailBase -> detailBase.getExpDetailCode().startsWith(expBase.getExpenseCode()))
                        .collect(Collectors.toList());
                if (billExpenseDetailService != null && CollectionUtils.isNotEmpty(filteredDetails)) {
                    //填报岗位不更新明细信息
                    if (!PositionEnum.MAKE_BILL.getCode().equals(qo.getPositionCode())
                            || (Objects.nonNull(qo.getExpenseCode()) && (Objects.equals(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.ABROAD.getCode(), qo.getExpenseCode())))
                            || Objects.equals(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.INLANDFEE.getCode(), expBase.getExpenseCode())
                            || Objects.equals(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.MEETING.getCode(), expBase.getExpenseCode())
                            || Objects.equals(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.TRAINING.getCode(), expBase.getExpenseCode())){

                            billExpenseDetailService.saveOrUpdate(pcxBillExpBase, filteredDetails, pcxBill);
                    }
                }
            }
        }
    }

    /**
     * 保存更新附件信息
     * @param pcxBillAttachRelationQOS
     * @param pcxBill
     */
    @Transactional
    public void processAttach(List<PcxBillAttachRelationQO> pcxBillAttachRelationQOS, PcxBill pcxBill) {
        //删除关联附件信息
        pcxAttachListRelationDao.deleteByBillId(pcxBill.getId());
        if (CollectionUtil.isEmpty(pcxBillAttachRelationQOS)) return;
        List<PcxAttachListRelation> relations = Lists.newArrayList();
        //重新添加关联附件信息
        pcxBillAttachRelationQOS.forEach(pcxBillAttachRelationQO -> {
            PcxAttachListRelation pcxAttachListRelation = new PcxAttachListRelation();
            pcxAttachListRelation.setId(IDGenerator.id());
            pcxAttachListRelation.setBillId(pcxBill.getId());
            pcxAttachListRelation.setAttachId(pcxBillAttachRelationQO.getAttachId());
            pcxAttachListRelation.setAttachTypeId(pcxBillAttachRelationQO.getAttachTypeId());
            pcxAttachListRelation.setAttachListId(pcxBillAttachRelationQO.getAttachListId());
            pcxAttachListRelation.setUploadWay(1);
            relations.add(pcxAttachListRelation);
        });
        batchServiceUtil.batchProcess(relations, PcxAttachListRelationDao.class,PcxAttachListRelationDao::insertSelective);
    }


    @Transactional
    public void processBalance(List<PcxBillBalanceQO> pcxBillBalances, PcxBill pcxBill,String positionCode) {
        //只有两个岗位才能编辑预算 其他岗位不能编辑预算
        if (!PositionEnum.isFinance(positionCode)
                && !Objects.equals(positionCode,PositionEnum.MAKE_BILL.getCode())
                && !Objects.equals(positionCode,PositionEnum.BUDGET_CONFIRM.getCode())) return;

        disposeTravelAmtApportion(pcxBill);

        List<PcxBillBalance> relations = processBalance(pcxBillBalances, pcxBill);
        //财务岗通过指标处理冲销借款或者申请
        if (CollectionUtil.isNotEmpty(relations) && PositionEnum.isFinance(positionCode)) {
            if (relations.stream().anyMatch(balance -> BillFuncCodeEnum.LOAN.getCode().equals(balance.getBalanceSource()))) {
                //查询关联的借款单
                List<PcxBillRelation> loanRelations = pcxBillRelationDao.selectList(Wrappers
                        .lambdaQuery(PcxBillRelation.class)
                        .eq(PcxBillRelation::getBillId, pcxBill.getId())
                        .eq(PcxBillRelation::getRelBillFuncCode, BillFuncCodeEnum.LOAN.getCode()));
                if (CollectionUtil.isNotEmpty(loanRelations)) {
                    loanRelations.forEach(loanRelation -> {
                        //处理冲销借款中的使用金额
                        BigDecimal usedAmt = relations.stream()
                                .filter(balance -> loanRelation.getRelBillId().equals(balance.getRelBillId()) && balance.getBalanceType().equals(BalanceTypeEnum.BUD.getCode()))
                                .map(PcxBillBalance::getUsedAmt)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        loanRelation.setUsedAmt(usedAmt);
                    });
                    batchServiceUtil.batchProcess(loanRelations, PcxBillRelationDao.class, PcxBillRelationDao::updateById);
                }
            }
            if (relations.stream().anyMatch(balance -> BillFuncCodeEnum.APPLY.getCode().equals(balance.getBalanceSource()))) {
                //查询关联的申请单
                PcxBillRelation applyRelation = pcxBillRelationDao.selectOne(Wrappers
                        .lambdaQuery(PcxBillRelation.class)
                        .eq(PcxBillRelation::getBillId, pcxBill.getId())
                        .eq(PcxBillRelation::getRelBillFuncCode, BillFuncCodeEnum.APPLY.getCode()));
                if (Objects.nonNull(applyRelation)){
                    BigDecimal usedAmt = relations.stream()
                            .filter(balance -> applyRelation.getRelBillId().equals(balance.getRelBillId()) && balance.getBalanceType().equals(BalanceTypeEnum.BUD.getCode()))
                            .map(PcxBillBalance::getUsedAmt)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    applyRelation.setUsedAmt(usedAmt);
                    pcxBillRelationDao.updateById(applyRelation);
                }
            }
        }
    }

    private void disposeTravelAmtApportion(PcxBill pcxBill) {
        if (!Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.TRAVEL.getCode())){
            return;
        }
        List<PcxBillAmtApportion> amtApportions = pcxBillAmtApportionDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
                .eq(PcxBillAmtApportion::getBillId, pcxBill.getId()));
        if (CollectionUtil.isNotEmpty(amtApportions)){
            BigDecimal checkAmt = pcxBill.getCheckAmt();
            BigDecimal lastAmt = pcxBill.getCheckAmt();
            //避免金额小于等于0报错
            BigDecimal reduce = amtApportions.stream().map(PcxBillAmtApportion::getApportionAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (reduce.compareTo(BigDecimal.ZERO)<=0){
                if (checkAmt.compareTo(BigDecimal.ZERO)<=0){
                    reduce = BigDecimal.ONE;
                }else{
                    reduce = checkAmt;
                }
            }
            for (int i = 0; i < amtApportions.size(); i++) {
                PcxBillAmtApportion amtApportion = amtApportions.get(i);
                if (i == amtApportions.size() - 1){
                    amtApportion.setApportionAmt(lastAmt);
                }else{
                    BigDecimal rate = amtApportion.getApportionAmt().divide(reduce, 2, RoundingMode.HALF_UP);
                    BigDecimal amt = checkAmt.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                    lastAmt = lastAmt.subtract(amt);
                    amtApportion.setApportionAmt(amt);
                }
                pcxBillAmtApportionDao.updateById(amtApportion);
            }

        }
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = pcxBillAmtApportionDepartmentService.selectByBillId(pcxBill.getId());
        if (CollectionUtil.isNotEmpty(amtApportionDepartments)){
            //避免金额小于等于0报错
            BigDecimal reduce = amtApportionDepartments.stream().map(PcxBillAmtApportionDepartment::getDepartmentAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal checkAmt = pcxBill.getCheckAmt();
            BigDecimal lastAmt = pcxBill.getCheckAmt();
            if (reduce.compareTo(BigDecimal.ZERO)<=0){
                if (checkAmt.compareTo(BigDecimal.ZERO)<=0){
                    reduce = BigDecimal.ONE;
                }else{
                    reduce = checkAmt;
                }
            }
            for (int i = 0; i < amtApportionDepartments.size(); i++) {
                PcxBillAmtApportionDepartment department = amtApportionDepartments.get(i);
                if (i == amtApportionDepartments.size() - 1){
                    department.setDepartmentAmt(lastAmt);
                }else{
                    BigDecimal rate = department.getDepartmentAmt().divide(reduce, 2, RoundingMode.HALF_UP);
                    BigDecimal amt = checkAmt.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                    lastAmt = lastAmt.subtract(amt);
                    department.setDepartmentAmt(amt);
                }
            }
            pcxBillAmtApportionDepartmentService.batchUpdate(amtApportionDepartments);
        }
    }

    /**
     * 保存更新指标信息
     * @param pcxBillBalances
     * @param pcxBill
     */
    public List<PcxBillBalance> processBalance(List<PcxBillBalanceQO> pcxBillBalances, PcxBill pcxBill) {
        pcxBillBalanceDao.deleteByBillId(pcxBill.getId(),null);
        if (CollectionUtil.isEmpty(pcxBillBalances)) return Lists.newArrayList();
        //重新添加关联信息
        List<PcxBillBalance> relations = Lists.newArrayList();
        pcxBillBalances.forEach(pcxBillBalanceQO -> {
            PcxBillBalance pcxBillBalance = new PcxBillBalance();
            BeanUtils.copyProperties(pcxBillBalanceQO,pcxBillBalance);
            pcxBillBalance.setBillId(pcxBill.getId());
            pcxBillBalance.setCreator(pcxBill.getCreator());
            pcxBillBalance.setCreatorName(pcxBill.getCreatorName());
            pcxBillBalance.setCreatedTime(DateUtil.nowTime());
            pcxBillBalance.setAgyCode(pcxBill.getAgyCode());
            pcxBillBalance.setFiscal(pcxBill.getFiscal());
            pcxBillBalance.setMofDivCode(pcxBill.getMofDivCode());
            pcxBillBalance.setBillFuncCode(pcxBill.getBillFuncCode());
            if (StringUtil.isEmpty(pcxBillBalance.getBalanceSource())){
                pcxBillBalance.setBalanceSource(pcxBill.getBillFuncCode());
            }

            //整单分摊按部门去分摊
            if (pcxBill.getApportionType() == 1 && (StringUtil.isEmpty(pcxBillBalance.getExpenseCode()))){
                List<PcxBillAmtApportionDepartment> billAmtApportionDepartments = pcxBillAmtApportionDepartmentService.selectByBillIdAndDept(pcxBill.getId(),null,pcxBillBalance.getDepartmentCode());
                if (CollectionUtil.isNotEmpty(billAmtApportionDepartments)){
                    Optional<PcxBillAmtApportionDepartment> first = billAmtApportionDepartments.stream().filter(item -> !Objects.equals(item.getExpenseTypeCode(), "*")).findFirst();
                    if (first.isPresent()){
                        PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment = first.get();
                        //如果是整单分摊的话 需要补充丢失的费用信息
                        pcxBillBalance.setExpenseCode(pcxBillAmtApportionDepartment.getExpenseTypeCode());
                        pcxBillBalance.setExpenseName(pcxBillAmtApportionDepartment.getExpenseTypeName());

                    }
                    billAmtApportionDepartments.forEach(item ->{
                        item.setBudDepartmentCode(pcxBillBalance.getBudDepartmentCode());
                        item.setBudDepartmentName(pcxBillBalance.getBudDepartmentName());
                    });
                    pcxBillAmtApportionDepartmentService.batchUpdate(billAmtApportionDepartments);
                }
            }

            //部门分摊一定按部门费用查询
            if (pcxBill.getApportionType() == 2){
                List<PcxBillAmtApportionDepartment> billAmtApportionDepartments = pcxBillAmtApportionDepartmentService.selectByBillIdAndDept(pcxBill.getId(),pcxBillBalance.getExpenseCode(),pcxBillBalance.getDepartmentCode());
                if (CollectionUtil.isNotEmpty(billAmtApportionDepartments)){
                    billAmtApportionDepartments.forEach(item ->{
                        item.setBudDepartmentCode(pcxBillBalance.getBudDepartmentCode());
                        item.setBudDepartmentName(pcxBillBalance.getBudDepartmentName());
                    });
                    pcxBillAmtApportionDepartmentService.batchUpdate(billAmtApportionDepartments);
                }
            }

            if(Objects.equals(pcxBillBalance.getExpenseCode(),PcxConstant.UNIVERSAL_EXPENSE_CODE)){
                pcxBillBalance.setExpenseName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
            }
            pcxBillBalance.setId(IDGenerator.id());
            relations.add(pcxBillBalance);
        });
        batchServiceUtil.batchProcess(relations, PcxBillBalanceDao.class,PcxBillBalanceDao::insert);
        return relations;
    }


    /**
     * 保存更新指标信息
     * @param pcxBillBalances
     * @param pcxBill
     */
    @Transactional
    public void processBudget(List<PcxBillBalanceQO> pcxBillBalances, PcxBill pcxBill) {
        if (CollectionUtil.isEmpty(pcxBillBalances)) return;
        pcxBillBalanceDao.deleteByBillId(pcxBill.getId(),pcxBillBalances.get(0).getBalanceType());
        //重新添加关联信息
        List<PcxBillBalance> relations = Lists.newArrayList();
        pcxBillBalances.forEach(pcxBillBalanceQO -> {
            PcxBillBalance pcxBillBalance = new PcxBillBalance();
            BeanUtils.copyProperties(pcxBillBalanceQO,pcxBillBalance);
            pcxBillBalance.setId(IDGenerator.id());
            pcxBillBalance.setBillId(pcxBill.getId());
            pcxBillBalance.setCreator(pcxBill.getCreator());
            pcxBillBalance.setCreatorName(pcxBill.getCreatorName());
            pcxBillBalance.setCreatedTime(DateUtil.nowTime());
            pcxBillBalance.setAgyCode(pcxBill.getAgyCode());
            pcxBillBalance.setFiscal(pcxBill.getFiscal());
            pcxBillBalance.setMofDivCode(pcxBill.getMofDivCode());
            relations.add(pcxBillBalance);
        });
        batchServiceUtil.batchProcess(relations, PcxBillBalanceDao.class,PcxBillBalanceDao::insert);
    }

    /**
     * 保存更新结算信息
     * @param pcxBillSettlementQOS
     * @param pcxBill
     */
    @Transactional
    public void processSettlement(Map<String, List<PcxBillSettlementQO>> pcxBillSettlementQOS, PcxBill pcxBill,String positionCode) {
        //删除结算信息
        pcxBillSettlementInfoDao.deleteByBillId(pcxBill.getId());
        if (Objects.isNull(pcxBillSettlementQOS))  return;
        Set<String> keySet = pcxBillSettlementQOS.keySet();
        for (String key : keySet){
            List<PcxBillSettlementQO> settlementCard = pcxBillSettlementQOS.get(key);
            if (CollectionUtil.isNotEmpty(settlementCard)){
                settlementCard.forEach(settlement -> {
                    settlement.setId(IDGenerator.id());
                    settlement.setSettlementType(key);
                    settlement.setSettlementName(SettlementTypeEnum.getNameByCode(key));
                    settlement.setBillId(pcxBill.getId());
                    settlement.setAgyCode(pcxBill.getAgyCode());
                    settlement.setMofDivCode(pcxBill.getMofDivCode());
                    settlement.setFiscal(pcxBill.getFiscal());
                    if (Objects.nonNull(settlement.getAccountType())){
                        settlement.setPayType(settlement.getAccountType() == 2?"0":"1");
                    }
                    //如果是制单岗位-并且非劳务非则不赋值核定金额
                    if (PositionEnum.MAKE_BILL.getCode().equals(positionCode) && !Objects.equals(settlement.getIsLabour(), PubConstant.STR_LOGIC_TRUE)) {
                        settlement.setCheckAmt(settlement.getInputAmt());
                    }

                    if (settlement.getCheckAmt() == null ||settlement.getCheckAmt().compareTo(BigDecimal.ZERO) == 0){
                        settlement.setCheckAmt(settlement.getInputAmt());
                    }

                });
                batchServiceUtil.batchProcess(settlementCard, PcxBillSettlementInfoDao.class,PcxBillSettlementInfoDao::insert);
            }
        }
    }

    /**
     * 保存更新关联单据信息(此处只处理借款单)
     * @param pcxBillRelationQOS
     * @param pcxBill
     */
    @Transactional
    public void processLoanRelation(List<PcxBillRelationQO> pcxBillRelationQOS, PcxBill pcxBill,String positionCode) {
        if (PositionEnum.isFinance(positionCode)){
            //财务审核岗处理冲销统一通过指标处理
            return;
        }
        pcxBillRelationDao.deleteByBillId(pcxBill.getId(), BillFuncCodeEnum.LOAN.getCode());
        if (CollectionUtil.isEmpty(pcxBillRelationQOS)) return;
        //重新添加关联信息
        ArrayList<PcxBillRelation> relations = Lists.newArrayList();
        pcxBillRelationQOS.forEach(pcxBillRelationQO -> {
            PcxBillRelation billRelation = new PcxBillRelation();
            BeanUtils.copyProperties(pcxBillRelationQO,billRelation);
            billRelation.setId(IDGenerator.id());
            billRelation.setBillId(pcxBill.getId());
            billRelation.setBillFuncCode(pcxBill.getBillFuncCode());
            billRelation.setRelBillFuncCode(BillFuncCodeEnum.LOAN.getCode());
            if (billRelation.getUsedAmt().compareTo(BigDecimal.ZERO) > 0){
                //冲销金额大于0的时候才会展示
                relations.add(billRelation);
            }
        });
        if (CollectionUtil.isNotEmpty(relations)){
            batchServiceUtil.batchProcess(relations, PcxBillRelationDao.class,PcxBillRelationDao::insert);
        }
    }

        /**
     * 保存更新关联单据信息(此处只处理会议、培训、公函等)
     * @param pcxBillRelationQo
     * @param pcxBill
     */
    @Transactional
    public void processPlanRelation(PcxBillRelationQO pcxBillRelationQo, PcxBill pcxBill, String positionCode) {
        pcxBillRelationDao.deleteByBillId(pcxBill.getId(), BillFuncCodeEnum.LOAN.getCode());
        if(Objects.isNull(pcxBillRelationQo)){
            throw new CommonException("计划不能为空");
        }
        //重新添加关联信息
        ArrayList<PcxBillRelation> relations = Lists.newArrayList();
        PcxBillRelation billRelation = new PcxBillRelation();
        BeanUtils.copyProperties(pcxBillRelationQo, billRelation);
        billRelation.setId(IDGenerator.id());
        billRelation.setBillId(pcxBill.getId());
        billRelation.setBillFuncCode(pcxBill.getBillFuncCode());
        billRelation.setRelBillFuncCode(BillFuncCodeEnum.PLAN.getCode());
        relations.add(billRelation);
        if (CollectionUtil.isNotEmpty(relations)) {
            batchServiceUtil.batchProcess(relations, PcxBillRelationDao.class, PcxBillRelationDao::insert);
        }
    }

    /**
     * 处理申请单据
     * @param qo
     * @param pcxBill
     */
    public void processApplyRelation(PcxBillQO qo, PcxBill pcxBill) {
        PcxBillRelationQO apply = qo.getApply();
        pcxBillRelationDao.deleteByBillId(pcxBill.getId(), BillFuncCodeEnum.APPLY.getCode());
        if (Objects.isNull(apply)) return;
        //重新添加关联信息
        PcxBillRelation billRelation = new PcxBillRelation();
        BeanUtils.copyProperties(apply,billRelation);
        billRelation.setId(IDGenerator.id());
        billRelation.setBillId(pcxBill.getId());
        billRelation.setBillFuncCode(pcxBill.getBillFuncCode());
        billRelation.setRelBillFuncCode(BillFuncCodeEnum.APPLY.getCode());
        //申请的使用金额为当前单据的核定金额
        PcxBill applyBill = billMainService.view(apply.getRelBillId());
        if (Objects.nonNull(applyBill)){
            if (pcxBill.getCheckAmt().compareTo(applyBill.getCheckAmt()) > 0){
                billRelation.setUsedAmt(applyBill.getCheckAmt());
            }else {
                billRelation.setUsedAmt(pcxBill.getCheckAmt());
            }
        }
        billRelation.setAgyCode(StringUtil.isEmpty(billRelation.getAgyCode()) ? qo.getAgyCode():billRelation.getAgyCode());
        billRelation.setFiscal(StringUtil.isEmpty(billRelation.getFiscal()) ? qo.getFiscal():billRelation.getFiscal());
        billRelation.setMofDivCode(StringUtil.isEmpty(billRelation.getMofDivCode()) ? qo.getMofDivCode():billRelation.getMofDivCode());
        pcxBillRelationDao.insert(billRelation);

    }

    /**
     * 送审
     * @param pcxBill
     */
    public void processApprove(PcxBill pcxBill,String comment) {
        SubmitQO startParam = new SubmitQO();
        startParam.setBillFuncCode(pcxBill.getBillFuncCode());
        startParam.setBillId(pcxBill.getId());
        startParam.setAgyCode(pcxBill.getAgyCode());
        startParam.setMofDivCode(pcxBill.getMofDivCode());
        startParam.setTenantId(PtyContext.getTenantId());
        startParam.setComment(comment);
        CheckMsg<Void> voidCheckMsg = processService.submitTask(startParam);
        if (!voidCheckMsg.isSuccess()){
            throw new CommonException("送审失败:" + voidCheckMsg.getMsgInfo());
        }

    }


    /**
     * 处理单据支付明细
     * @param saveQO
     * @param pcxBill
     */
    public void processPayDetail(PayDetailSaveQO saveQO, PcxBill pcxBill) {
        pcxBillPayDetailService.savePayDetails(saveQO);
    }

    /**
     * 获取费用信息
     * @param pcxBill
     * @return
     */
    public List<PcxBillExpBase> getExpenseList(PcxBill pcxBill) {
        if (StringUtil.isEmpty(pcxBill.getExpenseCodes())) return Lists.newArrayList();
        //获取所有子类的实现类
        String[] split = StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",");
        if (split.length <1) return Lists.newArrayList();
        List<PcxBillExpBase> list = Lists.newArrayList();
        Arrays.stream(split).forEach(expenseCode -> {
            //加载各个费用的实现类查询费用信息
            BillExpenseService<PcxBillExpBase> billExpenseService = ExpenseBeanUtil.getBean(expenseCode, pcxBill.getBizType());
            if (Objects.nonNull(billExpenseService)){
                PcxBillExpBase expBase =  billExpenseService.view(expenseCode, pcxBill);
                if (Objects.nonNull(expBase)){
                    list.add(expBase);
                }
            }
        });
        return list;
    }

    /**
     * 删除费用信息
     * @param pcxBill
     */
    public void deleteExpenseList(PcxBill pcxBill) {
        //获取所有子类的实现类
        String[] split = StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",");
        if (split.length <1) return;
        Arrays.stream(split).forEach(expenseCode -> {
            //加载各个费用的实现类查询费用信息
            BillExpenseService<PcxBillExpBase> billExpenseService = ExpenseBeanUtil.getBean(expenseCode, pcxBill.getBizType());
            if (Objects.nonNull(billExpenseService)){
                billExpenseService.delete(expenseCode, pcxBill);
            }
        });

    }

    /**
     * 获取明细信息
     * @param pcxBill
     * @return
     */
    public List<PcxBillExpDetailBase> getExpenseDetailList(PcxBill pcxBill) {
        //加载各个费用的实现类查询费用明细信息
        if (Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.COMMON.getCode())){
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailService = ExpenseBeanUtil.getDetailBean(PcxConstant.UNIVERSAL_EXPENSE_CODE);
            return detailService.view("", pcxBill);
        }
        //获取所有子类的实现类
        if (StringUtil.isEmpty(pcxBill.getExpenseCodes())) {
             pcxBill.setExpenseCodes("");
        }
        String[] split = StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",");
        if (split.length <1) return Lists.newArrayList();
        List<PcxBillExpDetailBase> list = Lists.newArrayList();
        Arrays.stream(split).forEach(expenseCode -> {
            //加载各个费用的实现类查询费用明细信息
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailService = ExpenseBeanUtil.getDetailBean(expenseCode);
            List<PcxBillExpDetailBase> expBase;
            if (Objects.nonNull(detailService)){
                if (Objects.equals("*",expenseCode)){
                    expBase = detailService.view("", pcxBill);
                }else {
                    expBase = detailService.viewByExpenseCode(expenseCode, pcxBill);
                }
                if (CollectionUtils.isNotEmpty(expBase)){
                    for (PcxBillExpDetailBase detailBase : expBase) {
                        disposeTaxRate(detailBase);
                    }
                    list.addAll(expBase);
                }
            }
        });
        return list;
    }

    private void disposeTaxRate(PcxBillExpDetailBase item) {
//        if (item.getTaxRate().compareTo(BigDecimal.ONE) < 0){
//            item.setTaxRate(new BigDecimal(100).multiply(item.getTaxRate()).setScale(2, RoundingMode.HALF_UP));
//        }
    }

    /**
     * 删除明细信息
     * @param pcxBill
     */
    public void deleteExpenseDetailList(PcxBill pcxBill) {
        //获取所有子类的实现类
        String[] split = StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",");
        if (split.length <1) return;
        Arrays.stream(split).forEach(expenseCode -> {
            //加载各个费用的实现类查询费用明细信息
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailService = ExpenseBeanUtil.getDetailBean(expenseCode);
            if (Objects.nonNull(detailService)){
                detailService.deleteByExpenseCode(expenseCode, pcxBill);
            }
        });
    }

    /**
     * 获取附件信息
     * @param pcxBill
     * @return
     */
    public List<PcxBillAttachRelationVO> getAttachList(PcxBill pcxBill) {
        PcxAttachListRelation pcxAttachListRelation = new PcxAttachListRelation();
        pcxAttachListRelation.setBillId(pcxBill.getId());
        pcxAttachListRelation.setBillIds(pcxBill.getIds());
        List<PcxAttachListRelation> attachListRelations = pcxAttachListRelationDao.select(pcxAttachListRelation);
        //根据attachListId 进行分组,返回map<String,List<PcxAttachListRelation>>
        Map<String,List<PcxAttachListRelation>> map = attachListRelations.stream().filter(r -> StringUtils.isNoneBlank(r.getAttachListId())).collect(Collectors.groupingBy(PcxAttachListRelation::getAttachListId));
        List<PcxBillAttachRelationVO> result = Lists.newArrayList();
        map.keySet().forEach(key->{
            List<PcxAttachListRelation> list = map.get(key);
            list.forEach(relation->{
                PcxBillAttachRelationVO pcxBillAttachRelationVO = new PcxBillAttachRelationVO();
                BeanUtils.copyProperties(relation,pcxBillAttachRelationVO);
                //查询费用信息
                PcxAttachList attachList = new PcxAttachList();
                attachList.setAttachListId(relation.getAttachListId());
                PcxAttachList pcxAttachList = pcxAttachListDao.selectById(attachList);
                if (Objects.nonNull(pcxAttachList)){
                    pcxBillAttachRelationVO.setExpenseCode(pcxAttachList.getFuncCode());
                    pcxBillAttachRelationVO.setAttachListName(pcxAttachList.getAttachListName());
                    pcxBillAttachRelationVO.setAttachTypeId(pcxAttachList.getAttachTypeId());
                } else {
                    //统一到其他附件中
                    PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
                    pcxBasItemQO.setItemCode(pcxBill.getItemCode());
                    pcxBasItemQO.setMofDivCode(pcxBill.getMofDivCode());
                    pcxBasItemQO.setAgyCode(pcxBill.getAgyCode());
                    pcxBasItemQO.setFiscal(pcxBill.getFiscal());
                    pcxBasItemQO.setBilltypeCode(pcxBill.getBillFuncCode());
                    PcxBasItemVO pcxBasItemVO = pcxBasItemService.selectByItemCode(pcxBasItemQO);
                    // 如果是通用类
                    if(PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxBill.getItemCode())){
                        pcxBasItemVO.setExpenseCodes(Collections.singletonList(PcxConstant.UNIVERSAL_EXPENSE_CODE));
                        pcxBillAttachRelationVO.setAttachListName("附件");
                        pcxBillAttachRelationVO.setAttachTypeId("PCX");
                        pcxBillAttachRelationVO.setAttachListId("PCX");
                    }else {
                        pcxBillAttachRelationVO.setExpenseCode(pcxBasItemVO.getExpenseCodes().get(0));
                        pcxBillAttachRelationVO.setAttachListName("附件");
                        pcxBillAttachRelationVO.setAttachTypeId("PCX");
                        pcxBillAttachRelationVO.setAttachListId("PCX");
                    }
                }
                PaAttach paAttach = pcxAttachService.getListById(relation.getAttachId());
                if (Objects.nonNull(paAttach)){
                    pcxBillAttachRelationVO.setFileName(paAttach.getFileName());
                    pcxBillAttachRelationVO.setCreator(paAttach.getCreator());
                }
                result.add(pcxBillAttachRelationVO);
            });
        });
        return result;
    }

    public List<MadProjectDTO> getProject(List<PcxBillBalanceVO> fundSource, PcxBill pcxBill){
        if (CollectionUtil.isEmpty(fundSource)) return null;
        List<String> projectCodes = fundSource.stream().map(PcxBillBalanceVO::getProjectCode).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(projectCodes)) return null;
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setAgyCode(pcxBill.getAgyCode());
        pcxBaseDTO.setMofDivCode(pcxBill.getMofDivCode());
        pcxBaseDTO.setFiscal(pcxBill.getFiscal());
        return madProjectExternalService.selectByMadCodes(pcxBaseDTO,projectCodes);
    }

    public List<PcxBillBalanceVO> getFundSource(PcxBill pcxBill) {
        List<PcxBillBalance> pcxBillBalances = pcxBillBalanceDao.selectList(Wrappers
                .lambdaQuery(PcxBillBalance.class)
                .eq(PcxBillBalance::getBillId, pcxBill.getId()));

        if (CollectionUtil.isEmpty(pcxBillBalances)) return null;

        //查询预算信息
        List<BudBalanceDTO> balanceDTOS = balanceExternalService.getBalanceByIds(pcxBillBalances.stream().map(PcxBillBalance::getBalanceId).collect(Collectors.toList()));
        //转成map key是id value是 对象
        Map<String,BudBalanceDTO> balanceRel = balanceDTOS.stream().collect(Collectors.toMap(BudBalanceDTO::getBalanceId, Function.identity(), (a, b) -> a));
        List<PcxBillBalanceVO> billBalanceVOS = pcxBillBalances.stream().map(item -> {
            PcxBillBalanceVO pcxBillBalanceVO = new PcxBillBalanceVO();
            //是否有其他数据展示
            BeanUtils.copyProperties(item, pcxBillBalanceVO);
            if (Objects.nonNull(balanceRel.get(pcxBillBalanceVO.getBalanceId()))) {
                BudBalanceDTO budBalanceDTO = balanceRel.get(pcxBillBalanceVO.getBalanceId());
                pcxBillBalanceVO.setBalanceAmt(budBalanceDTO.getBalanceAmt());
                pcxBillBalanceVO.setTotalAmt(budBalanceDTO.getTotalAmt());
            }
            return pcxBillBalanceVO;
        }).collect(Collectors.toList());
        return billBalanceVOS;
    }

    /**
     * 获取结算信息
     * @param pcxBill
     * @return
     */
    public Map<String, List<PcxBillSettlementVO>> getSettlementGroup(PcxBill pcxBill) {
        List<PcxBillSettlement> pcxBillSettlements = pcxBillSettlementInfoDao.selectList(Wrappers
                .lambdaQuery(PcxBillSettlement.class)
                .eq(PcxBillSettlement::getBillId, pcxBill.getId())
                .eq(PcxBillSettlement::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillSettlement::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillSettlement::getMofDivCode, pcxBill.getMofDivCode()));
        if (CollectionUtil.isEmpty(pcxBillSettlements)) return null;
        List<PcxBillSettlementVO> collect = pcxBillSettlements.stream().map(item -> {
            PcxBillSettlementVO pcxBillSettlementVO = new PcxBillSettlementVO();
            //是否有其他数据展示
            BeanUtils.copyProperties(item, pcxBillSettlementVO);
            return pcxBillSettlementVO;
        }).collect(Collectors.toList());
        Map<String, List<PcxBillSettlementVO>> collectMap = collect.stream().collect(Collectors.groupingBy(PcxBillSettlementVO::getSettlementType));
        return collectMap;
    }


    /**
     * 获取结算信息
     * @param pcxBill
     * @return
     */
    public List<PcxBillSettlementVO> getSettlement(PcxBill pcxBill) {
        List<PcxBillSettlement> pcxBillSettlements = pcxBillSettlementInfoDao.selectList(Wrappers
                .lambdaQuery(PcxBillSettlement.class)
                .eq(PcxBillSettlement::getBillId, pcxBill.getId())
                .eq(PcxBillSettlement::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillSettlement::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillSettlement::getMofDivCode, pcxBill.getMofDivCode()));
        if (CollectionUtil.isEmpty(pcxBillSettlements)) return null;
        return pcxBillSettlements.stream().map(item->{
            PcxBillSettlementVO pcxBillSettlementVO = new PcxBillSettlementVO();
            //是否有其他数据展示
            BeanUtils.copyProperties(item,pcxBillSettlementVO);
            return pcxBillSettlementVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取借款信息
     * @param pcxBill
     * @return
     */
    public List<PcxBillRelationVO> getLoanRelation(PcxBill pcxBill) {
        List<PcxBillRelation> billRelations = pcxBillRelationDao.selectList(Wrappers
                .lambdaQuery(PcxBillRelation.class)
                .eq(PcxBillRelation::getBillId, pcxBill.getId())
                .eq(PcxBillRelation::getRelBillFuncCode, BillFuncCodeEnum.LOAN.getCode()));
        if (CollectionUtil.isEmpty(billRelations)) return Collections.emptyList();
        return billRelations.stream().map(item->{
            PcxBillRelationVO relationVO = new PcxBillRelationVO();
            //是否有其他数据展示
            BeanUtils.copyProperties(item,relationVO);
            //查询单据信息
            PcxBill view = billMainService.view(item.getRelBillId());
            if (Objects.nonNull(view)){
                relationVO.setReason(view.getReason());
                relationVO.setUnRepayAmt(view.getCheckAmt().subtract(view.getLoanAmt()));
                relationVO.setInputAmt(view.getInputAmt());
                relationVO.setLoanAmt(view.getLoanAmt());
                relationVO.setCheckAmt(view.getCheckAmt());
                relationVO.setClaimantName(view.getClaimantName());
                relationVO.setClaimantCode(view.getClaimantCode());
                relationVO.setCreateTime(view.getCreatedTime());
                relationVO.setAuditTime(view.getAuditTime());
                relationVO.setPayTime(view.getPayTime());
                List<PcxBillSettlementVO> settlement = getSettlement(pcxBill);
                if (CollectionUtil.isNotEmpty(settlement)){
                    Set<String> distinct = settlement.stream().map(PcxBillSettlementVO::getSettlementName).collect(Collectors.toSet());
                    relationVO.setSettlementList(distinct);
                }
            }
            return relationVO;
        }).collect(Collectors.toList());
    }


    /**
     * 获取借款信息
     * @param pcxBill
     * @return
     */
    public PcxBillRelationVO getPlanRelation(PcxBill pcxBill) {
        List<PcxBillRelation> billRelations = pcxBillRelationDao.selectList(Wrappers
                .lambdaQuery(PcxBillRelation.class)
                .eq(PcxBillRelation::getBillId, pcxBill.getId())
                .eq(PcxBillRelation::getRelBillFuncCode, BillFuncCodeEnum.PLAN.getCode()));
        if (CollectionUtil.isEmpty(billRelations)) return new PcxBillRelationVO();

        PcxBillRelationVO relationVO = new PcxBillRelationVO();
        //是否有其他数据展示
        BeanUtils.copyProperties(billRelations.get(0),relationVO);
        //查询单据信息
        PcxBill view = billMainService.view(relationVO.getRelBillId());
        if (Objects.nonNull(view)){
            relationVO.setReason(view.getReason());
            relationVO.setUnRepayAmt(view.getCheckAmt().subtract(view.getLoanAmt()));
            relationVO.setInputAmt(view.getInputAmt());
            relationVO.setLoanAmt(view.getLoanAmt());
            relationVO.setCheckAmt(view.getCheckAmt());
            relationVO.setClaimantName(view.getClaimantName());
            relationVO.setClaimantCode(view.getClaimantCode());
            relationVO.setCreateTime(view.getCreatedTime());
            relationVO.setAuditTime(view.getAuditTime());
            relationVO.setPayTime(view.getPayTime());
            List<PcxBillSettlementVO> settlement = getSettlement(pcxBill);
            if (CollectionUtil.isNotEmpty(settlement)){
                Set<String> distinct = settlement.stream().map(PcxBillSettlementVO::getSettlementName).collect(Collectors.toSet());
                relationVO.setSettlementList(distinct);
            }
        }
        return relationVO;
    }

    /**
     * 获取申请信息
     * @param pcxBill
     * @return
     */
    public PcxBillRelationVO getApplyRelation(PcxBill pcxBill) {
        PcxBillRelation billRelation = pcxBillRelationDao.selectOne(Wrappers
                .lambdaQuery(PcxBillRelation.class)
                .eq(PcxBillRelation::getBillId, pcxBill.getId())
                .eq(PcxBillRelation::getRelBillFuncCode, BillFuncCodeEnum.APPLY.getCode()));
        if (Objects.isNull(billRelation)) return null;
        PcxBillRelationVO relationVO = BeanUtil.copyProperties(billRelation, PcxBillRelationVO.class);
        PcxBill applyBill = billMainService.view(billRelation.getRelBillId());
        if(Objects.nonNull(applyBill)) {
            relationVO.setApplyAmt(applyBill.getCheckAmt());
            relationVO.setUnRepayAmt(applyBill.getCheckAmt().subtract(applyBill.getLoanAmt()));
            relationVO.setInputAmt(applyBill.getInputAmt());
            relationVO.setLoanAmt(applyBill.getLoanAmt());
            relationVO.setCheckAmt(applyBill.getCheckAmt());
        }
        return relationVO;
    }

    /**
     * 查询指标下的单据信息
     * @param balanceIds
     * @param billFuncCode
     * @return
     */
    public List<PcxBillBalanceDTO> selectByBalanceId(List<String> balanceIds, String billFuncCode) {
        if (CollectionUtil.isEmpty(balanceIds)) {
            return Lists.newArrayList();
        }
        List<PcxBillBalanceDTO>  resultList = pcxBillBalanceDao.selectByBalanceId(billFuncCode, balanceIds);
        // 按照日期排序
        List<PcxBillBalanceDTO> sortList = resultList.stream()
                .sorted(Comparator.comparing(PcxBillBalanceDTO::getTransDate, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());
        return sortList;
    }


    private void updateApportionDepartment(PcxBill pcxBill, List<PcxBillExpBase> expenseList) {
        boolean enableExpenseApportion = isEnableExpenseApportion(pcxBill);
        if (!enableExpenseApportion){
            String departmentCode = "";
            String departmentName = "";
            for (PcxBillExpBase expBase : expenseList) {
                if (StringUtil.isNotEmpty(expBase.getDepartmentCode())){
                    departmentCode = expBase.getDepartmentCode();
                    departmentName = expBase.getDepartmentName();
                    break;
                }
            }
            if (StringUtil.isNotEmpty(departmentCode) && StringUtil.isNotEmpty(pcxBill.getId())){
                for (PcxBillExpBase expBase : expenseList) {
                    expBase.setDepartmentCode(departmentCode);
                    expBase.setDepartmentName(departmentName);
                }
                PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
                pcxBalancesQO.setFiscal(pcxBill.getFiscal());
                pcxBalancesQO.setAgyCode(pcxBill.getAgyCode());
                pcxBalancesQO.setMofDivCode(pcxBill.getMofDivCode());
                pcxBalancesQO.setDepartmentCode(departmentCode);
                BaseDataVo deFaultDepartment = pcxBudBalanceService.getDeFaultDepartment(pcxBalancesQO);
                String finDepartmentCode = departmentCode;
                String budDepartmentCode = Objects.nonNull(deFaultDepartment) ?deFaultDepartment.getCode():"";
                String budDepartmentName = Objects.nonNull(deFaultDepartment) ?deFaultDepartment.getName():"";
                List<PcxBillAmtApportionDepartment> amtApportionDepartments = pcxBillAmtApportionDepartmentDao.selectByBillId(pcxBill.getId());
                if (CollectionUtil.isNotEmpty(amtApportionDepartments) && amtApportionDepartments.stream().anyMatch(item->!Objects.equals(item.getDepartmentCode(), finDepartmentCode))){
                    for (PcxBillAmtApportionDepartment amtApportionDepartment : amtApportionDepartments) {
                        amtApportionDepartment.setDepartmentCode(departmentCode);
                        amtApportionDepartment.setDepartmentName(departmentName);
                        amtApportionDepartment.setBudDepartmentCode(budDepartmentCode);
                        amtApportionDepartment.setBudDepartmentName(budDepartmentName);
                    }
                    batchServiceUtil.batchProcess(amtApportionDepartments, PcxBillAmtApportionDepartmentDao.class, PcxBillAmtApportionDepartmentDao::updateById);
                }
            }
            // 更新主单据的费用承担部门
            pcxBill.setExpDepartmentCodes(departmentCode);
            pcxBill.setExpDepartmentNames(departmentName);
            billMainService.updateById(pcxBill);
        }
    }
}
