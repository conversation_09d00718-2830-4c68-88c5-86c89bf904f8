package com.pty.pcx.service.impl.positionblock.block;

import com.alibaba.fastjson.JSONObject;
import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.api.positionblock.IPcxBlockService;
import com.pty.pcx.common.constant.FundPositionEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.enu.block.BlockBeanEnum;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.StringUtil;
import com.pty.pcx.common.util.BudgetCtrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class DefaultBlockServiceImpl extends AbstractBlockService implements IPcxBlockService {


    @Autowired
    private IPcxBasExpTypeService expTypeService;

    /*****
     * 当找不到对应块儿的BEAN_NAME时，使用此块儿
     *   1. 先根据BlockBeanEnum中的配置，判断是否需要走拼装properties的逻辑
     *   2. 如果需要，根据formCode分组，然后拼装properties
     * @param qo
     * @return
     */
    @Override
    public List<PcxBlockVO> getBlockInfo(PcxBlockCondQO qo) {
        if(ObjectUtils.isEmpty(qo)|| ObjectUtils.isEmpty(qo.getPcxBasItemVO())){
            log.error("参数不正确:{}", JSONObject.toJSONString(qo));
            return new ArrayList<>();
        }
        BlockBeanEnum blockBeanEnum = BlockBeanEnum.getBlockMap().get(qo.getClassifyCode());
        if (!StringUtil.isNotBlank(blockBeanEnum.getBeanName())) {
            return super.getBlockInfo(qo);
        }
        Map<String, PcxBasExpType> expenseMap = qo.getExpenseMap();
        List<BlockProperty> blockProperties = getBlockProperties(qo);
        // 通用的属性拼装
        assembleUniversalProperty(qo, blockProperties);
        Map<String, List<BlockProperty>> groupedProperties = blockProperties.stream()
                .collect(Collectors.groupingBy(BlockProperty::getFormCode));
        List<PcxBlockVO> result = new ArrayList<>();
        FundPositionEnum fundPosition = getFundPosition(qo);
        String isEdit = getIsEdit(fundPosition, qo);
        String importantKey = "";
        if(CollectionUtil.isNotEmpty(expenseMap)){
            importantKey = expenseMap.keySet().iterator().next();
        }
        for (Map.Entry<String, List<BlockProperty>> entry : groupedProperties.entrySet()) {
            String formCode = entry.getKey();
            List<BlockProperty> propertyList = entry.getValue();
            //特殊属性
            Map<String, String> extBlockMap = new HashMap<>();
            extBlockMap.put(EDIT_KEY,isEdit);
            extBlockMap.put(IMPORTANT_KEY,formCode.equals(importantKey) ? PubConstant.STR_LOGIC_TRUE : PubConstant.STR_LOGIC_FALSE);
            PcxBlockVO blockVO = new PcxBlockVO();
            blockVO.setClassifyCode(qo.getClassifyCode());
            blockVO.setClassifyName(qo.getClassifyName());
            blockVO.setBlockCode(formCode);
            String blockName = getDefaultBlockName(qo, expenseMap, formCode);
            if (CollectionUtil.isNotEmpty(propertyList)) {
                if(StringUtil.isEmpty(blockName)){
                    blockName = propertyList.get(0).getFormName();
                }
                blockVO.setProperties(convertToBlockPropertyVO(propertyList,qo, fundPosition));
                //判断出国费、专属属性 并处理设置专属属性分页
                if(PcxConstant.ABROAD_EXPENSE_30212.equals(formCode) &&  PositionBlockEnum.SPECIFICITY.getCode().equals(qo.getClassifyCode())) {
                    //获取费用明细类型
                    List<PcxBasExpType> expenseTypeList = getExpenseType(qo, formCode);
                    //获取费用明细字段
                    Map<String, List<BlockProperty>> expenseFormProperties = getExpenseFormProperties(qo, expenseTypeList);
                    List<PcxBlockVO.PropertiesPageInfo> propertiesPageInfos = convertToBlockPropertyPageVO(propertyList, qo, fundPosition, expenseFormProperties);
                    extBlockMap.put(MULTI_PAGE_KEY, CollectionUtil.isEmpty(propertiesPageInfos)?PubConstant.STR_LOGIC_FALSE : PubConstant.STR_LOGIC_TRUE);
                    extBlockMap.put(MULTI_PAGE_NUM_KEY,CollectionUtil.isEmpty(propertiesPageInfos)?PubConstant.STR_LOGIC_FALSE : StringUtil.getStringValue(propertiesPageInfos.size()));
                    blockVO.setPropertiesPage(propertiesPageInfos);
                }
            }
            blockVO.setBlockName(blockName);
            blockVO.setArea(qo.getArea());
            blockVO.setBlockTitle(qo.getShowName());
            blockVO.setBlockExtMap(extBlockMap);
            result.add(blockVO);
        }
        return result;
    }

    private void assembleUniversalProperty(PcxBlockCondQO qo, List<BlockProperty> blockProperties) {
        boolean isSpecificity = PositionBlockEnum.SPECIFICITY.getCode().equals(qo.getClassifyCode());
        PcxBasItemVO pcxbasItem = qo.getPcxBasItemVO();
        if (isSpecificity && !ObjectUtils.isEmpty(pcxbasItem) && PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxbasItem.getItemCode())) {
            BlockProperty blockProperty = this.departmentProperty();
            blockProperties.add(blockProperty);
        }
    }

    protected List<BlockPropertyVO> convertToBlockPropertyVO(List<BlockProperty> blockProperties,PcxBlockCondQO qo,FundPositionEnum fundPosition) {
        if (CollectionUtil.isEmpty(blockProperties)) {
            return new ArrayList<>();
        }
        List<BlockPropertyVO> result = new ArrayList<>();
        boolean isSpecificity = PositionBlockEnum.SPECIFICITY.getCode().equals(qo.getClassifyCode());
        PcxBasItemVO pcxbasItem = qo.getPcxBasItemVO();
        for (BlockProperty blockProperty : blockProperties) {
            if (ObjectUtils.isEmpty(blockProperty) || ObjectUtils.isEmpty(blockProperty.getEditorCode())) {
                continue;
            }
            if(isSpecificity
                    && !BudgetCtrlUtil.isBudgetCtrlEnabled(pcxbasItem.getBudgetCtrl(),qo.getBillFuncCode())
                    && blockProperty.getFieldValue().equals("departmentCode")){
               continue;
            }
            // 财务审核岗位，不展示部门字段
            // mashaojie-20250207 先去掉
            /*if(PositionBlockEnum.SPECIFICITY.getCode().equals(qo.getClassifyCode()) && PcxNodeEnum.finance_audit.getId().equals(qo.getPositionCode())
                    && blockProperty.getFieldValue().equals("departmentCode")){
               continue;
            }*/
            BlockPropertyVO blockPropertyVO = new BlockPropertyVO();
            BeanUtils.copyProperties(blockProperty, blockPropertyVO);
            /*if(isSpecificity && (!PcxNodeEnum.make_bill.getId().equals(qo.getPositionCode()) || blockProperty.getFieldValue().equals("departmentCode"))){
                blockPropertyVO.setIsEdit(PubConstant.LOGIC_FALSE);
            }*/
            /*if (blockProperty.getFieldValue().equals("departmentCode")) {
                if (isSpecificity && !ObjectUtils.isEmpty(fundPosition)
                        && fundPosition.getPosition().equals(qo.getPositionCode())
                        && fundPosition.getIsCarryDept().equals(PubConstant.STR_LOGIC_TRUE)) {
                    blockPropertyVO.setIsEdit(PubConstant.LOGIC_TRUE);
                } else {
                    continue;
                }
            }*/
            result.add(blockPropertyVO);
        }
        return result;
    }

    protected List<PcxBlockVO.PropertiesPageInfo> convertToBlockPropertyPageVO(List<BlockProperty> blockProperties, PcxBlockCondQO qo, FundPositionEnum fundPosition, Map<String, List<BlockProperty>> expenseFormProperties) {
        if (CollectionUtil.isEmpty(blockProperties)) {
            return new ArrayList<>();
        }
        
        // 按照FieldPage字段进行分组
        Map<Integer, List<BlockProperty>> pageMap = blockProperties.stream()
                .collect(Collectors.groupingBy(BlockProperty::getFieldPage));
        
        List<PcxBlockVO.PropertiesPageInfo> result = new ArrayList<>();
        Set<String> budgetFields = new HashSet<>(Arrays.asList("rmbBudget", "foreignBudget")); // 支持扩展
        // 遍历分组结果，生成分页信息
        for (Map.Entry<Integer, List<BlockProperty>> entry : pageMap.entrySet()) {
            PcxBlockVO.PropertiesPageInfo pageInfo = new PcxBlockVO.PropertiesPageInfo();
            // 设置页码
            pageInfo.setStep(entry.getKey());
            // 设置页标题（取第一个属性的formName）
            if (!CollectionUtil.isEmpty(entry.getValue())) {
                pageInfo.setTitle(entry.getValue().get(0).getFormName());
            }
            // 转换属性列表
            List<BlockPropertyVO> properties = entry.getValue().stream()
                    .map(property -> {
                        BlockPropertyVO vo = new BlockPropertyVO();
                        BeanUtils.copyProperties(property, vo);
                        //如果存在人民币预算和外币预算 对 field进行赋值费用明细字段
                        String fieldValue = vo.getFieldValue();
                        if (fieldValue != null && budgetFields.contains(fieldValue)) {
                            vo.setFieldInfo(expenseFormProperties);
                        }
                        return vo;
                    })
                    .collect(Collectors.toList());
            pageInfo.setProperties(properties);
            result.add(pageInfo);
        }
        
        // 按照页码排序
        result.sort(Comparator.comparingInt(PcxBlockVO.PropertiesPageInfo::getStep));
        return result;
    }

    private List<PcxBasExpType> getExpenseType(PcxBlockCondQO qo, String formCode) {
        if(ObjectUtils.isEmpty(qo)){
            return new ArrayList<>();
        }
        PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
        expTypeQO.setMofDivCode(qo.getMofDivCode());
        expTypeQO.setAgyCode(qo.getAgyCode());
        expTypeQO.setFiscal(qo.getFiscal());
        expTypeQO.setTenantId(qo.getTenantId());
        expTypeQO.setLastCode(formCode);
        expTypeQO.setIsRefine(PubConstant.LOGIC_TRUE);
        return expTypeService.selectByQO(expTypeQO);
    }
    /****
     * 获取默认的块儿名称
     * @param qo
     * @param expenseMap
     * @param formCode
     * @return
     */
    private String getDefaultBlockName(PcxBlockCondQO qo, Map<String, PcxBasExpType> expenseMap, String formCode) {
        if (BillFuncCodeEnum.LOAN.getCode().equals(qo.getBillFuncCode())) {
            return qo.getPcxBasItemVO().getItemName();
        } else {
            return Optional.ofNullable(expenseMap.get(formCode))
                    .map(PcxBasExpType::getExpenseName)
                    .orElse(StringUtil.EMPTY);
        }
    }

    /****
     * 块儿是否可以编辑
     * @param fundPosition
     * @param qo
     * @return
     */
    private String getIsEdit(FundPositionEnum fundPosition, PcxBlockCondQO qo) {
        if(ObjectUtils.isEmpty(fundPosition)){
            return PubConstant.STR_LOGIC_FALSE;
        }
        if(PubConstant.STR_LOGIC_TRUE.equals(fundPosition.getIsCarryDept()) && qo.getPositionCode().equals(fundPosition.getPosition())){
            return PubConstant.STR_LOGIC_TRUE;
        }
        return PubConstant.STR_LOGIC_FALSE;
    }
}
