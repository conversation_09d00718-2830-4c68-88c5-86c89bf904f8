package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.lang.Assert;
import com.beust.jcommander.internal.Lists;
import com.pty.mad.common.LeaderTypeEnum;
import com.pty.mad.common.qo.MadWorkflowEmployeeQo;
import com.pty.mad.dto.PaLeaderInfoDto;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pty.mad.api.IMadProjectService;
import org.pty.mad.api.IPaLeaderInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxConstant.TRAVEL_EXPENSE_30211;

@Slf4j
@Indexed
@Service
public class AgySuperiorAuditIPositionService extends AbstractRuleDomainService implements IPositionService<String> {

    @Resource
    private IPaLeaderInfoService paLeaderInfoService;

    @Resource
    private IMadProjectService madProjectService;

    @Resource
    private PcxBillService pcxBillService;

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Override
    public List<String> getPositionIds() {
        return Lists.newArrayList(
                String.format("%s_%s", PcxNodeEnum.supervisor_agy_superior_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.supervisor_agy_superior_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.supervisor_agy_superior_audit.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.supervisor_agy_superior_audit.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.supervisor_agy_superior_audit.getId(), "5"),
                String.format("%s_%s", PcxNodeEnum.divisional_agy_superior_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.divisional_agy_superior_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.divisional_agy_superior_audit.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.divisional_agy_superior_audit.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.divisional_agy_superior_audit.getId(), "5"),
                String.format("%s_%s", PcxNodeEnum.financial_director.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.financial_director.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.financial_director.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.financial_director.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.financial_director.getId(), "5"),
                String.format("%s_%s", PcxNodeEnum.high_agy_superior_audit.getId(), "1"),
                String.format("%s_%s", PcxNodeEnum.high_agy_superior_audit.getId(), "2"),
                String.format("%s_%s", PcxNodeEnum.high_agy_superior_audit.getId(), "3"),
                String.format("%s_%s", PcxNodeEnum.high_agy_superior_audit.getId(), "4"),
                String.format("%s_%s", PcxNodeEnum.high_agy_superior_audit.getId(), "5")
        );
    }

    @Override
    public List<String> findPositionUser(String billId) {

        CheckMsg<PcxBillVO> msg = pcxBillService.view(billId, PositionBlockEnum.FUND_SOURCE, PositionBlockEnum.SPECIFICITY);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());
        PcxBillVO vo = msg.getData();
        PcxBill basicInfo = vo.getBasicInfo();
        MadWorkflowEmployeeQo mwq = getRuleDomain(billId, vo, basicInfo);

        List<PaLeaderInfoDto> infos = paLeaderInfoService.selectLeaderInfoByDeptCode(mwq);
        Map<String, List<PaLeaderInfoDto>> type$infos = infos.stream().collect(Collectors.groupingBy(PaLeaderInfoDto::getLeaderType));
        Assert.state(type$infos.size() <= 1, "领导查找审批人服务找到多个岗位{}", billId);
        if (type$infos.containsKey(LeaderTypeEnum.SUPERIOR.getCode())) {
            return indexOf(ThreadLocalUtil.get(), getSuperiorUserIds(infos));
        }
        return indexOf(ThreadLocalUtil.get(), getUserIds(basicInfo, infos.stream().map(PaLeaderInfoDto::getLeaderCode).collect(Collectors.toList())));
    }



    private List<String> indexOf(String positionId, List<String> superiorUserIds) {
        int index = Integer.parseInt(positionId.substring(positionId.length() - 1));
        return index > superiorUserIds.size() ? Collections.emptyList() : Collections.singletonList(superiorUserIds.get(index - 1));
    }


    private List<String> getSuperiorUserIds(List<PaLeaderInfoDto> infos) {
        Map<String[], List<PaLeaderInfoDto>> agy$mof$fiscal$infos = infos.stream().collect(Collectors.groupingBy(p -> new String[]{p.getSuperiorAgyCode(), p.getMofDivCode(), p.getFiscal()}));

        // 创建一个映射，将 employeeCode 映射到其在 empCodes 中的索引
        Map<String, Integer> empCodeIndexMap = infos.stream()
                .collect(Collectors.toMap(PaLeaderInfoDto::getLeaderCode, infos::indexOf, (a, b) -> a));

        List<MadEmployeeDTO> result = new ArrayList<>();
        agy$mof$fiscal$infos.forEach((key, value) -> {
            String agyCode = key[0];
            String mofDivCode = key[1];
            String fiscal = key[2];
            if (org.apache.commons.collections.CollectionUtils.isEmpty(infos))
                return;
            List<MadEmployeeDTO> employeeDTOS = getMadEmployeeService().selectByMadCodes(value.stream().map(PaLeaderInfoDto::getLeaderCode).collect(Collectors.toList()), agyCode, Integer.parseInt(fiscal), mofDivCode);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(employeeDTOS))
                return;
            Assert.state(employeeDTOS.stream().noneMatch(emp -> StringUtils.isBlank(emp.getUserCode())), "领导未设置账号:单位{}年度{}区划{},领导编码{}", agyCode, fiscal, mofDivCode, value.stream().map(PaLeaderInfoDto::getLeaderCode).collect(Collectors.toList()));

            result.addAll(employeeDTOS);
        });

        return result.stream().sorted(Comparator.comparingInt(emp -> empCodeIndexMap.get(emp.getEmployeeCode())))
                .map(MadEmployeeDTO::getUserCode).collect(Collectors.toList());
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
