package com.pty.pcx.service.impl.stand;

import com.pty.pcx.entity.stand.PcxStandCondition;
import com.pty.pcx.dao.stand.PcxStandConditionDao;
import com.pty.pcx.api.stand.PcxStandConditionService;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 支出标准筛选条件表(PcxStandCondition)表服务实现类
 * <AUTHOR>
 * @since 2024-11-04 14:58:46
 */
@Slf4j
@Indexed
@Service
public class PcxStandConditionServiceImpl implements PcxStandConditionService {

	@Autowired
	private PcxStandConditionDao pcxStandConditionDao;
}


