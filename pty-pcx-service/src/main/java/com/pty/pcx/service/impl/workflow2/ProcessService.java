package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.pty.pcx.api.transfer.PcxBillSyncService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillPayDetailService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillApproveStatusEnum;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.BillPayStatusEnum;
import com.pty.pcx.common.enu.BillStatusEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.dto.pa.PaUserDTO;
import com.pty.pcx.dto.pa.PaUserDesignateDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.pa.IPcxUserDesignateService;
import com.pty.pcx.pa.IPcxUserService;
import com.pty.pcx.qo.bill.EmpInfoQO;
import com.pty.pcx.qo.ecs.EcsBizDelQO;
import com.pty.pcx.qo.setting.ApprovalProcessDefinitionQO;
import com.pty.pcx.qo.workflow2.*;
import com.pty.pcx.service.impl.bill.balance.PcxBalanceCtrlService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.CallBackService;
import com.pty.pcx.service.impl.ecs.EcsBizService;
import com.pty.pcx.util.trans.Workflow2Transformer;
import com.pty.pcx.vo.ApprovalProcessDefinitionVO;
import com.pty.pcx.vo.workflow2.*;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.PtyContext;
import com.pty.workflow2.WorkflowConst;
import com.pty.workflow2.api.*;
import com.pty.workflow2.bo.*;
import com.pty.workflow2.entity.UserInfoEntity;
import com.pty.workflow2.extend.pcx.PcxNode;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import com.pty.workflow2.extend.pcx.PcxProcessDefinition;
import com.pty.workflow2.extend.pcx.finance.FinanceNode;
import com.pty.workflow2.extend.pcx.finance.FinanceNodeEnum;
import com.pty.workflow2.extend.pcx.finance.FinanceProcessDefinition;
import com.pty.workflow2.vo.ActHiTaskVo;
import com.pty.workflow2.vo.DoneTask;
import com.pty.workflow2.vo.TodoTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class ProcessService implements IProcessService {

    @Autowired
    @Qualifier("PtyWfRuntimeService2")
    private IPtyWfRuntimeService ptyWfRuntimeService;
    @Autowired
    @Qualifier("PtyWfTaskService2")
    private IPtyWfTaskService ptyWfTaskService;
    @Autowired
    @Qualifier("PtyWfDeployService2")
    private IPtyWfDeployService ptyWfDeployService;
    @Autowired
    private IPtyWfHiTaskService ptyWfHiTaskService;
    @Autowired
    private IPtyWfTaskQueryService ptyWfTaskQueryService;

    @Autowired
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Autowired
    private IPcxUserService pcxUserService;
    @Autowired
    private IPcxUserDesignateService pcxUserDesignateService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private EcsBizService ecsBizService;
    @Autowired
    private PcxBalanceCtrlService pcxBalanceCtrlService;
    @Autowired
    private PcxBillSyncService pcxBillSyncService;
    @Autowired
    private IPcxBillPayDetailService pcxBillPayDetailService;
    @Autowired
    private CallBackService callBackService;
    @Autowired
    private ParameterNamesModule parameterNamesModule;

    public CheckMsg<MadEmployeeDTO> getExpenseUserInfo(ProcessQO qo) {
        EmpInfoQO eQo = new EmpInfoQO();
        eQo.setUserCode(PtyContext.getUsername());
        eQo.setAgyCode(qo.getAgyCode());
        eQo.setFiscal(String.valueOf(DateUtil.thisYear()));
        eQo.setMofDivCode(qo.getMofDivCode());
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        BeanUtils.copyProperties(qo,pcxBaseDTO);
        pcxBaseDTO.setFiscal(String.valueOf(DateUtil.thisYear()));
        MadEmployeeDTO madEmployeeDTO = madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, eQo.getUserCode());
        if (Objects.isNull(madEmployeeDTO)){
            return CheckMsg.fail("当前用户未绑定人员信息，不能发起报销");
        }
        return CheckMsg.success(madEmployeeDTO);
    }

    public MadEmployeeDTO getExpenseUserInfoByCode(String agyCode, String mofDivCode, Integer fiscal, String employeeCode) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setAgyCode(agyCode);
        pcxBaseDTO.setFiscal(String.valueOf(fiscal));
        pcxBaseDTO.setMofDivCode(mofDivCode);
        return madEmployeeExternalService.selectEmployeeByEmpCode(pcxBaseDTO, employeeCode);
    }

    @Override
    @Transactional
    public CheckMsg<Void> startProcess(ProcessQO startParam) {
        try {
            StartProcessParam param = new StartProcessParam();
            param.setAgyCode(startParam.getAgyCode());
            param.setMofDivCode(startParam.getMofDivCode());
            param.setSubmitter(startParam.getSubmitter());
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setBusiType(startParam.getBillFuncCode());
            param.setBusiId(startParam.getBillId());
            param.setTenantId(startParam.getTenantId());
            param.setBusiDelegate(PCX_WORKFLOW_LISTENER);
            param.setProcessKey(startParam.toProcessKey());
            ptyWfRuntimeService.startProcess(param);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional
    public CheckMsg<Void> submitTask(SubmitQO submit) {
        // 后加签
        CheckMsg<Void> success = CheckMsg.success();
        PcxBill view = billMainService.view(submit.getBillId());
        Assert.state((view.getBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode())
                || view.getInputAmt().compareTo(BigDecimal.ZERO) > 0), "单据金额不能为0");
        if (BillApproveStatusEnum.SAVE.getCode().equals(view.getApproveStatus())){
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(view.getId())
                    .approveStatus(BillApproveStatusEnum.APPROVING.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);
        }
        submit.setAgyCode(view.getAgyCode());
        submit.setBillFuncCode(view.getBillFuncCode());
        if (StringUtils.isNotBlank(submit.getAfterApproveEmployeeCode())) {
            MadEmployeeDTO employeeDTO = this.getExpenseUserInfoByCode(submit.getAgyCode(), submit.getMofDivCode(), DateUtil.thisYear(), submit.getAfterApproveEmployeeCode());
            Assert.state(employeeDTO != null, "加签人员无法找到");
            CounterSignTaskParam param = new CounterSignTaskParam();
            param.setAgyCode(submit.getAgyCode());
            param.setMofDivCode(submit.getMofDivCode());
            param.setSubmitter(PtyContext.getUsername());
            param.setSubmitterName(PtyContext.getUsernameCn());
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setBusiType(submit.getBillFuncCode());
            param.setBusiId(submit.getBillId());
            param.setTenantId(PtyContext.getTenantId());
            param.setComment(submit.getComment());
            param.setUserCodes(Collections.singleton(employeeDTO.getUserCode()));
            param.setClients(taskOwners(submit));
            ptyWfTaskService.createSignTask(param);
            return success;
        }
        SubmitTaskParam param = new SubmitTaskParam();
        param.setAgyCode(submit.getAgyCode());
        param.setMofDivCode(submit.getMofDivCode());
        param.setSubmitter(PtyContext.getUsername());
        param.setSubmitterName(PtyContext.getUsernameCn());
        param.setBusiModule(PcxConstant.SYS_ID);
        param.setBusiType(submit.getBillFuncCode());
        param.setBusiId(submit.getBillId());
        param.setTenantId(PtyContext.getTenantId());
        param.setComment(submit.getComment());
        param.setBusiDelegate(PCX_WORKFLOW_LISTENER);
        param.setProcessKey(submit.toProcessKey());
        param.setClients(taskOwners(submit));
        ptyWfTaskService.submitTask(param);
        return success;
    }

    private List<UserInfoEntity> taskOwners(ProcessQO process) {
        TodoTaskParam todoTaskParam = new TodoTaskParam();
        todoTaskParam.setTenantId(process.getTenantId());
        todoTaskParam.setMofDivCode(process.getMofDivCode());
        todoTaskParam.setBusiId(process.getBillId());
        List<TodoTask> tasks = ptyWfTaskQueryService.getTodoTaskWithBusiId(todoTaskParam);
        if (!CollectionUtil.isEmpty(tasks)) {
            List<String> userCodes = tasks.stream().map(TodoTask::getUsers).flatMap(Collection::stream).collect(Collectors.toList());
            List<PaUserDesignateDTO> designateDTOS = pcxUserDesignateService.selectValidAsDesignate(PtyContext.getUsername());
            userCodes = userCodes.stream().filter(code -> !CollectionUtils.isEmpty(designateDTOS)
                    && designateDTOS.stream().anyMatch(designate -> designate.getUserCode().equals(code))).collect(Collectors.toList());
            if (!CollectionUtil.isEmpty(userCodes)) {
                List<PaUserDTO> paUserDTOS = pcxUserService.selectByUserCodes(userCodes);
                return paUserDTOS.stream().map(paUserDTO -> {
                    UserInfoEntity userInfoEntity = new UserInfoEntity();
                    userInfoEntity.setUserCode(paUserDTO.getUserCode());
                    userInfoEntity.setUserName(paUserDTO.getUserName());
                    return userInfoEntity;
                }).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional
    public CheckMsg<Void> rollbackTask(RollbackQO rollback) {
        try {
//            CheckMsg<MadEmployeeDTO> employeeMsg = this.getExpenseUserInfo(rollback);
//            Assert.state(employeeMsg.isSuccess(), "员工信息异常");
            Assert.notBlank(rollback.getComment(), "退回原因不能为空");
            RollBackTaskParam param = new RollBackTaskParam();
            param.setAgyCode(rollback.getAgyCode());
            param.setMofDivCode(rollback.getMofDivCode());
            param.setSubmitter(PtyContext.getUsername());
            param.setSubmitterName(PtyContext.getUsernameCn());
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setBusiType(rollback.getBillFuncCode());
            param.setBusiId(rollback.getBillId());
            param.setTenantId(rollback.getTenantId());
            param.setRollBackType(rollback.getRollbackType());
            param.setComment(rollback.getComment());
            param.setTargetNodeId(rollback.getNodeId());
            param.setClients(taskOwners(rollback));
            param.setIsCommitBack(rollback.isCommitBack());
            ptyWfTaskService.rollBackTask(param);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional
    public CheckMsg<Void> revokeTask(RevokeQO revoke) {
        try {
            RevokeTaskParam param = new RevokeTaskParam();
            param.setDoneTaskId(revoke.getTaskId());
            param.setAgyCode(revoke.getAgyCode());
            param.setMofDivCode(revoke.getMofDivCode());
            param.setSubmitter(PtyContext.getUsername());
            param.setSubmitterName(PtyContext.getUsernameCn());
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setBusiType(revoke.getBillFuncCode());
            param.setBusiId(revoke.getBillId());
            param.setTenantId(revoke.getTenantId());
            ptyWfTaskService.revokeTask(param);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
        return CheckMsg.success();
    }


    public CheckMsg<List<NodeInfoVO>> queryTaskCanReturnHiNodeList(ProcessQO qo) {
        HistoryQueryParams param = new HistoryQueryParams();
        param.setTenantId(PtyContext.getTenantId());
        param.setBusiModule(PcxConstant.SYS_ID);
        param.setBusiType(qo.getBillFuncCode());
        param.setBusiId(qo.getBillId());
        param.setTenantId(PtyContext.getTenantId());
        param.setBusiId(qo.getBillId());
        List<NodeInfoVO> result = Workflow2Transformer.INSTANCE.nodes2vos(ptyWfHiTaskService.getUserTaskListByReturn(param));
        Collections.reverse(result);
        List<String> userCodes = result.stream().flatMap(node -> node.getUserCodes().stream()).distinct().collect(Collectors.toList());

        List<PaUserDTO> userDTOS = pcxUserService.selectByUserCodes(userCodes);
        Map<String, PaUserDTO> dtoMap = userDTOS.stream().collect(Collectors.toMap(PaUserDTO::getUserCode, Function.identity(), (a, b) -> a));
        result.forEach(node -> {
            if (!CollectionUtils.isEmpty(node.getUserCodes())) {
                node.setNodeName(node.getNodeName() + "(" +
                        node.getUserCodes().stream().distinct().map(code -> dtoMap.get(code).getUserName()).collect(Collectors.joining(",")) +
                        ")");
            }
        });
        return CheckMsg.success(result);
    }

    @Override
    @Transactional
    public CheckMsg<Void> delegateTask(DelegateQO delegate) {
        try {
//            CheckMsg<MadEmployeeDTO> employeeMsg = this.getExpenseUserInfo(delegate);
//            Assert.state(employeeMsg.isSuccess(), "员工信息异常");
            DelegateTaskParam param = new DelegateTaskParam();
            MadEmployeeDTO madEmployeeDTO = madEmployeeExternalService.selectAgyEmployee(PcxBaseDTO.builder()
                            .agyCode(delegate.getAgyCode())
                            .mofDivCode(delegate.getMofDivCode())
                            .fiscal(delegate.getFiscal()).build(),
                    delegate.getTransferUserCode());
            Assert.state(Objects.nonNull(madEmployeeDTO), "查询员工信息异常 {}", delegate.getTransferUserCode());
            param.setAgyCode(delegate.getAgyCode());
            param.setMofDivCode(delegate.getMofDivCode());
            param.setSubmitter(PtyContext.getUsername());
            param.setSubmitterName(PtyContext.getUsernameCn());
            param.setTenantId(ObjectUtils.firstNonNull(delegate.getTenantId(), PtyContext.getTenantId()));
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setBusiType(delegate.getBillFuncCode());
            param.setBusiId(delegate.getBillId());
            param.setComment(delegate.getComment());
            param.setTransferUserCode(madEmployeeDTO.getUserCode());
            param.setClients(taskOwners(delegate));
            ptyWfTaskService.delegateTask(param);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional
    public CheckMsg<Void> delegateRollbackProcess(DelegateQO delegate) {
        try {
//            CheckMsg<MadEmployeeDTO> employeeMsg = this.getExpenseUserInfo(delegate);
//            Assert.state(employeeMsg.isSuccess(), "员工信息异常");
            CancelDelegateTaskParam param = new CancelDelegateTaskParam();
            param.setAgyCode(delegate.getAgyCode());
            param.setMofDivCode(delegate.getMofDivCode());
            param.setSubmitter(PtyContext.getUsername());
            param.setSubmitterName(PtyContext.getUsernameCn());
            param.setTenantId(ObjectUtils.firstNonNull(delegate.getTenantId(), PtyContext.getTenantId()));
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setBusiType(delegate.getBillFuncCode());
            param.setBusiId(delegate.getBillId());
            param.setClients(taskOwners(delegate));
            ptyWfTaskService.cancelDelegateTask(param);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail(e.getMessage());
        }
        return CheckMsg.success();
    }

    @Override
    @Deprecated
    public CheckMsg<Object> queryProcessDefinition(String agyCode, String mofDivCode, String billType, String tenantId) throws Exception {
        try {
            PcxProcessDefinition param = new PcxProcessDefinition();
            param.setAgyCode(agyCode);
            param.setBusiType(ObjectUtils.firstNonNull(billType, BillFuncCodeEnum.APPLY.getCode()));
            param.setTenantId(ObjectUtils.firstNonNull(tenantId, PtyContext.getTenantId()));
            param.setBusiModule(PcxConstant.SYS_ID);
            param.setMofDivCode(mofDivCode);
            param.setWfName(String.format("%s-%s", PcxConstant.SYS_ID, billType));
            return CheckMsg.success(ptyWfDeployService.getPcxWorkflowModel(param));
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail("查询流程定义异常");
        }
    }

    @Override
    public CheckMsg<ApprovalProcessDefinitionVO> queryProcessDefinition2(String agyCode, String mofDivCode, String billType, String tenantId) {
        try {
            if (billType.equals(BILL_TYPE_PAY_DETAIL_CHANGE)) {
                FinanceProcessDefinition param = new FinanceProcessDefinition();
                param.setAgyCode(agyCode);
                param.setBusiType(ObjectUtils.firstNonNull(billType, BillFuncCodeEnum.APPLY.getCode()));
                param.setTenantId(ObjectUtils.firstNonNull(tenantId, PtyContext.getTenantId()));
                param.setBusiModule(PcxConstant.SYS_ID);
                param.setMofDivCode(mofDivCode);
                param.setWfName(String.format("%s-%s", PcxConstant.SYS_ID, billType));
                param.setWfId(ProcessQO.getProcessKeyV1(mofDivCode, agyCode, PcxConstant.SYS_ID, billType));
                FinanceProcessDefinition financeWorkflowModel = (FinanceProcessDefinition) ptyWfDeployService.getFinanceWorkflowModel(param);
                return CheckMsg.success(convert2vo(financeWorkflowModel));
            }else {
                PcxProcessDefinition param = new PcxProcessDefinition();
                param.setAgyCode(agyCode);
                param.setBusiType(ObjectUtils.firstNonNull(billType, BillFuncCodeEnum.APPLY.getCode()));
                param.setTenantId(ObjectUtils.firstNonNull(tenantId, PtyContext.getTenantId()));
                param.setBusiModule(PcxConstant.SYS_ID);
                param.setMofDivCode(mofDivCode);
                param.setWfName(String.format("%s-%s", PcxConstant.SYS_ID, billType));
                param.setWfId(ProcessQO.getProcessKeyV1(mofDivCode, agyCode, PcxConstant.SYS_ID, billType));
                PcxProcessDefinition pcxWorkflowModel = (PcxProcessDefinition) ptyWfDeployService.getPcxWorkflowModel(param);
                return CheckMsg.success(convert2vo(pcxWorkflowModel));
            }
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail("查询流程定义异常");
        }
    }

    private ApprovalProcessDefinitionVO convert2vo(PcxProcessDefinition pcxWorkflow) {
        ApprovalProcessDefinitionVO vo = new ApprovalProcessDefinitionVO();
        vo.setMofDivCode(pcxWorkflow.getMofDivCode());
        vo.setAgyCode(pcxWorkflow.getAgyCode());
        vo.setBillType(pcxWorkflow.getBusiType());
        vo.setWfId(pcxWorkflow.getWfId());
        vo.setWfName(pcxWorkflow.getWfName());
        vo.setTenantId(ObjectUtils.firstNonNull(pcxWorkflow.getTenantId(), PtyContext.getTenantId()));

        vo.setAuditSteps(new ArrayList<>());

        pcxWorkflow.getNodes().forEach(node -> {
            if (node.getId().equals(PcxNodeEnum.start.getId()) || node.getId().equals(PcxNodeEnum.end.getId()))
                return;
            ApprovalProcessDefinitionVO.DefinitionRow row = new ApprovalProcessDefinitionVO.DefinitionRow();
            row.setOrder(node.getOrder());
            row.setEnabled(!node.isSkip());
            row.setStepCode(node.getId());
            row.setStepName(node.getLabel());
            row.setDesc(node.getDescription());
            if (node.getId().equals(PcxNodeEnum.finance_audit.getId())){
                //加载财务人员的来源
                PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
                pcxBaseDTO.setAgyCode(pcxWorkflow.getAgyCode());
                pcxBaseDTO.setMofDivCode(pcxWorkflow.getMofDivCode());
                pcxBaseDTO.setFiscal(DateUtil.year(new Date())+"");
                row.setRemark(madEmployeeExternalService.selectFinanceSource(pcxBaseDTO));
            }
            vo.getAuditSteps().add(row);
        });
        return vo;
    }

    private ApprovalProcessDefinitionVO convert2vo(FinanceProcessDefinition financeWorkflow) {
        ApprovalProcessDefinitionVO vo = new ApprovalProcessDefinitionVO();
        vo.setMofDivCode(financeWorkflow.getMofDivCode());
        vo.setAgyCode(financeWorkflow.getAgyCode());
        vo.setBillType(financeWorkflow.getBusiType());
        vo.setWfId(financeWorkflow.getWfId());
        vo.setWfName(financeWorkflow.getWfName());
        vo.setTenantId(ObjectUtils.firstNonNull(financeWorkflow.getTenantId(), PtyContext.getTenantId()));

        vo.setAuditSteps(new ArrayList<>());

        financeWorkflow.getNodes().forEach(node -> {
            if (node.getId().equals(PcxNodeEnum.start.getId()) || node.getId().equals(PcxNodeEnum.end.getId()))
                return;
            ApprovalProcessDefinitionVO.DefinitionRow row = new ApprovalProcessDefinitionVO.DefinitionRow();
            row.setOrder(node.getOrder());
            row.setEnabled(!node.isSkip());
            row.setStepCode(node.getId());
            row.setStepName(node.getLabel());
            row.setDesc(node.getDescription());
            vo.getAuditSteps().add(row);
        });
        return vo;
    }

    @Override
    @Transactional
    public CheckMsg<Object> deployProcessDefinition(ApprovalProcessDefinitionQO qo) {
        try {
            if (qo.getBillType().equals(BILL_TYPE_PAY_DETAIL_CHANGE)) {
                FinanceProcessDefinition financeWorkflow = convert2financeDto(qo);
                return CheckMsg.success(ptyWfDeployService.deployFinanceWorkflowModel(financeWorkflow));
            }
            PcxProcessDefinition pcxWorkflow = convert2dto(qo);
            return CheckMsg.success(ptyWfDeployService.deployPcxWorkflowModel(pcxWorkflow));
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CheckMsg.fail("部署流程定义异常");
        }
    }

    @Override
    @Deprecated
    public CheckMsg<List<ProcessHistoryVO>> listHistory(ProcessHistoryQO qo) {
        HistoryQueryParams params = new HistoryQueryParams();
        params.setAgyCode(qo.getAgyCode());
        params.setBusiId(qo.getBillId());
        params.setBusiType(qo.getBillFuncCode());
        params.setMofDivCode(qo.getMofDivCode());
        params.setTenantId(ObjectUtils.firstNonNull(qo.getTenantId(), PtyContext.getTenantId()));
        params.setBusiModule(PcxConstant.SYS_ID);
        List<ActHiTaskVo> actHiTaskVoList = ptyWfHiTaskService.approvalHstoryList(params);

        List<ProcessHistoryVO> result = Workflow2Transformer.INSTANCE.tasks2histories(actHiTaskVoList);
        Map<String, MadEmployeeDTO> madEmployeeMap = new HashMap<>();
        actHiTaskVoList.stream().collect(Collectors.groupingBy(t -> t.getAgyCode()+"-"+t.getMofDivCode()+"-"+t.getFiscal())).forEach((k, v) -> {
            ActHiTaskVo actHiTaskVo = v.get(0);
            List<MadEmployeeDTO> employeeDTOS = madEmployeeExternalService.selectByMadCodes(v.stream().flatMap(t -> t.getUserCodes().stream()).distinct().collect(Collectors.toList()),
                    actHiTaskVo.getAgyCode(), actHiTaskVo.getFiscal(), actHiTaskVo.getMofDivCode());
            madEmployeeMap.putAll(employeeDTOS.stream().collect(Collectors.toMap(MadEmployeeDTO::getEmployeeCode, Function.identity(), (a, b) -> a)));
        });
//        result.forEach(h -> {
//            h.getUserCodeList().forEach(u -> {
//                if (CollectionUtils.isEmpty(h.getUserCodes()))
//                    h.setUserCodes(new ArrayList<>());
//                h.getUserCodes().add(madEmployeeMap.get(u));
//            });
//        });
        // fix 前端要求历史倒序
        Collections.reverse(result);
        return CheckMsg.success(result);
    }

    @Override
    public CheckMsg<ProcessCompositeVO> listHistoryWithDelegate(ProcessHistoryQO qo) {

        List<ActHiTaskVo> actHiTaskVoList = getActHiTaskVos(qo);
        // 根据EndTime正序排列, endTime为null的排到最后
        actHiTaskVoList.sort(Comparator.comparing(ActHiTaskVo::getEndTime, Comparator.nullsLast(Comparator.naturalOrder())));

        ProcessCompositeVO result = new ProcessCompositeVO();

        // 根据 相邻的nodeId 分组
        List<List<ActHiTaskVo>> groupedHiTask = groupHiTask(actHiTaskVoList, result);

        List<ActHiTaskVo> myDelegatedTaskVoList = new ArrayList<>();

                // 同一节点下, 移除成对的加签和加签确认
        groupedHiTask.forEach(rs -> {
            int i = 0;
            while(i < rs.size()) {
                // 发现加签任务
                if (rs.get(i).getOperation() != null && rs.get(i).getOperation().equals(WorkflowConst.DELEGATE_TASK_DELETE_REASON)) {
                    // 寻找加签确认
                    for (int j = i + 1; j < rs.size(); j++) {
                        ActHiTaskVo tj = rs.get(j);
                        boolean contains = tj.getUserCodes().contains(rs.get(i).getDelegatedUserCodes().get(0));
                        if (tj.getOperation() != null && tj.getOperation().equals(WorkflowConst.DELEGATED_TASK_DELETE_REASON) && contains) {
                            // 移除加签确认
//                            rs.remove(j);
                            // 移除加签
                            rs.remove(i);
                            i = j;
                            i --;
                            break;
                        }
                    }
                }
                if (rs.get(i).getOperation() != null && rs.get(i).getOperation().equals(WorkflowConst.DELEGATE_TASK_DELETE_REASON)) {
                    myDelegatedTaskVoList.add(rs.get(i));
                    ActHiTaskVo delegating = new ActHiTaskVo();
                    delegating.setUserCodes(rs.get(i).getDelegatedUserCodes());
                    delegating.setOperation(WorkflowConst.DELEGATE_TASK_DELETE_REASON);
                    delegating.setNodeId(rs.get(i).getNodeId());
                    delegating.setNodeName(rs.get(i).getNodeName());
                    delegating.setOperationName("委派");
                    delegating.setStartTime(rs.get(i).getStartTime());
                    delegating.setEndTime(rs.get(i).getEndTime());
                    delegating.setStartTime(StrUtil.firstNonBlank(rs.get(i).getStartTime().substring(0, DatePattern.NORM_DATETIME_PATTERN.length()), ""));
                    delegating.setEndTime(StrUtil.firstNonBlank(rs.get(i).getEndTime().substring(0, DatePattern.NORM_DATETIME_PATTERN.length()), ""));
                    delegating.setIsFinished(1);
                    rs.set(i, delegating);
                }
                i++;
            }
        });

//        List<ActHiTaskVo> myDelegatedTaskVoList = groupedHiTask.stream().flatMap(Collection::stream).filter(t -> t.getOperation() != null
//                && t.getOperation().equals(WorkflowConst.DELEGATE_TASK_DELETE_REASON)
//                && !CollectionUtils.isEmpty(t.getDelegatedUserCodes()) && t.getDelegatedUserCodes().contains(PtyContext.getUsername())).collect(Collectors.toList());
        List<ProcessHistoryWithDelegateVO> approvalProcesses = new ArrayList<>();
        // 委派给我的任务, 要单独显示在详情页的顶部
        List<ProcessHistoryVO> myDelegatedProcesses = Workflow2Transformer.INSTANCE.tasks2histories(myDelegatedTaskVoList);
        Map<String, PaUserDTO> paUserMap = new HashMap<>();
        groupedHiTask.forEach(rs -> {
//            madEmployeeExternalService.selectByMadCodes(rs.stream().flatMap(t -> t.getUserCodes().stream()).distinct().collect(Collectors.toList()),
//                    rs.get(0).getAgyCode(), rs.get(0).getFiscal(), rs.get(0).getMofDovCode());
            List<PaUserDTO> paUserDTOS = pcxUserService.selectByUserCodes(rs.stream().flatMap(t -> t.getUserCodes().stream()).distinct().collect(Collectors.toList()));
            paUserMap.putAll(paUserDTOS.stream().collect(Collectors.toMap(PaUserDTO::getUserCode, Function.identity(), (a, b) -> a)));
        });

        List<List<ProcessHistoryVO>> _results = groupedHiTask.stream().map(Workflow2Transformer.INSTANCE::tasks2histories).collect(Collectors.toList());

        if (StringUtils.isNoneBlank(result.getDelegateUserCode()))
            result.setDelegateUserName(paUserMap.get(result.getDelegateUserCode()).getUserName());

        // 分组后按顺序
        myDelegatedProcesses.forEach(_h -> {
            _h.getUserCodeList().forEach(u -> {
                if (CollectionUtils.isEmpty(_h.getUserCodes()))
                    _h.setUserCodes(new ArrayList<>());
                _h.getUserCodes().add(paUserMap.get(u));
            });
        });
        _results.forEach(h -> {
            h.forEach(_h -> {
                _h.getUserCodeList().forEach(u -> {
                    if (CollectionUtils.isEmpty(_h.getUserCodes()))
                        _h.setUserCodes(new ArrayList<>());
                    _h.getUserCodes().add(paUserMap.get(u));
                });
            });
            approvalProcesses.add(new ProcessHistoryWithDelegateVO(h));
        });

        if (CollUtil.isNotEmpty(approvalProcesses)) {
            CheckMsg<ApprovalProcessDefinitionVO> definitionVOCheckMsg = this.queryProcessDefinition2(qo.getAgyCode(), qo.getMofDivCode(), qo.getBillFuncCode(), qo.getTenantId());
            if (definitionVOCheckMsg.isSuccess()) {
                ApprovalProcessDefinitionVO data = definitionVOCheckMsg.getData();
                if (data != null && CollUtil.isNotEmpty(data.getAuditSteps())) {
                    PcxBill bill = billMainService.view(qo.getBillId());
                    if (!bill.getApproveStatus().equals(BillApproveStatusEnum.APPROVED.getCode())) {
                        data.getAuditSteps().sort(Comparator.comparing(ApprovalProcessDefinitionVO.DefinitionRow::getOrder));
                        // approval process取出最后一条记录(未完成的), 查看在哪一个步骤
                        ProcessHistoryVO last = approvalProcesses.get(approvalProcesses.size() - 1).getApprovalProcess().get(approvalProcesses.get(approvalProcesses.size() - 1).getApprovalProcess().size() - 1);
                        AtomicBoolean appendUnfinishedNodes = new AtomicBoolean(false);
                        data.getAuditSteps().forEach(auditStep -> {
                            if (appendUnfinishedNodes.get() && auditStep.getEnabled()) {
                                approvalProcesses.add(new ProcessHistoryWithDelegateVO(new ArrayList<ProcessHistoryVO>() {{
                                    ProcessHistoryVO vo = new ProcessHistoryVO();
                                    vo.setPositionCode(auditStep.getStepCode());
                                    vo.setPositionName(auditStep.getStepName());
                                    vo.setUserCodes(new ArrayList<PaUserDTO>() {{
                                        PaUserDTO userDTO = new PaUserDTO();
                                        userDTO.setUserName("");
                                        userDTO.setUserCode("");
                                        add(userDTO);
                                    }});
                                    vo.setIsFinished(0);
                                    add(vo);
                                }}));
                            }
                            if (last.getPositionCode().startsWith(auditStep.getStepCode())) {
                                appendUnfinishedNodes.set(true);
                            }
                        });
                    }
                }

            }
        }

        result.setMyDelegatedProcess(myDelegatedProcesses);
        result.setApprovalProcess(approvalProcesses);

        return CheckMsg.success(result);
    }

    @Override
    public CheckMsg<ProcessHistoryVO> queryLatestApprovedHistory(ProcessHistoryQO qo) {
        List<ActHiTaskVo> actHiTaskVoList = getActHiTaskVos(qo);
        try {
            for (int i = actHiTaskVoList.size() - 1; i >= 0; i--) {
                if (actHiTaskVoList.get(i).getIsFinished().equals(PubConstant.LOGIC_TRUE)) {
                    ProcessHistoryVO processHistoryVO = Workflow2Transformer.INSTANCE.task2history(actHiTaskVoList.get(i));
                    return CheckMsg.success(processHistoryVO);
                }
            }
            return CheckMsg.fail("找不到对应记录");
        } catch (Exception e) {
            log.error("查询最后已审批节点异常", e);
            return CheckMsg.fail("查询最后已审批节点异常");
        }
    }

    @Override
    public CheckMsg<ProcessHistoryVO> queryNearestApproveUser(ProcessHistoryQO qo, String positionCode) {
        CheckMsg<ProcessCompositeVO> msg = listHistoryWithDelegate(qo);
        if (msg.isSuccess()) {
            List<ProcessHistoryVO> vos = msg.getData().getApprovalProcess().stream().flatMap(t -> t.getApprovalProcess().stream()).collect(Collectors.toList());
            Optional<ProcessHistoryVO> max = vos.stream().filter(vo -> vo.getIsFinished().equals(1) &&
                    vo.getPositionCode().equals(positionCode)).max(Comparator.comparing(ProcessHistoryVO::getEndTime));
            return max.map(CheckMsg::success).orElseGet(() -> CheckMsg.fail("找不到对应记录"));
        }
        return CheckMsg.fail(msg.getMsgInfo());
    }

    private List<ActHiTaskVo> getActHiTaskVos(ProcessHistoryQO qo) {
        HistoryQueryParams params = new HistoryQueryParams();
//        params.setAgyCode(qo.getAgyCode());
        params.setBusiId(qo.getBillId());
        params.setBusiType(qo.getBillFuncCode());
//        params.setMofDivCode(qo.getMofDivCode());
        params.setTenantId(ObjectUtils.firstNonNull(qo.getTenantId(), PtyContext.getTenantId()));
        params.setBusiModule(PcxConstant.SYS_ID);
        return ptyWfHiTaskService.approvalHstoryList(params);
    }

    private static String getTaskId(ProcessHistoryVO h) {
        return ObjectUtils.firstNonNull(h.getTaskId(), "unknown");
    }

    private static List<List<ActHiTaskVo>> groupHiTask(List<ActHiTaskVo> actHiTaskVoList, ProcessCompositeVO result) {
        Map<String, Integer> opMerge = MapUtil.<String, Integer>builder()
                .put(WorkflowConst.SUBMIT_TASK_DELETE_REASON, 1)
                .put(WorkflowConst.DELEGATE_TASK_DELETE_REASON, 1)
                .put(WorkflowConst.DELEGATED_TASK_DELETE_REASON, 1)
                .put(WorkflowConst.REVOKE_TASK_DELETE_REASON, 2)
                .put(WorkflowConst.ROLLBACK_TASK_DELETE_REASON, 3)
                .put(WorkflowConst.COUNTERSIGN_REASON, 4)
                .build();
        // 使用Stream API和reduce操作进行分组
        return actHiTaskVoList.stream().reduce(
                new ArrayList<List<ActHiTaskVo>>(),
                (acc, item) -> {
                    if (!acc.isEmpty()) {
                        List<ActHiTaskVo> lastGroup = acc.get(acc.size() - 1);
                        // 如果最后的组不为空且最后一个元素的nodeId与当前item相同，则添加到最后一组
                        if (!lastGroup.isEmpty()
                                && Objects.equals(lastGroup.get(lastGroup.size() - 1).getNodeId(), item.getNodeId())
                                && opMerge.getOrDefault(item.getOperation(), 1).equals(opMerge.getOrDefault(lastGroup.get(lastGroup.size() - 1).getOperation(), 1))) {
                            lastGroup.addAll(getTaskList(item, result));
                        } else {
                            // 否则创建一个新的组
                            acc.add(new ArrayList<>(getTaskList(item, result)));
                        }
                    } else {
                        // 如果累加器是空的，直接添加一个新组
                        acc.add(new ArrayList<>(getTaskList(item, result)));
                    }
                    return acc;
                },
                (acc1, acc2) -> { throw new UnsupportedOperationException("Parallel Stream not supported"); }
        );
    }

    private static List<ActHiTaskVo> getTaskList(ActHiTaskVo vo, ProcessCompositeVO result) {
        // 代办
        if (vo.getIsFinished() == 0 && !CollectionUtils.isEmpty(vo.getDelegatedUserCodes())) {
            result.setDelegateUserCode(vo.getDelegatedUserCodes().get(0));
            return new ArrayList<ActHiTaskVo>() {{
//                ActHiTaskVo delegating = new ActHiTaskVo();
//                delegating.setUserCodes(vo.getDelegatedUserCodes());
//                delegating.setOperation(WorkflowConst.DELEGATE_TASK_DELETE_REASON);
//                delegating.setNodeId(vo.getNodeId());
//                delegating.setNodeName(vo.getNodeName());
//                delegating.setOperationName("委派");
//                delegating.setIsFinished(1);
//                add(delegating);
                add(vo);
            }};
        }
        return new ArrayList<>(Collections.singletonList(vo));
    }


    @Override
    public CheckMsg<List<TodoTaskVO>> getTodoTaskWithCreator(TodoTaskParamQO taskQuery) {
        taskQuery.setBusiModule(ObjectUtils.firstNonNull(taskQuery.getBusiModule(), PcxConstant.SYS_ID));
        taskQuery.setTenantId(ObjectUtils.firstNonNull(taskQuery.getTenantId(), PtyContext.getTenantId()));
        List<TodoTask> tasks = ptyWfTaskQueryService.getTodoTaskWithCreators(Workflow2Transformer.INSTANCE.qo2param(taskQuery));
        return CheckMsg.success(Workflow2Transformer.INSTANCE.todoTasks2vos(tasks));
    }

    @Override
    public CheckMsg<List<TodoTaskVO>> getTodoTaskWithCandidateAndAssigned(TodoTaskParamQO taskQuery) {
        taskQuery.setBusiModule(ObjectUtils.firstNonNull(taskQuery.getBusiModule(), PcxConstant.SYS_ID));
        taskQuery.setTenantId(ObjectUtils.firstNonNull(taskQuery.getTenantId(), PtyContext.getTenantId()));
        List<TodoTask> tasks = ptyWfTaskQueryService.getTodoTaskWithCandidateAndAssigned(Workflow2Transformer.INSTANCE.qo2param(taskQuery));
        return CheckMsg.success(Workflow2Transformer.INSTANCE.todoTasks2vos(tasks));
    }

    @Override
    public CheckMsg<List<DoneTaskVO>> getDoneTaskWithCandidateAndAssigned(DoneTaskParamQO taskQuery) {
        taskQuery.setBusiModule(ObjectUtils.firstNonNull(taskQuery.getBusiModule(), PcxConstant.SYS_ID));
        taskQuery.setTenantId(ObjectUtils.firstNonNull(taskQuery.getTenantId(), PtyContext.getTenantId()));
        List<DoneTask> tasks = ptyWfTaskQueryService.getDoneTaskWithAssigned(Workflow2Transformer.INSTANCE.qo2param(taskQuery));
        return CheckMsg.success(Workflow2Transformer.INSTANCE.doneTasks2vos(tasks));
    }

    @Override
    @Transactional
    public CheckMsg<Void> deleteProcessInstance(PcxBill pcxBill) {
        DeleteProcessParam param = new DeleteProcessParam();
        param.setBusiId(pcxBill.getId());
        ptyWfRuntimeService.deleteProcess(param);
        return CheckMsg.success();
    }

    @Override
    public CheckMsg<PositionVO> getPositionCode(PositionQO qo) {
        try {
            Assert.notNull(qo, "参数为空");
            Assert.notNull(qo.getUserCode(), "用户编码不能为空");
            Assert.notNull(qo.getBillId(), "单据ID不能为空");
            String billId = qo.getBillId();
            String userCode = qo.getUserCode();
            List<PcxBill> pcxBills = billMainService.selectMainBill(PcxBill.builder().id(billId).build());
            if (CollectionUtils.isEmpty(pcxBills)) {
                return CheckMsg.fail("找不到对应的单据");
            }
            List<PaUserDesignateDTO> paUserDesignateDTOS = pcxUserDesignateService.selectValidAsDesignate(qo.getUserCode());
            log.info("当前委托人{}, 被委托信息{}", qo.getUserCode(), paUserDesignateDTOS);
            Set<String> userCodes = new HashSet<>();
            userCodes.add(qo.getUserCode());
            userCodes.addAll(paUserDesignateDTOS.stream().map(PaUserDesignateDTO::getUserCode).collect(Collectors.toList()));
            CheckMsg<List<TodoTaskVO>> todoResult = getTodoTaskWithCandidateAndAssigned(TodoTaskParamQO.builder()
//                    .agyCode(qo.getAgyCode())
                    .mofDivCode(qo.getMofDivCode()).candidateUsers(userCodes).build());
            CheckMsg<List<DoneTaskVO>> doneResult = getDoneTaskWithCandidateAndAssigned(DoneTaskParamQO.builder()
//                    .agyCode(qo.getAgyCode())
                    .mofDivCode(qo.getMofDivCode()).assignee(userCode).build());

            if (!todoResult.isSuccess())
                return CheckMsg.fail("获取代办异常" + todoResult.getMsgInfo());
            if (!doneResult.isSuccess())
                return CheckMsg.fail("获取已办异常" + doneResult.getMsgInfo());
            List<TodoTaskVO> tasks = todoResult.getData();
            Map<String, DoneTaskVO> doneTaskMap = doneResult.getData().stream().collect(Collectors.toMap(DoneTaskVO::getBusinessKey, Function.identity(),
                    (a, b) -> a.getEndTime().compareTo(b.getEndTime()) > 0 ? b : a));

            // 查询流程定义
            CheckMsg<Object> msg = null;
            PcxBill bill = pcxBills.get(0);
            msg = queryProcessDefinition(qo.getAgyCode(), qo.getMofDivCode(), bill.getBillFuncCode(), null);
            if (!msg.isSuccess())
                return CheckMsg.fail("获取流程定义异常" + msg.getMsgInfo());
            PcxProcessDefinition def = (PcxProcessDefinition) msg.getData();
            // 过滤出不跳过且非开始和结束节点的流程节点
            Pair<String, String> positionNode = findPositionCode(tasks.stream().collect(Collectors.toMap(TodoTaskVO::getBusiId, Function.identity())), doneTaskMap, def.getNodes(), billId, bill.getApproveStatus());
            String relPositionCode = findRelPositionCode(tasks.stream().collect(Collectors.toMap(TodoTaskVO::getBusiId, Function.identity())), doneTaskMap, def.getNodes(), billId, bill.getApproveStatus());
            return CheckMsg.success(
                    PositionVO
                            .builder()
                            .positionCode(positionNode.getKey())
                            .relPositionCode(relPositionCode)
                            .positionName(positionNode.getValue())
                            .taskId("")
                            .build());
        }catch (Exception e) {
            log.error("获取岗位编码异常", e);
            return CheckMsg.fail("获取岗位编码异常," + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void makeBillStart(String billId, String action) {
        log.info("makeBillStart {}, action: {}", billId, action);
        PcxBill bill = billMainService.view(billId);
        if ("rollback".equals(action)) {
            log.info("单据{}已退回,更新状态", billId);
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(billId)
                    .billStatus(BillStatusEnum.SAVE.getCode())
                    .approveStatus(BillApproveStatusEnum.APPROVING.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);
            pcxBalanceCtrlService.deleteCtrl(bill);
        }else if("revoke".equals(action)) {
            log.info("单据{}已撤回,更新状态", billId);
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(billId)
                    .billStatus(BillStatusEnum.SAVE.getCode())
                    .approveStatus(BillApproveStatusEnum.SAVE.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);
            pcxBalanceCtrlService.deleteCtrl(bill);
        }else{
            log.info("单据{}已送审,更新状态", billId);
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(billId)
                    .billStatus(BillStatusEnum.SUBMIT.getCode())
                    .approveStatus(BillApproveStatusEnum.APPROVING.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);
        }
    }

    @Override
    @Transactional
    public void processEnd(String billId) {
        PcxBill bill = billMainService.view(billId);
        // 升成内生凭证
        ecsBizService.saveBizData(billId);
        // 指标处理
        log.info("指标终审调用 billId {}",bill.getId());
        pcxBalanceCtrlService.auditCtrl(bill);
        // 更新冲销金额
        log.info("更新冲销金额 billId {}",bill.getId());
        callBackService.callBackUpdateLoanAmt(bill);

        pcxBillPayDetailService.submit(billId);
        BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                .billId(billId)
                .billStatus(BillStatusEnum.SUBMIT.getCode())
                .approveStatus(BillApproveStatusEnum.APPROVED.getCode())
                .payStatus(bill.getCheckAmt().compareTo(bill.getLoanAmt()) == 0 ? BillPayStatusEnum.PAID.getCode() : null)
                .build();
        billMainService.updateStatus(updateStatusDTO);
    }

    /**
     * 财务初审
     * @param billId
     * @return
     */
    @Override
    public void finance1AuditEnd(String billId) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<Void> revokeProcess(RevokeQO qo) {
        PcxBill bill = billMainService.view(qo.getBillId());
        Assert.notNull(bill, "单据不存在 {}", qo.getBillId());
        Assert.isTrue(BillApproveStatusEnum.APPROVED.getCode().equals(bill.getApproveStatus()), "单据状态未终审,无法进行终审撤回");
        // Assert.isTrue(!BillPayStatusEnum.PAID.getCode().equals(bill.getPayStatus()), "单据状态已支付,无法进行终审撤回");
        RevokeProcessParam param = new RevokeProcessParam();
        param.setAgyCode(qo.getAgyCode());
        param.setMofDivCode(qo.getMofDivCode());
        param.setTenantId(PtyContext.getTenantId());
        param.setSubmitter(PtyContext.getUsername());
        param.setBusiDelegate(PCX_WORKFLOW_LISTENER);
        param.setBusiModule(PcxConstant.SYS_ID);
        param.setBusiType(bill.getBillFuncCode());
        param.setBusiId(qo.getBillId());
        ptyWfRuntimeService.revokeProcess(param);
        // billMainService.updateApproveStatus(qo.getBillId(), BillApproveStatusEnum.APPROVING.getCode());
        // 更新单据状态
        billMainService.updateStatus(BillMainService.BillStatusUpdateDTO.builder()
                        .payStatus(BillPayStatusEnum.UNPAID.getCode())
                        .approveStatus(BillApproveStatusEnum.APPROVING.getCode())
                .build());
        // 更新支付明细支付状态
        pcxBillPayDetailService.cancelPay(qo.getBillId());
        pcxBalanceCtrlService.reStartCtrl(bill);
        pcxBillSyncService.revokeBill(qo.getBillId());
        ecsBizService.deleteBySrcBillIds(EcsBizDelQO.builder()
                        .mofDivCode(bill.getMofDivCode())
                        .agyCode(bill.getAgyCode())
                        .agyName(bill.getAgyName())
                        .fiscal(bill.getFiscal())
                        .billIds(Collections.singletonList(bill.getId()))
                .build());
        return CheckMsg.success();
    }

    public static Pair<String, String> findPositionCode(Map<String, TodoTaskVO> todoTaskMap, Map<String, DoneTaskVO> doneTaskMap, List<PcxNode> pcxNodes, String billId, String approveStatus) {
        // 过滤出不跳过且非开始和结束节点的流程节点
        List<PcxNode> _nodes = pcxNodes.stream().filter(node -> !node.isSkip() && !node.getId().equals(PcxNodeEnum.start.getId())
                && !node.getId().equals(PcxNodeEnum.end.getId())).collect(Collectors.toList());
        PcxNode last = _nodes.get(_nodes.size() - 1);
        PcxNode first = _nodes.get(0);
        // 如果是当前人代办任务, 进入代办岗
        TodoTaskVO todoTask = todoTaskMap.get(billId);
        DoneTaskVO doneTask = doneTaskMap.get(billId);
        if (todoTask != null) {
            return Pair.of(getNodeId(todoTask.getNodeId()), todoTask.getNode());
        }
        if (doneTask != null) {
            return Pair.of(getNodeId(doneTask.getNodeId()), doneTask.getNodeName());
        }
        // 其他的按单据审批状态区分，未审批的取制单岗，其他取最后一个节点作为位置代码
        // 2024/12/20 除终审的都返回make_bill
        // 2024/12/24 还原
        return approveStatus.equals(BillApproveStatusEnum.APPROVED.getCode()) ? Pair.of(last.getId(), last.getLabel()) : Pair.of(first.getId(), first.getLabel());
    }
    public static String findRelPositionCode(Map<String, TodoTaskVO> todoTaskMap, Map<String, DoneTaskVO> doneTaskMap, List<PcxNode> pcxNodes, String billId, String approveStatus) {
        // 过滤出不跳过且非开始和结束节点的流程节点
        List<PcxNode> _nodes = pcxNodes.stream().filter(node -> !node.isSkip() && !node.getId().equals(PcxNodeEnum.start.getId())
                && !node.getId().equals(PcxNodeEnum.end.getId())).collect(Collectors.toList());
        PcxNode last = _nodes.get(_nodes.size() - 1);
        PcxNode first = _nodes.get(0);
        // 如果是当前人代办任务, 进入代办岗
        TodoTaskVO todoTask = todoTaskMap.get(billId);
        DoneTaskVO doneTask = doneTaskMap.get(billId);
        if (todoTask != null) {
            return todoTask.getNodeId();
        }
        if (doneTask != null) {
            return doneTask.getNodeId();
        }
        // 其他的按单据审批状态区分，未审批的取制单岗，其他取最后一个节点作为位置代码
        // 2024/12/20 除终审的都返回make_bill
        // 2024/12/24 还原
        return approveStatus.equals(BillApproveStatusEnum.APPROVED.getCode()) ? last.getId() : first.getId();
    }

    private static String getNodeId(String nodeId) {
        if (nodeId.startsWith(PcxNodeEnum.relevance_audit.getId())) {
            return PcxNodeEnum.relevance_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.finance_audit.getId())) {
            return PcxNodeEnum.finance_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.dept_superior_audit.getId())) {
            return PcxNodeEnum.dept_superior_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.relevance_audit.getId())) {
            return PcxNodeEnum.relevance_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.divisional_agy_superior_audit.getId())) {
            return PcxNodeEnum.divisional_agy_superior_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.supervisor_agy_superior_audit.getId())) {
            return PcxNodeEnum.supervisor_agy_superior_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.high_agy_superior_audit.getId())) {
            return PcxNodeEnum.high_agy_superior_audit.getId();
        }
        if (nodeId.startsWith(PcxNodeEnum.financial_director.getId())) {
            return PcxNodeEnum.financial_director.getId();
        }
        return nodeId;
    }

    private PcxProcessDefinition convert2dto(ApprovalProcessDefinitionQO qo) {
        PcxProcessDefinition def = new PcxProcessDefinition();
        def.setTenantId(ObjectUtils.firstNonNull(qo.getTenantId(), PtyContext.getTenantId()));
        def.setWfId(qo.getWfId());
        def.setWfName(qo.getWfName());
        def.setBusiModule(PcxConstant.SYS_ID);
        def.setMofDivCode(qo.getMofDivCode());
        def.setAgyCode(qo.getAgyCode());
        def.setBusiType(qo.getBillType());
        def.setNodes(new ArrayList<>());
        AtomicInteger order = new AtomicInteger();
        qo.getAuditSteps().forEach(step -> {
            PcxNode node = new PcxNode();
            node.setId(step.getStepCode());
            node.setCustomLabel(step.getStepName());
            node.setSkip(!step.getEnabled());
            node.setOrder(order.incrementAndGet());
            def.getNodes().add(node);
        });
        def.getNodes().add(0, new PcxNode(PcxNodeEnum.start));
        PcxNode end = new PcxNode(PcxNodeEnum.end);
        end.setOrder(Integer.MAX_VALUE);
        def.getNodes().add(end);
        return def;
    }

    private FinanceProcessDefinition convert2financeDto(ApprovalProcessDefinitionQO qo) {
        FinanceProcessDefinition def = new FinanceProcessDefinition();
        def.setTenantId(ObjectUtils.firstNonNull(qo.getTenantId(), PtyContext.getTenantId()));
        def.setWfId(qo.getWfId());
        def.setWfName(qo.getWfName());
        def.setBusiModule(PcxConstant.SYS_ID);
        def.setMofDivCode(qo.getMofDivCode());
        def.setAgyCode(qo.getAgyCode());
        def.setBusiType(qo.getBillType());
        def.setNodes(new ArrayList<>());
        AtomicInteger order = new AtomicInteger();
        qo.getAuditSteps().forEach(step -> {
            FinanceNode node = new FinanceNode();
            node.setId(step.getStepCode());
            node.setLabel(step.getStepName());
            node.setSkip(!step.getEnabled());
            node.setOrder(order.incrementAndGet());
            def.getNodes().add(node);
        });
        def.getNodes().add(0, new FinanceNode(FinanceNodeEnum.start));
        FinanceNode end = new FinanceNode(FinanceNodeEnum.end);
        end.setOrder(Integer.MAX_VALUE);
        def.getNodes().add(end);
        return def;
    }

}
