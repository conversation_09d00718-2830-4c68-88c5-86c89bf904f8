package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.pcx.api.bill.PcxBillExpDetailMeetingService;
import com.pty.pcx.dao.bill.PcxBillExpDetailMeetingDao;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pub.common.util.CollectionUtil;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Indexed
public class PcxBillExpDetailMeetingServiceImpl implements PcxBillExpDetailMeetingService {

    @Resource
    private PcxBillExpDetailMeetingDao pcxBillExpDetailMeetingDao;

    @Override
    public List<PcxBillExpDetailMeeting> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpDetailMeetingDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailMeeting>()
                .in(CollectionUtil.isNotEmpty(billIds), PcxBillExpDetailMeeting::getBillId, billIds)
                .eq(PcxBillExpDetailMeeting::getAgyCode, agyCode)
                .eq(PcxBillExpDetailMeeting::getFiscal, fiscal)
                .eq(PcxBillExpDetailMeeting::getMofDivCode, mofDivCode));
    }

    @Override
    public List<PcxBillExpDetailMeeting> selectBatchIds(List<String> collect) {
        return pcxBillExpDetailMeetingDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailMeeting>()
                .in(CollectionUtil.isNotEmpty(collect), PcxBillExpDetailMeeting::getId, collect));
    }

    @Override
    public void deleteBatchIds(List<String> collect) {
        pcxBillExpDetailMeetingDao.deleteBatchIds(collect);
    }

}