package com.pty.pcx.service.impl.costcontrollevel;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.pty.mad.common.BizConditionEnum;
import com.pty.mad.common.MadFieldRelEnum;
import com.pty.mad.common.PtyRuleConstants;
import com.pty.mad.entity.MadEmpField;
import com.pty.mad.entity.MadEmployee;
import com.pty.pcx.api.costcontrollevel.PcxCostControlLevelService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.costcontrollevel.PcxCostControlLevelDao;
import com.pty.pcx.dto.PcxCarryOverDTO;
import com.pty.pcx.dto.costcontrollevel.BizRuleDTO;
import com.pty.pcx.dto.costcontrollevel.ConditionJsonDTO;
import com.pty.pcx.dto.costcontrollevel.PcxCostControlLevelDTO;
import com.pty.pcx.entity.costcontrollevel.PcxCostControlLevel;
import com.pty.pcx.qo.costcontrollevel.PcxCostControlLevelQO;
import com.pty.pcx.qo.costcontrollevel.PcxEmployeeWithCostLevelQO;
import com.pty.pcx.service.impl.bas.TransOptService;
import com.pty.pcx.vo.costcontrollevel.BizRuleVO;
import com.pty.pcx.vo.costcontrollevel.PcxCostControlLevelVO;
import com.pty.pcx.vo.costcontrollevel.PcxEmployeeWithCostLevelVO;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.rule.RuleType;
import com.pty.rule.api.IPaBizRuleService;
import com.pty.rule.api.IPtyRuleService;
import com.pty.rule.entity.PaBizRule;
import com.pty.rule.entity.PtyRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.pty.mad.api.IMadEmpFieldService;
import org.pty.mad.api.IMadEmployeeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 费用控制级别(PcxCostControlLevel)表服务实现类
 */
@Indexed
@Slf4j
@Service
public class PcxCostControlLevelServiceImpl implements PcxCostControlLevelService {

    @Autowired
    private PcxCostControlLevelDao pcxCostControlLevelDao;
    @Autowired
    private IPtyRuleService ptyRuleService;
    @Autowired
    private IPaBizRuleService paBizRuleService;
    @Autowired
    private IMadEmpFieldService iMadEmpFieldService;
    @Autowired
    private IMadEmployeeService employeeService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private TransOptService transOptService;

    /**
     * 分页查询
     *
     * @param pcxCostControlLevel 筛选条件
     * @return 查询结果
     */
    @Override
    public List<PcxCostControlLevelVO> queryList(PcxCostControlLevel pcxCostControlLevel) {
        List<PcxCostControlLevel> pcxCostControlLevels = pcxCostControlLevelDao.selectList(buildQueryWrapper(pcxCostControlLevel));
        List<String> bizCodes = pcxCostControlLevels
                .stream()
                .map(PcxCostControlLevel::getCostControlCode)
                .collect(Collectors.toList());

        return pcxCostControlLevels
                .stream()
                .map(costControlLevel -> convertToVO(costControlLevel, bizCodes))
                .collect(Collectors.toList());
    }


    @Override
    public PageInfo<PcxCostControlLevelVO> selectListPage(PcxCostControlLevelQO pcxCostControlLevelQO) {
        PageInfo<PcxCostControlLevel> pageInfo = PageHelper.startPage(pcxCostControlLevelQO.getPageIndex(), pcxCostControlLevelQO.getPageSize())
                .doSelectPageInfo(() -> {
                    pcxCostControlLevelDao.selectList(buildQueryWrapper(pcxCostControlLevelQO));
                });

        List<PcxCostControlLevel> pcxCostControlLevels = pageInfo.getList();
        List<String> bizCodes = pcxCostControlLevels
                .stream()
                .map(PcxCostControlLevel::getCostControlCode)
                .collect(Collectors.toList());

        List<PcxCostControlLevelVO> voList = pcxCostControlLevels
                .stream()
                .map(costControlLevel -> convertToVO(costControlLevel, bizCodes))
                .collect(Collectors.toList());

        PageInfo<PcxCostControlLevelVO> voPageInfo = new PageInfo<>(voList);
        voPageInfo.setPageNum(pageInfo.getPageNum());
        voPageInfo.setPageSize(pageInfo.getPageSize());
        voPageInfo.setTotal(pageInfo.getTotal());
        voPageInfo.setPages(pageInfo.getPages());
        voPageInfo.setHasNextPage(pageInfo.isHasNextPage());
        voPageInfo.setHasPreviousPage(pageInfo.isHasPreviousPage());
        voPageInfo.setIsFirstPage(pageInfo.isIsFirstPage());
        voPageInfo.setIsLastPage(pageInfo.isIsLastPage());

        return voPageInfo;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public PcxCostControlLevelVO queryById(String id) {
        PcxCostControlLevel pcxCostControlLevel = this.pcxCostControlLevelDao.selectById(id);
        if (pcxCostControlLevel != null) {
            String bizCode = pcxCostControlLevel.getCostControlCode();
            return convertToVO(pcxCostControlLevel, bizCode);
        } else {
            return null;
        }
    }

    /**
     * 新增数据
     *
     * @param pcxCostControlLevelDTO 实例对象
     * @return 实例对象
     */
    @Override
    public void insert(PcxCostControlLevelDTO pcxCostControlLevelDTO) {

        PcxCostControlLevel pcxCostControlLevel = new PcxCostControlLevel();
        BeanUtils.copyProperties(pcxCostControlLevelDTO, pcxCostControlLevel);
        pcxCostControlLevel.setId(IDGenerator.id());
        pcxCostControlLevel.setPriority(pcxCostControlLevelDTO.getPriority());
        pcxCostControlLevel.setCostControlCode(IdUtil.getSnowflakeNextIdStr());
        pcxCostControlLevel.setCreatedTime(DateUtil.nowTime());
        pcxCostControlLevel.setCreatorCode(PtyContext.getUsername());
        pcxCostControlLevel.setCreatorName(PtyContext.getUsername());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                pcxCostControlLevelDao.insert(pcxCostControlLevel);

                savePtyRulesAndBizRules(pcxCostControlLevelDTO, pcxCostControlLevel.getCostControlCode());
            }
        });
    }

    /**
     * 修改数据
     *
     * @param pcxCostControlLevelDTO 实例对象
     * @return 实例对象
     */
    @Override
    public void update(PcxCostControlLevelDTO pcxCostControlLevelDTO) {
        PcxCostControlLevelVO pcxCostControlLevelVO = queryById(pcxCostControlLevelDTO.getId());
        if (pcxCostControlLevelVO == null) {
            return;
        }
        String bizCode = pcxCostControlLevelVO.getCostControlCode();

        PaBizRule paBizRule = new PaBizRule();
        paBizRule.setBizCodes(Arrays.asList(bizCode));
        paBizRule.setAgyCode(pcxCostControlLevelDTO.getAgyCode());
        paBizRule.setFiscal(pcxCostControlLevelDTO.getFiscal());
        paBizRule.setMofDivCode(pcxCostControlLevelDTO.getMofDivCode());

        List<PaBizRule> paBizRuleList = paBizRuleService.selectList(paBizRule);

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                if (!paBizRuleList.isEmpty()) {
                    paBizRuleService.batchDelete(paBizRuleList.stream().map(PaBizRule::getId).collect(Collectors.toList()));

                    List<String> ruleIds = paBizRuleList.stream().map(PaBizRule::getPtyRuleId).collect(Collectors.toList());
                    ptyRuleService.batchDeleteRuleById(ruleIds);
                }

                PcxCostControlLevel pcxCostControlLevel = new PcxCostControlLevel();
                BeanUtils.copyProperties(pcxCostControlLevelVO, pcxCostControlLevel);
                pcxCostControlLevel.setPositionRank(pcxCostControlLevelDTO.getPositionRank());
                pcxCostControlLevel.setExpenseTypeCode(pcxCostControlLevelDTO.getExpenseTypeCode());
                pcxCostControlLevel.setExpenseTypeName(pcxCostControlLevelDTO.getExpenseTypeName());
                pcxCostControlLevel.setPriority(pcxCostControlLevelDTO.getPriority());
                pcxCostControlLevel.setModifiedTime(DateUtil.nowTime());
                pcxCostControlLevel.setModifier(PtyContext.getUsername());
                pcxCostControlLevelDao.updateById(pcxCostControlLevel);

                savePtyRulesAndBizRules(pcxCostControlLevelDTO, pcxCostControlLevel.getCostControlCode());
            }
        });

    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteById(String id) {
        PcxCostControlLevel pcxCostControlLevel = pcxCostControlLevelDao.selectById(id);
        if (pcxCostControlLevel == null) {
            return false;
        }
        String bizCode = pcxCostControlLevel.getCostControlCode();

        PaBizRule paBizRule = new PaBizRule();
        paBizRule.setBizCodes(Arrays.asList(bizCode));

        List<PaBizRule> paBizRuleList = paBizRuleService.selectList(paBizRule);

        if (!paBizRuleList.isEmpty()) {
            paBizRuleService.batchDelete(paBizRuleList.stream().map(PaBizRule::getId).collect(Collectors.toList()));

            List<String> ruleIds = paBizRuleList.stream().map(PaBizRule::getPtyRuleId).collect(Collectors.toList());
            ptyRuleService.batchDeleteRuleById(ruleIds);
        }

        return this.pcxCostControlLevelDao.deleteById(id) > 0;
    }

    @Override
    public List<PcxEmployeeWithCostLevelVO> getEmpsWithCostLevelByCodes(PcxEmployeeWithCostLevelQO pcxEmployeeWithCostLevelQO) {
        Assert.hasText(pcxEmployeeWithCostLevelQO.getAgyCode(), "单位编码不能为空");
        Assert.notNull(pcxEmployeeWithCostLevelQO.getFiscal(), "年度不能为空");
        Assert.hasText(pcxEmployeeWithCostLevelQO.getMofDivCode(), "区划不能为空");

        List<MadEmployee> madEmployees = new ArrayList<>();
        if (StringUtils.hasText(pcxEmployeeWithCostLevelQO.getName()) || StringUtils.hasText(pcxEmployeeWithCostLevelQO.getNameLike())) {
            MadEmployee emp = new MadEmployee();
            emp.setAgyCode(pcxEmployeeWithCostLevelQO.getAgyCode());
            emp.setFiscal(pcxEmployeeWithCostLevelQO.getFiscal());
            emp.setMofDivCode(pcxEmployeeWithCostLevelQO.getMofDivCode());
            emp.setMadName(pcxEmployeeWithCostLevelQO.getName());
            emp.setMadNameLike(pcxEmployeeWithCostLevelQO.getNameLike());
            madEmployees = employeeService.select(emp);
        }

        if (CollectionUtils.isNotEmpty(pcxEmployeeWithCostLevelQO.getMadCodes())) {
            madEmployees
                    = employeeService.listByMadCodes(pcxEmployeeWithCostLevelQO.getMadCodes(), pcxEmployeeWithCostLevelQO.getAgyCode(), pcxEmployeeWithCostLevelQO.getFiscal(), pcxEmployeeWithCostLevelQO.getMofDivCode());
        }

        LambdaQueryWrapper<PcxCostControlLevel> queryWrapper = buildLambdaQueryWrapper(pcxEmployeeWithCostLevelQO);
        List<PcxCostControlLevel> pcxCostControlLevels = pcxCostControlLevelDao.selectList(queryWrapper);

        Map<String, PaBizRule> bizRuleMap = new HashMap<>();
        Map<String, List<PaBizRule>> bizRuleLevelMap = new HashMap<>();
        Map<String, PtyRule> ptyRuleMap = new HashMap<>();
        List<String> ptyRuleIds = new ArrayList<>();
        if (!pcxCostControlLevels.isEmpty()) {
            List<String> bizCodes = pcxCostControlLevels.stream().map(PcxCostControlLevel::getCostControlCode).collect(Collectors.toList());

            PaBizRule paBizRule = new PaBizRule();
            paBizRule.setAgyCode(pcxEmployeeWithCostLevelQO.getAgyCode());
            paBizRule.setMofDivCode(pcxEmployeeWithCostLevelQO.getMofDivCode());
            paBizRule.setFiscal(String.valueOf(pcxEmployeeWithCostLevelQO.getFiscal()));
            paBizRule.setBizCodes(bizCodes);
            List<PaBizRule> paBizRuleList = paBizRuleService.selectList(paBizRule);

            for (PaBizRule bizRule : paBizRuleList) {
                String conditionJson = bizRule.getConditionJson();
                if (conditionJson != null) {
                    try {
                        List<ConditionJsonDTO> conditionJsonDTOS = JSON.parseArray(conditionJson, ConditionJsonDTO.class);

                        for (ConditionJsonDTO conditionJsonDTO : conditionJsonDTOS) {
                            List<String> vals = conditionJsonDTO.getVal();
                            for (String val : vals) {
                                bizRuleLevelMap.computeIfAbsent(val, k -> new ArrayList<>()).add(bizRule);
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取职级时出错", e);
                    }
                }

                bizRuleMap.put(bizRule.getBizCode(), bizRule);
                ptyRuleIds.add(bizRule.getPtyRuleId());
            }

            if (!paBizRuleList.isEmpty()) {
                PtyRule ptyRule = new PtyRule();
                ptyRule.setRuleIds(ptyRuleIds);
                List<PtyRule> ptyRules = ptyRuleService.selectRule(ptyRule);
                for (PtyRule rule : ptyRules) {
                    ptyRuleMap.put(rule.getRuleId(), rule);
                }
            }
        }

        // 获取关联字段名称
        MadEmpField madEmpField = new MadEmpField();
        madEmpField.setAgyCode(pcxEmployeeWithCostLevelQO.getAgyCode());
        madEmpField.setFiscal(pcxEmployeeWithCostLevelQO.getFiscal());
        List<String> atomCodes = Arrays.asList(MadFieldRelEnum.MANAGERIAL_RANK.name(), MadFieldRelEnum.SPECIALIST_RANK.name(), MadFieldRelEnum.SERVICEGRADE.name());
        madEmpField.setAtomCodes(atomCodes);
        List<MadEmpField> madEmpFieldList = iMadEmpFieldService.list(madEmpField);
        List<String> fields =
                madEmpFieldList.stream().map(MadEmpField::getFieldCode).map(StrUtil::toCamelCase).collect(Collectors.toList());

        return madEmployees
                .stream()
                .map(madEmployee -> {
                    PcxEmployeeWithCostLevelVO pcxEmployeeWithCostLevelVO = new PcxEmployeeWithCostLevelVO();
                    BeanUtils.copyProperties(madEmployee, pcxEmployeeWithCostLevelVO);

                    if (!pcxCostControlLevels.isEmpty()) {

                        List<PaBizRule> paBizRuleList = getPaBizRules(bizRuleLevelMap, madEmployee, fields);
                        List<String> bizCodeList = paBizRuleList.stream().map(PaBizRule::getBizCode).collect(Collectors.toList());

                        List<PcxCostControlLevel> pcxCostControlLevelList = pcxCostControlLevels
                                .stream()
                                .filter(pcxCostControlLevel -> bizCodeList.contains(pcxCostControlLevel.getCostControlCode()))
                                .sorted(Comparator.comparingInt(PcxCostControlLevel::getPriority).reversed())
                                .collect(Collectors.toList());

                        // 如果匹配上规则，则选择优先级最高的
                        // 如果没有匹配上规则，选择优先级最低的
                        PcxCostControlLevel pcxCostControlLevel;
                        if (!pcxCostControlLevelList.isEmpty()) {
                            pcxCostControlLevel = pcxCostControlLevelList.get(0);

                            Set<String> expenseTypeCodes = pcxCostControlLevelList.stream().map(PcxCostControlLevel::getExpenseTypeCode).collect(Collectors.toSet());
                            // 没匹配上费用类型的选择优先级最低的
                            Map<String, List<PcxCostControlLevel>> costControlLevelGroupedMap = pcxCostControlLevels
                                    .stream()
                                    .filter(costControlLevel -> !expenseTypeCodes.contains(costControlLevel.getExpenseTypeCode()))
                                    .collect(Collectors.groupingBy(PcxCostControlLevel::getExpenseTypeCode));

                            List<PcxCostControlLevel> controlLevels = new ArrayList<>(pcxCostControlLevelList);
                            controlLevels.addAll(costControlLevelGroupedMap.values().stream().map(value -> value.get(0)).collect(Collectors.toList()));

                            pcxEmployeeWithCostLevelVO.setPcxCostControlLevels(controlLevels);
                        } else {
                            pcxCostControlLevel = pcxCostControlLevels.get(0);

                            Map<String, List<PcxCostControlLevel>> costControlLevelGroupedMap = pcxCostControlLevels.stream().collect(Collectors.groupingBy(PcxCostControlLevel::getExpenseTypeCode));
                            List<PcxCostControlLevel> controlLevels = costControlLevelGroupedMap.values().stream().map(value -> value.get(0)).collect(Collectors.toList());
                            pcxEmployeeWithCostLevelVO.setPcxCostControlLevels(controlLevels);
                        }

                        pcxEmployeeWithCostLevelVO.setPcxCostControlLevel(pcxCostControlLevel);

                        if (bizRuleMap.containsKey(pcxCostControlLevel.getCostControlCode())) {
                            PaBizRule paBizRule = bizRuleMap.get(pcxCostControlLevel.getCostControlCode());
                            pcxEmployeeWithCostLevelVO.setPaBizRule(paBizRule);

                            pcxEmployeeWithCostLevelVO.setPtyRule(ptyRuleMap.get(paBizRule.getPtyRuleId()));
                        }
                    }

                    return pcxEmployeeWithCostLevelVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public CheckMsg<String> carryOver(PcxCarryOverDTO pcxCarryOverDTO) {
        String nextFiscal = Integer.valueOf(pcxCarryOverDTO.getFiscal()) + 1 + "";
        List<PcxCostControlLevel> pcxCostControlLevels = pcxCostControlLevelDao.selectList(Wrappers.lambdaQuery(PcxCostControlLevel.class)
                .eq(PcxCostControlLevel::getFiscal, nextFiscal));
        if (CollectionUtils.isNotEmpty(pcxCostControlLevels)) {
            return CheckMsg.success("已存在新数据");
        }
        pcxCostControlLevels = pcxCostControlLevelDao.selectList(Wrappers.lambdaQuery(PcxCostControlLevel.class)
                .eq(PcxCostControlLevel::getFiscal, pcxCarryOverDTO.getFiscal()));
        List<PcxCostControlLevel> nextFiscalList = carryOverList(pcxCostControlLevels, nextFiscal);
        transOptService.carryOverCostControlLevel(nextFiscalList);
        return CheckMsg.success("结转完成");
    }

    private List<PcxCostControlLevel> carryOverList(List<PcxCostControlLevel> pcxCostControlLevels, String nextFiscal) {
        List<PcxCostControlLevel> result = new ArrayList<>();
        for (PcxCostControlLevel pcxCostControlLevel : pcxCostControlLevels) {
            pcxCostControlLevel.setId(nextFiscal + "@" + pcxCostControlLevel.getId().substring(5));
            pcxCostControlLevel.setCostControlCode(nextFiscal + "@" + pcxCostControlLevel.getCostControlCode().substring(5));
            pcxCostControlLevel.setFiscal(nextFiscal);
            result.add(pcxCostControlLevel);
        }
        return result;
    }

    private List<PaBizRule> getPaBizRules(Map<String, List<PaBizRule>> bizRuleLevelMap, MadEmployee madEmployee, List<String> fields) {
        List<PaBizRule> paBizRuleList = new ArrayList<>();
        for (String fieldName : fields) {
            try {
                Field field = madEmployee.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                String fieldValue = (String) field.get(madEmployee);
                if (bizRuleLevelMap.containsKey(fieldValue)) {
                    paBizRuleList.addAll(bizRuleLevelMap.get(fieldValue));
                }
            } catch (Exception e) {
                log.error("获取 PaBizRule 失败", e);
            }
        }

        return paBizRuleList;
    }

    private LambdaQueryWrapper<PcxCostControlLevel> buildLambdaQueryWrapper(PcxEmployeeWithCostLevelQO pcxEmployeeWithCostLevelQO) {
        LambdaQueryWrapper<PcxCostControlLevel> queryWrapper = Wrappers.lambdaQuery(PcxCostControlLevel.class);
        queryWrapper.eq(StringUtils.hasText(pcxEmployeeWithCostLevelQO.getAgyCode()), PcxCostControlLevel::getAgyCode, pcxEmployeeWithCostLevelQO.getAgyCode());
        queryWrapper.eq(pcxEmployeeWithCostLevelQO.getFiscal() != null, PcxCostControlLevel::getFiscal, pcxEmployeeWithCostLevelQO.getFiscal());
        queryWrapper.eq(StringUtils.hasText(pcxEmployeeWithCostLevelQO.getMofDivCode()), PcxCostControlLevel::getMofDivCode, pcxEmployeeWithCostLevelQO.getMofDivCode());

        List<String> costControlCodes = pcxEmployeeWithCostLevelQO.getCostControlCodes();
        if (CollectionUtils.isNotEmpty(costControlCodes)) {
            queryWrapper.in(PcxCostControlLevel::getCostControlCode, costControlCodes);
        }
        queryWrapper.orderByAsc(PcxCostControlLevel::getPriority);
        return queryWrapper;
    }

    private LambdaQueryWrapper<PcxCostControlLevel> buildQueryWrapper(PcxCostControlLevel pcxCostControlLevel) {
        LambdaQueryWrapper<PcxCostControlLevel> queryWrapper = Wrappers.lambdaQuery(PcxCostControlLevel.class);
        queryWrapper.eq(StringUtils.hasText(pcxCostControlLevel.getAgyCode()), PcxCostControlLevel::getAgyCode, pcxCostControlLevel.getAgyCode());
        queryWrapper.eq(StringUtils.hasText(pcxCostControlLevel.getMofDivCode()), PcxCostControlLevel::getMofDivCode, pcxCostControlLevel.getMofDivCode());
        queryWrapper.eq(StringUtils.hasText(pcxCostControlLevel.getFiscal()), PcxCostControlLevel::getFiscal, pcxCostControlLevel.getFiscal());
        queryWrapper.eq(StringUtils.hasText(pcxCostControlLevel.getExpenseTypeCode()), PcxCostControlLevel::getExpenseTypeCode, pcxCostControlLevel.getExpenseTypeCode());
        queryWrapper.orderByDesc(PcxCostControlLevel::getPriority);
        return queryWrapper;
    }

    private void savePtyRulesAndBizRules(PcxCostControlLevelDTO pcxCostControlLevelDTO, String costControlCode) {
        List<BizRuleDTO> paBizRuleDtos = pcxCostControlLevelDTO.getPaBizRuleDtos();

        List<PtyRule> ptyRules = Lists.newArrayList();
        List<PaBizRule> paBizRules = Lists.newArrayList();
        for (BizRuleDTO paBizRuleDto : paBizRuleDtos) {
            PtyRule rule = new PtyRule();
            rule.setRuleId(IDGenerator.id());
            rule.setRuleName(DateUtil.getYear(DateUtil.nowDate()) + IDGenerator.id());
            rule.setRuleDesc("费用控制级别");
            rule.setRuleType(RuleType.LEGAL.name());
            rule.setRuleAct(PtyRuleConstants.PtyRuleActionEnum.ACCESS.name());
            rule.setRuleModule(PtyRuleConstants.PTY_RULE_MODULE);
            rule.setWhenCondition(MadFieldRelEnum.buildWhenCondition(BizConditionEnum.COST_CONTROL_BIZ.getCode(), paBizRuleDto.getConditionJson()));
            rule.setMustCondition(MadFieldRelEnum.buildMustCondition(BizConditionEnum.COST_CONTROL_BIZ.getCode(), paBizRuleDto.getConditionJson()));
            rule.setEnabled(1);
            rule.setRuleCreator(PtyContext.getUsername());
            rule.setRuleCreatedDate(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
            rule.setRuleModifyDate(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
            rule.setRuleReference("");
            rule.setRuleGroup("COST_CONTROL");
            rule.setRuleCorrect(1);
            rule.setRuleIdentify(0);
            ptyRules.add(rule);

            PaBizRule newBizRule = PaBizRule.builder()
                    .id(IDGenerator.id())
                    .ruleCode(costControlCode + "-" + rule.getRuleId())
                    .ruleName("费用控制级别")
                    .mofDivCode(pcxCostControlLevelDTO.getMofDivCode()).agyCode(pcxCostControlLevelDTO.getAgyCode()).fiscal(String.valueOf(pcxCostControlLevelDTO.getFiscal()))
                    .bizType(BizConditionEnum.COST_CONTROL_BIZ.getCode())
                    .bizCode(costControlCode)
                    .conditionJson(paBizRuleDto.getConditionJson())
                    .ptyRuleId(rule.getRuleId())
                    .remark(paBizRuleDto.getRemark())
                    .seq(paBizRuleDto.getSeq())
                    .build();
            paBizRules.add(newBizRule);
        }

        ptyRuleService.batchInsertRule(ptyRules);
        paBizRuleService.batchInsert(paBizRules);

    }

    private PcxCostControlLevelVO convertToVO(PcxCostControlLevel pcxCostControlLevel, String... bizCodes) {
        return this.convertToVO(pcxCostControlLevel, Arrays.asList(bizCodes));
    }

    private PcxCostControlLevelVO convertToVO(PcxCostControlLevel pcxCostControlLevel, List<String> bizCodes) {
        PaBizRule paBizRule = new PaBizRule();
        paBizRule.setAgyCode(pcxCostControlLevel.getAgyCode());
        paBizRule.setMofDivCode(pcxCostControlLevel.getMofDivCode());
        paBizRule.setFiscal(pcxCostControlLevel.getFiscal());
        paBizRule.setBizCodes(bizCodes);
        List<PaBizRule> paBizRules = paBizRuleService.selectList(paBizRule);

        PcxCostControlLevelVO pcxCostControlLevelVO = new PcxCostControlLevelVO();
        BeanUtils.copyProperties(pcxCostControlLevel, pcxCostControlLevelVO);

        Map<String, List<String>> bizRuleMap = paBizRules
                .stream()
                .collect(Collectors.groupingBy(PaBizRule::getBizCode, Collectors.mapping(PaBizRule::getConditionJson, Collectors.toList())));

        List<String> conditionJsons = bizRuleMap.get(pcxCostControlLevel.getCostControlCode());
        try {

            if (conditionJsons != null) {
                List<BizRuleVO> bizRuleVOS = conditionJsons
                        .stream()
                        .map(conditionJson -> {
                            BizRuleVO bizRuleVO = new BizRuleVO();
                            bizRuleVO.setConditionJson(conditionJson);
                            return bizRuleVO;
                        })
                        .collect(Collectors.toList());

                String positionRankSetting = parseConditionJsons(conditionJsons);
                pcxCostControlLevelVO.setPaBizRuleDtos(bizRuleVOS);
                pcxCostControlLevelVO.setPositionRankSetting(positionRankSetting);
            }

        } catch (Exception e) {
            log.error("获取岗位职级设置失败", e);
        }

        return pcxCostControlLevelVO;
    }

    private String parseConditionJsons(List<String> conditionJsons) {
        List<ConditionJsonDTO> records = new ArrayList<>();
        for (String conditionJson : conditionJsons) {
            if (conditionJson != null) {
                records.addAll(JSON.parseArray(conditionJson, ConditionJsonDTO.class));
            }
        }

        Map<String, List<ConditionJsonDTO>> recordMap = records.stream().collect(Collectors.groupingBy(ConditionJsonDTO::getName));

        return recordMap
                .entrySet()
                .stream()
                .map(entry -> {
                    String vals = entry.getValue()
                            .stream()
                            .map(record -> {
                                if (CollectionUtils.isEmpty(record.getLabel())) {
                                    return record.getVal().stream().sorted().collect(Collectors.joining("、"));
                                } else {
                                    return record.getLabel().stream().sorted().collect(Collectors.joining("、"));
                                }
                            })
                            .sorted()
                            .collect(Collectors.joining("、"));
                    return entry.getKey() + "包括 " + vals;
                })
                .collect(Collectors.joining(", "));
    }

}
