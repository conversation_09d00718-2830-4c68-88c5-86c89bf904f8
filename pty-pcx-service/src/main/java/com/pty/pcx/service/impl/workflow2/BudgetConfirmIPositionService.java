package com.pty.pcx.service.impl.workflow2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.pty.mad.entity.MadDepartment;
import com.pty.mad.entity.MadProject;
import com.pty.mad.qo.MadProjectQo;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.api.bill.PcxBillAmtApportionDepartmentService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.workflow2.IPositionService;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillAmtApportionDepartment;
import com.pty.pcx.vo.bill.PcxBillBalanceVO;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pty.mad.api.IMadDepartmentService;
import org.pty.mad.api.IMadProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class BudgetConfirmIPositionService implements IPositionService<String> {

    @Resource
    private PcxBillService pcxBillService;

    @Resource
    private IMadProjectService projectService;

    @Resource
    private IMadDepartmentService departmentService;

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Autowired
    private PcxBillAmtApportionDepartmentService pcxBillAmtApportionDepartmentService;


    @Override
    public List<String> getPositionIds() {
        return Collections.singletonList(PcxNodeEnum.budget_confirm.getId());
    }

    @Override
    public List<String> findPositionUser(String billNo) {
        CheckMsg<PcxBillVO> msg = pcxBillService.view(billNo, PositionBlockEnum.FUND_SOURCE);
        Assert.state(msg != null, "调用单据异常");
        Assert.state(msg.isSuccess(), "调用单据异常" + msg.getMsgInfo());
        PcxBillVO vo = msg.getData();
        PcxBill basicInfo = vo.getBasicInfo();
        List<String> approvals = new ArrayList<>();
//        List<PcxBillBalanceVO> fundSources = vo.getBudget();

//        List<String> budDeptCodes = new ArrayList<>();

//        if (!CollectionUtils.isEmpty(fundSources)) {
//            // 先找经费来源上的项目负责人
//            List<String> projectCodes = fundSources.stream().map(PcxBillBalanceVO::getProjectCode).filter(Objects::nonNull)
//                    .distinct().collect(Collectors.toList());
//
//            Map<String, List<PcxBillBalanceVO>> projBalances = fundSources.stream().collect(Collectors.groupingBy(PcxBillBalanceVO::getProjectCode));
//
//            if (!CollectionUtils.isEmpty(projectCodes)) {
//                MadProjectQo qo = new MadProjectQo();
//                qo.setAgyCode(basicInfo.getAgyCode());
//                qo.setMofDivCode(basicInfo.getMofDivCode());
//                qo.setFiscal(Integer.valueOf(basicInfo.getFiscal()));
//                qo.setMadCodes(projectCodes);
//                List<MadProject> madProjects = projectService.selectByQo(qo).stream().filter(p -> StringUtils.isNoneBlank(p.getProManager())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(madProjects)) {
//                    approvals.addAll(madProjects.stream().map(MadProject::getProManager).distinct().collect(Collectors.toList()));
//                    // 移除已找到项目负责人的经费来源
//                    madProjects.forEach(madProject -> {projBalances.remove(madProject.getMadCode());});
//                }
//            }
//            // 找不到项目负责人，找预算部门负责人
//            budDeptCodes = projBalances.values().stream()
//                    .flatMap(Collection::stream)
//                    .map(PcxBillBalanceVO::getBudDepartmentCode)
//                    .filter(StrUtil::isNotBlank)
//                    .distinct()
//                    .collect(Collectors.toList());
//
//        }

        List<PcxBillAmtApportionDepartment> departments = pcxBillAmtApportionDepartmentService.selectByBillId(basicInfo.getId());
        if (CollUtil.isNotEmpty(departments)) {
            MadDepartment department = new MadDepartment();
            List<String> departmentMadCodes = departments.stream().map(PcxBillAmtApportionDepartment::getBudDepartmentCode).distinct().collect(Collectors.toList());
            department.setMadCodes(departmentMadCodes);
            department.setMofDivCode(basicInfo.getMofDivCode());
//            department.setAgyCode(basicInfo.getAgyCode());
            department.setFiscal(Integer.valueOf(basicInfo.getFiscal()));
            List<MadDepartment> madDepartments = departmentService.select(department);
            Assert.isTrue(!CollectionUtils.isEmpty(madDepartments), "未找到预算部门信息");
            Assert.isTrue(madDepartments.size() == departmentMadCodes.size(), "预算部门{}信息不存在", departmentMadCodes.removeAll(madDepartments.stream().map(MadDepartment::getMadCode).collect(Collectors.toList())));
            List<MadDepartment> noneLeaderDepartments = madDepartments.stream().filter(d -> StringUtils.isBlank(d.getDeptLeaderCode())).collect(Collectors.toList());
            Assert.isTrue(CollUtil.isEmpty(noneLeaderDepartments), "请设置确定预算部门{}负责人", noneLeaderDepartments.stream().map(MadDepartment::getMadName).collect(Collectors.toList()));
            approvals.addAll(madDepartments.stream().map(dept -> {
                if (StrUtil.isNotBlank(dept.getDeptLeaderCode())) {
                    // 如果部门负责人和填报人一致，并且部门有支出审核人，则审批人员为有支出审核人，否则为部门负责人
                    if (dept.getDeptLeaderCode().equals(basicInfo.getClaimantCode()) && StrUtil.isNotBlank(dept.getApprovePersonnelCodes()))
                        return dept.getApprovePersonnelCodes();
                    return dept.getDeptLeaderCode();
                }
                return StrUtil.EMPTY;
            }).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        }


        return getUserIds(null, basicInfo.getMofDivCode(), Integer.valueOf(basicInfo.getFiscal()), approvals);
    }

    @Override
    public IPcxMadEmployeeService getMadEmployeeService() {
        return pcxMadEmployeeService;
    }
}
