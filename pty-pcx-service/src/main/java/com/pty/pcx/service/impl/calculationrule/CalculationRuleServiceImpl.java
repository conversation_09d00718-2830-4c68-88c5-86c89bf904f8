package com.pty.pcx.service.impl.calculationrule;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.pty.mad.common.BizConditionEnum;
import com.pty.mad.common.MadFieldRelEnum;
import com.pty.mad.common.PtyRuleConstants;
import com.pty.pcx.api.calculationrule.ICalculationRuleService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.calculationrule.CalculationRuleDTO;
import com.pty.pcx.dto.calculationrule.CalculationRuleReturnDTO;
import com.pty.pcx.dto.calculationrule.MealAllowanceDTO;
import com.pty.pcx.dto.ecs.ValsetDTO;
import com.pty.pcx.pty.IPcxRuleService;
import com.pty.pcx.qo.bas.PcxBasExpBizTypeQO;
import com.pty.pcx.vo.setting.PaBizRuleVO;
import com.pty.pub.common.util.*;
import com.pty.rule.RuleType;
import com.pty.rule.api.IPaBizRuleService;
import com.pty.rule.api.IPtyRuleService;
import com.pty.rule.entity.PaBizRule;
import com.pty.rule.entity.PtyRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 计算规则实现类
 */
@Service
@Indexed
@Slf4j
public class CalculationRuleServiceImpl implements ICalculationRuleService {

    @Autowired
    private IPtyRuleService ruleService;

    @Autowired
    private ParamEncapsuleContext paramEncapsuleContext;

    @Autowired
    private IPaBizRuleService paBizRuleService;

    @Autowired
    private IPcxRuleService pcxRuleService;

    /**
     * 根据业务数据获取计算规则公式以及计算结果
     * @param calculationRuleDTOList 请求参数，计算规则中变量的数据（包含判断条件变量以及计算公式变量）
     * @return 返回符合条件的全部计算公式以及每一项的计算结果
     */
    @Override
    public List<CalculationRuleReturnDTO> getCalculationRule(List<CalculationRuleDTO> calculationRuleDTOList) {
        List<CalculationRuleReturnDTO> returnDTOList = new ArrayList<>();
        if (CollectionUtil.isEmpty(calculationRuleDTOList)) {
            return returnDTOList;
        }
        CalculationRuleDTO param = calculationRuleDTOList.get(0);
        //获取pa_biz_rule数据，获取json数据，便于后续公式计算以及标准获取
        PaBizRule paBizRule = getPaBizRule(param);
        if (paBizRule == null || StringUtil.isEmpty(paBizRule.getConditionJson())) {
            return returnDTOList;
        }
        List<PtyRule> rules = this.selectCalculationRule(paBizRule);
        if (CollectionUtil.isNotEmpty(rules)) {
            //获取计算公式
            PtyRule calu = rules.stream().filter(e -> paBizRule.getPtyRuleId().equals(e.getRuleId())).findFirst().orElse(null);
            if (calu == null || StringUtil.isEmpty(calu.getMustCondition())) {
                //当计算公式为空时，直接返回空列表
                return returnDTOList;
            }
            String expression = calu.getMustCondition();
            Map<String, String> expressionMap = stringToMap(expression);
            //判断是否满足条件的规则
            List<PtyRule> ruleList = rules.stream().filter(e -> !paBizRule.getPtyRuleId().equals(e.getRuleId())).collect(Collectors.toList());
            //获取公式中全部的standCode
            Set<String> standCodeSet = getStandCodeList(paBizRule.getConditionJson());
            for (CalculationRuleDTO calculationRuleDTO : calculationRuleDTOList) {
                if (calculationRuleDTO.getMealAllowanceDTO() == null) {
                    continue;
                }
                Map<String, Object> standMap = paramEncapsuleContext.encapsulateParameters(calculationRuleDTO, standCodeSet);
                Map<String, Object> beanMap = new HashMap<>();
                try {
                    beanMap = convertToMap(calculationRuleDTO);
                } catch (Exception e) {
                    log.error("calculateSubsidy:实体转换map时，发生异常{}", LogUtil.stackTraceInfo(e));
                    return returnDTOList;
                }
                //封装附件规则中附加条件系数
                for (PtyRule ptyRule : ruleList) {
                    PcxBasExpBizTypeQO bizTypeQO = new PcxBasExpBizTypeQO();
                    MealAllowanceDTO mealAllowanceDTO = calculationRuleDTO.getMealAllowanceDTO();
                    String dormDays = mealAllowanceDTO.getDormDays();
                    //当食宿类型为有伙食有宿舍时，直接取值，住宿舍天数为空或为0时，食宿类型为2，否则则是没有住宿舍
                    String stayType = PcxConstant.IS_BASIC.equals(mealAllowanceDTO.getStayType()) ? mealAllowanceDTO.getStayType() : (StringUtil.isEmpty(dormDays) || PcxConstant.UN_BASIC.equals(dormDays)) ? "2" : mealAllowanceDTO.getStayType();
                    bizTypeQO.setStayType(stayType);
                    BeanUtils.copyProperties(mealAllowanceDTO, bizTypeQO);
                    //获取附件规则中附加条件系数，前边加a是防止公式计算出错
                    beanMap.put("a" + ptyRule.getRuleId(), pcxRuleService.fireRule(ptyRule, bizTypeQO) ? 1 : 0);
                }
                if (CollectionUtil.isNotEmpty(standMap)) {
                    beanMap.putAll(standMap);
                }
                log.info("calculateSubsidy:转换后的map值：{}", beanMap);
                try {
                    CalculationRuleReturnDTO calculationRuleReturnDTO = new CalculationRuleReturnDTO();
                    calculationRuleReturnDTO.setSalesCalculationResult(expression);

                    disposeStandAmt(beanMap, calculationRuleReturnDTO);

                    if (StringUtil.isNotEmpty(expressionMap.get(PcxConstant.ALL_SUBSIDY))) {
                        calculationRuleReturnDTO.setCalculationResult(AviatorEvaluator.execute(expressionMap.get(PcxConstant.ALL_SUBSIDY), beanMap));
                    }
                    if (StringUtil.isNotEmpty(expressionMap.get(PcxConstant.BASE_SUBSIDY))) {
                        calculationRuleReturnDTO.setBaseSubsidy(AviatorEvaluator.execute(expressionMap.get(PcxConstant.BASE_SUBSIDY), beanMap));
                    }
                    if (StringUtil.isNotEmpty(expressionMap.get(PcxConstant.DORM_SUBSIDY))) {
                        calculationRuleReturnDTO.setDormSubsidy(AviatorEvaluator.execute(expressionMap.get(PcxConstant.DORM_SUBSIDY), beanMap));
                    }
                    if (StringUtil.isNotEmpty(expressionMap.get(PcxConstant.HOLIDAY_WORK_SUBSIDY))) {
                        calculationRuleReturnDTO.setHolidayWorkSubsidy(AviatorEvaluator.execute(expressionMap.get(PcxConstant.HOLIDAY_WORK_SUBSIDY), beanMap));
                    }
                    if (StringUtil.isNotEmpty(expressionMap.get(PcxConstant.PM_SUBSIDY))) {
                        calculationRuleReturnDTO.setPmSubsidy(AviatorEvaluator.execute(expressionMap.get(PcxConstant.PM_SUBSIDY), beanMap));
                    }
                    calculationRuleReturnDTO.setKey(calculationRuleDTO.getKey());
                    calculationRuleReturnDTO.setStandResultList(calculationRuleDTO.getStandResultList());
                    returnDTOList.add(calculationRuleReturnDTO);
                } catch (Exception e) {
                    log.error("calculateSubsidy:计算公式发生异常{}", LogUtil.stackTraceInfo(e));
                }
            }
        }
        return returnDTOList;
    }

    private void disposeStandAmt(Map<String, Object> beanMap, CalculationRuleReturnDTO calculationRuleReturnDTO) {
        try {
            Object baseAmt = beanMap.get(PcxConstant.BASE_SUBSIDY);
            if (Objects.nonNull(baseAmt)){
                calculationRuleReturnDTO.setBaseSubsidyAmt(new BigDecimal(baseAmt.toString()));
            }
        }catch (Exception e){
            log.error("calculateSubsidy:处理baseSubsidy发生异常", e);
        }
        try {
            Object dormAmt = beanMap.get(PcxConstant.DORM_SUBSIDY);
            if (Objects.nonNull(dormAmt)){
                calculationRuleReturnDTO.setDormSubsidyAmt(new BigDecimal(dormAmt.toString()));
            }
        }catch (Exception e){
            log.error("calculateSubsidy:处理dormAmt发生异常", e);
        }
        try {
            Object holidayAmt = beanMap.get(PcxConstant.HOLIDAY_WORK_SUBSIDY);
            if (Objects.nonNull(holidayAmt)){
                calculationRuleReturnDTO.setHolidayWorkSubsidyAmt(new BigDecimal(holidayAmt.toString()));
            }
        }catch (Exception e){
            log.error("calculateSubsidy:处理holidayAmt发生异常", e);
        }
        try {
            Object pmAmt = beanMap.get(PcxConstant.PM_SUBSIDY);
            if (Objects.nonNull(pmAmt)){
                calculationRuleReturnDTO.setPmSubsidyAmt(new BigDecimal(pmAmt.toString()));
            }
        }catch (Exception e){
            log.error("calculateSubsidy:处理baseSubsidy发生异常", e);
        }

    }

    /**
     * 简单格式字符串转回 Map
     */
    private Map<String, String> stringToMap(String str) {
        Map<String, String> map = new HashMap<>();
        String[] entries = str.split(", ");
        for (String entry : entries) {
            String[] keyValue = entry.split("=");
            if (keyValue.length == 2) {
                map.put(keyValue[0], keyValue[1]);
            }
        }
        return map;
    }

    /**
     * 获取公式中全部支出标准
     * @param conditionJson
     * @return
     */
    private Set<String> getStandCodeList(String conditionJson) {
        return JSON.parseArray(conditionJson).stream()
                .map(obj -> (JSONObject) obj)
                .map(jsonObject -> jsonObject.getString("standCode"))
                .collect(Collectors.toSet());
    }

    /**
     * 获取pa_biz_rule数据，获取json数据，便于后续公式计算以及标准获取
     * @param calculationRuleDTO
     */
    private PaBizRule getPaBizRule(CalculationRuleDTO calculationRuleDTO) {
        PaBizRule calculationRule = PaBizRule.builder()
                .agyCode(calculationRuleDTO.getAgyCode())
                .fiscal(calculationRuleDTO.getFiscal())
                .mofDivCode(calculationRuleDTO.getMofDivCode())
                .bizCode(calculationRuleDTO.getExpenseType())
                .bizType(BizConditionEnum.CALCULATION_RULE.getCode())
                .ruleCode(PcxConstant.CALCULATION_RULE).build();
        return paBizRuleService.selectOne(calculationRule);
    }

    /**
     * 获取计算规则数据
     * @param paBizRuleVO
     * @return
     */
    @Override
    public CheckMsg<PaBizRule> getData(PaBizRuleVO paBizRuleVO) {
        CheckMsg checkMsg = checkParam(paBizRuleVO);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }
        return CheckMsg.success(paBizRuleService.selectOne(PaBizRule.builder()
                .bizCode(paBizRuleVO.getBizCode())
                .agyCode(paBizRuleVO.getAgyCode())
                .mofDivCode(paBizRuleVO.getMofDivCode())
                .fiscal(paBizRuleVO.getFiscal())
                .ruleCode(paBizRuleVO.getRuleCode())
                .build()));
    }

    /**
     * 保存计算规则,先删后增，先删除pa_biz_rule、pty_rule表，再将两表数据新增进去
     * @param paBizRuleVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(PaBizRuleVO paBizRuleVO) {
        //删除两表数据
        deleteRule(paBizRuleVO);

        //新增两表数据
        insertRule(paBizRuleVO);

    }

    @Override
    public List<ValsetDTO> getExtraSubsidyValset(String fiscal, String agyCode, String mofDivCode, String expenseTypeCode) {
        List<ValsetDTO> result = new ArrayList<>();
        CalculationRuleDTO calculationRuleDTO = new CalculationRuleDTO();
        calculationRuleDTO.setFiscal(fiscal);
        calculationRuleDTO.setAgyCode(agyCode);
        calculationRuleDTO.setMofDivCode(mofDivCode);
        calculationRuleDTO.setExpenseType(expenseTypeCode);
        try {
            PaBizRule paBizRule = getPaBizRule(calculationRuleDTO);
            if (!Objects.isNull(paBizRule)) {
                String conditionJson = paBizRule.getConditionJson();
                JSONArray jsonArray = JSON.parseArray(conditionJson);
                Set<String> set = new HashSet<>();
                for (Object o : jsonArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    String subsidyTypeCode = jsonObject.getString("standCode");
                    String subsidyTypeName = jsonObject.getString("standName");
                    if (Objects.equals(PcxConstant.HOLIDAY_WORK_SUBSIDY, subsidyTypeCode) ||
                            Objects.equals(PcxConstant.PM_SUBSIDY, subsidyTypeCode)) {
                        if (!set.contains(subsidyTypeCode)) {
                            ValsetDTO valsetDTO = new ValsetDTO();
                            valsetDTO.setValcode(subsidyTypeCode);
                            valsetDTO.setValName(subsidyTypeName);
                            result.add(valsetDTO);
                            set.add(subsidyTypeCode);
                        }

                    }
                }
            }

        }catch (Exception e){
            log.error("getValSet", e);
        }

        result.sort(Comparator.comparing(ValsetDTO::getValcode));
        return result;
    }

    /**
     * 新增pa_biz_rule、pty_rule两表数据
     * @param paBizRuleVO
     */
    private void insertRule(PaBizRuleVO paBizRuleVO) {
        PaBizRule data = new PaBizRule();
        String calculationRuleId = StringUtil.getUUID();
        BeanUtils.copyProperties(paBizRuleVO, data);
        data.setRuleCode(PcxConstant.CALCULATION_RULE);
        data.setRuleName(BizConditionEnum.CALCULATION_RULE.getDesc());
        data.setBizType(BizConditionEnum.CALCULATION_RULE.getCode());
        data.setPtyRuleId(calculationRuleId);
        data.setCreator(PtyContext.getUsername());
        data.setCreatedTime(DateUtil.nowDate());
        paBizRuleService.insertSelective(data);
        String conJson = data.getConditionJson();
        if (StringUtil.isNotEmpty(conJson)) {
            JSONArray array = JSON.parseArray(conJson);
            List<PtyRule> rules = new ArrayList<>();
            //封装附加规则内容
            setCondition(rules, array);
            //封装公式
            setCalculation(rules, array, data);
            ruleService.batchInsertRule(rules);
        }
    }

    /**
     * 封装计算公式ptyRule实体
     *
     */
    private void setCalculation(List<PtyRule> rules, JSONArray array, PaBizRule data) {
        //组装计算公式字符串，将基础规则放到前面，附加规则放到后面
        String calculationString = getCalculationString(array);
        if (StringUtil.isNotEmpty(calculationString)) {
            PtyRule ptyRule = new PtyRule();
            ptyRule.setRuleId(data.getPtyRuleId());
            ptyRule.setRuleName(DateUtil.getYear(DateUtil.nowDate()) + IDGenerator.id());
            ptyRule.setRuleDesc("计算规则条件");
            ptyRule.setRuleType(RuleType.LEGAL.name());
            ptyRule.setRuleAct(PtyRuleConstants.PtyRuleActionEnum.ACCESS.name());
            ptyRule.setRuleModule(PcxConstant.PCX_RULE_MODULE);
            ptyRule.setWhenCondition("true");
            ptyRule.setMustCondition(calculationString);
            ptyRule.setEnabled(PcxConstant.ENABLE);
            ptyRule.setRuleCreator(PtyContext.getUsername());
            ptyRule.setRuleCreatedDate(DateUtil.getCurDate());
            ptyRule.setRuleModifyDate(DateUtil.getCurDate());
            ptyRule.setRuleReference("");
            ptyRule.setRuleCorrect(1);
            ptyRule.setRuleGroup(PcxConstant.CALCULATION_RULE);
            ptyRule.setRuleIdentify(0);
            ptyRule.setRuleErrorTemplate("");
            rules.add(ptyRule);
        }

    }

    /**
     * 组装计算公式字符串，将基础规则放到前面，附加规则放到后面
     * @param array
     * @return
     */
    private String getCalculationString(JSONArray array) {
        StringBuilder calculation = new StringBuilder();
        Map<String, StringBuilder> subsidyBuilders = new HashMap<String, StringBuilder>() {{
            put(PcxConstant.BASE_SUBSIDY, new StringBuilder());
            put(PcxConstant.DORM_SUBSIDY, new StringBuilder());
            put(PcxConstant.HOLIDAY_WORK_SUBSIDY, new StringBuilder());
            put(PcxConstant.PM_SUBSIDY, new StringBuilder());
        }};

        for (Object o : array) {
            if (!(o instanceof JSONObject)) {
                continue;
            }

            JSONObject jsonObject = (JSONObject) o;
            String basicType = jsonObject.getString("basicType");
            String basicOperator = jsonObject.getString("basicOperator");
            String code = jsonObject.getString("code");
            String operator = jsonObject.getString("operator");
            String standCode = jsonObject.getString("standCode");
            String id = jsonObject.getString("id");
            String adjust = jsonObject.getString("adjust");
            String daysOperator = jsonObject.getString("daysOperator");

            // 构建公式
            String formula = PcxConstant.UN_BASIC.equals(basicType)
                    ? buildUnBasicFormula(code, operator, standCode, daysOperator, adjust)
                    : buildIsBasicFormula(basicOperator, id, code, operator, standCode, daysOperator, adjust);

            // 处理补贴类型
            String subsidyKey = PcxConstant.BASE_SUBSIDY.equals(standCode) ? PcxConstant.BASE_SUBSIDY
                    : PcxConstant.DORM_SUBSIDY.equals(standCode) ? PcxConstant.DORM_SUBSIDY
                    : PcxConstant.HOLIDAY_WORK_SUBSIDY.equals(standCode) ? PcxConstant.HOLIDAY_WORK_SUBSIDY
                    : PcxConstant.PM_SUBSIDY.equals(standCode) ? PcxConstant.PM_SUBSIDY : null;
            if (StringUtil.isNotEmpty(subsidyKey)) {
                StringBuilder currentBuilder = subsidyBuilders.get(subsidyKey);
                if (currentBuilder.length() == 0) {
                    if (PcxConstant.BASE_SUBSIDY.equals(subsidyKey)) {
                        currentBuilder.append(buildUnBasicFormula(code, operator, standCode, daysOperator, adjust));
                    } else {
                        currentBuilder.append(buildIsBasicFormula(StringUtil.EMPTY, id, code, operator, standCode, daysOperator, adjust));
                    }
                } else {
                    currentBuilder.append(buildIsBasicFormula(basicOperator, id, code, operator, standCode, daysOperator, adjust));
                }
            }
            // 处理主计算公式
            if (PcxConstant.UN_BASIC.equals(basicType)) {
                calculation.insert(0, formula);
            } else {
                calculation.append(formula);
            }
        }
        subsidyBuilders.put(PcxConstant.ALL_SUBSIDY, calculation);

        return mapToString(subsidyBuilders);
    }

    /**
     * 将map转成字符串
     * @param map
     * @return
     */
    private String mapToString(Map<?, ?> map) {
        return map.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining(", "));
    }

    /**
     * 构建公式
     * @param jsonObject
     * @return
     */
    private String buildFormula(JSONObject jsonObject) {
        String formula;
        String basicOperator = jsonObject.getString("basicOperator");
        String code = jsonObject.getString("code");
        String operator = jsonObject.getString("operator");
        String standCode = jsonObject.getString("standCode").replaceAll("-", "");
        String id = jsonObject.getString("id");
        String adjust = jsonObject.getString("adjust");
        String daysOperator = jsonObject.getString("daysOperator");

        if (PcxConstant.UN_BASIC.equals(jsonObject.getString("basicType"))) {
            formula = buildUnBasicFormula(code, operator, standCode, daysOperator, adjust);
        } else {
            formula = buildIsBasicFormula(basicOperator, id, code, operator, standCode, daysOperator, adjust);
        }
        return formula;
    }

    /**
     * 封装基础规则公式
     * @param code
     * @param operator
     * @param standCode
     * @param daysOperator
     * @param adjust
     * @return
     */
    private String buildUnBasicFormula(String code, String operator, String standCode, String daysOperator, String adjust) {
        String unBasic = "(";
        if (StringUtil.isEmpty(daysOperator)) {
            if (StringUtil.isEmpty(adjust)) {
                unBasic += "decimal(" + code + ")" + operator + "decimal(" + standCode + "))";
            } else {
                unBasic += "decimal(" + adjust + ")" + operator + "decimal(" + standCode + "))";
            }
        } else {
            unBasic += "(decimal(" + code + ")" + daysOperator + "decimal(" + adjust + "))" + operator + "decimal(" + standCode + "))";
        }
        return unBasic;
    }

    /**
     * 封装附加规则公式
     * @param basicOperator
     * @param id
     * @param code
     * @param operator
     * @param standCode
     * @param daysOperator
     * @param adjust
     * @return
     */
    private String buildIsBasicFormula(String basicOperator, String id, String code, String operator, String standCode, String daysOperator, String adjust) {
        String isBasic = basicOperator + "(decimal(a" + id + ")*(";
        if (StringUtil.isEmpty(daysOperator)) {
            if (StringUtil.isEmpty(adjust)) {
                isBasic += "decimal(" + code + ")" + operator + "decimal(" + standCode + ")))";
            } else {
                isBasic += "decimal(" + adjust + ")" + operator + "decimal(" + standCode + ")))";
            }
        } else {
            isBasic += "(decimal(" + code + ")" + daysOperator + "decimal(" + adjust + "))" + operator + "decimal(" + standCode + ")))";
        }
        return isBasic;
    }

    /**
     * 封装附加规则内容
     * @param rules
     * @param array
     */
    private void setCondition(List<PtyRule> rules, JSONArray array) {
        array.stream().filter(o -> o instanceof JSONObject && PcxConstant.IS_BASIC.equals(((JSONObject) o).get("basicType"))).forEach(e -> {
            PtyRule rule = new PtyRule();
            JSONObject jsonObject = (JSONObject) e;
            rule.setRuleId(jsonObject.getString("id"));
            rule.setRuleName(DateUtil.getYear(DateUtil.nowDate()) + IDGenerator.id());
            rule.setRuleDesc("计算规则条件");
            rule.setRuleType(RuleType.LEGAL.name());
            rule.setRuleAct(PtyRuleConstants.PtyRuleActionEnum.ACCESS.name());
            rule.setRuleModule(PcxConstant.PCX_RULE_MODULE);
            String json = StringUtil.nullToEmpty(jsonObject.get("conditionJson"));
            rule.setWhenCondition(MadFieldRelEnum.buildWhenCondition(BizConditionEnum.CALCULATION_RULE.getCode(), json));
            rule.setMustCondition(buildCalcuMustCondition(json));
            rule.setEnabled(PcxConstant.ENABLE);
            rule.setRuleCreator(PtyContext.getUsername());
            rule.setRuleCreatedDate(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
            rule.setRuleModifyDate(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
            rule.setRuleReference("");
            rule.setRuleCorrect(1);
            rule.setRuleGroup(PcxConstant.CALCULATION_RULE);
            rule.setRuleIdentify(0);
            rule.setRuleErrorTemplate("");
            rules.add(rule);
        });
    }

    /**
     * 构建计算规则公式
     *
     * @return
     */
    public String buildCalcuMustCondition(String json) {
        JSONArray array = JSON.parseArray(json);
        List<String> formulaList = new ArrayList<>();
        array.forEach(i -> {
            String key = ((JSONObject) i).getString("key");
            JSONArray val = JSON.parseArray(StringUtil.nullToEmpty(((JSONObject) i).get("val")));
            if (val != null) {
                if (val.size() > 1) {
                    StringBuilder valStr = new StringBuilder("(");
                    for (int k = 0; k < val.size(); k++) {
                        valStr.append("pcx.").append(key).append("=='").append(val.get(k)).append("'");
                        if (k != val.size() - 1) {
                            valStr.append(" or ");
                        }
                    }
                    valStr.append(")");
                    formulaList.add(valStr.toString());
                } else {
                    formulaList.add("pcx." + key + "==" + "'" + val.get(0) + "'");
                }
            }

        });
        return String.join(" and ", formulaList);
    }

    /**
     * 删除pa_biz_rule、pty_rule两表数据
     * @param paBizRuleVO
     */
    private void deleteRule(PaBizRuleVO paBizRuleVO) {
        String id = paBizRuleVO.getId();
        //先查询pa_biz_rule表数据
        PaBizRule paBizRule = paBizRuleService.selectById(id);
        if (paBizRule == null) {
            return;
        }
        paBizRuleService.deleteById(id);
        String conditionJson = paBizRule.getConditionJson();
        List<String> ptyRuleIdList = new ArrayList<>();
        ptyRuleIdList.add(paBizRule.getPtyRuleId());
        if (StringUtil.isNotEmpty(conditionJson)) {
            JSONArray array = JSON.parseArray(conditionJson);
            ptyRuleIdList.addAll(array.stream().filter(o -> o instanceof JSONObject).map(e -> ((JSONObject) e).getString("id")).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(ptyRuleIdList)) {
            ruleService.batchDeleteRuleById(ptyRuleIdList);
        }
    }

    /**
     * 前端参数校验
     * @param paBizRuleVO
     * @return
     */
    private CheckMsg checkParam(PaBizRuleVO paBizRuleVO) {
        if (paBizRuleVO == null
                || StringUtil.isEmpty(paBizRuleVO.getAgyCode())
                || StringUtil.isEmpty(paBizRuleVO.getMofDivCode())
                || StringUtil.isEmpty(paBizRuleVO.getFiscal())
                || StringUtil.isEmpty(paBizRuleVO.getBizCode())
                || StringUtil.isEmpty(paBizRuleVO.getRuleCode())) {
            return CheckMsg.fail("传参错误，请检查参数");
        }
        return CheckMsg.success();
    }

    /**
     * 查询计算公式规则
     * @param paBizRule
     * @return
     */
    private List<PtyRule> selectCalculationRule(PaBizRule paBizRule) {
        String conditionJson = paBizRule.getConditionJson();
        List<String> ruleIds = new ArrayList<>();
        JSON.parseArray(conditionJson).stream()
                .filter(o -> o instanceof JSONObject && PcxConstant.IS_BASIC.equals(((JSONObject) o).get("basicType")))
                .forEach(e -> {
                    ruleIds.add(((JSONObject) e).getString("id"));
                });
        ruleIds.add(paBizRule.getPtyRuleId());
        PtyRule cond = new PtyRule();
        cond.setRuleIds(ruleIds);
        return ruleService.selectRule(cond);
    }

    /**
     * 将对象转换为 Map，其中引用类型的字段会递归解析，并使用前缀表示嵌套关系。
     *
     * @param obj 要转换的对象
     * @return 包含对象字段和值的 Map
     * @throws IllegalAccessException 如果无法访问对象的字段
     */
    public static Map<String, Object> convertToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> resultMap = new HashMap<>();
        if (obj == null) {
            return resultMap;
        }
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            Object value = field.get(obj);
            if (Objects.isNull(value)) {
                // 忽略空值字段
                continue;
            }
            if (value.getClass().getName().startsWith("com.pty")) {
                // 递归处理引用类型的字段，并添加前缀
                resultMap.putAll(convertToMap(value));
            } else {
                // 直接将字段名和值放入 Map
                resultMap.put(field.getName(), value);
            }
        }
        return resultMap;
    }

    /**
     * 为 Map 中的每个键添加指定的前缀。
     *
     * @param map    要处理的 Map
     * @param prefix 要添加的前缀
     * @return 添加前缀后的 Map
     */
    private static Map<String, Object> prefixKeys(Map<String, Object> map, String prefix) {
        Map<String, Object> prefixedMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            prefixedMap.put(prefix + entry.getKey(), entry.getValue());
        }
        return prefixedMap;
    }

}
