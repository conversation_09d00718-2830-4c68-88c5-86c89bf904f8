package com.pty.pcx.service.impl.ecs.processor;

import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.service.impl.ecs.ProcessChain;
import com.pty.pcx.service.impl.ecs.Processor;
import com.pty.pcx.service.impl.ecs.handler.Handler;
import com.pty.pcx.service.impl.ecs.handler.HandlerFactory;

/**
 * 费用标准加工器
 */
public class StandProcessor implements Processor {

    @Override
    public void execute(InvoiceDtoWrapper ecsBaseDto, ProcessChain chain) {
        Handler handler = HandlerFactory.getStandHandler(ecsBaseDto);
        if (handler != null) {
            handler.handle(ecsBaseDto, chain.getContext());
        }
        chain.process(ecsBaseDto);
    }
}
