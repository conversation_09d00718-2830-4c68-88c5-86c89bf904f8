package com.pty.pcx.service.impl.printtemplate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.pty.pcx.api.printtemplate.IPcxPrintTemplateService;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.printtemplate.PcxPrintTemplateDao;
import com.pty.pcx.entity.printtemplate.PcxPrintTemplate;
import com.pty.pcx.qo.printtemplate.PcxPrintTemplateQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ValidUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: PcxPrintTemplateService
 * @Description: 打印模版
 * @Date: 2024/11/13  上午10:27
 * @Author: wangbao
 **/
@Service
@Indexed
@Slf4j
public class PcxPrintTemplateServiceImpl implements IPcxPrintTemplateService {

    @Autowired
    private PcxPrintTemplateDao pcxPrintTemplateDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    /**
     * 批量执行新增或修改：todo 补充ureport查询接口，需要完成文件系统查询、本地文件路径查询、数据库查询 三种实现方式
     *
     * @return
     */
    @Override
    public CheckMsg saveOrUpdate(List<PcxPrintTemplateQO> pcxPrintTemplateQOS) {
        CheckMsg paramCheckMsg = checkSaveParam(pcxPrintTemplateQOS);
        if(!paramCheckMsg.isSuccess()){
            return paramCheckMsg;
        }
        batchServiceUtil.batchProcess(pcxPrintTemplateQOS, PcxPrintTemplateDao.class, PcxPrintTemplateServiceImpl::singleSaveOrUpdate);
        return CheckMsg.success();
    }

    /**
     * 单条新增或修改：根据id查询是否已经存在，存在则修改，不存在则新增
     */
    private static void singleSaveOrUpdate(PcxPrintTemplateDao pcxPrintTemplateDao, PcxPrintTemplateQO pcxPrintTemplateQO) {
        PcxPrintTemplate pcxPrintTemplate = pcxPrintTemplateDao.selectById(pcxPrintTemplateQO);
        if (ObjectUtil.isNotEmpty(pcxPrintTemplate)) {
            pcxPrintTemplateDao.updateById(pcxPrintTemplateQO);
        } else {
            pcxPrintTemplateDao.insertSelective(pcxPrintTemplateQO);
        }
    }

    /**
     * 批量删除打印模版
     */
    @Override
    public CheckMsg deleteById(PcxPrintTemplateQO pcxPrintTemplateQO) {
        try {
            CheckMsg paramCheckMsg = checkDeleteParam(pcxPrintTemplateQO);
            if(!paramCheckMsg.isSuccess()){
                return paramCheckMsg;
            }
            batchServiceUtil.batchProcess(pcxPrintTemplateQO.getIds(), PcxPrintTemplateDao.class, PcxPrintTemplateServiceImpl::singleDeleteById);
            return CheckMsg.success();
        } catch (Exception e) {
            return CheckMsg.fail(e.getMessage());
        }
    }

    /**
     * 参数校验：批量删除
     */
    private CheckMsg checkDeleteParam(PcxPrintTemplateQO pcxPrintTemplateQO) {
        try {
            validBaseInfo(pcxPrintTemplateQO);
            ValidUtil.checkContainsEmptyStr(pcxPrintTemplateQO.getIds(), "参数异常，打印模版id列表不能含有空元素");
            return CheckMsg.success();
        } catch (Exception e) {
            return CheckMsg.fail(e.getMessage());
        }
    }

    /**
     * 单条删除
     */
    private static void singleDeleteById(PcxPrintTemplateDao pcxPrintTemplateDao, String id) {
        if(StringUtil.isEmpty(id)){
            return;
        }
        PcxPrintTemplateQO pcxPrintTemplateQO = new PcxPrintTemplateQO();
        pcxPrintTemplateQO.setId(id);
        pcxPrintTemplateDao.deleteById(pcxPrintTemplateQO);
    }

    /**
     * 查询打印模版
     */
    @Override
    public CheckMsg selectByFuncCode(PcxPrintTemplateQO pcxPrintTemplateQO) {
        CheckMsg paramCheckMsg = checkSelectByFuncCodeParam(pcxPrintTemplateQO);
        if(!paramCheckMsg.isSuccess()){
            return paramCheckMsg;
        }
        List<PcxPrintTemplate> pcxPrintTemplateList = new ArrayList<>();
        if(StringUtil.isEmpty(pcxPrintTemplateQO.getBilltypeCode())){
            pcxPrintTemplateList = pcxPrintTemplateDao.select(pcxPrintTemplateQO);
        }else{
            // 后端过滤查询结果
            pcxPrintTemplateList = filterResultByBillType(pcxPrintTemplateQO);
        }
        // 后端补充字段信息
        fillBilltypeName(pcxPrintTemplateList);
        return CheckMsg.success(pcxPrintTemplateList);
    }

    @Override
    public List<PcxPrintTemplate> selectByQO(PcxPrintTemplateQO qo) {
        if(ObjectUtil.isEmpty(qo)){
            return new ArrayList<>();
        }
        return pcxPrintTemplateDao.selectByQO(qo);
    }

    /**
     * 参数校验：查询打印模版
     */
    private CheckMsg checkSelectByFuncCodeParam(PcxPrintTemplateQO pcxPrintTemplateQO) {
        try {
            validBaseInfo(pcxPrintTemplateQO);
            ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getFuncCode(), "参数异常，功能编码 不能为空");
            ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getFuncType(), "参数异常，功能类型 不能为空");
            return CheckMsg.success();
        } catch (Exception e) {
            return CheckMsg.fail(e.getMessage());
        }
    }

    /**
     * 后端过滤单据种类
     */
    private List<PcxPrintTemplate> filterResultByBillType(PcxPrintTemplateQO pcxPrintTemplateQO) {
        List<PcxPrintTemplate> pcxPrintTemplateList;
        String billtypeCode = pcxPrintTemplateQO.getBilltypeCode();
        pcxPrintTemplateQO.setBilltypeCode(null);
        pcxPrintTemplateList = pcxPrintTemplateDao.select(pcxPrintTemplateQO);
        List<String> billtypeList = Arrays.asList(billtypeCode.split(","));
        pcxPrintTemplateList = pcxPrintTemplateList.stream().filter(e -> billtypeList.contains(e.getBilltypeCode())).collect(Collectors.toList());
        return pcxPrintTemplateList;
    }

    /**
     * 补充单据类型名称
     */
    private static void fillBilltypeName(List<PcxPrintTemplate> pcxPrintTemplateList) {
        if(CollectionUtil.isEmpty(pcxPrintTemplateList)){
            return;
        }
        for (PcxPrintTemplate pcxPrintTemplate : pcxPrintTemplateList) {
            if(ObjectUtil.isEmpty(pcxPrintTemplate) || StringUtil.isEmpty(pcxPrintTemplate.getBilltypeCode())){
                continue;
            }
            String[] billtypeArray = pcxPrintTemplate.getBilltypeCode().split(",");
            String billtypeName = Arrays.stream(billtypeArray).filter(StringUtil::isNotEmpty)
                    .map(BillFuncCodeEnum::getNameByFuncCode).collect(Collectors.joining(","));
            pcxPrintTemplate.setBilltypeName(billtypeName);
        }
    }

    /**
     * 新增或修改：检查参数是否为空 funcCode、funcType、billtypeCode、id
     */
    private CheckMsg checkSaveParam(List<PcxPrintTemplateQO> pcxPrintTemplateQOS) {
        try {
            ValidUtil.checkEmptyCollection(pcxPrintTemplateQOS, "参数异常，pcxPrintTemplateQOS 不能为空");
            for(PcxPrintTemplateQO pcxPrintTemplateQO : pcxPrintTemplateQOS){
                validBaseInfo(pcxPrintTemplateQO);
                ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getFuncCode(), "参数异常，功能编码 不能为空");
                ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getFuncType(), "参数异常，功能类型 不能为空");
                ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getBilltypeCode(), "参数异常，单据类型不能为空 不能为空");
                if(ObjectUtil.isEmpty(pcxPrintTemplateQO.getId())){
                    pcxPrintTemplateQO.setId(StringUtil.getUUID());
                }
            }
            return CheckMsg.success();
        } catch (Exception e) {
            return CheckMsg.fail(e.getMessage());
        }
    }

    /**
     * 参数校验：基本信息的入参 fiscal、agyCode、mofDivCode
     */
    private static void validBaseInfo(PcxPrintTemplateQO pcxPrintTemplateQO) {
        ValidUtil.checkEmptyObject(pcxPrintTemplateQO, "参数异常，pcxPrintTemplateQO 不能为空");
        ValidUtil.checkEmptyObject(pcxPrintTemplateQO.getFiscal(), "参数异常，年度不能为空");
        ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getAgyCode(), "参数异常，单位编码不能为空");
        ValidUtil.checkEmptyStr(pcxPrintTemplateQO.getMofDivCode(), "参数异常，区划编码不能为空");
    }

}
