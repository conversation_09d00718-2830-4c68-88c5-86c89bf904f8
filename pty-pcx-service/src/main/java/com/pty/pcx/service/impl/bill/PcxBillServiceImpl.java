package com.pty.pcx.service.impl.bill;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.pty.balance.vo.BillDTO;
import com.pty.balance.vo.CtrlDTO;
import com.pty.balance.vo.QBalVO;
import com.pty.ecs.common.EcsServiceMsg;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.mad.common.MadConstants;
import com.pty.mad.common.MadUtils;
import com.pty.mad.entity.MadWorkLocations;
import com.pty.pcx.api.bas.*;
import com.pty.pcx.api.bill.PcxBillBalanceService;
import com.pty.pcx.api.bill.PcxBillExpAssService;
import com.pty.pcx.api.bill.PcxBillRelationService;
import com.pty.pcx.api.calculationrule.ICalculationRuleService;
import com.pty.pcx.api.contract.PcxBillContractRelService;
import com.pty.pcx.api.costcontrollevel.PcxCostControlLevelService;
import com.pty.pcx.api.datascope.DataScopeService;
import com.pty.pcx.api.financeapproval.PcxFinanceApprovalService;
import com.pty.pcx.api.mybatisplus.bill.IPcxBillPlusService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.setting.IPcxPaFieldSettingService;
import com.pty.pcx.api.stand.PcxStandKeyService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillPayDetailService;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.balance.IBalanceExternalService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxCityClassifyConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.exception.ForbidTipsException;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dao.bill.labour.PcxBillExpDetailLabourDao;
import com.pty.pcx.dao.labour.PcxLabourInfoDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.dto.ecs.ValsetDTO;
import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.dto.mad.MadEmployeeCardDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.dto.pa.PaOrgDTO;
import com.pty.pcx.dto.pa.PaUserDesignateDTO;
import com.pty.pcx.dto.wit.AuditDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxSettlementRule;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.labour.PcxBillExpDetailLabour;
import com.pty.pcx.entity.bill.labour.PcxLabourInfo;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.entity.costcontrollevel.PcxCostControlLevel;
import com.pty.pcx.entity.setting.PaOption;
import com.pty.pcx.entity.stand.qo.PcxStandQueryQO;
import com.pty.pcx.entity.stand.vo.PcxStandVO;
import com.pty.pcx.entity.stand.vo.PcxStandValueVO;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.mad.IMadEmployeeCardExternalService;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.mad.IMadWorklocationsExternalService;
import com.pty.pcx.pa.IPcxFileService;
import com.pty.pcx.pa.IPcxUserDesignateService;
import com.pty.pcx.pa.impl.PcxBillNoService;
import com.pty.pcx.pa.impl.PcxUserService;
import com.pty.pcx.qo.bas.*;
import com.pty.pcx.qo.bill.*;
import com.pty.pcx.qo.contract.ContractVO;
import com.pty.pcx.qo.costcontrollevel.PcxEmployeeWithCostLevelQO;
import com.pty.pcx.qo.datascope.DataScopeQO;
import com.pty.pcx.qo.ecs.BuildExpRelQO;
import com.pty.pcx.qo.financeapproval.PcxFinanceApprovalBillQO;
import com.pty.pcx.qo.setting.PaFieldSettingQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailQO;
import com.pty.pcx.qo.workflow2.DoneTaskParamQO;
import com.pty.pcx.qo.workflow2.PositionQO;
import com.pty.pcx.qo.workflow2.ProcessHistoryQO;
import com.pty.pcx.qo.workflow2.TodoTaskParamQO;
import com.pty.pcx.service.impl.bill.balance.PcxBalanceCtrlService;
import com.pty.pcx.service.impl.bill.handler.BillListService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.BillValidateService;
import com.pty.pcx.service.impl.bill.handler.CallBackService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseDetailService4Metting;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseDetailService4Training;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseDetailService4Travel;
import com.pty.pcx.service.impl.ecs.EcsExpCommonService;
import com.pty.pcx.service.impl.ecs.EcsExpOptService;
import com.pty.pcx.service.impl.ecs.EcsExpTransService;
import com.pty.pcx.service.impl.workflow2.ProcessService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.PcxBillCaseUtil;
import com.pty.pcx.util.trans.PageTransformer;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.balance.BalanceBackDetailVO;
import com.pty.pcx.vo.bas.BasItemVO;
import com.pty.pcx.vo.bas.CityPeakVO;
import com.pty.pcx.vo.bas.PcxSettlementRuleExpVO;
import com.pty.pcx.vo.bill.*;
import com.pty.pcx.vo.costcontrollevel.PcxEmployeeWithCostLevelVO;
import com.pty.pcx.vo.ecs.BillTripVO;
import com.pty.pcx.vo.ecs.EcsExpMatchVO;
import com.pty.pcx.vo.ecs.WitEcsVO;
import com.pty.pcx.vo.financeapproval.PcxFinanceApprovalBillVO;
import com.pty.pcx.vo.setting.PaFieldSettingVO;
import com.pty.pcx.vo.treasurypay.detail.PayDetailVO;
import com.pty.pcx.vo.workflow2.DoneTaskVO;
import com.pty.pcx.vo.workflow2.ProcessCompositeVO;
import com.pty.pcx.vo.workflow2.TodoTaskVO;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.*;
import com.pty.pub.entity.vo.ExcelExportTitleRowVO;
import com.pty.pub.entity.vo.ExcelExportVO;
import com.pty.rule.RuleAct;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import com.pty.workflow2.extend.pcx.PcxProcessDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.BusinessRuleEnum.BusinessOptionEnum.APPLY_ENABLE;
import static com.pty.pcx.common.constant.BusinessRuleEnum.BusinessOptionEnum.APPROVAL_ENABLE;
import static com.pty.pcx.util.ExpenseBeanUtil.getDetailBean;
import static com.pty.pub.entity.vo.ExcelExportTitleRowVO.ALIGN_CENTER;

/**
 * 单据主表(PcxBill)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:02
 */
@Slf4j
@Service("pcxBillService")
@Indexed
public class PcxBillServiceImpl extends ServiceImpl<PcxBillDao, PcxBill> implements IPcxBillPlusService {

    @Resource
    private ICalculationRuleService calculationRuleService;

    @Autowired
    private IPcxPaFieldSettingService pcxPaFieldSettingService;
    @Autowired
    private BillExpenseCommonService billExpenseCommonService;
    @Autowired
    private DataScopeService dataScopeService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private BillValidateService billValidateService;
    @Autowired
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Autowired
    private IMadEmployeeCardExternalService madEmployeeCardExternalService;
    @Autowired
    private BillListService billListService;
    @Autowired
    private IProcessService processService;
    @Autowired
    private IEcsBillExternalService ecsBillExternalService;
    @Autowired
    private IPcxBasExpTypeService pcxBasExpTypeService;
    @Autowired
    private PcxBalanceCtrlService pcxBalanceCtrlService;
    @Autowired
    private PcxSettlementRuleService pcxSettlementRuleService;
    @Autowired
    private PcxSettlementRuleExpService pcxSettlementRuleExpService;
    @Resource
    private PcxBillExpStandResultDao pcxBillExpStandResultDao;
    @Autowired
    private EcsExpOptService ecsExpOptService;
    @Autowired
    private PcxBillRelationService pcxBillRelationService;
    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;
    @Autowired
    private IWitAuditRuleService witAuditRuleService;
    @Autowired
    private PcxBillCaseUtil pcxBillCaseUtil;
    @Autowired
    private IPcxFileService pcxFileService;
    @Autowired
    private PcxFinanceApprovalService pcxFinanceApprovalService;
    @Autowired
    private PcxUserService pcxUserService;

    @Autowired
    private PcxBillDao pcxBillDao;

    @Autowired
    private WitDataBuilder witDataBuilder;

    @Autowired
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;

    @Autowired
    private IPcxBasItemService pcxBasItemService;

    @Autowired
    private IBalanceExternalService balanceExternalService;

    @Autowired
    private PcxBillRelationDao pcxBillRelationDao;

    @Resource
    private PcxBillNoService pcxBillNoService;

    @Autowired
    private PcxStandKeyService pcxStandKeyService;

    @Autowired
    private PcxCostControlLevelService pcxCostControlLevelService;

    @Autowired
    private PcxBasCityClassifyService pcxBasCityClassifyService;

    @Autowired
    private CallBackService callBackService;
    @Autowired
    private BatchServiceUtil batchServiceUtil;
    @Autowired
    private PcxBillBalanceDao pcxBillBalanceDao;

    @Autowired
    private IBusinessRuleOptionService bussinessRuleOptionService;

    @Autowired
    private Map<String, IBalanceExternalService> balanceExternalServiceMap;

    @Autowired
    private IPcxUserDesignateService pcxUserDesignateService;

    @Autowired
    private PcxBillBalanceService pcxBillBalanceService;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;
    @Resource
    private PcxBillContractRelService pcxBillContractRelService;
    @Autowired
    private IMadWorklocationsExternalService madWorklocationsExternalService;
    @Autowired
    private CacheManager cacheManager;
    @Resource
    private PcxBillAmtApportionDepartmentDao pcxBillAmtApportionDepartmentDao;
    @Resource
    private IBusinessRuleOptionService businessRuleOptionService;

    @Resource
    private EcsExpCommonService ecsExpCommonService;

    @Resource
    private PcxBasExpTypeDao pcxBasExpTypeDao;

    @Resource
    private PcxBillExpDetailLabourDao pcxBillExpDetailLabourDao;

    @Resource
    private PcxLabourInfoDao pcxLabourInfoDao;

    public static final Supplier<String> TODO_ORDER_ID_LIST = () -> String.format("NEXT_TODO:LINK:%s", PtyContext.getTenantId());
    @Autowired
    private IPcxBillPayDetailService payDetailService;
    @Autowired
    private EcsExpTransService ecsExpTransService;
    @Autowired
    private PcxBillExpAssService pcxBillExpAssService;

    @Override
    public CheckMsg<MadEmployeeDTO> getExpenseUserInfo(EmpInfoQO qo) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        BeanUtils.copyProperties(qo, pcxBaseDTO);
        pcxBaseDTO.setFiscal(String.valueOf(DateUtil.thisYear()));
        MadEmployeeDTO madEmployeeDTO = madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, qo.getUserCode());
        if (Objects.isNull(madEmployeeDTO)) {
            return CheckMsg.fail("当前用户未绑定人员信息，不能发起报销");
        }
        if (StringUtil.isNotEmpty(madEmployeeDTO.getWorkLocationId())) {
            //查询当前员工的工作地
            MadWorkLocations madWorkLocations = madWorklocationsExternalService.selectById(madEmployeeDTO.getWorkLocationId());
            if (madWorkLocations != null) {
                madEmployeeDTO.setLocationName(madWorkLocations.getLocationName());
                madEmployeeDTO.setLocationCode(madWorkLocations.getLocationCode());
                madEmployeeDTO.setAddress(madWorkLocations.getAddress());
                madEmployeeDTO.setDistrict(madWorkLocations.getDistrict());
                madEmployeeDTO.setDistrictCode(madWorkLocations.getDistrictCode());
                madEmployeeDTO.setCity(madWorkLocations.getCity());
                madEmployeeDTO.setCityCode(madWorkLocations.getCityCode());
                madEmployeeDTO.setProvince(madWorkLocations.getProvince());
                madEmployeeDTO.setProvinceCode(madWorkLocations.getProvinceCode());
            }
        }
        return CheckMsg.success(madEmployeeDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<Map<String, Object>> saveDataAndApproved(PcxBillQO qo) {
        //保存
        PcxBill pcxBill = this.saveOrUpdate(qo);

        boolean approvalEnabled = approvalEnabled(pcxBill);
        // fixme 此处针对确定预算岗位进行指标占用
        boolean saveCtrl = false;
        if(PositionEnum.BUDGET_CONFIRM.getCode().equals(qo.getPositionCode()) && approvalEnabled){
            //预占额度
            pcxBalanceCtrlService.saveCtrl(pcxBill, Objects.isNull(qo.getIsValidate())?Boolean.TRUE:qo.getIsValidate() == 1);
            saveCtrl = true;
        }
        //送审,启用审批流程
        if (Objects.nonNull(qo.getIsApprove()) && qo.getIsApprove() == 1 && approvalEnabled) {
            //预占额度
            if (!saveCtrl){
                pcxBalanceCtrlService.saveCtrl(pcxBill, Objects.isNull(qo.getIsValidate())?Boolean.TRUE:qo.getIsValidate() == 1);
            }

            billExpenseCommonService.processApprove(pcxBill, qo.getComment());
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(pcxBill.getId())
                    .billStatus(BillStatusEnum.SUBMIT.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);

            // 未启用审批流程，直接更新为已送审,已支付,已完成
        }else if (Objects.nonNull(qo.getIsApprove()) && qo.getIsApprove() == 1) {
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(pcxBill.getId())
                    .billStatus(BillStatusEnum.SUBMIT.getCode())
                    .approveStatus(BillApproveStatusEnum.APPROVED.getCode())
                    .payStatus(BillPayStatusEnum.PAID.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("billId", pcxBill.getId());
        result.put("auditRuleResult", RuleAct.ACCESS.toString());
        return CheckMsg.success(result);

    }

    private boolean approvalEnabled(PcxBill pcxBill) {
        return this.approvalEnabled(Integer.valueOf(pcxBill.getFiscal()), pcxBill.getAgyCode(), pcxBill.getMofDivCode());
    }

    private boolean approvalEnabled(Integer fiscal, String agyCode, String mofDivCode) {
        return queryOption(fiscal, agyCode, mofDivCode, APPROVAL_ENABLE, "查询是否启用审批流程异常", "未配置审批流程是否启用参数");
    }

    private boolean applyEnabled(Integer fiscal, String agyCode, String mofDivCode) {
        return queryOption(fiscal, agyCode, mofDivCode, APPLY_ENABLE, "查询是否启用事前申请异常", "未配置事前申请是否启用参数");
    }

    private boolean queryOption(Integer fiscal, String agyCode, String mofDivCode, BusinessRuleEnum.BusinessOptionEnum rule,
                                String failMsg, String nullMsg) {
        PaOptionQO optionQO = new PaOptionQO();
        optionQO.setMofDivCode(mofDivCode);
        optionQO.setFiscal(fiscal);
        optionQO.setAgyCode(agyCode);
        optionQO.setOptCode(rule.getOptCode());
        optionQO.setGroupName(rule.getGroupName());

        // 查询是否开启审批流程
        Response<?> response = businessRuleOptionService.selectByQO(optionQO);
        Assert.isTrue(response.isSuccess(), failMsg);
        Assert.isTrue(Objects.nonNull(response.getData()), nullMsg);
        PaOption approvalEnabled = (PaOption) response.getData();
        return approvalEnabled.getOptValue().equals(rule.getOptValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcxBill saveOrUpdate(PcxBillQO qo) {
        billValidateService.preData(qo);
        billValidateService.validate(qo);
        PcxBill pcxBill = billMainService.saveOrUpdate(qo.getPcxBill());
        if (CollectionUtils.isEmpty(qo.getClassifyCodes())) {
            qo.setClassifyCodes(Lists.newArrayList(PositionBlockEnum.values()).stream().map(PositionBlockEnum::getCode).collect(Collectors.toList()));
        }
        processSave(qo, pcxBill);
        //同步所有附件到ecs
        syncAttachToEcs(pcxBill);
        return pcxBill;
    }


    @Override
    public PcxBill viewBasic(String billId) {
        return billMainService.view(billId);
    }


    @Override
    public CheckMsg<PcxBillVO> view(String billId, PositionBlockEnum... viewType) {
        List<String> viewTypeList = Arrays.stream(viewType).map(PositionBlockEnum::getCode).collect(Collectors.toList());
        return view(billId, viewTypeList);
    }


    @Override
    public CheckMsg<PcxBillVO> view(String billId, List<String> viewType) {
        PcxBillVO pcxBillVO = new PcxBillVO();
        // 获取主单信息
        PcxBill pcxBill = billMainService.view(billId);
        if (Objects.isNull(pcxBill)) return CheckMsg.fail("单据不存在");

        PositionQO positionQO = new PositionQO();
        positionQO.setBillId(billId);
        positionQO.setUserCode(PtyContext.getUsername());
        positionQO.setFiscal(pcxBill.getFiscal());
        positionQO.setMofDivCode(pcxBill.getMofDivCode());
        positionQO.setAgyCode(pcxBill.getAgyCode());
        pcxBillVO.setBasicInfo(pcxBill);
        pcxBillVO.setBillId(pcxBill.getId());
        collectApportionDept(pcxBill);
        pcxBillVO.setApportionDeptList(pcxBill.getApportionDeptList());
        if (CollectionUtils.isEmpty(viewType)) {
            viewType = Lists.newArrayList(PositionBlockEnum.values()).stream().map(PositionBlockEnum::getCode).collect(Collectors.toList());
        }
        // 目前通用、培训、会议报销需要展示合同的块
        if (!Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.COMMON.getCode()) &&
                !Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.MEETING.getCode()) &&
                !Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.TRAINING.getCode())) {
            viewType.remove(PositionBlockEnum.CONTRACT.getCode());
        }
        List<ValsetDTO> valset = calculationRuleService.getExtraSubsidyValset(pcxBill.getFiscal(), pcxBill.getAgyCode(), pcxBill.getMofDivCode(), PcxConstant.TRAVEL_DETAIL_3021103);
        pcxBillVO.setOpenExtraSubsidy(CollectionUtils.isNotEmpty(valset));
        pcxBillVO.setOnlySubsidy(pcxBill.getOnlySubsidy());
        viewProcess(pcxBill, pcxBillVO, viewType);
        return CheckMsg.success(pcxBillVO);
    }


    private void collectApportionDept(PcxBill pcxBill) {
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = pcxBillAmtApportionDepartmentDao.selectList(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
                .eq(PcxBillAmtApportionDepartment::getBillId, pcxBill.getId()));
        pcxBill.setApportionDeptList(amtApportionDepartments.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getDepartmentName()))
                .map(PcxBillAmtApportionDepartment::getDepartmentName).distinct().collect(Collectors.toList()));
    }

    @Override
    public CheckMsg<String> next(PcxBillViewQO qo) {
        // 获取上一次next的全部待办结果
        Cache cache = cacheManager.getCache(TODO_ORDER_ID_LIST.get());
        List<String> cacheTodoOrderIds = cache.get(PtyContext.getUsername(), List.class);
        // 获取所有待办任务
        PcxFinanceApprovalBillQO billListQO = new PcxFinanceApprovalBillQO();
        billListQO.setFiscal(qo.getFiscal());
        billListQO.setMofDivCode(qo.getMofDivCode());
        billListQO.setAgyCode(qo.getAgyCode());
        billListQO.setSortField("taskStartTime");
        billListQO.setSortOrder("desc");
        billListQO.setPageLabelCode(PcxBillProcessConstant.BillIndexLabel.APPROVE.getCode().toLowerCase());
        billListQO.setPageIndex(1);
        billListQO.setUserCode(PtyContext.getUsername());
        billListQO.setPageSize(Integer.MAX_VALUE);
        CheckMsg<?> pageResult = pcxFinanceApprovalService.selectFinanceApprovalList(billListQO);
        if (!pageResult.isSuccess()) {
            return CheckMsg.fail(pageResult.getMsgInfo());
        }
        List<String> dbTodoOrderIds = ((PageResult<PcxFinanceApprovalBillVO>)pageResult.getData()).getResult().stream().map(PcxFinanceApprovalBillVO::getId).collect(Collectors.toList());
        String nextBillId = null;
        cache.put(PtyContext.getUsername(), dbTodoOrderIds);
        // 如果存在当前id, 则从缓存中获取下一条
        if (CollectionUtils.isNotEmpty(cacheTodoOrderIds) && cacheTodoOrderIds.contains(qo.getBillId())) {
            int index = cacheTodoOrderIds.indexOf(qo.getBillId());
            int ptr = (index + 1) % cacheTodoOrderIds.size();
            while (ptr != index) {
                if (dbTodoOrderIds.contains(cacheTodoOrderIds.get(ptr)))
                    nextBillId = cacheTodoOrderIds.get(ptr);
                ptr = (ptr + 1) % cacheTodoOrderIds.size();
            }
        }
        // 如果缓存中不存在当前id, 直接从数据库记录中获取第一条
        if (CollectionUtils.isNotEmpty(dbTodoOrderIds) && (CollectionUtils.isEmpty(cacheTodoOrderIds) || !cacheTodoOrderIds.contains(qo.getBillId()) || nextBillId == null)) {
            nextBillId = dbTodoOrderIds.get(0);
        }
        // 找到当前单据所在
        return CheckMsg.success(nextBillId);
    }

    @Override
    public CheckMsg<Integer> todoTaskNum(PcxBillListQO qo) {

        // 设置填报人
        qo.setClaimantUserCode(PtyContext.getUsername());
        // 查询待审批数据集合
        List<TodoTaskVO> todoTasks = fetchTodoTasks(qo);
        Map<String, List<TodoTaskVO>> bizTodoTaskMap = todoTasks.stream().collect(Collectors.groupingBy(TodoTaskVO::getBusiId));

        // 待审批单据编码
        List<String> myApprovalBizIds = todoTasks.stream().map(TodoTaskVO::getBusiId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(myApprovalBizIds)) {
            List<PcxBill> pcxBills = billMainService.selectMainBill(PcxBill.builder().ids(myApprovalBizIds).build());
            Map<String, PcxBill> bizTodoBillMap = pcxBills.stream().collect(Collectors.toMap(PcxBill::getId, Function.identity()));
            // make bill岗的代办任务是退回的,不能留,否则会排序到我的代办里
            myApprovalBizIds = myApprovalBizIds.stream().filter(bizId -> !bizTodoBillMap.containsKey(bizId) ||
                    !(bizTodoBillMap.get(bizId).getClaimantUserCode().equals(qo.getClaimantUserCode()) && bizTodoTaskMap.get(bizId).stream().anyMatch(todo -> todo.getNodeId().equals(PcxNodeEnum.make_bill.getId())))).collect(Collectors.toList());
        }
        return CheckMsg.success(myApprovalBizIds.size());
    }

    @Override
    public CheckMsg<Map<String, Long>> billFuncTaskNum(PcxBillListQO qo) {
        qo.setClaimantUserCode(PtyContext.getUsername());
        List<PcxBill> pcxBills = billMainService.selectMainBill(PcxBill.builder()
                        .owner(PtyContext.getUsername())
                        .build());
        Map<String, Long> result = pcxBills.stream().collect(Collectors.groupingBy(bill -> {
            if (bill.getBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode()))
                return PcxBillProcessConstant.BillIndexLabel.MY_APPLY.getCode();
            if (bill.getBillFuncCode().equals(BillFuncCodeEnum.EXPENSE.getCode()))
                return PcxBillProcessConstant.BillIndexLabel.MY_EXPENSE.getCode();
            if (bill.getBillFuncCode().equals(BillFuncCodeEnum.LOAN.getCode()))
                return PcxBillProcessConstant.BillIndexLabel.MY_LOAN.getCode();
            return StringUtil.EMPTY;
        }, Collectors.counting()));
        return CheckMsg.success(result);
    }

    @Override
    public CheckMsg<PcxBillPageResult<PcxBillListVO>> selectWithPage(PcxBillListQO qo) {
        if (qo == null) {
            return CheckMsg.fail("请求参数不能为空");
        }

        try {
            //设置填报人
            qo.setClaimantUserCode(PtyContext.getUsername());

            List<TodoTaskVO> todoTasks = fetchTodoTasks(qo);
            List<TodoTaskVO> approvingTasks = fetchApprovingTasks(qo);
            List<DoneTaskVO> doneTasks = fetchDoneTasks(qo);
            Map<String, List<TodoTaskVO>> bizTodoTaskMap = todoTasks.stream().collect(Collectors.groupingBy(TodoTaskVO::getBusiId));


            // 待审批单据编码
            List<String> myApprovalBizIds = todoTasks.stream().map(TodoTaskVO::getBusiId).collect(Collectors.toList());
            // 已审批单据编码
            List<String> myApprovedBizIds = doneTasks.stream().map(DoneTaskVO::getBusinessKey).distinct().collect(Collectors.toList());

            // 如果传递了id, 那么不在待办、已办、 个人审批中单据的, 直接返回空集合
            if (StringUtil.isNotBlank(qo.getBillId())) {
                List<String> allBizIds = new ArrayList<>();
                allBizIds.addAll(myApprovalBizIds);
                allBizIds.addAll(myApprovedBizIds);
                allBizIds.addAll(approvingTasks.stream().map(TodoTaskVO::getBusiId).collect(Collectors.toList()));
                PcxBill bill = billMainService.view(qo.getBillId());
                PcxBillPageResult<PcxBillListVO> data = new PcxBillPageResult<>();
                // 非自己的单子且非代报的单子
                if (!bill.getClaimantUserCode().equals(PtyContext.getUsername()) && !bill.getCreator().equals(PtyContext.getUsername())) {
                    if (ObjectUtil.isNull(bill))
                        return CheckMsg.success(data);

                    if (!allBizIds.contains(qo.getBillId()) && !bill.getCreator().equals(PtyContext.getUsername())) {
                        return CheckMsg.success(data);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(myApprovalBizIds)) {
                List<PcxBill> pcxBills = billMainService.selectMainBill(PcxBill.builder().ids(myApprovalBizIds).build());
                Map<String, PcxBill> bizTodoBillMap = pcxBills.stream().collect(Collectors.toMap(PcxBill::getId, Function.identity()));
                // make bill岗的代办任务是退回的,不能留,否则会排序到我的代办里
                myApprovalBizIds = myApprovalBizIds.stream().filter(bizId -> !bizTodoBillMap.containsKey(bizId) ||
                        !(bizTodoBillMap.get(bizId).getClaimantUserCode().equals(qo.getClaimantUserCode()) && bizTodoTaskMap.get(bizId).stream().anyMatch(todo -> todo.getNodeId().equals(PcxNodeEnum.make_bill.getId())))).collect(Collectors.toList());
            }

            // 按照待审批->我的报销->其他的顺序进行分页
            com.github.pagehelper.PageInfo<PcxBill> bills = billMainService.selectIndexPage(qo, myApprovalBizIds, myApprovedBizIds, qo.getLabel());

            if (CollectionUtils.isEmpty(bills.getList())) {
                return CheckMsg.success(PageTransformer.INSTANCE.transPageBillToVO(PageTransformer.INSTANCE.plus2result(bills)));
            }

            // 分页对象转换
            PcxBillPageResult<PcxBillListVO> result = PageTransformer.INSTANCE.transPageBillToVO(PageTransformer.INSTANCE.plus2result(bills));

            // 如果是待办页, 将再查询待办单据id集合, 根据事项分组
            setItemBillCollection(result, qo, myApprovalBizIds, myApprovedBizIds, qo.getLabel());

            // 查询流程审批节点
            setProcessNodes(result, qo, todoTasks, doneTasks);

            // 处理分页数据
            billListService.handleIndexPageData(result.getResult(), qo, todoTasks, approvingTasks);

            // 赋值task id
//            pendingTaskId(qo, result, doneTasks, todoTasks, approvingTasks);

            return CheckMsg.success(result);
        } catch (NullPointerException e) {
            log.error("空指针异常，请求参数: {}", qo, e);
            return CheckMsg.fail("获取待审批单据异常", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("非法参数异常，请求参数: {}", qo, e);
            return CheckMsg.fail("获取待审批单据异常", e.getMessage());
        } catch (Exception e) {
            log.error("未知异常，请求参数: {}", qo, e);
            return CheckMsg.fail("获取待审批单据异常", e.getMessage());
        }
    }

    private void setItemBillCollection(PcxBillPageResult<PcxBillListVO> result, PcxBillListQO qo, List<String> myApprovalBizIds, List<String> myApprovedBizIds, PcxBillProcessConstant.BillIndexLabel label) {
        if (PcxBillProcessConstant.BillIndexLabel.APPROVE.equals(label)) {
            result.setBillItemRelVOList(billMainService.selectBillIdAndItemRelation(qo, myApprovalBizIds, myApprovedBizIds, qo.getLabel()));
        }
    }

    /**
     * 给列表记录返回工作流任务id
     * @param qo
     * @param result
     * @param doneTasks
     * @param todoTasks
     * @param approvingTasks
     */
    private static void pendingTaskId(PcxBillListQO qo, PageResult<PcxBillListVO> result, List<DoneTaskVO> doneTasks, List<TodoTaskVO> todoTasks, List<TodoTaskVO> approvingTasks) {
//        if (PcxBillProcessConstant.BillIndexLabel.APPROVED.equals(qo.getLabel())) {
        result.getResult().forEach(item -> {
            DoneTaskVO max = doneTasks.stream().filter(doneTask -> doneTask.getBusinessKey().equals(item.getBillId())).max(Comparator.comparing(DoneTaskVO::getEndTime)).orElse(DoneTaskVO.builder().build());
            item.setTaskId(max.getTaskId());
            item.setCanRevoke(max.isCanRevoke());
        });
//            return;
//        }
//        if (qo.getLabel() == null || PcxBillProcessConstant.BillIndexLabel.APPROVE.equals(qo.getLabel())) {
//            result.getResult().forEach(item -> {
//                TodoTaskVO max = todoTasks.stream().filter(todoTask -> todoTask.getBusiId().equals(item.getBillId())).findFirst().orElse(new TodoTaskVO());
//                item.setTaskId(StrUtil.firstNonBlank(item.getTaskId(), max.getTaskId()));
//            });
//        }
//        result.getResult().forEach(item -> {
//            TodoTaskVO max = approvingTasks.stream().filter(todoTask -> todoTask.getBusiId().equals(item.getBillId())).findFirst().orElse(new TodoTaskVO());
//            item.setTaskId(StrUtil.firstNonBlank(item.getTaskId(), max.getTaskId()));
//        });
    }

    @Override
    public ResponseEntity<byte[]> exportData(PcxBillExportQO qo) throws IOException {
        PcxBillExpLedgerQO pcxBill = this.setExpLedgerParam(qo);
        String title = "支出台账";
        if ("apply".equals(qo.getExpLedgerTab())) {// 申请单
            title = "支出台账_申请单";
        } else if ("expense".equals(qo.getExpLedgerTab())) {// 支出费用
            title = "支出台账_支出费用";
        } else if ("dealing".equals(qo.getExpLedgerTab())) {// 往来费用
            title = "支出台账_往来费用";
        }
        Map<String, Object> params = BeanUtil.beanToMap(pcxBill);
        ExcelExportVO excelExportVO = MadUtils.formTitle(params);
        excelExportVO.setTitle(title);
        excelExportVO.setFileName(title);
        PaFieldSettingQO paFieldSettingQO = new PaFieldSettingQO();
        BeanUtil.copyProperties(qo, paFieldSettingQO);
        paFieldSettingQO.validateUserQuery();
        paFieldSettingQO.setIsShow(1);//列表显示字段才导出
        List<PaFieldSettingVO> fieldSettingList = pcxPaFieldSettingService.getUserSettings(paFieldSettingQO);//查询导出字段列表
        if (CollectionUtil.isEmpty(fieldSettingList)) {
            return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + StringUtil.urlEncode(title) + MadConstants.XLSX)
                    .body(new byte[0]);
        }
        excelExportVO.setTitleRowVOList(buildDzExportTitleRowVOS(fieldSettingList));//导出字段处理
        List<PcxBillExpLedgerVO> pcxBillList = pcxBillDao.selectPcxBillJoinVoucherList(pcxBill);
        List<PcxBillExpLedgerVO> data = setExpLedgerVO(pcxBillList);
        if (CollectionUtil.isEmpty(data)) {
            return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + StringUtil.urlEncode(title) + MadConstants.XLSX)
                    .body(new byte[0]);
        }
        File file = ExcelUtils.export(excelExportVO, data);
        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + StringUtil.urlEncode(title) + MadConstants.XLSX)
                .body(FileUtil.readAllBytes(file.toPath()));
    }

    private List<List<ExcelExportTitleRowVO>> buildDzExportTitleRowVOS(List<PaFieldSettingVO> fieldSettingList) {
        List<List<ExcelExportTitleRowVO>> resultList = Lists.newArrayList();
        List<ExcelExportTitleRowVO> exportTitleRowVOS = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(fieldSettingList)) {
            fieldSettingList.forEach(item -> {
                exportTitleRowVOS.add(buildExcelTitleRowVO(item.getColumnCode(), item.getColumnName(), ALIGN_CENTER));
            });
        }
        resultList.add(exportTitleRowVOS);
        return resultList;
    }

    private static ExcelExportTitleRowVO buildExcelTitleRowVO(String colCode, String colName, String colAlign) {
        return buildExcelTitleRowVO(colCode, colName, 120, colAlign);
    }

    private static ExcelExportTitleRowVO buildExcelTitleRowVO(String colCode, String colName, Integer colWidth, String colAlign) {
        return buildExcelTitleRowVO(colCode, colName, "string", colAlign, colWidth);
    }

    private static ExcelExportTitleRowVO buildExcelTitleRowVO(String colCode, String colName, String colDataType, String colAlign, Integer colWidth) {
        ExcelExportTitleRowVO exportTitleRowVO = new ExcelExportTitleRowVO();
        exportTitleRowVO.setCode(colCode);
        exportTitleRowVO.setName(colName);
        exportTitleRowVO.setRowSpan(1);
        exportTitleRowVO.setColSpan(1);
        exportTitleRowVO.setWidth(colWidth);
        exportTitleRowVO.setDataType(colDataType);
        exportTitleRowVO.setAlign(colAlign);
        return exportTitleRowVO;
    }

    @Override
    public CheckMsg<PageResult<PcxBillExpLedgerVO>> selectExpLedgerPage(PcxBillListQO qo) {
        if (qo == null) {
            return CheckMsg.fail("请求参数不能为空");
        }
        try {
            // 查询与台账支出相关的主单据信息
            PcxBillExpLedgerQO pcxBill = this.setExpLedgerParam(qo);
            PageInfo<PcxBillExpLedgerVO> pageInfo = PageHelper.startPage(qo.getPageIndex(), qo.getPageSize())
                    .doSelectPageInfo(() -> {
                        pcxBillDao.selectPcxBillJoinVoucherList(pcxBill);// 单据查询
                    });
            List<PcxBillExpLedgerVO> pcxBillList = pageInfo.getList();
            if (CollectionUtils.isEmpty(pcxBillList)) {
                return CheckMsg.success(new PageResult<>());
            }
            List<PcxBillExpLedgerVO> result = setExpLedgerVO(pcxBillList);
            // 分页转换
            Page<PcxBillExpLedgerVO> resultPage = new Page<PcxBillExpLedgerVO>(qo.getPageIndex(), qo.getPageSize(), pageInfo.getTotal()).setRecords(result);
            // 返回包含单据支付详情列表的成功消息
            return CheckMsg.success(PageTransformer.INSTANCE.plus2result(resultPage));
        } catch (NullPointerException e) {
            log.error("空指针异常，请求参数: {}", qo, e);
            return CheckMsg.fail("获取单据异常", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("非法参数异常，请求参数: {}", qo, e);
            return CheckMsg.fail("获取单据异常", e.getMessage());
        } catch (Exception e) {
            log.error("未知异常，请求参数: {}", qo, e);
            return CheckMsg.fail("获取单据异常", e.getMessage());
        }
    }

    /**
     * 根据查询对象获取待办任务列表
     * 此方法主要用于处理待办任务的查询逻辑，根据查询对象的条件决定是否查询待办任务，
     * 并将查询结果返回
     *
     * @param qo 查询对象，包含查询待办任务所需的条件，如标签、部门代码、机构代码和用户代码
     * @return 返回待办任务列表，如果查询失败或没有待办任务，则返回空列表
     */
    public List<TodoTaskVO> fetchTodoTasks(PcxBillListQO qo) {
        List<TodoTaskVO> todoTasks = new ArrayList<>();
        // 判断查询标签是否为null或"批准"，如果是，则执行待办任务查询
        List<PaUserDesignateDTO> asDesignate = pcxUserDesignateService.selectValidAsDesignate(PtyContext.getUsername());
        PaUserDesignateDTO designate = pcxUserDesignateService.selectValidDesignate(PtyContext.getUsername());
        log.info("当前登录人{}, 委托信息{}, 被委托信息{}", PtyContext.getUsername(), designate, asDesignate);
        Set<String> userCodes = new HashSet<>();
        userCodes.add(PtyContext.getUsername());
        userCodes.addAll(asDesignate.stream().map(PaUserDesignateDTO::getUserCode).collect(Collectors.toList()));
        qo.setDesignateUser(ObjectUtils.isEmpty(designate) ? "" : designate.getDesignateUserCode());
        qo.setDesignatedUserList(asDesignate.stream().map(PaUserDesignateDTO::getUserCode).collect(Collectors.toList()));
        if (qo.getLabel() == null || PcxBillProcessConstant.BillIndexLabel.APPROVE.equals(qo.getLabel())) {
            // 构建待办任务查询参数，并调用服务方法获取待办任务列表
            CheckMsg<List<TodoTaskVO>> todoMsg = processService.getTodoTaskWithCandidateAndAssigned(TodoTaskParamQO.builder()
                    .mofDivCode(qo.getMofDivCode())
//                    .agyCode(qo.getAgyCode())
                    .candidateUsers(userCodes)
                    .build());
            // 如果查询成功，则将查询结果赋值给待办任务列表
            if (todoMsg.isSuccess()) {
                todoTasks = todoMsg.getData().stream().peek(todoTask -> {
                    todoTask.setDesignate(!todoTask.getUsers().contains(PtyContext.getUsername()));
                    if (todoTask.isDesignate()) {
                        // 取交集
                        todoTask.setUsers(
                                todoTask.getUsers().stream().filter(
                                        userCode -> asDesignate.stream().anyMatch(paUserDesignateDTO -> paUserDesignateDTO.getUserCode().equals(userCode)))
                                        .collect(Collectors.toSet()));
                    }
                }).filter(todoTask -> {
                    if (qo.isOnlyFinance()) {
                        return todoTask.getNodeId().startsWith(PcxNodeEnum.finance_audit.getId()) ||
                                todoTask.getNodeId().startsWith(PcxNodeEnum.financial_director.getId()) ||
                                todoTask.getNodeId().startsWith(PcxNodeEnum.legality_audit.getId());
                    }else {
                        return true;
                    }
                }).collect(Collectors.toList());
            }
        }
        // 返回待办任务列表
        return todoTasks;
    }


    /**
     * 根据查询对象获取用户审批中的任务列表
     * 此方法专注于处理审批中的任务，特别是当查询标签为null或特定值时
     * 它通过调用流程服务来获取待办任务，旨在为用户提供一个筛选机制，
     * 以便用户可以轻松地获取他们提交审批的任务
     *
     * @param qo 查询对象，包含查询审批中任务所需的参数，如MOF部门代码、机构代码、用户代码和标签
     * @return 返回一个待审批任务的列表如果查询条件不满足或服务调用失败，将返回一个空列表
     */
    public List<TodoTaskVO> fetchApprovingTasks(PcxBillListQO qo) {
        List<TodoTaskVO> approvingTasks = new ArrayList<>();
        // 判断查询对象的标签是否为null或特定值，以决定是否需要查询待审批任务
        if (qo.getLabel() == null || PcxBillProcessConstant.BillIndexLabel.EXPENSE.equals(qo.getLabel())
                || PcxBillProcessConstant.BillIndexLabel.MY_APPLY.equals(qo.getLabel())
                || PcxBillProcessConstant.BillIndexLabel.MY_EXPENSE.equals(qo.getLabel())
                || PcxBillProcessConstant.BillIndexLabel.MY_LOAN.equals(qo.getLabel())) {

            List<String> userCodes = new ArrayList<>();
            userCodes.add(PtyContext.getUsername());
            userCodes.add(pcxUserService.selectBeReportUserCode(PtyContext.getUsername()));

            // 调用流程服务获取待办任务，构建参数时使用查询对象中的相应值
            CheckMsg<List<TodoTaskVO>> approvingMsg = processService.getTodoTaskWithCreator(TodoTaskParamQO.builder()
                    .mofDivCode(qo.getMofDivCode())
                    .agyCode(qo.getAgyCode())
                    // 这里要找到所有当前登录人的被代报人信息
//                    .candidateUsers(CollectionUtil.set(false, qo.getClaimantUserCode()))
                    .creators(userCodes)
//                    .creator(qo.getClaimantUserCode())
                    .build());
            // 如果服务调用成功，则获取并保存待审批任务列表
            if (approvingMsg.isSuccess()) {
                approvingTasks = approvingMsg.getData();
            }
        }
        // 返回待审批任务列表，无论是否为空
        return approvingTasks;
    }


    /**
     * 根据查询对象获取用户审批中的任务列表
     * 此方法专注于处理审批中的任务，特别是当查询标签为null或特定值时
     * 它通过调用流程服务来获取待办任务，旨在为用户提供一个筛选机制，
     * 以便用户可以轻松地获取他们提交审批的任务
     *
     * @param qo 查询对象，包含查询审批中任务所需的参数，如MOF部门代码、机构代码、用户代码和标签
     * @return 返回一个待审批任务的列表如果查询条件不满足或服务调用失败，将返回一个空列表
     */
    public List<DoneTaskVO> fetchDoneTasks(PcxBillListQO qo) {
        List<DoneTaskVO> approvingTasks = new ArrayList<>();
        // 判断查询对象的标签是否为null或特定值，以决定是否需要查询已审批任务
        if (!PcxBillProcessConstant.BillIndexLabel.APPROVE.equals(qo.getLabel())) {
            // 调用流程服务获取待办任务，构建参数时使用查询对象中的相应值
            CheckMsg<List<DoneTaskVO>> approvingMsg = processService.getDoneTaskWithCandidateAndAssigned(DoneTaskParamQO.builder()
                    .mofDivCode(qo.getMofDivCode())
                    .assignee(qo.getClaimantUserCode())
                    .build());
            // 如果服务调用成功，则获取并保存待审批任务列表
            if (approvingMsg.isSuccess()) {
                approvingTasks = approvingMsg.getData();
                if (PcxBillProcessConstant.BillIndexLabel.APPROVED.equals(qo.getLabel())) {
                    approvingTasks.removeAll(approvingTasks.stream().filter(doneTask -> PcxNodeEnum.make_bill.getId().equals(doneTask.getNodeId())).collect(Collectors.toList()));
                }
//                        .stream()
//                        .filter(task ->
//                                !Objects.equals(task.getNodeId(), PcxNodeEnum.make_bill.getId())
//                                && (task.getOperation().equals(WorkflowConst.SUBMIT_TASK_DELETE_REASON) || task.getOperation().equals(WorkflowConst.ROLLBACK_TASK_DELETE_REASON))
//                ).collect(Collectors.toList());
            }
        }
        // 返回待审批任务列表，无论是否为空
        return approvingTasks;
    }

    @Override
    public void updateComparedStatus(PcxBillQO qo) {
        if (CollectionUtils.isEmpty(qo.getIds()) || ObjectUtils.isEmpty(qo.getPcxBill()) || ObjectUtils.isEmpty(qo.getPcxBill().getComparedStatus())) {
            return;
        }
        // 更新单据的对比状态
        pcxBillDao.update(PcxBill.builder().comparedStatus(qo.getPcxBill().getComparedStatus()).build(), Wrappers.lambdaUpdate(PcxBill.class)
                .in(PcxBill::getId, qo.getIds()));
    }

    @Override
    public List<MadEmployeeDTO> selectClaimant(String billId) {
        CheckMsg<PcxBillVO> data = view(billId, PositionBlockEnum.EXPENSE_DETAIL);
        if (!data.isSuccess() && data.getData() == null) return Lists.newArrayList();
        PcxBill basicInfo = data.getData().getBasicInfo();
        String creator = basicInfo.getCreator();
        PcxBaseDTO pcxBaseDTO = PcxBaseDTO.builder()
                .agyCode(basicInfo.getAgyCode())
                .mofDivCode(basicInfo.getMofDivCode())
                .fiscal(basicInfo.getFiscal()).build();
        MadEmployeeDTO madEmployeeDTO = madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, creator);
        List<PcxBillExpDetailBase> expenseDetail = data.getData().getExpenseDetail();
        List<String> codes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(expenseDetail)){
            expenseDetail.forEach(expense -> {
                if (expense instanceof PcxBillExpDetailTravel){
                    PcxBillExpDetailTravel travelExpense = (PcxBillExpDetailTravel) expense;
                    if (Objects.equals(PcxConstant.EMP_TYPE_INNER,travelExpense.getEmpType())){
                        codes.add(travelExpense.getEmpCode());
                    }
                }
            });
        }
        if (CollectionUtil.isNotEmpty(codes)){
            List<MadEmployeeDTO> madEmployeeDTOS = madEmployeeExternalService.selectByMadCodes(codes, basicInfo.getAgyCode(), Integer.valueOf(basicInfo.getFiscal()), basicInfo.getMofDivCode());
            if (CollectionUtil.isNotEmpty(madEmployeeDTOS)){
                return madEmployeeDTOS.stream()
                        .filter(baseDataVo -> !Objects.equals(baseDataVo.getEmployeeCode(),madEmployeeDTO.getEmployeeCode()))
                        .collect(Collectors
                                .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MadEmployeeDTO::getEmployeeCode))), ArrayList::new));
            }
        }
        return Lists.newArrayList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reOpenBill(PcxBillQO qo) {
        Assert.notNull(qo, "请求参数不能为空");
        Assert.notNull(qo.getBillId(), "单据ID不能为空");
        PcxBill bill = billMainService.view(qo.getBillId());
        boolean approvalEnabled = approvalEnabled(bill);
        Assert.isTrue(!approvalEnabled, "审批流程已启用, 无权通过此接口更新单据状态");

        BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                .billId(bill.getId())
                .billStatus(BillStatusEnum.SAVE.getCode())
                .approveStatus(BillApproveStatusEnum.SAVE.getCode())
                .payStatus(BillPayStatusEnum.UNPAID.getCode())
                .build();
        billMainService.updateStatus(updateStatusDTO);
        return true;
    }

    @Override
    public PcxBillVO calculateBillAmt(PcxBillCalculateQO qo) {
        //根据页面的传入的数据转换成vo数
        return pcxBillCaseUtil.caseQO2VO(qo);
    }

    @Override
    public CheckMsg initBill(InitBillQO qo) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        BeanUtils.copyProperties(qo, pcxBaseDTO);
        pcxBaseDTO.setFiscal(String.valueOf(DateUtil.thisYear()));
        MadEmployeeDTO madEmployeeDTO = madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, qo.getUserCode());
        if (Objects.isNull(madEmployeeDTO)) {
            return CheckMsg.fail("当前用户未绑定人员信息，不能发起报销");
        }
        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        BeanUtils.copyProperties(qo, pcxBasExpTypeQO);
        List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectItemExpenseTypeList(pcxBasExpTypeQO);
        ItemBizTypeEnum bizType = ItemBizTypeEnum.COMMON;
        List<PcxBillExpBase> pcxBillExpBases = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pcxBasExpTypes)) {
            bizType = ItemBizTypeEnum.expenseTypeMatchBizType(pcxBasExpTypes.get(0).getExpenseCode());
        }
        PcxBill bill = PcxBill.builder()
                .agyCode(qo.getAgyCode())
                .mofDivCode(qo.getMofDivCode())
                .fiscal(qo.getFiscal())
                .billFuncCode(qo.getBillFuncCode())
                .creator(qo.getUserCode())
                .creatorName(madEmployeeDTO.getEmployeeName())
                .claimantName(madEmployeeDTO.getEmployeeName())
                .claimantCode(madEmployeeDTO.getEmployeeCode())
                .claimantUserCode(qo.getUserCode())
                .departmentCode(madEmployeeDTO.getDepartmentCode())
                .departmentName(madEmployeeDTO.getDepartmentName())
                .bizType(bizType.getCode())
                .bizTypeName(bizType.getName())
                .itemCode(qo.getItemCode())
                .itemName(qo.getItemName())
                .build();
        ecsExpTransService.initBill(bill, pcxBillExpBases);
        return view( bill.getId());
    }


    /**
     * 根据查询结果设置流程节点信息
     * 此方法旨在根据功能代码对单据列表进行分组，并为每组内的单据设置相应的流程节点信息
     * 主要步骤包括：
     * 1. 按功能代码对结果进行分组
     * 2. 遍历每个分组，查询对应的流程定义
     * 3. 过滤出需要的节点，并为单据设置岗位代码
     *
     * @param result    查询结果对象，包含单据列表信息
     * @param qo        查询对象，包含查询参数
     * @param todoTasks
     * @param doneTasks
     */
    private void setProcessNodes(PageResult<PcxBillListVO> result, PcxBillListQO qo, List<TodoTaskVO> todoTasks, List<DoneTaskVO> doneTasks) {
        Map<String, TodoTaskVO> todoTaskMap = todoTasks.stream().collect(Collectors.toMap(TodoTaskVO::getBusiId, Function.identity()));
        Map<String, DoneTaskVO> doneTaskMap = doneTasks.stream().collect(Collectors.toMap(DoneTaskVO::getBusinessKey, Function.identity(),
                (a, b) -> a.getEndTime().compareTo(b.getEndTime()) > 0 ? b : a));
        // 按功能代码对单据列表进行分组
        result.getResult().stream().collect(Collectors.groupingBy(PcxBillListVO::getBillFuncCode)).forEach((k, v) -> {
            definePositionCode(qo, k, v, todoTaskMap, doneTaskMap);
        });
        // 设置是否可以撤回标识
        result.getResult().forEach(e -> {
            DoneTaskVO max = doneTasks.stream().filter(doneTask -> doneTask.getBusinessKey().equals(e.getBillId())).max(Comparator.comparing(DoneTaskVO::getEndTime)).orElse(DoneTaskVO.builder().build());
            e.setTaskId(max.getTaskId());
            e.setCanRevoke(max.isCanRevoke());
            e.setCanFinishedRevoke(max.isCanFinishedRevoke());
        });
    }

    private void definePositionCode(PcxBillListQO qo, String k, List<PcxBillListVO> v, Map<String, TodoTaskVO> todoTaskMap, Map<String, DoneTaskVO> doneTaskMap) {
        try {
            // 查询流程定义
            CheckMsg<Object> msg = processService.queryProcessDefinition(qo.getAgyCode(), qo.getMofDivCode(), k, null);
            PcxProcessDefinition def = (PcxProcessDefinition) msg.getData();
            // 如果流程节点列表非空，未审批的取制单岗，其他取最后一个节点作为位置代码
            if (!CollectionUtils.isEmpty(def.getNodes())) {
                v.forEach(e -> {
                    e.setPositionCode(ProcessService.findPositionCode(todoTaskMap, doneTaskMap, def.getNodes(), e.getBillId(), e.getApproveStatus()).getKey());
                });
            }
        } catch (Exception e) {
            // 记录查询流程审批节点失败的错误日志
            log.error("查询流程审批节点失败，请求参数: {}", qo, e);
        }
    }

    private PcxBillExpLedgerQO setExpLedgerParam(PcxBillListQO qo) {
        PcxBillExpLedgerQO pcxBill = new PcxBillExpLedgerQO();
        BeanUtil.copyProperties(qo, pcxBill);
        List<String> billFuncCodeList = new ArrayList<String>();
        if ("apply".equals(qo.getExpLedgerTab())) {// 申请单
            billFuncCodeList.add(BillFuncCodeEnum.APPLY.getCode());
        } else if ("expense".equals(qo.getExpLedgerTab())) {// 支出费用
            billFuncCodeList.add(BillFuncCodeEnum.EXPENSE.getCode());
        } else if ("dealing".equals(qo.getExpLedgerTab())) {// 往来费用
            billFuncCodeList.add(BillFuncCodeEnum.LOAN.getCode());
        }
        if (ObjectUtil.isNotEmpty(qo.getItemCode())) {
            pcxBill.setItemCode(null);
            pcxBill.setItemCodeLike(qo.getItemCode());// 查询包含事项子类型
        }
        pcxBill.setBillFuncCodes(billFuncCodeList);
        DataScopeQO dataScopeQO = new DataScopeQO();
        dataScopeQO.setFiscal(qo.getFiscal());
        dataScopeQO.setAgyCode(qo.getAgyCode());
        dataScopeQO.setMofDivCode(qo.getMofDivCode());
        dataScopeQO.setUserCode(qo.getUserCode());
        JSONObject dataScope = new JSONObject();
        dataScope = dataScopeService.getDataScope(dataScopeQO);// 用户数据权限设置
        pcxBill.setDataScope(dataScope);
        if (CollectionUtil.isNotEmpty(dataScope)) {// 数据权限处理
            String code = dataScope.getString("code");
            String scope = dataScope.getString("scope");
            pcxBill.setDataScopeCode(code);
            if ("1".equals(code)) {// 仅个人数据权限（DAO层处理）
                pcxBill.setDataScopeUserCode(scope);
            } else if ("2".equals(code)) {// 授权部门数据权限（DAO层处理）
                pcxBill.setDataScopeDeptCodes(scope.split(","));
            } else if ("3".equals(code)) {// 授权机构全部数据权限（DAO层处理）
                pcxBill.setDataScopeAgyCodes(scope.split(","));
            }
        }
        return pcxBill;
    }

    private List<PcxBillExpLedgerVO> setExpLedgerVO(List<PcxBillExpLedgerVO> pcxBillList) {
        List<String> billIds = pcxBillList.stream().map(PcxBillExpLedgerVO::getId).collect(Collectors.toList());
        List<String> applyRelationBillIds = new ArrayList<String>();
        List<PcxBillRelation> applyPcxBillRelations = pcxBillRelationService.selectByBillIds(billIds, BillFuncCodeEnum.APPLY.getCode());// 关联申请单
        if (CollectionUtils.isNotEmpty(applyPcxBillRelations)) {
            applyRelationBillIds = applyPcxBillRelations.stream().map(PcxBillRelation::getBillId).collect(Collectors.toList());// 关联申请单的单据id列表
        }
        List<String> contractRelationBillIds = new ArrayList<String>();
        List<PcxBillRelation> contractPcxBillRelations = pcxBillRelationService.selectByBillIds(billIds, BillFuncCodeEnum.CONTRACT.getCode());// 关联合同单
        if (CollectionUtils.isNotEmpty(contractPcxBillRelations)) {
            contractRelationBillIds = contractPcxBillRelations.stream().map(PcxBillRelation::getBillId).collect(Collectors.toList());// 关联合同单的单据id列表
        }
        List<PcxBillExpLedgerVO> result = new ArrayList<PcxBillExpLedgerVO>();
        result = BeanUtil.copyToList(pcxBillList, PcxBillExpLedgerVO.class);
        List<String> applyFinalRelationBillIds = applyRelationBillIds;
        List<String> contractFinalRelationBillIds = contractRelationBillIds;
        result.forEach(x -> {
            if (!Objects.nonNull(x.getLoanAmt())) {
                x.setLoanAmt(BigDecimal.ZERO);// 核销金额
            }
            if (!Objects.nonNull(x.getCheckAmt())) {
                x.setCheckAmt(BigDecimal.ZERO);// 申请/往来金额
            }
            if (applyFinalRelationBillIds.contains(x.getId())) {//关联申请单
                x.setIsPreApply("是");// 是否事前申请
            } else {
                x.setIsPreApply("否");// 是否事前申请
            }
            if (contractFinalRelationBillIds.contains(x.getId())) {//关联合同单
                x.setIsSignContract("是");// 是否签约合同
            } else {
                x.setIsSignContract("否");// 是否签约合同
            }
            if ("0".equals(x.getApproveStatus())) {
                x.setApproveStatus("保存");// 审批状态
            } else if ("1".equals(x.getApproveStatus())) {
                x.setApproveStatus("审批中");// 审批状态
            } else if ("2".equals(x.getApproveStatus())) {
                x.setApproveStatus("审批完成");// 审批状态
            }
            if ("0".equals(x.getPayStatus())) {
                x.setPayStatus("未支付");// 支付状态
            } else if ("1".equals(x.getPayStatus())) {
                x.setPayStatus("部分支付");// 支付状态
            } else if ("2".equals(x.getPayStatus())) {
                x.setPayStatus("已支付");// 支付状态
            } else if ("3".equals(x.getPayStatus())) {
                x.setPayStatus("支付失败");// 支付状态
            }
            if ("0".equals(x.getCompletedStatus())) {
                x.setCompletedStatus("未结项");// 结项状态
            } else if ("1".equals(x.getCompletedStatus())) {
                x.setCompletedStatus("待结项");// 结项状态
            } else if ("2".equals(x.getCompletedStatus())) {
                x.setCompletedStatus("结项中");// 结项状态
            } else if ("3".equals(x.getCompletedStatus())) {
                x.setCompletedStatus("完结");// 结项状态
            }
            x.setPositionName("待审批岗位名称");// TODO 待审批岗位名称（待定）
        });
        return result;
    }


    /*****
     * 获取单据金额统计数据
     * @param qo
     * @return CheckMsg<Map < String, BigDecimal>>
     */
    @Override
    public CheckMsg<Map<String, BigDecimal>> getAmtSum(PcxBillListQO qo) {
        // 初始化结果对象，默认设置为成功状态
        CheckMsg<Map<String, BigDecimal>> result = CheckMsg.success();
        // 初始化结果 Map，用于存储最终返回的数据
        Map<String, BigDecimal> resultMap = new HashMap<>();
        resultMap.put("checkAmt", BigDecimal.ZERO);
        PcxBillExpLedgerQO pcxBill = this.setExpLedgerParam(qo);
        BigDecimal checkAmt = pcxBillDao.selectCheckAmtCount(pcxBill);
        if (Objects.nonNull(checkAmt)) {
            resultMap.put("checkAmt", checkAmt);
        }
        // 返回结果对象并设置数据
        return result.setData(resultMap);
    }

    /*****
     * 获取终审的单据
     * @param qo
     * @return CheckMsg<Map < String, Object>>
     *    返回值说明：   apply - 是否需要申请单
     *                 applyCtrlCode - 申请单控制代码
     *                 applyData - 申请单数据
     *                 loan - 是否需要借款单
     *                 loanData - 借款单数据
     */
    @Override
    public CheckMsg<Map<String, Object>> getFinalBill(PcxBillQO qo) {
        // 初始化结果对象，默认设置为成功状态
        CheckMsg<Map<String, Object>> result = CheckMsg.success();
        // 验证必填字段是否符合要求
        CheckMsg<?> validationMsg = validateRequiredFields(qo);
        if (!validationMsg.isSuccess()) {
            return result.setSuccess(false).setMsgInfo(validationMsg.getMsgInfo());
        }
        // 初始化结果 Map，用于存储最终返回的数据
        Map<String, Object> resultMap = initResultMap();
        // 处理借款项逻辑，将相关信息存入 resultMap
        handleLoanItems(qo, resultMap);
        // 判断功能代码是否为 "报销"
        if (BillFuncCodeEnum.EXPENSE.getCode().equals(qo.getBillFuncCode())) {
            // 根据事项代码查询费用类型列表
            List<PcxBasExpType> expTypes = selectByItemCode(qo);
            if (CollectionUtils.isEmpty(expTypes)) {
                // 如果未查询到费用类型 - 默认false
                resultMap.put(BillFuncCodeEnum.APPLY.getCode(), PubConstant.LOGIC_FALSE);
                // 判断是否为通用事项
                if (StringUtil.isNotBlank(qo.getItemCode()) && PcxConstant.UNIVERSAL_ITEM_CODE.equals(qo.getItemCode())) {
                    PaOptionQO paOptionQO = new PaOptionQO();
                    paOptionQO.setAgyCode(qo.getAgyCode());
                    paOptionQO.setMofDivCode(qo.getMofDivCode());
                    paOptionQO.setFiscal(Integer.parseInt(qo.getFiscal()));
                    String optCode = bussinessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.UNIVERSAL_APPLY.getOptCode());
                    if (StringUtil.isNotBlank(optCode) && PubConstant.STR_LOGIC_TRUE.equals(optCode)) {
                        resultMap.put(BillFuncCodeEnum.APPLY.getCode(), PubConstant.LOGIC_TRUE);
                    }
                }
                resultMap.put("applyCtrlCode", ApplyControlEnum.APPLY_NO.getCode());
                processApprovedBills(qo, resultMap, false);
                return result.setData(resultMap);
            }
            //检查费用类型中是否需要计划
            boolean isNeedPlan = expTypes.stream().anyMatch(item ->
                    PubConstant.LOGIC_TRUE == item.getIsNeedPlan()
            );
            //默认为不需要计划
            resultMap.put("isNeedPlan", PubConstant.LOGIC_FALSE);
            if (isNeedPlan) {
                resultMap.put("isNeedPlan", PubConstant.LOGIC_TRUE);
            }

            // 检查费用类型中是否包含差旅费用
            boolean isHasTravel = expTypes.stream().anyMatch(item ->
                    StringUtil.isNotBlank(item.getExpenseCode()) &&
                            LoanEnums.TRAVEL_EXP_CODE.equals(item.getExpenseCode())
            );
            // 筛选需要申请的费用类型，并按申请控制代码进行分组
            Map<String, List<PcxBasExpType>> needApplyExp = expTypes.stream()
                    .filter(item -> PubConstant.LOGIC_TRUE == item.getIsNeedApply())
                    .collect(Collectors.groupingBy(PcxBasExpType::getApplyCtrlCode));
            // 处理申请控制相关逻辑
            processApplyControl(resultMap, needApplyExp);
            // 处理已批准的单据，并将结果存入 resultMap
            processApprovedBills(qo, resultMap, isHasTravel);
            return result.setData(resultMap);
        } else if (BillFuncCodeEnum.LOAN.getCode().equals(qo.getBillFuncCode())) {
            // 判断功能代码是否为借款
            // 获取最终的借款单据列表
            List<PcxMainBillVO> finalLoanBill = getFinalLoanBill(qo);
            // 将贷款单据数据存入 resultMap
            resultMap.put(BillFuncCodeEnum.APPLY.getCode() + "Data", finalLoanBill);
            return result.setData(resultMap);
        }
        // 返回结果对象并设置数据
        return result.setData(resultMap);
    }

    private List<PcxMainBillVO> getFinalLoanBill(PcxBillQO qo) {
        List<PcxBill> pcxBills = getApprovedBills(qo, Collections.singletonList(BillFuncCodeEnum.APPLY.getCode()));
        List<PcxMainBillVO> applyBills = new ArrayList<>();
        for (PcxBill pcxBill : pcxBills) {
            if (LoanEnums.LoanValsetEnum.LOAN_TRAVEL.getCode().equals(qo.getItemCode())) {
                if (Arrays.asList(StringUtil.getStringValue(pcxBill.getExpenseCodes()).split(",")).contains(LoanEnums.TRAVEL_EXP_CODE)) {
                    PcxMainBillVO pcxMainBillVO = new PcxMainBillVO();
                    BeanUtils.copyProperties(pcxBill, pcxMainBillVO);
                    applyBills.add(pcxMainBillVO);
                }
            } else {
                PcxMainBillVO pcxMainBillVO = new PcxMainBillVO();
                BeanUtils.copyProperties(pcxBill, pcxMainBillVO);
                applyBills.add(pcxMainBillVO);
            }
        }
        return applyBills;
    }

    // 处理申请控制代码
    private void processApplyControl(Map<String, Object> resultMap, Map<String, List<PcxBasExpType>> needApplyExp) {
        if (ObjectUtils.isEmpty(needApplyExp)) {
            resultMap.put(BillFuncCodeEnum.APPLY.getCode(), PubConstant.LOGIC_FALSE);
        } else {
            String applyCtrlCode = needApplyExp.size() > 1
                    ? ApplyControlEnum.APPLY_NO.getCode()
                    : needApplyExp.keySet().iterator().next();
            resultMap.put("applyCtrlCode", applyCtrlCode);
        }
    }

    // 处理借款单
    private void handleLoanItems(PcxBillQO qo, Map<String, Object> resultMap) {
        List<BasItemVO> loanItems = getLoanItems(qo);
        resultMap.put(BillFuncCodeEnum.LOAN.getCode(),
                CollectionUtils.isEmpty(loanItems) ? PubConstant.LOGIC_FALSE : PubConstant.LOGIC_TRUE);
    }

    // 处理已审批的单据并分类
    private void processApprovedBills(PcxBillQO qo, Map<String, Object> resultMap, boolean isHasTravel) {
        List<PcxBill> pcxBills = getApprovedBills(qo, Arrays.asList(BillFuncCodeEnum.APPLY.getCode(), BillFuncCodeEnum.LOAN.getCode()));
        if (CollectionUtils.isEmpty(pcxBills)) {
            return;
        }
        // 查询已审批的借款单据是否存在关联单，然后用useAmt判断是否还有未还款金额，如果有则返回
        List<String> loanBillIds = pcxBills.stream().map(PcxBill::getId).collect(Collectors.toList());
        List<PcxBillRelation> pcxBillRelations = pcxBillRelationDao.selectByRelBillIds(loanBillIds);
        Map<String, List<PcxBillRelation>> groupedByRelBillId = pcxBillRelations
                .stream()
                .collect(Collectors.groupingBy(PcxBillRelation::getRelBillId));
        Map<String, BigDecimal> billAmtMap = new HashMap<>();
        for (Map.Entry<String, List<PcxBillRelation>> entry : groupedByRelBillId.entrySet()) {
            String key = entry.getKey();
            List<PcxBillRelation> billData = entry.getValue();
            BigDecimal countUseAmt = billData.stream().filter(item ->
                            BillFuncCodeEnum.EXPENSE.getCode().equals(item.getBillFuncCode())
                                    && BillFuncCodeEnum.LOAN.getCode().equals(item.getRelBillFuncCode()))
                    .map(PcxBillRelation::getUsedAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            billAmtMap.put(key, countUseAmt);
        }
        List<PcxMainBillVO> applyBills = new ArrayList<>();
        List<PcxMainBillVO> loanBills = new ArrayList<>();
        for (PcxBill pcxBill : pcxBills) {
            if (isApplyBill(qo, pcxBill)) {
                // 报销获取借借款单数据
                PcxMainBillVO pcxMainBillVO = new PcxMainBillVO();
                BeanUtils.copyProperties(pcxBill, pcxMainBillVO);
                applyBills.add(pcxMainBillVO);
            } else if (BillFuncCodeEnum.LOAN.getCode().equals(pcxBill.getBillFuncCode())) {
                if (billAmtMap.get(pcxBill.getId()) != null && pcxBill.getCheckAmt().compareTo(billAmtMap.get(pcxBill.getId())) <= 0) {
                    continue;
                }
                // 报销获取借款单数据
                // 当事项没有差旅的时候，借款不需要展示差旅借款
                if (!isHasTravel && LoanEnums.LoanValsetEnum.LOAN_TRAVEL.getCode().equals(pcxBill.getItemCode())) {
                    continue;
                }
                if (!BillPayStatusEnum.PAID.getCode().equals(pcxBill.getPayStatus())) {
                    continue;
                }
                PcxMainBillVO pcxMainBillVO = new PcxMainBillVO();
                BeanUtils.copyProperties(pcxBill, pcxMainBillVO);
                BigDecimal unRepayAmt = pcxMainBillVO.getCheckAmt().subtract(pcxMainBillVO.getLoanAmt());
                if (unRepayAmt.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                pcxMainBillVO.setUnRepayAmt(unRepayAmt);
                loanBills.add(pcxMainBillVO);
            }
        }
        resultMap.put(BillFuncCodeEnum.APPLY.getCode() + "Data", applyBills);
        resultMap.put(BillFuncCodeEnum.LOAN.getCode() + "Data", loanBills);
    }

    // 判断是否为报销获取申请单
    private boolean isApplyBill(PcxBillQO qo, PcxBill pcxBill) {
        return BillFuncCodeEnum.APPLY.getCode().equals(pcxBill.getBillFuncCode())
                && pcxBill.getItemCode().equals(qo.getItemCode())
                && BillFuncCodeEnum.EXPENSE.getCode().equals(qo.getBillFuncCode());
    }

    private List<BasItemVO> getLoanItems(PcxBillQO qo) {
        PcxBasItemQO pcxBasItemQO = new PcxBasItemQO();
        pcxBasItemQO.setBilltypeCode(BillFuncCodeEnum.LOAN.getCode());
        pcxBasItemQO.setFiscal(qo.getFiscal());
        pcxBasItemQO.setAgyCode(qo.getAgyCode());
        pcxBasItemQO.setMofDivCode(qo.getMofDivCode());
        pcxBasItemQO.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
        pcxBasItemQO.setUserCode(qo.getUserCode());
        List<BasItemVO> loanItems = pcxBasItemService.getOwnItem(pcxBasItemQO);
        return loanItems;
    }

    private Map<String, Object> initResultMap() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(BillFuncCodeEnum.APPLY.getCode(), PubConstant.LOGIC_TRUE);
        resultMap.put("applyCtrlCode", ApplyControlEnum.APPLY_BEFORE.getCode());
        resultMap.put(BillFuncCodeEnum.APPLY.getCode() + "Data", new ArrayList<>());
        resultMap.put(BillFuncCodeEnum.LOAN.getCode(), PubConstant.LOGIC_TRUE);
        resultMap.put(BillFuncCodeEnum.LOAN.getCode() + "Data", new ArrayList<>());
        return resultMap;
    }

    /**
     * 根据事项类型查询费用类型
     *
     * @param qo
     * @return
     */
    private List<PcxBasExpType> selectByItemCode(PcxBillQO qo) {
        PcxBasExpTypeQO param = new PcxBasExpTypeQO();
        param.setFiscal(qo.getFiscal());
        param.setMofDivCode(qo.getMofDivCode());
        param.setAgyCode(qo.getAgyCode());
        param.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        param.setItemCode(qo.getItemCode());
        List<PcxBasExpType> pcxBasExpTypeList = pcxBasExpTypeService.selectByItemCode(param);
        return pcxBasExpTypeList;
    }

    /**
     * 获取审批通过且未结项的申请单据
     *
     * @param qo
     * @return
     */
    private List<PcxBill> getApprovedBills(PcxBillQO qo, List<String> billFuncCodes) {
        PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
        pcxMadBaseQO.setFiscal(qo.getFiscal());
        pcxMadBaseQO.setMofDivCode(qo.getMofDivCode());
        pcxMadBaseQO.setAgyCode(qo.getAgyCode());
        pcxMadBaseQO.setUserCode(qo.getUserCode());
        pcxMadBaseQO.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        List<MadEmployeeDTO> madEmployeeDTOS = pcxMadEmployeeService.select(pcxMadBaseQO);
        if (CollectionUtils.isEmpty(madEmployeeDTOS)) {
            return new ArrayList<>();
        }
        MadEmployeeDTO madEmployeeDTO = madEmployeeDTOS.get(0);
        PcxBill pcxBill = new PcxBill();
        pcxBill.setMofDivCode(qo.getMofDivCode());
        pcxBill.setAgyCode(qo.getAgyCode());
        pcxBill.setFiscal(qo.getFiscal());
        pcxBill.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        pcxBill.setBillFuncCodes(billFuncCodes);
        pcxBill.setCreator(qo.getUserCode());
        pcxBill.setClaimantCode(madEmployeeDTO.getEmployeeCode());
        pcxBill.setApproveStatus(BillApproveStatusEnum.APPROVED.getCode());
        pcxBill.setCompletedStatus(BillCompletedStatusEnum.PENDING.getCode());
        return billMainService.selectMainBill(pcxBill);
    }

    @Override
    public CheckMsg getEnableBillType(PcxMadBaseQO qo) {
        // 验证必填字段
        if (StringUtil.isEmpty(qo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getFiscal())) {
            return CheckMsg.fail("年度不能为空");
        }
        // 初始化结果映射
        HashMap<String, Integer> result = new HashMap<>();
        // 准备通用查询对象
        PcxBasItemQO basItemQuery = createBasItemQuery(qo);
        // 检查申请单据类型
        checkApplyBillType(basItemQuery, result);
        // 检查借款单据类型
        checkLoanBillType(qo, basItemQuery, result);

        return CheckMsg.success().setData(result);
    }

    /**
     * 创建基础项目查询对象
     */
    private PcxBasItemQO createBasItemQuery(PcxMadBaseQO qo) {
        PcxBasItemQO query = new PcxBasItemQO();
        query.setFiscal(qo.getFiscal());        // 设置财政年度
        query.setAgyCode(qo.getAgyCode());      // 设置单位编码
        query.setMofDivCode(qo.getMofDivCode()); // 设置区划编码
        query.setUserCode(qo.getUserCode());    // 设置用户编码
        return query;
    }

    /**
     * 检查申请单据类型可用性
     */
    private void checkApplyBillType(PcxBasItemQO query, HashMap<String, Integer> result) {
        boolean approvalEnabled = this.applyEnabled(Integer.valueOf(query.getFiscal()), query.getAgyCode(), query.getMofDivCode());
        if (!approvalEnabled) {
            result.put(BillFuncCodeEnum.APPLY.getCode(), PubConstant.LOGIC_FALSE);
            return;
        }
        query.setBilltypeCode(BillFuncCodeEnum.APPLY.getCode()); // 设置项目类型为申请
        List<BasItemVO> applyItems = pcxBasItemService.getOwnItem(query);
        result.put(BillFuncCodeEnum.APPLY.getCode(),
                CollectionUtils.isEmpty(applyItems) ? PubConstant.LOGIC_FALSE : PubConstant.LOGIC_TRUE);
    }

    /**
     * 检查借款单据类型可用性
     */
    private void checkLoanBillType(PcxMadBaseQO qo, PcxBasItemQO query, HashMap<String, Integer> result) {
        // 创建业务选项查询对象
        PaOptionQO optionQuery = new PaOptionQO();
        optionQuery.setFiscal(Integer.parseInt(qo.getFiscal()));    // 设置财政年度
        optionQuery.setMofDivCode(qo.getMofDivCode());             // 设置区划编码
        optionQuery.setAgyCode(qo.getAgyCode());                   // 设置单位编码
        // 获取借款业务规则选项值
        String loanOption = bussinessRuleOptionService.getOptionValueByOptionCode(
                optionQuery, BusinessRuleEnum.BusinessOptionEnum.LOAN.getOptCode());
        if (StringUtil.isEmpty(loanOption) || PubConstant.STR_LOGIC_FALSE.equals(loanOption)) {
            result.put(BillFuncCodeEnum.LOAN.getCode(), PubConstant.LOGIC_FALSE);
        } else {
            query.setBilltypeCode(BillFuncCodeEnum.LOAN.getCode()); // 设置项目类型为借款
            List<BasItemVO> loanItems = pcxBasItemService.getOwnItem(query);
            result.put(BillFuncCodeEnum.LOAN.getCode(),
                    CollectionUtils.isEmpty(loanItems) ? PubConstant.LOGIC_FALSE : PubConstant.LOGIC_TRUE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<Void> deleteData(PcxBillQO qo) {
        PcxBill pcxBill = new PcxBill();
        BeanUtils.copyProperties(qo, pcxBill);
        pcxBill.setId(qo.getBillId());
        pcxBill = billMainService.view(pcxBill.getId());
        Assert.state(!Objects.isNull(pcxBill), "未找到相关单据");
        billMainService.delete(pcxBill);
        processService.deleteProcessInstance(pcxBill);
        BuildExpRelQO relQO = new BuildExpRelQO();
        relQO.setAgencyCode(pcxBill.getAgyCode());
        relQO.setTargetBillId(pcxBill.getId());
        relQO.setTargetBillNo(pcxBill.getBillNo());
        relQO.setTargetFiscalYear(Integer.valueOf(pcxBill.getFiscal()));
        relQO.setMofDivCode(pcxBill.getMofDivCode());
        relQO.setUserCode(pcxBill.getClaimantCode());
        relQO.setUserName(pcxBill.getClaimantName());
        try {
            EcsServiceMsg ecsServiceMsg = ecsBillExternalService.deleteExpRel(relQO);
            log.info("ecs删除关联关系结果:{}", JSON.toJSONString(ecsServiceMsg));
        } catch (Exception e) {
            log.error("ecs删除关联关系失败，参数:{}", qo, e);
            throw new RuntimeException(e);
        }
        try {
            pcxBalanceCtrlService.deleteCtrl(pcxBill);
        } catch (Exception e) {
            log.error("指标删除关联关系结果，参数:{}", qo, e);
            throw new RuntimeException(e);
        }
        CheckMsg<Void> success = CheckMsg.success();
        success.setMsgInfo("单据删除成功");
        return success;
    }


    @Override
    public CheckMsg<Map<String, List<PcxBillSettlement>>> selectDefaultSettlement(DefaultSettlementQO expenseQO) {
        //查询启用的结算方式
        PcxSettlementRule qo = new PcxSettlementRule();
        qo.setAgyCode(expenseQO.getAgyCode());
        qo.setFiscal(expenseQO.getFiscal());
        qo.setMofDivCode(expenseQO.getMofDivCode());
        List<BaseDataVo> baseDataVos = pcxSettlementRuleService.selectEnable(qo);
        if (CollectionUtils.isEmpty(baseDataVos)) {
            return CheckMsg.fail("当前单位未配置结算方式");
        }
        //查询所有的单据的明细以及费用信息
        PcxBill pcxBill = billMainService.view(expenseQO.getBillId());
        if (Objects.isNull(pcxBill)) return CheckMsg.fail("未找到相关单据");
        List<PcxBillExpBase> expenseList = billExpenseCommonService.getExpenseList(pcxBill);
        //此处只处理差旅的默认的赋值，其余费用不做处理
        List<PcxBillSettlement> settlementList = new ArrayList<>();
        for (PcxBillExpBase e : expenseList) {
            if (PcxConstant.TRAVEL_EXPENSE_30211.equals(e.getExpenseCode())) {
                //加载差旅费用的明细
                BillExpenseDetailService4Travel detailBean = getDetailBean(BillExpenseDetailService4Travel.class);
                List<PcxBillExpDetailTravel> detailTravels = detailBean.listByExpenseCode(e.getExpenseCode(), pcxBill);
                detailTravels.forEach(detailTravel -> {
                    //查询当前费用明细默认的结算方式
                    PcxSettlementRuleExpQO settlementRuleExpQO = new PcxSettlementRuleExpQO();
                    settlementRuleExpQO.setExpenseCode(detailTravel.getExpDetailCode());
                    settlementRuleExpQO.setAgyCode(detailTravel.getAgyCode());
                    settlementRuleExpQO.setFiscal(detailTravel.getFiscal());
                    settlementRuleExpQO.setMofDivCode(detailTravel.getMofDivCode());
                    Response<PcxSettlementRuleExpVO> pcxSettlementRuleExpVOResponse = pcxSettlementRuleExpService.selectByExpenseCode(settlementRuleExpQO);
                    if (pcxSettlementRuleExpVOResponse.isSuccess()) {
                        //获取当前人员信息
                        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
                        pcxBaseDTO.setAgyCode(detailTravel.getAgyCode());
                        pcxBaseDTO.setFiscal(detailTravel.getFiscal());
                        pcxBaseDTO.setMofDivCode(detailTravel.getMofDivCode());

                        CheckMsg<List<MadEmployeeCardDTO>> listCheckMsg = madEmployeeCardExternalService.selectEmployeeCardByUniqueKey(pcxBaseDTO.getAgyCode(), pcxBaseDTO.getFiscal(), pcxBaseDTO.getMofDivCode(), detailTravel.getEmpCode(), null, null);
                        if (listCheckMsg.isSuccess() && CollectionUtils.isNotEmpty(listCheckMsg.getData())) {
                            PcxSettlementRuleExpVO data = pcxSettlementRuleExpVOResponse.getData();
                            PcxBillSettlement settlement = new PcxBillSettlement();
                            settlement.setSettlementType(data.getDefaultSettlement());
                            settlement.setSettlementName(data.getDefaultSettlementName());
                            settlement.setCheckAmt(detailTravel.getCheckAmt());
                            settlement.setInputAmt(detailTravel.getInputAmt());
                            MadEmployeeCardDTO madEmployeeDTO = listCheckMsg.getData().stream()
                                    .filter(item->StringUtil.isNotBlank(item.getAccountNo())
                                            &&StringUtil.isNotBlank(item.getAccountName())
                                            &&StringUtil.isNotBlank(item.getBankNodeName()))
                                    .findFirst().get();
                            if (Objects.nonNull(madEmployeeDTO)){
                                settlement.setPayeeBankName(madEmployeeDTO.getBankNodeName());
                                settlement.setPayeeAccNo(madEmployeeDTO.getAccountNo());
                                settlement.setPayeeAccName(madEmployeeDTO.getAccountName());
                                settlementList.add(settlement);
                            }
                        }
                    }
                });
            }

            // 针对会议、培训 费用类型涉及到对劳务人员费用结算方式（会处理主单据金额噢）.
            if (PcxConstant.MEETING_EXPENSE_30215.equals(e.getExpenseCode()) || PcxConstant.TRAINING_EXPENSE_30216.equals(e.getExpenseCode())) {
                if (PcxConstant.MEETING_EXPENSE_30215.equals(e.getExpenseCode())) {
                    BillExpenseDetailService4Metting detailBean = getDetailBean(BillExpenseDetailService4Metting.class);
                    List<PcxBillExpDetailMeeting> detailMeetings = detailBean.listByExpenseCode(e.getExpenseCode(), pcxBill);
                    // 循环内部是否包含劳务费，处理生成明细的结算方式数据
                    if (CollectionUtils.isNotEmpty(detailMeetings)) {
                        // 筛选会议费用明细中的劳务费明细（3021503）且包含劳务人员编码的记录
                        List<PcxBillExpDetailMeeting> labourExpenses = detailMeetings.stream()
                                .filter(detailMeeting -> Objects.equals(PcxConstant.MEETING_EXPENSE_3021503, detailMeeting.getExpDetailCode())
                                        && StringUtil.isNotEmpty(detailMeeting.getLabourCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(labourExpenses)) {
                            //得到劳务人员信息id
                            List<String> labourInfoIds = labourExpenses.stream()
                                    .map(PcxBillExpDetailMeeting::getLabourCode)
                                    .distinct()
                                    .collect(Collectors.toList());
                            List<PcxBillExpDetailLabour> pcxBillExpDetailLabours = pcxBillExpDetailLabourDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                                    .in(PcxBillExpDetailLabour::getLabourInfoId, labourInfoIds)
                                    .eq(PcxBillExpDetailLabour::getBillId, pcxBill.getId()));
                            //基于劳务人员id转成map。避免value重复的情况 覆盖
                            Map<String, PcxBillExpDetailLabour> pcxBillExpDetailLabourMap = pcxBillExpDetailLabours.stream()
                                    .collect(Collectors.toMap(PcxBillExpDetailLabour::getLabourInfoId, m -> m));

                            for (PcxBillExpDetailMeeting detailMeeting : labourExpenses) {
                                PcxBillExpDetailLabour pcxBillExpDetailLabour = pcxBillExpDetailLabourMap.get(detailMeeting.getLabourCode());
                                if(pcxBillExpDetailLabour != null){
                                    //减去主单金额：
                                    pcxBill.setInputAmt(pcxBill.getInputAmt().subtract(detailMeeting.getInputAmt()));
                                    pcxBill.setCheckAmt(pcxBill.getCheckAmt().subtract(detailMeeting.getCheckAmt()));
                                }
                            }
                        }

                    }
                }else{
                    BillExpenseDetailService4Training detailBean = getDetailBean(BillExpenseDetailService4Training.class);
                    List<PcxBillExpDetailTraining> detailMeetings = detailBean.listByExpenseCode(e.getExpenseCode(), pcxBill);
                    // 循环内部是否包含劳务费，处理生成明细的结算方式数据
                    if (CollectionUtils.isNotEmpty(detailMeetings)) {
                        // 筛选会议费用明细中的劳务费明细（3021503）且包含劳务人员编码的记录
                        List<PcxBillExpDetailTraining> labourExpenses = detailMeetings.stream()
                                .filter(detailMeeting -> Objects.equals(PcxConstant.TRAINING_EXPENSE_302160201, detailMeeting.getExpDetailCode())
                                        && StringUtil.isNotEmpty(detailMeeting.getLabourCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(labourExpenses)) {
                            //得到劳务人员信息id
                            List<String> labourInfoIds = labourExpenses.stream()
                                    .map(PcxBillExpDetailTraining::getLabourCode)
                                    .distinct()
                                    .collect(Collectors.toList());
                            List<PcxBillExpDetailLabour> pcxBillExpDetailLabours = pcxBillExpDetailLabourDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                                    .in(PcxBillExpDetailLabour::getLabourInfoId, labourInfoIds)
                                    .eq(PcxBillExpDetailLabour::getBillId, pcxBill.getId()));
                            //基于劳务人员id转成map。避免value重复的情况 覆盖
                            Map<String, PcxBillExpDetailLabour> pcxBillExpDetailLabourMap = pcxBillExpDetailLabours.stream()
                                    .collect(Collectors.toMap(PcxBillExpDetailLabour::getLabourInfoId, m -> m));
                            System.out.println("pcxBillExpDetailLabourMap");
                            for (PcxBillExpDetailTraining detailTraining : labourExpenses) {
                                PcxBillExpDetailLabour pcxBillExpDetailLabour = pcxBillExpDetailLabourMap.get(detailTraining.getLabourCode());
                                if(pcxBillExpDetailLabour != null){
                                    //减去主单金额：
                                    pcxBill.setInputAmt(pcxBill.getInputAmt().subtract(detailTraining.getInputAmt()));
                                    pcxBill.setCheckAmt(pcxBill.getCheckAmt().subtract(detailTraining.getCheckAmt()));
                                }
                            }
                        }

                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(settlementList)) {
            PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
            pcxBaseDTO.setAgyCode(pcxBill.getAgyCode());
            pcxBaseDTO.setFiscal(pcxBill.getFiscal());
            pcxBaseDTO.setMofDivCode(pcxBill.getMofDivCode());
            CheckMsg<List<MadEmployeeCardDTO>> listCheckMsg = madEmployeeCardExternalService.selectEmployeeCardByUniqueKey(pcxBaseDTO.getAgyCode(), pcxBaseDTO.getFiscal(), pcxBaseDTO.getMofDivCode(), pcxBill.getClaimantCode(), null, null);
            if (listCheckMsg.isSuccess() && CollectionUtils.isNotEmpty(listCheckMsg.getData())) {

                PcxBillSettlement defaultSettlement = new PcxBillSettlement();
                defaultSettlement.setSettlementType(FormSettingEnums.FormTypeEnum.SETTLE_TRANSFER.getCode());
                defaultSettlement.setSettlementName(FormSettingEnums.FormTypeEnum.SETTLE_TRANSFER.getName());
                defaultSettlement.setCheckAmt(pcxBill.getCheckAmt().subtract(pcxBill.getLoanAmt()));
                defaultSettlement.setInputAmt(pcxBill.getInputAmt().subtract(pcxBill.getLoanAmt()));
                MadEmployeeCardDTO madEmployeeDTO = listCheckMsg.getData().get(0);
                defaultSettlement.setPayeeBankName(madEmployeeDTO.getBankNodeName());
                defaultSettlement.setPayeeAccNo(madEmployeeDTO.getAccountNo());
                defaultSettlement.setPayeeAccName(madEmployeeDTO.getAccountName());
                defaultSettlement.setPayeeAccCode(madEmployeeDTO.getEmployeeCode());
                if(StringUtil.isNotEmpty(defaultSettlement.getPayeeAccNo())&&StringUtil.isNotEmpty(defaultSettlement.getPayeeAccName())&&StringUtil.isNotEmpty(defaultSettlement.getPayeeBankName())){
                    settlementList.add(defaultSettlement);
                }
            }

        }
        Map<String, List<PcxBillSettlement>> collectMap = settlementList.stream().collect(Collectors.groupingBy(PcxBillSettlement::getSettlementType));
        return CheckMsg.success(collectMap);
    }



    @Override
    public CheckMsg<Map<String, List<PcxBillSettlement>>> defaultLabourSettlement(DefaultSettlementQO expenseQO) {
        //查询启用的结算方式
        PcxSettlementRule qo = new PcxSettlementRule();
        qo.setAgyCode(expenseQO.getAgyCode());
        qo.setFiscal(expenseQO.getFiscal());
        qo.setMofDivCode(expenseQO.getMofDivCode());
        List<BaseDataVo> baseDataVos = pcxSettlementRuleService.selectEnable(qo);
        if (CollectionUtils.isEmpty(baseDataVos)) {
            return CheckMsg.fail("当前单位未配置结算方式");
        }
        //查询所有的单据的明细以及费用信息
        PcxBill pcxBill = billMainService.view(expenseQO.getBillId());
        if (Objects.isNull(pcxBill)) return CheckMsg.fail("未找到相关单据");
        List<PcxBillExpBase> expenseList = billExpenseCommonService.getExpenseList(pcxBill);
        //此处只处理差旅的默认的赋值，其余费用不做处理
        List<PcxBillSettlement> settlementList = new ArrayList<>();
        for (PcxBillExpBase e : expenseList) {
            // 针对会议、培训 费用类型涉及到对劳务人员费用结算方式（会处理主单据金额噢）.
            if (PcxConstant.MEETING_EXPENSE_30215.equals(e.getExpenseCode()) || PcxConstant.TRAINING_EXPENSE_30216.equals(e.getExpenseCode())) {
                if (PcxConstant.MEETING_EXPENSE_30215.equals(e.getExpenseCode())) {
                    BillExpenseDetailService4Metting detailBean = getDetailBean(BillExpenseDetailService4Metting.class);
                    List<PcxBillExpDetailMeeting> detailMeetings = detailBean.listByExpenseCode(e.getExpenseCode(), pcxBill);
                    // 循环内部是否包含劳务费，处理生成明细的结算方式数据
                    if (CollectionUtils.isNotEmpty(detailMeetings)) {
                        // 筛选会议费用明细中的劳务费明细（3021503）且包含劳务人员编码的记录
                        List<PcxBillExpDetailMeeting> labourExpenses = detailMeetings.stream()
                                .filter(detailMeeting -> Objects.equals(PcxConstant.MEETING_EXPENSE_3021503, detailMeeting.getExpDetailCode())
                                        && StringUtil.isNotEmpty(detailMeeting.getLabourCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(labourExpenses)) {
                            //得到劳务人员信息id
                            List<String> labourInfoIds = labourExpenses.stream()
                                    .map(PcxBillExpDetailMeeting::getLabourCode)
                                    .distinct()
                                    .collect(Collectors.toList());
                            List<PcxBillExpDetailLabour> pcxBillExpDetailLabours = pcxBillExpDetailLabourDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                                    .in(PcxBillExpDetailLabour::getLabourInfoId, labourInfoIds)
                                    .eq(PcxBillExpDetailLabour::getBillId, pcxBill.getId()));
                            List<PcxLabourInfo> pcxLabourInfos = pcxLabourInfoDao.selectList(new LambdaQueryWrapper<PcxLabourInfo>()
                                    .in(PcxLabourInfo::getId, labourInfoIds));
                            //基于劳务人员id转成map。避免value重复的情况 覆盖
                            Map<String, PcxBillExpDetailLabour> pcxBillExpDetailLabourMap = pcxBillExpDetailLabours.stream()
                                    .collect(Collectors.toMap(PcxBillExpDetailLabour::getLabourInfoId, m -> m));
                            Map<String, PcxLabourInfo> collect = pcxLabourInfos.stream()
                                    .collect(Collectors.toMap(PcxLabourInfo::getId, m -> m));

                            for (PcxBillExpDetailMeeting detailMeeting : labourExpenses) {
                                PcxBillExpDetailLabour pcxBillExpDetailLabour = pcxBillExpDetailLabourMap.get(detailMeeting.getLabourCode());
                                if(pcxBillExpDetailLabour != null){
                                    PcxBillSettlement settlement = new PcxBillSettlement();
                                    settlement.setSettlementType(pcxBillExpDetailLabour.getSettlementType());
                                    settlement.setSettlementName(pcxBillExpDetailLabour.getSettlementName());
                                    // 设置检查金额：如果税金为空或0则使用原金额，否则减去税金
                                    BigDecimal taxAmt = pcxBillExpDetailLabour.getTaxAmt();
                                    BigDecimal checkAmt = detailMeeting.getCheckAmt();
                                    if (taxAmt == null || taxAmt.compareTo(BigDecimal.ZERO) == 0) {
                                        settlement.setCheckAmt(checkAmt);
                                    } else {
                                        settlement.setCheckAmt(checkAmt.subtract(taxAmt));
                                    }
                                    settlement.setInputAmt(detailMeeting.getInputAmt());
                                    settlement.setPayeeBankName(pcxBillExpDetailLabour.getBankNodeName());
                                    settlement.setPayeeAccNo(StringUtil.isEmpty(pcxBillExpDetailLabour.getAccountNo())
                                            ? pcxBillExpDetailLabour.getLabourInfoId() : pcxBillExpDetailLabour.getAccountNo());
                                    if(StringUtil.isEmpty(pcxBillExpDetailLabour.getAccountName())){
                                        PcxLabourInfo pcxLabourInfo = collect.get(pcxBillExpDetailLabour.getLabourInfoId());
                                        if(pcxLabourInfo != null){
                                            settlement.setPayeeAccName(pcxLabourInfo.getUserName());
                                        }
                                    }else {
                                        settlement.setPayeeAccName(pcxBillExpDetailLabour.getAccountName());
                                    }
                                    settlement.setIsLabour(PubConstant.STR_LOGIC_TRUE);
                                    settlement.setTaxAmt(Objects.isNull(pcxBillExpDetailLabour.getTaxAmt()) ? BigDecimal.ZERO : pcxBillExpDetailLabour.getTaxAmt());
                                    settlementList.add(settlement);
                                    //减去主单金额：
                                    pcxBill.setInputAmt(pcxBill.getInputAmt().subtract(detailMeeting.getInputAmt()));
                                    pcxBill.setCheckAmt(pcxBill.getCheckAmt().subtract(detailMeeting.getCheckAmt()));
                                }
                            }
                        }
                    }
                }else{
                    BillExpenseDetailService4Training detailBean = getDetailBean(BillExpenseDetailService4Training.class);
                    List<PcxBillExpDetailTraining> detailMeetings = detailBean.listByExpenseCode(e.getExpenseCode(), pcxBill);
                    // 循环内部是否包含劳务费，处理生成明细的结算方式数据
                    if (CollectionUtils.isNotEmpty(detailMeetings)) {
                        // 筛选会议费用明细中的劳务费明细（3021503）且包含劳务人员编码的记录
                        List<PcxBillExpDetailTraining> labourExpenses = detailMeetings.stream()
                                .filter(detailMeeting -> Objects.equals(PcxConstant.TRAINING_EXPENSE_302160201, detailMeeting.getExpDetailCode())
                                        && StringUtil.isNotEmpty(detailMeeting.getLabourCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(labourExpenses)) {
                            //得到劳务人员信息id
                            List<String> labourInfoIds = labourExpenses.stream()
                                    .map(PcxBillExpDetailTraining::getLabourCode)
                                    .distinct()
                                    .collect(Collectors.toList());
                            List<PcxBillExpDetailLabour> pcxBillExpDetailLabours = pcxBillExpDetailLabourDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                                    .in(PcxBillExpDetailLabour::getLabourInfoId, labourInfoIds)
                                    .eq(PcxBillExpDetailLabour::getBillId, pcxBill.getId()));
                            //基于劳务人员id转成map。避免value重复的情况 覆盖
                            Map<String, PcxBillExpDetailLabour> pcxBillExpDetailLabourMap = pcxBillExpDetailLabours.stream()
                                    .collect(Collectors.toMap(PcxBillExpDetailLabour::getLabourInfoId, m -> m));
                            List<PcxLabourInfo> pcxLabourInfos = pcxLabourInfoDao.selectList(new LambdaQueryWrapper<PcxLabourInfo>()
                                    .in(PcxLabourInfo::getId, labourInfoIds));
                            Map<String, PcxLabourInfo> collect = pcxLabourInfos.stream()
                                    .collect(Collectors.toMap(PcxLabourInfo::getId, m -> m));
                            for (PcxBillExpDetailTraining detailTraining : labourExpenses) {
                                PcxBillExpDetailLabour pcxBillExpDetailLabour = pcxBillExpDetailLabourMap.get(detailTraining.getLabourCode());
                                if(pcxBillExpDetailLabour != null){
                                    PcxBillSettlement settlement = new PcxBillSettlement();
                                    settlement.setSettlementType(pcxBillExpDetailLabour.getSettlementType());
                                    settlement.setSettlementName(pcxBillExpDetailLabour.getSettlementName());
                                    // 设置检查金额：如果税金为空或0则使用原金额，否则减去税金
                                    BigDecimal taxAmt = pcxBillExpDetailLabour.getTaxAmt();
                                    BigDecimal checkAmt = detailTraining.getCheckAmt();
                                    if (taxAmt == null || taxAmt.compareTo(BigDecimal.ZERO) == 0) {
                                        settlement.setCheckAmt(checkAmt);
                                    } else {
                                        settlement.setCheckAmt(checkAmt.subtract(taxAmt));
                                    }
                                    settlement.setInputAmt(detailTraining.getInputAmt());
                                    settlement.setPayeeBankName(pcxBillExpDetailLabour.getBankNodeName());
                                    settlement.setPayeeAccNo(StringUtil.isEmpty(pcxBillExpDetailLabour.getAccountNo())
                                            ? pcxBillExpDetailLabour.getLabourInfoId() : pcxBillExpDetailLabour.getAccountNo());
                                    if(StringUtil.isEmpty(pcxBillExpDetailLabour.getAccountName())){
                                        PcxLabourInfo pcxLabourInfo = collect.get(pcxBillExpDetailLabour.getLabourInfoId());
                                        if(pcxLabourInfo != null){
                                            settlement.setPayeeAccName(pcxLabourInfo.getUserName());
                                        }
                                    }else {
                                        settlement.setPayeeAccName(pcxBillExpDetailLabour.getAccountName());
                                    }
                                    settlement.setIsLabour(PubConstant.STR_LOGIC_TRUE);
                                    settlement.setTaxAmt(Objects.isNull(pcxBillExpDetailLabour.getTaxAmt()) ? BigDecimal.ZERO : pcxBillExpDetailLabour.getTaxAmt());
                                    settlementList.add(settlement);
                                    //减去主单金额：
                                    pcxBill.setInputAmt(pcxBill.getInputAmt().subtract(detailTraining.getInputAmt()));
                                    pcxBill.setCheckAmt(pcxBill.getCheckAmt().subtract(detailTraining.getCheckAmt()));
                                }
                            }
                        }

                    }
                }
            }
        }

        Map<String, List<PcxBillSettlement>> collectMap = settlementList.stream().collect(Collectors.groupingBy(PcxBillSettlement::getSettlementType));
        return CheckMsg.success(collectMap);
    }


    @Override
    public CheckMsg saveRel(PcxBillRelationQO qo) {
        // todo-20250107 - mashaojie 申请单不做自动结项，只在pc做手动结项操作
//        completedApply(qo);
        return pcxBillRelationService.save(qo);
    }

    /***
     *  申请单结项
     *  1. 是保险单关联的申请单需要走这个逻辑
     *  2. 将需要解绑的申请单还原
     *  3. 查询到当前要挂的申请单
     *    3.1 判断申请单的checkAmt 是0的时候走自动结项
     *    3.2 todo 申请单的checkAmt 不是0的时候
     */
    private void completedApply(PcxBillRelationQO qo) {
        if (!BillFuncCodeEnum.EXPENSE.getCode().equals(qo.getBillFuncCode()) || !BillFuncCodeEnum.APPLY.getCode().equals(qo.getRelBillFuncCode())) {
            return;
        }
        PcxBillRelationQO pcxBillRelationQO = new PcxBillRelationQO();
        pcxBillRelationQO.setBillId(qo.getBillId());
        pcxBillRelationQO.setBillFuncCode(BillFuncCodeEnum.EXPENSE.getCode());
        pcxBillRelationQO.setRelBillFuncCode(BillFuncCodeEnum.APPLY.getCode());
        pcxBillRelationQO.setMofDivCode(qo.getMofDivCode());
        pcxBillRelationQO.setAgyCode(qo.getAgyCode());
        pcxBillRelationQO.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
        pcxBillRelationQO.setFiscal(qo.getFiscal());
        PcxBillRelation pcxBillRelation = pcxBillRelationService.selectApplyRelByQO(pcxBillRelationQO);
        restoreCompletedApply(pcxBillRelation);
        // 查询申请单
        PcxBill pcxBill = new PcxBill();
        pcxBill.setId(qo.getRelBillId());
        pcxBill.setMofDivCode(qo.getMofDivCode());
        pcxBill.setAgyCode(qo.getAgyCode());
        pcxBill.setFiscal(qo.getFiscal());
        pcxBill.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
        Optional<PcxBill> applyBillOptional = billMainService.selectMainBill(pcxBill).stream().findFirst();
        if (!applyBillOptional.isPresent()) {
            return;
        }
        PcxBill applyBill = applyBillOptional.get();
        // 判断当checkAmt 是0的时候走自动结项
        if (applyBill.getCheckAmt().compareTo(BigDecimal.ZERO) == 0) {
            applyBill.setCompletedStatus(BillCompletedStatusEnum.COMPLETED.getCode());
            applyBill.setBillStatus(BillStatusEnum.COMPLETE.getCode());
            billMainService.updateCompletedStatus(applyBill);
        }
        billMainService.updateCompletedStatus(pcxBill);
    }

    private void restoreCompletedApply(PcxBillRelation pcxBillRelation) {
        if (ObjectUtils.isEmpty(pcxBillRelation)) {
            return;
        }
        String applyBillId = pcxBillRelation.getRelBillId();
        billMainService.selectMainBill(PcxBill.builder().id(applyBillId).build()).stream().findFirst().ifPresent(applyBill -> {
            applyBill.setCompletedStatus(BillCompletedStatusEnum.PENDING.getCode());
            applyBill.setBillStatus(BillStatusEnum.SUBMIT.getCode());
            billMainService.updateCompletedStatus(applyBill);
        });
    }

    @Override
    public CheckMsg<List<WitRuleResult>> auditRule(String billId, List<String> anchorPoints) {
        if (CollectionUtils.isEmpty(anchorPoints)) {
            return CheckMsg.success();
        }
        List<WitRuleResult> resultList = new ArrayList<>();
        CheckMsg<PcxBillVO> checkMsg = this.view(billId);
        if (!checkMsg.isSuccess() || checkMsg.getData() == null) {
            return CheckMsg.fail("获取单据信息失败");
        }
        PcxBillVO data = checkMsg.getData();
        PcxBill basicInfo = data.getBasicInfo();
        //加载单位信息
        PaOrgDTO paOrgDTO = witDataBuilder.buildPaOrgDTO(basicInfo);
        //加载申请人信息
        MadEmployeeDTO madEmployeeDTO = witDataBuilder.buildMadEmployeeDTO(basicInfo);
        BigDecimal yearSub = BigDecimal.ZERO;
        if (Objects.equals(basicInfo.getBizType(),ItemBizTypeEnum.TRAVEL.getCode())){
            yearSub = witDataBuilder.buildYearSub(madEmployeeDTO.getEmployeeCode());
        }
        //加载历史借款单
        PcxBill loanBill = witDataBuilder.buildBeforeLoan(basicInfo);
        //加载票信息
        List<WitEcsVO> ecsVOList = witDataBuilder.buildEcsVOList(data);
        //加载分摊部门信息
        List<PcxBillAmtApportionDepartment> apportionDepartments = pcxBillAmtApportionDepartmentDao.selectByBillId(basicInfo.getId());
        //查询当前报销人是否是部门领导人
        List<MadDepartmentDTO> madDepartmentDTOS = witDataBuilder.buildLeaderDepartment(basicInfo);
        //开始按块稽核
        BigDecimal finalYearSub = yearSub;
        anchorPoints.forEach(ruleReference -> {
            AuditDTO.ReqMeta reqMeta = new AuditDTO.ReqMeta();
            reqMeta.setBillId(billId);
            reqMeta.setFiscal(data.getBasicInfo().getFiscal());
            reqMeta.setCreator(data.getBasicInfo().getCreator());
            reqMeta.setCreatorName(data.getBasicInfo().getCreatorName());
            reqMeta.setRefBlock(ruleReference);

            AuditDTO auditDTO = new AuditDTO();
            auditDTO.setReqMeta(reqMeta);
            auditDTO.setPcxBill(data.getBasicInfo());
            auditDTO.setExpenses(data.getSpecificity());
            auditDTO.setDetails(data.getExpenseDetail());
            auditDTO.setAttachs(data.getAttachList());
            auditDTO.setSettles(data.getSettlements());
            auditDTO.setBalances(data.getBudget());

            List<PcxBillRelationVO> relations = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(data.getLoan())) {
                relations.addAll(data.getLoan());
            }
            if (data.getApply() != null) {
                relations.add(data.getApply());
            }
            auditDTO.setRelations(relations);
            auditDTO.setYearSubAmt(finalYearSub);

            if (loanBill != null) {
                auditDTO.setBeforeLoan(loanBill);
            }
            if (paOrgDTO != null) {
                auditDTO.setPaOrg(paOrgDTO);
            }
            if (madEmployeeDTO != null) {
                auditDTO.setMadEmployeeDTO(madEmployeeDTO);
            }
            if (CollectionUtils.isNotEmpty(madDepartmentDTOS)){
                auditDTO.setLeaderDepartments(madDepartmentDTOS);
            }
            if (CollectionUtils.isNotEmpty(ecsVOList)) {
                auditDTO.setInvoices(ecsVOList);
            }

            if (CollectionUtils.isNotEmpty(apportionDepartments)){
                auditDTO.setApportionDepartments(apportionDepartments);
            }

            if (log.isDebugEnabled()) {
                log.debug("开始稽核规则 块信息{}请求参数{}", auditDTO.getReqMeta().getRefBlock(), JSONObject.toJSONString(auditDTO));
            }

            CheckMsg<WitRuleResult> checkMsg1 = witAuditRuleService.auditRule(auditDTO);
            if (checkMsg1.isSuccess() && checkMsg1.getData() != null) {
                resultList.add(checkMsg1.getData());
            }
        });

        return CheckMsg.success(resultList);
    }


    @Override
    public ResponseEntity<byte[]> downAllBillAttachZip(List<String> billIds) throws IOException {
        Set<String> attachIds = new HashSet<>();
        // 查询附件清单
        List<PcxBillAttachRelationVO> attachList = billExpenseCommonService.getAttachList(PcxBill.builder()
                .ids(billIds)
                .build());
        attachIds.addAll(attachList.stream().map(PcxBillAttachRelationVO::getAttachId).distinct().collect(Collectors.toList()));
        //报销单关联的票本身
        List<PcxExpDetailEcsRel> ecsRelList = pcxExpDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .in(PcxExpDetailEcsRel::getBillId, billIds));
        List<String> fileIds = ecsRelList.stream().filter(item -> StringUtil.isNotEmpty(item.getFileId())).map(item -> item.getFileId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fileIds)) {
            attachIds.addAll(fileIds);
        }
        // 票据关联附件
        // 费用关联附件
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .in(PcxBillExpAttachRel::getBillId, billIds));
        attachIds.addAll(attachRelList.stream().map(PcxBillExpAttachRel::getAttachId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(attachIds)) {
            return pcxFileService.downloadFilesAsZip(attachIds);
        }
        return ResponseEntity.notFound().build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg changeBalance(PcxBillQO qo, Boolean isValidate) {
        //指标经费来源处理
        if (StringUtil.isEmpty(qo.getBillId()) || Objects.nonNull(isValidate)) {
            return CheckMsg.fail("参数错误");
        }
        if (CollectionUtils.isEmpty(qo.getBudget())) {
            return CheckMsg.fail("经费来源不能为空");
        }
        PcxBill bill = billMainService.view(qo.getBillId());
        billExpenseCommonService.processBalance(qo.getBudget(), bill);
        if (!BillApproveStatusEnum.APPROVING.getCode().equals(bill.getApproveStatus())) {
            //指标预占
            return CheckMsg.success("修改成功");
        }
        if (Objects.nonNull(isValidate)) {
            pcxBalanceCtrlService.saveCtrl(bill, isValidate);
        }
        return CheckMsg.success("修改成功");
    }

    @Override
    public PcxBillBasicVO basicInfo(String billId) {
        PcxBill pcxBill = billMainService.view(billId);
        if (Objects.nonNull(pcxBill)) {
            PcxBillBasicVO pcxBillBasicVO = new PcxBillBasicVO();
            BeanUtils.copyProperties(pcxBill, pcxBillBasicVO);
            if (StringUtil.isNotEmpty(pcxBill.getExpenseCodes())) {
                pcxBillBasicVO.setExpenseCodes(Arrays.stream(pcxBill.getExpenseCodes().split(",")).collect(Collectors.toList()));
            }
            pcxBillBasicVO.setBillId(billId);
            return pcxBillBasicVO;
        }
        return null;
    }

    @Override
    public List<PcxBill> selectByBillIds(List<String> billIds) {
        if(CollectionUtils.isEmpty(billIds)){
            return new ArrayList<>();
        }
        return billMainService.selectByIds(billIds);
    }

    @Override
    public PageResult<PcxBillRepaymentVO> getPcxBillRepaymentList(PcxBillRepaymentQO qo) {
        Page<PcxBillRepaymentVO> page = new Page<>();
        page.setCurrent(qo.getPageIndex());
        page.setSize(qo.getPageSize());
//        List<PcxBillRepaymentVO> result = pcxBillDao.selectPcxBillRepaymentPage(qo);
        PageInfo<PcxBillRepaymentVO> pageInfo = PageHelper.startPage(qo.getPageIndex(), qo.getPageSize())
                .doSelectPageInfo(() -> {
                    pcxBillDao.selectPcxBillRepaymentPage(qo);
                });
        List<PcxBillRepaymentVO> result = pageInfo.getList();
//        Integer count = pcxBillDao.selectPcxBillRepaymentPageCount(qo);
        Long count = pageInfo.getTotal();
        PageResult<PcxBillRepaymentVO> pageResult = new PageResult<>();

        if (CollectionUtils.isNotEmpty(result)) {
            //批量查询预算指标信息
            List<String> billIds = result.stream().map(PcxBillRepaymentVO::getId).collect(Collectors.toList());
            List<PcxBillBalance> pcxBillBalanceList = pcxBillBalanceDao.selectList(Wrappers.lambdaQuery(PcxBillBalance.class).in(PcxBillBalance::getBillId, billIds));
            if (CollectionUtils.isNotEmpty(pcxBillBalanceList)) {

                List<String> balanceIds = pcxBillBalanceList.stream().map(PcxBillBalance::getBalanceId).collect(Collectors.toList());
                List<BudBalanceDTO> balanceDTOS = balanceExternalService.getBalanceByIds(balanceIds);
                if (CollectionUtils.isNotEmpty(balanceDTOS)) {
                    result.forEach(item -> {
                        List<PcxBillBalance> billBalances = pcxBillBalanceList.stream().filter(balance -> balance.getBillId().equals(item.getId())).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(billBalances)) {
                            item.setBalanceNo(billBalances.stream().map(PcxBillBalance::getBalanceNo).collect(Collectors.joining(",")));
                            List<BudBalanceDTO> dtoList = balanceDTOS.stream().filter(balanceDTO -> billBalances.stream().map(p -> p.getBalanceId()).collect(Collectors.toList()).contains(balanceDTO.getBalanceId())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(dtoList)) {
                                item.setBalanceDepartmentName(dtoList.stream().map(BudBalanceDTO::getDepartmentName).distinct().collect(Collectors.joining(",")));
                            }

                        }
                    });
                }
            }
        }
        pageResult.setTotal(count);
        pageResult.setResult(result);

        return pageResult;
    }

    @Override
    public List<PcxBillRepaymentVO> getPcxBillRepaymentList(String billId) {
        List<String> funcCodes = new ArrayList<>();
        funcCodes.add(BillFuncCodeEnum.REPAYMENT.getCode());
        funcCodes.add(BillFuncCodeEnum.EXPENSE.getCode());
        return pcxBillDao.selectPcxBillRepaymentListById(billId, funcCodes);
    }

    @Override
    @Transactional
    public CheckMsg<Map<String, Object>> addRepayment(PcxBillAddRepaymentQO qo) {
        PcxBill pcxBill = billMainService.view(qo.getBillId());
        if (null == pcxBill) {
            throw new ForbidTipsException("单据不存在");
        }
        if (qo.getLoanAmt().compareTo(pcxBill.getCheckAmt().subtract(pcxBill.getLoanAmt())) > 0) {
            throw new ForbidTipsException("还款金额不能大于剩余还款金额");
        }

        PcxBill repayPcxBill = new PcxBill();
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        PcxBillRelation pcxBillRelation = new PcxBillRelation();
        this.convertPcxBill(pcxBill, repayPcxBill, qo, attachRelList, pcxBillRelation);
        pcxBillDao.insert(repayPcxBill);
        if (CollectionUtils.isNotEmpty(attachRelList)) {
            batchServiceUtil.batchProcess(attachRelList, PcxBillExpAttachRelDao.class, PcxBillExpAttachRelDao::insert);
        }
        pcxBillRelationDao.insert(pcxBillRelation);
        //TODO 关联借款指标
        saveBillBalance(pcxBill.getId(), repayPcxBill.getId(), qo.getLoanAmt());

        //指标成功之后更新冲销金额
        callBackService.callBackUpdateLoanAmt(repayPcxBill);
        //判断借款单是否有关联申请单
        List<PcxBill> applyBillList = pcxBillDao.selectList(Wrappers.lambdaQuery(PcxBill.class).eq(PcxBill::getBillFuncCode, BillFuncCodeEnum.APPLY.getCode()).apply(" id in (select t.rel_bill_id from pcx_bill_relation t where t.bill_id = {0} )", pcxBill.getId()));
        if (CollectionUtil.isNotEmpty(applyBillList)) {
            //更新申请单冲销金额
            pcxBillDao.updateApplyBillLoanAmt(applyBillList.get(0).getId(), qo.getLoanAmt());
        }

        //TODO 修改指标预占
        pcxBalanceCtrlService.saveAuditCtrl(repayPcxBill, null, false);
        return CheckMsg.success();
    }

    private void saveBillBalance(String loanBillId, String repayBillId, BigDecimal repayAmt) {
        //获取借款单的预算指标
        List<PcxBillBalance> loanBillBalanceList = pcxBillBalanceDao.selectList(Wrappers.lambdaQuery(PcxBillBalance.class)
                .eq(PcxBillBalance::getBillId, loanBillId));
        if (CollectionUtil.isNotEmpty(loanBillBalanceList)) {

            List<PcxBillBalance> repayBillBalanceList = new ArrayList<>();

            List<PcxBillBalance> loanBudBillBalanceList = loanBillBalanceList.stream().filter(p -> p.getBalanceType().equals(BalanceTypeEnum.BUD.getCode())).collect(Collectors.toList());
            List<PcxBillBalance> loanIbalBillBalanceList = loanBillBalanceList.stream().filter(p -> p.getBalanceType().equals(BalanceTypeEnum.IBAL.getCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(loanBudBillBalanceList)) {
                //获取借款单历史的还款记录
                List<PcxBillBalance> repayHistoryBillBalanceList = pcxBillBalanceDao.selectList(Wrappers.lambdaQuery(PcxBillBalance.class)
                        .apply("bill_id in (select t.bill_id from pcx_bill_relation t where t.rel_bill_id = {0})", loanBillId)
                        .ne(PcxBillBalance::getBillId, repayBillId).eq(PcxBillBalance::getBalanceType, BalanceTypeEnum.BUD.getCode()));
                if (CollectionUtil.isNotEmpty(repayHistoryBillBalanceList)) {
                    for (PcxBillBalance item : loanBudBillBalanceList) {
                        if (repayAmt.compareTo(BigDecimal.ZERO) > 0) {
                            List<PcxBillBalance> balances = repayHistoryBillBalanceList.stream().filter(p -> p.getBalanceId().equals(item.getBalanceId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(balances)) {
                                BigDecimal usedAmt = balances.stream().map(PcxBillBalance::getUsedAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (item.getUsedAmt().compareTo(usedAmt) > 0) {
                                    if (repayAmt.compareTo(item.getUsedAmt().subtract(usedAmt)) >= 0) {
                                        PcxBillBalance repayBillBalance = new PcxBillBalance();
                                        BeanUtils.copyProperties(item, repayBillBalance);
                                        repayBillBalance.setBillId(repayBillId);
                                        repayBillBalance.setId(IDGenerator.snowflakeId());
                                        repayBillBalance.setUsedAmt(item.getUsedAmt().subtract(usedAmt));
                                        repayBillBalanceList.add(repayBillBalance);
                                        repayAmt = repayAmt.subtract(item.getUsedAmt().subtract(usedAmt));
                                    } else if (repayAmt.compareTo(item.getUsedAmt().subtract(usedAmt)) < 0) {
                                        PcxBillBalance repayBillBalance = new PcxBillBalance();
                                        BeanUtils.copyProperties(item, repayBillBalance);
                                        repayBillBalance.setId(IDGenerator.snowflakeId());
                                        repayBillBalance.setBillId(repayBillId);
                                        repayBillBalance.setUsedAmt(repayAmt);
                                        repayBillBalanceList.add(repayBillBalance);
                                        repayAmt = BigDecimal.ZERO;
                                    }
                                }
                            } else {
                                if (item.getUsedAmt().compareTo(repayAmt) >= 0) {
                                    PcxBillBalance repayBillBalance = new PcxBillBalance();
                                    BeanUtils.copyProperties(item, repayBillBalance);
                                    repayBillBalance.setBillId(repayBillId);
                                    repayBillBalance.setUsedAmt(repayAmt);
                                    repayBillBalance.setId(IDGenerator.snowflakeId());
                                    repayBillBalanceList.add(repayBillBalance);
                                    repayAmt = BigDecimal.ZERO;
                                } else if (item.getUsedAmt().compareTo(repayAmt) < 0) {
                                    PcxBillBalance repayBillBalance = new PcxBillBalance();
                                    BeanUtils.copyProperties(item, repayBillBalance);
                                    repayBillBalance.setBillId(repayBillId);
                                    repayBillBalance.setUsedAmt(item.getUsedAmt());
                                    repayBillBalance.setId(IDGenerator.snowflakeId());
                                    repayBillBalanceList.add(repayBillBalance);
                                    repayAmt = repayAmt.subtract(item.getUsedAmt());
                                }
                            }
                        } else {
                            break;
                        }
                    }
                } else {
                    for (PcxBillBalance item : loanBudBillBalanceList) {
                        if (repayAmt.compareTo(BigDecimal.ZERO) > 0) {
                            if (item.getUsedAmt().compareTo(repayAmt) >= 0) {
                                PcxBillBalance repayBillBalance = new PcxBillBalance();
                                BeanUtils.copyProperties(item, repayBillBalance);
                                repayBillBalance.setBillId(repayBillId);
                                repayBillBalance.setUsedAmt(repayAmt);
                                repayBillBalance.setId(IDGenerator.snowflakeId());
                                repayBillBalanceList.add(repayBillBalance);
                                repayAmt = BigDecimal.ZERO;
                            } else if (item.getUsedAmt().compareTo(repayAmt) < 0) {
                                PcxBillBalance repayBillBalance = new PcxBillBalance();
                                BeanUtils.copyProperties(item, repayBillBalance);
                                repayBillBalance.setBillId(repayBillId);
                                repayBillBalance.setUsedAmt(item.getUsedAmt());
                                repayBillBalance.setId(IDGenerator.snowflakeId());
                                repayBillBalanceList.add(repayBillBalance);
                                repayAmt = repayAmt.subtract(item.getUsedAmt());
                            }
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(loanIbalBillBalanceList) && CollectionUtil.isNotEmpty(repayBillBalanceList)) {
                loanIbalBillBalanceList.forEach(item -> {
                    List<PcxBillBalance> collect = repayBillBalanceList.stream().filter(p -> p.getProjectCode().equals(item.getProjectCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        PcxBillBalance repayBillBalance = new PcxBillBalance();
                        BeanUtils.copyProperties(item, repayBillBalance);
                        repayBillBalance.setBillId(repayBillId);
                        BigDecimal usedAmt = collect.stream().map(p -> p.getUsedAmt()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        repayBillBalance.setUsedAmt(usedAmt);
                        repayBillBalance.setId(IDGenerator.snowflakeId());
                        repayBillBalanceList.add(repayBillBalance);
                    }
                });
            }

            if (CollectionUtil.isNotEmpty(repayBillBalanceList)) {
                batchServiceUtil.batchProcess(repayBillBalanceList, PcxBillBalanceDao.class, PcxBillBalanceDao::insert);
            }
        }


    }

    /**
     * 查询申请单费用标准
     *
     * @param qo
     * @return
     */
    @Override
    public CheckMsg<List<ExpStandSnapshotsFrontVO>> getReqBillExpenseStand(PcxReqBillExpenseStandQO qo) {

        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        pcxBasExpTypeQO.setAgyCode(qo.getAgyCode());
        pcxBasExpTypeQO.setFiscal(qo.getFiscal());
        pcxBasExpTypeQO.setMofDivCode(qo.getMofDivCode());
        pcxBasExpTypeQO.setLastCode(qo.getExpenseTypeCode());
        List<PcxBasExpType> pcxBasExpTypes = (List<PcxBasExpType>) pcxBasExpTypeService.getExpTypeDetail(pcxBasExpTypeQO).getData();
        if (CollectionUtils.isEmpty(pcxBasExpTypes)) {
            return CheckMsg.fail("费用项不存在费用明细");
        }

        // 查询淡、旺季
        QueryCityPeakClassifyQO queryCityPeakClassifyQO = new QueryCityPeakClassifyQO();
        queryCityPeakClassifyQO.setAgyCode(qo.getAgyCode());
        queryCityPeakClassifyQO.setFiscal(qo.getFiscal());
        queryCityPeakClassifyQO.setMofDivCode(qo.getMofDivCode());
        queryCityPeakClassifyQO.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK);
        queryCityPeakClassifyQO.setDataCodes(Arrays.asList(qo.getTravelPlaceCode().split(",")));
        queryCityPeakClassifyQO.setPeakDateQO(new PeakDateQO(qo.getStartTime(), qo.getFinishTime()));
        List<CityPeakVO> cityPeakVOList = pcxBasCityClassifyService.isCityPeak(queryCityPeakClassifyQO);
        Map<String, List<String>> cityPeakMap = new HashMap<>();
        List<String> isPeakList = new ArrayList<>();
        List<String> isNotPeakList = new ArrayList<>();
        cityPeakMap.put("isPeak", isPeakList);
        cityPeakMap.put("isNotPeak", isNotPeakList);
        for (CityPeakVO cityPeakVO : cityPeakVOList) {
            if (cityPeakVO.getPeakFlag() != null) {
                if (cityPeakVO.getPeakFlag() == 1) {
                    isPeakList.add(cityPeakVO.getDataCode());
                } else if (cityPeakVO.getPeakFlag() == 3) {
                    isPeakList.add(cityPeakVO.getDataCode());
                    isNotPeakList.add(cityPeakVO.getDataCode());
                } else {
                    isNotPeakList.add(cityPeakVO.getDataCode());
                }
            } else {
                isNotPeakList.add(cityPeakVO.getDataCode());
            }
        }

        List<ExpStandSnapshotsFrontVO> result = new ArrayList<>();

        // 查询每个费用明细
        for (PcxBasExpType pcxBasExpType : pcxBasExpTypes) {

            // 费用标准列表
            PcxStandQueryQO pcxStandQueryQO = new PcxStandQueryQO();
            BeanUtils.copyProperties(qo, pcxStandQueryQO);
            pcxStandQueryQO.setExpenseTypeCode(pcxBasExpType.getExpenseCode());
            List<PcxStandVO> normalStands = pcxStandKeyService.selectFixedStand(pcxStandQueryQO).getData();
            List<PcxStandVO> pcxStandVOS = new ArrayList<>(normalStands);
            List<PcxStandVO> commonStands = pcxStandKeyService.selectCommonStand(pcxStandQueryQO).getData();
            pcxStandVOS.addAll(commonStands);

            // 有费用标准数据
            if (CollectionUtils.isNotEmpty(pcxStandVOS)) {
                // 标准值过滤
                for (PcxStandVO pcxStandVO : pcxStandVOS) {
                    List<PcxStandValueVO> standValueList = pcxStandVO.getStandValueList();

                    // 过滤费控级别
                    if (pcxStandVO.getRowKeyCode().equals("controlLevel") || pcxStandVO.getColumnKeyCode().equals("controlLevel")) {
                        PcxEmployeeWithCostLevelQO pcxEmployeeWithCostLevelQO = new PcxEmployeeWithCostLevelQO();
                        pcxEmployeeWithCostLevelQO.setAgyCode(qo.getAgyCode());
                        pcxEmployeeWithCostLevelQO.setFiscal(Integer.valueOf(qo.getFiscal()));
                        pcxEmployeeWithCostLevelQO.setMofDivCode(qo.getMofDivCode());
                        pcxEmployeeWithCostLevelQO.setMadCodes(qo.getSelectFollower().stream().map(PcxBillTravelFellows::getEmpCode).collect(Collectors.toList()));
                        List<PcxEmployeeWithCostLevelVO> employeeWithCostLevelVOS = pcxCostControlLevelService.getEmpsWithCostLevelByCodes(pcxEmployeeWithCostLevelQO);
                        Set<PcxCostControlLevel> costLevelEntitySet = new HashSet<>();
                        for (PcxEmployeeWithCostLevelVO employeeWithCostLevelVO : employeeWithCostLevelVOS) {
                            if (CollectionUtils.isNotEmpty(employeeWithCostLevelVO.getPcxCostControlLevels())) {
                                costLevelEntitySet.addAll(employeeWithCostLevelVO.getPcxCostControlLevels());
                            }
                            if (!Objects.isNull(employeeWithCostLevelVO.getPcxCostControlLevel())) {
                                costLevelEntitySet.add(employeeWithCostLevelVO.getPcxCostControlLevel());
                            }
                        }
                        List<String> costLevelCodeList = costLevelEntitySet.stream().sorted(Comparator.comparing(PcxCostControlLevel::getPriority).reversed()).map(PcxCostControlLevel::getCostControlCode).collect(Collectors.toList());
                        if (pcxStandVO.getRowKeyCode().equals("controlLevel")) {
                            standValueList = standValueList.stream().filter(item -> costLevelCodeList.contains(item.getRowValueCode())).collect(Collectors.toList());
                        } else {
                            standValueList = standValueList.stream().filter(item -> costLevelCodeList.contains(item.getColumnValueCode())).collect(Collectors.toList());
                        }
                    }

                    List<String> cityList = new ArrayList<>();
                    // 淡旺季节过滤
                    if (CollectionUtils.isEmpty(pcxStandVO.getConditionList().stream().filter(item -> item.getCondValueCode().equals("peak")).collect(Collectors.toList()))) {
                        cityList = isNotPeakList;
                    } else {
                        cityList = isPeakList;
                    }

                    // 过滤城市分类数据
                    if (pcxStandVO.getRowKeyCode().equals("cityClassify") || pcxStandVO.getColumnKeyCode().equals("cityClassify")) {
                        Set<String> travelPlaceCodeList = new HashSet<>();
                        for (String placeCode : cityList) {
                            PcxBasCityClassify pcxBasCityClassify = new PcxBasCityClassify();
                            pcxBasCityClassify.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_CITY);
                            pcxBasCityClassify.setDataCode(placeCode);
                            pcxBasCityClassify.setAgyCode(qo.getAgyCode());
                            pcxBasCityClassify.setFiscal(qo.getFiscal());
                            pcxBasCityClassify.setMofDivCode(qo.getMofDivCode());
                            List<PcxBasCityClassify> cityClassifyList = pcxBasCityClassifyService.selectList(pcxBasCityClassify);
                            if (CollectionUtils.isNotEmpty(cityClassifyList)) {
                                for (PcxBasCityClassify basCityClassify : cityClassifyList) {
                                    travelPlaceCodeList.add(basCityClassify.getClassifyCode());
                                }
                            } else {
                                travelPlaceCodeList.add("other");
                            }
                        }
                        if (pcxStandVO.getRowKeyCode().equals("cityClassify")) {
                            standValueList = standValueList.stream().filter(item -> travelPlaceCodeList.contains(item.getRowValueCode())).collect(Collectors.toList());
                        } else {
                            standValueList = standValueList.stream().filter(item -> travelPlaceCodeList.contains(item.getColumnValueCode())).collect(Collectors.toList());
                        }
                    }

                    // 过滤淡旺季分类数据
                    if (pcxStandVO.getRowKeyCode().equals("cityPeak") || pcxStandVO.getColumnKeyCode().equals("cityPeak")) {
                        Set<String> travelPlaceCodeList = new HashSet<>();
                        for (String placeCode : cityList) {
                            PcxBasCityClassify pcxBasCityClassify = new PcxBasCityClassify();
                            pcxBasCityClassify.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK);
                            pcxBasCityClassify.setDataCode(placeCode);
                            pcxBasCityClassify.setAgyCode(qo.getAgyCode());
                            pcxBasCityClassify.setFiscal(qo.getFiscal());
                            pcxBasCityClassify.setMofDivCode(qo.getMofDivCode());
                            List<PcxBasCityClassify> cityClassifyList = pcxBasCityClassifyService.selectList(pcxBasCityClassify);
                            if (CollectionUtils.isNotEmpty(cityClassifyList)) {
                                for (PcxBasCityClassify basCityClassify : cityClassifyList) {
                                    travelPlaceCodeList.add(basCityClassify.getClassifyCode());
                                }
                            }
                        }
                        if (pcxStandVO.getRowKeyCode().equals("cityPeak")) {
                            standValueList = standValueList.stream().filter(item -> travelPlaceCodeList.contains(item.getRowValueCode())).collect(Collectors.toList());
                        } else {
                            standValueList = standValueList.stream().filter(item -> travelPlaceCodeList.contains(item.getColumnValueCode())).collect(Collectors.toList());
                        }
                    }

                    if (CollectionUtils.isEmpty(standValueList)) {
                        continue;
                    }

                    ExpStandSnapshotsFrontVO expStandSnapshotsFrontVO = new ExpStandSnapshotsFrontVO();

                    List<ExpStandSnapshotValueVO> snapshotValueList = standValueList.stream().map(item -> {
                        ExpStandSnapshotValueVO expStandSnapshotValueVO = new ExpStandSnapshotValueVO();
                        BeanUtils.copyProperties(item, expStandSnapshotValueVO);
                        expStandSnapshotValueVO.setStandValue(item.getStandardValue());
                        return expStandSnapshotValueVO;
                    }).collect(Collectors.toList());

                    // 根据column分组
                    Map<String, List<ExpStandSnapshotValueVO>> standValueMap = snapshotValueList.stream().collect(Collectors.groupingBy(ExpStandSnapshotValueVO::getColumnValueName));
                    List<List<ExpStandSnapshotValueVO>> standValuesList = new ArrayList<>();
                    List<ExpStandSnapshotValueVO> snapshotValueVOS = new ArrayList<>();
                    standValueMap.forEach((key, value) -> {
                        // 添加第一行表头
                        if (CollectionUtils.isEmpty(snapshotValueVOS)) {
                            List<String> rowValueNames = value.stream().map(row -> row.getRowValueName()).collect(Collectors.toList());
                            rowValueNames.add(0, pcxStandVO.getColumnKeyName());
                            for (String rowValueName : rowValueNames) {
                                ExpStandSnapshotValueVO rowNameVO = new ExpStandSnapshotValueVO();
                                rowNameVO.setStandValue(rowValueName);
                                snapshotValueVOS.add(rowNameVO);
                            }

                            standValuesList.add(0, snapshotValueVOS);
                        }

                        // 添加其他行数据
                        ExpStandSnapshotValueVO rowNameVO = new ExpStandSnapshotValueVO();
                        rowNameVO.setStandValue(key);
                        value.add(0, rowNameVO);
                        standValuesList.add(value);
                    });

                    // 生成最终标准模型
                    expStandSnapshotsFrontVO.setExpenseTypeCode(pcxStandVO.getExpenseTypeCode());
                    expStandSnapshotsFrontVO.setExpenseTypeName(pcxStandVO.getExpenseTypeName());
                    expStandSnapshotsFrontVO.setValueSource(pcxStandVO.getValueSource());
                    expStandSnapshotsFrontVO.setStandValue(standValuesList);
                    result.add(expStandSnapshotsFrontVO);

                    if (pcxStandVO.getExpenseTypeCode().equals("3021102")) {
                        String exTypeName = pcxStandVO.getExpenseTypeName();
                        if (CollectionUtils.isEmpty(pcxStandVO.getConditionList().stream().filter(item -> item.getCondValueCode().equals("peak")).collect(Collectors.toList()))) {
                            expStandSnapshotsFrontVO.setExpenseTypeName(exTypeName + "（标准）");
                        } else {
                            expStandSnapshotsFrontVO.setExpenseTypeName(exTypeName + "（旺季）");
                        }
                    }
                }
            }
        }

        // 结果排序
        result = result.stream()
                .sorted(Comparator.comparing(ExpStandSnapshotsFrontVO::getExpenseTypeCode))
                .sorted(Comparator.comparing(ExpStandSnapshotsFrontVO::getExpenseTypeName).reversed())
                .collect(Collectors.toList());

        return CheckMsg.success(result);
    }

    @Override
    public CheckMsg<PageResult<PcxBill>> getApplyBill(PcxBillQueryQO qo) {
        if (StringUtil.isEmpty(qo.getAgyCode()) || StringUtil.isEmpty(qo.getFiscal()) || StringUtil.isEmpty(qo.getMofDivCode())) {
            return CheckMsg.fail("参数错误");
        }
        if (StringUtil.isEmpty(qo.getPageSize()) || StringUtil.isEmpty(qo.getPageIndex())) {
            return CheckMsg.fail("分页参数错误");
        }
        CheckMsg<PageResult<PcxBill>> success = CheckMsg.success();
        qo.setBillFuncCode(BillFuncCodeEnum.APPLY.getCode());
        qo.setApproveStatus(BillApproveStatusEnum.APPROVED.getCode());
        PageResult<PcxBill> bill = billMainService.selectWithPage(qo);
        return success.setData(bill);
    }

    @Override
    public CheckMsg<?> setSettle(PcxBillQO qo) throws Exception {
        //判断单据ids集合 （ids 是否为空）
        if (CollectionUtils.isEmpty(qo.getIds())) {
            return CheckMsg.fail("请至少选择一条待办结的单据");
        }

        //根据单据ids集合 查询单据列表
        List<PcxBill> pcxBills = billMainService.selectMainBill(PcxBill.builder().ids(qo.getIds()).build());
        if (CollectionUtil.isEmpty(pcxBills)) {
            return CheckMsg.fail("未查找到单据信息");
        }
        //查询单据关联的指标余额控制表信息
        Map<String, QBalVO> ctrlBalanceMap = balanceExternalService.getCtrlBalanceByBillId(qo.getIds());
        //查询单据关联的指标。通过指标id获取使用金额 getUsedAmt 用于办结判断
        List<PcxBillBalance> pcxBillBalances = pcxBillBalanceService.selectByBillIds(qo.getIds());
        Map<String, List<PcxBillBalance>> pcxBillBalanceMap = pcxBillBalances.stream().collect(Collectors.groupingBy(PcxBillBalance::getBillId));
        //一次性获取该单据对应管理的其他单据并分组
        List<PcxBillRelation> pcxBillRelations = pcxBillRelationService.selectByRelBillIds(qo.getIds());
        Map<String, List<PcxBillRelation>> pcxBillMap = pcxBillRelations.stream().collect(Collectors.groupingBy(PcxBillRelation::getBillId));

        Map<String, PcxBill> relBillMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(pcxBillMap)) {
            List<PcxBill> relBillList = billMainService.selectMainBill(PcxBill.builder().ids(new ArrayList<>(pcxBillMap.keySet())).build());
            relBillMap = relBillList.stream()
                    .collect(Collectors.toMap(PcxBill::getId, bill -> bill));
        }
        //获取指标外部bean
        IBalanceExternalService balanceExternalService = getBalanceBean(qo.getMofDivCode(), qo.getAgyCode(), qo.getFiscal(), qo.getTenantId());
        PcxBillServiceImpl billService = SpringUtil.getBean(PcxBillServiceImpl.class);
        List<PcxSettleBalance> settleBalanceList = new ArrayList<>();
        List<PcxBill> saveBills = new ArrayList<>();
        // 执行结项处理，使用事务控制
        for (PcxBill pcxBill : pcxBills) {
            // 办结状态字段的校验
            if (BillStatusEnum.COMPLETE.getCode().equals(pcxBill.getBillStatus()) || BillCompletedStatusEnum.COMPLETED.getCode().equals(pcxBill.getCompletedStatus())) {
                throw new RuntimeException("[" + pcxBill.getBillNo() + "]单据状态已变更，无法办结");
            }
            pcxBill.setCompleted(qo.getUserCode());
            pcxBill.setClaimantName(qo.getUserName());
            pcxBill.setCompletedStatus(BillCompletedStatusEnum.COMPLETED.getCode());

            //验判断本条单据是否都走完流程
            isEndBill(pcxBill, pcxBillMap, relBillMap);
            //设置需要变更单据信息(下面是计算信息，前面校验过了就能结项)
            buildSettleBalance(pcxBill, saveBills);
            // 如果申请金额为0 无需执行后续计算动作
            if (Optional.ofNullable(pcxBill.getCheckAmt()).orElse(BigDecimal.ZERO).signum() == 0) {
                continue;
            }

            List<PcxBillBalance> billBalances = pcxBillBalanceMap.get(pcxBill.getId());
            if (CollectionUtils.isEmpty(billBalances)) {
                log.info("单据ID:{},申请指标为空,无需计算指标", pcxBill.getId());
                continue;
            }
            //下一步单据结项 指标额度计算返还
            List<PcxSettleBalance> pcxSettleBalances = billService.billSettle(ctrlBalanceMap.get(pcxBill.getId()).getCanUseAmt(), pcxBill, qo, billBalances, balanceExternalService);

            //收集结果
            if (CollectionUtil.isNotEmpty(pcxSettleBalances)) {
                settleBalanceList.addAll(pcxSettleBalances);
            }

        }
        //保存单据指标明细以及更新单据状态
        billService.saveSettleData(saveBills, settleBalanceList);

        return CheckMsg.success().setData(qo.getIds()).setMsgInfo("办结完成!");
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveSettleData(List<PcxBill> saveBills, List<PcxSettleBalance> settleBalanceList) {
        batchServiceUtil.batchProcess(saveBills, PcxBillDao.class, PcxBillDao::updateById);
        if (CollectionUtils.isNotEmpty(settleBalanceList)) {
            batchServiceUtil.batchProcess(settleBalanceList, PcxSettleBalanceDao.class, PcxSettleBalanceDao::insert);
        }
    }

    private void buildSettleBalance(PcxBill applyBill, List<PcxBill> saveBills) {
        //申请单可以结项，返还指标
        String completedStatus = StringUtil.isNotBlank(applyBill.getCompletedStatus())
                ? applyBill.getCompletedStatus()
                : BillCompletedStatusEnum.COMPLETED.getCode();
        String billStatus = BillCompletedStatusEnum.COMPLETED.getCode().equals(completedStatus)
                ? BillStatusEnum.COMPLETE.getCode()
                : applyBill.getBillStatus();
        PcxBill updateBill = new PcxBill();
        //结项人信息由外部赋值
        updateBill.setCompleted(applyBill.getCompleted());
        updateBill.setCompletedName(applyBill.getCompletedName());
        updateBill.setCompletedTime(com.pty.pub.common.util.DateUtil.nowTime());
        updateBill.setCompletedStatus(completedStatus);
        updateBill.setBillStatus(billStatus);
        updateBill.setId(applyBill.getId());
        saveBills.add(updateBill);
    }

    /**
     * 指标额度计算返还并结项
     *
     * @param canUseAmt
     * @param applyBill
     * @param qo
     * @param applyBillBalances
     * @param balanceExternalService
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public List<PcxSettleBalance> billSettle(BigDecimal canUseAmt, PcxBill applyBill, PcxBillQO qo, List<PcxBillBalance> applyBillBalances, IBalanceExternalService balanceExternalService) throws Exception {
        //申请单可以结项，返还指标
        applyBill.setInputAmt(applyBill.getCheckAmt().subtract(canUseAmt));
        applyBill.setCheckAmt(applyBill.getCheckAmt().subtract(canUseAmt));

        //指标信息结项表  pcx_settle_balance 待实现
        List<PcxSettleBalance> setBalances = collectSetBalance(applyBill.getId(), canUseAmt, applyBillBalances, balanceExternalService);
        if (CollectionUtils.isNotEmpty(setBalances)) {
            CtrlDTO ctrlDTO = pcxBalanceCtrlService.convertToCtrlDTO(applyBill, true);
            ctrlDTO.setCurUser(qo.getUserCode());
            ctrlDTO.setRemark(PcxBill.class.getName() + ".billSettle()");
            ctrlDTO.getBillDto().getNotElementMap().put("isSettle", "1");
            ctrlDTO.getRelationBills().forEach(billDto -> {
                billDto.getNotElementMap().put("isSettle", "1");
            });

            // 遍历结算余额，调整额度
            for (PcxSettleBalance settleBalance : setBalances) {
                for (BillDTO dto : ctrlDTO.getRelationBills()) {
                    //保留原逻辑、预算额度结算
                    if (dto.getBillId().equals(settleBalance.getBalanceId())) {
                        dto.setAmt(dto.getAmt().subtract(
                                Objects.nonNull(settleBalance.getSettleAmt()) ? settleBalance.getSettleAmt() : BigDecimal.ZERO));
                    }
                }
            }

            // 保存审计控制信息
            balanceExternalService.saveAuditCtrl(ctrlDTO);
        }
        return setBalances;
    }

    /**
     * 计算申请占指标结算金额，并保存到数据库，终审重启需要重置额度 m每个指标的结项金额为申请总金额，减去额度里申请已还金额
     * 体彩中心：报销换指标，申请上指标和报销不一致，结项时，目前只处理申请单指标直接把申请余额赋值给结项金额
     */
    private List<PcxSettleBalance> collectSetBalance(String relBillId, BigDecimal canUseAmt, List<PcxBillBalance> applyBillBalances, IBalanceExternalService balanceExternalService) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        List<PcxSettleBalance> settleBalances = new ArrayList<>();
        Map<String, BigDecimal> balanceMap = new HashMap<>();
        if (applyBillBalances.size() > 1) {

            List<BalanceBackDetailVO> balanceBackList = balanceExternalService.getBalanceBackData(relBillId);
            Map<String, List<BalanceBackDetailVO>> listMap = balanceBackList.stream()
                    .collect(Collectors.groupingBy(BalanceBackDetailVO::getRelateBalanceId));
            for (Map.Entry<String, List<BalanceBackDetailVO>> entry : listMap.entrySet()) {
                balanceMap.put(entry.getKey(), entry.getValue().stream().map(BalanceBackDetailVO::getBackAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
        for (PcxBillBalance billBalance : applyBillBalances) {
            BigDecimal backAmt = balanceMap.get(billBalance.getBalanceId()) == null ? BigDecimal.ZERO
                    : balanceMap.get(billBalance.getBalanceId());
            BigDecimal settleAmt = billBalance.getUsedAmt().subtract(backAmt);
            if (settleAmt.signum() > 0) {
                PcxSettleBalance settleBalance = new PcxSettleBalance();
                settleBalance.setId(IDGenerator.id());
                settleBalance.setBillId(billBalance.getBillId());
                settleBalance.setBalanceId(billBalance.getBalanceId());
                settleBalance.setBalanceNo(billBalance.getBalanceNo());
                settleBalance.setSettleStatus(BillSettleBalanceStatusEnum.COMPLETED.getCode());
                settleBalance.setCreatedTime(com.pty.pub.common.util.DateUtil.nowTime());
                if (applyBillBalances.size() > 1) {
                    //申请用多指标，需要计算每个指标还多少钱
                    settleBalance.setSettleAmt(settleAmt);
                } else {
                    //申请用单指标，结项余额
                    settleBalance.setSettleAmt(canUseAmt);
                }
                totalAmt = totalAmt.add(settleBalance.getSettleAmt());
                settleBalances.add(settleBalance);
            }
        }
        return settleBalances;
    }

    private IBalanceExternalService getBalanceBean(String mofDivCode, String agyCode, String fiscal, String tenantId) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setFiscal(Integer.parseInt(fiscal));
        paOptionQO.setMofDivCode(mofDivCode);
        paOptionQO.setAgyCode(agyCode);
        paOptionQO.setTenantId(tenantId);
        String balanceServiceName = bussinessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.BALANCE_SERVICE_BEAN.getOptCode());
        IBalanceExternalService balanceExternalService = balanceExternalServiceMap.get(balanceServiceName);
        if (ObjectUtils.isEmpty(balanceExternalService)) {
            log.error("获取bean失败,optCode{},mof_div_code: {},agy_code:{},fiscal:{},tenant_id:{}", BusinessRuleEnum.BusinessOptionEnum.BALANCE_SERVICE_BEAN.getOptCode(), mofDivCode, agyCode, fiscal, tenantId);
            throw new RuntimeException("业务处理失败");
        }
        return balanceExternalService;
    }

    /**
     * 检查单据是否可以办结
     * 此方法用于判断给定的 PcxBill 单据是否可以顺利办结它首先检查与该单据相关联的其他单据是否都已经完成流程
     * 如果有关联的单据未完成流程，则抛出运行时异常，指出无法办结的原因
     **/
    private void isEndBill(PcxBill pcxBill, Map<String, List<PcxBillRelation>> pcxBillMap, Map<String, PcxBill> relBillMap) {
        //判断单据办结的单子关联的其他单子是否都走完流程
        List<PcxBillRelation> pcxBillRelations = pcxBillMap.get(pcxBill.getId());
        if (null == pcxBillRelations || pcxBillRelations.size() == 0) {
            return;
        }
        //判断单据工作流状态(先判断单据审批状态)
        for (PcxBillRelation pcxBillRelation : pcxBillRelations) {
            PcxBill relBill = relBillMap.get(pcxBillRelation.getBillId());
            if (null == relBill) {
                continue;
            }
            if (BillApproveStatusEnum.APPROVED.getCode().equals(relBill.getApproveStatus())) {
                continue;
            }
            throw new RuntimeException("占用[" + pcxBill.getBillNo() + "]申请单的其他单据流程未走完，无法结项！");
        }
    }

    @Override
    @Transactional
    public CheckMsg<Map<String, Object>> finishBill(PcxBillExpenseCompletedQO qo) {
        if (StringUtils.isBlank(qo.getBillIds())) {
            throw new ForbidTipsException("参数错误");
        }
        List<String> billIds = Arrays.asList(qo.getBillIds().split(","));
        List<PcxBill> pcxBillList = pcxBillDao.selectBatchIds(billIds);
        if (CollectionUtils.isEmpty(pcxBillList)) {
            throw new ForbidTipsException("单据不存在");
        }

        for (PcxBill pcxBill : pcxBillList) {
            if (pcxBill.getBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode()) || pcxBill.getBillFuncCode().equals(BillFuncCodeEnum.LOAN.getCode())) {
                pcxBill.setCompletedStatus(qo.getCompletedStatus());
                billMainService.updateCompletedStatus(pcxBill);
                //单据归还指标
                if (pcxBill.getCheckAmt().compareTo(BigDecimal.ZERO) > 0) {
                    try {
                        pcxBalanceCtrlService.saveCtrl(pcxBill, null, true);
                    } catch (Exception e) {
                        log.info("办结归还指标异常======={}", e.getMessage());
                        throw new ForbidTipsException("办结归还指标异常");
                    }
                }
            }
        }
        return CheckMsg.success();
    }

    /**
     * 查询本人年度报销金额和笔数
     *
     * @param qo
     * @return
     */
    @Override
    public CheckMsg<PcxMyFiscalExpenseVO> getMyFiscalExpenseInfo(PcxMyFiscalExpenseQO qo) {
        List<String> billFuncCodes = new ArrayList<>();
        billFuncCodes.add(BillFuncCodeEnum.EXPENSE.getCode());

        List<PcxBill> pcxBills = pcxBillDao.selectList(Wrappers.lambdaQuery(PcxBill.class)
                .eq(PcxBill::getFiscal, qo.getFiscal())
                .eq(PcxBill::getMofDivCode, qo.getMofDivCode())
                .eq(PcxBill::getAgyCode, qo.getAgyCode())
                .in(PcxBill::getBillFuncCode, billFuncCodes)
                .eq(PcxBill::getClaimantUserCode, PtyContext.getUsername())
                .eq(PcxBill::getApproveStatus, BillApproveStatusEnum.APPROVED.getCode()));  // 统计已审批单据
        PcxMyFiscalExpenseVO pcxMyFiscalExpenseVO = new PcxMyFiscalExpenseVO();
        pcxMyFiscalExpenseVO.setTotalExpenseCount(String.valueOf(pcxBills.size()));
        pcxMyFiscalExpenseVO.setTotalExpenseMoney(pcxBills.stream().map(PcxBill::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        return CheckMsg.success(pcxMyFiscalExpenseVO);
    }

    @Override
    public List<String> selectInvalidBillIds() {
        return pcxBillDao.selectInvalidBillIds();
    }

    private void convertPcxBill(PcxBill pcxBill, PcxBill repayPcxBill, PcxBillAddRepaymentQO qo, List<PcxBillExpAttachRel> attachList, PcxBillRelation pcxBillRelation) {
        BeanUtils.copyProperties(pcxBill, repayPcxBill);
        repayPcxBill.setLoanAmt(qo.getLoanAmt());
        repayPcxBill.setBillStatus(BillStatusEnum.ARCHIVE.getCode());
        repayPcxBill.setApproveStatus(BillApproveStatusEnum.APPROVED.getCode());
        repayPcxBill.setPayStatus(BillPayStatusEnum.PAID.getCode());
        repayPcxBill.setId(IDGenerator.snowflakeId());
        repayPcxBill.setTransDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
        repayPcxBill.setItemCode("");
        repayPcxBill.setItemName("");
        repayPcxBill.setBillFuncCode(BillFuncCodeEnum.REPAYMENT.getCode());
        repayPcxBill.setBillFuncName(BillFuncCodeEnum.REPAYMENT.getName());
        repayPcxBill.setPayTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
        repayPcxBill.setInputAmt(qo.getLoanAmt());
        repayPcxBill.setCheckAmt(qo.getLoanAmt());
        repayPcxBill.setSettlementAmt(BigDecimal.ZERO);
        repayPcxBill.setCreator(qo.getUserCode());
        repayPcxBill.setCreatorName(qo.getUserName());
        repayPcxBill.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        repayPcxBill.setAgyCode(qo.getAgyCode());
        repayPcxBill.setFiscal(qo.getFiscal());
        repayPcxBill.setMofDivCode(qo.getMofDivCode());
        repayPcxBill.setBillNo(pcxBillNoService.getBillNo(repayPcxBill.getMofDivCode(), repayPcxBill.getAgyCode(), repayPcxBill.getBillFuncCode(), BeanUtil.beanToMap(repayPcxBill)));

        pcxBillRelation.setId(IDGenerator.snowflakeId());
        pcxBillRelation.setBillFuncCode(repayPcxBill.getBillFuncCode());
        pcxBillRelation.setBillId(repayPcxBill.getId());
        pcxBillRelation.setRelBillFuncCode(pcxBill.getBillFuncCode());
        pcxBillRelation.setRelBillId(pcxBill.getId());
        pcxBillRelation.setRelBillNo(pcxBill.getBillNo());
        pcxBillRelation.setRelBillName(pcxBill.getReason());
        pcxBillRelation.setAgyCode(qo.getAgyCode());
        pcxBillRelation.setFiscal(qo.getFiscal());
        pcxBillRelation.setUsedAmt(qo.getLoanAmt());
        pcxBillRelation.setMofDivCode(qo.getMofDivCode());
        pcxBillRelation.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        pcxBillRelation.setCreator(qo.getUserCode());
        pcxBillRelation.setCreatorName(qo.getUserName());
        pcxBillRelation.setCreatedTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

        if (CollectionUtils.isNotEmpty(qo.getAttachList())) {
            for (PcxBillExpAttachRelQO pcxBillAttachRelatonQO : qo.getAttachList()) {
                PcxBillExpAttachRel pcxBillExpAttachRel = new PcxBillExpAttachRel();
                pcxBillExpAttachRel.setId(IDGenerator.id());
                pcxBillExpAttachRel.setBillId(repayPcxBill.getId());
                pcxBillExpAttachRel.setRelType(PcxExpAttachRelType.REPAYMENT.getCode());
                pcxBillExpAttachRel.setRelId("");
                pcxBillExpAttachRel.setAttachId(pcxBillAttachRelatonQO.getId());
                pcxBillExpAttachRel.setFileName(pcxBillAttachRelatonQO.getName());
                pcxBillExpAttachRel.setFiscal(qo.getFiscal());
                pcxBillExpAttachRel.setAgyCode(qo.getAgyCode());
                pcxBillExpAttachRel.setMofDivCode(qo.getMofDivCode());
                attachList.add(pcxBillExpAttachRel);
            }
        }
    }


    public boolean needEcsCompare(PcxBill pcxBill){
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxBill.getMofDivCode());
        paOptionQO.setAgyCode(pcxBill.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxBill.getFiscal()));
        String value = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.PRINT_BILL.getOptCode());
        if (Objects.equals(PcxConstant.PrintBillType.PASTE_BILL, value)){
            pcxExpDetailEcsRelDao.updateComparedNoNeedCompare(pcxBill.getId());
            //如果是粘贴单模式，并且全是电子票，则无需比对
            Long count = pcxExpDetailEcsRelDao.selectCount(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                    .eq(PcxExpDetailEcsRel::getBillId, pcxBill.getId())
                    .ne(PcxExpDetailEcsRel::getEcsBillId, "")
                    .eq(PcxExpDetailEcsRel::getEcsBillKind, EcsEnum.BillKind.PAPER.getCode()));
            return Objects.nonNull(count) && count > 0;
        }
        return true;
    }

    @Override
    @Transactional
    public CheckMsg<Map<String, Object>> approved(PcxBillApprovedVO qo) {
        PcxBill pcxBill = billMainService.view(qo.getBillId());
        if (Objects.isNull(pcxBill)) return CheckMsg.fail("单据不存在");
        if (!pcxBill.getBillStatus().equals(BillStatusEnum.SAVE.getCode())) return CheckMsg.fail("单据状态不正确");
        //稽核规则校验
        if (Objects.nonNull(qo.getIsValidate()) && qo.getIsValidate() == 1){
            witAuditRuleService.validateRule(pcxBill,null);
        }
        boolean approvalEnabled = approvalEnabled(pcxBill);
        if (approvalEnabled) {
            //指标预占
            pcxBalanceCtrlService.saveCtrl(pcxBill, !Objects.isNull(qo.getIsValidate()) && qo.getIsValidate() == 1);
            billExpenseCommonService.processApprove(pcxBill, qo.getComment());

            Integer compareStatus = null;
            //查询是否打印粘贴单配置，如果是，则查询报销单是否全部电子票，如果是，则修改报销单纸电对比状态到完成
            if (BillFuncCodeEnum.LOAN.getCode().equals(pcxBill.getBillFuncCode()) ||
                    BillFuncCodeEnum.APPLY.getCode().equals(pcxBill.getBillFuncCode())) {
                // 无需比对
                compareStatus = ComparedResultStatus.NO_NEED_CHECK.getCode();
            }else {
                if (!needEcsCompare(pcxBill)) {
                    compareStatus = ComparedResultStatus.CHECKED.getCode();
                }
            }
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(pcxBill.getId())
                    .billStatus(BillStatusEnum.SUBMIT.getCode())
                    .comparedStatus(compareStatus)
                    .build();
            billMainService.updateStatus(updateStatusDTO);
        }else {
            BillMainService.BillStatusUpdateDTO updateStatusDTO = BillMainService.BillStatusUpdateDTO.builder()
                    .billId(pcxBill.getId())
                    .billStatus(BillStatusEnum.SUBMIT.getCode())
                    .approveStatus(BillApproveStatusEnum.APPROVED.getCode())
                    .payStatus(BillPayStatusEnum.PAID.getCode())
                    .build();
            billMainService.updateStatus(updateStatusDTO);
        }

        return CheckMsg.success();
    }

    /**
     * 查询单据费用标准
     */
    @Override
    public CheckMsg<List<ExpStandSnapshotsFrontVO>> getBillExpenseStand(PcxBillExpenseStandQO qo) {
        //获取费用标准json列表
        List<PcxBillExpStandResult> standList = pcxBillExpStandResultDao.selectListByBillId(qo.getBillId());
        List<ExpStandSnapshotVO> expStandSnapshotVOS = standList.stream().filter(item -> StringUtil.isNotEmpty(item.getStandSnapshot())).map(item -> JSONObject.parseObject(item.getStandSnapshot(), ExpStandSnapshotVO.class)).collect(Collectors.toList());
        // 根据费用类型code和condition分组
        Map<ExpStandSnapshotValueEqualsVO, List<ExpStandSnapshotVO>> expStandSnapshotVOMap = expStandSnapshotVOS.stream().collect(Collectors.groupingBy(item -> new ExpStandSnapshotValueEqualsVO(item.getExpenseTypeCode(), item.getCondition())));
        // 汇总各个标准值
        List<ExpStandSnapshotsFrontVO> result = new ArrayList<>();
        List<ExpStandSnapshotsFrontVO> finalResult = result;
        expStandSnapshotVOMap.values().forEach(item -> {
            // 对结果去重
            item = item.stream().distinct().collect(Collectors.toList());
            for (int i = 0; i < item.size(); i++) {
                ExpStandSnapshotVO expStandSnapshotVO = item.get(i);
                List<ExpStandSnapshotValueVO> standValues = expStandSnapshotVO.getStandValue();
                standValues = standValues.stream().filter(it -> expStandSnapshotVO.getRealColumnValue().equals(it.getColumnValueCode()) && expStandSnapshotVO.getRealRowValue().equals(it.getRowValueCode())).collect(Collectors.toList());
                ExpStandSnapshotsFrontVO expStandSnapshotsFrontVO = new ExpStandSnapshotsFrontVO();
                BeanUtils.copyProperties(expStandSnapshotVO, expStandSnapshotsFrontVO);
                if (StringUtil.isNotEmpty(expStandSnapshotsFrontVO.getValueEditorCode()) && expStandSnapshotsFrontVO.getValueEditorCode().equals("input")) {
                    // 手动输入
                    expStandSnapshotsFrontVO.setValueSource("");
                }
                // 添加标准、旺季
                if (expStandSnapshotVO.getExpenseTypeCode().equals("3021102")) {
                    String typeName = expStandSnapshotsFrontVO.getExpenseTypeName();
                    expStandSnapshotsFrontVO.setExpenseTypeName(typeName + "（标准）");
                    if (com.pty.pub.common.util.CollectionUtil.isNotEmpty(expStandSnapshotVO.getCondition())&&StringUtil.isNotBlank(expStandSnapshotVO.getCondition().get(0).getCondValueName())) {
                        expStandSnapshotsFrontVO.setExpenseTypeName(typeName + "（旺季）");
                    }
                }
                // 自定义补助加标准名
                if (expStandSnapshotVO.getExpenseTypeCode().equals("3021103") && expStandSnapshotVO.getSeq()!=0) {
                    expStandSnapshotsFrontVO.setExpenseTypeName(expStandSnapshotVO.getExpenseTypeName() + "（" + expStandSnapshotVO.getStandName()+"）");
                }

                AtomicBoolean isExist = new AtomicBoolean(false);
                // 拼接table
                // 根据column分组
                Map<String, List<ExpStandSnapshotValueVO>> standValueMap = standValues.stream().collect(Collectors.groupingBy(ExpStandSnapshotValueVO::getColumnValueName));
                List<List<ExpStandSnapshotValueVO>> standValuesList = new ArrayList<>();
                List<ExpStandSnapshotValueVO> snapshotValueVOS = new ArrayList<>();
                standValueMap.forEach((key, value) -> {
                    // 第一行表头
                    if (CollectionUtils.isEmpty(standValuesList)) {
                        List<String> rowValueNames = value.stream().map(row -> row.getRowValueName()).collect(Collectors.toList());
                        rowValueNames.add(0, expStandSnapshotVO.getColumnKeyName());
                        for (String rowValueName : rowValueNames) {
                            ExpStandSnapshotValueVO rowNameVO = new ExpStandSnapshotValueVO();
                            rowNameVO.setStandValue(rowValueName);
                            snapshotValueVOS.add(rowNameVO);
                        }
                    }

                    // 其他行数据
                    ExpStandSnapshotValueVO rowNameVO = new ExpStandSnapshotValueVO();
                    rowNameVO.setStandValue(key);
                    value.add(0, rowNameVO);

                    // 表头相同的合并
                    finalResult.forEach(tableItem -> {
                        List<ExpStandSnapshotValueVO> expStandSnapshotValueVOS = tableItem.getStandValue().get(0);
                        if(expStandSnapshotValueVOS.equals(snapshotValueVOS) && expStandSnapshotsFrontVO.getExpenseTypeName().equals(tableItem.getExpenseTypeName())){
                            tableItem.getStandValue().add(value);
                            isExist.set(true);
                        }
                    });

                    if(!isExist.get()){
                        // 添加第一行表头
                        if (CollectionUtils.isEmpty(standValuesList)) {
                            standValuesList.add(0, snapshotValueVOS);
                        }

                        // 添加其他行数据
                        standValuesList.add(value);
                    }
                });
                // 新增表
                if(!isExist.get()){
                    expStandSnapshotsFrontVO.setStandValue(standValuesList);
                    finalResult.add(expStandSnapshotsFrontVO);
                }
            }
        });
        // 结果排序
        result = result.stream()
                .sorted(Comparator.comparing(ExpStandSnapshotsFrontVO::getExpenseTypeName).reversed())
                .sorted(Comparator.comparing(ExpStandSnapshotsFrontVO::getExpenseTypeCode))
                .collect(Collectors.toList());
        return CheckMsg.success(result);
    }

    private CheckMsg<?> validateRequiredFields(PcxBillQO qo) {
        if (StringUtil.isEmpty(qo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getFiscal())) {
            return CheckMsg.fail("年度不能为空");
        }
        return CheckMsg.success();
    }

    /**
     * 动态保存信息
     *
     * @param qo
     * @param pcxBill
     * @return
     */
    @NotNull
    private void processSave(PcxBillQO qo, PcxBill pcxBill) {
        boolean flag = true;//防止重复处理
        for (String item : qo.getClassifyCodes()) {
            PositionBlockEnum blockEnum = PositionBlockEnum.getByCode(item);
            switch (Objects.requireNonNull(blockEnum)) {
                case SPECIFICITY:
                    if (flag) {
                        //处理费用和费用明细信息
                        billExpenseCommonService.processExpense(qo, pcxBill);
                        flag = false;
                    }
                case EXPENSE_DETAIL:
                case ECSEXPMATCH:
                    if (flag) {
                        //处理费用和费用明细信息
                        billExpenseCommonService.processExpense(qo, pcxBill);
                        flag = false;
                    }
                    //通用报销审核处理票的查验状态
                    //通用的票核验状态已经实时更新了，不需要在这里在更新一次，而且前端传过来的票的核验状态已经是错的了
                    //ecsExpCommonService.updateEcsCheckStatus(qo.getEcsExpMatch(), pcxBill);
                    break;
                case ATTACH_LIST:
                    //处理附件信息
                    billExpenseCommonService.processAttach(qo.getAttachList(), pcxBill);
                    break;
                case FUND_SOURCE:
                    //指标经费来源处理
                    billExpenseCommonService.processBalance(qo.getBudget(), pcxBill,qo.getPositionCode());
                    break;
                case SETTLEMENT:
                    //处理结算信息
                    billExpenseCommonService.processSettlement(qo.getSettlement(), pcxBill,qo.getPositionCode());
                    break;
                case LOAN:
                    //处理关联单据信息（处理借款）
                    billExpenseCommonService.processLoanRelation(qo.getLoan(), pcxBill, qo.getPositionCode());
                    break;
                case APPLY:
                    //处理关联单据信息（申请单）
                    billExpenseCommonService.processApplyRelation(qo, pcxBill);
                    break;
                case PAY_DETAIL:
                    //处理支付明细
                    if (PositionEnum.isFinance(qo.getPositionCode()))
                        billExpenseCommonService.processPayDetail(qo.getPayDetail(), pcxBill);
                    break;
                case CONTRACT:
                    //保存关联合同与付款计划
                    pcxBillContractRelService.updateContractRel(qo.getContract(), pcxBill);
                    break;
                case VISITORS:
                    //处理来访人员
                    pcxBillExpAssService.processPcxBillExpAss(qo.getVisitors(), pcxBill, PcxBillProcessConstant.PcxBillExpAss.VISITORS.getCode());
                    break;
                case ARRANGE:
                    //处理接待安排
                    pcxBillExpAssService.processPcxBillExpAss(qo.getArrange(), pcxBill,  PcxBillProcessConstant.PcxBillExpAss.ARRANGE.getCode());
                    break;
                case PLAN:
                    //处理关联单据信息（处理计划、公函）
                    billExpenseCommonService.processPlanRelation(qo.getPlan(), pcxBill, qo.getPositionCode());
                    break;
                case LETTER:
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 动态查看
     *
     * @param pcxBill
     * @param pcxBillVO
     * @return
     */
    @NotNull
    private void viewProcess(PcxBill pcxBill, PcxBillVO pcxBillVO, List<String> viewType) {
        // 按照 PositionBlockEnum 的索引排序 viewType
        viewType.sort(Comparator.comparing(PositionBlockEnum::getIndex));
        String positionCode = ThreadLocalUtil.get();

        // 标志位用于防止重复处理 expenseDetail 和 fundSource
        boolean isExpenseDetailProcessed = false;
        boolean isFundSourceProcessed = false;

        for (String type : viewType) {
            PositionBlockEnum blockEnum = PositionBlockEnum.getByCode(type);
            if (blockEnum == null) {
                continue; // 跳过无法识别的类型
            }

            switch (blockEnum) {
                case SPECIFICITY:
                    List<PcxBillExpBase> expenseList = billExpenseCommonService.getExpenseList(pcxBill);
                    pcxBillVO.setSpecificity(expenseList);
                    break;
                case EXPENSE_DETAIL:
                case ECSEXPMATCH:
                    if (!isExpenseDetailProcessed) {
                        List<PcxBillExpDetailBase> expenseDetailList = billExpenseCommonService.getExpenseDetailList(pcxBill);
                        disposeNoEcsReasonName(expenseDetailList);
                        pcxBillVO.setExpenseDetail(expenseDetailList);

                        Pair<EcsExpMatchVO, List<BillTripVO>> pair = ecsExpOptService.ecsExpMatchAndTripVo(pcxBill, false, null);
                        pcxBillVO.setEcsExpMatch(pair.getLeft());
                        pcxBillVO.setTripList(pair.getRight());
                        isExpenseDetailProcessed = true;
                    }
                    break;
                case ATTACH_LIST:
                    List<PcxBillAttachRelationVO> attachList = billExpenseCommonService.getAttachList(pcxBill);
                    // TODO 使用partitioningBy一次性完成分类(目前在回显的时候坐类型过滤区分了，但是其他处暂未处理，待确定)
                    Map<Boolean, List<PcxBillAttachRelationVO>> partitioned = attachList.stream()
                            .collect(Collectors.partitioningBy(attach -> PcxConstant.ATTACH_TYPE_FOREX_RECEIPT.equals(attach.getAttachTypeId())));
                    pcxBillVO.setAttachList(partitioned.getOrDefault(false, Collections.emptyList()));
                    pcxBillVO.setReceiptList(partitioned.getOrDefault(true, Collections.emptyList()));
                    break;
                case FUND_SOURCE:
                case BUDGET:
                    if (!isFundSourceProcessed) {
                        List<PcxBillBalanceVO> fundSource = billExpenseCommonService.getFundSource(pcxBill);
                        pcxBillVO.setBudget(fundSource);
                        isFundSourceProcessed = true;
                    }
                    break;
                case SETTLEMENT:
                    List<PcxBillSettlementVO> settlement = billExpenseCommonService.getSettlement(pcxBill);
                    pcxBillVO.setSettlements(settlement);
                    break;
                case LOAN:
                    List<PcxBillRelationVO> loanRelation = billExpenseCommonService.getLoanRelation(pcxBill);
                    pcxBillVO.setLoan(loanRelation);
                    break;
                case APPLY:
                    PcxBillRelationVO applyRelation = billExpenseCommonService.getApplyRelation(pcxBill);
                    pcxBillVO.setApply(applyRelation);
                    break;
                case APPROVAL_PROCESS:
                    ProcessHistoryQO qo = new ProcessHistoryQO();
                    qo.setBillId(pcxBill.getId());
                    qo.setBillFuncCode(pcxBill.getBillFuncCode());
                    qo.setAgyCode(pcxBill.getAgyCode());
                    qo.setMofDivCode(pcxBill.getMofDivCode());
                    CheckMsg<ProcessCompositeVO> listCheckMsg = processService.listHistoryWithDelegate(qo);
                    if (listCheckMsg.isSuccess()) {
                        pcxBillVO.setApprovalProcess(listCheckMsg.getData().getApprovalProcess());
                        pcxBillVO.setMyDelegatedProcess(listCheckMsg.getData().getMyDelegatedProcess());
                        pcxBillVO.setDelegateUserName(listCheckMsg.getData().getDelegateUserName());
                    }
                    break;
                case PAY_DETAIL:
                    if (PositionEnum.isFinance(positionCode)) {
                        List<PcxBillBalanceVO> budget = pcxBillVO.getBudget();
                        CheckMsg<PayDetailVO> voCheckMsg = payDetailService.calculatePayDetails(
                                PayDetailQO.builder()
                                        .billId(pcxBill.getId())
                                        .allocateType(PayDetailQO.AllocateTypeEnum.ORDER)
                                        .balanceList(budget)
                                        .build(), true);
                        pcxBillVO.setPayDetail(voCheckMsg.getData());
                    }
                    break;
                case CONTRACT:
                    ContractVO contractVO = pcxBillContractRelService.getBillContract(pcxBill);
                    pcxBillVO.setContract(contractVO);
                    break;
                case VISITORS:
                    pcxBillVO.setVisitors(pcxBillExpAssService.selectList(pcxBill, PcxBillProcessConstant.PcxBillExpAss.VISITORS.getCode()));
                    break;
                case ARRANGE:
                    pcxBillVO.setArrange(pcxBillExpAssService.selectList(pcxBill, PcxBillProcessConstant.PcxBillExpAss.ARRANGE.getCode()));
                    break;
                case PLAN:
                    PcxBillRelationVO planRelation = billExpenseCommonService.getPlanRelation(pcxBill);
                    pcxBillVO.setPlan(planRelation);
                    break;
                default:
                    break;
            }
        }
    }


    private void disposeNoEcsReasonName(List<PcxBillExpDetailBase> expenseDetailList) {
        if (CollectionUtils.isNotEmpty(expenseDetailList)) {
            for (PcxBillExpDetailBase detailBase : expenseDetailList) {
                detailBase.setNoEcsReasonName(NoEcsReasonEnum.getDescByCode(detailBase.getExpDetailCode(), detailBase.getNoEcsReason()));
            }
        }
    }



    private void syncAttachToEcs(PcxBill pcxBill) {

        List<PcxBillAttachRelationVO>  attachList = billExpenseCommonService.getAttachList(pcxBill);
        if (attachList == null || attachList.isEmpty()) {
            attachList = new ArrayList<>();
        }
        try {
            PcxBillRelationVO apply = billExpenseCommonService.getApplyRelation(pcxBill);
            if (apply != null && apply.getIsVirtual() == 1) {
                PcxBillAttachRelationVO applyAttach = new PcxBillAttachRelationVO();
                applyAttach.setAttachId(apply.getRelBillId());
                attachList.add(applyAttach);
            }
        }catch (Exception e){
            log.error("获取申请单附件失败，bilId {}",pcxBill.getId(), e);
        }

        //加载集合规则附件
        WitRuleResult result = new WitRuleResult();
        result.setBillId(pcxBill.getId());
        result.setFiscal(pcxBill.getFiscal());
        result.setAgyCode(pcxBill.getAgyCode());
        List<String> attaachIds = witAuditRuleService.queryRuleAttach(result);
        if (CollectionUtils.isNotEmpty(attaachIds)){
            for (String attaachId : attaachIds) {
                PcxBillAttachRelationVO applyAttach = new PcxBillAttachRelationVO();
                applyAttach.setAttachId(attaachId);
                attachList.add(applyAttach);
            }
        }

        try {
            if (!attachList.isEmpty()) {
                ecsExpTransService.bindEcsRel(pcxBill, attachList);
            }
        }catch (Exception e){
            log.error("同步附件清单失败，bilId {}",pcxBill.getId(), e);
        }
    }

}
