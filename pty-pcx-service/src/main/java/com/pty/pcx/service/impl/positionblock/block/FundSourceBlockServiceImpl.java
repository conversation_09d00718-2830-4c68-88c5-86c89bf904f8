package com.pty.pcx.service.impl.positionblock.block;


import com.pty.pcx.api.positionblock.IPcxBlockService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.FundPositionEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.dto.block.BlockProperty;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class FundSourceBlockServiceImpl extends AbstractBlockService implements IPcxBlockService {

    private static final Set<String> FIEXD_FIELD_SET = new HashSet<>(Arrays.asList("totalAmt", "balanceAmt"));
    @Override
    public List<PcxBlockVO> getBlockInfo(PcxBlockCondQO qo) {
        Map<String, PcxBasExpType> expenseMap = qo.getExpenseMap();
        List<BlockProperty> blockProperties = getBlockProperties(qo);
        Map<String, List<BlockProperty>> groupedProperties = blockProperties.stream()
                .collect(Collectors.groupingBy(BlockProperty::getFormCode));
        List<PcxBlockVO> result = new ArrayList<>();
        boolean isShowAmt = isShowAmt(qo);
        FundPositionEnum fundPosition = getFundPosition(qo);
        String isEdit = getIsEdit(fundPosition, qo);
        for (Map.Entry<String, List<BlockProperty>> entry : groupedProperties.entrySet()) {
            String formCode = entry.getKey();
            List<BlockProperty> propertyList = entry.getValue();
            PcxBlockVO blockVO = new PcxBlockVO();
            blockVO.setClassifyCode(qo.getClassifyCode());
            blockVO.setClassifyName(qo.getClassifyName());
            blockVO.setBlockCode(formCode);
            String blockName = Optional.ofNullable(expenseMap.get(formCode))
                    .map(PcxBasExpType::getExpenseName)
                    .orElse(StringUtil.EMPTY);
            if (CollectionUtil.isNotEmpty(propertyList)) {
                if(StringUtil.isEmpty(blockName)){
                    blockName = propertyList.get(0).getFormName();
                }
                blockVO.setProperties(convertToBlockPropertyVO(propertyList,isShowAmt));
            }
            blockVO.setBlockName(blockName);
            blockVO.setArea(qo.getArea());
            Map<String, String> extBlockMap = new HashMap<>();
            extBlockMap.put(EDIT_KEY,isEdit);
            blockVO.setBlockExtMap(extBlockMap);
            blockVO.setBlockTitle(qo.getShowName());
            result.add(blockVO);
        }
        return result;
    }

    protected List<BlockPropertyVO> convertToBlockPropertyVO(List<BlockProperty> blockProperties, boolean isShowAmt) {
        if (CollectionUtil.isEmpty(blockProperties)) {
            return new ArrayList<>();
        }
        List<BlockPropertyVO> result = new ArrayList<>();
        for (BlockProperty blockProperty : blockProperties) {
            if (ObjectUtils.isEmpty(blockProperty.getEditorCode())) {
                continue;
            }
            if(FIEXD_FIELD_SET.contains(blockProperty.getFieldValue()) && !isShowAmt){
                continue;
            }
            BlockPropertyVO blockPropertyVO = new BlockPropertyVO();
            BeanUtils.copyProperties(blockProperty, blockPropertyVO);
            result.add(blockPropertyVO);
        }
        return result;
    }

    /****
     * 是否展示金额字段
     * @param pcxPositionBlockQO
     * @return
     */
    private boolean isShowAmt(PcxBlockCondQO pcxPositionBlockQO){
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxPositionBlockQO.getMofDivCode());
        paOptionQO.setAgyCode(pcxPositionBlockQO.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxPositionBlockQO.getFiscal()));
        paOptionQO.setTenantId(StringUtil.isEmpty(pcxPositionBlockQO.getTenantId()) ? PtyContext.getTenantId() : pcxPositionBlockQO.getTenantId());
        String optCode = bussinessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.ALLOWED_SHOW_AMOUNT.getOptCode());
        return PubConstant.STR_LOGIC_TRUE.equals(optCode);
    }

    /****
     * 块儿是否可以编辑
     * @param fundPosition
     * @param qo
     * @return
     */
    private String getIsEdit(FundPositionEnum fundPosition, PcxBlockCondQO qo) {
        // 如果是财务岗、财务总监岗或者预算岗，对经费来源块儿有编辑权利
        if(PositionEnum.isFinance(qo.getPositionCode()) || PcxNodeEnum.budget_confirm.getId().equals(qo.getPositionCode())){
            return PubConstant.STR_LOGIC_TRUE;
        }
        if(ObjectUtils.isEmpty(fundPosition)){
            return PubConstant.STR_LOGIC_FALSE;
        }
        if(PubConstant.STR_LOGIC_TRUE.equals(fundPosition.getIsCarryProject()) && qo.getPositionCode().equals(fundPosition.getPosition())){
            return PubConstant.STR_LOGIC_TRUE;
        }
        return PubConstant.STR_LOGIC_FALSE;
    }
}
