package com.pty.pcx.service.impl.remind;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.pty.pa.portal.pojo.RemindData;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.remind.IPcxRemindService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bill.PcxBillListQO;
import com.pty.pcx.vo.bill.PcxBillListVO;
import com.pty.pcx.vo.bill.PcxBillPageResult;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.util.PtyContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PcxTodoTaskRemindService implements IPcxRemindService {


    @Autowired
    private PcxBillService pcxBillService;


    @Override
    public RemindData getRemindData(Map<String, Object> params) {
        // 获取待办
        // 获取所有待办任务
        log.info("待办任务获取开始{}", params);
        PcxBillListQO billListQO = new PcxBillListQO();
        billListQO.setMofDivCode(params.get("mofDivCode").toString());
        billListQO.setAgyCode(params.get("agyCode").toString());
        billListQO.setFiscal(params.get("fiscal").toString());
        billListQO.setLabel(PcxBillProcessConstant.BillIndexLabel.APPROVE);
        billListQO.setPageIndex(1);
        billListQO.setPageSize(Integer.MAX_VALUE);
        billListQO.setOnlyFinance(Boolean.TRUE);
        PtyContext.setTenantId(StrUtil.firstNonBlank(PtyContext.getTenantId(), "cx"));
        CheckMsg<PcxBillPageResult<PcxBillListVO>> msg = pcxBillService.selectWithPage(billListQO);
        RemindData result = RemindData.create().id("pcx_remind").purpose("待审核").systemId(PcxConstant.SYS_ID.toLowerCase()).title("报销系统单据审核");
        if (!msg.isSuccess())
            return result.content(Collections.emptyList()).count(0);

        return result.content(msg.getData().getResult().stream().map(bill -> new JSONObject()
                .fluentPut("billId", bill.getBillId())
                .fluentPut("desc", String.format("%s-%s-%s-%s-%s", bill.getBillFuncName(), bill.getClaimantName(), bill.getItemName(), bill.getCheckAmt(), bill.getBillNo()))
                .fluentPut("billNo", StrUtil.EMPTY)).collect(Collectors.toList())).count(Math.toIntExact(msg.getData().getTotal()));
    }
}
