package com.pty.pcx.service.impl.bill;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pty.pcx.api.bill.PcxBillExpDetailTravelService;
import com.pty.pcx.dao.bill.PcxBillExpDetailTravelDao;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 单据费用明细_差旅费_明细(PcxBillExpDetailTravel)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:04
 */
@Service("pcxBillExpDetailTravelService")
@Indexed
public class PcxBillExpDetailTravelServiceImpl implements PcxBillExpDetailTravelService {

    @Resource
    private PcxBillExpDetailTravelDao pcxBillExpDetailTravelDao;
    @Override
    public List<PcxBillExpDetailTravel> selectList(List<String> billIdList, String agyCode, String fiscal, String mofDivCode) {
        return pcxBillExpDetailTravelDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailTravel>()
                .in(PcxBillExpDetailTravel::getBillId, billIdList)
                .eq(Objects.nonNull(agyCode), PcxBillExpDetailTravel::getAgyCode, agyCode)
                .eq(PcxBillExpDetailTravel::getFiscal, fiscal)
                .eq(Objects.nonNull(mofDivCode), PcxBillExpDetailTravel::getMofDivCode, mofDivCode));
    }

    @Override
    public BigDecimal sumYearSub(String empCode) {
        return pcxBillExpDetailTravelDao.sumYearSub(DateUtil.formatDate(DateUtil.beginOfYear(new Date())), empCode);
    }

}
