package com.pty.pcx.service.impl.stand;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pty.mad.entity.PaValset;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.stand.PcxStandKeyService;
import com.pty.pcx.api.stand.PcxStandValueService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasCityClassifyDao;
import com.pty.pcx.dao.stand.PcxStandConditionDao;
import com.pty.pcx.dao.stand.PcxStandKeyDao;
import com.pty.pcx.dao.stand.PcxStandValueDao;
import com.pty.pcx.dto.PcxCarryOverDTO;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.setting.PaOption;
import com.pty.pcx.entity.stand.PcxStandCondition;
import com.pty.pcx.entity.stand.PcxStandKey;
import com.pty.pcx.entity.stand.PcxStandValue;
import com.pty.pcx.entity.stand.qo.PcxStandQO;
import com.pty.pcx.entity.stand.qo.PcxStandQueryQO;
import com.pty.pcx.entity.stand.qo.PcxStandShowButtonQO;
import com.pty.pcx.entity.stand.vo.PcxStandConditionVO;
import com.pty.pcx.entity.stand.vo.PcxStandVO;
import com.pty.pcx.entity.stand.vo.PcxStandValueVO;
import com.pty.pcx.qo.bill.BillExpenseStandQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.service.impl.bas.TransOptService;
import com.pty.pcx.vo.bill.ExpStandVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.pty.mad.api.IPaValsetService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import springfox.documentation.service.ApiListing;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 支出标准要素定义表(PcxStandKey)表服务实现类
 * <AUTHOR>
 * @since 2024-11-04 14:58:12
 */
@Slf4j
@Indexed
@Service
public class PcxStandKeyServiceImpl implements PcxStandKeyService {

	@Autowired
	private PcxStandKeyDao pcxStandKeyDao;
	@Autowired
	private PcxStandConditionDao pcxStandConditionDao;
	@Autowired
	private PcxStandValueDao pcxStandValueDao;
	@Autowired
	private PcxStandValueService pcxStandValueService;
	@Autowired
	private TransOptService transOptService;
	@Autowired
	private IBusinessRuleOptionService businessRuleOptionService;
	@Autowired
	private PcxBasCityClassifyDao pcxBasCityClassifyDao;
	@Autowired
	@RestClientReference(microServiceNames = {"mad"})
	private IPaValsetService paValsetService;




	@Override
	public CheckMsg<List<PcxStandVO>> selectFixedStand(PcxStandQueryQO pcxStandQueryQO) {
		//seq=0的为标准固定数据
		return selectStandForFilter(pcxStandQueryQO, a->a.getSeq() < 0);
	}

	private void collectStandQo(List<PcxStandKey> pcxStandKeys, List<PcxStandCondition> pcxStandConditions,
								List<PcxStandValue> pcxStandValues, List<PcxStandVO> result) {
		Map<String, List<PcxStandCondition>> conditionMap = pcxStandConditions.stream().collect(Collectors.groupingBy(PcxStandCondition::getStandCode));
		Map<String, List<PcxStandValue>> valueMap = pcxStandValues.stream().collect(Collectors.groupingBy(PcxStandValue::getStandCode));

		for (PcxStandKey pcxStandKey : pcxStandKeys) {
			PcxStandVO standQO = new PcxStandVO();

			result.add(standQO);

			BeanUtils.copyProperties(pcxStandKey, standQO);

			//解析行和列的value数据
			analysisRowAndColumn(standQO);

			//填充条件和值数据
			List<PcxStandConditionVO> conditionQOList = new ArrayList<>();
			standQO.setConditionList(conditionQOList);
			List<PcxStandValueVO> valueQOList = new ArrayList<>();
			standQO.setStandValueList(valueQOList);

			List<PcxStandCondition> conditionList = conditionMap.get(pcxStandKey.getStandCode());
			if (CollectionUtils.isNotEmpty(conditionList)){
				for (PcxStandCondition pcxStandCondition : conditionList) {
					PcxStandConditionVO conditionQO = new PcxStandConditionVO();
					BeanUtils.copyProperties(pcxStandCondition, conditionQO);
					conditionQOList.add(conditionQO);
				}
			}
			List<PcxStandValue> valueList = valueMap.get(pcxStandKey.getStandCode());
			if (CollectionUtils.isNotEmpty(valueList)){
				for (PcxStandValue pcxStandValue : valueList) {
					PcxStandValueVO valueQO = new PcxStandValueVO();
					BeanUtils.copyProperties(pcxStandValue, valueQO);
					valueQOList.add(valueQO);
				}
			}
		}
	}

	private void analysisRowAndColumn(PcxStandVO standQO) {
		String[] rowValueCodes = standQO.getRowValueCode().split(",");
		String[] rowValueNames = standQO.getRowValueName().split(",");
		for (int i = 0; i < rowValueNames.length; i++) {
			// cityclassify根据code获取城市列表
			standQO.getRowValueList().add(new PcxStandVO.NameAndCode(rowValueNames[i]+getCityList(standQO,1,rowValueCodes[i]), rowValueCodes[i]));
		}
		String[] columnValueCodes = standQO.getColumnValueCode().split(",");
		String[] columnValueNames = standQO.getColumnValueName().split(",");
		for (int i = 0; i < columnValueNames.length; i++) {
			// cityclassify根据code获取城市列表
			standQO.getColumnValueList().add(new PcxStandVO.NameAndCode(columnValueNames[i]+getCityList(standQO,2,columnValueCodes[i]), columnValueCodes[i]));
		}
	}

	/**
	 * 获取城市列表字符串
	 * @param cityClassifyCode
	 * @return
	 */
	private String getCityList(PcxStandVO standQO,int type, String cityClassifyCode){
		if(type==1 && !standQO.getRowKeyCode().equals("cityClassify")){
			return "";
		}
		if(type==2 && !standQO.getColumnKeyCode().equals("cityClassify")){
			return "";
		}
		PcxBasCityClassify pcxBasCityClassify = new PcxBasCityClassify();
		pcxBasCityClassify.setClassifyCode(cityClassifyCode);
		pcxBasCityClassify.setAgyCode(standQO.getAgyCode());
		pcxBasCityClassify.setFiscal(standQO.getFiscal());
		pcxBasCityClassify.setMofDivCode(standQO.getMofDivCode());
		List<PcxBasCityClassify> pcxBasCityClassifies = pcxBasCityClassifyDao.selectList(pcxBasCityClassify);
		return "  " + pcxBasCityClassifies.stream().map(PcxBasCityClassify::getDataName).collect(Collectors.joining(", "));
	}

	private CheckMsg validSelectStand(PcxStandQueryQO pcxStandQueryQO) {
		if (Objects.isNull(pcxStandQueryQO)){
			return CheckMsg.fail("参数为空");
		}
		if(StringUtil.isEmpty(pcxStandQueryQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQueryQO.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQueryQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(pcxStandQueryQO.getExpenseTypeCode())){
			return CheckMsg.fail("费用类型编码不能为空");
		}
		return CheckMsg.success();
	}

	public List<PcxStandKey> queryStandKeyByExpenseTypeCode(PcxStandQueryQO pcxStandQueryQO){
		// 创建一个PcxStandKey对象
		PcxStandKey newPcxStandKey = new PcxStandKey();

		// 把pcxStandKey中的expenseTypeCode、agyCode、fiscal、mofDivCode赋值给它
		newPcxStandKey.setExpenseTypeCode(pcxStandQueryQO.getExpenseTypeCode());
		newPcxStandKey.setAgyCode(pcxStandQueryQO.getAgyCode());
		newPcxStandKey.setFiscal(pcxStandQueryQO.getFiscal());
		newPcxStandKey.setMofDivCode(pcxStandQueryQO.getMofDivCode());

		// 使用这个对象作为参数调用pcxStandKeyDao的selectList方法
		return pcxStandKeyDao.selectList(Wrappers.query(newPcxStandKey));
	}

	@Override
	public CheckMsg<List<PcxStandVO>> selectCommonStand(PcxStandQueryQO pcxStandQueryQO) {
		//seq!=0的为添加的标准
		return selectStandForFilter(pcxStandQueryQO, a->a.getSeq() >=0);
	}

	private CheckMsg<List<PcxStandVO>> selectStandForFilter(PcxStandQueryQO pcxStandQueryQO, Predicate<PcxStandKey> filter) {
		//校验查询参数
		CheckMsg checkMsg = validSelectStand(pcxStandQueryQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		//查询标准，条件，值
		List<PcxStandKey> pcxStandKeys = queryStandKeyByExpenseTypeCode(pcxStandQueryQO);

		pcxStandKeys = pcxStandKeys.stream().filter(filter).sorted(Comparator.comparingInt(PcxStandKey::getSeq)).collect(Collectors.toList());
		List<PcxStandVO> result = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(pcxStandKeys)){
			List<String> standCodeList = pcxStandKeys.stream().map(PcxStandKey::getStandCode).collect(Collectors.toList());
			//查询标准的表单配置数据，组装筛选条件
			List<PcxStandValue> pcxStandValues = pcxStandValueDao.queryStandStandValueList(pcxStandQueryQO.getAgyCode(), pcxStandQueryQO.getFiscal(),
					pcxStandQueryQO.getMofDivCode(), standCodeList);
			List<PcxStandCondition> pcxStandConditions = pcxStandConditionDao.queryStandConditionList(pcxStandQueryQO.getAgyCode(), pcxStandQueryQO.getFiscal(),
					pcxStandQueryQO.getMofDivCode(), standCodeList);
			//组装数据
			collectStandQo(pcxStandKeys, pcxStandConditions, pcxStandValues, result);
		}

		specialStand(result);

		//组装成给前端展示的结构
		return CheckMsg.success(result);
	}

	List<PcxStandVO.NameAndCode> controlModeList = Arrays.asList(new PcxStandVO.NameAndCode("控制","1"),
			new PcxStandVO.NameAndCode("提醒","2"),new PcxStandVO.NameAndCode("不控制","3"));

	private void specialStand(List<PcxStandVO> result) {
		if (CollectionUtils.isEmpty(result)){
			return;
		}
		List<PcxStandVO> specialList = result.stream()
				.filter(item->Arrays.asList("30215","3021501").contains(item.getExpenseTypeCode())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(specialList)){
			return;
		}
		for (PcxStandVO vo : specialList) {
			for (int i = 0; i < vo.getColumnValueList().size(); i += 2) {
				PcxStandVO.NameAndCode one = vo.getColumnValueList().get(i);
				PcxStandVO.NameAndCode two = vo.getColumnValueList().get(i+1);
				PcxStandVO.CombineColumn combineColumn = new PcxStandVO.CombineColumn();
				String name = i == 0? one.getName().substring(0, 9):one.getName().substring(0,4);
				combineColumn.setName(name);
				combineColumn.getChildren().add(new PcxStandVO.CombineItem(one.getCode(), one.getName().replace(name, ""),"input", null));
				combineColumn.getChildren().add(new PcxStandVO.CombineItem(two.getCode(), two.getName().replace(name, ""),"select", controlModeList));
				vo.getCombineColumnList().add(combineColumn);
			}
		}
	}

	@Override
	public CheckMsg<String> delStand(PcxStandQueryQO pcxStandQueryQO) {
		//校验参数
		CheckMsg checkMsg = isDeleteValid(pcxStandQueryQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		//调用queryStandKey查询标准信息
		//标准信息如果为null则返回Response.fail().setMsg("标准不存在");
//		PcxStandKey pcxStandKey = pcxStandKeyDao.selectByStandCode(pcxStandQueryQO.getStandCode());
		Page<PcxStandKey> page = pcxStandKeyDao.selectPage(new Page<>(1, 1), new LambdaQueryWrapper<PcxStandKey>()
				.eq(PcxStandKey::getStandCode, pcxStandQueryQO.getStandCode()));
		if (page.getRecords().isEmpty()){
			return CheckMsg.fail("未查询到标准信息");
		}
		PcxStandKey pcxStandKey = page.getRecords().get(0);
		//固定标准不能删除
		if (pcxStandKey.getSeq() < 0){
			return CheckMsg.fail("固定标准不能删除");
		}
		//调用pcxStandValueService.delStand删除标准数据，删除条件数据，删除值数据
		pcxStandValueService.delStand(pcxStandKey);
		return CheckMsg.success("删除成功");
	}


	@Override
	public CheckMsg<String> saveStand(PcxStandQO pcxStandQO) {
		//校验保存参数
		CheckMsg checkMsg = isSaveValid(pcxStandQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		//uuid生成standCode
		pcxStandQO.setStandCode(UUID.randomUUID().toString());
		pcxStandQO.setId(IDGenerator.id());
		//查询当前费用类型支出标准最大序号
		int maxSeq = pcxStandKeyDao.selectMaxSeq(pcxStandQO.getExpenseTypeCode(),pcxStandQO.getAgyCode(),
				pcxStandQO.getFiscal(),pcxStandQO.getMofDivCode(),pcxStandQO.getTenantId());
		//最大序号+1赋值给新增数据
		if (pcxStandQO.getSeq() !=null){
			pcxStandQO.setSeq(pcxStandQO.getSeq());
		}else{
			pcxStandQO.setSeq(maxSeq+1);
		}
		pcxStandQO.setCreater(pcxStandQO.getUserCode());
		pcxStandQO.setCreateTime(DateUtil.nowTime());
		pcxStandQO.setUpdater(pcxStandQO.getUserCode());
		pcxStandQO.setUpdateTime(DateUtil.nowTime());

		//查询条件集合赋值standCode，标准值集合赋值standCode
		fillConditionAndValueBaseMessage(pcxStandQO);

		//插入pcxStandKey数据，插入pcxStandCondition数据，插入pcxStandValue数据
		pcxStandValueService.insertStand(pcxStandQO);
		return CheckMsg.success("保存成功");
    }

	@Override
	public CheckMsg<String> updateStand(PcxStandQO pcxStandQO) {
		//校验参数
		CheckMsg checkMsg = isUpdateValid(pcxStandQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		//根据id查询标准数据
//		PcxStandKey existsStand = pcxStandKeyDao.selectByStandCode(pcxStandQO.getStandCode());
		long page = pcxStandKeyDao.selectCount(new LambdaQueryWrapper<PcxStandKey>()
				.eq(PcxStandKey::getStandCode, pcxStandQO.getStandCode()));
		if (page < 1){
			return CheckMsg.fail("未查询到标准信息");
		}
		pcxStandQO.setUpdater(pcxStandQO.getUserCode());
		pcxStandQO.setUpdateTime(DateUtil.nowTime());

		//便利筛选条件数据，标准值数据填充属性
		fillConditionAndValueBaseMessage(pcxStandQO);

		//调用pcxStandValueService.updateStand方法，更新标准，删除原有筛选条件，标准值，插入新的筛选条件标准值
		pcxStandValueService.updateStand(pcxStandQO);
		return CheckMsg.success("更新成功");
	}

	private void fillConditionAndValueBaseMessage(PcxStandQO pcxStandQO) {
		if (CollectionUtils.isNotEmpty(pcxStandQO.getConditionList())){
			for (PcxStandConditionVO pcxStandConditionQO : pcxStandQO.getConditionList()) {
				pcxStandConditionQO.setId(IDGenerator.id());
				pcxStandConditionQO.setStandCode(pcxStandQO.getStandCode());
				pcxStandConditionQO.setFiscal(pcxStandQO.getFiscal());
				pcxStandConditionQO.setAgyCode(pcxStandQO.getAgyCode());
				pcxStandConditionQO.setMofDivCode(pcxStandQO.getMofDivCode());
				pcxStandConditionQO.setTenantId(PtyContext.getTenantId());
			}
		}
		if (CollectionUtils.isNotEmpty(pcxStandQO.getStandValueList())){
			for (PcxStandValueVO pcxStandValueQO : pcxStandQO.getStandValueList()) {
				pcxStandValueQO.setId(IDGenerator.id());
				pcxStandValueQO.setStandCode(pcxStandQO.getStandCode());
				pcxStandValueQO.setFiscal(pcxStandQO.getFiscal());
				pcxStandValueQO.setAgyCode(pcxStandQO.getAgyCode());
				pcxStandValueQO.setMofDivCode(pcxStandQO.getMofDivCode());
				pcxStandValueQO.setTenantId(PtyContext.getTenantId());
			}
		}
	}


	@Override
	public CheckMsg<String> updateStandSingleValue(PcxStandValueVO pcxStandValueQO) {
		CheckMsg checkMsg = validUpdateSingleValue(pcxStandValueQO);
		if(!checkMsg.isSuccess()){
			return checkMsg;
		}
		if (0 == pcxStandValueDao.updateValueByStandCode(pcxStandValueQO)){
			return CheckMsg.fail("修改失败");
		}
		return CheckMsg.success("更新成功");
	}

	@Override
	public CheckMsg<PcxStandVO> analysisStand(PcxStandQO pcxStandQO) {
		CheckMsg checkMsg = analysisValid(pcxStandQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		PcxStandVO vo = new PcxStandVO();
		BeanUtils.copyProperties(pcxStandQO, vo);
		List<PcxStandValueVO> valueVOS = new ArrayList<>();
		String rowKeyCode = pcxStandQO.getRowKeyCode();
		String rowKeyName = pcxStandQO.getRowKeyName();
		String columnKeyCode = pcxStandQO.getColumnKeyCode();
		String columnKeyName = pcxStandQO.getColumnKeyName();
		String[] rowValueCodeArr = pcxStandQO.getRowValueCode().split(",");
		String[] rowValueNameArr = pcxStandQO.getRowValueName().split(",");
		String[] columnValueCodeArr = pcxStandQO.getColumnValueCode().split(",");
		String[] columnValueNameArr = pcxStandQO.getColumnValueName().split(",");
		for (int i = 0; i < rowValueCodeArr.length; i++) {
			for (int j = 0; j < columnValueCodeArr.length; j++) {
				PcxStandValueVO pcxStandValueVO = new PcxStandValueVO();
				pcxStandValueVO.setRowKeyCode(rowKeyCode);
				pcxStandValueVO.setRowKeyName(rowKeyName);
				pcxStandValueVO.setColumnKeyCode(columnKeyCode);
				pcxStandValueVO.setColumnKeyName(columnKeyName);
				pcxStandValueVO.setRowValueCode(rowValueCodeArr[i]);
				pcxStandValueVO.setRowValueName(rowValueNameArr[i]);
				pcxStandValueVO.setColumnValueCode(columnValueCodeArr[j]);
				pcxStandValueVO.setColumnValueName(columnValueNameArr[j]);
				pcxStandValueVO.setFiscal(pcxStandQO.getFiscal());
				pcxStandValueVO.setAgyCode(pcxStandQO.getAgyCode());
				pcxStandValueVO.setMofDivCode(pcxStandQO.getMofDivCode());
				pcxStandValueVO.setStandardValue("");
				valueVOS.add(pcxStandValueVO);
			}
		}
		vo.setStandValueList(valueVOS);
		//解析行和列的value数据
		analysisRowAndColumn(vo);
		return CheckMsg.success(vo);
	}

	@Override
	public List<ExpStandVO> billExpenseStandList(BillExpenseStandQO billExpenseStandQO) {
		CheckMsg checkMsg = validBillExpenseStandList(billExpenseStandQO);
		if(!checkMsg.isSuccess()){
			return Collections.emptyList();
		}
		return pcxStandKeyDao.billExpenseStandList(billExpenseStandQO);
	}

	@Override
	public CheckMsg<String> carryOver(PcxCarryOverDTO pcxStandQO) {
		String nextFiscal = Integer.valueOf( pcxStandQO.getFiscal())+1+"";
		List<PcxStandKey> pcxStandKeys = pcxStandKeyDao.selectList(Wrappers.lambdaQuery(PcxStandKey.class)
				.eq(PcxStandKey::getFiscal, nextFiscal));
		if (CollectionUtils.isNotEmpty(pcxStandKeys)){
			return CheckMsg.success("已存在新数据");
		}
		pcxStandKeys = pcxStandKeyDao.selectList(Wrappers.lambdaQuery(PcxStandKey.class)
				.eq(PcxStandKey::getFiscal, pcxStandQO.getFiscal()));
		List<PcxStandCondition> conditionList = pcxStandConditionDao.selectList(Wrappers.lambdaQuery(PcxStandCondition.class)
				.eq(PcxStandCondition::getFiscal, pcxStandQO.getFiscal()));
		List<PcxStandValue> pcxStandValues = pcxStandValueDao.selectList(Wrappers.lambdaQuery(PcxStandValue.class)
				.eq(PcxStandValue::getFiscal, pcxStandQO.getFiscal()));
		MutableTriple<List<PcxStandKey>, List<PcxStandCondition>, List<PcxStandValue>> newData = carryOverNewData(pcxStandKeys, conditionList, pcxStandValues, nextFiscal);

		transOptService.carryOverStand(newData.getLeft(), newData.getMiddle(), newData.getRight());
		return CheckMsg.success("结转完成");
	}

	/**
	 * 获取标准名称和代码
	 * @param pcxStandQueryQO
	 * @return
	 */
	@Override
	public CheckMsg<List<PcxStandKey>> getStandKey(PcxStandQueryQO pcxStandQueryQO) {
		CheckMsg checkMsg = checkParam(pcxStandQueryQO);
		if (!checkMsg.isSuccess()) {
			return checkMsg;
		}
		PaValset paValset = new PaValset();
		paValset.setFiscal(Integer.valueOf(pcxStandQueryQO.getFiscal()));
		paValset.setMofDivCode(pcxStandQueryQO.getMofDivCode());
		paValset.setValsetCode(PcxConstant.SUBSIDY_TYPE);
		paValset.setAgyCode(PcxConstant.SYSTEM_FLAG);
		List<PaValset> paValsets = paValsetService.selectByValsetCode(paValset);
		if (CollectionUtil.isNotEmpty(paValsets)) {
			return CheckMsg.success(paValsets.stream().map(e -> {
				PcxStandKey pcxStandKey = new PcxStandKey();
				pcxStandKey.setStandCode(e.getValCode());
				pcxStandKey.setStandName(e.getVal());
				return pcxStandKey;
			}).collect(Collectors.toList()));
		}
		return CheckMsg.success(new ArrayList<>());
	}

	@Override
	public Response<?> showButton(PcxStandShowButtonQO pcxStandShowButtonQO) {
		String expTypeCode = pcxStandShowButtonQO.getExpTypeCode();
		if(StringUtils.isBlank(expTypeCode)) {
			return Response.success(Collections.emptyList());
		}
		PaOptionQO optionQuery = new PaOptionQO();
		optionQuery.setFiscal(pcxStandShowButtonQO.getFiscal());
		optionQuery.setMofDivCode(pcxStandShowButtonQO.getMofDivCode());
		optionQuery.setAgyCode(pcxStandShowButtonQO.getAgyCode());
		optionQuery.setGroupName("PCX_STAND_BUTTON");
		optionQuery.setFieldValuesetCode(expTypeCode);
		return businessRuleOptionService.selectListByQO(optionQuery);
	}

	private CheckMsg checkParam(PcxStandQueryQO pcxStandQueryQO) {
		if (Objects.isNull(pcxStandQueryQO)){
			return CheckMsg.fail("参数为空");
		}
		if (StringUtil.isEmpty(pcxStandQueryQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if (StringUtil.isEmpty(pcxStandQueryQO.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if (StringUtil.isEmpty(pcxStandQueryQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(pcxStandQueryQO.getExpenseTypeCode())) {
			return CheckMsg.fail("费用类型编码不能为空");
		}
		return CheckMsg.success();
	}

	private MutableTriple<List<PcxStandKey>, List<PcxStandCondition>, List<PcxStandValue>> carryOverNewData(List<PcxStandKey> pcxStandKeys,
																											List<PcxStandCondition> conditionList,
																											List<PcxStandValue> pcxStandValues,
																											String nextFiscal) {
		List<PcxStandKey> newStandKeys = new ArrayList<>();
		Map<String, String> standCodeMap = new HashMap<>();
		for (PcxStandKey pcxStandKey : pcxStandKeys) {
			pcxStandKey.setId(IDGenerator.id());
			String standCode = nextFiscal+"@" + pcxStandKey.getStandCode().substring(5);
			standCodeMap.put(pcxStandKey.getStandCode(), standCode);
			pcxStandKey.setStandCode(standCode);
			String rowCode = disposeCostControlLevel(pcxStandKey.getRowKeyCode(), pcxStandKey.getRowValueCode(), nextFiscal);
			String columnCode = disposeCostControlLevel(pcxStandKey.getColumnKeyCode(), pcxStandKey.getColumnValueCode(), nextFiscal);
			pcxStandKey.setFiscal(nextFiscal);
			pcxStandKey.setRowValueCode(rowCode);
			pcxStandKey.setColumnValueCode(columnCode);
			newStandKeys.add(pcxStandKey);
		}
		List<PcxStandCondition> newStandConditions = new ArrayList<>();
		for (PcxStandCondition condition : conditionList) {
			condition.setId(IDGenerator.id());
			condition.setStandCode(standCodeMap.get(condition.getStandCode()));
			String rowCode =  disposeCostControlLevel(condition.getCondKeyCode(), condition.getCondValueCode(), nextFiscal);
			condition.setCondValueCode(rowCode);
			condition.setFiscal(nextFiscal);
			newStandConditions.add(condition);
		}
		List<PcxStandValue> newStandValues = new ArrayList<>();
		for (PcxStandValue pcxStandValue : pcxStandValues) {
			pcxStandValue.setId(IDGenerator.id());
			pcxStandValue.setStandCode(standCodeMap.get(pcxStandValue.getStandCode()));
			String rowCode = disposeCostControlLevel(pcxStandValue.getRowKeyCode(), pcxStandValue.getRowValueCode(), nextFiscal);
			String columnCode = disposeCostControlLevel(pcxStandValue.getColumnKeyCode(), pcxStandValue.getColumnValueCode(), nextFiscal);
			pcxStandValue.setFiscal(nextFiscal);
			pcxStandValue.setRowValueCode(rowCode);
			pcxStandValue.setColumnValueCode(columnCode);
			newStandValues.add(pcxStandValue);
		}
		return MutableTriple.of(newStandKeys, newStandConditions, newStandValues);
	}

	private String disposeCostControlLevel(String rowKeyCode, String rowValueCode, String newFiscal) {
		if ("controlLevel".equals(rowKeyCode)){
			String[] split = rowValueCode.split(",");
			StringBuilder sb = new StringBuilder();
			for (String s : split) {
				String newCode = newFiscal + "@" + s.substring(5);
				sb.append(newCode).append(",");
			}
			return sb.substring(0, sb.length()-1);
		}
		return rowValueCode;
	}

	private CheckMsg validBillExpenseStandList(BillExpenseStandQO billExpenseStandQO) {
		if (Objects.isNull(billExpenseStandQO)){
			return CheckMsg.fail("参数为空");
		}
		if (StringUtil.isEmpty(billExpenseStandQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if (StringUtil.isEmpty(billExpenseStandQO.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if (StringUtil.isEmpty(billExpenseStandQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (CollectionUtils.isEmpty(billExpenseStandQO.getExpenseTypeCodes())){
			return CheckMsg.fail("费用类型编码不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg validUpdateSingleValue(PcxStandValueVO pcxStandValueQO){
		if (Objects.isNull(pcxStandValueQO)){
			return CheckMsg.fail("参数为空");
		}
		if (StringUtil.isEmpty(pcxStandValueQO.getStandCode())){
			return CheckMsg.fail("标准编码不能为空");
		}
		if (StringUtil.isEmpty(pcxStandValueQO.getColumnKeyCode())){
			return CheckMsg.fail("列要素编码不能为空");
		}
		if (StringUtil.isEmpty(pcxStandValueQO.getColumnValueCode())){
			return CheckMsg.fail("列要素值编码不能为空");
		}
		if (StringUtil.isEmpty(pcxStandValueQO.getRowKeyCode())){
			return CheckMsg.fail("行要素编码不能为空");
		}
		if (StringUtil.isEmpty(pcxStandValueQO.getRowValueCode())){
			return CheckMsg.fail("行要素值编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandValueQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandValueQO.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandValueQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg isValid(PcxStandQO pcxStandQO) {
		if (Objects.isNull(pcxStandQO)){
			return CheckMsg.fail("参数为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg isDeleteValid(PcxStandQueryQO pcxStandQueryQO) {
		if (Objects.isNull(pcxStandQueryQO)){
			return CheckMsg.fail("参数为空");
		}
		if(StringUtil.isEmpty(pcxStandQueryQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQueryQO.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQueryQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(pcxStandQueryQO.getStandCode())){
			return CheckMsg.fail("标准编码不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg isUpdateValid(PcxStandQO pcxStandQO) {
		CheckMsg checkMsg = isSaveValid(pcxStandQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		if (StringUtil.isEmpty(pcxStandQO.getStandCode())){
			return CheckMsg.fail("标准编码不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg isSaveValid(PcxStandQO pcxStandQO) {
		CheckMsg checkMsg = isValid(pcxStandQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		if(StringUtil.isEmpty(pcxStandQO.getExpenseTypeCode())){
			return CheckMsg.fail("费用类型编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getExpenseTypeName())){
			return CheckMsg.fail("费用类型名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getStandName())){
			return CheckMsg.fail("标准名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowKeyCode())){
			return CheckMsg.fail("行要素编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowKeyName())){
			return CheckMsg.fail("行要素名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowValueCode())){
			return CheckMsg.fail("行要素值编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowValueName())){
			return CheckMsg.fail("行要素值名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnKeyCode())){
			return CheckMsg.fail("列要素编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnKeyName())){
			return CheckMsg.fail("列要素名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnValueCode())){
			return CheckMsg.fail("列要素值编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnValueName())){
			return CheckMsg.fail("列要素值名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getUserCode())){
			return CheckMsg.fail("操作人编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getUserName())){
			return CheckMsg.fail("操作人名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getValueEditorCode())){
			return CheckMsg.fail("值表单类型不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg analysisValid(PcxStandQO pcxStandQO) {
		CheckMsg checkMsg = isValid(pcxStandQO);
		if (!checkMsg.isSuccess()){
			return checkMsg;
		}
		if(StringUtil.isEmpty(pcxStandQO.getExpenseTypeCode())){
			return CheckMsg.fail("费用类型编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getExpenseTypeName())){
			return CheckMsg.fail("费用类型名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getStandName())){
			return CheckMsg.fail("标准名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowKeyCode())){
			return CheckMsg.fail("行要素编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowKeyName())){
			return CheckMsg.fail("行要素名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowValueCode())){
			return CheckMsg.fail("行要素值编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getRowValueName())){
			return CheckMsg.fail("行要素值名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnKeyCode())){
			return CheckMsg.fail("列要素编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnKeyName())){
			return CheckMsg.fail("列要素名称不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnValueCode())){
			return CheckMsg.fail("列要素值编码不能为空");
		}
		if(StringUtil.isEmpty(pcxStandQO.getColumnValueName())){
			return CheckMsg.fail("列要素值名称不能为空");
		}
		return CheckMsg.success();
	}
}


