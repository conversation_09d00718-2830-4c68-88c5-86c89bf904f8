package com.pty.pcx.service.impl.bill.handler.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillExpDetailAbroadDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.qo.bill.PcxExpDetailEcsRelQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("billExpenseDetailServiceAbroad")
@Slf4j
@Indexed
public class BillExpenseDetailServiceAbroad extends BillExpenseCommonService implements BillExpenseDetailService<PcxBillExpDetailAbroad, PcxBillExpAbroad> {
    @Autowired
    private PcxBillExpDetailAbroadDao pcxBillExpDetailAbroadDao;

    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Override
    public CheckMsg<Void> validate(List<PcxBillExpDetailAbroad> expDetail, String billFuncCode) {
        return CheckMsg.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PcxBillExpDetailAbroad> saveOrUpdate(PcxBillExpAbroad expense, List<PcxBillExpDetailAbroad> detail, PcxBill pcxBill) {
        if (CollectionUtils.isEmpty(detail)) {
            log.error("没有明细数据, 当前费用类型编码: {}", expense.getExpenseCode());
            return detail;
        }

        // 分类：需要插入的记录 和 需要更新的记录
        List<PcxBillExpDetailAbroad> toInsert = Lists.newArrayList();
        List<PcxBillExpDetailAbroad> toUpdate = Lists.newArrayList();

        List<String> existingIds = new ArrayList<>();
        if (!detail.isEmpty()) {
            List<String> idList = detail.stream()
                    .filter(Objects::nonNull)
                    .map(PcxBillExpDetailAbroad::getId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (!idList.isEmpty()) {
                List<PcxBillExpDetailAbroad> pcxBillExpDetailAbroads = pcxBillExpDetailAbroadDao.selectBatchIds(idList);
                existingIds.addAll(pcxBillExpDetailAbroads.stream()
                        .map(PcxBillExpDetailAbroad::getId)
                        .collect(Collectors.toList()));
            }
        }
        // 遍历 detail 并分类处理
        detail.stream()
                .filter(Objects::nonNull) // 确保 detailAbroad 不为 null
                .forEach(detailAbroad -> {
                    // 设置公共字段
                    prepareCommonFields(detailAbroad, pcxBill, expense);

                    // 判断是插入还是更新
                    if (StringUtil.isEmpty(detailAbroad.getId())|| !existingIds.contains(detailAbroad.getId())) {
                        toInsert.add(detailAbroad);
                    } else {
                        toUpdate.add(detailAbroad);
                    }
                });

        // 批量插入
        if (CollectionUtils.isNotEmpty(toInsert)) {
            batchServiceUtil.batchProcess(toInsert, PcxBillExpDetailAbroadDao.class, PcxBillExpDetailAbroadDao::insert);
        }
        // 批量更新
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            batchServiceUtil.batchProcess(toUpdate, PcxBillExpDetailAbroadDao.class, PcxBillExpDetailAbroadDao::updateById);
        }
        // 更新票信息（保持原逻辑一致）
        updateTicketInfo(detail, pcxBill);

        return detail;
    }

    @Override
    public List<PcxBillExpDetailAbroad> viewByExpenseCode(String expenseCode, PcxBill pcxBill) {
        // TODO 后续涉及处理数据 在进行补充处理逻辑
        return pcxBillExpDetailAbroadDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailAbroad.class)
                .select(
                        PcxBillExpDetailAbroad::getId,
                        PcxBillExpDetailAbroad::getBillId,
                        PcxBillExpDetailAbroad::getExpenseId,
                        PcxBillExpDetailAbroad::getExpenseCode,
                        PcxBillExpDetailAbroad::getExpDetailCode,
                        PcxBillExpDetailAbroad::getInputAmt,
                        PcxBillExpDetailAbroad::getCheckAmt,
                        PcxBillExpDetailAbroad::getAgyCode,
                        PcxBillExpDetailAbroad::getFiscal,
                        PcxBillExpDetailAbroad::getMofDivCode
                )
                .likeRight(PcxBillExpDetailAbroad::getExpDetailCode, expenseCode)
                .eq(PcxBillExpDetailAbroad::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailAbroad::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailAbroad::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailAbroad::getMofDivCode, pcxBill.getMofDivCode()));
    }

    @Override
    public List<PcxBillExpDetailAbroad> listByExpenseCode(String expenseCode, PcxBill pcxBill) {
        return pcxBillExpDetailAbroadDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailAbroad.class)
                .select(
                        PcxBillExpDetailAbroad::getId,
                        PcxBillExpDetailAbroad::getBillId,
                        PcxBillExpDetailAbroad::getExpenseId,
                        PcxBillExpDetailAbroad::getExpenseCode,
                        PcxBillExpDetailAbroad::getExpDetailCode,
                        PcxBillExpDetailAbroad::getInputAmt,
                        PcxBillExpDetailAbroad::getCheckAmt,
                        PcxBillExpDetailAbroad::getAgyCode,
                        PcxBillExpDetailAbroad::getFiscal,
                        PcxBillExpDetailAbroad::getMofDivCode
                )
                .likeRight(PcxBillExpDetailAbroad::getExpenseCode, expenseCode)
                .like(StringUtil.isNotEmpty(expenseCode), PcxBillExpDetailAbroad::getExpDetailCode, expenseCode)
                .eq(PcxBillExpDetailAbroad::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailAbroad::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailAbroad::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailAbroad::getMofDivCode, pcxBill.getMofDivCode()));
    }

    @Override
    public List<PcxBillExpDetailAbroad> view(String expenseDetailCode, PcxBill pcxBill) {
        // TODO 后续涉及处理数据 在进行补充处理逻辑
        return pcxBillExpDetailAbroadDao.selectList(Wrappers.lambdaQuery(PcxBillExpDetailAbroad.class)
                .select(
                        PcxBillExpDetailAbroad::getId,
                        PcxBillExpDetailAbroad::getBillId,
                        PcxBillExpDetailAbroad::getExpenseId,
                        PcxBillExpDetailAbroad::getExpenseCode,
                        PcxBillExpDetailAbroad::getExpDetailCode,
                        PcxBillExpDetailAbroad::getInputAmt,
                        PcxBillExpDetailAbroad::getCheckAmt,
                        PcxBillExpDetailAbroad::getAgyCode,
                        PcxBillExpDetailAbroad::getFiscal,
                        PcxBillExpDetailAbroad::getMofDivCode
                )
                .likeRight(PcxBillExpDetailAbroad::getExpDetailCode, expenseDetailCode)
                .eq(PcxBillExpDetailAbroad::getBillId, pcxBill.getId())
                .eq(PcxBillExpDetailAbroad::getAgyCode, pcxBill.getAgyCode())
                .eq(PcxBillExpDetailAbroad::getFiscal, pcxBill.getFiscal())
                .eq(PcxBillExpDetailAbroad::getMofDivCode, pcxBill.getMofDivCode()));
    }

    @Override
    public void deleteByExpenseCode(String expenseCode, PcxBill pcxBill) {
        //TODO 出国费已经在费用主单信息中处理了费用明细的删除
    }

    @Override
    public void delete(String expenseDetailCode, PcxBill pcxBill) {

    }

    @Override
    public void deleteByIds(List<String> ids) {
        pcxBillExpDetailAbroadDao.deleteBatchIds(ids);
    }

    /**
     * 设置公共字段
     *
     * @param detailAbroad 当前操作的实体对象
     * @param pcxBill      单据对象
     * @param expense      费用对象
     */
    private void prepareCommonFields(PcxBillExpDetailAbroad detailAbroad, PcxBill pcxBill, PcxBillExpAbroad expense) {
        detailAbroad.setBillId(pcxBill.getId());
        detailAbroad.setId(Objects.isNull(detailAbroad.getId()) ? IDGenerator.id() : detailAbroad.getId());
        detailAbroad.setExpenseId(expense.getId());
        detailAbroad.setAgyCode(pcxBill.getAgyCode());
        detailAbroad.setFiscal(pcxBill.getFiscal());
        detailAbroad.setMofDivCode(pcxBill.getMofDivCode());

        // 设置 checkAmt
        if (detailAbroad.getCheckAmt() == null || detailAbroad.getCheckAmt().compareTo(BigDecimal.ZERO) == 0) {
            detailAbroad.setCheckAmt(detailAbroad.getInputAmt());
        }
    }

    /**
     * 更新票信息
     *
     * @param detail  明细记录列表
     * @param pcxBill 单据对象
     */
    private void updateTicketInfo(List<PcxBillExpDetailAbroad> detail, PcxBill pcxBill) {
        // 查询关联的票信息
        PcxExpDetailEcsRelQO param = new PcxExpDetailEcsRelQO();
        param.setBillId(pcxBill.getId());
        param.setFiscal(pcxBill.getFiscal());
        param.setAgyCode(pcxBill.getAgyCode());
        param.setMofDivCode(pcxBill.getMofDivCode());
        param.setTenantId(StringUtil.isNotBlank(pcxBill.getTenantId()) ? pcxBill.getTenantId() : PtyContext.getTenantId());
        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelService.selectByQo(param);
        if (CollectionUtils.isNotEmpty(pcxExpDetailEcsRels)) {
            // 根据明细ID分组
            Map<String, List<PcxExpDetailEcsRel>> groupedByDetailId = pcxExpDetailEcsRels.stream()
                    .filter(item -> StringUtil.isNotBlank(item.getDetailId()))
                    .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getDetailId));

            // 需要更新的票信息
            if (ObjectUtils.isNotEmpty(groupedByDetailId)){
                List<PcxExpDetailEcsRel> updates = Lists.newArrayList();

                detail.forEach(detailAbroad -> {
                    List<PcxExpDetailEcsRel> relatedTickets = groupedByDetailId.get(detailAbroad.getId());
                    if (CollectionUtils.isNotEmpty(relatedTickets)) {
                        PcxExpDetailEcsRel ticket = relatedTickets.get(0);
                        ticket.setEcsCheckStatus(detailAbroad.getEcsCheckStatus());
                        ticket.setCheckReason(detailAbroad.getCheckReason());
                        updates.add(ticket);
                    }
                });

                // 批量更新票信息
                if (CollectionUtils.isNotEmpty(updates)) {
                    batchServiceUtil.batchProcess(updates, PcxExpDetailEcsRelDao.class, PcxExpDetailEcsRelDao::updateById);
                }
            }
        }
    }
}
