package com.pty.pcx.dao.bas;

import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bas.PcxBasItemAuth;
import com.pty.pcx.qo.bas.PcxBasItemAuthQO;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

@Indexed
@MyBatisDao
public interface PcxBasItemAuthDao {
    List<PcxBasItemAuth> selectByQO(PcxBasItemAuthQO qo);

    void insertSelective(PcxBasItemAuth pcxBasItemAuth);

    void deleteByItemCode(PcxBasItemAuthQO qo);

    List<PcxBasItem> getOwnItem(PcxBasItemAuthQO qo);
}
