package com.pty.pcx.dao.stand;

import com.pty.pcx.entity.stand.PcxStandTaxRate;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 劳务标准税率表(PcxStandTaxRate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-04 11:45:49
 */
@Indexed
@MyBatisDao
public interface PcxStandTaxRateDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxStandTaxRate selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxStandTaxRate 实例对象
     * @return 对象列表
     */
    List<PcxStandTaxRate> selectList(PcxStandTaxRate pcxStandTaxRate);

    /**
     * 新增数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxStandTaxRate pcxStandTaxRate);

    /**
     * 修改数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 影响行数
     */
    int update(PcxStandTaxRate pcxStandTaxRate);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

}
