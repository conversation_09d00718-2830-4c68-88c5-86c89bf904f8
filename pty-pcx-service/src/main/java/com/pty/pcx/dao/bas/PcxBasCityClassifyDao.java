package com.pty.pcx.dao.bas;

import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.qo.bas.DataQO;
import com.pty.pcx.qo.bas.QueryCityPeakClassifyQO;
import com.pty.pub.common.anno.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Indexed;
import java.util.List;

/**
 * 城市分类表(PcxBasCityClassify)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-08 10:06:02
 */
@Indexed
@MyBatisDao
public interface PcxBasCityClassifyDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxBasCityClassify selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxBasCityClassify 实例对象
     * @return 对象列表
     */
    List<PcxBasCityClassify> selectList(PcxBasCityClassify pcxBasCityClassify);

    /**
     * 新增数据
     *
     * @param pcxBasCityClassify 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxBasCityClassify pcxBasCityClassify);

    /**
     * 修改数据
     *
     * @param pcxBasCityClassify 实例对象
     * @return 影响行数
     */
    int update(PcxBasCityClassify pcxBasCityClassify);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    int deleteClassify(@Param("classifyType") Integer classifyType,
                       @Param("expenseTypeCode") String expenseTypeCode,
                       @Param("agyCode") String agyCode,
                       @Param("fiscal") String fiscal,
                       @Param("mofDivCode") String mofDivCode);

    void batchInsert(@Param("list") List<PcxBasCityClassify> list);

    List<DataQO> selectAllCity();

    List<DataQO> selectAllProvince();

    List<PcxBasCityClassify> selectCityClassify();

    /**
     * 获取
     * @param cityPeakClassifyQO
     * @return
     */
    List<PcxBasCityClassify> selectCityPeakList(QueryCityPeakClassifyQO cityPeakClassifyQO);

    PcxBasCityClassify selectCityPeak(@Param("classifyCode") String classifyCode);
}


