package com.pty.pcx.dao;

import com.pty.pub.common.anno.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Indexed;

import java.util.List;
import java.util.Map;

@Indexed
@MyBatisDao
public interface SqlDao {


    /**
     * 查询列表
     * @param sqlStr
     * @return
     */
    List<Map<String, Object>> queryListBySql(@Param("sqlStr") String sqlStr);

    /**
     * 查询单个数据
     * @param sqlStr
     * @return
     */
    Map<String, Object> queryFormData(@Param("sqlStr") String sqlStr);

    /**
     * 新增
     * @param map
     */
    void executeInsertSQL(Map<String, Object> map);

    /**
     * 修改
     * @param map
     */
    void executeUpdateSQL(Map<String, Object> map);
}
