package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pty.pcx.entity.bill.PcxBillAbroadTrip;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

/**
 * 单据费用表_出国费(PcxBillAbroadTrip)表数据库访问层
 * 提供出国费相关数据的CRUD操作
 *
 * <AUTHOR>
 * @since 2025-04-14 11:42:51
 */
@Indexed
@MyBatisDao
public interface PcxBillAbroadTripDao extends BaseMapper<PcxBillAbroadTrip> {
    // 继承BaseMapper接口，默认提供基本的CRUD操作
    // 如需自定义查询方法，可在此添加
}