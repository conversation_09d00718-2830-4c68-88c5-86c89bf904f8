package com.pty.pcx.dao.wit;

import com.pty.pcx.entity.wit.PcxAuditExpenseRelation;
import com.pty.pub.common.anno.MyBatisDao;
import com.pty.pub.common.dao.PtyDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 无纸化报销费用关联规则表  dao类
 */
@MyBatisDao
@Indexed
public interface PcxAuditExpenseRelationDao extends PtyDao<PcxAuditExpenseRelation> {

  /**
   * 根据费用代码查询系统级关联规则
   * @param expenseCodeList 费用代码集合
   * @return 关联集合
   */
  List<PcxAuditExpenseRelation> selectSysRationByExpenseCodeList(List<String> expenseCodeList);

  /**
   * 根据规则id查询关联规则
   * @param ruleIdList 规则id集合
   * @return 关联集合
   */
  List<PcxAuditExpenseRelation> selectSysRelationByRuleIdList(List<String> ruleIdList);

  /**
   *  批量插入关联规则
   * @param auditExpenseRelation 关联规则集合
   */
  void insert(PcxAuditExpenseRelation auditExpenseRelation);


  /**
   * 根据规则id删除关联规则
   * @param list 规则id集合
   */
  void delByRuleIds(List<String> list);
}
