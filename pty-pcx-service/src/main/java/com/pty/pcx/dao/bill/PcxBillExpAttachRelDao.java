package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.entity.bill.PcxBillExpAttachRel;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;


/**
 * 费用明细与附件关联表(PcxBillExpDetailAttachRel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-20 10:21:42
 */
@Indexed
@MyBatisDao
public interface PcxBillExpAttachRelDao extends BaseMapper<PcxBillExpAttachRel> {


    default List<PcxBillExpAttachRel> selectByBillId(String billId){
        return selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, billId));
    }
}


