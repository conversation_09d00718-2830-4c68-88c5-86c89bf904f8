<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pty.pcx</groupId>
        <artifactId>pty-pcx</artifactId>
        <version>4.0.1.223-ENT-SNAPSHOT</version>
    </parent>

    <artifactId>pty-pcx-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.pty.db</groupId>
            <artifactId>pty-db-starter</artifactId>
            <version>${db.version}</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.ureport2</groupId>
            <artifactId>pty-ureport2-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.license</groupId>
            <artifactId>pty-license-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.license</groupId>
            <artifactId>pty-license-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.fileservice</groupId>
            <artifactId>pty-fileservice-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.portal</groupId>
            <artifactId>pty-portal-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.setting</groupId>
            <artifactId>pty-setting-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-external</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.workflow2</groupId>
            <artifactId>pty-workflow2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.workflow2</groupId>
            <artifactId>pty-workflow2-extend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.balance</groupId>
            <artifactId>pty-balance-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.balance</groupId>
            <artifactId>pty-balance-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.rule</groupId>
            <artifactId>pty-rule-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ecs</groupId>
            <artifactId>pty-ecs-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.bud</groupId>
            <artifactId>pty-bud-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.bud</groupId>
            <artifactId>pty-bud-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pct</groupId>
            <artifactId>pty-pct-entity</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.pty.message</groupId>-->
<!--            <artifactId>pty-message-service</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.pty.message</groupId>
            <artifactId>pty-message-sdk-starter</artifactId>
        </dependency>
        <!--// todo 马少杰，调整下调用api-->
        <dependency>
            <groupId>com.pty.rule</groupId>
            <artifactId>pty-rule-service</artifactId>
            <version>${rule.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.openapi</groupId>
            <artifactId>pty-openapi-sdk-starter</artifactId>
        </dependency>
    </dependencies>
</project>
