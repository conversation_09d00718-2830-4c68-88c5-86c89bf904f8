package com.pty.pcx.api.bas;

import com.pty.pcx.dto.mad.MadDepartmentDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.vo.bas.PcxDepartmentEmployeeVO;

import java.util.List;
import java.util.function.Function;

public interface IPcxMadDepartmentService {

    List<PcxDepartmentEmployeeVO> selectAgyDepartment(PcxMadBaseQO pcxDepartmentQO, Function<MadEmployeeDTO, PcxDepartmentEmployeeVO> convertor, boolean needSpellComposeCode);

    List<PcxDepartmentEmployeeVO> selectAllAgyDepartmentEmployee(PcxMadBaseQO pcxDepartmentQO);

    List<MadDepartmentDTO> select(PcxMadBaseQO pcxDepartmentQO);
}
