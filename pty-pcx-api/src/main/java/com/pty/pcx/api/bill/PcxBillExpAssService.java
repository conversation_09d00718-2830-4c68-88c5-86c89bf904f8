package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpAss;
import java.util.List;

public interface PcxBillExpAssService {
    /**
     * 查询获取单据费用子表数据
     * @param billIds
     * @param assType
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    List<PcxBillExpAss> selectList(List<String> billIds,String assType, String agyCode, String fiscal, String mofDivCode);

    /**
     * 处理PcxBillExpAss数据
     * @param pcxBillExpAssList
     */
    void processPcxBillExpAss(List<PcxBillExpAss> pcxBillExpAssList, PcxBill pcxBill, String asstype);

    /**
     * 删除公共表数据
     * @param pcxBill
     */
    void deleteByBillId(PcxBill pcxBill);

    /**
     * 查询获取单据费用子表数据
     * @param pcxBill
     * @param assType
     * @return
     */
    List<PcxBillExpAss> selectList(PcxBill pcxBill,String assType);


}