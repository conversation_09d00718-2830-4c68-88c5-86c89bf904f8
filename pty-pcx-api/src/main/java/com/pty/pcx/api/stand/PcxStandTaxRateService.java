package com.pty.pcx.api.stand;

import com.pty.pcx.entity.stand.PcxStandTaxRate;
import com.pty.pcx.entity.stand.qo.PcxStandTaxRateQO;

import java.util.List;

/**
 * 劳务标准税率表(PcxStandTaxRate)表服务接口
 * <AUTHOR>
 * @since 2024-11-04 11:43:21
 */
public interface PcxStandTaxRateService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxStandTaxRate selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxStandTaxRate 实例对象
     * @return 对象列表
     */
    List<PcxStandTaxRate> selectList(PcxStandTaxRate pcxStandTaxRate);

    /**
     * 新增数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxStandTaxRate pcxStandTaxRate);

    /**
     * 修改数据
     *
     * @param pcxStandTaxRate 实例对象
     * @return 影响行数
     */
    int update(PcxStandTaxRate pcxStandTaxRate);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 根据税率编码查询税率列表
     * @param pcxStandTaxRateQO
     * @return
     */
    List<PcxStandTaxRate> selectByRateTypeCode(PcxStandTaxRateQO pcxStandTaxRateQO);

    /**
     * 批量更新
     * @param pcxStandTaxRates
     * @return
     */
    void batchUpdate(List<PcxStandTaxRate> pcxStandTaxRates);

}

