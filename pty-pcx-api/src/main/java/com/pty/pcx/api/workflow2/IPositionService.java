package com.pty.pcx.api.workflow2;

import cn.hutool.core.lang.Assert;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bill.PcxBill;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface IPositionService<T> {
    List<String> getPositionIds();
    List<String> findPositionUser(T t);

    default List<String> getUserIds(PcxBill bill, List<String> empCodes) {
        return this.getUserIds(bill.getAgyCode(), bill.getMofDivCode(), Integer.valueOf(bill.getFiscal()), empCodes);
    }

    default List<String> getUserIds(String agyCode, String mofDivCode, Integer fiscal, List<String> empCodes) {
        if (CollectionUtils.isEmpty(empCodes))
            return Collections.emptyList();
        List<MadEmployeeDTO> employeeDTOS = getMadEmployeeService().selectByMadCodes(empCodes, agyCode, fiscal, mofDivCode);
        if (CollectionUtils.isEmpty(employeeDTOS))
            return Collections.emptyList();
        Assert.state(employeeDTOS.stream().noneMatch(emp -> StringUtils.isBlank(emp.getUserCode())), "领导未设置账号:单位{}年度{}区划{},领导编码{}", agyCode, fiscal, mofDivCode, empCodes);
        // 创建一个映射，将 employeeCode 映射到其在 empCodes 中的索引
        Map<String, Integer> empCodeIndexMap = empCodes.stream()
                .collect(Collectors.toMap(empCode -> empCode, empCodes::indexOf, (a, b) -> a));

        return employeeDTOS.stream().sorted(Comparator.comparingInt(emp -> empCodeIndexMap.get(emp.getEmployeeCode())))
                .map(MadEmployeeDTO::getUserCode).collect(Collectors.toList());
    }

    IPcxMadEmployeeService getMadEmployeeService();

}
