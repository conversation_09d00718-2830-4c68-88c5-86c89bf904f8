package com.pty.pcx.api.bas;

import com.pty.pcx.qo.bas.PcxSettlementRuleQO;
import com.pty.pcx.qo.bas.PcxSettlementRuleUpdateQO;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.entity.bas.PcxSettlementRule;

import java.util.List;

/**
 * 结算规则设置(PcxBasSettlementSetting)表服务接口
 * <AUTHOR>
 * @since 2024-12-09 18:07:02
 */
public interface PcxSettlementRuleService {

    /**
     *
     * @param qo
     * @return
     */
    List<BaseDataVo> selectEnable(PcxSettlementRule qo);

    /***
     * 根据费用代码查找单位的结算方式
     *  1. 当费用是多个结算方式时，返回“启用的结算方式”
     *  2. 当费用是单个结算方式时，返回“费用类型对应结算方式特殊设置”
     * 慎用，此方法只提供给scheme使用
     * @param qo
     * @return
     */
    List<BaseDataVo> selectSettlementByExpenses(PcxSettlementRuleQO qo);

    /**
     * 查询所有结算方式
     * @param qo
     * @return
     */
    List<PcxSettlementRule> selectAll(PcxSettlementRule qo);

    /**
     * 更新结算方式启用状态
     * @param qo
     */
    void update(PcxSettlementRuleUpdateQO qo);
}

