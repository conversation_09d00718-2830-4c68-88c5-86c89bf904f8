package com.pty.pcx.api.bas;

import com.pty.pcx.qo.bas.PcxBasCityPeakQO;
import com.pty.pcx.qo.bas.PcxBasCityPeakVO;
import com.pty.pcx.vo.BaseDataVo;

import java.util.List;

/**
 * 城市淡旺季表
 */
public interface IPcxBasCityPeakService {

    /**
     * 获取所有淡旺季城市
     * @param pcxBasCityPeakQO
     * @return
     */
    List<BaseDataVo> selectAllCityPeakList(PcxBasCityPeakQO pcxBasCityPeakQO);

    /**
     * 获取城市淡旺季列表
     * @param pcxBasCityPeakQO
     * @return
     */
    List<PcxBasCityPeakVO> selectCityPeakList(PcxBasCityPeakQO pcxBasCityPeakQO);

    /**
     * 新增/修改城市淡旺季
     * @param pcxBasCityPeakQOList
     * @return
     */
    void saveOrUpdate(List<PcxBasCityPeakQO> pcxBasCityPeakQOList);

    /**
     * 删除城市淡旺季
     * @param pcxBasCityPeakQOList
     */
    void deletePcxBasCityPeak(List<PcxBasCityPeakQO> pcxBasCityPeakQOList);
}
