package com.pty.pcx.api.bill;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.qo.ecs.AddInvoicesQO;
import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.qo.ecs.StartExpenseQO;
import com.pty.pcx.qo.ecs.common.UpdateEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateNoEcsCommonQO;
import com.pty.pcx.qo.training.DelLabourQo;
import com.pty.pcx.qo.training.SaveLabourQo;
import com.pty.pcx.qo.training.ViewLabourQo;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.training.LabourExpDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 培训费抽象服务类
 * 提供培训费相关操作的抽象方法
 */
public interface PcxBillExpTrainingService {


    /**
     * 根据条件查询培训费列表
     *
     * @param billIds 单据ID列表
     * @return 培训费信息列表
     */
    List<PcxBillExpTraining> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode);

    /**
     * 根据条件查询培训费列表(携带计划类型)
     *
     * @param billIds 单据ID列表
     * @return 培训费信息列表
     */
    List<PcxBillExpTraining> selectList(List<String> billIds, String planCode, String agyCode, String fiscal, String mofDivCode);

    /**
     * 理票，保存培训费报销单
     *
     * @param expenseQO
     * @param ecsMap
     * @return
     */
    String saveBillAndExpense(StartExpenseQO expenseQO, List<InvoiceDtoWrapper> wrappers);

    /**
     * 保存 师资讲课费、专家咨询费接口
     *
     * @param req 接口入参
     * @return 结果
     */
    CheckMsg<?> saveLabourExpDetail(SaveLabourQo req);

    CheckMsg<?> delLabourExpDetail(DelLabourQo req);

    /**
     * 添加发票信息
     * @param invoiceQO
     * @param ecsMap
     * @param bill
     */
    void addEcsBills(AddInvoicesQO invoiceQO, List<InvoiceDtoWrapper> wrappers, PcxBill bill);

    CheckMsg<LabourExpDetailVO> viewLabourExpDetail(ViewLabourQo req);

    CheckMsg<PcxBillVO> updateLabourExpDetail(SaveLabourQo req);
    /**
     * 修改发票信息
     *
     * @param ecsCommonQO
     * @param view
     */
    void updateEcsBill(UpdateEcsCommonQO ecsCommonQO, PcxBill view);

    void updateNoEcs(UpdateNoEcsCommonQO qo, PcxBill view);
}