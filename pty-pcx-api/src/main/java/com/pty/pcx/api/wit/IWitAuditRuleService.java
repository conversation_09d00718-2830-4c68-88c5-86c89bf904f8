package com.pty.pcx.api.wit;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.wit.AuditDTO;
import com.pty.pcx.dto.wit.PcxBillDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.wit.PcxAuditRule;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.entity.wit.WitRuleResultDetail;
import com.pty.pcx.qo.wit.WitAttachQO;
import com.pty.pcx.qo.wit.WitCreateRuleQO;
import com.pty.rule.aviator.AviatorRule;

import java.util.List;


public interface IWitAuditRuleService {

  List<AviatorRule> selectRules(PcxAuditRule auditRule);

  CheckMsg<WitRuleResult> auditRule(AuditDTO auditDTO);

  PcxBillDTO buildData(AuditDTO auditDTO);

  CheckMsg<List<WitRuleResult>> queryRuleResult(WitRuleResult result);

  /**
   * 查询稽核规则的附件
   * @param result 查询条件
   * @return 返回稽核规则附件id
   */
  List<String> queryRuleAttach(WitRuleResult result);




  void saveRuleResult(WitRuleResult result, List<WitRuleResultDetail> oldDetailList);

  /**
   * 处理稽核结果
   * @param detail
   * @return
   */
  CheckMsg<?> auditHandle(WitRuleResultDetail detail);

  /**
   * 稽核规则验证
   * @param pcxBill
   * @param positionCode
   */
  void validateRule(PcxBill pcxBill, String positionCode);

  /**
   * 获取稽核的最终结果
   * @param results
   * @param billStatus
   * @return
   */
  String getResult(List<WitRuleResult> results,String billStatus);

  /**
   * 删除附件票的关联信息
   * @param witAttachQO
   */
  void delAttachRel(WitAttachQO witAttachQO);

  /**
   * 添加附件票的关联信息
   * @param witAttachQO
   */
  void addAttachRel(WitAttachQO witAttachQO);

  /**
   * 创建人工稽核规则
   * @param createRuleQO
   */
  WitRuleResultDetail createRule(WitCreateRuleQO createRuleQO);
}
