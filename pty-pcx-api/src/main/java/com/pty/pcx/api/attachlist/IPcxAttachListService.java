package com.pty.pcx.api.attachlist;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.attachlist.PcxAttachListQO;
import com.pty.pcx.qo.attachlist.PcxAttachListRelationQO;
import com.pty.pcx.vo.attachlist.PcxAttachListVO;

import java.util.List;

/**
 * @ClassName: IPaAttachListService
 * @Description: 附件清单
 * @Date: 2024/11/6  下午8:08
 * @Author: wangbao
 **/
public interface IPcxAttachListService {

    /**
     * 保存附件清单：新增或修改
     *
     * @param paAttachListQOList 附件清单集合
     * @return CheckMsg
     */
    CheckMsg<?> saveOrUpdate(List<PcxAttachListQO> paAttachListQOList);

    /**
     * 删除附件清单
     * @return checkMsg.isSuccess() 则全部删除成功；否则部分附件清单删除失败
     */
    CheckMsg<?> deleteByAttachListId(PcxAttachListQO paAttachListQO);

    /**
     * 保存或更新附件清单
     *
     * @param paAttachListQO 附件清单
     */
    CheckMsg<?> selectByFuncCode(PcxAttachListQO paAttachListQO);

    List<PcxAttachListVO> selectByQO(PcxAttachListQO pcxAttachListQO);

    /**
     * 保存购汇单附件关联关系接口
     * @param pcxAttachListQOList
     * @return
     */
    CheckMsg<?> saveForexReceiptRel(List<PcxAttachListRelationQO> pcxAttachListQOList);

    /**
     * 保存购汇单附件关联关系接口
     * @param pcxAttachListQOList
     * @return
     */
    CheckMsg<?> deleteForexReceiptRel(List<PcxAttachListRelationQO> pcxAttachListQOList);

}
