package com.pty.pcx.api.treasurypay.detail;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBillStatusHistory;
import com.pty.pcx.qo.treasurypay.detail.PcxBillStatusHistoryQO;
import com.pty.pcx.vo.treasurypay.detail.PcxBillStatusHistoryVO;

import java.util.List;

/**
 * 单据流转历史表(PcxBillStatusHistory)表服务接口
 * <AUTHOR>
 * @since 2024-12-20 09:05:17
 */
public interface IPcxBillStatusHistoryService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxBillStatusHistory selectById(String id);

    /**
     * 查询单据流转信息
     * @param qo
     * @return
     */
    CheckMsg<List<PcxBillStatusHistoryVO>> selectBillStatusHistory(PcxBillStatusHistoryQO qo);
}
