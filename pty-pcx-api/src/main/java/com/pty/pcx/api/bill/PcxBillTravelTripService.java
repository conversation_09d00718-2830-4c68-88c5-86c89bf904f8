package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBillTravelTrip;

import java.util.List;

/**
 * 差旅费行程表，存储差旅费单据的行程信息(PcxBillTravelTrip)表服务接口
 * <AUTHOR>
 * @since 2024-11-27 19:53:18
 */
public interface PcxBillTravelTripService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxBillTravelTrip selectById(String id);


    List<PcxBillTravelTrip> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode);

    /**
     * 新增数据
     *
     * @param pcxBillTravelTrip 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxBillTravelTrip pcxBillTravelTrip);


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

}


