package com.pty.pcx.api.stand;

import com.pty.pcx.entity.stand.PcxStandAbroad;
import com.pty.pcx.entity.stand.vo.PcxStandAbroadCountryVO;
import com.pty.pcx.qo.bill.PcxBillAbroadTripQO;
import com.pty.pcx.vo.bill.PcxBillAbroadTripVO;

import java.util.List;
import java.util.Map;

/**
 * 支出标准-出国标准(PcxStandAbroad)表服务接口
 * <AUTHOR>
 * @since 2024-11-04 14:26:47
 */
public interface PcxStandAbroadService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxStandAbroad selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxStandAbroad 实例对象
     * @return 对象列表
     */
    List<PcxStandAbroadCountryVO> selectList(PcxStandAbroad pcxStandAbroad);
	
    /**
     * 新增数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxStandAbroad pcxStandAbroad);

    /**
     * 修改数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 影响行数
     */
    int update(PcxStandAbroad pcxStandAbroad);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 重建城市信息
     * @param abroad
     * @return
     */
    int rebuildCityInfo(PcxStandAbroad abroad);


    /**
     * 根据国际行程信息构建对应支出标准
     * @param qo
     * @return
     */
    List<Map<String, Object>> buildByTripInfo(PcxBillAbroadTripQO qo);
}
