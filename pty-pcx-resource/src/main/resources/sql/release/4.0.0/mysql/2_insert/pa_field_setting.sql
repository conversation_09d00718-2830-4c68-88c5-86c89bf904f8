-- 待审批
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',1,'billNo','单据编号',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'billNo','单据编号','string','billNo',1,'1',null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',2,'taskStartTime','上一岗审批日期',null,1,150,'DESC',null,null,null,null,null,null,null,null,null,null,null,null,'taskStartTime','上一岗审批日期','string','taskStartTime',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',3,'billFuncName','业务类型',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'billFuncName','业务类型','string','billFuncName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',4,'itemName','事项类型',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'itemName','事项类型','string','itemName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',5,'claimantName','申请人',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'claimantName','申请人','string','claimantName',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',6,'departmentName','申请部门',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'departmentName','申请部门','string','departmentName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',7,'expDepartmentNames','费用承担部门',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'expDepartmentNames','费用承担部门','string','expDepartmentNames',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',8,'checkAmt','申请金额',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'checkAmt','申请金额','decimal','checkAmt',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',9,'reason','申请事由',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'reason','申请事由','string','reason',1,'0',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',10,'agyName','单位',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'agyName','单位','string','agyName',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approve','2025','*','*',11,'projectNames','项目',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'projectNames','项目','string','projectNames',1,'0',null,null,null,null,'87','cx';

-- 已审批
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',1,'billNo','单据编号',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'billNo','单据编号','string','billNo',1,'1',null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',2,'approvePost','待审批岗位',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'approvePost','待审批岗位','string','approvePost',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',3,'billFuncName','业务类型',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'billFuncName','业务类型','string','billFuncName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',4,'itemName','事项类型',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'itemName','事项类型','string','itemName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',5,'claimantName','申请人',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'claimantName','申请人','string','claimantName',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',6,'departmentName','申请部门',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'departmentName','申请部门','string','departmentName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',7,'expDepartmentNames','费用承担部门',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'expDepartmentNames','费用承担部门','string','expDepartmentNames',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',8,'checkAmt','申请金额',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'checkAmt','申请金额','decimal','checkAmt',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',9,'reason','申请事由',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'reason','申请事由','string','reason',1,'0',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',10,'agyName','单位',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'agyName','单位','string','agyName',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-approved','2025','*','*',11,'projectNames','项目',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'projectNames','项目','string','projectNames',1,'0',null,null,null,null,'87','cx';

-- 全部
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',1,'billNo','单据编号',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'billNo','单据编号','string','billNo',1,'1',null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',2,'taskStartTime','上一岗审批日期',null,1,150,'DESC',null,null,null,null,null,null,null,null,null,null,null,null,'taskStartTime','上一岗审批日期','string','taskStartTime',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',3,'approvePost','待审批岗位',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'approvePost','待审批岗位','string','approvePost',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',4,'billFuncName','业务类型',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'billFuncName','业务类型','string','billFuncName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',5,'itemName','事项类型',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'itemName','事项类型','string','itemName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',6,'claimantName','申请人',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'claimantName','申请人','string','claimantName',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',7,'departmentName','申请部门',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'departmentName','申请部门','string','departmentName',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',8,'expDepartmentNames','费用承担部门',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'expDepartmentNames','费用承担部门','string','expDepartmentNames',1,'0','filter',null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',9,'checkAmt','申请金额',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'checkAmt','申请金额','decimal','checkAmt',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',10,'reason','申请事由',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'reason','申请事由','string','reason',1,'0',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',11,'agyName','单位',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'agyName','单位','string','agyName',1,'1',null,null,null,null,'87','cx';
INSERT INTO pa_field_setting (id,sys_id,menu_url,fiscal,user_code,agy_code,ord_seq,column_code,column_name,explain_desc,is_show,column_width,sort,is_locking,type,ext_field01,ext_field02,ext_field03,ext_field04,ext_field05,ext_field06,ele_level,atom_code,atom_data_url,is_null,code,name,data_type,code_name,disabled,required,editor,bud_sort,son_title,is_show_code,mof_div_code,tenant_id)
SELECT	UUID(),'PCX','pcx-ops-work-audit-all','2025','*','*',12,'projectNames','项目',null,1,150,null,null,null,null,null,null,null,null,null,null,null,null,null,'projectNames','项目','string','projectNames',1,'0',null,null,null,null,'87','cx';