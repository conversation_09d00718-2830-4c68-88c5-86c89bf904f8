INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120000', 'expense', '*', '', 'settlementPay', 'BSCX', '2024', '87', 2, 'amt', '', '金额', '金额', 0, 0, 1, '', '', 'formatInput', '金额输入框', '', '', 'int', '数字', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120001', 'expense', '*', '', 'settlementPay', 'BSCX', '2024', '87', 2, 'receiveAccountName', '', '收款方户名', '收款方户名', 0, 0, 1, '', '', 'selectInput', '输入选择框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac1200010', 'expense', '*', '', 'settlementCash', 'BSCX', '2024', '87', 2, 'amt', '', '金额', '金额', 0, 0, 1, '', '', 'formatInput', '金额输入框', '', '', 'int', '数字', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac1200012', 'expense', '*', '', 'settlementInvoice', 'BSCX', '2024', '87', 2, 'amt', '', '金额', '金额', 0, 0, 1, '', '', 'formatInput', '金额输入框', '', '', 'int', '数字', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120002', 'expense', '*', '', 'settlementPay', 'BSCX', '2024', '87', 2, 'receiveAccountNo', '', '收款方账号', '收款方账号', 0, 0, 1, '', '', 'selectInput', '输入选择框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120003', 'expense', '*', '', 'settlementPay', 'BSCX', '2024', '87', 2, 'receiveBank', '', '收款方开户银行', '收款方开户银行', 0, 0, 1, '', '', 'bankInput', '银行输入框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120004', 'expense', '*', '', 'settlementPay', 'BSCX', '2024', '87', 2, 'remark', '', '说明', '说明', 0, 0, 1, '', '', 'input', '输入框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120005', 'expense', '*', '', 'settlementCard', 'BSCX', '2024', '87', 2, 'amt', '', '金额', '金额', 0, 0, 1, '', '', 'formatInput', '金额输入框', '', '', 'int', '数字', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120006', 'expense', '*', '', 'settlementCard', 'BSCX', '2024', '87', 2, 'receiveAccountName', '', '收款方户名', '收款方户名', 0, 0, 1, '', '', 'selectInput', '输入选择框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120007', 'expense', '*', '', 'settlementCard', 'BSCX', '2024', '87', 2, 'receiveAccountNo', '', '收款方账号', '收款方账号', 0, 0, 1, '', '', 'selectInput', '输入选择框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120008', 'expense', '*', '', 'settlementCard', 'BSCX', '2024', '87', 2, 'receiveBank', '', '收款方开户银行', '收款方开户银行', 0, 0, 1, '', '', 'bankInput', '银行输入框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120009', 'expense', '*', '', 'settlementCard', 'BSCX', '2024', '87', 2, 'remark', '', '说明', '说明', 0, 0, 1, '', '', 'input', '输入框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120011', 'expense', '*', '', 'settlementCash', 'BSCX', '2024', '87', 2, 'remark', '', '说明', '说明', 0, 0, 1, '', '', 'input', '输入框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);
INSERT INTO pcx_bas_form_setting (id, form_classify, form_code, form_name, form_type, agy_code, fiscal, mof_div_code, bill_func_code, field_value, field_label, field_title, field_name, is_edit, is_null, is_enabled, notes, remarks, editor_code, editor_name, font_style, show_type, data_type_code, data_type_name, data_source_code, data_source_name, data_classify_code, data_classify_name, url, def_val, field_control, show_relation, max_length, min_length, is_extend, seq, modifier, modifier_name, modified_time, creator_code, creator_name, created_time, tenant_id, only_select_last) VALUES ('ca04fbd5-a2f4-11ed-ad10-0242ac120013', 'expense', '*', '', 'settlementInvoice', 'BSCX', '2024', '87', 2, 'remark', '', '说明', '说明', 0, 0, 1, '', '', 'input', '输入框', '', '', 'string', '字符串', '', '', '', '', '', '', NULL, NULL, 0, 0, 0, 0, '', '', '', '', '', '', '', 0);