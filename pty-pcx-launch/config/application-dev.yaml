spring:
  cache:
    type: caffeine
    expiretime: 7200
    ehcache:
      config: ${user.dir}/config/ehcache.xml
  #redis配置
  redis:
    host: localhost
    port: 6379
    database: 1
    password: 123456
    timeout: 3000
    pool:
      max-active: 8
      max-wait: -1
      max-idle: 8
      min-idle: 0
# 日志配置
logging:
  config: ${user.dir}/config/logback.xml
  logback:
    dir: ${user.dir}/logs
#ureport固定配置
ureport:
  disableHttpSessionReportCache: false
  disableFileProvider: false
  fileStoreDir: ureportfiles
  debug: true
pty:
  income:
    sysid: income
    appid: C01223285AD9460E
    privateKey: D81F4E6C5E24DFDDF7FF7B050F80CD07AEDA62A77BB32B8B69EDF1DE94B4DD88
    publicKey: C23207AC4FF084CC47C75887BB51C244E638583E14E4E4CA6C0CDD32EC28E846DD3B0C3C8CB39F2FA250F673839C485E7CFDF894E9442AF067F94295D12E57A9