package com.pty.pcx.common.util;

import java.util.Arrays;
import java.util.List;

public class StringSimilarity {
    /**
     * 计算两个字符串之间的 Levenshtein 距离
     *
     * @param s1 第一个字符串
     * @param s2 第二个字符串
     * @return Levenshtein 距离
     */
    public static int levenshteinDistance(String s1, String s2) {
        int len1 = s1.length();
        int len2 = s2.length();

        // 创建一个二维数组来存储子问题的解
        int[][] dp = new int[len1 + 1][len2 + 1];

        // 初始化边界条件
        for (int i = 0; i <= len1; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= len2; j++) {
            dp[0][j] = j;
        }

        // 填充 dp 数组
        for (int i = 1; i <= len1; i++) {
            for (int j = 1; j <= len2; j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(
                            dp[i - 1][j] + 1,    // 删除
                            Math.min(
                                    dp[i][j - 1] + 1,  // 插入
                                    dp[i - 1][j - 1] + 1 // 替换
                            )
                    );
                }
            }
        }

        return dp[len1][len2];
    }

    /**
     * 计算两个字符串之间的相似度
     *
     * @param s1 第一个字符串
     * @param s2 第二个字符串
     * @return 相似度（范围在 0 到 1 之间）
     */
    public static double similarity(String s1, String s2) {
        if (s1 == null || s2 == null) {
            throw new IllegalArgumentException("Strings must not be null");
        }
        if (s1.equals(s2)) {
            return 1.0;
        }
        if (s1.isEmpty() || s2.isEmpty()) {
            return 0.0;
        }

        int levDistance = levenshteinDistance(s1, s2);
        int maxLength = Math.max(s1.length(), s2.length());

        return 1.0 - (double) levDistance / maxLength;
    }

    public static void main(String[] args) {
        String str1 = "孙佳旺";
        String str2 = "孙佳旺(财信)";
        String str3 = "孙佳旺*";
        List<String> list = Arrays.asList(str1, str2, str3);
        list.forEach(e -> {
            double similarity = similarity(str1, e);
            System.out.printf("%s %s 相似度: %f%n", str1, e, similarity);
        });

//        double similarity = similarity(str1, str2);
//        System.out.println("相似度: " + similarity);
    }
}
