package com.pty.pcx.common.util;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.pty.pub.common.exception.CommonException;

public class AlgorithmUtils {

    public static <T> Object performSubtraction(T x, Object y) {
        if (x instanceof Collection && y instanceof Collection) {
            @SuppressWarnings("unchecked") Collection<T> collectionX = (Collection<T>) x;
            @SuppressWarnings("unchecked") Collection<?> collectionY = (Collection<?>) y;
            Set<T> resultSet = new HashSet<>(collectionX);
            resultSet.removeAll((Collection<T>) collectionY);
            return resultSet;
        } else if (x instanceof Collection && y instanceof String) {
            Collection<?> collectionX = (Collection<?>) x;
            String strY = (String) y;
            List<String> resultList = new ArrayList<>();
            for (Object item : collectionX) {
                if (item instanceof String) {
                    String strItem = (String) item;
                    String replaced = strItem.replace(strY, "");
                    resultList.add(replaced);
                }
            }
            return resultList;
        } else if (x instanceof String && y instanceof String) {
            String strX = (String) x;
            String strY = (String) y;
            return strX.replace(strY, "");
        } else if (x instanceof String && y instanceof Number) {
            String strX = (String) x;
            Number numY = (Number) y;
            try {
                double numX = Double.parseDouble(strX);
                double result = numX - numY.doubleValue();
                DecimalFormat df = new DecimalFormat("0.##");
                String formattedResult = df.format(result);
                return String.valueOf(formattedResult);
            } catch (NumberFormatException e) {
                return strX.replace(String.valueOf(numY), "");
            }
        } else if (x instanceof Number && y instanceof Number) {
            Number numX = (Number) x;
            Number numY = (Number) y;
            double result = numX.doubleValue() - numY.doubleValue();
            if (x instanceof Integer) {
                return (int) result;
            } else if (x instanceof Long) {
                return (long) result;
            } else if (x instanceof Float) {
                return (float) result;
            } else if (x instanceof Double) {
                return result;
            } else {
                throw new CommonException(x + "与" + y + "该数据数据类型不支持");
            }
        } else {
            throw new CommonException(x + "与" + y + "该数据数据类型不支持");
        }
    }

    public static <T> Object performAddition(T x, Object y) {
        if (x instanceof Collection && y instanceof Collection) {
             Collection<T> collectionX = (Collection<T>) x;
             Collection<?> collectionY = (Collection<?>) y;
            Collection<T> resultCollection = new ArrayList<>(collectionX);
            resultCollection.addAll((Collection<T>) collectionY);
            return resultCollection;
        } else if (x instanceof String && y instanceof String) {
            String strX = (String) x;
            String strY = (String) y;
            return strX.concat(strY);
        } else if (x instanceof String && y instanceof Number) {
            String strX = (String) x;
            Number numY = (Number) y;
            try {
                double numX = Double.parseDouble(strX);
                double result = numX + numY.doubleValue();
                DecimalFormat df = new DecimalFormat("0.##");
                String formattedResult = df.format(result);
                return String.valueOf(formattedResult);
            } catch (NumberFormatException e) {
                return strX.concat(String.valueOf(numY));
            }
        } else if (x instanceof Number && y instanceof Number) {
            Number numX = (Number) x;
            Number numY = (Number) y;
            double result = numX.doubleValue() + numY.doubleValue();
            if (x instanceof Integer) {
                return (int) result;
            } else if (x instanceof Long) {
                return (long) result;
            } else if (x instanceof Float) {
                return (float) result;
            } else if (x instanceof Double) {
                return result;
            } else {
                throw new CommonException(x + "与" + y + "该数据数据类型不支持");
            }
        } else {
            throw new CommonException(x + "与" + y + "该数据数据类型不支持");
        }
    }

    public static <T> T getEleAtIndex(List<T> collection, int index) {
        // 检查索引是否在列表的有效范围内
        if (index < 0 || index >= collection.size()) {
            return null;
        }
        return collection.get(index);
    }

    public static <T> Collection<T> findDuplicates(T obj, String fieldName) {
        if (null == obj) {
            return null;
        }
        Collection<T> collection = (Collection<T>) obj;
        Function<T, Object> keyExtractor = (fieldName != null && !fieldName.isEmpty()) ? createKeyExtractor(fieldName) : item -> (Object) item; // 显式转换为 Object

        Map<Object, Long> counts = collection.stream().collect(Collectors.groupingBy(keyExtractor, Collectors.counting()));
        // 用于跟踪已经被添加到结果中的键
        Set<Object> addedKeys = new HashSet<>();

        return collection.stream().filter(item -> {
            Object key = keyExtractor.apply(item);
            // 检查该键是否已经添加过，以及它是否是重复的
            return key != null && counts.getOrDefault(key, 0L) > 1 && addedKeys.add(key);
        }).collect(Collectors.toList());
    }

    private static <T> Function<T, Object> createKeyExtractor(String fieldName) {
        return item -> {
            try {
                Field field = item.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(item);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new CommonException("数据结构中字段不存在: " + fieldName);
            }
        };
    }


    public static <T> String concatenate(Object obj, List<String> fieldNames, String delimiter) {
        if (obj == null) {
            return "";
        }
        Collection<T> collection = (Collection<T>) obj;
        // 检查第一个元素是否是基础数据类型
        boolean isBaseType = collection.iterator().next() instanceof Integer || collection.iterator().next() instanceof Double || collection.iterator().next() instanceof String;

        if (isBaseType && (fieldNames == null || fieldNames.isEmpty())) {
            // 如果是基础数据类型且没有提供属性名，则直接拼接
            return collection.stream().map(Object::toString).collect(Collectors.joining(delimiter)) + delimiter;
        } else {
            // 对于对象类型，使用反射获取属性值并拼接
            return collection.stream().map(item -> {
                StringBuilder sb = new StringBuilder();
                for (String fieldName : fieldNames) {
                    try {
                        Field field = item.getClass().getDeclaredField(fieldName);
                        field.setAccessible(true); // 确保可以访问私有字段
                        Object value = field.get(item);
                        sb.append(value);
                        sb.append(" ");
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new CommonException("数据结构中字段不存在: " + fieldName);
                    }
                }
                return sb.toString();
            }).collect(Collectors.joining(delimiter)) + delimiter;
        }
    }
}
