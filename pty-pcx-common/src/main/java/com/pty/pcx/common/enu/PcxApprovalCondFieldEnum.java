package com.pty.pcx.common.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 财务审批页面的条件字段枚举
 * createdTime: 2024/12/26  下午1:45
 * creator: wangbao
 **/
@Getter
@AllArgsConstructor
public enum PcxApprovalCondFieldEnum {
    BILL_NO("billNo", "单据编号"),
    REASON("reason", "事由" ),
    CHECK_AMT("checkAmt", "核定金额"),
    ITEM_NAME("itemName", "事项类型名称"),
    CLAIMANT_NAME("claimantName", "填报人员名称"),
    DEPARTMENT_NAME("departmentName", "填报部门名称"),
    TRANS_DATE("modifiedTime", "业务发生日期"),
    TASK_START_TIME("taskStartTime", "上一岗审批日期"),
    TASK_END_TIME("taskEndTime", "审批通过时间"),
    ;

    private final String code;
    private final String name;

    private static final List<Map<String, Object>> resultList = Collections.synchronizedList(new ArrayList<>());

    public static List<Map<String, Object>> getList() {
        if (!resultList.isEmpty()) {
            return resultList;
        }
        synchronized (PcxApprovalCondFieldEnum.class) {
            if (!resultList.isEmpty()) {
                return resultList;
            }
            for (PcxApprovalCondFieldEnum element : PcxApprovalCondFieldEnum.values()) {
                Map<String, Object> map = new HashMap<>();
                map.put("code", element.getCode());
                map.put("name", element.getName());
                resultList.add(map);
            }
        }
        return resultList;
    }

    public static PcxApprovalCondFieldEnum getByCode(String code) {
        for (PcxApprovalCondFieldEnum element : PcxApprovalCondFieldEnum.values()) {
            if (element.getCode().equals(code)) {
                return element;
            }
        }
        return null;
    }
    //获取枚举的所有code
    public static List<String> getAllCode() {
        List<String> codes = new ArrayList<>();
        for (PcxApprovalCondFieldEnum element : PcxApprovalCondFieldEnum.values()) {
            codes.add(element.getCode());
        }
        return codes;
    }
}
