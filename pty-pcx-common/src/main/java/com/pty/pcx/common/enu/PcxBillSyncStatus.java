package com.pty.pcx.common.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PcxBillSyncStatus {

    PENDING_SYNC(0, "待同步"),
    SYNCED(1, "已同步"),
    SYNC_FAILED(2, "同步失败"),
    PENDING_REVOKE(3, "待撤回"),
    DRAFT_PENDING_SYNC(10, "待同步（草稿）"),
    DRAFT_SYNCED(11, "已同步（草稿）"),
    DRAFT_SYNC_FAILED(12, "同步失败（草稿）"),
    IS_CLOSED(-2, "关账");

    private final Integer code;
    private final String name;

    public static Integer getSyncedStatus(boolean isFinalReview) {
        return isFinalReview ? PcxBillSyncStatus.SYNCED.getCode() : PcxBillSyncStatus.DRAFT_SYNCED.getCode();
    }

    public static Integer getSynceFailedStatus(boolean isFinalReview) {
        return isFinalReview ? PcxBillSyncStatus.SYNC_FAILED.getCode() : PcxBillSyncStatus.DRAFT_SYNC_FAILED.getCode();
    }

}
