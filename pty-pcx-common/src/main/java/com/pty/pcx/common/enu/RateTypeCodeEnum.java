package com.pty.pcx.common.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;


@AllArgsConstructor
@Getter
public enum RateTypeCodeEnum {
    OUTSIDE_LABOR_TAX_RATE_MONTH("01-1", "外部劳务费税率(按月)"),
    OUTSIDE_LABOR_TAX_RATE_YEAR("01-2", "外部劳务费税率(按年)"),
    SALARY_AND_WAGES_TAX_RATE_MONTH("02-1", "工资薪金税率(按月)"),
    SALARY_AND_WAGES_TAX_RATE_YEAR("02-2", "工资薪金税率(按年)"),
    FOREIGN_LABOR_TAX_RATE_MONTH("03-1", "外籍人员劳务费税率(按月)"),
    FOREIGN_LABOR_TAX_RATE_YEAR("03-2", "外籍人员劳务费税率(按年)"),
    INTERNSHIP_STUDENT_LABOR_TAX_RATE_MONTH("04-1", "实习学生劳务费税率(按月)"),
    INTERNSHIP_STUDENT_LABOR_TAX_RATE_YEAR("04-2", "实习学生劳务费税率(按年)"),
    ;
    /**
     * 税率类型代码
     */
    private String code;
    /**
     * 税率类型名称
     */
    private String name;


    public static String getNameByCode(String code){
        for(RateTypeCodeEnum e : RateTypeCodeEnum.values()){
            if(e.getCode().equals(code)){
                return e.getName();
            }
        }
        return "";
    }

    /**
     * 将枚举转换为 Map，key 为 code，value 为 name
     * @return 转换后的 Map
     */
    public static List<Map<String, String>> toList() {
        //将此枚举返回 List<Map<String, String>> 这种类型 Map的key为 code和 name
        List<Map<String, String>> list = new ArrayList<>();
        Arrays.stream(RateTypeCodeEnum.values()).forEach(e->{
            Map<String, String> item = new HashMap<>();
            item.put("code",e.code);
            item.put("name",e.name);
            list.add(item);

        });
        return list;
    }

}
