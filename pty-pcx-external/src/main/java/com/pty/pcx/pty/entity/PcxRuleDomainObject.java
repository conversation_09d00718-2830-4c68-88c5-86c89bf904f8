package com.pty.pcx.pty.entity;

import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.qo.bas.PcxBasExpBizTypeQO;
import com.pty.rule.RuleDomainObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@RuleDomainObject.Domain(module = PcxConstant.PCX_RULE_MODULE, ns = PcxConstant.PCX_RULE_NS)
public class PcxRuleDomainObject extends RuleDomainObject {

    private PcxBasExpBizTypeQO pcxBasExpBizTypeQO;

    public BigDecimal getEcsBizAmt() {
        return getPcxBasExpBizTypeQO().getEcsBizAmt();
    }

    public String getEcsBizTypeCode() {
        return getPcxBasExpBizTypeQO().getEcsBizTypeCode();
    }

    public String getMofDivCode() {
        return getPcxBasExpBizTypeQO().getMofDivCode();
    }

    public String getAgyCode() {
        return getPcxBasExpBizTypeQO().getAgyCode();
    }

    public String getFiscal() {
        return getPcxBasExpBizTypeQO().getFiscal();
    }

    public String getTenantId() {
        return getPcxBasExpBizTypeQO().getTenantId();
    }

    public String getStayType() {
        return getPcxBasExpBizTypeQO().getStayType();
    }

    public String getDepartmentCode(){
        return getPcxBasExpBizTypeQO().getDepartmentCode();
    }

    public String getTravelType(){
        return getPcxBasExpBizTypeQO().getTravelType();
    }

    public String getExtraSubsidy() {
        return getPcxBasExpBizTypeQO().getExtraSubsidy();
    }
    public boolean fireRule(String ruleId) {
        // todo 等待稽核规则模块完成
        return Boolean.TRUE;
    }

//    public String fillErrTemp() {
//        return getPcxBasExpBizTypeQO.getErrorMsg();
//    }

}
