package com.pty.pcx.gip.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.balance.api.IBalanceQueryService;
import com.pty.balance.constants.BalanceTypeRelationEnum;
import com.pty.balance.entity.PtyBudBalance;
import com.pty.gal.vo.voucher.VoucherVO;
import com.pty.gip.GipAccountedRelation;
import com.pty.gip.api.IGipStadVouService;
import com.pty.gip.api.IGipVouOptCallBack;
import com.pty.gip.bill.GipBillAccounted;
import com.pty.gip.common.GipResultMsg;
import com.pty.gip.vo.GipBillVO;
import com.pty.gip.vou.GipVoucherDO;
import com.pty.pa.security.api.OrgService;
import com.pty.pa.security.entity.PtyOrg;
import com.pty.pa.security.entity.vo.PtyOrgVo;
import com.pty.pcx.api.bill.PcxBillRelationService;
import com.pty.pcx.api.rule.IPcxVouRuleService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.mad.MadProjectDTO;
import com.pty.pcx.dto.treasurervou.PcxGenVouDTO;
import com.pty.pcx.dto.treasurervou.PcxGipVouBillDTO;
import com.pty.pcx.dto.treasurervou.PcxGipVouDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillRelation;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.gip.IPcxGipVouService;
import com.pty.pcx.api.mybatisplus.bill.IPcxBillPlusService;
import com.pty.pcx.gip.PcxGipCrDrDTO;
import com.pty.pcx.gip.constant.PcxGipConstants;
import com.pty.pcx.gip.utils.PcxGipUtil;
import com.pty.pcx.mad.IMadProjectExternalService;
import com.pty.pcx.qo.rule.PcxVouSummaryAcoQO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class PcxGipVouServiceImpl implements IPcxGipVouService, IGipVouOptCallBack {

    @Autowired(required = false)
    @RestClientReference(microServiceNames = "gip")
    private IGipStadVouService gipStadVouService;
    @Autowired(required = false)
    @RestClientReference(microServiceNames = "pa")
    private OrgService orgService;
    @Autowired(required = false)
    @RestClientReference(microServiceNames = "mad")
    private IMadProjectExternalService madProjectExternalService;
    @Autowired
    private IPcxBillPlusService pcxBillPlusService;
    @Autowired
    private PcxBillRelationService pcxBillRelationService;
    @Autowired
    @Lazy
    private IBalanceQueryService balanceQueryService;

    @Override
    public CheckMsg<PcxGenVouDTO> handleTreasurerVou(PcxGenVouDTO pcxGenVouDTO) {
        Map<String, Object> srcData = MapUtil.bean2Map(buildGipVouDTO(pcxGenVouDTO));
        log.info("生成凭证入参：{}", JSONObject.toJSONString(srcData));
        GipResultMsg<List<GipBillVO>> response = gipStadVouService.genVoucher(srcData);
        if (!response.isSuccess() || CollectionUtil.isEmpty(response.getData())
                || StringUtil.isEmpty(response.getData().get(0).getVoucherId())) {
            log.error("生成凭证失败：{}", response.getMsgInfo());
            return CheckMsg.fail(response.getMsgInfo());
        }
        if (PcxGipConstants.GAL_ERROR_CODE.equals(response.getCode())) {
            String result = response.getData().stream().map(GipBillVO::getSummary).filter(StringUtil::isNotEmpty).collect(Collectors.joining(","));
            if (StringUtil.isNotEmpty(result)) {
                return CheckMsg.fail(result);// 失败原因
            }
            return CheckMsg.fail("核算失败码返回{}，但未返回失败原因", response.getCode());// 失败原因
        }
        if (ObjectUtil.isEmpty(response.getData().get(0))){
            return CheckMsg.fail("生成凭证失败，{response.getData().get(0)}返回数据为空");
        }
        pcxGenVouDTO.setVouId(response.getData().get(0).getVoucherId());
        pcxGenVouDTO.setVouNo(response.getData().get(0).getVouNo());
        pcxGenVouDTO.setVouDate(response.getData().get(0).getVoucherDate());
        return CheckMsg.success(pcxGenVouDTO);
    }

    @Override
    public CheckMsg<String> handlePreviewVou(PcxGenVouDTO pcxGenVouDTO) {
        log.info("预览生成凭证入参：{}", JSONObject.toJSONString(buildGipVouDTO(pcxGenVouDTO)));
        Response response = gipStadVouService.preViewVoucher(JSONObject.toJSONString(buildGipVouDTO(pcxGenVouDTO)));
        if(ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getData())){
            log.error("handlePreviewVou ==> 调用gip组装接口，返回值 response 为空, 预览失败");
            return CheckMsg.fail("handlePreviewVou ==> 调用gip接口，返回值 response 为空, 预览失败");
        }
        List<GipVoucherDO> resList = (List<GipVoucherDO>) response.getData();
        if(ObjectUtil.isEmpty(resList) || ObjectUtil.isEmpty(resList.get(0)) || StringUtil.isEmpty(resList.get(0).getDraId())){
            return CheckMsg.fail("handlePreviewVou ==> 调用gip接口，返回值 response.getData() 为空, 预览失败");
        }
        return CheckMsg.success(resList.get(0).getDraId());
    }

    /**
     * 构建生成凭证入参
     */
    @SneakyThrows
    private PcxGipVouDTO buildGipVouDTO(PcxGenVouDTO pcxGenVouDTO) {
        if (StringUtil.isEmpty(pcxGenVouDTO.getPcxBills().get(0).getBillFuncCode())) {
            throw new CommonException("单据的BillFuncCode为空导致无法记账");
        }
        Integer isCtrlBalance = getIsCtrlBalance(pcxGenVouDTO);
        PtyOrgVo orgVo = getPtyOrgVo(pcxGenVouDTO);
        Map<String, PtyBudBalance> budBalanceMap = getBudBalanceMap(pcxGenVouDTO);
        Map<String, MadProjectDTO> projectDTOMap = getMadProjectMap(pcxGenVouDTO,budBalanceMap);
        PcxGipVouDTO pcxGipVouDTO = PcxGipVouDTO.builder()
                .sysId(PcxConstant.SYS_ID)
                .isMergeVou(1)
                .isCtrlBalance(isCtrlBalance)
                .agyCode(pcxGenVouDTO.getPcxTreasurerVouQO().getAgyCode())
                .fiscal(pcxGenVouDTO.getPcxTreasurerVouQO().getFiscal())
                .mofDivCode(pcxGenVouDTO.getPcxTreasurerVouQO().getMofDivCode())
                .tenantId(pcxGenVouDTO.getPcxTreasurerVouQO().getTenantId())
                .acbCode(pcxGenVouDTO.getPcxTreasurerVouQO().getAcbCode())
                .acbName(pcxGenVouDTO.getPcxTreasurerVouQO().getAcbName())
                .isCover(pcxGenVouDTO.getPcxTreasurerVouQO().getIsCover())
                .build();
        PcxGipCrDrDTO pcxGipCrDrDTO = PcxGipCrDrDTO.builder()
                .orgVo(orgVo)
                .pcxGenVouDTO(pcxGenVouDTO)
                .budBalanceMap(budBalanceMap)
                .projectDTOMap(projectDTOMap)
                .build();
        pcxGipVouDTO.setBills(pcxGenVouDTO.getPcxBills().stream()
                .filter(Objects::nonNull) // 确保 bill 不为 null
                .map(bill -> {
                    pcxGipCrDrDTO.setBill(bill);
                    return buildBillData(pcxGipCrDrDTO);
                }).collect(Collectors.toList()));
        pcxGipVouDTO.setIsPexExpBudUnLoan(pcxGenVouDTO.getPcxTreasurerVouQO().getIsPexExpBudUnLoan());//默认0，只有有指标无借款才为1 校验 0 不校验
        if (ObjectUtil.isEmpty(pcxGenVouDTO)) {
            throw new CommonException("构建生成凭证入参异常：PcxGipVouDTO为空");
        }
        genSummaryAndAco(pcxGipCrDrDTO, pcxGipVouDTO);
        return pcxGipVouDTO;
    }

    private Map<String, MadProjectDTO> getMadProjectMap(PcxGenVouDTO pcxGenVouDTO, Map<String, PtyBudBalance> budBalanceMap) {
        PcxBaseDTO pcxBaseDTO = PcxBaseDTO.builder()
                .tenantId(pcxGenVouDTO.getPcxTreasurerVouQO().getTenantId())
                .mofDivCode(pcxGenVouDTO.getPcxTreasurerVouQO().getMofDivCode())
                .agyCode(pcxGenVouDTO.getPcxTreasurerVouQO().getAgyCode())
                .fiscal(String.valueOf(pcxGenVouDTO.getPcxTreasurerVouQO().getFiscal()))
                .build();

        if (ObjectUtil.isEmpty(budBalanceMap)) {
            return new HashMap<>();
        }
        List<PtyBudBalance> list = budBalanceMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(list)) {
            List<String> proCodes = list.stream().map(PtyBudBalance::getProjectCode).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            List<MadProjectDTO> madProjects = madProjectExternalService.selectByMadCodes(pcxBaseDTO, proCodes);
            return madProjects.stream().collect(Collectors.toMap(MadProjectDTO::getProjectCode, Function.identity()));
        }
        return new HashMap<>();
    }


    /**
     * 根据记账规则匹配摘要和科目
     */
    @SneakyThrows
    private static void genSummaryAndAco(PcxGipCrDrDTO pcxGipCrDrDTO, PcxGipVouDTO pcxGipVouDTO) {
        List<PcxVouSummaryAcoQO> vouSummaryAcoQOList = MapUtil.convertListMap2ListBean(pcxGipCrDrDTO.getAllDetails(), PcxVouSummaryAcoQO.class);
        CheckMsg<?> checkMsg = SpringUtil.getBean(IPcxVouRuleService.class).selectVouSummaryAndAco(vouSummaryAcoQOList);
        if (!checkMsg.isSuccess() || ObjectUtil.isEmpty(checkMsg.getData())) {
            log.error("生成凭证获取摘要和科目失败，原因：{}", checkMsg.getMsgInfo());
            throw new CommonException("生成凭证获取摘要和科目失败，原因：" + checkMsg.getMsgInfo());
        }
        List<Map<String, Object>> resultList = (List<Map<String, Object>>)checkMsg.getData();
        Map<String, Map<String, Object>> vouruleMap = resultList.stream().collect(Collectors.toMap(item -> item.get(PcxGipConstants.DETAIL_ID).toString(), Function.identity(), (a, b) -> a));
        for (Map<String, Object> bill : pcxGipVouDTO.getBills()) {
            for (Map<String, Object> detailMap : (List<Map<String, Object>>)bill.get(PcxGipConstants.DETAIL_BILLS)) {
                detailMap.putAll(vouruleMap.get(detailMap.get(PcxGipConstants.DETAIL_ID)));
            }
        }
    }

    @NotNull
    private Map<String, PtyBudBalance> getBudBalanceMap(PcxGenVouDTO pcxGenVouDTO) {
        //抽取pcxGenVouDTO里的pcxBillPayDetailList的balanceId集合
        List<String> balanceIds = pcxGenVouDTO.getPcxBillPayDetailList().stream().map(PcxBillPayDetail::getBalanceId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(balanceIds)) {
            return new HashMap<>();
        }
        List<PtyBudBalance> budBalances = balanceQueryService.getBalanceByIds(balanceIds);
        //把budBalances转成Map
        return budBalances.stream().collect(Collectors.toMap(PtyBudBalance::getBalanceId, v -> v));
    }

    @NotNull
    private Integer getIsCtrlBalance(PcxGenVouDTO pcxGenVouDTO) {
        boolean pcxGalCtrl = balanceQueryService.getCtrlRuleStatus(pcxGenVouDTO.getPcxTreasurerVouQO().getMofDivCode(),
                pcxGenVouDTO.getPcxTreasurerVouQO().getAgyCode(),String.valueOf(pcxGenVouDTO.getPcxTreasurerVouQO().getFiscal()),
                BalanceTypeRelationEnum.GAL.getName(), BalanceTypeRelationEnum.EXPENSE.getName());//启用报销生成凭证控制0:关闭，1:开启
        int loanOrRepay = (Objects.equals(BillFuncCodeEnum.LOAN.getCode(), pcxGenVouDTO.getPcxBills().get(0).getBillFuncCode())
                || Objects.equals(BillFuncCodeEnum.REPAYMENT.getCode(), pcxGenVouDTO.getPcxBills().get(0).getBillFuncCode())) ? 0 : 1;
        return pcxGalCtrl ? loanOrRepay : 0;
    }

    private PtyOrgVo getPtyOrgVo(PcxGenVouDTO pcxGenVouDTO) {
        PtyOrg cond = new PtyOrg();
        cond.setOrgCode(pcxGenVouDTO.getPcxTreasurerVouQO().getAgyCode());
        cond.setMofDivCode(pcxGenVouDTO.getPcxTreasurerVouQO().getMofDivCode());
        cond.setFiscal(pcxGenVouDTO.getPcxTreasurerVouQO().getFiscal());
        cond.setTenantId(pcxGenVouDTO.getPcxTreasurerVouQO().getTenantId());
        return orgService.getOrgInfo(cond);
    }

    @SneakyThrows
    private Map<String, Object> buildBillData(PcxGipCrDrDTO pcxGipCrDrDTO) {
        if (ObjectUtil.isEmpty(pcxGipCrDrDTO.getBill())) {
            throw new CommonException("构建生成凭证入参异常：PcxBill为空");
        }
        // 过滤当前单据的明细数据
        List<PcxBillPayDetail> billPayDetails = pcxGipCrDrDTO.getPcxGenVouDTO().getPcxBillPayDetailList().stream()
                .filter(billPayDetail -> billPayDetail.getBillId().equals(pcxGipCrDrDTO.getBill().getId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(billPayDetails)) {
            throw new CommonException("构建生成凭证入参异常：PcxBillPayDetailList为空");
        }
        List<Map<String, Object>> details = JSON.parseObject(JSON.toJSONString(billPayDetails), new TypeReference<List<Map<String, Object>>>() {});
        PcxGipVouBillDTO pcxGipVouBillDTO = PcxGipVouBillDTO.builder()
                .sysId(PcxConstant.SYS_ID)
                .tableName(PcxGipConstants.PCX_BILL)
                .industryType(pcxGipCrDrDTO.getOrgVo().getIndustryType())//行业类型
                .orgType(pcxGipCrDrDTO.getOrgVo().getOrgType())//机构类型
                .summary(pcxGipCrDrDTO.getBill().getReason())
                .billId(pcxGipCrDrDTO.getBill().getId())
                .build();
        pcxGipCrDrDTO.setDetails(details);
        pcxGipVouBillDTO.setDetailBills(handleDrCrDetails(pcxGipCrDrDTO));
        // 有指标无借款，借款金额等于0，isPexExpBudUnLoan=1
        if (ObjectUtil.isEmpty(pcxGipCrDrDTO.getBill().getLoanAmt()) || ObjectUtil.isEmpty(pcxGipCrDrDTO.getBill().getLoanAmt())) {
            pcxGipCrDrDTO.getPcxGenVouDTO().getPcxTreasurerVouQO().setIsPexExpBudUnLoan(1);
        }
        pcxGipVouBillDTO.setPerd(pcxGipVouBillDTO.getVouPerd(pcxGipCrDrDTO.getPcxGenVouDTO().getPcxTreasurerVouQO().getLoginDate()));
        Map<String, Object> gipVouBillMap = MapUtil.bean2Map(pcxGipVouBillDTO);
        Map<String, Object> billMap = MapUtil.bean2Map(pcxGipCrDrDTO.getBill());
        Map<String, Object> treasurerVouMap = MapUtil.bean2Map(pcxGipCrDrDTO.getPcxGenVouDTO().getPcxTreasurerVouQO());
        gipVouBillMap.putAll(treasurerVouMap);
        gipVouBillMap.putAll(billMap);
        return gipVouBillMap;
    }

    @SneakyThrows
    private List<Map<String, Object>> handleDrCrDetails(PcxGipCrDrDTO pcxGipCrDrDTO) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> detail : pcxGipCrDrDTO.getDetails()) {
            Map<String, Object> billMap = MapUtil.bean2Map(pcxGipCrDrDTO.getBill());
            Map<String, Object> budBalanceMap = MapUtil.bean2Map(pcxGipCrDrDTO.getBudBalanceMap().get(detail.get(PcxGipConstants.BALANCE_ID)));
            Map<String, Object> treasurerVouMap = MapUtil.bean2Map(pcxGipCrDrDTO.getPcxGenVouDTO().getPcxTreasurerVouQO());
            Map<String, Object> drRow = new HashMap<>(billMap);
            drRow.put(PcxGipConstants.DETAIL_ID, IDGenerator.id());
            drRow.put(PcxGipConstants.TABLE_NAME, PcxGipConstants.PCX_BILL_EXPENSE);
            drRow.put(PcxGipConstants.EXP_AMT, detail.get(PcxGipConstants.CHECK_AMT));
            drRow.putAll(detail);
            drRow.putAll(treasurerVouMap);
            drRow.putAll(budBalanceMap);

            Map<String, Object> crRow = new HashMap<>(billMap);
            crRow.put(PcxGipConstants.DETAIL_ID, IDGenerator.id());
            crRow.put(PcxGipConstants.TABLE_NAME, PcxGipConstants.PCX_BILL_PAY_DETAIL);
            crRow.put(PcxGipConstants.PAY_AMT, detail.get(PcxGipConstants.CHECK_AMT));
            crRow.putAll(detail);
            crRow.putAll(treasurerVouMap);
            crRow.putAll(budBalanceMap);

            PcxGipUtil.extractedProType(pcxGipCrDrDTO, drRow, crRow);
            result.add(drRow);
            result.add(crRow);
        }
        pcxGipCrDrDTO.setAllDetails(result);
        return result;
    }

    /**
     * 生成凭证回调方法
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void genVou(List<GipBillVO> gipBillVOS) {
        if (CollectionUtil.isEmpty(gipBillVOS)) {
            log.error("生成凭证回调方法>gipBillVOS为空");
            throw new CommonException("生成凭证回调方法>gipBillVOS为空");
        }
        if (ObjectUtil.isEmpty(gipBillVOS.get(0).getVoucherId()) || ObjectUtil.isEmpty(gipBillVOS.get(0).getVouNo())
                || ObjectUtil.isEmpty(gipBillVOS.get(0).getVoucherDate()) || CollectionUtil.isEmpty(gipBillVOS.get(0).getBillIdList())) {
            log.error("生成凭证回调方法>gipBillVOS中单据id集合为空/凭证号/凭证id/凭证日期存在空值");
            throw new CommonException("生成凭证回调方法>gipBillVOS中单据id集合为空/凭证号/凭证id/凭证日期存在空值");
        }
        LambdaUpdateWrapper<PcxBill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(PcxBill::getId, gipBillVOS.get(0).getBillIdList());
        lambdaUpdateWrapper.set(PcxBill::getIsVou, 1);
        lambdaUpdateWrapper.set(PcxBill::getVouId, gipBillVOS.get(0).getVoucherId());
        lambdaUpdateWrapper.set(PcxBill::getVouNo, gipBillVOS.get(0).getVouNo());
        lambdaUpdateWrapper.set(PcxBill::getVouDate, gipBillVOS.get(0).getVoucherDate());
        pcxBillPlusService.update(lambdaUpdateWrapper);
    }

    /**
     * 撤销凭证回调方法
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelVou(List<GipAccountedRelation> gipAccountedRelations) {
        List<String> billIds = gipAccountedRelations.stream().map(GipAccountedRelation::getBillId).distinct().collect(Collectors.toList());
        if(CollectionUtil.isEmpty(billIds)) {
            log.error("撤销凭证回调方法>gipAccountedRelations中单据id集合为空");
            throw new CommonException("撤销凭证回调方法>gipAccountedRelations中单据id集合为空");
        }
        LambdaUpdateWrapper<PcxBill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(PcxBill::getId, billIds);
        lambdaUpdateWrapper.set(PcxBill::getIsVou, 0);
        lambdaUpdateWrapper.set(PcxBill::getVouId, "");
        lambdaUpdateWrapper.set(PcxBill::getVouNo, "");
        lambdaUpdateWrapper.set(PcxBill::getVouDate, "");
        pcxBillPlusService.update(lambdaUpdateWrapper);
    }

    /**
     * 单据撤销凭证回调方法(暂时无用)
     */
    @Override
    public void billCancelVou(List<GipBillAccounted> invalidBills) {}

    /**
     * 撤销凭证校验方法
     */
    @Override
    public com.pty.mad.common.CheckMsg isCancelOrDelVou(List<VoucherVO> voucherVOList) {
        if (CollectionUtil.isEmpty(voucherVOList)) {
            return com.pty.mad.common.CheckMsg.fail("voucherVOList为空");
        }
        List<String> billIds = voucherVOList.get(0).getGipBillIds();
        if (CollectionUtil.isEmpty(billIds)) {
            return com.pty.mad.common.CheckMsg.fail("gipBillId为空");
        }
        LambdaQueryWrapper<PcxBill> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(PcxBill::getId, billIds);
        List<PcxBill> pcxBills = pcxBillPlusService.list(lambdaQueryWrapper);
        //billIds和pcxBills长度是否一致
        if (billIds.size() != pcxBills.size() || pcxBills.isEmpty()) {
            return com.pty.mad.common.CheckMsg.fail("voucherVOList.get(0).getGipBillIds()和从数据库查的pcxBills个数不一致");
        }
        if (Objects.equals(BillFuncCodeEnum.LOAN.getCode(), pcxBills.get(0).getBillFuncCode())) {
            List<PcxBillRelation> pcxBillRelations = pcxBillRelationService.selectByRelBillIds(billIds);
            List<String> relBillIds = pcxBillRelations.stream().map(PcxBillRelation::getBillId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<PcxBill> relLambdaQueryWrapper = Wrappers.lambdaQuery();
            relLambdaQueryWrapper.in(PcxBill::getId, relBillIds);
            List<PcxBill> relPcxBills = pcxBillPlusService.list(lambdaQueryWrapper);
            //relPcxBills里报销单单据存在已记账
            if (relPcxBills.stream().anyMatch(bill -> Objects.equals(BillFuncCodeEnum.EXPENSE.getCode(), bill.getBillFuncCode())
                    && Objects.equals(1, bill.getIsVou()))) {
                return com.pty.mad.common.CheckMsg.fail("该借款单冲销的报销单已记账,不能进行此操作,请确认!");
            }
        }
        return com.pty.mad.common.CheckMsg.success();
    }
}
