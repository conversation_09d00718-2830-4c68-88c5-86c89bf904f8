package com.pty.pcx.ecs;


import com.alibaba.fastjson.JSONObject;
import com.pty.ecs.common.EcsServiceMsg;
import com.pty.pcx.dto.ecs.EcsMsgDTO;
import com.pty.pcx.dto.ecs.UpdateEcsBillDTO;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.ecs.BuildExpRelQO;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import com.pty.pcx.qo.ecs.compared.PcxEcsComparedResultQO;
import com.pty.pub.common.bean.Response;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

public interface IEcsBillExternalService {

    Map<String, List> queryBillList2(ExpInvoiceQO qo);

    EcsServiceMsg buildExpRel(BuildExpRelQO qo);

    EcsServiceMsg deleteExpRel(BuildExpRelQO qo);

    Response expInfoSave(UpdateEcsBillDTO updateEcsBillDTO);

    Map<String, List<PcxBasExpType>> queryBillExpTypeList(ExpInvoiceQO qo);

    Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMap(ExpInvoiceQO expInvoiceQO);

    Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMapByEcsIds(List<String> ecsBillIds, String fiscal, String agyCode, String mofDivCode);

    List<JSONObject> getEcsBillList(List<String> ecsBillIds, String fiscal, String agyCode, String mofDivCode);

    /**
     * 获取对票的分析数据
     * @param qo
     * @return
     */
    List<Map<String,String>> selectEcsAnalysisData(PcxEcsComparedResultQO qo);
}
