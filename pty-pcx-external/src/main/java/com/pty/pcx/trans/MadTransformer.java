package com.pty.pcx.trans;

import com.pty.mad.entity.*;
import com.pty.mad.vo.MadEmployeeCardVO;
import com.pty.pcx.dto.mad.*;
import com.pty.pcx.qo.mad.MadAgyAccountQO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MadTransformer {
    MadTransformer INSTANCE = Mappers.getMapper(MadTransformer.class);

    @Mappings(value = {
            @Mapping(target = "employeeCode", source = "madCode"),
            @Mapping(target = "employeeName", source = "madName")
    })
    MadEmployeeDTO toDTO(MadEmployee madEmployee);

    List<MadEmployeeDTO> toDTOs(List<MadEmployee> madEmployees);


    MadAgyAccount toMadAgyAccount(MadAgyAccountQO qo);

    com.pty.mad.qo.MadAgyAccountQO toMadAgyAccountQO(MadAgyAccountQO qo);

    MadAgyAccountDTO toMadAgyAccountDTO(MadAgyAccount madAgyAccount);

    List<MadAgyAccountDTO> toMadAgyAccountDTOs(List<MadAgyAccount> madAgyAccounts);

    MadEmployeeCardDTO toMadEmployeeCardDTO(MadEmployeeCardVO madEmployeeCardVO);

    List<MadEmployeeCardDTO> toMadEmployeeCardDTOs(List<MadEmployeeCardVO> madEmployeeCardVOs);

    MadCurrentDTO toMadCurrentDTO(MadCurrent madCurrent);

    List<MadCurrentDTO> toMadCurrentDTOs(List<MadCurrent> madCurrents);

    List<MadProjectDTO> toMadProjectDTOs(List<MadProject> select);
}
