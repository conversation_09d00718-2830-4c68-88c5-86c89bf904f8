package com.pty.pcx.mad.impl;

import cn.hutool.core.lang.Assert;
import com.pty.mad.entity.MadBankNode;
import com.pty.pcx.dto.mad.MadBankNodeDTO;
import com.pty.pcx.mad.IMadBankNodeExternalService;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.pty.mad.api.IMadBankNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Indexed
@Service
public class PtyMadBankNodeServiceImpl implements IMadBankNodeExternalService {

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IMadBankNodeService madBankNodeService;

    /**
     * 根据给定的查询参数，从数据库中查询符合条件的银行网点信息，并将结果转换为 DTO 格式返回。
     *
     * @param param 包含查询条件的参数映射，其中键为查询条件的字段名，值为相应的查询值
     * @return 返回一个包含符合条件的银行网点 DTO 对象列表
     */
    @Override
    public List<MadBankNodeDTO> select(Map<String, Object> param) {
        // 创建一个 MadBankNode 对象，用于封装查询条件
        MadBankNode madBankNode = new MadBankNode();
        // 如果查询条件中包含开户行网点名称，则设置到 madBankNode 对象中
        String bankNodeName = StringUtil.getStringValue(param.get("bankNodeName"));
        if (StringUtil.isNotEmpty(bankNodeName)) {
            madBankNode.setBanknodeName(bankNodeName);
        }
        // 如果查询条件中包含开户行行号，则设置到 madBankNode 对象中
        String bankNodeCode = StringUtil.getStringValue(param.get("bankNodeCode"));
        if (StringUtil.isNotEmpty(bankNodeCode)) {
            madBankNode.setBanknodeCode(bankNodeCode);
        }
        Object objectBankNodeNames = param.get("bankNodeNameList");
        // 使用 Optional 来处理 null 情况
        List<String> bankNodeNameList = Optional.ofNullable(objectBankNodeNames)
                .filter(obj -> obj instanceof List<?>)
                .map(obj -> (List<String>) obj)
                .orElse(Collections.emptyList());
        // mad加 list 银行名称
//        madBankNode.setBankNameList(bankNodeNameList);
        // 调用 madBankNodeService 查询符合条件的银行网点数据，返回查询结果
        List<MadBankNode> madBankNodes = madBankNodeService.list(madBankNode);
        // 将查询结果转换为 DTO 格式，并返回一个包含所有 DTO 的列表
        return madBankNodes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public MadBankNodeDTO selectByBankCodeAndBankNodeCode(String bankCode, String banknodeCode) {
        MadBankNode madBankNode = new MadBankNode();
        madBankNode.setBankCode(bankCode);
        madBankNode.setBanknodeCode(banknodeCode);
        List<MadBankNode> list = madBankNodeService.list(madBankNode);
        Assert.state(CollectionUtils.isNotEmpty(list), "找不到银行网点编码对应的银行信息, bankCode = {}, banknodeCode = {}", bankCode, banknodeCode);
        Assert.state(list.size() == 1, "根据银行代码和银行网点代码查询到多个结果, bankCode = {}, banknodeCode = {}", bankCode, banknodeCode);
        return this.convertToDto(list.get(0));
    }

    @Override
    public MadBankNodeDTO selectByBankNodeNo(String bankNodeNo) {
        MadBankNode madBankNode = new MadBankNode();
        madBankNode.setBanknodeNo(bankNodeNo);
        List<MadBankNode> list = madBankNodeService.list(madBankNode);
        Assert.state(CollectionUtils.isNotEmpty(list), "找不到银行网点行号对应的银行信息, bankNodeNo = {}", bankNodeNo);
        Assert.state(list.size() == 1, "根据银行网点行号查询到多个结果, bankNodeNo = {}", bankNodeNo);
        return this.convertToDto(list.get(0));
    }

    @Override
    public List<MadBankNodeDTO> selectByBankNodeNos(List<String> bankNodeNos) {
        if (CollectionUtils.isEmpty(bankNodeNos))
            return Collections.emptyList();
        MadBankNode madBankNode = new MadBankNode();
        madBankNode.setBanknodeNoList(bankNodeNos);
        List<MadBankNode> list = madBankNodeService.list(madBankNode);
        return list.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    public List<MadBankNodeDTO> selectByBankCodeAndBankNodeCodeList(List<String> bankCodes, List<String> bankNodeCodes) {
        if (CollectionUtils.isEmpty(bankCodes) || CollectionUtils.isEmpty(bankNodeCodes))
            return Collections.emptyList();
        MadBankNode madBankNode = new MadBankNode();
        madBankNode.setBankCodeList(bankCodes);
        madBankNode.setBanknodeCodeList(bankNodeCodes);
        List<MadBankNode> list = madBankNodeService.list(madBankNode);
        return list.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    public List<MadBankNodeDTO> selectByBankCodeAndBankNodeNoList(List<String> bankCodes, List<String> banknodeNos) {
        if (CollectionUtils.isEmpty(bankCodes) || CollectionUtils.isEmpty(banknodeNos))
            return Collections.emptyList();
        MadBankNode madBankNode = new MadBankNode();
        madBankNode.setBankCodeList(bankCodes);
        madBankNode.setBanknodeNoList(banknodeNos);
        List<MadBankNode> list = madBankNodeService.list(madBankNode);
        return list.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    private MadBankNodeDTO convertToDto(MadBankNode madBankNode) {
        MadBankNodeDTO dto = new MadBankNodeDTO();
        // 设置银行网点代码
        dto.setBankCode(madBankNode.getBankCode());
        // 设置银行名称
        dto.setBankName(madBankNode.getBankName());
        // 设置银行网点行号
        dto.setBankNodeNo(madBankNode.getBanknodeNo());
        // 设置银行网点代码
        dto.setBankNodeCode(madBankNode.getBanknodeCode());
        // 设置银行网点名称
        dto.setBankNodeName(madBankNode.getBanknodeName());
        // 设置银行网点地址
        dto.setBankNodeAddress(madBankNode.getBanknodeAddress());
        dto.setCityCode(madBankNode.getCityCode());
        dto.setCityName(madBankNode.getCityName());
        dto.setProvinceCode(madBankNode.getProvinceCode());
        dto.setProvinceName(madBankNode.getProvinceName());
        // 返回转换后的 DTO 对象
        return dto;
    }
}
