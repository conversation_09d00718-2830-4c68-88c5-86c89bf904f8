package com.pty.pcx.mad.impl;

import cn.hutool.core.bean.BeanUtil;
import com.pty.mad.entity.MadAgency;
import com.pty.mad.tree.AgencyTreeNode;
import com.pty.pcx.dto.mad.MadAgencyDTO;
import com.pty.pcx.dto.mad.MadAgencyTreeDTO;
import com.pty.pcx.mad.IMadAgencyExternalService;
import com.pty.pcx.qo.bas.MadAgencyQO;
import com.pty.pub.common.rest.RestClientReference;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.IMadAgencyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PtyMadAgencyServiceImpl implements IMadAgencyExternalService {
    @Autowired(required = false)
    @RestClientReference(microServiceNames = "mad")
    private IMadAgencyService madAgencyService;

    @Override
    public List<MadAgencyTreeDTO> selectAgyTree(MadAgencyQO dto) {
        MadAgency param = new MadAgency();
        BeanUtil.copyProperties(dto, param);
        List<AgencyTreeNode> agencyTree = madAgencyService.getAgencyTree(param);
        if (agencyTree != null && !agencyTree.isEmpty()) {
            return agencyTree.stream().map(agencyTreeNode -> {
                MadAgencyTreeDTO dto1 = new MadAgencyTreeDTO();
                BeanUtil.copyProperties(agencyTreeNode, dto1);
                return dto1;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<MadAgencyDTO> getAgencyListByCond(MadAgencyQO qo) {
        MadAgency param = new MadAgency();
        BeanUtil.copyProperties(qo,param);
        List<MadAgency> agencyListByCond = madAgencyService.getAgencyListByCond(param);
        if (agencyListByCond != null && !agencyListByCond.isEmpty()) {
            return agencyListByCond.stream().map(agency -> {
                MadAgencyDTO dto = new MadAgencyDTO();
                BeanUtil.copyProperties(agency,dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
