<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pty.pcx</groupId>
        <artifactId>pty-pcx</artifactId>
        <version>4.0.1.223-ENT-SNAPSHOT</version>
    </parent>

    <artifactId>pty-pcx-entity</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.rule</groupId>
            <artifactId>pty-rule-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>${mybatis-plus.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.pty.openapi</groupId>
            <artifactId>pty-openapi-entity</artifactId>
        </dependency>
    </dependencies>

</project>