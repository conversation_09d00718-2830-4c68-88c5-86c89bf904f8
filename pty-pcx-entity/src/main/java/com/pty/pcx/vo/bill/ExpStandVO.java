package com.pty.pcx.vo.bill;

import lombok.Data;

import java.util.List;

/**
 * 报销展示标准vo
 */
@Data
public class ExpStandVO {

    private String expenseTypeCode;
    private String expenseTypeName;
    private String standCode;
    private String standName;

    private List<ExpStandConditionVO> condition;

    private Integer seq;

    private String rowKeyCode;
    private String rowKeyName;

    private String rowValueCode;
    private String rowValueName;

    private String columnKeyCode;
    private String columnKeyName;

    private String columnValueCode;
    private String columnValueName;
    private String realColumnValue;
    private String realRowValue;


    private String valueSource;
    private String valueEditorCode;

    private List<ExpStandValueVO> standValue;
}
