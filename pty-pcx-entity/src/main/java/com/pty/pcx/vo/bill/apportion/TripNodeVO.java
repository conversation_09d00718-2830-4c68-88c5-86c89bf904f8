package com.pty.pcx.vo.bill.apportion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TripNodeVO {

    @ApiModelProperty("分段id")
    private String segmentId;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String finishTime;
    @ApiModelProperty("城市编码")
    private String cityCode;
    @ApiModelProperty("城市名称")
    private String cityName;
    @ApiModelProperty("金额")
    private BigDecimal amt;
}
