package com.pty.pcx.vo.ecs;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.vo.training.LabourExpDetailVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 理票返回vo
 */
@Data
public class EcsExpMatchVO {

    private String billId;

    private String departmentCode;
    private String departmentName;

    private String tidyEcsHint;

    public BigDecimal getTotalAmt() {
        BigDecimal totalAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(tripList)) {
            for (EcsBillTripVO tripVO : tripList) {
                BigDecimal decimal = tripVO.getEcsList().stream().map(EcsExpenseVO::getCheckAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                totalAmt = totalAmt.add(decimal);
            }
        }
        if (CollectionUtils.isNotEmpty(ecsExpList)) {
            BigDecimal decimal = ecsExpList.stream().map(EcsExpenseVO::getCheckAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            totalAmt = totalAmt.add(decimal);
        }
        if (CollectionUtils.isNotEmpty(subsidyList)) {
            BigDecimal decimal = subsidyList.stream().map(SubsidyVO::getTotalAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            totalAmt = totalAmt.add(decimal);
        }
        if (CollectionUtils.isNotEmpty(ecsRelList)) {
            BigDecimal ecsRelAmt = ecsRelList.stream().map(EcsRelVO::getCheckAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            totalAmt = totalAmt.add(ecsRelAmt);
        }
        if (CollectionUtils.isNotEmpty(unMatchEcsList)) {
            BigDecimal ecsRelAmt = unMatchEcsList.stream().map(EcsExpenseVO::getInputAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            totalAmt = totalAmt.add(ecsRelAmt);
        }

        if (CollectionUtils.isNotEmpty(detailLabourInfos)) {
            for (LabourExpDetailVO detailLabourInfo : detailLabourInfos) {
                if (detailLabourInfo != null) {
                    LabourExpDetailVO.LabourExpInfo labourExpInfo = detailLabourInfo.getLabourExpInfo();
                    if (labourExpInfo != null && labourExpInfo.getShouldAmt() != null) {
                        totalAmt = totalAmt.add(labourExpInfo.getShouldAmt());
                    }
                }
            }
        }

        return totalAmt;
    }

    /**
     * 行程信息
     */
    private List<EcsBillTripVO> tripList = new ArrayList<>();

    /**
     * 匹配的票列表
     */
    private List<EcsExpenseVO> ecsExpList = new ArrayList<>();

    /**
     * 未匹配的票列表
     */
    private List<EcsExpenseVO> unMatchEcsList = new ArrayList<>();

    /**
     * 错误票列表
     */
    private List<EcsWrongVO> wrongEcsList = new ArrayList<>();

    //按人员汇总成列表，可以点击看明细
    //差旅补助
    private List<SubsidyVO> subsidyList = new ArrayList<>();

    private List<EcsRelVO> ecsRelList;

    private List<EcsExpenseTypeAmtVO> expenseTypeAmtList;


    private List<String> expenseTypeCodeList;
    /**
     * TripTypeEnum
     */
    private String tripType = "";

    @JsonIgnore
    private List<EcsExpenseVO> allEcsList;

    @JsonIgnore
    List<JSONObject> ecsbills;

    @JsonIgnore
    List<String> noIncludedTripTaxiDetailIds = new ArrayList<>();

    // 讲课费、咨询费劳务人员信息（模拟生成票）
    private List<LabourExpDetailVO> detailLabourInfos;
}
