package com.pty.pcx.vo.ecs;

import com.pty.pcx.qo.ecs.UpdateEcsDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class EcsDetailAmtVO{

    private String ecsBillId;
    private String ecsDetailId;
    private String itemName;
    @NotBlank(message = "票关联id不能为空", groups = {UpdateEcsDetail.class})
    private String ecsRelId;
    @NotNull(message = "票金额不能为空", groups = {UpdateEcsDetail.class})
    private BigDecimal ecsAmt;
    @NotNull(message = "报销金额不能为空", groups = {UpdateEcsDetail.class})
    private BigDecimal inputAmt;
    //@NotNull(message = "发票数量不能为空", groups = {UpdateEcsDetail.class})
    private Integer ecsNum;
    private String remark;

    private String expenseTypeCode;
    private String expenseTypeName;

    private List<EcsRelItemVO.ExpenseTypeVO> expenseTypeList = new ArrayList<>();

    private List<EcsCommonDetailVO> detailList = new ArrayList<>();
}
