package com.pty.pcx.vo.printtemplate;

import com.pty.pcx.entity.printtemplate.PcxPrintTemplate;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: PcxPrintTemplateVO
 * @Description: 打印模版VO
 * @Date: 2024/11/13  下午3:50
 * @Author: wangbao
 **/
@Data
public class PcxPrintTemplateVO {

    /**
     * 报销单的打印模版
     */
    private List<PcxPrintTemplate>  expensePrintTemplateList;

    /**
     * 申请单的打印模版
     */
    private List<PcxPrintTemplate>  applyPrintTemplateList;

    /**
     * 借款单的打印模版
     */
    private List<PcxPrintTemplate>  loanPrintTemplateList;

    /**
     * 创建初始化对象，初始化字段的值
     */
    public static PcxPrintTemplateVO create(){
        PcxPrintTemplateVO pcxPrintTemplateVO = new PcxPrintTemplateVO();
        pcxPrintTemplateVO.setApplyPrintTemplateList(new ArrayList<>());
        pcxPrintTemplateVO.setExpensePrintTemplateList(new ArrayList<>());
        pcxPrintTemplateVO.setLoanPrintTemplateList(new ArrayList<>());
        return pcxPrintTemplateVO;
    }

}
