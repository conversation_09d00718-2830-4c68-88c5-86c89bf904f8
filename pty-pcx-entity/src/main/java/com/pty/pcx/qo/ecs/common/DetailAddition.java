package com.pty.pcx.qo.ecs.common;

import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DetailAddition extends ExpInvoiceQO {
    @NotNull(message = "费用承担部门不能为空")
    private String departmentCode;
    private String departmentName;

    private String budGetItemCode;
    private String budGetItem;

    private String accountingExpenseItemCode;
    private String accountingExpenseItem;

    private String customerCode;
    private String customer;

    private String biIncomeTypeCode;
    private String biIncomeType;

    private String accountingIncomeTypeCode;
    private String accountingIncomeType;

    private String tenderingProCode;
    private String tenderingPro;

    private String devProCode;
    private String devPro;

    private String a8PpProCode;
    private String a8PpPro;

    private String deferredAssetsCode;
    private String deferredAssets;
}
