package com.pty.pcx.qo.bas;

import com.pty.pcx.common.valid.Delete;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import com.pty.pcx.qo.PageQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PcxVehicleInfoQO extends PageQO implements Serializable {

    @ApiModelProperty("车辆的唯一标识 ID（雪花算法生成）")
    @NotBlank(message = "id不能为空", groups = {Delete.class})
    private String id;

    @ApiModelProperty("单位编码")
    @NotBlank(message = "agyCode不能为空", groups = {Query.class, Update.class})
    private String agyCode;

    @ApiModelProperty("区划")
    @NotBlank(message = "mofDivCode不能为空", groups = {Query.class, Update.class})
    private String mofDivCode;

    @ApiModelProperty("年度")
    @NotBlank(message = "fiscal不能为空", groups = {Query.class, Update.class})
    private String fiscal;

    @ApiModelProperty("车牌号")
    @Column(name = "license_plate")
    @NotBlank(message = "车牌号不能为空", groups = {Update.class})
    private String licensePlate;

    @ApiModelProperty("车牌号颜色")
    @Column(name = "license_plate_color")
    @NotBlank(message = "车牌号颜色不能为空", groups = {Update.class})
    private String licensePlateColor;

    @ApiModelProperty("车辆类型（如轿车、客车、货车等）")
    @Column(name = "vehicle_type")
    @NotBlank(message = "车辆类型不能为空", groups = {Update.class})
    private String vehicleType;

    @ApiModelProperty("车辆品牌")
    @Column(name = "brand")
    private String brand;

    @ApiModelProperty("车辆型号")
    @Column(name = "model")
    private String model;

    @ApiModelProperty("车辆颜色")
    @Column(name = "vehicle_color")
    private String vehicleColor;

    @ApiModelProperty("车辆识别号码（VIN）")
    @Column(name = "vin")
    private String vin;

    @ApiModelProperty("发动机号码")
    @Column(name = "engine_number")
    private String engineNumber;

    @ApiModelProperty("座位数")
    @Column(name = "seating_capacity")
    private Integer seatingCapacity;

    @ApiModelProperty("载货量（单位：千克或吨，根据实际情况）")
    @Column(name = "load_capacity")
    private BigDecimal loadCapacity;

    @ApiModelProperty("燃料类型（如汽油、柴油、电动等）")
    @Column(name = "fuel_type")
    private String fuelType;
}
