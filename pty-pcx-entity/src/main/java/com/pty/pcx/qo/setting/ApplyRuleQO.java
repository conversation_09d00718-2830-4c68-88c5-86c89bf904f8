package com.pty.pcx.qo.setting;


import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import com.pty.pcx.dto.rule.PaBizRuleDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 申请规则
 * <AUTHOR>
 * @date 2024/11/08
 */
@Data
public class ApplyRuleQO implements Serializable {

    /**
     * 费用编码
     */
    @ApiModelProperty("费用代码")
    @NotBlank(message = "费用代码不能为空", groups = {Update.class,Query.class})
    private String expenseCode;


    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    @NotBlank(message = "费用代码不能为空", groups = {Update.class,Query.class})
    private String agyCode;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    @NotBlank(message = "费用代码不能为空", groups = {Update.class,Query.class})
    private String mofDivCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    @NotBlank(message = "费用代码不能为空", groups = {Update.class,Query.class})
    private String fiscal;

    /**
     * 是否需要申请
     */
    @ApiModelProperty("是否需要申请")
    @NotNull(message = "是否需要申请不能为空", groups = {Update.class})
    private Integer isNeedApply;

    /**
     * 申请途径编码
     */
    @ApiModelProperty("申请途径编码")
    @NotBlank(message = "费用代码不能为空", groups = {Update.class})
    private String applyCtrlCode;

    /**
     * 申请途径名称
     */
    private String applyCtrlName;

    /**
     * 申请费用级次
     */
    private Integer applyCtrlLevel;

    /**
     * 是否需要申请
     */
    private Integer isNeedPlan;

    /**
     * 是否适用于费用明细
     */
    @ApiModelProperty("是否适用于费用明细")
    @NotNull(message = "费用代码不能为空", groups = {Update.class})
    private Integer isApplyDetail;

    /**
     * 业务规则数据
     */
    @ApiModelProperty("业务规则数据list")
    @NotNull(message = "业务规则数据不能为空", groups = {Update.class})
    private List<PaBizRuleDto> paBizRuleDtos;


}
