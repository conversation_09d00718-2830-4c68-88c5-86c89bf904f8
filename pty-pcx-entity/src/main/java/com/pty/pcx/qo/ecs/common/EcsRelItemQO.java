package com.pty.pcx.qo.ecs.common;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class EcsRelItemQO{
    @NotBlank(message = "票关联id不能为空")
    private String ecsRelId;
    @NotBlank(message = "费用类型编码不能为空")
    private String expenseTypeCode;
    @NotBlank(message = "费用类型名称不能为空")
    private String expenseTypeName;

    @NotNull(message = "核定金额不能为空")
    private BigDecimal checkAmt;

    private BigDecimal taxAmt;

    private BigDecimal taxRate;
}
