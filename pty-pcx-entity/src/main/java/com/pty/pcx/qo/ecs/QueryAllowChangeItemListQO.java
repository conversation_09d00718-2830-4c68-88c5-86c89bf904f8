package com.pty.pcx.qo.ecs;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: niemancun
 * @date: 2022/5/17
 * @description: 查询可以变更的事项列表
 **/
@Data
public class QueryAllowChangeItemListQO {

    @NotBlank(message = "报销单id不能为空")
    private String billId;
    @NotBlank(message = "年度不能为空")
    private String fiscal;
    @NotBlank(message = "单位编码不能为空")
    private String agyCode;
    @NotBlank(message = "区划不能为空")
    private String mofDivCode;
    @NotBlank(message = "人员编码不能为空")
    private String userCode;
}
