package com.pty.pcx.qo.block;


import com.pty.pcx.common.enu.FormSettingEnums;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class BlockPropertyQO {
    private String userCode;

    private String userName;

    private String mofDivCode;

    private String agyCode;

    private String fiscal;

    private String tenantId;

    // 单据类型
    private FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum;

    // 费用类型code集合
    private List<String> basExpTypeCodes;

    // 块儿分类类型
    private String classifyCode;

    // 事项分类
    private String itemCode;

    private String billId;
}
