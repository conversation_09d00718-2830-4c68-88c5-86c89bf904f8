package com.pty.pcx.qo.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PeakDateQO implements Serializable {

    @ApiModelProperty("开始日期")
    private String startDay;

    @ApiModelProperty("结束日期")
    private String endDay;

    @ApiModelProperty("开始日期")
    private LocalDate startLocalDate;

    @ApiModelProperty("结束日期")
    private LocalDate endLocalDate;

    public PeakDateQO(String startDay, String endDay) {
        this.startDay = startDay;
        this.endDay = endDay;
    }


}
