package com.pty.pcx.qo.bill;

import cn.hutool.crypto.SecureUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PcxBillPaySettlementBalanceQO implements Serializable {


//    @ApiModelProperty("单据主键ID")
//    private String billId;
//
//    @ApiModelProperty("结算方式类型")
//    private String settlementType;
//
//    @ApiModelProperty("结算方式名称")
//    private String settlementName;
//
//    @ApiModelProperty("结算方式使用金额")
//    private BigDecimal inputAmt;

    @ApiModelProperty("结算方式现金指标映射")
    @Builder.Default
    private List<SettlementExtQO> settle_cash = new ArrayList<>();

    @ApiModelProperty("结算方式汇款转账指标映射")
    @Builder.Default
    private List<SettlementExtQO> settle_transfer = new ArrayList<>();

    @ApiModelProperty("结算方式公务卡指标映射")
    @Builder.Default
    private List<SettlementExtQO> settle_busicard = new ArrayList<>();

    @ApiModelProperty("结算方式支票指标映射")
    @Builder.Default
    private List<SettlementExtQO> settle_cheque = new ArrayList<>();


    @ApiModelProperty("结算方式支票指标映射")
    @Builder.Default
    private List<SettlementExtQO> settle_loan = new ArrayList<>();



//    @ApiModelProperty("结算方式指标映射")
//    @Builder.Default
//    private List<SettlementExtQO> settlementExtVOS = new ArrayList<>();


    @Data
    @SuperBuilder
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static final class SettlementExtQO extends PcxBillSettlement implements Serializable {

//        @ApiModelProperty("付款方账号")
//        private String payAccountNo;
//
//        @ApiModelProperty("付款方名称")
//        private String payAccountName;
//
//        @ApiModelProperty("付款方开户行")
//        private String payBankName;
//
//        @ApiModelProperty("付款方账号类型")
//        private String payAccountType;
//
//        @ApiModelProperty("付款方开户城市")
//        private String payAccCity;
//
//        @ApiModelProperty("付款方银行编码")
//        private String payBankCode;

        @ApiModelProperty("费用类型代码")
        private String expenseCode;

        @ApiModelProperty("经济分类代码")
        private String expecoCode;

        @ApiModelProperty("分配的指标/预算")
        private List<BalanceExtQO> balanceList;

        @ApiModelProperty("收款方开户行节点编码")
        private String payeeBankNodeCode;

        @ApiModelProperty("收款方开户行行号")
        private String payeeBankNodeNo;

        @ApiModelProperty("收款方开户城市")
        private String payeeAccCity;

        @ApiModelProperty("收款方银行编码")
        private String payeeBankNodeName;

        @ApiModelProperty("收款方银行编码")
        private String payeeBankCode;

        @ApiModelProperty("收款方账号")
        @JsonProperty("receiveAccountNo")
        private String payeeAccNo;

        @ApiModelProperty("收款方银行网点行号")
        private String payeeAccCode;

        @ApiModelProperty("收款方名称")
        @JsonProperty("receiveAccountName")
        private String payeeAccName;

        @ApiModelProperty("收款方开户行")
        @JsonProperty("receiveBank")
        private String payeeBankName;

        @ApiModelProperty("收款方账户类型")
        private String payeeAccountType;

        @ApiModelProperty("收款方账户类型名称")
        private String payeeAccountTypeName;

        @ApiModelProperty("结算方式类型名称")
        private String settlementTypeName;

        @JsonIgnore
        @Builder.Default
        private BigDecimal allocatedAmt = BigDecimal.ZERO;

        @ApiModelProperty("是否满足匹配")
        private Boolean isSatisfied;

        public void setSettlementUk(String settlementUk) {
            super.setSettlementUk(StringUtils.isNotBlank(settlementUk) ? settlementUk : SecureUtil.md5(this.getBillId() + this.getSettlementType() + StringUtil.getStringValue(this.getPayeeAccNo())));
        }
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BalanceExtQO implements Serializable {
        @ApiModelProperty("支付详情ID")
        private String payDetailId;

        @ApiModelProperty("指标ID")
        private String balanceId;

        @ApiModelProperty("指标编号")
        private String balanceNo;

        @ApiModelProperty("指标类型")
        private String balanceType;

        @ApiModelProperty("部门编码")
        private String departmentCode;

        @ApiModelProperty("部门")
        private String department;

        @ApiModelProperty("费用类型代码")
        @JsonProperty("expenseTypeCodes")
        private String expenseCode;

        @ApiModelProperty("费用类型名称")
        @JsonProperty("expenseTypeNames")
        private String expenseName;

        @ApiModelProperty("经济分类代码")
        private String expecoCode;

        @ApiModelProperty("经济分类名称")
        private String expeco;

        @ApiModelProperty("预算项目代码")
        private String projectCode;

        @ApiModelProperty("预算项目名称")
        private String project;

        @ApiModelProperty("使用金额")
        @JsonProperty("currentUsedAmt")
        private BigDecimal usedAmt;

        @ApiModelProperty("可用余额")
        private BigDecimal balanceAmt;

        @ApiModelProperty("预算金额")
        private BigDecimal totalAmt;

        /**
         * 前端使用的唯一值
         * MD5(balanceId/expenseCode)
         */
        private String spanId;
        @ApiModelProperty("付款方账号")
        private String payAccountNo;

        @ApiModelProperty("付款方名称")
        private String payAccountName;

        @ApiModelProperty("付款方开户行")
        private String payBankName;

        @ApiModelProperty("付款方账号类型")
        private String payAccountType;

        @ApiModelProperty("付款方账号类型名称")
        private String payAccountTypeName;

        @ApiModelProperty("付款方开户城市")
        private String payAccCity;

        @ApiModelProperty("付款方银行编码")
        private String payBankCode;

        @ApiModelProperty("页面展示付款账号信息")
        private String payAccountInfo;

        @ApiModelProperty("指标来源apply, loan")
        private String balanceSource;

        @ApiModelProperty("关联单据id")
        private String relBillId;

        @ApiModelProperty("关联单据编码")
        private String relBillNo;

        @JsonIgnore
        @Builder.Default
        private BigDecimal allocatedAmt = BigDecimal.ZERO;
    }
}
