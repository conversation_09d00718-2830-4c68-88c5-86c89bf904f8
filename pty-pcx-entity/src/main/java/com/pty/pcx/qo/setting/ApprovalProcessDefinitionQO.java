package com.pty.pcx.qo.setting;

import com.pty.pcx.common.valid.Query;
import com.pty.pcx.vo.ApprovalProcessDefinitionVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ApprovalProcessDefinitionQO {

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "区划")
    @NotBlank(message = "区划不能为空", groups = Query.class)
    private String mofDivCode;

    @ApiModelProperty(value = "机构代码")
    @NotBlank(message = "机构代码不能为空", groups = Query.class)
    private String agyCode;

    @ApiModelProperty(value = "单据类型")
    @NotBlank(message = "单据类型不能为空", groups = Query.class)
    private String billType;

    @ApiModelProperty(value = "工作流ID")
    private String wfId;

    @ApiModelProperty(value = "工作流名称")
    private String wfName;

    @ApiModelProperty(value = "审核步骤")
    List<ApprovalProcessDefinitionVO.DefinitionRow> auditSteps;
}
