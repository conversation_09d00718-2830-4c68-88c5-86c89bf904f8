package com.pty.pcx.qo.ecs.common;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class EcsCommonBaseQO {
    @NotBlank(message = "年度不能为空")
    private String fiscal;
    @NotBlank(message = "单位不能为空")
    private String agyCode;
    @NotBlank(message = "区划不能为空")
    private String mofDivCode;
    @NotBlank(message = "人员编码不能为空")
    private String userCode;
    @NotBlank(message = "人员名称不能为空")
    private String userName;
}
