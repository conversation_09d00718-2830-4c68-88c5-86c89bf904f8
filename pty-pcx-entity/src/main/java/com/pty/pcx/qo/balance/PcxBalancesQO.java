package com.pty.pcx.qo.balance;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PcxBalancesQO {

    private String userCode;

    private String userName;

    private String mofDivCode;

    private String agyCode;

    private String fiscal;

    private String tenantId;

    private List<String> projectCodes;
    /**
     * 根据部门编码获取部门的所有父级部门
     */
    private List<String> departmentCodes;

    private String departmentCode;

    private String expenseCode;

    private String employeeCode;
    /***
     * 岗位编码
     */
    private String positionCode;
    /***
     * 事项编码
     */
    private String itemCode;

    /**
     * 搜索关键字
     */
    private String keyword;
}
