package com.pty.pcx.qo.ecs;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description: 未匹配的ecs补充行程
 * <AUTHOR>
 */
@Data
public class UnMatchEcsReplenishQO {

    @NotBlank(message = "报销单id不能为空")
    private String billId;
    @NotBlank(message = "年度不能为空")
    private String fiscal;
    @NotBlank(message = "单位不能为空")
    private String agyCode;
    @NotBlank(message = "区划不能为空")
    private String mofDivCode;

    @NotEmpty(message = "票id不能为空")
    private List<String> ecsBillIds;
    @NotBlank(message = "明细id不能为空")
    private String detailId;
}
