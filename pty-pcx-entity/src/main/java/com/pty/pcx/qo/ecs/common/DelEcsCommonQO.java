package com.pty.pcx.qo.ecs.common;

import com.pty.pcx.qo.ecs.DelEcs;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DelEcsCommonQO extends ExpInvoiceQO {
    @NotBlank(message = "billId不能为空", groups = {DelEcs.class})
    private String billId;
    /**
     * 票关联id
     */
    @NotBlank(message = "票关联不能为空", groups = {DelEcs.class})
    private String ecsRelId;
}
