package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(schema = "pcx_settle_balance")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PcxSettleBalance implements Serializable {

    private static final long serialVersionUID = 584124560115375213L;

    @Id
    @ApiModelProperty("主键，唯一标识每条记录")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("单据ID")
    @Column(name = "bill_id")
    private String billId;

    @ApiModelProperty("指标ID")
    @Column(name = "balance_id")
    private String balanceId;

    @ApiModelProperty("指标编号")
    @Column(name = "balance_no")
    private String balanceNo;

    @ApiModelProperty("该指标结项金额")
    @Column(name = "settle_amt")
    private BigDecimal settleAmt;

    @ApiModelProperty("结项状态（0，已结项，未重启；1，已重启）")
    @Column(name = "settle_status")
    private String settleStatus;

    @ApiModelProperty("创建人编码")
    @Column(name = "creator")
    private String creator;

    @ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

}
