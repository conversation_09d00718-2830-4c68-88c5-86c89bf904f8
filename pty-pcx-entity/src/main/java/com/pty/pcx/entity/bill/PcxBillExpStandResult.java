package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 费用支出标准结果(PcxBillExpStandResult)实体类
 *
 * <AUTHOR>
 * @since 2024-12-05 15:35:00
 */
@Data
@Entity
@Table(schema = "pcx_bill_exp_stand_result")
public class PcxBillExpStandResult implements Serializable {
    private static final long serialVersionUID = -98519996968518856L;

    @Id
	@ApiModelProperty("关联表主键Id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单号")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("关联费用ID")
    @Column(name = "expense_id")
    private String expenseId;
    
	@ApiModelProperty("费用明细ID")
    @Column(name = "detail_id")
    private String detailId;

    @ApiModelProperty("票Id")
    @Column(name = "ecs_bill_id")
    private String ecsBillId;
    
	@ApiModelProperty("标准code")
    @Column(name = "stand_code")
    private String standCode;
    
	@ApiModelProperty("标准name")
    @Column(name = "stand_name")
    private String standName;
    
	@ApiModelProperty("标准值")
    @Column(name = "stand_value")
    private String standValue;
    
	@ApiModelProperty("标准值名称")
    @Column(name = "stand_value_name")
    private String standValueName;
    
	@ApiModelProperty("超标原因")
    @Column(name = "reason")
    private String reason;
    
	@ApiModelProperty("超标处理方式")
    @Column(name = "handle_type")
    private String handleType;
    
	@ApiModelProperty("标准快照")
    @Column(name = "stand_snapshot")
    private String standSnapshot;
    
	@ApiModelProperty("单位名称")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    public BigDecimal getStandBigDecimal() {
        try {
            return new BigDecimal(standValue);
        }catch (Exception e){
            return BigDecimal.ZERO;
        }
    }
}
