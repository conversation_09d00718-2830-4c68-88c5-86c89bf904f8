package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单据费用明细_通用_明细(PcxBillExpDetailCommon)实体类
 *
 * <AUTHOR>
 * @since 2024-12-05 13:36:45
 */
@Data
@Entity
@Table(schema = "pcx_bill_exp_detail_common")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PcxBillExpDetailCommon extends PcxBillExpDetailBase implements Serializable {
    private static final long serialVersionUID = 341227346185533486L;

    @ApiModelProperty("标准金额")
    @Column(name = "stand_amt")
    private BigDecimal standAmt;

    @ApiModelProperty("来源 ecs，replenish，manual")
    @Column(name = "source")
    private String source;

    @ApiModelProperty("抵扣税率")
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @ApiModelProperty("抵扣税额")
    @Column(name = "tax_amt")
    private BigDecimal taxAmt;

    @ApiModelProperty("发票张数")
    @Column(name = "ecs_num")
    private Integer ecsNum = 1;

    @ApiModelProperty("无票原因")
    @Column(name = "no_ecs_reason")
    private String noEcsReason;

	@ApiModelProperty("自定义06字段code")
    @Column(name = "acitem06_code")
    private String acitem06Code;
    
	@ApiModelProperty("自定义06字段name")
    @Column(name = "acitem06_name")
    private String acitem06Name;
    
	@ApiModelProperty("自定义07字段code")
    @Column(name = "acitem07_code")
    private String acitem07Code;
    
	@ApiModelProperty("自定义07字段name")
    @Column(name = "acitem07_name")
    private String acitem07Name;
    
	@ApiModelProperty("自定义08字段code")
    @Column(name = "acitem08_code")
    private String acitem08Code;
    
	@ApiModelProperty("自定义08字段name")
    @Column(name = "acitem08_name")
    private String acitem08Name;
    
	@ApiModelProperty("自定义09字段code")
    @Column(name = "acitem09_code")
    private String acitem09Code;
    
	@ApiModelProperty("自定义09字段name")
    @Column(name = "acitem09_name")
    private String acitem09Name;
    
	@ApiModelProperty("自定义10字段code")
    @Column(name = "acitem10_code")
    private String acitem10Code;
    
	@ApiModelProperty("自定义10字段name")
    @Column(name = "acitem10_name")
    private String acitem10Name;

    @ApiModelProperty("备注")
    @Column(name = "remark")
    private String remark;


    @TableField(exist = false)
    private String expenseTypeCode;

    
}
