package com.pty.pcx.entity.stand.qo;

import com.pty.pcx.common.valid.Enable;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PcxStandAbroadQO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("单位编码")
    @NotBlank(message = "agyCode不能为空", groups = {Query.class})
    private String agyCode;

    @ApiModelProperty("区划")
    @NotBlank(message = "mofDivCode不能为空", groups = {Query.class})
    private String mofDivCode;

    @ApiModelProperty("年度")
    @NotBlank(message = "fiscal不能为空", groups = {Query.class})
    private String fiscal;



    @ApiModelProperty("住宿费标准金额")
    @Min(value = 0, message = "住宿费标准金额不能小于0", groups = {Update.class})
    @NotNull(message = "住宿费标准金额不能为空")
    private BigDecimal stayAmt;

    @ApiModelProperty("伙食费标准金额")
    @Min(value = 0, message = "伙食费标准金额不能小于0", groups = {Update.class})
    @NotNull(message = "伙食费标准金额不能为空")
    private BigDecimal foodAmt;

    @ApiModelProperty("杂费标准金额")
    @Min(value = 0, message = "杂费标准金额不能小于0", groups = {Update.class})
    @NotNull(message = "杂费标准金额不能为空")
    private BigDecimal otherAmt;


    @ApiModelProperty("主键id集合")
    @NotEmpty(message = "ids不能为空", groups = {Enable.class})
    private List<String> ids;

    @ApiModelProperty("国家代码")
    @NotBlank(message = "countryCode不能为空", groups = {Update.class})
    private String countryCode;

    @ApiModelProperty("城市代码集合")
    @NotEmpty(message = "cityCodes不能为空", groups = {Update.class})
    private List<String> cityCodeSet;

    @ApiModelProperty("币种代码")
    @NotBlank(message = "currencyCode不能为空", groups = {Update.class})
    private String currencyCode;

    @ApiModelProperty("币种名称")
    @NotBlank(message = "currencyName不能为空", groups = {Update.class})
    private String currencyName;

    @ApiModelProperty("是否启用")
    @NotNull(message = "isEnabled不能为空", groups = {Enable.class})
    private Integer isEnabled;
}
