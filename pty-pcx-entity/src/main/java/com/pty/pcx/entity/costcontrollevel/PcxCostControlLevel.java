package com.pty.pcx.entity.costcontrollevel;

import com.pty.pub.common.anno.Desc;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 费用控制级别(PcxCostControlLevel)实体类
 */
@Data
@Entity
@Table(name = "pcx_cost_control_level")
@Desc("费用控制级别")
public class PcxCostControlLevel implements Serializable {

    private static final long serialVersionUID = 447433923033163231L;

    /**
     * 主键
     */
    private String id;
    /**
     * 单位编码
     */
    private String agyCode;
    /**
     * 区划
     */
    private String mofDivCode;
    /**
     * 年度
     */
    private String fiscal;
    /**
     * 费用控制编码
     */
    private String costControlCode;
    /**
     * 岗位职级
     */
    private String positionRank;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 费用类型编码
     */
    private String expenseTypeCode;
    /**
     * 费用类型名称
     */
    private String expenseTypeName;
    /**
     * 0=否 1=是
     */
    private Integer isEnabled;
    /**
     * 修改人编码
     */
    private String modifier;
    /**
     * 修改人名称
     */
    private String modifierName;
    /**
     * 修改时间
     */
    private String modifiedTime;
    /**
     * 创建人编码
     */
    private String creatorCode;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private String createdTime;

}

