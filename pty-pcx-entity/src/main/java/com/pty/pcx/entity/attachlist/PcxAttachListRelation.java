package com.pty.pcx.entity.attachlist;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * PaAttachListRelation 实体类，对应于 pa_attach_list_relation 表
 */
@Data
@Table(name = "pa_attach_list_relation")
@NoArgsConstructor
public class PcxAttachListRelation implements Serializable {

    private static final long serialVersionUID = 633364575805616477L;

    /**
     * 附件清单关系ID，主键
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 附件主键
     */
    @Column(name = "attach_id")
    private String attachId;

    /**
     * 附件类型ID
     */
    @Column(name = "attach_type_id")
    private String attachTypeId;

    /**
     * 单据ID（暂不考虑）
     */
    @Column(name = "bill_id")
    private String billId;

    /**
     * 附件清单ID
     */
    @Column(name = "attach_list_id")
    private String attachListId;

    /**
     * 上传方式
     */
    @Column(name = "upload_way")
    private Integer uploadWay;


    @TableField(exist = false)
    private List<String> billIds;

    @TableField(exist = false)
    private List<String> attachListIds;
    /**
     * 附件清单名称
     */
    @TableField(exist = false)
    private String attachListName;

}
