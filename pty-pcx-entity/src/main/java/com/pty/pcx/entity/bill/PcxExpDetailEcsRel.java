package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pty.pcx.common.constant.PcxConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 报销单费用明细与票附件关联表，存储费用明细与票务附件的关系信息(PcxExpDetailEcsRel)实体类
 *
 * <AUTHOR>
 * @since 2024-11-27 19:47:50
 */
@Data
@Entity
@Table(schema = "pcx_exp_detail_ecs_rel")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PcxExpDetailEcsRel implements Serializable {
    private static final long serialVersionUID = -16661966422303366L;

    @Id
    @ApiModelProperty("主键，唯一标识每条记录")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("单据费用明细id，关联费用明细表")
    @Column(name = "detail_id")
    private String detailId;

    @ApiModelProperty("单据费用明细id，关联费用明细表")
    @Column(name = "expense_id")
    private String expenseId;

    @ApiModelProperty("费用类型")
    @Column(name = "expense_type_code")
    private String expenseTypeCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_type_name")
    private String expenseTypeName;

    @ApiModelProperty("是否匹配")
    @Column(name = "is_confirm")
    private Integer isConfirm;

    @ApiModelProperty("附件id")
    @Column(name = "file_id")
    private String fileId;

    @ApiModelProperty("附件名称")
    @Column(name = "file_name")
    private String fileName;

    @ApiModelProperty("票id，关联票务信息")
    @Column(name = "ecs_bill_id")
    private String ecsBillId;

    @ApiModelProperty("ecs类型，标识票务类型")
    @Column(name = "ecs_bill_type")
    private String ecsBillType;

    @ApiModelProperty("发票属性(paper：纸质影像, std:数据标准, elec：普通电子)")
    @Column(name = "ecs_bill_kind")
    private String ecsBillKind;

    @ApiModelProperty("发票号")
    @Column(name = "ecs_bill_no")
    private String ecsBillNo;

    @ApiModelProperty("票明细id")
    @Column(name = "ecs_detail_id")
    private String ecsDetailId;

    @ApiModelProperty("发票说明")
    @Column(name = "ecs_bill_desc")
    private String ecsBillDesc;

    @ApiModelProperty("手动添加分组")
    @Column(name = "manual_key")
    private String manualKey;

    @ApiModelProperty("报销单id，关联报销单信息")
    @Column(name = "bill_id")
    private String billId;

    @ApiModelProperty("事项")
    @Column(name = "item_name")
    private String itemName;

    @ApiModelProperty("单位")
    @Column(name = "item_unit")
    private String itemUnit;

    @ApiModelProperty("数量")
    @Column(name = "item_qty")
    private String itemQty;

    @ApiModelProperty("单价")
    @Column(name = "item_price")
    private BigDecimal itemPrice;

    @ApiModelProperty("票时间")
    @Column(name = "ecs_bill_date")
    private String ecsBillDate;

    @ApiModelProperty("职员编码")
    @Column(name = "emp_code")
    private String empCode;

    @ApiModelProperty("票金额")
    @Column(name = "ecs_amt")
    private BigDecimal ecsAmt;

    @ApiModelProperty("录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;

    @ApiModelProperty("核定金额")
    @Column(name = "check_amt")
    private BigDecimal checkAmt;

    @ApiModelProperty("ecs展示数据，存储票务展示信息")
    @Column(name = "ecs_content")
    private String ecsContent;

    @ApiModelProperty("无票原因")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty("燃油附加费")
    @Column(name = "fsc_amt")
    private BigDecimal fscAmt;

    @ApiModelProperty("民航发展基金")
    @Column(name = "cadf_amt")
    private BigDecimal cadfAmt;

    @ApiModelProperty("其他费用")
    @Column(name = "other_amt")
    private BigDecimal otherAmt;

    @ApiModelProperty("保险费")
    @Column(name = "insured_amt")
    private BigDecimal insuredAmt;

    @ApiModelProperty("是否实名")
    @Column(name = "is_real_name")
    private Integer isRealName;

    @ApiModelProperty("税率")
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @ApiModelProperty("税额")
    @Column(name = "tax_amt")
    private BigDecimal taxAmt;

    @ApiModelProperty("财务岗票验证状态")
    @Column(name = "ecs_check_status")
    private Integer ecsCheckStatus;

    @ApiModelProperty("财务岗票未通过原因")
    @Column(name = "check_reason")
    private String checkReason;

    @ApiModelProperty("创建时间，记录创建记录的时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    @ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    @ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("报销人所在单位社会信用代码(校验税号)")
    @Column(name = "pur_tax_id")
    private String purTaxId;

    @ApiModelProperty("报销人所在单位名称")
    @Column(name = "pur_name")
    private String purName;

    @ApiModelProperty("报销人所在单位开户行及账号")
    @Column(name = "pur_account_no")
    private String purAccountNo;

    @ApiModelProperty("销售方名称")
    @Column(name = "seller_name")
    private String sellerName;

    @ApiModelProperty("销售方纳税人识别号")
    @Column(name = "seller_tax_id")
    private String sellerTaxId;

    //通用报销保存费用类型，不是费用明细项
    @TableField(exist = false)
    private String baseExpTypeCode;
    @TableField(exist = false)
    private String baseExpTypeName;

    // 扩展要素的默认值，主要从发票或附件内获取
    @TableField(exist = false)
    private Map<String, Object> extItemDefValMap;

    // 票是否包含事项费用类型,第一位0没有费用，1有费用，第二位1费用包含在事项中，0费用不包含在事项中
    @TableField(exist = false)
    private int expenseTypeMatchBit = 0;

    public boolean isExpenseTypeMatch(){
        return (expenseTypeMatchBit & PcxConstant.ECS_HAVE_EXPENSE_TYPE) == 0 || (expenseTypeMatchBit & PcxConstant.ECS_EXPENSE_INCLUDE_BY_ITEM) > 0;
    }

    /**
     * 父级编码
     */
    @TableField(exist = false)
    private String parentCode;

    /**
     * 最顶层编码
     */
    @TableField(exist = false)
    private String lastCode;

    public PcxExpDetailEcsRel(String agyCode, String fiscal, String mofDivCode, String tenantId) {
        this.agyCode = agyCode;
        this.fiscal = fiscal;
        this.mofDivCode = mofDivCode;
        this.tenantId = tenantId;
    }
}

