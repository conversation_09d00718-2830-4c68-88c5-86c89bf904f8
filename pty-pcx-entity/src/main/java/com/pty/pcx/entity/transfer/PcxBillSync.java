package com.pty.pcx.entity.transfer;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 单据同步信息
 */
@Data
@TableName("pcx_bill_sync")
public class PcxBillSync {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 单据ID
     */
    private String billId;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 外部系统编码
     */
    private String systemCode;

    /**
     * 外部系统名称
     */
    private String systemName;

    /**
     * 外部系统返回的单据编号
     */
    private String externalBillId;

    /**
     * 外部系统编号
     */
    private String externalBillNo;

    /**
     * 同步状态
     */
    private Integer syncStatus;

    /**
     * 同步时间
     */
    private Date syncTime;

    /**
     * 同步消息
     */
    private String syncMessage;

    /**
     * 单据状态
     */
    private Integer billStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 付款单ID
     */
    private String paymentId;

    /**
     * 付款单编号
     */
    private String paymentNo;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmt;

    /**
     * 付款日期
     */
    private Date paymentDate;

    /**
     * 付款状态
     */
    private String paymentStatus;

    /**
     * 业务日期
     */
    private String businessDate;

    /**
     * 会计年度
     */
    private String fiscalYear;

    /**
     * 会计期数
     */
    private String fiscalPeriod;

    /**
     * 凭证字
     */
    private String voucherWord;

    /**
     * 凭证号
     */
    private String voucherNo;

    /**
     * 单位编码
     */
    private String agyCode;

    /**
     * 年度
     */
    private String fiscal;

    /**
     * 区划
     */
    private String mofDivCode;

    /**
     * 租户ID
     */
    private String tenantId;

}