package com.pty.pcx.entity.stand;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 支出标准要素定义表(PcxStandKey)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:44:25
 */
@Data
@Entity
@Table(schema = "pcx_stand_key")
public class PcxStandKey implements Serializable {
    private static final long serialVersionUID = 151745860164050238L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("费用类型编码")
    @Column(name = "expense_type_code")
    private String expenseTypeCode;
    
	@ApiModelProperty("费用类型名称")
    @Column(name = "expense_type_name")
    private String expenseTypeName;
    
	@ApiModelProperty("标准代码(后面关联筛选条件表、明细表)")
    @Column(name = "stand_code")
    private String standCode;
    
	@ApiModelProperty("标准名称")
    @Column(name = "stand_name")
    private String standName;
    
	@ApiModelProperty("优先级")
    @Column(name = "seq")
    private Integer seq;
    
	@ApiModelProperty("行要素编码")
    @Column(name = "row_key_code")
    private String rowKeyCode;
    
	@ApiModelProperty("行要素名称")
    @Column(name = "row_key_name")
    private String rowKeyName;
    
	@ApiModelProperty("行要素值编码")
    @Column(name = "row_value_code")
    private String rowValueCode;
    
	@ApiModelProperty("行要素值名称")
    @Column(name = "row_value_name")
    private String rowValueName;
    
	@ApiModelProperty("列要素编码")
    @Column(name = "column_key_code")
    private String columnKeyCode;
    
	@ApiModelProperty("列要素名称")
    @Column(name = "column_key_name")
    private String columnKeyName;
    
	@ApiModelProperty("列要素值编码")
    @Column(name = "column_value_code")
    private String columnValueCode;
    
	@ApiModelProperty("列要素值名称")
    @Column(name = "column_value_name")
    private String columnValueName;
    
	@ApiModelProperty("创建人")
    @Column(name = "creater")
    private String creater;
    
	@ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private String createTime;
    
	@ApiModelProperty("修改人")
    @Column(name = "updater")
    private String updater;
    
	@ApiModelProperty("修改时间")
    @Column(name = "update_time")
    private String updateTime;
    
	@ApiModelProperty("是否删除（0否、1是）")
    @Column(name = "is_deleted")
    private Integer isDeleted;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("是否启用")
    @Column(name = "is_enabled")
    private Integer isEnabled;
    
	@ApiModelProperty("生效日期")
    @Column(name = "effective_date")
    private String effectiveDate;

    @ApiModelProperty("取值来源")
    @Column(name = "value_source")
    private String valueSource;

    @ApiModelProperty("值表单类型")
    @Column(name = "value_editor_code")
    private String valueEditorCode;
    
}

