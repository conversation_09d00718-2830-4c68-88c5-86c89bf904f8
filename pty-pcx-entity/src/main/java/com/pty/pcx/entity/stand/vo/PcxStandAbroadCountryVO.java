package com.pty.pcx.entity.stand.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.entity.stand.PcxStandAbroad;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PcxStandAbroadCountryVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonIgnore
    private final PcxStandAbroad delegate;

    private List<PcxStandAbroadCityVO> children;

    private Integer seq;

    /**
     * 已选城市
     */
    private List<String> checkedCityCodes;

    public PcxStandAbroadCountryVO() {
        this.delegate = new PcxStandAbroad();
        this.children = new ArrayList<>();
    }

    public PcxStandAbroadCountryVO(PcxStandAbroad delegate) {
        this.delegate = delegate;
        this.children = new ArrayList<>();
    }

    public String getAgyCode() {
        return delegate.getAgyCode();
    }

    public String getFiscal() {
        return delegate.getFiscal();
    }

    public String getMofDivCode() {
        return delegate.getMofDivCode();
    }

    public String getTenantId() {
        return delegate.getTenantId();
    }

    public String getCountryCode() {
        return delegate.getCountryCode();
    }

    public String getCountryName() {
        return delegate.getCountryName();
    }
}
