package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pty.pub.common.entity.MapperGenerator;
import lombok.*;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.pty.pub.common.anno.Desc;

/**
 * 招待费用明细表
 * @Date: 2025/5/14
 */


@Data
@Entity
@Table ( name ="pcx_bill_exp_detail_inlandfee" )
public class PcxBillExpDetailInlandfee extends PcxBillExpDetailBase implements Serializable{
private static final long serialVersionUID = 1L;

  @Column(name = "id" )
  @Id
  @Desc("费用信息明细表主键ID")
  private String id;

  @Column(name = "bill_id" )
  @Desc("事项主键ID")
  private String billId;

  @Column(name = "expense_id" )
  @Desc("费用主键ID")
  private String expenseId;

  @Column(name = "exp_detail_code" )
  @Desc("费用类型编码")
  private String expDetailCode;

  @Column(name = "exp_detail_name" )
  @Desc("费用类型名称")
  private String expDetailName;

  @Column(name = "exp_detail_level" )
  @Desc("费用类型级别(1、2)")
  private String expDetailLevel;

  @Column(name = "input_amt" )
  @Desc("费用录入金额")
  private BigDecimal inputAmt;

  @Column(name = "check_amt" )
  @Desc("费用核定金额")
  private BigDecimal checkAmt;

  @Column(name = "agy_code" )
  @Desc("单位编码")
  private String agyCode;

  @Column(name = "fiscal" )
  @Desc("年度")
  private String fiscal;

  @Column(name = "mof_div_code" )
  @Desc("区划")
  private String mofDivCode;

  @Column(name = "tenant_id" )
  @Desc("租户ID")
  private String tenantId;

  @Column(name = "remarks" )
  @Desc("备注")
  private String remarks;

  @Column(name = "people_num" )
  @Desc("招待人数")
  private Integer peopleNum;

  @Column(name = "food_num" )
  @Desc("陪餐人数")
  private Integer foodNum;

  @Column(name = "ecs_amt" )
  @Desc("票据金额（1对1，汇总，拆分）")
  private BigDecimal ecsAmt;

  @Column(name = "ecs_num" )
  @Desc("发票张数")
  private Integer ecsNum;

  @Column(name = "no_ecs_reason" )
  @Desc("无票原因")
  private String noEcsReason;

  @Column(name = "exp_source" )
  @Desc("来源，ecs，replenish，manual")
  private String expSource;

  @Column(name = "creator" )
  @Desc("创建人代码")
  private String creator;

  @Column(name = "creator_name" )
  @Desc("创建人名称")
  private String creatorName;

  @Column(name = "created_time" )
  @Desc("创建时间")
  private String createdTime;

  @Column(name = "modifier" )
  @Desc("修改人编码")
  private String modifier;

  @Column(name = "modifier_name" )
  @Desc("修改人名称")
  private String modifierName;

  @Column(name = "modified_time" )
  @Desc("修改时间")
  private String modifiedTime;

  @Column(name = "field01" )
  @Desc("自定义01字段")
  private String field01;

  @Column(name = "field02" )
  @Desc("自定义02字段")
  private String field02;

  @Column(name = "field03" )
  @Desc("自定义03字段")
  private String field03;

  @Column(name = "field04" )
  @Desc("自定义04字段")
  private String field04;

  @Column(name = "field05" )
  @Desc("自定义05字段")
  private String field05;

  @Column(name = "field06" )
  @Desc("自定义06字段")
  private String field06;

  @Column(name = "field07" )
  @Desc("自定义07字段")
  private String field07;

  @Column(name = "field08" )
  @Desc("自定义08字段")
  private String field08;

  @Column(name = "field09" )
  @Desc("自定义09字段")
  private String field09;

  @Column(name = "field10" )
  @Desc("自定义10字段")
  private String field10;

  @Column(name = "acitem01_code" )
  @Desc("自定义01字段code")
  private String acitem01Code;

  @Column(name = "acitem01_name" )
  @Desc("自定义01字段name")
  private String acitem01Name;

  @Column(name = "acitem02_code" )
  @Desc("自定义02字段code")
  private String acitem02Code;

  @Column(name = "acitem02_name" )
  @Desc("自定义02字段name")
  private String acitem02Name;

  @Column(name = "acitem03_code" )
  @Desc("自定义03字段code")
  private String acitem03Code;

  @Column(name = "acitem03_name" )
  @Desc("自定义03字段name")
  private String acitem03Name;

  @Column(name = "acitem04_code" )
  @Desc("自定义04字段code")
  private String acitem04Code;

  @Column(name = "acitem04_name" )
  @Desc("自定义04字段name")
  private String acitem04Name;

  @Column(name = "acitem05_code" )
  @Desc("自定义05字段code")
  private String acitem05Code;

  @Column(name = "acitem05_name" )
  @Desc("自定义05字段name")
  private String acitem05Name;

  @Column(name = "acitem06_code" )
  @Desc("自定义06字段code")
  private String acitem06Code;

  @Column(name = "acitem06_name" )
  @Desc("自定义06字段name")
  private String acitem06Name;

  @Column(name = "acitem07_code" )
  @Desc("自定义07字段code")
  private String acitem07Code;

  @Column(name = "acitem07_name" )
  @Desc("自定义07字段name")
  private String acitem07Name;

  @Column(name = "acitem08_code" )
  @Desc("自定义08字段code")
  private String acitem08Code;

  @Column(name = "acitem08_name" )
  @Desc("自定义08字段name")
  private String acitem08Name;

  @Column(name = "acitem09_code" )
  @Desc("自定义09字段code")
  private String acitem09Code;

  @Column(name = "acitem09_name" )
  @Desc("自定义09字段name")
  private String acitem09Name;

  @Column(name = "acitem10_code" )
  @Desc("自定义10字段code")
  private String acitem10Code;

  @Column(name = "acitem10_name" )
  @Desc("自定义10字段name")
  private String acitem10Name;

  @Column(name = "parent_code")
  @Desc("父级编码")
  private String parentCode;

  @Column(name = "last_code")
  @Desc("最顶层编码")
  private String lastCode;

  @TableField(exist = false)
  private List<String> groupNo;

  @TableField(exist = false)
  private String expenseTypeCode;

  @TableField(exist = false)
  private String expenseTypeName;

  @TableField(exist = false)
  private BigDecimal ticketAmt;

  @TableField(exist = false)
  private BigDecimal standAmt;

  @TableField(exist = false)
  private BigDecimal taxRate;

  @TableField(exist = false)
  private BigDecimal taxAmt;

  @TableField(exist = false)
  private String departmentCode;

  @TableField(exist = false)
  private String departmentName;
}
