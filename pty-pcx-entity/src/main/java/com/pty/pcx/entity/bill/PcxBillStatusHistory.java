package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 单据流转历史表(PcxBillStatusHistory)实体类
 *
 * <AUTHOR>
 * @since 2024-12-20 08:52:55
 */
@Data
@Entity
@Table(schema = "pcx_bill_status_history")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PcxBillStatusHistory implements Serializable {
    private static final long serialVersionUID = -37598911600168761L;

    @Id
	@ApiModelProperty("主键")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据的类型")
    @Column(name = "bill_type")
    private String billType;
    
	@ApiModelProperty("关联的原始单据编号")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("记录单据进行的操作类型，如终审、驳回等")
    @Column(name = "op_type")
    private String opType;
    
	@ApiModelProperty("操作时间(datetime类型)")
    @Column(name = "op_time")
    @TableField(fill = FieldFill.INSERT)
    private Date opTime;
    
	@ApiModelProperty("单据在操作前所处的状态")
    @Column(name = "from_status")
    private String fromStatus;
    
	@ApiModelProperty("单据经过操作后变更到的新状态")
    @Column(name = "to_status")
    private String toStatus;
    
	@ApiModelProperty("操作备注信息")
    @Column(name = "remark")
    private String remark;
    
	@ApiModelProperty("执行此次操作的人员编号")
    @Column(name = "creator")
    private String creator;
    
	@ApiModelProperty("执行操作的人员姓名")
    @Column(name = "creator_name")
    private String creatorName;
    
	@ApiModelProperty("创建时间(字符串类型)")
    @Column(name = "created_time")
    private String createdTime;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
}

