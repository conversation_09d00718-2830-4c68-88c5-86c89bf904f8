package com.pty.pcx.entity.stand;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 支出标准值表(PcxStandValue)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:44:25
 */
@Data
@Entity
@Table(schema = "pcx_stand_value")
public class PcxStandValue implements Serializable {
    private static final long serialVersionUID = -32549095563177856L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("标准代码（唯一要素关联）")
    @Column(name = "stand_code")
    private String standCode;
    
	@ApiModelProperty("行要素编码")
    @Column(name = "row_key_code")
    private String rowKeyCode;
    
	@ApiModelProperty("行要素名称")
    @Column(name = "row_key_name")
    private String rowKeyName;
    
	@ApiModelProperty("行要素值编码")
    @Column(name = "row_value_code")
    private String rowValueCode;
    
	@ApiModelProperty("行要素值名称")
    @Column(name = "row_value_name")
    private String rowValueName;
    
	@ApiModelProperty("列要素编码")
    @Column(name = "column_key_code")
    private String columnKeyCode;
    
	@ApiModelProperty("列要素名称")
    @Column(name = "column_key_name")
    private String columnKeyName;
    
	@ApiModelProperty("列要素值编码")
    @Column(name = "column_value_code")
    private String columnValueCode;
    
	@ApiModelProperty("列要素值名称")
    @Column(name = "column_value_name")
    private String columnValueName;
    
	@ApiModelProperty("标准值")
    @Column(name = "standard_value")
    private String standardValue;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
}

