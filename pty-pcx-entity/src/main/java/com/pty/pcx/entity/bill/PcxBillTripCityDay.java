package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 职员行程城市驻留天数(PcxBillTripCityDay)实体类
 *
 * <AUTHOR>
 * @since 2025-03-19 09:24:39
 */
@Data
@Entity
@Table(schema = "pcx_bill_trip_city_day")
public class PcxBillTripCityDay implements Serializable {
    private static final long serialVersionUID = -35382926507847798L;

    @Id
	@ApiModelProperty("$column.comment")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("报销单id")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("人员编码")
    @Column(name = "emp_code")
    private String empCode;
    
	@ApiModelProperty("城市编码")
    @Column(name = "city_code")
    private String cityCode;

	@ApiModelProperty("城市驻留天数")
    @Column(name = "city_days")
    private double cityDays;
    
	@ApiModelProperty("工作日天数")
    @Column(name = "work_days")
    private double workDays;
    
	@ApiModelProperty("住宿舍天数")
    @Column(name = "dorm_days")
    private double dormDays;

    @ApiModelProperty("工作日住宿舍天数")
    @Column(name = "work_dorm_days")
    private double workDormDays;

    @ApiModelProperty("住酒店天数")
    @Column(name = "hotel_days")
    private double hotelDays;

    @ApiModelProperty("住酒店天数")
    @Column(name = "work_hotel_days")
    private double workHotelDays;


    @ApiModelProperty("周末加班天数")
    @Column(name = "work_extra_days")
    private double workExtraDays;

    @ApiModelProperty("项目经理天数")
    @Column(name = "manager_days")
    private double managerDays;

    @ApiModelProperty("来时时间")
    @Column(name = "start_time")
    private String startTime;

    @ApiModelProperty("结束时间")
    @Column(name = "finish_time")
    private String finishTime;

    @ApiModelProperty("行程id")
    @Column(name = "trip_id")
    private String tripId;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
}

