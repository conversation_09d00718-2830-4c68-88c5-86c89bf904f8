package com.pty.pcx.entity.financeapproval;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 财务审批的工作流任务（作用是接收 TodoTaskVO 对象和 DoneTaskVO 对象的字段信息）
 * createdTime: 2025/1/2  上午10:03
 * creator: wangbao
 **/
@Data
@NoArgsConstructor
public class FinanceApprovalTaskDTO {

    /**
     * 任务是否已完成，1为已办，0为待办
     */
    private Integer isDone;

    /**
     * 业务单据id
     */
    @JSONField(alternateNames = {"businessKey", "busiId"})
    private String businessKey;

    /**
     * 流程实例id
     */
    private String procInstId;

    /**
     * 待办任务id
     */
    private String taskId;

    /**
     * 任务节点名称（如 单位领导审核）
     */
    @JSONField(alternateNames = {"nodeName", "node"})
    private String nodeName;

    /**
     * 待办任务节点id（如 agy_superior_audit）
     */
    private String nodeId;

    /**
     * 任务创建时间
     */
    private String createTime;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 单位代码
     */
    private String agyCode;

    /**
     * 区划
     */
    private String mofDivCode;

    /**
     * 主键id
     */
    private String id;

    /**
     * 业务开始时间（如 2024-12-25 18:08:57.784）
     */
    private String startTime;

    /**
     * 业务结束时间（如 2024-12-25 18:08:57.784）
     */
    private String endTime;

    /**
     * 业务处理人编码（如 ***********）
     */
    private String assignee;

    /**
     * 业务操作编码（如 submit_task）
     */
    private String operation;

    /**
     * 业务模块（如 PCX）
     */
    private String busiModule;

    /**
     * 业务类型（如 apply）
     */
    private String busiType;

    /**
     * 年度
     */
    private Integer fiscal;

    /**
     * 评论
     */
    private String comment;

    /**
     * 委托人编码
     */
    private String delegatedCode;

    // 租户id
    private String tenantId;
    // 是否可以撤回
    private boolean canRevoke;
    private boolean canFinishedRevoke = false;

}
