package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pty.pcx.dto.bill.EmpInfo;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 单据费用明细_差旅费_明细(PcxBillExpDetailTravel)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(schema = "pcx_bill_exp_detail_travel")
public class PcxBillExpDetailTravel extends PcxBillExpDetailBase implements Serializable {
    private static final long serialVersionUID = 127189210058449265L;

    @ApiModelProperty("基础补助")
    @Column(name = "base_amt")
    private BigDecimal baseAmt;

    @ApiModelProperty("其他补助")
    @Column(name = "extra_amt")
    private BigDecimal extraAmt;

    @ApiModelProperty("周末加班补助")
    @Column(name = "work_extra_amt")
    private BigDecimal workExtraAmt;

    @ApiModelProperty("项目经理补助")
    @Column(name = "manager_amt")
    private BigDecimal managerAmt;

    @ApiModelProperty("住宿舍补助")
    @Column(name = "dorm_amt")
    private BigDecimal dormAmt;
    @ApiModelProperty("标准金额")
    @Column(name = "stand_amt")
    private BigDecimal standAmt;

    @ApiModelProperty("来源 ecs，replenish，manual")
    @Column(name = "source")
    private String source;

    @ApiModelProperty("抵扣税率")
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @ApiModelProperty("抵扣税额")
    @Column(name = "tax_amt")
    private BigDecimal taxAmt;

    @ApiModelProperty("发票张数")
    @Column(name = "ecs_num")
    private Integer ecsNum = 1;

    @ApiModelProperty("无票原因")
    @Column(name = "no_ecs_reason")
    private String noEcsReason;

    @ApiModelProperty("人员编码")
    @Column(name = "emp_code")
    private String empCode;

    @ApiModelProperty("人员姓名")
    @Column(name = "emp_name")
    private String empName;

    @ApiModelProperty("人员类别")
    @Column(name = "emp_type")
    private String empType;

    @ApiModelProperty("人员级别")
    @Column(name = "bud_level")
    private String budLevel;

    @ApiModelProperty("是否有补助")
    @Column(name = "is_subsidy")
    private Integer isSubsidy = 1;

    @ApiModelProperty("住宿人员分组")
    @Column(name = "hotel_group")
    private String hotelGroup;

    @ApiModelProperty("住宿人员分组在分组")
    @Column(name = "hotel_seq")
    private Integer hotelSeq;

    @ApiModelProperty("出差天数")
    @Column(name = "travel_days")
    private Double travelDays;

    @ApiModelProperty("工作日天数")
    @Column(name = "work_days")
    private Double workDays;

    @ApiModelProperty("住宿舍天数")
    @Column(name = "dorm_days")
    private Double dormDays;

    @ApiModelProperty("工作日住宿舍天数")
    @Column(name = "work_dorm_days")
    private Double workDormDays;

    @ApiModelProperty("周末加班天数")
    @Column(name = "work_extra_days")
    private double workExtraDays;

    @ApiModelProperty("项目经理天数")
    @Column(name = "manager_days")
    private double managerDays;

    @ApiModelProperty("住酒店天数")
    @Column(name = "hotel_days")
    private double hotelDays;

    @ApiModelProperty("工作日住酒店天数")
    @Column(name = "work_hotel_days")
    private double workHotelDays;

    @ApiModelProperty("超标准原因")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty("开始日期")
    @Column(name = "start_time")
    private String startTime;

    @ApiModelProperty("结束日期")
    @Column(name = "finish_time")
    private String finishTime;

    @ApiModelProperty("交通工具代码")
    @Column(name = "traffic_tool_code")
    private String trafficToolCode;

    @ApiModelProperty("交通工具名称")
    @Column(name = "traffic_tool_name")
    private String trafficToolName;

    @ApiModelProperty("车次")
    @Column(name = "train_no")
    private String trainNo;

    @ApiModelProperty("出发地点火车站等")
    @Column(name = "start_place")
    private String startPlace;

    @ApiModelProperty("到达地点火车站等")
    @Column(name = "end_place")
    private String endPlace;

    @ApiModelProperty("出发城市编码")
    @Column(name = "start_city_code")
    private String startCityCode;

    @ApiModelProperty("到达城市编码")
    @Column(name = "end_city_code")
    private String endCityCode;

    @ApiModelProperty("出发城市编码")
    @Column(name = "start_city")
    private String startCity;

    @ApiModelProperty("到达城市")
    @Column(name = "end_city")
    private String endCity;

    @ApiModelProperty("标准座位席别")
    @Column(name = "stand_seat_level")
    private String standSeatLevel;

    @ApiModelProperty("实际座位席别")
    @Column(name = "input_seat_level")
    private String inputSeatLevel;

    @ApiModelProperty("行程主键")
    @Column(name = "trip_id")
    private String tripId;

    @ApiModelProperty("行程顺序")
    @Column(name = "trip_seq")
    private Integer tripSeq;

    @ApiModelProperty("里程")
    @Column(name = "mileage")
    private String mileage;

    @ApiModelProperty("车型")
    @Column(name = "car_type")
    private String carType;

    @ApiModelProperty("住宿费理行程时，无到达票或无返程票")
    @Column(name = "trip_flag")
    private Integer tripFlag = 0;

    @ApiModelProperty("对应的改签费明细id")
    @Column(name = "origin_id")
    private String originId;

    @ApiModelProperty("行程分段id")
    @Column(name = "trip_segment_id")
    private String tripSegmentId;

    @ApiModelProperty("挂载行程md5")
    @Column(name = "mount_trip")
    private String mountTrip;

    @ApiModelProperty("补助信息")
    @Column(name = "subsidy_extra")
    private String subsidyExtra;



    @ApiModelProperty("人员列表")
    @TableField(exist = false)
    private List<EmpInfo> empInfoList;
    @TableField(exist = false)
    private String tripSort;
    //人员信息
    @TableField(exist = false)
    private List<EmpInfo> empInfo;

    public List<EmpInfo> getEmpInfo() {
        List<EmpInfo> result = new ArrayList<>();
        if (StringUtil.isNotEmpty(this.empCode)){
            EmpInfo info = new EmpInfo();
            info.setEmpCode(this.empCode);
            info.setEmpName(this.empName);
            info.setEmpType(this.empType);
            info.setBudLevel(this.budLevel);
            info.setIsSubsidy(this.isSubsidy);
            result.add(info);
        }
        return result;
    }

}
