package com.pty.pcx.entity.printtemplate;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "pcx_print_template")
@NoArgsConstructor
public class PcxPrintTemplate {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 单据类型：1申请单、2报销单、4借款单（支持多选，多选的时候用逗号隔开，如 "1,2,4"）
     * {@link com.pty.pcx.common.constant.PcxConstant}
     */
    @Column(name = "billtype_code")
    private String billtypeCode;

    private String billtypeName;

    /**
     * 功能类型：1-事项类型、2-费用明细类型
     * {@link com.pty.pcx.common.constant.PcxConstant}
     */
    @Column(name = "func_type")
    private String funcType;

    /**
     * 功能编码：事项类型编码或费用明细类型编码
     */
    @Column(name = "func_code")
    private String funcCode;

    /**
     * 页面模版名称（前端手动输入，页面上展示给用户的模版名称）
     */
    @Column(name = "tmp_name")
    private String tmpName;

    /**
     * 系统模版名称（根据数据库查询获取，文件系统中的模版文件名称）
     */
    @Column(name = "tmp_value")
    private String tmpValue;

    /**
     * 系统模版路径（文件系统中的模版文件地址）
     */
    @Column(name = "tmp_path")
    private String tmpPath;

    /**
     * 年度
     */
    @Column(name = "fiscal")
    private Integer fiscal;

    /**
     * 区划编码
     */
    @Column(name = "mof_div_code")
    private String mofDivCode;

    /**
     * 单位编码
     */
    @Column(name = "agy_code")
    private String agyCode;

    /**
     * 租户编码
     */
    @Column(name = "tenant_id")
    private String tenantId;
}
