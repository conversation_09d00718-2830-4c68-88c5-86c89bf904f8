package com.pty.pcx.entity.treasurypay.change;

import com.pty.pub.common.anno.Desc;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;


@Data
@Entity
@Builder
@NoArgsConstructor()
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@Table ( name ="pcx_change_bill" )
public class PcxChangeBill implements Serializable{
private static final long serialVersionUID = 1L;

  @Column(name = "id" )
  @Id
  @Desc("申请单变更单id")
  private String id;

  @Column(name = "change_bill_no" )
  @Desc("变更单单号")
  private String changeBillNo;

  @Column(name = "change_type" )
  @Desc("变更类型(0:基本信息 1 结算方式 2 预算指标)")
  private String changeType;

  @Column(name = "change_date" )
  @Desc("变更日期")
  private String changeDate;

  @Column(name = "change_bill_status" )
  @Desc("单据变更状态（'0'.变更内容待提交；'1'.变更内容已提交 '2' 已确认变更 ）")
  private String changeBillStatus;

  @Column(name = "bill_id" )
  @Desc("单据ID")
  private String billId;

  @ApiModelProperty("单据状态")
  @Column(name = "bill_status")
  private String billStatus;

  @Column(name = "correction_desc" )
  @Desc("更正说明")
  private String correctionDesc;

  @Column(name = "source_reason" )
  @Desc("源事由")
  private String sourceReason;

  @Column(name = "change_reason" )
  @Desc("变更后事由")
  private String changeReason;

  @Column(name = "receiver" )
  @Desc("接收人编码")
  private String receiver;

  @Column(name = "agy_code" )
  @Desc("单位编码")
  private String agyCode;

  @Column(name = "fiscal" )
  @Desc("年度")
  private String fiscal;

  @Column(name = "mof_div_code" )
  @Desc("区划")
  private String mofDivCode;

  @Column(name = "modifier" )
  @Desc("修改人编码")
  private String modifier;

  @Column(name = "modifier_name" )
  @Desc("修改人名称")
  private String modifierName;

  @Column(name = "modified_time" )
  @Desc("修改时间")
  private String modifiedTime;

  @Column(name = "creator_code" )
  @Desc("创建人代码")
  private String creatorCode;

  @Column(name = "creator_name" )
  @Desc("创建人名称")
  private String creatorName;

  @Column(name = "created_time" )
  @Desc("创建时间")
  private String createdTime;

  @Column(name = "tenant_id" )
  @Desc("租户ID")
  private String tenantId;

}
