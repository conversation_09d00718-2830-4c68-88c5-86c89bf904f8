package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 行程分段(PcxBillTripSegment)实体类
 *
 * <AUTHOR>
 * @since 2025-04-01 07:38:57
 */
@Data
@Entity
@Table(schema = "pcx_bill_trip_segment")
public class PcxBillTripSegment implements Serializable {
    private static final long serialVersionUID = -68040580838038918L;

    @Id
	@ApiModelProperty("$column.comment")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("报销单id")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("行程id")
    @Column(name = "trip_id")
    private String tripId;
    
	@ApiModelProperty("开始时间")
    @Column(name = "start_time")
    private String startTime;
    
	@ApiModelProperty("结束时间")
    @Column(name = "finish_time")
    private String finishTime;
    
	@ApiModelProperty("序号")
    @Column(name = "seq")
    private Integer seq;
    
	@ApiModelProperty("城市编码")
    @Column(name = "city_code")
    private String cityCode;
    
	@ApiModelProperty("城市名称")
    @Column(name = "city_name")
    private String cityName;
    
	@ApiModelProperty("报销金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;
    
	@ApiModelProperty("分摊分摊id")
    @Column(name = "apportion_id")
    private String apportionId;
    
}

