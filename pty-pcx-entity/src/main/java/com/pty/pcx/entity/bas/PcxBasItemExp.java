package com.pty.pcx.entity.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pcx_bas_item_exp")
@NoArgsConstructor
public class PcxBasItemExp implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty("关联表主键Id")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("事项类型代码")
    @Column(name = "item_code")
    private String itemCode;

    @ApiModelProperty("事项类型名称")
    @Column(name = "item_name")
    private String itemName;

    @ApiModelProperty("费用类型代码")
    @Column(name = "expense_code")
    private String expenseCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_name")
    private String expenseName;

    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;

    @ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

    @ApiModelProperty("创建人代码")
    @Column(name = "creator_code")
    private String creatorCode;

    @ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("排序")
    @Column(name = "seq")
    private Integer seq;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("单据类型代码")
    @Column(name = "billtype_code")
    private String billtypeCode;
}