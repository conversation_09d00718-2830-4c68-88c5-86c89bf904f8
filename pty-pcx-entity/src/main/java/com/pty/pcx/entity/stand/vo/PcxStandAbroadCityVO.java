package com.pty.pcx.entity.stand.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.entity.stand.PcxStandAbroad;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class PcxStandAbroadCityVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final String GROUP_CONCAT_SEPARATOR = " ";
    @JsonIgnore
    private final PcxStandAbroad delegate;

    private List<String> ids;

    private Set<String> cityCodeSet;

    private Set<String> cityNameSet;

    private Integer seq;

    private String identity;

    public PcxStandAbroadCityVO() {
        this.delegate = new PcxStandAbroad();
    }

    public PcxStandAbroadCityVO(PcxStandAbroad delegate) {
        this.delegate = delegate;
    }

    public String getCountryCode() {
        return delegate.getCountryCode();
    }

    public String getCountryName() {
        return delegate.getCountryName();
    }

    public String getCityCode() {
        return String.join(GROUP_CONCAT_SEPARATOR, this.cityCodeSet);
    }

    public String getCityName() {
        return String.join(GROUP_CONCAT_SEPARATOR, this.cityNameSet);
    }

    public BigDecimal getStayAmt() {
        return delegate.getStayAmt();
    }

    public BigDecimal getFoodAmt() {
        return delegate.getFoodAmt();
    }

    public BigDecimal getOtherAmt() {
        return delegate.getOtherAmt();
    }

    public String getCurrencyCode() {
        return delegate.getCurrencyCode();
    }

    public String getCurrencyName() {
        return delegate.getCurrencyName();
    }

    public String getContinentCode() {
        return delegate.getContinentCode();
    }

    public String getContinentName() {
        return delegate.getContinentName();
    }

    public String getAgyCode() {
        return delegate.getAgyCode();
    }

    public String getFiscal() {
        return delegate.getFiscal();
    }

    public String getMofDivCode() {
        return delegate.getMofDivCode();
    }

    public String getTenantId() {
        return delegate.getTenantId();
    }

    public Integer getIsEnabled() {
        return delegate.getIsEnabled();
    }
}
