package com.pty.pcx.entity.project;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 项目开支范围关联表
 */
@Data
@TableName("pcx_project_expense")
public class PcxProjectExpense {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 费用类型编码
     */
    private String expenseCode;

    /**
     * 费用类型名称
     */
    private String expenseName;

    /**
     * 年度
     */
    private Integer fiscal;

    /**
     * 单位编码
     */
    private String agyCode;

    /**
     * 区划
     */
    private String mofDivCode;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 预算项目编码
     */
    private String budgetItemCode;

    /**
     * 预算项目名称
     */
    private String budgetItemName;

    /**
     * 核算项目编码
     */
    private String accountingItemCode;

    /**
     * 核算项目名称
     */
    private String accountingItemName;

    /**
     * 是否从ERP同步
     */
    private Integer isErpSynced;

}
