package com.pty.pcx.entity.treasurypay.change;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;


@Data
@Entity
@Builder
@NoArgsConstructor()
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@Table ( name ="pcx_change_bill_pay_detail" )
public class  PcxChangeBillPayDetail implements Serializable{
private static final long serialVersionUID = 1L;

  @Column(name = "id" )
  @Id
  @ApiModelProperty("主键id")
  private String id;

  @Column(name = "change_bill_id" )
  @ApiModelProperty("变更单单号")
  private String changeBillId;

  @Column(name = "expense_code" )
  @ApiModelProperty("费用类型")
  private String expenseCode;

  @Column(name = "info_source" )
  @ApiModelProperty("信息数据来源")
  private String infoSource;

  @Column(name = "info_source_id" )
  @ApiModelProperty("信息数据来源ID")
  private String infoSourceId;

  @Column(name = "bill_id" )
  @ApiModelProperty("单据主键ID")
  private String billId;

  @Column(name = "expense_id" )
  @ApiModelProperty("费用信息主键ID")
  private String expenseId;

  @Column(name = "balance_id" )
  @ApiModelProperty("额度ID")
  private String balanceId;

  @ApiModelProperty("额度编码")
  @Column(name = "balance_no")
  private String balanceNo;

  @ApiModelProperty("结算信息唯一键")
  @Column(name = "settlement_uk")
  private String settlementUk;

  @Column(name = "pay_account_no" )
  @ApiModelProperty("付款方账号")
  private String payAccountNo;

  @ApiModelProperty("付款方账号类型")
  @Column(name = "pay_account_type_code")
  private String payAccountTypeCode;

  @ApiModelProperty("付款方账号类型")
  @Column(name = "pay_account_type_name")
  private String payAccountTypeName;

  @Column(name = "pay_account_name" )
  @ApiModelProperty("付款方名称")
  private String payAccountName;

  @ApiModelProperty("付款方开户城市")
  @Column(name = "pay_account_city")
  private String payAccountCity;

  @ApiModelProperty("付款方银行编码")
  @Column(name = "pay_bank_code")
  private String payBankCode;

  @Column(name = "pay_bank_name" )
  @ApiModelProperty("付款方开户行")
  private String payBankName;

  @Column(name = "payee_account_no" )
  @ApiModelProperty("收款方账号")
  private String payeeAccountNo;

  @Column(name = "payee_account_name" )
  @ApiModelProperty("收款方名称")
  private String payeeAccountName;

  @Column(name = "payee_bank_name" )
  @ApiModelProperty("收款方开户行")
  private String payeeBankName;

  @ApiModelProperty("收款方开户城市")
  @Column(name = "payee_account_city")
  private String payeeAccountCity;

  @ApiModelProperty("收款银行编码")
  @Column(name = "payee_bank_code")
  private String payeeBankCode;

  @ApiModelProperty("收款银行网点行号")
  @Column(name = "payee_bank_node_no")
  private String payeeBankNodeNo;

  @ApiModelProperty("收款银行网点名称")
  @Column(name = "payee_bank_node_name")
  private String payeeBankNodeName;

  @ApiModelProperty("收款账户类型")
  @Column(name = "payee_account_type_code")
  private String payeeAccountTypeCode;

  @ApiModelProperty("收款账户类型名称")
  @Column(name = "payee_account_type_name")
  private String payeeAccountTypeName;

  @Column(name = "settlement_type" )
  @ApiModelProperty("结算方式类型")
  private String settlementType;

  @ApiModelProperty("结算方式类型名称")
  @Column(name = "settlement_type_name")
  private String settlementTypeName;

  @Column(name = "check_amt" )
  @ApiModelProperty("核定金额")
  private BigDecimal checkAmt;

  @Column(name = "pay_no" )
  @ApiModelProperty("支付申请单号")
  private String payNo;

  @Column(name = "pay_status" )
  @ApiModelProperty("支付状态(0=待生成,1=已生成,9=变更中,10=作废,3=支付异常,2=支付成功)")
  private String payStatus;

  @Column(name = "summary" )
  @ApiModelProperty("备注说明")
  private String summary;

  @Column(name = "agy_code" )
  @ApiModelProperty("单位编码")
  private String agyCode;

  @Column(name = "agy_name" )
  @ApiModelProperty("单位名称")
  private String agyName;

  @Column(name = "fiscal" )
  @ApiModelProperty("年度")
  private String fiscal;

  @Column(name = "mof_div_code" )
  @ApiModelProperty("区划")
  private String mofDivCode;

  @Column(name = "tenant_id" )
  @ApiModelProperty("租户ID")
  private String tenantId;

  @ApiModelProperty("对公/对私(1：个人对私 0：单位对公)")
  @Column(name = "pay_type")
  private String payType;

  @ApiModelProperty("付款方式编码")
  @Column(name = "pay_type_code")
  private String payTypeCode;

  @ApiModelProperty("付款方式名称")
  @Column(name = "pay_type_name")
  private String payTypeName;

  @ApiModelProperty("ep详情ID")
  @Column(name = "ref_set_id")
  private String refSetId;

  @Column(name = "paying_time" )
  @ApiModelProperty("支付请求时间")
  private String payingTime;

  @Column(name = "paid_time" )
  @ApiModelProperty("支付完成时间")
  private String paidTime;

  @Column(name = "modifier" )
  @ApiModelProperty("修改人编码")
  private String modifier;

  @Column(name = "modifier_name" )
  @ApiModelProperty("修改人名称")
  private String modifierName;

  @Column(name = "modified_time" )
  @ApiModelProperty("修改时间")
  private String modifiedTime;

  @Column(name = "creator" )
  @ApiModelProperty("创建人代码")
  private String creator;

  @Column(name = "creator_name" )
  @ApiModelProperty("创建人名称")
  private String creatorName;

  @Column(name = "created_time" )
  @ApiModelProperty("创建时间")
  private String createdTime;

  @Column(name = "ver" )
  @ApiModelProperty("数据版本")
  private Integer ver;

  @Column(name = "is_deleted" )
  @ApiModelProperty("是否作废,0是有效的数据,非0均为作废数据,每次作废取唯一键最大的isDeleted+1写入,避免使用payStatus造成真正唯一键无法防重")
  private Integer isDeleted;

}
