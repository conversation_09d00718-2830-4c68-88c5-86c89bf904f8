package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 单据费用表_差旅费(PcxBillExpTravel)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:04
 */
@Data
@Entity
@Table(schema = "pcx_bill_exp_travel")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PcxBillExpTravel extends PcxBillExpBase implements Serializable {
    private static final long serialVersionUID = -92581105717404347L;

    private String id;
	@ApiModelProperty("开始时间")
    @Column(name = "start_time")
    private String startTime;
    
	@ApiModelProperty("结束时间")
    @Column(name = "finish_time")
    private String finishTime;
    
	@ApiModelProperty("出差天数")
    @Column(name = "days")
    private BigDecimal days;
    
	@ApiModelProperty("出差总人数")
    @Column(name = "travel_emp_count")
    private Integer travelEmpCount;

	@ApiModelProperty("是否需要派车")
    @Column(name = "need_assigned_vehicle")
    private Integer needAssignedVehicle;

    @ApiModelProperty("开始地点")
    @Column(name = "start_place_code")
    private String startPlaceCode;

    @ApiModelProperty("开始地点")
    @Column(name = "start_place_name")
    private String startPlaceName;

    @ApiModelProperty("出差地点")
    @Column(name = "travel_place_code")
    private String travelPlaceCode;

    @ApiModelProperty("出差地点")
    @Column(name = "travel_place_name")
    private String travelPlaceName;

	@ApiModelProperty("自定义01字段")
    @Column(name = "field01")
    private String field01;
    
	@ApiModelProperty("自定义02字段")
    @Column(name = "field02")
    private String field02;
    
	@ApiModelProperty("自定义03字段")
    @Column(name = "field03")
    private String field03;
    
	@ApiModelProperty("自定义04字段")
    @Column(name = "field04")
    private String field04;
    
	@ApiModelProperty("自定义05字段")
    @Column(name = "field05")
    private String field05;
    
	@ApiModelProperty("自定义06字段")
    @Column(name = "field06")
    private String field06;
    
	@ApiModelProperty("自定义07字段")
    @Column(name = "field07")
    private String field07;
    
	@ApiModelProperty("自定义08字段")
    @Column(name = "field08")
    private String field08;
    
	@ApiModelProperty("自定义09字段")
    @Column(name = "field09")
    private String field09;
    
	@ApiModelProperty("自定义10字段")
    @Column(name = "field10")
    private String field10;
    
	@ApiModelProperty("自定义01字段code")
    @Column(name = "acitem01_code")
    private String acitem01Code;
    
	@ApiModelProperty("自定义01字段name")
    @Column(name = "acitem01_name")
    private String acitem01Name;
    
	@ApiModelProperty("自定义02字段code")
    @Column(name = "acitem02_code")
    private String acitem02Code;
    
	@ApiModelProperty("自定义02字段name")
    @Column(name = "acitem02_name")
    private String acitem02Name;
    
	@ApiModelProperty("自定义03字段code")
    @Column(name = "acitem03_code")
    private String acitem03Code;
    
	@ApiModelProperty("自定义03字段name")
    @Column(name = "acitem03_name")
    private String acitem03Name;
    
	@ApiModelProperty("自定义04字段code")
    @Column(name = "acitem04_code")
    private String acitem04Code;
    
	@ApiModelProperty("自定义04字段name")
    @Column(name = "acitem04_name")
    private String acitem04Name;
    
	@ApiModelProperty("自定义05字段code")
    @Column(name = "acitem05_code")
    private String acitem05Code;
    
	@ApiModelProperty("自定义05字段name")
    @Column(name = "acitem05_name")
    private String acitem05Name;
    
	@ApiModelProperty("自定义06字段code")
    @Column(name = "acitem06_code")
    private String acitem06Code;
    
	@ApiModelProperty("自定义06字段name")
    @Column(name = "acitem06_name")
    private String acitem06Name;
    
	@ApiModelProperty("自定义07字段code")
    @Column(name = "acitem07_code")
    private String acitem07Code;
    
	@ApiModelProperty("自定义07字段name")
    @Column(name = "acitem07_name")
    private String acitem07Name;
    
	@ApiModelProperty("自定义08字段code")
    @Column(name = "acitem08_code")
    private String acitem08Code;
    
	@ApiModelProperty("自定义08字段name")
    @Column(name = "acitem08_name")
    private String acitem08Name;
    
	@ApiModelProperty("自定义09字段code")
    @Column(name = "acitem09_code")
    private String acitem09Code;
    
	@ApiModelProperty("自定义09字段name")
    @Column(name = "acitem09_name")
    private String acitem09Name;
    
	@ApiModelProperty("自定义10字段code")
    @Column(name = "acitem10_code")
    private String acitem10Code;
    
	@ApiModelProperty("自定义10字段name")
    @Column(name = "acitem10_name")
    private String acitem10Name;

    // ------ 差旅特殊组件处理 start --------
    //同行人
    @TableField(exist = false)
    private List<PcxBillTravelFellows> selectFollower;
    // ------ 差旅特殊组件处理 end --------

}
