package com.pty.pcx.entity.treasurypay.detail;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.common.enu.BillPayDetailStatusEnum;
import com.pty.pcx.common.enu.BillPayTypeEnum;
import com.pty.pcx.dto.mad.MadAgyAccountDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillBalance;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.qo.bill.PcxBillPaySettlementBalanceQO;
import com.pty.pub.common.anno.Desc;
import com.pty.pub.common.util.PtyContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * (com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-12-13 18:36:32
 */
@Data
@Entity
@SuperBuilder
@Table(schema = "pcx_bill_pay_detail")
@AllArgsConstructor
public class PcxBillPayDetail implements Serializable {
    private static final long serialVersionUID = -42421904187109881L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据主键ID")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("费用类型")
    @Column(name = "expense_code")
    private String expenseCode;
    
	@ApiModelProperty("额度ID")
    @Column(name = "balance_id")
    private String balanceId;

	@ApiModelProperty("额度编码")
    @Column(name = "balance_id")
    private String balanceNo;

    @ApiModelProperty("额度来源")
    @Column(name = "balance_source")
    private String balanceSource;

//	@ApiModelProperty("结算信息主键id")
//    @Column(name = "settlement_id")
//    private String settlementId;

    @ApiModelProperty("结算信息唯一键")
    @Column(name = "settlement_uk")
    private String settlementUk;

    @ApiModelProperty("指标唯一键")
    @Column(name = "balance_uk")
    private String balanceUk;
    
	@ApiModelProperty("付款方账号")
    @Column(name = "pay_account_no")
    private String payAccountNo;

	@ApiModelProperty("付款方账号类型")
    @Column(name = "pay_account_type_code")
    private String payAccountTypeCode;

	@ApiModelProperty("付款方账号类型")
    @Column(name = "pay_account_type_name")
    private String payAccountTypeName;
    
	@ApiModelProperty("付款方名称")
    @Column(name = "pay_account_name")
    private String payAccountName;

    @ApiModelProperty("付款方式编码")
    @Column(name = "pay_type_code")
    private String payTypeCode;

    @ApiModelProperty("付款方式名称")
    @Column(name = "pay_type_name")
    private String payTypeName;
    
	@ApiModelProperty("付款方开户行")
    @Column(name = "pay_bank_name")
    private String payBankName;

    @ApiModelProperty("付款方开户城市")
    @Column(name = "pay_account_city")
    private String payAccountCity;

    @ApiModelProperty("付款方银行编码")
    @Column(name = "pay_bank_code")
    private String payBankCode;
    
	@ApiModelProperty("收款方账号")
    @Column(name = "payee_account_no")
    private String payeeAccountNo;
    
	@ApiModelProperty("收款方名称")
    @Column(name = "payee_account_name")
    private String payeeAccountName;
    
	@ApiModelProperty("收款方开户行")
    @Column(name = "payee_bank_name")
    private String payeeBankName;

    @ApiModelProperty("收款方开户城市")
    @Column(name = "payee_account_city")
    private String payeeAccountCity;

    @ApiModelProperty("收款银行编码")
    @Column(name = "payee_bank_code")
    private String payeeBankCode;

    @ApiModelProperty("收款银行网点行号")
    @Column(name = "payee_bank_node_no")
    private String payeeBankNodeNo;

    @ApiModelProperty("收款银行网点名称")
    @Column(name = "payee_bank_node_name")
    private String payeeBankNodeName;

    @ApiModelProperty("收款账户类型")
    @Column(name = "payee_account_type_code")
    private String payeeAccountTypeCode;

    @ApiModelProperty("收款账户类型名称")
    @Column(name = "payee_account_type_name")
    private String payeeAccountTypeName;

    @ApiModelProperty("对公/对私(1：个人对私 0：单位对公)")
    @Column(name = "pay_type")
    private String payType;
    
	@ApiModelProperty("结算方式类型")
    @Column(name = "settlement_type")
    private String settlementType;

    @ApiModelProperty("结算方式类型名称")
    @Column(name = "settlement_type_name")
    private String settlementTypeName;
    
	@ApiModelProperty("核定金额")
    @Column(name = "check_amt")
    private BigDecimal checkAmt;
    
	@ApiModelProperty("备注说明")
    @Column(name = "summary")
    private String summary;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("单位名称")
    @Column(name = "agy_name")
    private String agyName;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("确认时间")
    @Column(name = "confirm_time")
    private String confirmTime;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("支付申请单号")
    @Column(name = "pay_no")
    private String payNo;

    /**
     * @see BillPayDetailStatusEnum
     */
    @ApiModelProperty("支付状态(0=待生成,1=已生成,9=变更中,10=作废,3=支付异常,2=支付成功)")
    @Column(name = "pay_status")
    private String payStatus;

    @Column(name = "paying_time" )
    @ApiModelProperty("支付请求时间")
    private String payingTime;

    @Column(name = "paid_time" )
    @ApiModelProperty("支付完成时间")
    private String paidTime;

    @Column(name = "bank_receipt" )
    @ApiModelProperty("银行回单状态")
    private String bankReceipt;

    @Column(name = "modifier" )
    @ApiModelProperty("修改人编码")
    private String modifier;

    @Column(name = "modifier_name" )
    @ApiModelProperty("修改人名称")
    private String modifierName;

    @Column(name = "modified_time" )
    @ApiModelProperty("修改时间")
    private String modifiedTime;

    @Column(name = "creator" )
    @ApiModelProperty("创建人代码")
    private String creator;

    @Column(name = "creator_name" )
    @ApiModelProperty("创建人名称")
    private String creatorName;

    @Column(name = "created_time" )
    @ApiModelProperty("创建时间")
    private String createdTime;

    @Column(name = "ver" )
    @Desc("数据版本")
    private Integer ver;

    @Column(name = "is_deleted" )
    @Desc("是否作废,0是有效的数据,非0均为作废数据,每次作废取唯一键最大的isDeleted+1写入,避免使用payStatus造成真正唯一键无法防重")
    private Integer isDeleted;

    @Column(name = "loan_bill_id" )
    @ApiModelProperty("借款单id")
    private String loanBillId;

    @Column(name = "loan_bill_no" )
    @ApiModelProperty("借款单编码")
    private String loanBillNo;

    @TableField(exist = false)
    private List<String> neStatusList;

    @TableField(exist = false)
    private List<String> statusList;

    @JsonIgnore
    @TableField(exist = false)
    private BigDecimal allocatedAmt;

    @TableField(exist = false)
    private PcxBillPaySettlementBalanceQO.SettlementExtQO settlementExtQO;

    @TableField(exist = false)
    private PcxBillPaySettlementBalanceQO.BalanceExtQO balanceExtQO;

    @JsonIgnore
    @TableField(exist = false)
    private PcxBill bill;

    @TableField(exist = false)
    private String expenseName;

    public PcxBillPayDetail() {
        assembleBill();
        assembleBalance();
        assembleSettlement();
    }

    public PcxBillPayDetail(PcxBillPayDetail detail, PcxBillBalance first, BigDecimal allocatedAmt, PcxBillSettlement pcxBillSettlement) {
        this();
        BeanUtil.copyProperties(detail, this);
        this.balanceId = first.getId();
        this.checkAmt = allocatedAmt;
        this.settlementType = pcxBillSettlement.getSettlementType();
        this.setPayeeAccountName(pcxBillSettlement.getPayeeAccName());
        this.setPayeeAccountNo(pcxBillSettlement.getPayeeAccNo());
        this.setPayeeBankName(pcxBillSettlement.getPayeeBankName());
    }

    public PcxBillPayDetail sprinkle(PcxBillPaySettlementBalanceQO.SettlementExtQO settlementExtQO) {
        this.settlementExtQO = settlementExtQO;
        this.settlementType = settlementExtQO.getSettlementType();
        this.settlementTypeName = settlementExtQO.getSettlementTypeName();
        this.settlementUk = settlementExtQO.getSettlementUk();
        this.payeeAccountNo = settlementExtQO.getPayeeAccNo();
        this.payeeAccountName = settlementExtQO.getPayeeAccName();
        this.payeeBankName = settlementExtQO.getPayeeBankName();
        this.payeeBankCode = settlementExtQO.getPayeeBankCode();
        this.payeeAccountCity = settlementExtQO.getPayeeAccCity();
        this.payeeBankNodeNo = settlementExtQO.getPayeeBankNodeNo();
        this.payType = settlementExtQO.getPayType();
        this.payeeBankNodeName = settlementExtQO.getPayeeBankNodeName();
        this.payStatus = BillPayDetailStatusEnum.STASH.getCode();
        this.payeeAccountTypeCode = settlementExtQO.getPayeeAccountType();
        this.payeeAccountTypeName = settlementExtQO.getPayeeAccountTypeName();
        this.settlementExtQO.setBalanceList(ObjectUtils.firstNonNull(this.settlementExtQO.getBalanceList(), new ArrayList<>()));
        return this;
    }

    public PcxBillPayDetail sprinkle(PcxBillPaySettlementBalanceQO.BalanceExtQO balanceExtQO) {
        // 添加指标, 如果指标ID不为空, 则添加指标扩展信息
        if (Objects.nonNull(balanceExtQO.getBalanceId())) {
            this.balanceExtQO = ObjectUtil.cloneByStream(balanceExtQO);
            BigDecimal minAlloc = this.settlementExtQO.getAllocatedAmt().compareTo(this.balanceExtQO.getAllocatedAmt()) > 0 ? this.balanceExtQO.getAllocatedAmt() : this.settlementExtQO.getAllocatedAmt();
            this.balanceExtQO.setUsedAmt(minAlloc);
            this.balanceId = balanceExtQO.getBalanceId();
            this.balanceNo = balanceExtQO.getBalanceNo();
            this.expenseCode = balanceExtQO.getExpenseCode();
            this.expenseName = balanceExtQO.getExpenseName();
            this.payAccountName = ObjectUtils.firstNonNull(balanceExtQO.getPayAccountName(), this.payAccountName, StringUtils.EMPTY);
            this.balanceExtQO.setPayAccountName(this.payAccountName);
            this.payAccountNo = ObjectUtils.firstNonNull(balanceExtQO.getPayAccountNo(), this.payAccountNo, StringUtils.EMPTY);
            this.balanceExtQO.setPayAccountNo(this.payAccountNo);
            this.payBankCode = ObjectUtils.firstNonNull(balanceExtQO.getPayBankCode(), this.payBankCode, StringUtils.EMPTY);
            this.balanceExtQO.setPayBankCode(this.payBankCode);
            this.payAccountTypeCode = ObjectUtils.firstNonNull(balanceExtQO.getPayAccountType(), this.payAccountTypeCode, StringUtils.EMPTY);
            this.payAccountTypeName = ObjectUtils.firstNonNull(balanceExtQO.getPayAccountTypeName(), this.payAccountTypeName, StringUtils.EMPTY);
            this.balanceExtQO.setPayAccountType(this.payAccountTypeCode);
            this.payAccountCity = ObjectUtils.firstNonNull(balanceExtQO.getPayAccCity(), this.payAccountCity, StringUtils.EMPTY);
            this.balanceExtQO.setPayAccCity(this.payAccountCity);
            this.payBankName = ObjectUtils.firstNonNull(balanceExtQO.getPayBankName(), this.payBankName, StringUtils.EMPTY);
            this.balanceExtQO.setPayBankName(this.payBankName);
            this.setPayTypeCode(BillPayTypeEnum.BANK_TRANSFER.getCode());
            this.setPayTypeName(BillPayTypeEnum.BANK_TRANSFER.getName());
            if (!this.settlementExtQO.getBalanceList().stream().map(PcxBillPaySettlementBalanceQO.BalanceExtQO::getBalanceId).collect(Collectors.toList()).contains(balanceExtQO.getBalanceId())) {
                this.settlementExtQO.getBalanceList().add(this.balanceExtQO);
            }
        }
        return this;
    }

    public PcxBillPayDetail sprinkle(PcxBill pcxBill) {
        this.bill = pcxBill;
        this.setBillId(pcxBill.getId());
        this.setMofDivCode(pcxBill.getMofDivCode());
        this.setFiscal(pcxBill.getFiscal());
        this.setAgyCode(pcxBill.getAgyCode());
        this.setTenantId(pcxBill.getTenantId());
        this.setAgyName(pcxBill.getAgyName());
        this.setCreator(PtyContext.getUsername());
        this.setCreatorName(PtyContext.getUsernameCn());
        this.setCreatedTime(DateUtil.now());
        this.setPayStatus(BillPayDetailStatusEnum.STASH.getCode());
        this.setModifier(PtyContext.getUsername());
        this.setModifierName(PtyContext.getUsernameCn());
        this.setModifiedTime(DateUtil.now());
        this.setVer(0);
        this.setIsDeleted(0);
        return this;
    }


    public PcxBillPayDetail sprinkle(MadAgyAccountDTO account) {
        if (Objects.nonNull(account)) {
            this.setPayAccountNo(account.getAccountCode());
            this.setPayAccountName(account.getAccountName());
            this.setPayBankName(account.getBankName());
            this.setPayBankCode(account.getBankCode());
            this.setPayAccountTypeCode(account.getAccountType());
        }
        return this;
    }

    public PcxBillPayDetail assembleSettlement() {
        if (Objects.nonNull(this.settlementUk))
            this.settlementExtQO = PcxBillPaySettlementBalanceQO.SettlementExtQO.builder()
                    .payeeAccCity(this.payeeAccountCity)
                    .payeeBankNodeNo(this.payeeBankNodeNo)
                    .payeeBankNodeName(this.payeeBankNodeName)
                    .payeeBankCode(this.payeeBankCode)
                    .payeeAccNo(this.payeeAccountNo)
                    .build();
        return this;
    }

    public PcxBillPayDetail assembleBalance() {
        if (Objects.nonNull(this.balanceId))
            this.balanceExtQO = PcxBillPaySettlementBalanceQO.BalanceExtQO.builder()
                    .payAccountNo(this.payAccountNo)
                    .payAccountName(this.payAccountName)
                    .payBankName(this.payBankName)
                    .payAccountType(this.payAccountTypeCode)
                    .payAccCity(this.payAccountCity)
                    .payBankCode(this.payBankCode)
                    .balanceId(this.balanceId)
                    .balanceNo(this.balanceNo)
                    .usedAmt(this.checkAmt)
                    .relBillId(this.loanBillId)
                    .relBillNo(this.loanBillNo)
                    .build();
        return this;
    }

    public PcxBillPayDetail assembleBill() {
        if (Objects.nonNull(this.billId))
            this.bill = PcxBill.builder()
                    .id(this.billId)
                    .build();
        return this;
    }
}

