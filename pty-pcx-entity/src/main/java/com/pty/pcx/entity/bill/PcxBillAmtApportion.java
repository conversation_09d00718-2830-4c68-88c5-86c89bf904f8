package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报销单费用分摊(PcxBillAmtApportion)实体类
 *
 * <AUTHOR>
 * @since 2025-04-01 07:38:42
 */
@Data
@Entity
@Table(schema = "pcx_bill_amt_apportion")
public class PcxBillAmtApportion implements Serializable {
    private static final long serialVersionUID = -22298650012509191L;

    @Id
	@ApiModelProperty("$column.comment")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("报销单id")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("费用类型编码")
    @Column(name = "expense_type_code")
    private String expenseTypeCode;
    
	@ApiModelProperty("费用类型名称")
    @Column(name = "expense_type_name")
    private String expenseTypeName;

    @ApiModelProperty("费用类型名称")
    @Column(name = "seq")
    private Integer seq;

    @ApiModelProperty("录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;

	@ApiModelProperty("金额")
    @Column(name = "apportion_amt")
    private BigDecimal apportionAmt;
    
}

