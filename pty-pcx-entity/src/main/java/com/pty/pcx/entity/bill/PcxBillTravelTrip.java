package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 差旅费行程表，存储差旅费单据的行程信息(PcxBillTravelTrip)实体类
 *
 * <AUTHOR>
 * @since 2024-11-27 19:53:18
 */
@Data
@Entity
@Table(schema = "pcx_bill_travel_trip")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PcxBillTravelTrip implements Serializable {
    private static final long serialVersionUID = -38153911564931012L;

    @Id
	@ApiModelProperty("关联表主键Id，唯一标识每条行程记录")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据id，标识差旅费单据")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("开始时间，记录行程开始时间")
    @Column(name = "start_time")
    private String startTime;
    
	@ApiModelProperty("结束时间，记录行程结束时间")
    @Column(name = "end_time")
    private String endTime;
    
	@ApiModelProperty("行程人编码，标识行程人员")
    @Column(name = "emp_code")
    private String empCode;
    
	@ApiModelProperty("行程人，记录行程人员的姓名")
    @Column(name = "emp_name")
    private String empName;
    
	@ApiModelProperty("单位名称，记录行程人员所属单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度，记录行程发生的财政年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划，记录行程发生的行政区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户ID，标识数据所属租户")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("行程类型，traffic,hotel")
    @Column(name = "trip_type")
    private String tripType;
    
}

