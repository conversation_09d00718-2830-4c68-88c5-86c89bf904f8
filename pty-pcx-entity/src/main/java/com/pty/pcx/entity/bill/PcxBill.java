package com.pty.pcx.entity.bill;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.common.enu.BillShowStatusEnum;
import com.pty.pcx.common.enu.ComparedResultStatus;
import com.pty.pub.common.anno.Desc;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 单据主表(PcxBill)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:02
 */
@Data
@Entity
@Table(schema = "pcx_bill")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PcxBill implements Serializable {
    private static final long serialVersionUID = -30714772983455500L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单号(根据单号规则生成)")
    @Column(name = "bill_no")
    private String billNo;
    
	@ApiModelProperty("填报人员编码")
    @Column(name = "claimant_code")
    private String claimantCode;

	@ApiModelProperty("填报用户编码")
    @Column(name = "claimant_user_code")
    private String claimantUserCode;
    
	@ApiModelProperty("填报人员名称")
    @Column(name = "claimant_name")
    private String claimantName;
    
	@ApiModelProperty("报销人部门code")
    @Column(name = "department_code")
    private String departmentCode;
    
	@ApiModelProperty("报销人部门名称")
    @Column(name = "department_name")
    private String departmentName;
    
	@ApiModelProperty("业务日期")
    @Column(name = "trans_date")
    private String transDate;

    @ApiModelProperty("事由")
    @Column(name = "reason")
    private String reason;
    
	@ApiModelProperty("事项类型代码")
    @Column(name = "item_code")
    private String itemCode;
    
	@ApiModelProperty("事项类型名称")
    @Column(name = "item_name")
    private String itemName;
    
	@ApiModelProperty("终审时间")
    @Column(name = "audit_time")
    private String auditTime;
    
	@ApiModelProperty("终审人编码")
    @Column(name = "audit_code")
    private String auditCode;
    
	@ApiModelProperty("终审人名称")
    @Column(name = "audit_name")
    private String auditName;
    
	@ApiModelProperty("支付完成时间（报销/借款）")
    @Column(name = "pay_time")
    private String payTime;
    
	@ApiModelProperty("支付状态")
    @Column(name = "pay_status")
    private String payStatus;
    
	@ApiModelProperty("单据状态")
    @Column(name = "bill_status")
    private String billStatus;

    @ApiModelProperty("审核状态")
    @Column(name = "approve_status")
    private String approveStatus;
    
	@ApiModelProperty("单据种类编码")
    @Column(name = "bill_func_code")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String billFuncCode;
    
	@ApiModelProperty("单据种类名称（申请/报销/借款/还款/计划）")
    @Column(name = "bill_func_name")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String billFuncName;
    
	@ApiModelProperty("录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;
    
	@ApiModelProperty("核定金额")
    @Column(name = "check_amt")
    private BigDecimal checkAmt;

    @ApiModelProperty("结算金额（报销/借款）")
    @Column(name = "settlement_amt")
    private BigDecimal settlementAmt;
    
	@ApiModelProperty("冲销金额（报销）")
    @Column(name = "loan_amt")
    private BigDecimal loanAmt;

    @Column(name = "tax_amt")
    @Desc("代扣税金（劳务）")
    private BigDecimal taxAmt;

    @ApiModelProperty("预计还款时间")
    @Column(name = "repay_date")
    private String repayDate;

    @ApiModelProperty("业务类型")
    @Column(name = "biz_type")
    private Integer bizType;

    @ApiModelProperty("业务类型")
    @Column(name = "biz_type_name")
    private String bizTypeName;
    
	@ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;
    
	@ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;
    
	@ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;
    
	@ApiModelProperty("创建人代码")
    @Column(name = "creator")
    private String creator;
    
	@ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;
    
	@ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String agyCode;

    @ApiModelProperty("单位名称")
    @Column(name = "agy_name")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String agyName;

	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String mofDivCode;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String tenantId;
    
	@ApiModelProperty("费用集合")
    @Column(name = "expense_codes")
    private String expenseCodes;

    @ApiModelProperty("费用集合")
    @Column(name = "expense_names")
    private String expenseNames;

    @ApiModelProperty("费用承担部门编码集合（逗号隔开）")
    @Column(name = "exp_department_codes")
    private String expDepartmentCodes;

    @ApiModelProperty("费用承担部门集合")
    @Column(name = "exp_department_names")
    private String expDepartmentNames;

    @ApiModelProperty("项目编码集合（逗号隔开）")
    @Column(name = "project_codes")
    private String projectCodes;

    @ApiModelProperty("项目名称集合")
    @Column(name = "project_names")
    private String projectNames;

    @ApiModelProperty("资金性质集合（逗号隔开）")
    @Column(name = "fundtype_codes")
    private String fundtypeCodes;

    @ApiModelProperty("资金性质集合")
    @Column(name = "fundtype_names")
    private String fundtypeNames;

    /***
      @see ComparedResultStatus
     */
    @ApiModelProperty("纸电比对状态 -1 未比对,1 对比通过 ,2 对比异常, 3无需比对")
    @Column(name = "compared_status")
    private Integer comparedStatus;

    @ApiModelProperty("办结状态（待结项/结项中/完结）")
    @Column(name = "completed_status")
    private String completedStatus;

    @ApiModelProperty("办结时间")
    @Column(name = "completed_time")
    private String completedTime;

    @ApiModelProperty("办结人")
    @Column(name = "completed")
    private String completed;

    @ApiModelProperty("办结人名称")
    @Column(name = "completed_name")
    private String completedName;

    @ApiModelProperty("是否已记账")
    @Column(name = "is_vou")
    private Integer isVou;

    @ApiModelProperty("凭证id")
    @Column(name = "vou_id")
    private String vouId;

    @ApiModelProperty("凭证号")
    @Column(name = "vou_no")
    private String vouNo;

    @ApiModelProperty("凭证日期")
    @Column(name = "vou_date")
    private String vouDate;

    @ApiModelProperty("拓展json")
    @Column(name = "expand_json")
    private String expandJson;

    @TableField(exist = false)
    private String keyword;

    @TableField(exist = false)
    private List<String> apportionDeptList;

    @TableField(exist = false)
    private List<String> keywordFields;

    @JsonIgnore
    @TableField(exist = false)
    private List<String> ids;

    @JsonIgnore
    @TableField(exist = false)
    private List<String> billFuncCodes;

    /**
     * 首页列表中查询所有单据
     * 在sql中根据weight确定 前端是否进入历史单据
     * @see BillShowStatusEnum
     * weight > 0 代表BillShowStatus.HISTORY
     */
    //-----------------------------------------------
    @JsonIgnore
    @TableField(exist = false)
    private Boolean history;


    @ApiModelProperty("内容/简报")
    @TableField(exist = false)
    private String content;

    @ApiModelProperty("附件数量")
    @TableField(exist = false)
    private Integer attachCount;

    @ApiModelProperty("票数量")
    @TableField(exist = false)
    private Integer ecsCount;
    @ApiModelProperty("代办状态")
    @TableField(exist = false)
    private String todoStatus;
    /**
     * 单位统一社会信用代码
     */
    @TableField(exist = false)
    private String dutyNo;

    /**
     * 分摊模式
     */
    @TableField(exist = false)
    private Integer apportionType;

    /**
     * 仅报补助
     */
    @TableField(exist = false)
    private Boolean onlySubsidy;

    @TableField(exist = false)
    private String owner;

    public Integer getApportionType() {
        if (StringUtil.isEmpty(expandJson)){
            return 1;
        }
        JSONObject json = JSONObject.parseObject(expandJson);
        Integer apportionType = json.getInteger("apportionType");
        if (Objects.isNull(apportionType)){
            return 1;
        }
        return apportionType;
    }

    public void setApportionType(Integer apportionType) {
        if (StringUtil.isEmpty(expandJson)){
            expandJson = "{}";
        }
        JSONObject json = JSONObject.parseObject(expandJson);
        json.put("apportionType", apportionType);
        this.expandJson = json.toJSONString();
    }

    public void setOnlySubsidy(boolean onlySubsidy) {
        if (StringUtil.isEmpty(expandJson)){
            expandJson = "{}";
        }
        JSONObject json = JSONObject.parseObject(expandJson);
        json.put("onlySubsidy", onlySubsidy);
        this.expandJson = json.toJSONString();
    }

    public Boolean getOnlySubsidy() {
        if (StringUtil.isEmpty(expandJson)){
            return Boolean.FALSE;
        }
        JSONObject json = JSONObject.parseObject(expandJson);
        Boolean only = json.getBoolean("onlySubsidy");
        if (Objects.isNull(only)){
            return Boolean.FALSE;
        }
        return only;
    }

    //-----------------------------------------------

    public String getBillFunc() {
        return billFuncCode;
    }

    public List<String> getExpenses() {
        List<String> list = new ArrayList<>();
        if (StringUtil.isNotEmpty(expenseCodes)) {
            list.addAll(Arrays.asList(expenseCodes.split(",")));
        }
        if (StringUtil.isNotEmpty(expenseNames)) {
            list.addAll(Arrays.asList(expenseNames.split(",")));
        }
        return list;
    }

    /**
     * 获取单个默认的费用承担部门
     * @return
     */
    public String getSingleDepartmentCode() {
        if (StringUtil.isEmpty(expDepartmentCodes)){
            return departmentCode;
        }
        if (expDepartmentCodes.split(",").length>1){
            return expDepartmentCodes.split(",")[0];
        }else {
            return expDepartmentCodes;
        }

    }

    /**
     * 获取单个默认的费用承担部门
     * @return
     */
    public String getSingleDepartmentName() {
        if (StringUtil.isEmpty(expDepartmentNames)){
            return departmentName;
        }
        if (expDepartmentNames.split(",").length>1){
            //默认先取第一个
            return expDepartmentNames.split(",")[0];
        }else {
            return expDepartmentNames;
        }
    }
}
