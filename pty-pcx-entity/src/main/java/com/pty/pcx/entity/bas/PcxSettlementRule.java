package com.pty.pcx.entity.bas;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 结算规则设置(PcxBasSettlementSetting)实体类
 *
 * <AUTHOR>
 * @since 2024-12-09 18:07:02
 */
@Data
@Entity
@Table(schema = "pcx_settlement_rule")
public class PcxSettlementRule implements Serializable {
    private static final long serialVersionUID = 659481206038949961L;

    @Id
	@ApiModelProperty("表主键Id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("结算方式")
    @Column(name = "settlement_type")
    private String settlementType;
    
	@ApiModelProperty("结算方式名称")
    @Column(name = "settlement_name")
    private String settlementName;
    
	@ApiModelProperty("状态0停用1启用（默认1）")
    @Column(name = "is_enabled")
    private Integer isEnabled;
    
	@ApiModelProperty("单位名称")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @TableField(exist = false)
    private List<String> settlementTypes;
}
