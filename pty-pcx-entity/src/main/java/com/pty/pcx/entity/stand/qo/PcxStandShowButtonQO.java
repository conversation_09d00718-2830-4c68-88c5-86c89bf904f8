package com.pty.pcx.entity.stand.qo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PcxStandShowButtonQO {

    @NotNull(message = "年度不能为空")
    private Integer fiscal;

    @NotBlank(message = "区划编码不能为空")
    private String mofDivCode;

    @NotBlank(message = "单位编码不能为空")
    private String agyCode;

    /**
     * 按钮类型编码(不传默认为城市分类)
     */
    private String btnTypeCode;

    /**
     * 费用类型编码
     */
    private String expTypeCode;

}
