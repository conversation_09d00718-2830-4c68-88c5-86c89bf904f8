package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单据费用明细表_出国费_明细(PcxBillExpDetailAbroad)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
@Entity
@Table(name = "pcx_bill_exp_detail_abroad")
public class PcxBillExpDetailAbroad extends PcxBillExpDetailBase implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty("费用明细主键ID")
    @Column(name = "id", nullable = false, length = 50)
    private String id;

    @ApiModelProperty("单据主键ID")
    @Column(name = "bill_id", length = 50)
    private String billId;

    @ApiModelProperty("费用主键ID")
    @Column(name = "expense_id", length = 50)
    private String expenseId;

    @ApiModelProperty("行程关联费用明细ID")
    @Column(name = "trip_expense_id", length = 50)
    private String tripExpenseId;

    @ApiModelProperty("费用类型代码")
    @Column(name = "expense_code", length = 30)
    private String expenseCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_name", length = 100)
    private String expenseName;

    @ApiModelProperty("费用类型明细编码")
    @Column(name = "exp_detail_code", length = 30)
    private String expDetailCode;

    @ApiModelProperty("费用类型明细名称")
    @Column(name = "exp_detail_name", length = 100)
    private String expDetailName;

    @ApiModelProperty("费用录入金额")
    @Column(name = "input_amt", precision = 16, scale = 2)
    private BigDecimal inputAmt;

    @ApiModelProperty("费用核定金额")
    @Column(name = "check_amt", precision = 16, scale = 2)
    private BigDecimal checkAmt;

    @ApiModelProperty("单位编码")
    @Column(name = "agy_code", length = 30)
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal", length = 4)
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code", length = 9)
    private String mofDivCode;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id", length = 50)
    private String tenantId;

    @ApiModelProperty("城市代码")
    @Column(name = "city", length = 50)
    private String city;

    @ApiModelProperty("城市名称")
    @Column(name = "city_name", length = 50)
    private String cityName;

    @ApiModelProperty("国家代码")
    @Column(name = "country", length = 50)
    private String country;

    @ApiModelProperty("国家名称")
    @Column(name = "country_name", length = 500)
    private String countryName;

    @ApiModelProperty("标准")
    @Column(name = "normal_amt", precision = 16, scale = 2)
    private BigDecimal normalAmt;

    @ApiModelProperty("天数")
    @Column(name = "days")
    private Integer days;

    @ApiModelProperty("人数")
    @Column(name = "person_num")
    private Integer personNum;

    @ApiModelProperty("补充说明")
    @Column(name = "remark", length = 256)
    private String remark;

    @ApiModelProperty("币种")
    @Column(name = "currency", length = 50)
    private String currency;

    @ApiModelProperty("创建人")
    @Column(name = "creator", length = 30)
    private String creator;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time", length = 100)
    private String createTime;

    @ApiModelProperty("创建人姓名")
    @Column(name = "creator_name", length = 20)
    private String creatorName;

    @ApiModelProperty("修改人编码")
    @Column(name = "modifier", length = 30)
    private String modifier;

    @ApiModelProperty("修改人名称")
    @Column(name = "modifier_name", length = 100)
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time", length = 20)
    private String modifiedTime;

    @ApiModelProperty("费用类型（外币、人民币）")
    @Column(name = "expense_type")
    private Integer expenseType;

    @ApiModelProperty("是否删除 1己删除 2末删除")
    @Column(name = "is_deleted_number")
    private Integer isDeletedNumber;

    @TableField(exist = false)
    private BigDecimal standAmt;

    @TableField(exist = false)
    private String source;

    @TableField(exist = false)
    private BigDecimal taxRate;

    @TableField(exist = false)
    private BigDecimal taxAmt;

    @TableField(exist = false)
    private Integer ecsNum = 1;

    @TableField(exist = false)
    private String noEcsReason;

    @TableField(exist = false)
    private String departmentCode;

    @TableField(exist = false)
    private String departmentName;
}