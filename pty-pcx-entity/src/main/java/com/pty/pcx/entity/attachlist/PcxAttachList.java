package com.pty.pcx.entity.attachlist;

import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * PaAttachList 实体类，对应于 pa_attach_list 表
 */
@Data
@Table(name = "pa_attach_list")
@NoArgsConstructor
public class PcxAttachList implements Serializable {

    private static final long serialVersionUID = -8707124179980683238L;

    /**
     * 附件清单ID，主键
     */
    @Id
    @Column(name = "attach_list_id")
    @NotBlank(message = "附件清单id不能为空", groups = {Update.class})
    private String attachListId;

    /**
     * 附件类型ID
     */
    @Column(name = "attach_type_id")
    @NotBlank(message = "附件类型ID不能为空", groups = {Update.class})
    private String attachTypeId;

    /**
     * 单据类型：支持多选，多选的时候用逗号隔开，如 "apply,expense"）
     * {@link BillFuncCodeEnum}
     */
    @Column(name = "billtype_code")
    @NotBlank(message = "单据类型不能为空", groups = {Update.class})
    private String billtypeCode;

    /**
     * 功能类型：1-事项类型、2-费用类型
     */
    @Column(name = "func_type")
    @NotBlank(message = "功能类型不能为空", groups = {Update.class, Query.class})
    private String funcType;

    /**
     * 功能编码：事项类型编码或费用明细类型编码
     */
    @Column(name = "func_code")
    @NotBlank(message = "功能编码不能为空", groups = {Update.class, Query.class})
    private String funcCode;

    /**
     * 附件名称
     */
    @Column(name = "attach_list_name")
    @NotBlank(message = "附件名称不能为空", groups = {Update.class})
    private String attachListName;

    /**
     * 说明
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 提供方式code：1-线上，2-线下
     */
    @Column(name = "provide_way_code")
    private String provideWayCode;

    /**
     * 提供方式名称：1-线上，2-线下
     */
    @Column(name = "provide_way_name")
    private String provideWayName;

    /**
     * 是否必须，apply申请单必须、expense报销单必须（支持多选，多选的时候用逗号隔开，如 "apply,expense"）
     * {@link com.pty.pcx.common.enu.BillFuncCode}
     */
    @Column(name = "is_necessary")
    private String isNecessary;

    /**
     * 附件格式
     */
    @Column(name = "attach_format")
    private String attachFormat;

    /**
     */
    @Column(name = "agy_code")
    @NotBlank(message = "单位编码不能为空", groups = {Update.class, Query.class})
    private String agyCode;

    /**
     * 年度
     */
    @Column(name = "fiscal")
    @NotNull(message = "年份不能为空", groups = {Update.class, Query.class})
    private Integer fiscal;

    /**
     * 行号
     */
    @Column(name = "row_num")
    private Integer rowNum;

    /**
     * 附件清单模版名称
     */
    @Column(name = "template_name")
    @NotBlank(message = "模版名称不能为空", groups = {Update.class})
    private String templateName;

    /**
     * 附件清单关联模版附件id
     */
    @Column(name = "template_id")
    @NotBlank(message = "模版ID不能为空", groups = {Update.class})
    private String templateId;

    /**
     * 项目大类code
     */
    @Column(name = "category_code")
    private String categoryCode;

    /**
     * 附件上传类型，支持多选，例如：form,qrcode,eloam,cherryBill,attchBox
     */
    @Column(name = "upload_type")
    private String uploadType;

    /**
     * 区划编码
     */
    @Column(name = "mof_div_code")
    @NotBlank(message = "区划编码不能为空", groups = {Update.class, Query.class})
    private String mofDivCode;

    /**
     * 关联费用
     */
    @Column(name = "relate_expense")
    private String relateExpense;

    /**
     * 租户
     */
    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "seq")
    private Integer seq;

}
