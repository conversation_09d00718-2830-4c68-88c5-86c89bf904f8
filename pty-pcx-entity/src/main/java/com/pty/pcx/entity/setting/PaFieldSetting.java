package com.pty.pcx.entity.setting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pa_field_setting")
@NoArgsConstructor
public class PaFieldSetting implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @ApiModelProperty("主键id")
  @Column(name = "id")
  private String id;

  @ApiModelProperty("系统代码")
  @Column(name = "sys_id")
  private String sysId;

  @ApiModelProperty("菜单地址")
  @Column(name = "menu_url")
  private String menuUrl;

  @ApiModelProperty("年度")
  @Column(name = "fiscal")
  private String fiscal;

  @JsonIgnore
  @TableField(exist = false)
  private String fiscalOrAll;

  @ApiModelProperty("用户编码")
  @Column(name = "user_code")
  private String userCode;

  @JsonIgnore
  @TableField(exist = false)
  private String userCodeOrAll;

  @ApiModelProperty("单位")
  @Column(name = "agy_code")
  private String agyCode;

  @JsonIgnore
  @TableField(exist = false)
  private String agyCodeOrAll;

  @ApiModelProperty("排序值（升序）")
  @Column(name = "ord_seq")
  private Integer ordSeq;

  @ApiModelProperty("列名编码")
  @Column(name = "column_code")
  private String columnCode;

  @ApiModelProperty("列名名称")
  @Column(name = "column_name")
  private String columnName;

  @ApiModelProperty("描述")
  @Column(name = "explain_desc")
  private String explainDesc;

  @ApiModelProperty("是否显示（1=是；0=否）")
  @Column(name = "is_show")
  private Integer isShow;

  @ApiModelProperty("列宽")
  @Column(name = "column_width")
  private Integer columnWidth;

  @ApiModelProperty("排序方式（10=禁用，11=打开，12=关闭）")
  @Column(name = "sort")
  private String sort;

  @ApiModelProperty("是否锁定")
  @Column(name = "is_locking")
  private Integer isLocking;

  @ApiModelProperty("区分类型")
  @Column(name = "type")
  private String type;

  @Column(name = "ext_field01")
  private String extField01;

  @Column(name = "ext_field02")
  private String extField02;

  @Column(name = "ext_field03")
  private String extField03;

  @Column(name = "ext_field04")
  private String extField04;

  @Column(name = "ext_field05")
  private String extField05;

  @Column(name = "ext_field06")
  private String extField06;

  @Column(name = "ele_level")
  private String eleLevel;

  @Column(name = "atom_code")
  private String atomCode;

  @Column(name = "atom_data_url")
  private String atomDataUrl;

  @Column(name = "is_null")
  private Integer isNull;

  @Column(name = "code")
  private String code;

  @Column(name = "name")
  private String name;

  @Column(name = "data_type")
  private String dataType;

  @Column(name = "code_name")
  private String codeName;

  @Column(name = "disabled")
  private String disabled;

  @Column(name = "required")
  private String required;

  @Column(name = "editor")
  private String editor;

  @Column(name = "bud_sort")
  private String budSort;

  @Column(name = "son_title")
  private String sonTitle;

  @Column(name = "is_show_code")
  private String isShowCode;

  @ApiModelProperty("区划编码")
  @Column(name = "mof_div_code")
  private String mofDivCode;

  @JsonIgnore
  @TableField(exist = false)
  private String mofDivCodeOrAll;

  @Column(name = "tenant_id")
  private String tenantId;

  /**
   * 验证参数是否为空(查询接口)
   * @throws IllegalArgumentException 如果有参数为空,则抛出异常
   */
  public void validateQuery() throws IllegalArgumentException {
    if (StringUtil.isEmpty(fiscal)) {
      throw new IllegalArgumentException("年度[fiscal]不能为空");
    }
    if (StringUtil.isEmpty(agyCode)) {
      throw new IllegalArgumentException("单位编码[agyCode]不能为空");
    }
    if (StringUtil.isEmpty(mofDivCode)) {
      throw new IllegalArgumentException("区划编码[mofDivCode]不能为空");
    }
    if (StringUtil.isEmpty(tenantId)) {
      this.tenantId = PtyContext.getTenantId();
    }
  }

  /**
   * 验证参数是否为空(查询接口)
   * @throws IllegalArgumentException 如果有参数为空,则抛出异常
   */
  public void validateUserQuery() throws IllegalArgumentException {
    if (StringUtil.isEmpty(sysId)) {
      throw new IllegalArgumentException("系统代码[sysId]不能为空");
    }
    if (StringUtil.isEmpty(menuUrl)) {
      throw new IllegalArgumentException("菜单参数[menuUrl]不能为空");
    }
    if (StringUtil.isEmpty(fiscal)) {
      throw new IllegalArgumentException("年度[fiscal]不能为空");
    } else {
//      this.fiscalOrAll = fiscal;// 年度不做限制
      this.fiscal = null;
    }
    if (StringUtil.isEmpty(agyCode)) {
      throw new IllegalArgumentException("单位编码[agyCode]不能为空");
    } else {
      this.agyCodeOrAll = agyCode;//查询单位或者系统预设*对应的数据
      this.agyCode = null;
    }
    if (StringUtil.isEmpty(mofDivCode)) {
      throw new IllegalArgumentException("区划编码[mofDivCode]不能为空");
    } else {
      this.mofDivCodeOrAll = mofDivCode;//查询区划或者系统预设*对应的数据
      this.mofDivCode = null;
    }
    if (StringUtil.isEmpty(tenantId)) {
      this.tenantId = PtyContext.getTenantId();
    }
    if (StringUtil.isEmpty(userCode)) {
      String currentUserCode = PtyContext.getUsername();
      this.userCodeOrAll = currentUserCode;//查询用户code或者系统预设*对应的数据
    } else {
      this.userCodeOrAll = userCode;//查询用户code或者系统预设*对应的数据
      this.userCode = null;
    }
  }
}
