package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报销单费用分摊部门(PcxBillAmtApportionDepartment)实体类
 *
 * <AUTHOR>
 * @since 2025-04-01 07:38:57
 */
@Data
@Entity
@Table(schema = "pcx_bill_amt_apportion_department")
public class PcxBillAmtApportionDepartment implements Serializable {
    private static final long serialVersionUID = 128117888850286533L;

    @Id
	@ApiModelProperty("id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("报销单id")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;

	@ApiModelProperty("比例")
    @Column(name = "department_rate")
    private BigDecimal departmentRate;

	@ApiModelProperty("金额")
    @Column(name = "department_amt")
    private BigDecimal departmentAmt;

    @ApiModelProperty("录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;

    @ApiModelProperty("分摊主表id")
    @Column(name = "apportion_id")
    private String apportionId;

    @ApiModelProperty("费用类型编码")
    @Column(name = "expense_type_code")
    private String expenseTypeCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_type_name")
    private String expenseTypeName;

	@ApiModelProperty("分摊部门")
    @Column(name = "department_code")
    private String departmentCode;

	@ApiModelProperty("分摊部门名称")
    @Column(name = "department_name")
    private String departmentName;

    @ApiModelProperty("预算部门")
    @Column(name = "bud_department_code")
    private String budDepartmentCode;

    @ApiModelProperty("预算部门名称")
    @Column(name = "bud_department_name")
    private String budDepartmentName;

	@ApiModelProperty("拓展元素1")
    @Column(name = "acitem01_code")
    private String acitem01Code;

	@ApiModelProperty("拓展元素1")
    @Column(name = "acitem01_name")
    private String acitem01Name;

	@ApiModelProperty("拓展元素2")
    @Column(name = "acitem02_code")
    private String acitem02Code;

    @ApiModelProperty("拓展元素2")
    @Column(name = "acitem02_name")
    private String acitem02Name;

	@ApiModelProperty("拓展元素3")
    @Column(name = "acitem03_code")
    private String acitem03Code;

    @ApiModelProperty("拓展元素3")
    @Column(name = "acitem03_name")
    private String acitem03Name;

	@ApiModelProperty("拓展元素4")
    @Column(name = "acitem04_code")
    private String acitem04Code;

    @ApiModelProperty("拓展元素4")
    @Column(name = "acitem04_name")
    private String acitem04Name;

	@ApiModelProperty("拓展元素5")
    @Column(name = "acitem05_code")
    private String acitem05Code;

	@ApiModelProperty("拓展元素5")
    @Column(name = "acitem05_name")
    private String acitem05Name;

	@ApiModelProperty("拓展元素6")
    @Column(name = "acitem06_code")
    private String acitem06Code;

	@ApiModelProperty("拓展元素6")
    @Column(name = "acitem06_name")
    private String acitem06Name;

	@ApiModelProperty("拓展元素7")
    @Column(name = "acitem07_code")
    private String acitem07Code;

	@ApiModelProperty("拓展元素7")
    @Column(name = "acitem07_name")
    private String acitem07Name;

	@ApiModelProperty("拓展元素8")
    @Column(name = "acitem08_code")
    private String acitem08Code;

	@ApiModelProperty("拓展元素8")
    @Column(name = "acitem08_name")
    private String acitem08Name;

	@ApiModelProperty("拓展元素9")
    @Column(name = "acitem09_code")
    private String acitem09Code;

	@ApiModelProperty("拓展元素9")
    @Column(name = "acitem09_name")
    private String acitem09Name;

	@ApiModelProperty("拓展元素10")
    @Column(name = "acitem10_code")
    private String acitem10Code;

	@ApiModelProperty("拓展元素10")
    @Column(name = "acitem10_name")
    private String acitem10Name;
    
}

