package com.pty.pcx.entity.ecscompared;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pcx_ecs_compared_result")
@NoArgsConstructor
public class PcxEcsComparedResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty("主键ID")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("修改人")
    @Column(name = "modifier")
    private String modifier;

    @ApiModelProperty("修改人")
    @Column(name = "modifier_name")
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

    @ApiModelProperty("创建人")
    @Column(name = "creator")
    private String creator;

    @ApiModelProperty("创建人")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("原始附件ID")
    @Column(name = "source_attach_id")
    private String sourceAttachId;

    @ApiModelProperty("纸质发票信息")
    @Column(name = "bill_context")
    private String billContext;

    @ApiModelProperty("纸质附件ID")
    @Column(name = "attach_id")
    private String attachId;

    @ApiModelProperty("对比结果")
    @Column(name = "compared_status")
    private String comparedStatus;

    @ApiModelProperty("发票号码")
    @Column(name = "ecs_bill_no")
    private String ecsBillNo;

    @ApiModelProperty("ECS系统单据ID")
    @Column(name = "ecs_id")
    private String ecsId;

    @ApiModelProperty("PCX系统单据ID")
    @Column(name = "pcx_bill_id")
    private String pcxBillId;

    @ApiModelProperty("PCX系统单据编号")
    @Column(name = "pcx_bill_no")
    private String pcxBillNo;

    @ApiModelProperty("发票业务类型")
    @Column(name = "ecs_bill_type")
    private String ecsBillType;

    @ApiModelProperty("发票的类型")
    @Column(name = "ecs_bill_kind")
    private String ecsBillKind;

    @ApiModelProperty("发票金额")
    @Column(name = "ecs_bill_amt")
    private String ecsBillAmt;

    @ApiModelProperty("发票明细")
    @Column(name = "ecs_summary")
    private String ecsSummary;

    @ApiModelProperty("批次号")
    @Column(name = "batch_id")
    private String batchId;

    @Column(name = "compared_summary")
    @ApiModelProperty(value = "对比结果备注")
    private String comparedSummary;
}