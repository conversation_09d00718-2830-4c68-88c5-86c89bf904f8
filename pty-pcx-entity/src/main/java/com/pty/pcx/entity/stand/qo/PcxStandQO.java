package com.pty.pcx.entity.stand.qo;

import com.pty.pcx.entity.stand.PcxStandKey;
import com.pty.pcx.entity.stand.vo.PcxStandConditionVO;
import com.pty.pcx.entity.stand.vo.PcxStandValueVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PcxStandQO extends PcxStandKey {

    /**
     *
     */
    private List<PcxStandConditionVO> conditionList;

    /**
     *
     */
    private List<PcxStandValueVO> standValueList;

    @ApiModelProperty("人员编码")
    private String userCode;
    @ApiModelProperty("人员姓名")
    private String userName;
}
