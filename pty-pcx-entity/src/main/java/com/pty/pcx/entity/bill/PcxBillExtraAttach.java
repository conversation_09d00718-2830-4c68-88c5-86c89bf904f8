package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 额外补助附件(PcxBillExtraAttach)实体类
 *
 * <AUTHOR>
 * @since 2025-05-07 07:25:34
 */
@Data
@Entity
@Table(schema = "pcx_bill_extra_attach")
public class PcxBillExtraAttach implements Serializable {
    private static final long serialVersionUID = 829255696295359034L;

    @Id
	@ApiModelProperty("$column.comment")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("报销单id")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("额外类型")
    @Column(name = "extra_type")
    private Integer extraType;
    
	@ApiModelProperty("职员编码")
    @Column(name = "emp_code")
    private String empCode;
    
	@ApiModelProperty("附件编码")
    @Column(name = "file_id")
    private String fileId;
    
	@ApiModelProperty("附件名称")
    @Column(name = "file_name")
    private String fileName;
    
}

