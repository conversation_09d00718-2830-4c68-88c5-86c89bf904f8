package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 报销单外部人员(PcxBillOuterEmp)实体类
 *
 * <AUTHOR>
 * @since 2025-02-12 10:02:14
 */
@Data
@Entity
@Table(schema = "pcx_bill_outer_emp")
public class PcxBillOuterEmp implements Serializable {
    private static final long serialVersionUID = -56400014541459001L;

    @Id
	@ApiModelProperty("主键")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据编码")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("人员编码")
    @Column(name = "emp_code")
    private String empCode;
    
	@ApiModelProperty("人员名称")
    @Column(name = "emp_name")
    private String empName;
    
	@ApiModelProperty("内外部")
    @Column(name = "emp_type")
    private String empType;
    
	@ApiModelProperty("费控级别")
    @Column(name = "bud_level")
    private String budLevel;

    @ApiModelProperty("是否有补助")
    @Column(name = "is_subsidy")
    private Integer isSubsidy;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
}

