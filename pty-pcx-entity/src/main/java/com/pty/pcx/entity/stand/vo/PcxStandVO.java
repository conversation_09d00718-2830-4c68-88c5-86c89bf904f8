package com.pty.pcx.entity.stand.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.List;

/**
 * 支出标准要素定义表(PcxStandKey)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:44:25
 */
@Data
public class PcxStandVO {

    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("费用类型编码")
    private String expenseTypeCode;

    @ApiModelProperty("费用类型名称")
    private String expenseTypeName;

    @ApiModelProperty("标准代码(后面关联筛选条件表、明细表)")
    private String standCode;

    @ApiModelProperty("标准名称")
    private String standName;

    @ApiModelProperty("优先级")
    private Integer seq;

    @ApiModelProperty("行要素编码")
    private String rowKeyCode;

    @ApiModelProperty("行要素名称")
    private String rowKeyName;

    @ApiModelProperty("行要素值编码")
    private String rowValueCode;

    @ApiModelProperty("行要素值名称")
    private String rowValueName;

    @ApiModelProperty("列要素编码")
    private String columnKeyCode;

    @ApiModelProperty("列要素名称")
    private String columnKeyName;

    @ApiModelProperty("列要素值编码")
    private String columnValueCode;

    @ApiModelProperty("列要素值名称")
    private String columnValueName;

    @ApiModelProperty("单位")
    private String agyCode;

    @ApiModelProperty("年度")
    private String fiscal;

    @ApiModelProperty("区划")
    private String mofDivCode;

    @ApiModelProperty("租户")
    private String tenantId;

    @ApiModelProperty("生效日期")
    private String effectiveDate;

    @ApiModelProperty("取值来源")
    private String valueSource;

    @ApiModelProperty("值表单类型")
    @Column(name = "value_editor_code")
    private String valueEditorCode;

    /**
     *
     */
    private List<PcxStandConditionVO> conditionList;

    /**
     *
     */
    private List<PcxStandValueVO> standValueList;

    @ApiModelProperty("人员编码")
    private String userCode;
    @ApiModelProperty("人员姓名")
    private String userName;

    private List<NameAndCode> rowValueList = new ArrayList<>();
    private List<NameAndCode> columnValueList = new ArrayList<>();

    private List<CombineColumn> combineColumnList = new ArrayList<>();
    @AllArgsConstructor
    @Data
    public static class NameAndCode{
        private String name;
        private String code;
    }

    @Data
    public static class CombineColumn{
        private String name;
        private List<CombineItem> children = new ArrayList<>();

    }
    @Data
    @AllArgsConstructor
    public static class CombineItem{
        private String code;
        private String name;
        private String editorCode;
        private List<NameAndCode> valueList = new ArrayList<>();
    }

}

