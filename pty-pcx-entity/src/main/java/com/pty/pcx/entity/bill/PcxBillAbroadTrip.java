package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 出国费行程表(PcxBillAbroadTrip)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
@Entity
@Table(name = "pcx_bill_abroad_trip")
public class PcxBillAbroadTrip implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty("关联表主键Id")
    @Column(name = "id", nullable = false, length = 50)
    private String id;

    @ApiModelProperty("行程关联费用明细ID")
    @Column(name = "trip_expense_id", length = 50)
    private String tripExpenseId;

    @ApiModelProperty("报销单主键")
    @Column(name = "bill_id", length = 50)
    private String billId;

    @ApiModelProperty("开始时间")
    @Column(name = "start_time", length = 20)
    private String startTime;

    @ApiModelProperty("结束时间")
    @Column(name = "end_time", length = 20)
    private String endTime;

    @ApiModelProperty("行程人编码（多人逗号隔开）")
    @Column(name = "emp_code", length = 500)
    private String empCode;

    @ApiModelProperty("行程人（多人逗号隔开）")
    @Column(name = "emp_name", length = 500)
    private String empName;

    @ApiModelProperty("单位名称")
    @Column(name = "agy_code", length = 30)
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal", length = 4)
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code", length = 9)
    private String mofDivCode;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id", length = 50)
    private String tenantId;

    @ApiModelProperty("行程类型，traffic，hotel")
    @Column(name = "trip_type", length = 10)
    private String tripType;

    @ApiModelProperty("外方提供伙食")
    @Column(name = "provide_food")
    private String provideFood;

    @ApiModelProperty("外方提供交通接待")
    @Column(name = "provide_tool")
    private String provideTool;

    @ApiModelProperty("币种")
    @Column(name = "currency", length = 20)
    private String currency;

    @ApiModelProperty("乘坐外航航班")
    @Column(name = "foreign_air", length = 50)
    private String foreignAir;

    @ApiModelProperty("乘坐外航航班原由")
    @Column(name = "foreign_air_reason", length = 256)
    private String foreignAirReason;

    @ApiModelProperty("补充说明")
    @Column(name = "remark", length = 256)
    private String remark;

    @ApiModelProperty("出发地点编码")
    @Column(name = "start_place_code", length = 30)
    private String startPlaceCode;

    @ApiModelProperty("出发地点名称")
    @Column(name = "start_place_name", length = 50)
    private String startPlaceName;

    @ApiModelProperty("到达地点编码")
    @Column(name = "end_place_code", length = 30)
    private String endPlaceCode;

    @ApiModelProperty("到达地点名称")
    @Column(name = "end_place_name", length = 50)
    private String endPlaceName;

    @ApiModelProperty("是否删除 1己删除 2末删除")
    @Column(name = "is_deleted_number")
    private Integer isDeletedNumber;

    @TableField(exist = false)
    private String currencyName;
    // 辅助字段外方提供伙食含义名
    @TableField(exist = false)
    private String provideFoodName;
    // 辅助字段外方提供交通接待含义名
    @TableField(exist = false)
    private String provideToolName;
}