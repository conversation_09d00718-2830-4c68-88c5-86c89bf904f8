package com.pty.pcx.entity.print;

import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.vo.workflow2.ProcessHistoryVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PcxPrintResult  implements Serializable {

    private static final long serialVersionUID = 1631072202743305252L;

    private List<Map<String,Object>> rows;

    private List<Map<String,Object>> specificity;

    private List<Map<String,Object>> processNode;
}
