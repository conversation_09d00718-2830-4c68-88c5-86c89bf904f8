package com.pty.pcx.entity.stand;

import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 劳务标准税率表(PcxStandTaxRate)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 11:44:24
 */
@Data
@Entity
@Table(schema = "pcx_stand_tax_rate")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PcxStandTaxRate implements Serializable {
    private static final long serialVersionUID = -89734737249956611L;

    @Id
    @ApiModelProperty("税率id")
    @NotBlank(message = "税率id不能为空", groups = {Update.class})
    @Column(name = "id")
    private String id;

    @ApiModelProperty("金额下限")
    @Column(name = "min_amt")
    private BigDecimal minAmt;

    @ApiModelProperty("金额上限")
    @Column(name = "max_amt")
    private BigDecimal maxAmt;

    @ApiModelProperty("扣除费用")
    @Column(name = "sub_expense")
    private BigDecimal subExpense;

    @ApiModelProperty("扣除比率")
    @Column(name = "sub_rate")
    private BigDecimal subRate;

    @ApiModelProperty("税率")
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @ApiModelProperty("速算扣除数")
    @Column(name = "sub_num")
    private BigDecimal subNum;

    @ApiModelProperty("税率类型名称 (01、外部劳务费税率； 02、工资薪金税率；03、外籍人员劳务费税率；04、实习学生劳务费税率)")
    @Column(name = "rate_type_name")
    private String rateTypeName;

    @ApiModelProperty("税率类型 (01、外部劳务费税率； 02、工资薪金税率；03、外籍人员劳务费税率；04、实习学生劳务费税率)")
    @Column(name = "rate_type_code")
    @NotBlank(message = "税率类型不能为空", groups = {Query.class, Update.class})
    private String rateTypeCode;

    @ApiModelProperty("单位")
    @Column(name = "agy_code")
    @NotBlank(message = "agyCode不能为空", groups = {Query.class,Update.class})
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    @NotBlank(message = "fiscal不能为空", groups = {Query.class,Update.class})
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    @NotBlank(message = "mofDivCode不能为空", groups = {Query.class,Update.class})
    private String mofDivCode;

    @ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("是否启用")
    @Column(name = "is_enabled")
    private Integer isEnabled;

    @ApiModelProperty("生效日期")
    @Column(name = "effective_date")
    private String effectiveDate;

}

