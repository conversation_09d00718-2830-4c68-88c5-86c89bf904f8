package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 单据费用表_出国费(PcxBillExpAbroad)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04 17:42:51
 */
@Data
@Entity
@Table(name = "pcx_bill_exp_abroad")
@EqualsAndHashCode(callSuper = true)
public class PcxBillExpAbroad extends PcxBillExpBase implements Serializable {
    private static final long serialVersionUID = 914453979220219894L;

    @Id
    @ApiModelProperty("费用信息主键ID")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("事项主键ID")
    @Column(name = "bill_id")
    private String billId;

    @ApiModelProperty("费用类型代码")
    @Column(name = "expense_code")
    private String expenseCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_name")
    private String expenseName;

    @ApiModelProperty("费用承担部门编码")
    @Column(name = "department_code")
    private String departmentCode;

    @ApiModelProperty("费用承担部门")
    @Column(name = "department_name")
    private String departmentName;

    @ApiModelProperty("费用录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;

    @ApiModelProperty("费用核定金额")
    @Column(name = "check_amt")
    private BigDecimal checkAmt;

    @ApiModelProperty("组团名称")
    @Column(name = "abroad_team_name")
    private String abroadTeamName;

    @ApiModelProperty("组团类型编码")
    @Column(name = "abroad_team_type")
    private String abroadTeamType;

    @ApiModelProperty("组团类型名称")
    @Column(name = "abroad_team_type_name")
    private String abroadTeamTypeName;

    @ApiModelProperty("是否有计划")
    @Column(name = "is_plan")
    private Integer isPlan;

    @ApiModelProperty("计划类型编码")
    @Column(name = "plan_code")
    private String planCode;

    @ApiModelProperty("计划类型名称")
    @Column(name = "plan_name")
    private String planName;

    @ApiModelProperty("出访开始日期")
    @Column(name = "abroad_start_date")
    private String abroadStartDate;

    @ApiModelProperty("出访结束日期")
    @Column(name = "abroad_finish_date")
    private String abroadFinishDate;

    @ApiModelProperty("预计开始月份")
    @Column(name = "pre_start_month")
    private String preStartMonth;

    @ApiModelProperty("预计结束月份")
    @Column(name = "pre_finish_month")
    private String preFinishMonth;

    @ApiModelProperty("预计季度")
    @Column(name = "pre_quarter")
    private String preQuarter;

    @ApiModelProperty("备注")
    @Column(name = "remarks")
    private String remarks;

    @ApiModelProperty("出国任务")
    @Column(name = "abroad_job")
    private String abroadJob;

    @ApiModelProperty("出国天数")
    @Column(name = "abroad_visit_days")
    private Integer abroadVisitDays;

    @ApiModelProperty("组团人数")
    @Column(name = "abroad_people_num")
    private Integer abroadPeopleNum;

    @ApiModelProperty("批准文号")
    @Column(name = "abroad_approval_num")
    private String abroadApprovalNum;

    @ApiModelProperty("所需经费")
    @Column(name = "need_amt")
    private BigDecimal needAmt;

    @ApiModelProperty("组团单位")
    @Column(name = "abroad_team_unit")
    private String abroadTeamUnit;

    @ApiModelProperty("出访国别")
    @Column(name = "abroad_visit_country")
    private String abroadVisitCountry;

    @ApiModelProperty("出访路线")
    @Column(name = "abroad_visit_route")
    private String abroadVisitRoute;

    @ApiModelProperty("出访获得成效")
    @Column(name = "abroad_achievement")
    private String abroadAchievement;

    @ApiModelProperty("使用外事费人数")
    @Column(name = "abroad_affairs_num")
    private Integer abroadAffairsNum;

    @ApiModelProperty("组团人员")
    @Column(name = "abroad_team_personnel")
    private String abroadTeamPersonnel;

    @ApiModelProperty("其他单位组织")
    @Column(name = "abroad_other_units")
    private String abroadOtherUnits;

    @ApiModelProperty("团长名称")
    @Column(name = "abroad_leader_name")
    private String abroadLeaderName;

    @ApiModelProperty("团长级别")
    @Column(name = "abroad_leader_level")
    private String abroadLeaderLevel;

    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("自定义01字段")
    @Column(name = "field01")
    private String field01;

    @ApiModelProperty("自定义02字段")
    @Column(name = "field02")
    private String field02;

    @ApiModelProperty("自定义03字段")
    @Column(name = "field03")
    private String field03;

    @ApiModelProperty("自定义04字段")
    @Column(name = "field04")
    private String field04;

    @ApiModelProperty("自定义05字段")
    @Column(name = "field05")
    private String field05;

    @ApiModelProperty("自定义06字段")
    @Column(name = "field06")
    private String field06;

    @ApiModelProperty("自定义07字段")
    @Column(name = "field07")
    private String field07;

    @ApiModelProperty("自定义08字段")
    @Column(name = "field08")
    private String field08;

    @ApiModelProperty("自定义09字段")
    @Column(name = "field09")
    private String field09;

    @ApiModelProperty("自定义010字段")
    @Column(name = "field10")
    private String field10;

    @ApiModelProperty("自定义01字段code")
    @Column(name = "acitem01_code")
    private String acitem01Code;

    @ApiModelProperty("自定义01字段name")
    @Column(name = "acitem01_name")
    private String acitem01Name;

    @ApiModelProperty("自定义02字段code")
    @Column(name = "acitem02_code")
    private String acitem02Code;

    @ApiModelProperty("自定义02字段name")
    @Column(name = "acitem02_name")
    private String acitem02Name;

    @ApiModelProperty("自定义03字段code")
    @Column(name = "acitem03_code")
    private String acitem03Code;

    @ApiModelProperty("自定义03字段name")
    @Column(name = "acitem03_name")
    private String acitem03Name;

    @ApiModelProperty("自定义04字段code")
    @Column(name = "acitem04_code")
    private String acitem04Code;

    @ApiModelProperty("自定义04字段name")
    @Column(name = "acitem04_name")
    private String acitem04Name;

    @ApiModelProperty("自定义05字段code")
    @Column(name = "acitem05_code")
    private String acitem05Code;

    @ApiModelProperty("自定义05字段name")
    @Column(name = "acitem05_name")
    private String acitem05Name;

    @ApiModelProperty("自定义06字段code")
    @Column(name = "acitem06_code")
    private String acitem06Code;

    @ApiModelProperty("自定义06字段name")
    @Column(name = "acitem06_name")
    private String acitem06Name;

    @ApiModelProperty("自定义07字段code")
    @Column(name = "acitem07_code")
    private String acitem07Code;

    @ApiModelProperty("自定义07字段name")
    @Column(name = "acitem07_name")
    private String acitem07Name;

    @ApiModelProperty("自定义08字段code")
    @Column(name = "acitem08_code")
    private String acitem08Code;

    @ApiModelProperty("自定义08字段name")
    @Column(name = "acitem08_name")
    private String acitem08Name;

    @ApiModelProperty("自定义09字段code")
    @Column(name = "acitem09_code")
    private String acitem09Code;

    @ApiModelProperty("自定义09字段name")
    @Column(name = "acitem09_name")
    private String acitem09Name;

    @ApiModelProperty("自定义10字段code")
    @Column(name = "acitem10_code")
    private String acitem10Code;

    @ApiModelProperty("自定义10字段name")
    @Column(name = "acitem10_name")
    private String acitem10Name;

    @ApiModelProperty("审核单位")
    @Column(name = "audit_agy")
    private String auditAgy;

    @ApiModelProperty("审核依据")
    @Column(name = "audit_reason")
    private String auditReason;

    @ApiModelProperty("出访目标和必要性")
    @Column(name = "target_necessity")
    private String targetNecessity;

    @ApiModelProperty("出国计划")
    @Column(name = "abroad_plan")
    private String abroadPlan;

    @ApiModelProperty("时间和国别是否符合规定")
    @Column(name = "time_country_check")
    private String timeCountryCheck;

    @ApiModelProperty("线路是否符合规定")
    @Column(name = "route_check")
    private String routeCheck;

    @ApiModelProperty("团组人数是否符合规定")
    @Column(name = "people_num_check")
    private String peopleNumCheck;

    @ApiModelProperty("其他事项")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty("是否列入年度预算")
    @Column(name = "list_year_budget")
    private String listYearBudget;

    @ApiModelProperty("需事先报批的支出事项")
    @Column(name = "before_spend")
    private String beforeSpend;

    @ApiModelProperty("创建人")
    @Column(name = "creator")
    private String creator;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private String createTime;

    @ApiModelProperty("创建人姓名")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;

    @ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

    @ApiModelProperty("是否删除 1己删除 2末删除")
    @Column(name = "is_deleted_number")
    private Integer isDeletedNumber;

    //TODO 出国费费用明细 （定义成object 返回时候结构有区别为json）
    @TableField(exist = false)
    private Object pcxBillExpDetailAbroad;

    //TODO 出访国别行程信息
    @TableField(exist = false)
    private List<PcxBillAbroadTrip> pcxBillAbroadTrip;

}