package com.pty.pcx.entity.payTest;

import com.pty.pub.common.anno.Desc;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * (PcxBankAccountTest)实体类
 *
 * <AUTHOR>
 * @since 2025-01-20 10:53:54
 */
@Data
@Entity
@Table(name = "pcx_bank_account_test")
@Desc("银行支付测试账号")
public class PcxBankAccountTest implements Serializable {
    private static final long serialVersionUID = -36437523903806830L;
/**
     * 主键id
     */
    private String id;
/**
     * 测试银行类型
     */
    private String bankType;
    /**
     * 是否单位账户
     */
    private String isAgyAccount;
    /**
     * 银行账号
     */
    private String accountNo;
    /**
     * 银行户名
     */
    private String accountName;
/**
     * 卡片类型
     */
    private String cardType;
/**
     * 银行代码
     */
    private String bankCode;
/**
     * 银行名称
     */
    private String bankName;
/**
     * 银行网点行号
     */
    private String bankNodeNo;
/**
     * 银行网点代码
     */
    private String bankNodeCode;
/**
     * 银行网点名称
     */
    private String bankNodeName;
/**
     * 城市代码
     */
    private String cityCode;
/**
     * 城市名称
     */
    private String cityName;
/**
     *  备注说明
     */
    private String remark;
/**
     * 单位编码
     */
    private String agyCode;
/**
     * 年度
     */
    private String fiscal;
/**
     * 区划
     */
    private String mofDivCode;
/**
     * 租户ID
     */
    private String tenantId;
/**
     * 创建时间
     */
    private String createTime;
/**
     * 创建人名称
     */
    private String creatorName;

}

