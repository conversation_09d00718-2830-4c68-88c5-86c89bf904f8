package com.pty.pcx.entity.bill;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单据结算信息(PcxBillSettlementInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-11-29 11:08:39
 */
@Data
@Entity
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "pcx_bill_settlement")
public class PcxBillSettlement implements Serializable {
    private static final long serialVersionUID = 984169860335371711L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据主键ID")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("结算方式类型")
    @Column(name = "settlement_type")
    private String settlementType;

    @ApiModelProperty("结算方式名称")
    @Column(name = "settlement_name")
    private String settlementName;

	@ApiModelProperty("收款方账号")
    @Column(name = "payee_acc_no")
    private String payeeAccNo;
    
	@ApiModelProperty("收款方编码")
    @Column(name = "payee_acc_code")
    private String payeeAccCode;
    
	@ApiModelProperty("收款方名称")
    @Column(name = "payee_acc_name")
    private String payeeAccName;
    
	@ApiModelProperty("收款方开户行")
    @Column(name = "payee_bank_name")
    private String payeeBankName;

	@ApiModelProperty("对公/对私(1：个人对私 0：单位对公)")
    @Column(name = "pay_type")
    private String payType;
    
	@ApiModelProperty("费用录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;
    
	@ApiModelProperty("费用核定金额")
    @Column(name = "check_amt")
    private BigDecimal checkAmt;
    
	@ApiModelProperty("备注说明")
    @Column(name = "summary")
    private String summary;
    
	@ApiModelProperty("排序")
    @Column(name = "seq")
    private Integer seq;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("是否劳务")
    @Column(name = "isLabour")
    private String isLabour;

    @ApiModelProperty("代缴税金")
    @Column(name = "taxAmt")
    private BigDecimal taxAmt;

    @ApiModelProperty("结算方式唯一键md5值")
    @Column(name = "settlement_uk")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String settlementUk;

}
