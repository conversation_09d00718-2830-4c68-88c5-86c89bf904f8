package com.pty.pcx.entity.stand.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支出标准要素定义表(PcxStandKey)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:44:25
 */
@Data
public class PcxStandQueryQO {

    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("费用类型编码")
    private String expenseTypeCode;

    @ApiModelProperty("单位")
    private String agyCode;

    @ApiModelProperty("年度")
    private String fiscal;

    @ApiModelProperty("区划")
    private String mofDivCode;

    @ApiModelProperty("标准编码")
    private String standCode;

//    @ApiModelProperty("租户")
//    private String tenantId;
}

