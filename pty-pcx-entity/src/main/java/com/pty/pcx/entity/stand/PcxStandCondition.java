package com.pty.pcx.entity.stand;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 支出标准筛选条件表(PcxStandCondition)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:44:23
 */
@Data
@Entity
@Table(schema = "pcx_stand_condition")
public class PcxStandCondition implements Serializable {
    private static final long serialVersionUID = 661505283349066750L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("标准代码（唯一要素关联）")
    @Column(name = "stand_code")
    private String standCode;
    
	@ApiModelProperty("筛选条件要素编码")
    @Column(name = "cond_key_code")
    private String condKeyCode;
    
	@ApiModelProperty("筛选条件要素名称")
    @Column(name = "cond_key_name")
    private String condKeyName;
    
	@ApiModelProperty("筛选条件要素值编码")
    @Column(name = "cond_value_code")
    private String condValueCode;
    
	@ApiModelProperty("筛选条件要素值名称")
    @Column(name = "cond_value_name")
    private String condValueName;
    
	@ApiModelProperty("操作符（1:包含  2:大于等于  3:大于  4:等于）")
    @Column(name = "operator")
    private String operator;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
}

