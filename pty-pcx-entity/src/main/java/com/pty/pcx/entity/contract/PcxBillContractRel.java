package com.pty.pcx.entity.contract;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报销单关联合同付款计划(PcxBillContractRel)实体类
 *
 * <AUTHOR>
 * @since 2025-01-16 16:01:12
 */
@Data
@Entity
@Table(schema = "pcx_bill_contract_rel")
public class PcxBillContractRel implements Serializable {
    private static final long serialVersionUID = -74278820084569014L;

    @Id
	@ApiModelProperty("主键")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("报销单id")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("合同id")
    @Column(name = "contract__id")
    private String contractId;
    
	@ApiModelProperty("合同编号")
    @Column(name = "contract_no")
    private String contractNo;
    
	@ApiModelProperty("合同名称")
    @Column(name = "contract_name")
    private String contractName;
    
	@ApiModelProperty("合同金额")
    @Column(name = "contract_amt")
    private BigDecimal contractAmt;
    
	@ApiModelProperty("甲方")
    @Column(name = "party_a_name")
    private String partyAName;
    
	@ApiModelProperty("乙方")
    @Column(name = "party_b_name")
    private String partyBName;

    @ApiModelProperty("签订日期")
    @Column(name = "sign_date")
    private String signDate;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("付款计划id")
    @Column(name = "plan_id")
    private String planId;
    
	@ApiModelProperty("付款计划名称")
    @Column(name = "plan_name")
    private String planName;
    
	@ApiModelProperty("计划内容")
    @Column(name = "plan_content")
    private String planContent;
    
	@ApiModelProperty("付款比例")
    @Column(name = "plan_pay_radio")
    private BigDecimal planPayRadio;
    
	@ApiModelProperty("付款金额")
    @Column(name = "plan_pay_amt")
    private BigDecimal planPayAmt;
    
	@ApiModelProperty("计划付款日期")
    @Column(name = "plan_pay_date")
    private String planPayDate;
    
	@ApiModelProperty("执行条件")
    @Column(name = "plan_pay_condition")
    private String planPayCondition;

    @ApiModelProperty("本次付款金额")
    @Column(name = "pay_amt")
    private BigDecimal payAmt;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
}

