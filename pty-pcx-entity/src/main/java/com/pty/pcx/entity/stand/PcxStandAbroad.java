package com.pty.pcx.entity.stand;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支出标准-出国标准(PcxStandAbroad)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 14:25:43
 */
@Data
@Entity
@Table(schema = "pcx_stand_abroad")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PcxStandAbroad implements Serializable {
    private static final long serialVersionUID = -87853389047840847L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("国家")
    @Column(name = "country_code")
    private String countryCode;
    
	@ApiModelProperty("国家名称")
    @Column(name = "country_name")
    private String countryName;
    
	@ApiModelProperty("城市编码")
    @Column(name = "city_code")
    private String cityCode;
    
	@ApiModelProperty("城市名称")
    @Column(name = "city_name")
    private String cityName;
    
	@ApiModelProperty("住宿费标准金额")
    @Column(name = "stay_amt")
    private BigDecimal stayAmt;
    
	@ApiModelProperty("伙食费标准金额")
    @Column(name = "food_amt")
    private BigDecimal foodAmt;
    
	@ApiModelProperty("杂费标准金额")
    @Column(name = "other_amt")
    private BigDecimal otherAmt;
    
	@ApiModelProperty("币种code")
    @Column(name = "currency_code")
    private String currencyCode;
    
	@ApiModelProperty("币种名称")
    @Column(name = "currency_name")
    private String currencyName;
    
	@ApiModelProperty("排序字段")
    @Column(name = "seq")
    private Integer seq;
    
	@ApiModelProperty("所属洲代码")
    @Column(name = "continent_code")
    private String continentCode;
    
	@ApiModelProperty("所属洲名称")
    @Column(name = "continent_name")
    private String continentName;
    
	@ApiModelProperty("单位")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户")
    @Column(name = "tenant_id")
    private String tenantId;
    
	@ApiModelProperty("是否启用")
    @Column(name = "is_enabled")
    private Integer isEnabled;
    
	@ApiModelProperty("生效日期")
    @Column(name = "effective_date")
    private String effectiveDate;

    @JsonIgnore
    private List<String> ids;

    @JsonIgnore
    private List<String> cityCodes;
    
}

