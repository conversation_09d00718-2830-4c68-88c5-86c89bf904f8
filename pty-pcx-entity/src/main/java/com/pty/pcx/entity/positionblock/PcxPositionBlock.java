package com.pty.pcx.entity.positionblock;

import com.pty.pcx.common.valid.Query;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

@Entity
@Table(name = "pcx_position_block")
@Data
@NoArgsConstructor
public class PcxPositionBlock {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 单据功能编码（例如：申请单、报销单）
     */
    @Column(name = "bill_func_code")
    private String billFuncCode;

    /**
     * 岗位编码（例如：填单、审核、确定预算）
     */
    @Column(name = "position_code")
    private String positionCode;

    /**
     * 岗位样式内容（JSON串）
     */
    @Column(name = "position_context")
    private String positionContext;

    /**
     * 样式分类编码
     */
    @Column(name = "classify_code")
    private String classifyCode;

    /**
     * 样式分类名称
     */
    @Column(name = "classify_name")
    private String classifyName;

    /**
     * 年度
     */
    @Column(name = "fiscal")
    @NotBlank(message = "年度不能为空", groups = {Query.class})
    private String fiscal;

    /**
     * 序号
     */
    @Column(name = "seq")
    private Integer seq;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 单位编码
     */
    @Column(name = "agy_code")
    @NotBlank(message = "单位编码不能为空", groups = {Query.class})
    private String agyCode;

    /**
     * 区划编码
     */
    @Column(name = "mof_div_code")
    @NotBlank(message = "区划不能为空", groups = {Query.class})
    private String mofDivCode;

    /**
     * 修改人编码
     */
    @Column(name = "modifier")
    private String modifier;

    /**
     * 修改人名称
     */
    @Column(name = "modifier_name")
    private String modifierName;

    /**
     * 修改时间
     */
    @Column(name = "modified_time")
    private String modifiedTime;

    /**
     * 创建人编码
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 创建人名称
     */
    @Column(name = "creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private String createdTime;

    /**
     * 版本编码
     */
    @Column(name = "ver")
    private String ver;

    /***
     * 块儿的区域; left right;
     */
    @Column(name = "area")
    private String area;

    /****
     * 块儿前端展示名称
     */
    @Column(name = "show_name")
    private String showName;
}
