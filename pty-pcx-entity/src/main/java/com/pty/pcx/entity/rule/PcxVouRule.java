package com.pty.pcx.entity.rule;

import com.pty.pub.common.anno.Desc;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:记账规则
 * @date: 2025/1/25
 */
@Data
@Table(name = "pcx_vou_rule")
public class PcxVouRule implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id")
  @Desc("id")
  private String id;

  @Column(name = "fiscal")
  @Desc("年度")
  private String fiscal;

  @Column(name = "agy_code")
  @Desc("单位")
  private String agyCode;

  @Column(name = "tenant_id")
  @Desc("租户id")
  private String tenantId;

  @Column(name = "expense_code")
  @Desc("费用类型")
  private String expenseCode;

  @Column(name = "expense_name")
  @Desc("费用类型名称")
  private String expenseName;

  @Column(name = "expense_detail_code")
  @Desc("费用明细code")
  private String expenseDetailCode;

  @Column(name = "expense_detail_name")
  @Desc("费用明细")
  private String expenseDetailName;

  @Column(name = "settlement_type")
  @Desc("结算方式code")
  private String settlementType;

  @Column(name = "settlement_name")
  @Desc("结算方式")
  private String settlementName;

  @Column(name = "pay_account_type_code")
  @Desc("付款账户类型code")
  private String payAccountTypeCode;

  @Column(name = "pay_account_type_name")
  @Desc("付款账户类型")
  private String payAccountTypeName;

  @Column(name = "ecs_biz_type_code")
  @Desc("电子凭证类型code")
  private String ecsBizTypeCode;

  @Column(name = "ecs_biz_type_name")
  @Desc("电子凭证类型")
  private String ecsBizTypeName;

  @Column(name = "bill_func_code")
  @Desc("数据所属单据块")
  //(expense:费用明细,loan:借款,repay:还款)
  private String billFuncCode;

  @Column(name = "bill_func_name")
  @Desc("数据所属单据块名称")
  private String billFuncName;

  @Column(name = "account_aco")
  @Desc("会计科目")
  private String accountAco;

  @Column(name = "account_aco_name")
  @Desc("会计科目名称")
  private String accountAcoName;

  //1:借，2:贷
  @Column(name = "dr_cr")
  @Desc("借贷方类型")
  private Integer drCr;

  //dr:借方分录，taxAmount:税额(分组使用)，cr:借方分录
  @Column(name = "dr_type")
  @Desc("借方分录类型")
  private String drType;

  @Column(name = "ord")
  @Desc("排序")
  private Integer ord;

  @Column(name = "acb_code")
  @Desc("账套编码")
  private String acbCode;

  @Column(name = "acb_name")
  @Desc("账套名称")
  private String acbName;

  @Column(name = "acs_code")
  @Desc("科目体系")
  private String acsCode;

  @Column(name = "aca_code")
  @Desc("会计体系,fac：财务会计；bac：预算会计")
  private String acaCode;

  @Column(name = "mof_div_code")
  @Desc("区划")
  private String mofDivCode;
  /**
   * json存储
   * {"claimantName":"报销人","departmentName":"部门","anyString_0":"测试","anyString_1":"啊"}
   */
  @Column(name="summary_rule_content")
  @Desc("摘要规则内容")
  private String summaryRuleContent;

  @Column(name="is_merge_by_fac_dr")
  @Desc("财务会计借方合并，合并为1,不合并为0")
  private Integer isMergeByFacDr;

  @Column(name="is_merge_by_fac_cr")
  @Desc("财务会计贷方合并，合并为1,不合并为0")
  private Integer isMergeByFacCr;

  @Column(name="is_merge_by_bac_dr")
  @Desc("预算会计借方合并，合并为1,不合并为0")
  private Integer isMergeByBacDr;

  @Column(name="is_merge_by_bac_cr")
  @Desc("预算会计贷方合并，合并为1,不合并为0")
  private Integer isMergeByBacCr;

  @Column(name="is_merge_by_summary")
  @Desc("摘要相同合并，合并为1,不合并为0")
  private Integer isMergeBySummary;

  @Column(name = "atom_code")
  @Desc("条件项")
  private String atomCode;

  @Column(name = "atom_name")
  @Desc("条件项名称")
  private String atomName;

  @Column(name = "field_code")
  @Desc("条件项对应字段")
  private String fieldCode;

  @Column(name = "field_name")
  @Desc("条件项对应字段名")
  private String fieldName;

  @Column(name = "acitem01_code")
  @Desc("扩展要素01")
  private String acitem01Code;

  @Column(name = "acitem01_name")
  private String acitem01Name;

  @Column(name = "acitem02_code")
  @Desc("扩展要素02")
  private String acitem02Code;

  @Column(name = "acitem02_name")
  private String acitem02Name;

  @Column(name = "acitem03_code")
  @Desc("扩展要素03")
  private String acitem03Code;

  @Column(name = "acitem03_name")
  private String acitem03Name;

  @Column(name = "acitem04_code")
  @Desc("扩展要素04")
  private String acitem04Code;

  @Column(name = "acitem04_name")
  private String acitem04Name;

  @Column(name = "acitem05_code")
  @Desc("扩展要素05")
  private String acitem05Code;

  @Column(name = "acitem05_name")
  private String acitem05Name;

  @Column(name = "acitem06_code")
  @Desc("扩展要素06")
  private String acitem06Code;

  @Column(name = "acitem06_name")
  private String acitem06Name;

  @Column(name = "acitem07_code")
  @Desc("扩展要素07")
  private String acitem07Code;

  @Column(name = "acitem07_name")
  private String acitem07Name;

  @Column(name = "acitem08_code")
  @Desc("扩展要素08")
  private String acitem08Code;

  @Column(name = "acitem08_name")
  private String acitem08Name;

  @Column(name = "acitem09_code")
  @Desc("扩展要素09")
  private String acitem09Code;

  @Column(name = "acitem09_name")
  private String acitem09Name;

  @Column(name = "acitem10_code")
  @Desc("扩展要素10")
  private String acitem10Code;

  @Column(name = "acitem10_name")
  private String acitem10Name;


}
