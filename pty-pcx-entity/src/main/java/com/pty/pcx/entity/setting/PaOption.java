package com.pty.pcx.entity.setting;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pa_option")
@NoArgsConstructor
public class PaOption implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "opt_id")
    private String optId;

    @Column(name = "sys_id")
    private String sysId;

    @Column(name = "fiscal")
    private int fiscal;

    @Column(name = "agy_code")
    private String agyCode;

    @Column(name = "acb_code")
    private String acbCode;

    @Column(name = "opt_code")
    private String optCode;

    @Column(name = "opt_name")
    private String optName;

    @Column(name = "opt_value")
    private String optValue;

    @Column(name = "opt_desc")
    private String optDesc;

    @Column(name = "is_visible")
    private Integer isVisible;

    @Column(name = "is_edit")
    private Integer isEdit;

    @Column(name = "atom_code")
    private String atomCode;

    @Column(name = "field_disptype")
    private String fieldDisptype;

    @Column(name = "group_name")
    private String groupName;

    @Column(name = "conmode_code")
    private Integer conmodeCode;

    @Column(name = "field_valueset_code")
    private String fieldValuesetCode;

    @Column(name = "adm_code")
    private String admCode;

    @Column(name = "is_super_control")
    private Integer isSuperControl;

    @Column(name = "ord_seq")
    private Integer ordSeq;

    @Column(name = "is_enable_setting")
    private Integer isEnableSetting;

    @Column(name = "setting_content")
    private String settingContent;

    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "mof_div_code")
    private String mofDivCode;
}