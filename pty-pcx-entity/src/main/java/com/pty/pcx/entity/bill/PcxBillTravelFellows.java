package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 差旅费同行人(PcxBillTravelFellows)实体类
 *
 * <AUTHOR>
 * @since 2024-11-30 12:35:57
 */
@Data
@Entity
@Table(schema = "pcx_bill_travel_fellows")
public class PcxBillTravelFellows implements Serializable {
    private static final long serialVersionUID = 785277360250548108L;

    @Id
	@ApiModelProperty("关联表主键Id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单号")
    @Column(name = "bill_id")
    private String billId;
    
	@ApiModelProperty("关联差旅费用ID")
    @Column(name = "expense_id")
    private String expenseId;
    
	@ApiModelProperty("同行人编码")
    @Column(name = "emp_code")
    @NotBlank(message = "同行人编码不能为空")
    private String empCode;
    
	@ApiModelProperty("同行人名称")
    @Column(name = "emp_name")
    private String empName;
    
	@ApiModelProperty("同行人类型")
    @Column(name = "emp_type")
    private String empType;
    
	@ApiModelProperty("费控级别")
    @Column(name = "bud_level")
    private String budLevel;
    
	@ApiModelProperty("是否领取补助")
    @Column(name = "is_subsidy")
    private Integer isSubsidy;
    
	@ApiModelProperty("单位名称")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;
    
}
