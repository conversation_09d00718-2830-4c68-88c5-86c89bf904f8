package com.pty.pcx.entity.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pcx_bas_exp_type")
@NoArgsConstructor
public class PcxBasExpType implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty("主键")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("费用类型代码")
    @Column(name = "expense_code")
    private String expenseCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_name")
    private String expenseName;

    @ApiModelProperty("父级代码")
    @Column(name = "parent_code")
    private String parentCode;

    @ApiModelProperty("末级费用类型代码(费用项需要)")
    @Column(name = "last_code")
    private String lastCode;

    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("是否费用项细化")
    @Column(name = "is_refine")
    private Integer isRefine;

    @ApiModelProperty("是否启用")
    @Column(name = "is_enabled")
    private Integer isEnabled;

    @ApiModelProperty("支出指南")
    @Column(name = "expend_guide")
    private String expendGuide;

    @ApiModelProperty("是否需要年初计划")
    @Column(name = "is_plan")
    private Integer isPlan;

    @ApiModelProperty("是否系统预置")
    @Column(name = "is_system")
    private Integer isSystem;

    @ApiModelProperty("是否为叶子节点")
    @Column(name = "is_leaf")
    private Integer isLeaf;

    @ApiModelProperty("是否占用预算标志位")
    @Column(name = "is_ctrl_balance")
    private Integer isCtrlBalance;

    @ApiModelProperty("申请途径编码")
    @Column(name = "apply_ctrl_code")
    private String applyCtrlCode;

    @ApiModelProperty("申请途径名称")
    @Column(name = "apply_ctrl_name")
    private String applyCtrlName;

    @ApiModelProperty("申请是否有用到费用明细")
    @Column(name = "is_apply_detail")
    private Integer isApplyDetail;

    @ApiModelProperty("是否需要申请单")
    @Column(name = "is_need_apply")
    private Integer isNeedApply;

    @ApiModelProperty("是否需要计划")
    @Column(name = "is_need_plan")
    private Integer isNeedPlan;

    @ApiModelProperty("申请(费用明细)控制级别")
    @Column(name = "apply_ctrl_level")
    private Integer applyCtrlLevel;

    @ApiModelProperty("描述、说明")
    @Column(name = "summary")
    private String summary;

    @ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;

    @ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

    @ApiModelProperty("创建人代码")
    @Column(name = "creator")
    private String creator;

    @ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("排序")
    @Column(name = "seq")
    private Integer seq;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("是否有标准")
    @Column(name = "is_has_stand")
    private Integer isHasStand;

    @ApiModelProperty("费用明细层级")
    @Column(name = "detail_level")
    private Integer detailLevel;
}