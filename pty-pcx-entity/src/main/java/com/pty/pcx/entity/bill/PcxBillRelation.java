package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单据关联表(PcxBillRelation)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25 16:21:00
 */
@Data
@Entity
@Table(schema = "pcx_bill_relation")
public class PcxBillRelation implements Serializable {
    private static final long serialVersionUID = -22105046050966211L;

    @Id
	@ApiModelProperty("关联表id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据id")
    @Column(name = "bill_id")
    private String billId;

    @ApiModelProperty("关联表类型")
    @Column(name = "bill_func_code")
    private String billFuncCode;

	@ApiModelProperty("关联表类型")
    @Column(name = "rel_bill_func_code")
    private String relBillFuncCode;
    
	@ApiModelProperty("关联单据id")
    @Column(name = "rel_bill_id")
    private String relBillId;
    
	@ApiModelProperty("关联单据no")
    @Column(name = "rel_bill_no")
    private String relBillNo;

    @ApiModelProperty("关联单据no")
    @Column(name = "rel_bill_name")
    private String relBillName;

    @ApiModelProperty("是否是虚拟单")
    @Column(name = "is_virtual")
    private Integer isVirtual;
    
	@ApiModelProperty("使用金额")
    @Column(name = "used_amt")
    private BigDecimal usedAmt;
    
	@ApiModelProperty("报销单关联借款单选中的借款单的结算方式")
    @Column(name = "select_settlement")
    private String selectSettlement;
    
	@ApiModelProperty("创建人代码")
    @Column(name = "creator")
    private String creator;
    
	@ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;
    
	@ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;


    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
}
