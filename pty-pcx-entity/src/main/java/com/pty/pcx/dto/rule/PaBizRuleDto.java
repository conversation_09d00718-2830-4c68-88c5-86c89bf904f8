package com.pty.pcx.dto.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaBizRuleDto {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务规则数据编码")
    private String ruleCode;

    @ApiModelProperty("业务规则数据名称")
    private String ruleName;

    @ApiModelProperty("单位编码")
    private String agyCode;

    @ApiModelProperty("区划")
    private String mofDivCode;

    @ApiModelProperty("年度")
    private String fiscal;

    @ApiModelProperty("租户ID")
    private String tenantId;

    /**
     * 业务类型 {@link com.pty.mad.common.BizConditionEnum}
     */
    @ApiModelProperty("业务类型")
    private String bizType;

    /**
     * 业务编码（例如：票类规则的业务编码为费用编码 expenseCode）
     */
    @ApiModelProperty("业务 code")
    private String bizCode;

    @ApiModelProperty("稽核规则 id")
    private String ptyRuleId;

    @ApiModelProperty("条件 json（与前端自定义数据结构，用以回显使用）")
    private String conditionJson;

    @ApiModelProperty("说明")
    private String remark;

    @ApiModelProperty("顺序")
    private Integer seq;

    @ApiModelProperty("修改人编码")
    private String modifier;

    @ApiModelProperty("修改人名称")
    private String modifierName;

    @ApiModelProperty("修改时间")
    private String modifiedTime;

    @ApiModelProperty("创建人代码")
    private String creator;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("创建时间")
    private String createdTime;
}
