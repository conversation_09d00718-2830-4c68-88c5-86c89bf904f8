package com.pty.pcx.dto.ecs.biz;

import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class EcsBizExpDTO extends EcsBizDTO implements Serializable {

    /**
     * 单据ID主键
     */
    @Id
    private String billId;

    /**
     * 单据号
     */
    private String billNo;

    /**
     * 年度
     */
    private Integer fiscalYear;

    /**
     * 区划编码
     */
    private String mofDivCode;

    /**
     * 单位编码
     */
    private String agencyCode;

    /**
     * 单位名称
     */
    private String agencyName;
    /**
     * 来源单据号
     */
    private String srcBillId;

    /**
     * 凭证类型编码
     */
    private String billTypeCode;

    /**
     * 凭证类型名称
     */
    private String billTypeName;
    /**
     * 报销单类型编码
     */
    private String expenseTypeCode;
    /**
     * 报销单类型名称
     */
    private String expenseTypeName;

    /**
     * 报销人部门编码
     */
    private String claimantDeptCode;

    /**
     * 报销人部门名称
     */
    private String claimantDeptName;

    /**
     * 报销人编码
     */
    private String claimantCode;

    /**
     * 报销人名称
     */
    private String claimantName;

    /**
     * 制单金额
     */
    private BigDecimal inputAmt;

    /**
     * 核定金额
     */
    private BigDecimal checkedAmt;

    /**
     * 报销事由
     */
    private String expReason;

    /**
     * 项目负责人代码
     */
    private String projectUserCode;

    /**
     * 项目负责人名称
     */
    private String projectUserName;

    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 申请类型
     */
    private String applyType;

    /**
     * 借款类别名称
     */
    private String loanTypeName;

    /**
     * 预算管理部门编码
     */
    private String budMorgCode;

    /**
     * 预算管理部门
     */
    private String budMorgName;

    /**
     * 预算单位编码
     */
    private String budAgyCode;

    /**
     * 预算单位名称
     */
    private String budAgyName;

    /**
     * 终审日期
     */
    private String auditedTime;

    /**
     * 终审人
     */
    private String auditedUser;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人
     */
    private String createUserCode;

    /**
     * 创建人姓名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后修改人
     */
    private String updateUserCode;

    /**
     * 最后修改人名称
     */
    private String updateUser;

    /**
     * 最后修改时间
     */
    private String updateTime;

    private Integer ver;

    /**
     * 1删除 2未删除
     */
    private Integer isDeleted;

    /**
     * 是否核对 1核对 2未核对
     */
    private Integer isChecked;

    private String tenantId;

    /**
     * 附件唯一标识
     */
    private String attachId;
    /**
     * 附件名称
     */
    private String fileName;
    /**
     * 附件格式
     */
    private String fileFormat;

    private List<String> billIds;

    /**
     * 匹配状态，用于对账
     * 0：未匹配，1：部分匹配，2:完全匹配
     */
    private Integer matchStatus;
    /**
     * 报销单结算方式集合
     */
    private List<EcsBizExpSetModeDTO> setmodeList;

    /**
     * 报销单关联的申请单id集合
     */
    private List<String> appIdList;
}
