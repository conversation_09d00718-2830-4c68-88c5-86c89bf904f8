package com.pty.pcx.dto.ecs;

import com.pty.pcx.common.constant.PcxConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EcsMsgDTO {
    private String ecsBillId;
    private List<String> bizTypeCodes;
    private List<String> bizTypeNames;
    private Integer isValid;//0未验真，1已验真，2验真失败,3无需查验
    private Map<String, Integer> attachCountMap;//附件数量
    private String purTaxId; //报销人所在单位社会信用代码(校验税号)
    private String purName; //报销人所在单位名称
    private String purAccountNo;//报销人所在单位开户行及账号
    private Integer isInvSeal; //是否存在发票专用章
    private Integer isManSupSeal; //是否存在监制章
    private String sellerName;          //销售方名称
    private String sellerTaxId;         //销售方纳税人识别号
    private String invCode;             //发票代码
    private String invNo;               //发票号码
    private String identifyCode;        //校验码
    private Integer isBillEdited;
    private EcsModifiedBillDTO ecsModifiedBillDTO;

    public Integer isRelFileHotel(){
        return Objects.nonNull(attachCountMap) && attachCountMap.containsKey(PcxConstant.ATTACH_FILE_HOTEL)? 1 : 2;
    }

    public Integer isRelFileTaxi(){
        return Objects.nonNull(attachCountMap) && attachCountMap.containsKey(PcxConstant.ATTACH_FILE_TAXI)? 1 : 2;
    }

    public Integer relFileCnt(){
        return Objects.nonNull(attachCountMap)? attachCountMap.values().stream().reduce(0, Integer::sum) : 0;
    }
    public Integer relFileHotelCnt(){
        return Objects.nonNull(attachCountMap)? attachCountMap.getOrDefault(PcxConstant.ATTACH_FILE_HOTEL, 0) : 0;
    }
    public Integer relFileTaxiCnt(){
        return Objects.nonNull(attachCountMap)? attachCountMap.getOrDefault(PcxConstant.ATTACH_FILE_TAXI, 0) : 0;
    }
    public Integer relOtherFileCnt(){
        Integer num = 0;
        if (Objects.nonNull(attachCountMap)){
            for (Map.Entry<String, Integer> entry : attachCountMap.entrySet()) {
                if (!PcxConstant.ATTACH_FILE_HOTEL.equals(entry.getKey()) && !PcxConstant.ATTACH_FILE_TAXI.equals(entry.getKey())){
                    num += entry.getValue();
                }
            }
        }
        return num;
    }

    public String getBizTypeName(){
        if (CollectionUtils.isEmpty(bizTypeNames)){
            return "";
        }
        return bizTypeNames.get(0);
    }
}
