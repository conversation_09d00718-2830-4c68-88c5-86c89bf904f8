package com.pty.pcx.dto;

import com.pty.pcx.common.valid.BillSave;
import com.pty.pcx.common.valid.BillView;
import com.pty.pcx.common.valid.Delete;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PcxBaseDTO implements Serializable {

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("单位编码")
    @NotBlank(message = "单位编码不能为空", groups = {Delete.class, BillSave.class, BillView.class})
    private String agyCode;

    @ApiModelProperty("单位名称")
    private String agyName;

    @ApiModelProperty("年度")
    @NotBlank(message = "年度不能为空", groups = {Delete.class, BillSave.class,BillView.class})
    private String fiscal;

    @ApiModelProperty("区划")
    @NotBlank(message = "区划不能为空", groups = {Delete.class, BillSave.class,BillView.class})
    private String mofDivCode;
}
