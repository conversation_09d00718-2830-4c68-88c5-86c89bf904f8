package com.pty.pcx.dto.ecs.inv;

import lombok.Data;

@Data
public class ExpDetailDTO {
    /**
     * 入住时间
     */
    private String checkInTime;
    /**
     * 离店时间
     */
    private String checkOutTime;
    /**
     * 城市信息
     */
    private String cityName;
    /**
     * 费用类型编码
     */
    private String expenseTypeCode;
    /**
     * 费用类型名称
     */
    private String expenseTypeName;
    /**
     * 人员信息
     */
    private String passengerName;

    private String remark;

    private String deptStation;

    private String arrStation;
}
