package com.pty.pcx.dto.ecs.inv;

import com.pty.pcx.entity.bas.PcxBasExpType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

// 财政电子屏efi
@Data
public class PcxEfiInvoiceDto extends InvoiceBaseDto{

    private BigDecimal totalAmt;        // 实际报销金额

    private List<Detail> detailList = new ArrayList<>();
    private List<ExpDetailDTO> billExpDetailList = new ArrayList<>();


    @Data
    public static final class Detail {
        private String itemName;        // 项目/服务名称
        private String itemUnit;        // 计量单位
        private String itemQty;         // 数量
        private String itemCode;
        private BigDecimal taxAmt;      // 税额
        private BigDecimal taxRate;         // 税率
        private String detailId;
        private BigDecimal itemAmt;

        private BigDecimal ecsAmt = BigDecimal.ZERO; //报销自己计算得来的项目金额

        /**
         * 票据业务类型
         */
        private String bizTypeCode;
        private String bizTypeName;

        /**
         * 票对于费用类型
         */
        private List<PcxBasExpType> expTypeList;
    }
}
