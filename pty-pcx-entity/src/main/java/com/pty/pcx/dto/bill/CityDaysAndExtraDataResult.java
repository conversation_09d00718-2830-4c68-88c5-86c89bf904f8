package com.pty.pcx.dto.bill;

import com.pty.pcx.entity.bill.PcxBillExtraAttach;
import com.pty.pcx.entity.bill.PcxBillExtraSubsidy;
import com.pty.pcx.entity.bill.PcxBillTripCityDay;
import lombok.Data;

import java.util.List;

@Data
public class CityDaysAndExtraDataResult {

    private List<PcxBillTripCityDay> cityDays;
    private List<PcxBillExtraSubsidy> extraSubsidies;
    private List<PcxBillExtraAttach> extraAttaches;

    public static CityDaysAndExtraDataResult of(List<PcxBillTripCityDay> cityDays, List<PcxBillExtraSubsidy> extraSubsidies, List<PcxBillExtraAttach> extraAttaches) {
        CityDaysAndExtraDataResult result = new CityDaysAndExtraDataResult();
        result.cityDays = cityDays;
        result.extraSubsidies = extraSubsidies;
        result.extraAttaches = extraAttaches;
        return result;
    }
}
