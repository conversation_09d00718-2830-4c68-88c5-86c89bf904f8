package com.pty.pcx.dto.ecs;

import lombok.Data;

@Data
public class TripEmpCityDateDTO {
    private String empCode;
    private String empName;
    private String budLevel;
    private Integer isSubsidy;
    private String empType;
    private String cityCode;
    private double travelDays;
    private double dormDays;
    private String tripId;
    private String startTime;
    private String finishTime;
    private double workDays;
    private double workDormDays;
    //额外加班天数
    private double workExtraDays;
    //额外项目经理天数
    private double managerDays;
    private double hotelDays;
    private double workHotelDays;

    public String getKey(){
        return String.format("%s-%s-%s-%s-%s", empCode, cityCode, tripId, startTime, finishTime);
    }
}
