package com.pty.pcx.dto.project;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ProjectExpenseTypeImportAttachDTO {

    @NotBlank(message = "附件不能为空")
    private String attachId;

    @NotBlank(message = "单位编码不能为空")
    private String agyCode;

    @NotBlank(message = "区划不能为空")
    private String mofDivCode;

    @NotNull(message = "年度不能为空")
    private Integer fiscal;

}
