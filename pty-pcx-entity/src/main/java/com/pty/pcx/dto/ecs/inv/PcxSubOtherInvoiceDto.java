package com.pty.pcx.dto.ecs.inv;

import com.alibaba.fastjson.annotation.JSONField;
import com.pty.pcx.entity.bas.PcxBasExpType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

// 原始报销凭证-定额发票quota、门票ticket
@Data
public class PcxSubOtherInvoiceDto extends InvoiceBaseDto{

    private BigDecimal taxAmt;      // TODO 税额
    private String taxRate;         // TODO 税率
    private String purName;         // TODO 报销人所在单位名称
    private String sellerName;      // TODO 销售方名称(酒店、饭店等)


    /**
     * 票据业务类型
     */
    private String bizTypeCode;
    private String bizTypeName;

    /**
     * 票对于费用类型
     */
    private List<PcxBasExpType> expTypeList;
}
