package com.pty.pcx.dto.ecs.inv;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pty.pcx.dto.ecs.settlement.EcsBillSettleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true) // Add this annotation
public class InvoiceBaseDto {

    private String billId;                  // 发票的主键id, 报销用于回写电子凭证状态, 判断是否已报销
    private String attachId;                // 发票的附件id, 报销用于关联附件清单
    private String fileName;                // 发票的附件名称
    private String billNo;

    private BigDecimal billAmt;

    private String billDate;        // 乘坐日期

    private String billTypeCode;        // 票据类型编码
    private String billTypeName;        // 票据类型名称
    private String billKind;            // 发票属性(paper：纸质影像, std:数据标准, elec：普通电子)
    private Integer isValid;             // 查验结果(0:未查验, 1:查验成功, 2:查验失败, 3:无需查验)

    private String claimer;             //认领人
    private String claimerName;         //认领人姓名

    private Integer isRelExp;           //是否已报销，1=是，2=否

    private String billDescpt;

    private List<EcsBillSettleDTO> billSettlList = new ArrayList<>();

    private List<Object> fileList;

    private String remarks;

    private Integer isInvSeal;          //TODO 是否存在发票专用章(稽核校验)
    private Integer isManSupSeal;       //TODO 是否存在监制章(稽核校验)
    private String purTaxId;            // 报销人所在单位社会信用代码(校验税号)
    private String purName;             // 报销人所在单位名称
    private String purAccountNo;        // 报销人所在单位开户行及账号
    private String purAdd;              //购买方地址
    private String sellerName;          //销售方名称
    private String sellerTaxId;         //销售方纳税人识别号
    private String invCode;             //发票代码
    private String invNo;               //发票号码
    private String identifyCode;        //校验码

    private Integer isBillEdited;; //是否修改，2=未修改，1=已修改

    /**
     * 代报人编码
     */
    private String authUserCode;
    /**
     * 代报人姓名
     */
    private String authUser;

    private boolean otherBizEcs = false;

    public void afterInit(){

    }
}
