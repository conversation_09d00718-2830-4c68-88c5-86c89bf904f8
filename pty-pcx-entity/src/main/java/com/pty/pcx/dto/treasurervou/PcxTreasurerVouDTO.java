package com.pty.pcx.dto.treasurervou;

import com.pty.pub.common.bean.Response;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PcxTreasurerVouDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sysId;
    private Integer isMergeVou;
    private Integer isMergeByFacDr;//借贷方合并规则,合并为1,不合并为0
    private Integer isMergeByFacCr;
    private Integer isMergeByBacDr;
    private Integer isMergeByBacCr;
    private Integer isMergeByAcitem;//辅助核算项(科目)相同合并
    private Integer isMergeBySummary;
    private String vouDetailSortMode;
    private Integer isCtrlBalance;
    private Integer isPexExpBudUnLoan;
    private String agyCode;
    private String agyName;
    private Integer fiscal;
    private String mofDivCode;
    private String tenantId;
    private String acbCode;
    private String acbName;
    private String vouNo;
    private String vouId;
    private Integer isCover;


    String creator;
    String creatorName;
    String bizTypeId;
    Integer isMergeByBillNo;
    String vouDate;
    String vouTypeCode;
    String attachCnt;
    String vouSummary;
    List<String> bills;
    List<String> vouIds;
    List<String> billIdsSorted;



    List<String> agyAcitems;
    Response response;
    String loginDate;
    Integer isPreview;
    String bussModule;
    private Integer isFillFreeVouNo;
    private Integer isForceMerge;

    private Integer isConfirm = 0;
    List<String> mountConditions;
    String startDate;
    String endDate;
    private String groupByField;
    private Integer isSyncCache;

    private Integer isAppendVou;
}
