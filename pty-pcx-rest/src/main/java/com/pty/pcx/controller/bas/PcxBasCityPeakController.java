package com.pty.pcx.controller.bas;

import com.pty.pcx.api.bas.IPcxBasCityPeakService;
import com.pty.pcx.qo.bas.PcxBasCityPeakQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Indexed
@RestController
@RequestMapping(value = "/pcx/pcxBasCityPeak", method = {RequestMethod.POST})
@Api(value = "城市淡旺季接口", tags = {"城市淡旺季表(PcxBasCityPeak)表控制层"})
@RestSchema(schemaId = "/pcxBasCityPeakController")
@Slf4j
public class PcxBasCityPeakController {


    @Autowired
    private IPcxBasCityPeakService pcxBasCityPeakService;

    /**
     * 查询淡旺季城市列表
     */
    @ApiOperation("查询淡旺季城市信息列表")
    @PostMapping(value = "/selectAllCityList")
    public Response selectAllCityPeakList(@RequestBody PcxBasCityPeakQO pcxBasCityPeakQO) {
        try {
            return Response.success(pcxBasCityPeakService.selectAllCityPeakList(pcxBasCityPeakQO));
        }catch (Exception e){
            log.error("查询淡旺季城市列表信息失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }
    }

    @ApiOperation("查询城市淡旺季信息列表")
    @PostMapping(value = "/selectCityPeakList")
    public Response selectCityPeakList(@RequestBody PcxBasCityPeakQO pcxBasCityPeakQO) {
        try {
            return Response.success(pcxBasCityPeakService.selectCityPeakList(pcxBasCityPeakQO));
        }catch (Exception e){
            log.error("查询城市淡旺季信息列表失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }
    }

    @ApiOperation("新增/修改城市淡旺季")
    @PostMapping("saveOrUpdate")
    public Response saveOrUpdate(@RequestBody List<PcxBasCityPeakQO> pcxBasCityPeakQOList) {
        try {
            pcxBasCityPeakService.saveOrUpdate(pcxBasCityPeakQOList);
            return Response.success().setMsg("保存成功");
        }catch (Exception e){
            log.error("新增/修改城市淡旺季失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }
    }

    @ApiOperation("删除城市淡旺季")
    @PostMapping("deletePcxBasCityPeak")
    public Response deletePcxBasCityPeak(@RequestBody List<PcxBasCityPeakQO> pcxBasCityPeakQOList) {
        try {
            pcxBasCityPeakService.deletePcxBasCityPeak(pcxBasCityPeakQOList);
            return Response.success().setMsg("删除成功");
        }catch (Exception e){
            log.error("删除城市淡旺季失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }
    }

}
