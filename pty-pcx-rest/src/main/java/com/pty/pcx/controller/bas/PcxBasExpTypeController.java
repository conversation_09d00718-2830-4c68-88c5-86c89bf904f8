package com.pty.pcx.controller.bas;


import com.pty.pcx.api.bas.IPcxBasExpTypeService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Indexed
@RequestMapping(value = "/pcx/pcxBasExpType")
@Api(value = "费用类型表接口", tags = {"PcxBasExpTypeController"})
@Slf4j
@RestSchema(schemaId = "/PcxBasExpTypeController")
@RestController
public class PcxBasExpTypeController {

    @Autowired
    private IPcxBasExpTypeService pcxBasExpTypeService;

    @PostMapping(value = "/pageList")
    @ApiOperation(value = "分页查询")
    public Response<?> selectPage(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        try{
            return pcxBasExpTypeService.selectWithPage(pcxBasExpTypeQO);
        }catch (Exception e){
            log.error("分页查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

        @PostMapping(value = "/getAll")
    @ApiOperation(value = "查询所有的")
    public Response<?> getAll(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        try{
            CheckMsg<?> checkMsg = pcxBasExpTypeService.getAll(pcxBasExpTypeQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/queryExpense")
    @ApiOperation(value = "查询所有的")
    public Response<?> queryExpense(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        try{
            return Response.success(pcxBasExpTypeService.queryExpense(pcxBasExpTypeQO));
        }catch (Exception e){
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "保存")
    public Response<?> save(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        return pcxBasExpTypeService.save(pcxBasExpTypeQO);
    }

    @PostMapping(path = "/update")
    @ApiOperation("修改费用类型")
    public Response<?> update(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        try {
            return pcxBasExpTypeService.updateById(pcxBasExpTypeQO);
        }catch (Exception e){
            log.error("新增失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(path = "/remove")
    @ApiOperation("删除费用类型")
    public Response<?> remove(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        try {
            return pcxBasExpTypeService.deleteByQO(pcxBasExpTypeQO);
        }catch (Exception e){
            log.error("删除失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/selectById")
    @ApiOperation(value = "通过主键查询单条数据")
    public Response<?> selectById(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        try {
            return pcxBasExpTypeService.selectById(pcxBasExpTypeQO);
        }catch (Exception e){
            log.error("查询单条数据失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(path = "/enable")
    @ApiOperation("费用类型启用")
    public Response<?> enableByIds(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        Response<?> res = Response.commonResponse(Response.SUCCESS_CODE,"费用类型启用成功");
        try {
            res = pcxBasExpTypeService.disEnableOrEnable(pcxBasExpTypeQO, PubConstant.LOGIC_TRUE);
        } catch (Exception e) {
            log.error("费用类型启用失败：{}", e.getMessage(),e);
            res.setCode(Response.FAIL_CODE).setMsg("费用类型启用失败");
        }
        return res;
    }

    @PostMapping(path = "/disEnable")
    @ApiOperation("费用类型停用")
    public Response<?> disEnableByIds(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        Response<?> res = Response.commonResponse(Response.SUCCESS_CODE,"费用类停用成功");
        try {
            res =  pcxBasExpTypeService.disEnableOrEnable(pcxBasExpTypeQO, PubConstant.LOGIC_FALSE);
        } catch (Exception e) {
            log.error("费用类型停用失败：{}", e.getMessage(),e);
            res.setCode(Response.FAIL_CODE).setMsg("费用类型停用失败");
        }
        return res;
    }

    @PostMapping(value = "/getExpType/detail")
    @ApiOperation(value = "查询费用类型详情")
    public Response<?> getExpTypeDetail(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO) {
        Response<?> res = Response.commonResponse(Response.SUCCESS_CODE,"查询费用类型详情成功");
        try {
            return pcxBasExpTypeService.getExpTypeDetail(pcxBasExpTypeQO);
        } catch (Exception e) {
            log.error("查询费用类型详情失败：{}", e.getMessage(),e);
            res.setCode(Response.FAIL_CODE).setMsg("查询费用类型详情失败");
        }
        return res;
    }

    @PostMapping(value = "/getExpTypeLevel")
    @ApiOperation(value = "获取费用明细层高")
    public Response<Map<Integer, String>> getExpDetailLevel(@RequestBody PcxBasExpTypeQO pcxBasExpTypeQO){
        Response<Map<Integer, String>> res = Response.commonResponse(Response.SUCCESS_CODE,"查询费用类型详情成功");
        try {
            Map<Integer, String>  expDetailLevel = pcxBasExpTypeService.getExpDetailLevel(pcxBasExpTypeQO);
            return res.setData(expDetailLevel);
        } catch (Exception e) {
            log.error("获取费用明细层高失败：{}", e.getMessage(),e);
            res.setCode(Response.FAIL_CODE).setMsg("获取费用明细层高失败");
        }
        return res;
    }
}
