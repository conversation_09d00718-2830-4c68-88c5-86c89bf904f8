package com.pty.pcx.controller.mad;

import com.pty.pcx.api.mad.MadExtDataService;
import com.pty.pcx.qo.mad.MadAgyInfoLimitQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@Api(value = "拓展元素查询接口", tags = {"拓展元素查询接口"})
@RestSchema(schemaId = "PcxMadExtDataController")
@RequestMapping("/pcx/madext")
@RestController
@Indexed
@Slf4j
public class PcxMadExtDataController {

    @Autowired
    private MadExtDataService madExtDataService;

    @RequestMapping(value = "/queryAgyInfoLimit/{atomCode}", method = RequestMethod.POST)
    public Response queryAgyInfoLimit(@PathVariable("atomCode") String atomCode, @RequestBody @Valid MadAgyInfoLimitQO qo){
        qo.setMadName(qo.getKeyword());
        qo.setAtomCode(atomCode);
        return Response.success(madExtDataService.queryAgyInfoLimit(qo));
    }
}
