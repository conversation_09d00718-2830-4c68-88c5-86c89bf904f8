package com.pty.pcx.controller.treasurypay.detail;

import com.alibaba.fastjson.JSONObject;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillPayDetailService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillStatusHistoryService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.qo.treasurypay.detail.*;
import com.pty.pcx.util.trans.PcxBillTransformer;
import com.pty.pcx.vo.treasurypay.detail.*;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * (PcxBillPayDetail)表控制层
 * <AUTHOR>
 * @since 2024-12-13 18:45:05
 */
@Slf4j
@Indexed
@RequestMapping(value = "/pcx/pcxBillPayDetail", method = {RequestMethod.POST})
@Api(value = "出纳确认、出纳支付接口", tags = {"出纳确认、出纳支付接口"})
@RestSchema(schemaId = "/pcxBillPayDetailController")
@RestController
public class PcxBillPayDetailController {

    @Autowired
    private IPcxBillPayDetailService pcxBillPayDetailService;

    @Autowired
    private IPcxBillStatusHistoryService pcxBillStatusHistoryService;

    @Autowired
    private IPcxBasItemService pcxBasItemService;

    @PostMapping(value = "/selectList")
    @ApiOperation(value = "查询支付申请列表")
    public Response<List<PcxBillPayDetailVO>> selectList(@RequestBody PcxBillPayDetailQO qo) {
        try {
            CheckMsg<List<PcxBillPayDetailVO>> result = pcxBillPayDetailService.selectList(qo);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("查询数据列表异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    @PostMapping(value = "/confirm")
    @ApiOperation(value = "确认生成支付申请")
    public Response<Void> confirm(@RequestBody PcxBillPayDetailQO qo) {
        try {
            CheckMsg<Void> result = pcxBillPayDetailService.confirm(qo);
            if (result.isSuccess()) {
                return Response.success(null);
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("确认生成支付申请异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    @PostMapping(value = "/selectDetail")
    @ApiOperation(value = "查询支付申请详情")
    public Response<PcxBillPayDetailSelVO> selectDetail(@RequestBody PcxBillPayDetailSelQO qo) {
        try {
            CheckMsg<PcxBillPayDetailSelVO> result = pcxBillPayDetailService.selectDetail(qo);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("查询数据列表异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }


    @PostMapping("/allocateSettlementBalance")
    @ApiOperation("为结算分配指标")
    public Response<PaySettlementBalanceVO> allocateSettlementBalance(@RequestBody AllocateBalanceQO qo) {
        log.info("支付明细=>指标变更触发支付明细计算, billId={}, params={}", qo.getBillId(), JSONObject.toJSONString(qo));
        CheckMsg<PaySettlementBalanceVO> res = pcxBillPayDetailService.allocateSettlementBalance(qo);
        if (res.isSuccess())
            return Response.success(res.getData());
        return Response.businessFailResponse(res.getMsgInfo());
    }


    @PostMapping("/allocatePayDetails")
    @ApiOperation("根据指标获取支付明细")
    public Response<List<PcxBillPayDetail>> allocatePayDetails(@RequestBody AllocateBalanceQO qo) {
        log.info("支付明细=>指标变更触发支付明细计算, billId={}, params={}", qo.getBillId(), JSONObject.toJSONString(qo));
        CheckMsg<List<PcxBillPayDetail>> res = pcxBillPayDetailService.allocatePayDetails(qo);
        if (res.isSuccess())
            return Response.success(res.getData());
        return Response.businessFailResponse(res.getMsgInfo());
    }


    @PostMapping(value = "/calculatePayDetails")
    @ApiOperation(value = "重新计算支付明细")
    public Response<PayDetailVO> calculatePayDetails(@RequestBody PayDetailShadowQO qo) {
        log.info("支付明细=>经费来源变更触发支付明细计算, billId={}, params={}", qo.getBillId(), JSONObject.toJSONString(qo));
        qo.getFundSource().forEach(item -> {item.setBillId(qo.getBillId());});
        CheckMsg<PayDetailVO> res = pcxBillPayDetailService.calculatePayDetails(PcxBillTransformer.INSTANCE.toPayDetailQO(qo), Boolean.FALSE);
        if (res.isSuccess())
            return Response.success(res.getData());
        return Response.businessFailResponse(res.getMsgInfo());
    }


    /**
     * 查询出纳确认列表
     */
    @PostMapping(value = "/selectListForPayDetailStatus")
    @ApiOperation(value = "查询出纳确认列表")
    public Response<PageResult<? extends AbstractPcxBillPayDetailForSomething>> selectListForPaySuccess(@RequestBody PcxBillPayDetailQO qo) {
        try {
            BillPayDetailStatusEnum status = BillPayDetailStatusEnum.getByCode(qo.getPayStatus());
            Assert.notNull(status, "暂不支持的确认状态" + qo.getPayStatus());
            CheckMsg<PageResult<? extends AbstractPcxBillPayDetailForSomething>> result = pcxBillPayDetailService.selectListForDetailStatus(qo, status);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("查询出纳确认列表异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }


    @PostMapping(value = "/billFuncCodeItemRel")
    @ApiOperation(value = "查询单据类型和事项关联下拉数据")
    public Response<?> billFuncCodeItemRel(@RequestBody BillFuncCodeItemRelQO qo) {
        try {
            return pcxBasItemService.billFuncCodeItemRel(qo);
        }catch (Exception e) {
            log.error("查询单据类型和事项关联下拉数据异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }


    @PostMapping(value = "/selectListForChangeBillStatus")
    @ApiOperation(value = "查询出纳确认列表-变更中")
    public Response<Map<String, Object>> selectListForChangeBillSuccess(@RequestBody PcxBillPayDetailQO qo) {
        try {
            CheckMsg<Map<String, Object>> result = pcxBillPayDetailService.selectListForChangeBillStatus(qo);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("查询出纳确认列表异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 查询出纳支付列表
     */
    @PostMapping(value = "/selectListForPayerDetail")
    @ApiOperation(value = "查询出纳支付列表")
    public Response<PageResult<? extends AbstractPcxBillPayDetailForSomething>> selectListForPayerDetail(@RequestBody PcxBillPayDetailQO qo) {
        try {
            BillPayerDetailEnum status = BillPayerDetailEnum.getByCode(qo.getPayStatus());
            Assert.notNull(status, "暂不支持的支付状态" + qo.getPayStatus());
            CheckMsg<PageResult<? extends AbstractPcxBillPayDetailForSomething>> result = pcxBillPayDetailService.selectListForPayerDetail(qo, status);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("查询出纳支付列表异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 查询单据流转信息
     */
    @PostMapping(value = "/selectBillStatusHistory")
    @ApiOperation(value = "查询单据流转信息")
    public Response<List<PcxBillStatusHistoryVO>> selectBillStatusHistory(@RequestBody @Validated PcxBillStatusHistoryQO qo) {
        try {
            CheckMsg<List<PcxBillStatusHistoryVO>> result = pcxBillStatusHistoryService.selectBillStatusHistory(qo);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("查询单据流转信息异常 req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 普通支付
     */
    @PostMapping(value = "/normalPay")
    @ApiOperation(value = "普通支付")
    public Response<?> normalPay(@RequestBody @Validated PcxBillPayQO pcxBillPayQO) {
        try {
            CheckMsg<?> result = pcxBillPayDetailService.normalFlatPay(pcxBillPayQO);
            if (result.isSuccess()) {
                return Response.commonResponse(result.getMsgInfo());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("支付异常 req {}", pcxBillPayQO, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 是否批量支付弹窗
     */
    @PostMapping(value = "/canBatchPay")
    @ApiOperation(value = "是否批量支付弹窗")
    public Response<?> canBatchPay(@RequestBody @Validated PcxBillPayQO pcxBillPayQO) {
        try {
            CheckMsg<?> result = pcxBillPayDetailService.canBatchPay(pcxBillPayQO);
            return Response.success(result.getData());
        }catch (Exception e) {
            log.error("支付异常 req {}", pcxBillPayQO, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 批量支付
     */
    @PostMapping(value = "/batchPay")
    @ApiOperation(value = "批量支付")
    public Response<?> batchPay(@RequestBody @Validated PcxBillPayQO pcxBillPayQO) {
        try {
            CheckMsg<?> result = pcxBillPayDetailService.normalPay(pcxBillPayQO);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("支付异常 req {}", pcxBillPayQO, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 批量查询支付状态
     */
    @PostMapping(value = "/refreshPayStatus")
    @ApiOperation(value = "批量查询支付状态")
    public Response<List<PcxBillRefreshPayVO>> refreshPayStatus(@RequestBody @Validated PcxBillRefreshPayQO pcxBillRefreshPayQO) {
        try {
            CheckMsg<List<PcxBillRefreshPayVO>> result = pcxBillPayDetailService.refreshPayStatus(pcxBillRefreshPayQO);
            if (result.isSuccess()) {
                return Response.success(result.getData());
            }
            return Response.businessFailResponse(result.getMsgInfo());
        }catch (Exception e) {
            log.error("批量查询支付状态异常 req {}", pcxBillRefreshPayQO, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    @PostMapping(value = "/payTypeEnum")
    @ApiOperation(value = "支付类型枚举")
    public Response<List<JSONObject>> payTypeEnum() {
        return Response.success(Arrays.stream(BillPayTypeEnum.values()).map(e -> new JSONObject().fluentPut("code", e.getCode()).fluentPut("name", e.getName())).collect(Collectors.toList()));
    }

    @PostMapping(value = "/financialTypeEnum")
    @ApiOperation(value = "资金类型枚举")
    public Response<List<JSONObject>> financialTypeEnum() {
        return Response.success(Arrays.stream(BillCapitalTypeEnum.values()).map(e -> new JSONObject().fluentPut("code", e.getCode()).fluentPut("name", e.getName())).collect(Collectors.toList()));
    }


    /**
     * 撤回
     */
    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤回")
    public Response<Void> revoke(@RequestBody PcxBillPayDetailQO qo) {
        try {
//            CheckMsg<Void> result = pcxBillPayDetailService.disable(qo.getId(), BillChangeTypeEnum.REVOKE, qo.getTargetId());
//            if (result.isSuccess()) {
//                return Response.success();
//            }
//            return Response.businessFailResponse(result.getMsgInfo());
            return Response.success();
        }catch (Exception e) {
            log.error("撤回数据异常req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 取消撤回
     */
    @PostMapping(value = "/cancelRevoke")
    @ApiOperation(value = "取消撤回")
    public Response<Void> cancelRevoke(@RequestBody PcxBillPayDetailQO qo) {
        try {
//            CheckMsg<Void> result = pcxBillPayDetailService.disable(qo.getId(), BillChangeTypeEnum.REVOKE, qo.getTargetId());
//            if (result.isSuccess()) {
//                return Response.success();
//            }
//            return Response.businessFailResponse(result.getMsgInfo());
            return Response.success();
        }catch (Exception e) {
            log.error("撤回数据异常req {}", qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }

    /**
     * 撤回结果查询
     */
    @PostMapping(value = "/revokeResult")
    @ApiOperation(value = "撤回结果查询")
    public Response<Boolean> revokeResult(@RequestBody PcxBillPayDetailQO qo) {
        try {
//            CheckMsg<PcxBillPayDetailVO> result = pcxBillPayDetailService.selectDetail(qo);
//            if (result.isSuccess()) {
//                return Response.success(result.getData());
//            }
//            return Response.businessFailResponse(result.getMsgInfo());
            return Response.success(Boolean.TRUE).setMsg("撤回成功");
        }catch (Exception e){
            log.error("撤回结果查询异常 req {}",qo, e);
            return Response.businessFailResponse(e.getMessage());
        }
    }


    private static ExecutorService executorService = Executors.newFixedThreadPool(200);

    @SneakyThrows
    @PostMapping(value = "/testExecutorEnhance")
    @ApiOperation(value = "测试ExecutorEnhance")
    public Response<Void> testExecutorEnhance() {
        try {
            PtyContext.setTenantId("***********");
            List<Integer> list = new ArrayList<>();
            for (int i = 0; i < 5000; i++) {
                list.add(i);
            }
            list.parallelStream().forEach(i -> {
                System.out.println(Thread.currentThread().getName() + ":" + PtyContext.getTenantId());
            });
            PtyContext.clearValue();
            return Response.success();
        }catch (Exception e){
            log.error("测试ExecutorEnhance异常", e);
            return Response.businessFailResponse(e.getMessage());
        }
    }
}
