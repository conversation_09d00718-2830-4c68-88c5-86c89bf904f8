package com.pty.pcx.controller.plan;

import com.pty.pcx.api.plan.PcxPlanService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.plan.PcxPlanQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: PcxPlanController
 * @Description: 基础信息计划业务块接口
 * @Date: 2025/3/3  下午16:14
 * @Author: zhangmeng
 **/
@Api(value = "所有计划统一接口", tags = {"所有计划统一接口"})
@RestSchema(schemaId = "pcxPlanController")
@RequestMapping("/pcx/plan")
@RestController
@Indexed
@Slf4j
public class PcxPlanController {

    @Autowired
    private PcxPlanService pcxPlanService;

    @PostMapping(value = "/getList")
    @ApiOperation(value = "查询计划")
    public Response<?> queryPlan(@RequestBody PcxPlanQO qo) {
        try {
            CheckMsg<?> list = pcxPlanService.getList(qo);
            if(!list.isSuccess()){
                return Response.fail().setMsg(list.getMsgInfo());
            }
            return Response.success().setMsg("查询计划成功").setData(list.getData());
        } catch (Exception e) {
            log.error("查询计划报错：{}", e.getMessage(), e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/getPlanByBillId")
    @ApiOperation(value = "根据BillId查询计划")
    public Response<?> queryPlanByBillId(@RequestBody PcxPlanQO qo) {
        try {
            CheckMsg<?> list = pcxPlanService.getPlanByBillId(qo);
            if(!list.isSuccess()){
                return Response.fail().setMsg(list.getMsgInfo());
            }
            return Response.success().setMsg("查询计划成功").setData(list.getData());
        } catch (Exception e) {
            log.error("查询计划报错：{}", e.getMessage(), e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/getUnusedList")
    @ApiOperation(value = "查询未使用的计划")
    public Response<?> queryUnusedPlan(@RequestBody PcxPlanQO qo) {
        try {
            CheckMsg<?> list = pcxPlanService.queryUnusedPlan(qo);
            if(!list.isSuccess()){
                return Response.fail().setMsg(list.getMsgInfo());
            }
            return Response.success().setMsg("查询计划成功").setData(list.getData());
        } catch (Exception e) {
            log.error("查询计划报错：{}", e.getMessage(), e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/updateByQo")
    @ApiOperation(value = "修改计划")
    public Response<?> updatePlan(@RequestBody PcxPlanQO qo) {
        try {
            CheckMsg<?> checkMsg = pcxPlanService.updateByQo(qo);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success().setMsg("修改计划成功");
        } catch (Exception e) {
            log.error("修改计划报错：{}", e.getMessage(), e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "保存计划")
    public Response<?> savePlan(@RequestBody PcxPlanQO qo) {
        try {
            CheckMsg<?> checkMsg = pcxPlanService.save(qo);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success().setMsg("保存计划成功");
        } catch (Exception e) {
            log.error("保存计划报错：{}", e.getMessage(), e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/deleteByQo")
    @ApiOperation(value = "删除计划")
    public Response<?> deletePlan(@RequestBody PcxPlanQO qo) {
        try {
            CheckMsg<?> checkMsg = pcxPlanService.deleteByQo(qo);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success().setMsg("删除计划成功");
        } catch (Exception e) {
            log.error("删除计划报错：{}", e.getMessage(), e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

}
