package com.pty.pcx.controller.bill;

import com.pty.pcx.api.bill.PcxBillExtraSubsidyService;
import com.pty.pcx.api.ecs.EcsProcessService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bill.extrasubsidy.QueryDetailExtraSubsidyQO;
import com.pty.pcx.qo.bill.extrasubsidy.QueryExtraSubsidyQO;
import com.pty.pcx.qo.bill.extrasubsidy.UpdateExtraSubsidyQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@Api(value = "额外补助相关接口", tags = {"额外补助流程接口"})
@RestSchema(schemaId = "PcxBillExtraSubsidyController")
@RequestMapping("/pcx/extrasubsidy")
@RestController
@Indexed
@Slf4j
public class PcxBillExtraSubsidyController {

    @Resource
    private PcxBillExtraSubsidyService pcxBillExtraSubsidyService;
    @Resource
    private EcsProcessService ecsProcessService;

    @PostMapping(value = "/queryExtraSubsidy")
    @ApiOperation(value = "查询额外补助")
    public Response queryExtraSubsidy(@RequestBody @Valid QueryExtraSubsidyQO qo) {
        try {
            CheckMsg<?> checkMsg = pcxBillExtraSubsidyService.queryExtraSubsidy(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询额外补助异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询额外补助异常");
        }
    }

    @PostMapping(value = "/updateExtraSubsidy")
    @ApiOperation(value = "更新额外补助")
    public Response updateExtraSubsidy(@RequestBody @Valid UpdateExtraSubsidyQO qo) {
        try {
            CheckMsg<?> checkMsg = ecsProcessService.updateExtraSubsidy(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("更新额外补助异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "更新额外补助异常");
        }
    }

    @PostMapping(value = "/queryDetailExtraSubsidy")
    @ApiOperation(value = "查询明细的其他补助信息")
    public Response queryDetailExtraSubsidy(@RequestBody @Valid QueryDetailExtraSubsidyQO qo) {
        try {
            CheckMsg<?> checkMsg = pcxBillExtraSubsidyService.queryDetailExtraSubsidy(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询明细的其他补助信息异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询明细的其他补助信息异常");
        }
    }

}
