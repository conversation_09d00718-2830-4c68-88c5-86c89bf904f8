package com.pty.pcx.controller.setting;


import cn.hutool.core.lang.Pair;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.qo.setting.ApprovalProcessDefinitionQO;
import com.pty.pcx.qo.workflow2.*;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.workflow2.PcxWorkflowListener;
import com.pty.pcx.service.impl.workflow2.PositionServiceManager;
import com.pty.pcx.vo.ApprovalProcessDefinitionVO;
import com.pty.pcx.vo.workflow2.NodeInfoVO;
import com.pty.pcx.vo.workflow2.PositionVO;
import com.pty.pcx.vo.workflow2.ProcessCompositeVO;
import com.pty.pcx.vo.workflow2.ProcessHistoryVO;
import com.pty.pub.common.anno.TxTraceable;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.PtyContext;
import com.pty.setting.api.IPaBillTypeService;
import com.pty.setting.entity.PaBillType;
import com.pty.workflow2.api.IPtyWfDeployService;
import com.pty.workflow2.api.IPtyWfTaskQueryService;
import com.pty.workflow2.api.proxy.ExecutionContext;
import com.pty.workflow2.bo.TodoTaskParam;
import com.pty.workflow2.vo.IdentityInfo;
import com.pty.workflow2.vo.TodoTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Indexed;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Indexed
@RequestMapping(value = "/pcx/approvalProcessDef")
@Api(value = "审核流程定义相关接口", tags = {"审核流程定义控制层"})
@Slf4j
@RestSchema(schemaId = "/approvalProcessDef")
@RestController
public class ApprovalProcessDefinitionController {

    @Autowired
    private IPaBillTypeService paBillTypeService;
    @Autowired
    @Qualifier("PtyWfDeployService2")
    private IPtyWfDeployService ptyWfDeployService;
    @Autowired
    private IProcessService processService;
    @Autowired
    private PositionServiceManager positionServiceManager;

    @Autowired
    private PcxWorkflowListener pcxWorkflowListener;

    @Autowired
    private IPtyWfTaskQueryService ptyWfTaskQueryService;

    @Autowired
    private BillMainService billMainService;

    @Autowired
    private IWitAuditRuleService witAuditRuleService;

    /**
     * 获取业务类型
     */
    @PostMapping(value = "/selectBusinessTypeEnums")
    @ApiOperation(value = "获取业务类型")
    public Response<List<PaBillType>> selectBusinessTypeEnums() {
        List<PaBillType> billTypes = paBillTypeService.selectBySystemCode("pcx");
        return Response.success(billTypes);
    }


    /**
     * 获取审核流程定义
     */
    @PostMapping(value = "/selectApprovalProcessDef")
    @ApiOperation(value = "获取审核流程定义")
    public Response<ApprovalProcessDefinitionVO> selectApprovalProcessDef(@Validated(Query.class) @RequestBody ApprovalProcessDefinitionQO param) {
        try {
            ApprovalProcessDefinitionVO apdVo = new ApprovalProcessDefinitionVO();
            CheckMsg<ApprovalProcessDefinitionVO> msg = processService.queryProcessDefinition2(param.getAgyCode(), param.getMofDivCode(), param.getBillType(), param.getTenantId());
            if (Objects.nonNull(msg) && msg.isSuccess()) {
                apdVo = msg.getData();
            }
            return Response.success(apdVo);
        } catch (Exception e) {
            log.error("获取流程定义异常", e);
            return Response.businessFailResponse("流程定义异常:" + e.getMessage());
        }

    }

    /**
     * 调整审核流程定义
     * @param qo
     * @return
     */
    @TxTraceable(business = "调整审核流程定义")
    @PostMapping(value = "/updateApprovalProcessDef")
    @ApiOperation(value = "调整审核流程定义")
    public Response<String> updateApprovalProcessDef(@Validated(Query.class) @RequestBody ApprovalProcessDefinitionQO qo) {
        try {
            CheckMsg<Object> msg = processService.deployProcessDefinition(qo);
            if (Objects.nonNull(msg) && msg.isSuccess()) {
                return Response.success("流程部署成功");
            }else {
                return Response.businessFailResponse("流程部署失败");
            }
        } catch (Exception e) {
            log.error("流程定义异常", e);
            return Response.businessFailResponse("读取流程异常:" + e.getMessage());
        }
    }

    @PostMapping(value = "/listHistory")
    @ApiOperation(value = "获取流程历史记录")
    public Response<List<ProcessHistoryVO>> listHistory(@Validated(Query.class) @RequestBody ProcessHistoryQO qo) {
        try {
            CheckMsg<List<ProcessHistoryVO>> msg = processService.listHistory(qo);
            if (msg.isSuccess())
                return Response.success(msg.getData());
            else
                return Response.businessFailResponse(msg.getMsgInfo());
        }catch (Exception e) {
            log.error("获取流程历史记录异常", e);
            return Response.businessFailResponse("获取流程历史记录异常");
        }
    }

    @PostMapping(value = "/listHistoryWithDelegate")
    @ApiOperation(value = "获取流程历史记录,按taskId分组")
    public Response<ProcessCompositeVO> listHistoryWithDelegate(@Validated(Query.class) @RequestBody ProcessHistoryQO qo) {
        try {
            CheckMsg<ProcessCompositeVO> msg = processService.listHistoryWithDelegate(qo);
            if (msg.isSuccess())
                return Response.success(msg.getData());
            else
                return Response.businessFailResponse(msg.getMsgInfo());
        }catch (Exception e) {
            log.error("获取流程历史记录异常", e);
            return Response.businessFailResponse("获取流程历史记录异常");
        }
    }

    @PostMapping(value = "/queryNearestApproveUser/{positionCode}")
    @ApiOperation(value = "获取最新对应节点的审批人")
    public Response<ProcessHistoryVO> queryNearestApproveUser(@Validated(Query.class) @RequestBody ProcessHistoryQO qo, @PathVariable("positionCode") String positionCode) {
        try {
            CheckMsg<ProcessHistoryVO> msg = processService.queryNearestApproveUser(qo, positionCode);
            if (msg.isSuccess())
                return Response.success(msg.getData());
            else
                return Response.businessFailResponse(msg.getMsgInfo());
        }catch (Exception e) {
            log.error("获取最新对应节点的审批人异常", e);
            return Response.businessFailResponse("获取最新对应节点的审批人异常");
        }
    }

    @PostMapping(value = "/getPositionCode")
    @ApiOperation(value = "获取岗位编码")
    public Response<PositionVO> getPositionCode(@RequestBody PositionQO qo) {
        try {
            CheckMsg<PositionVO> res = processService.getPositionCode(qo);
            if (res.isSuccess())
                return Response.success(res.getData());
            return Response.businessFailResponse("获取岗位编码异常" + res.getMsgInfo());
        }catch (Exception e) {
            log.error("获取岗位编码异常", e);
            return Response.businessFailResponse("获取岗位编码异常" + e.getMessage());
        }
    }


    /**
     * 测试流程启动
     */
    @PostMapping(value = "/testStart")
    @ApiOperation(value = "测试启动流程")
    public Response<String> testStart(@RequestBody ProcessQO qo) {
        processService.startProcess(qo);
        return Response.success("流程启动成功");
    }

    /**
     * 测试任务提交
     */
    @TxTraceable(business = "单据送审/审批通过")
    @PostMapping(value = "/submit")
    @ApiOperation(value = "单据送审/审批通过")
    public Response<String> submit(@RequestBody @Validated(Update.class) SubmitQO qo) {
        try {
            log.info("提交流程参数:{}", qo);
            CheckMsg<Void> msg = processService.submitTask(qo);
            if (msg.isSuccess())
                return Response.success("流程提交成功").setMsg("流程提交成功");
            else
                return Response.businessFailResponse(msg.getMsgInfo());
        }catch (Exception e) {
            log.error("流程提交异常", e);
            String message = e.getMessage();
            // 去除第一个和第二个:之间的内容
            int tip = 0;
            while (message.indexOf(":") > 0 && tip++ < 2) {
                message = message.substring(message.indexOf(":") + 1);
            }
            return Response.businessFailResponse(message);
        }
    }


    @TxTraceable(business = "批量审批通过")
    @PostMapping(value = "/batchSubmit")
    @ApiOperation(value = "批量审批通过")
    public Response<String> batchSubmit(@RequestBody SubmitQO qo) {
        if (CollectionUtils.isNotEmpty(qo.getBillIds())) {
            AtomicInteger errCnt = new AtomicInteger();
            TodoTaskParam taskParam = new TodoTaskParam();
            taskParam.setBusiIds(new HashSet<>(qo.getBillIds()));
            taskParam.setMofDivCode(qo.getMofDivCode());
            taskParam.setTenantId(PtyContext.getTenantId());
            List<TodoTask> tasks = ptyWfTaskQueryService.getTodoTaskWithBusiIds(taskParam);
            Assert.notEmpty(tasks, "没有对应的待审批任务");
            Map<String, TodoTask> billTodoTaskRel = tasks.stream().collect(Collectors.toMap(TodoTask::getBusiId, todoTask -> todoTask, (t1, t2) -> t1));
            List<String> errBills = new ArrayList<>();
            qo.getBillIds().forEach(billId -> {
                try {
                    PcxBill bill = billMainService.view(billId);
                    TodoTask todoTask = billTodoTaskRel.get(billId);
                    String positionCode = todoTask.getNodeId();
                    witAuditRuleService.validateRule(bill, positionCode);

                    qo.setBillId(billId);
                    qo.setMofDivCode(bill.getMofDivCode());
                    Response<String> submit = this.submit(qo);
                    if (!submit.isSuccess()) {
                        errCnt.getAndIncrement();
                        errBills.add(billId);
                    }
                }catch (Exception e) {
                    log.error("批量审批通过异常{},{}", billId, e.getMessage());
                    errBills.add(billId);
                    errCnt.getAndIncrement();
                }
            });
            if (!errBills.isEmpty()) {
                List<PcxBill> pcxBills = billMainService.selectByIds(errBills);
                return Response.businessFailResponse(String.format("%d个单据审批失败,单号[%s]",  pcxBills.size(), pcxBills.stream().map(PcxBill::getBillNo).collect(Collectors.joining(","))));
            }
        }
        return Response.success("流程提交成功").setMsg("流程提交成功");
    }

    /**
     * 测试任务回退
     */
    @TxTraceable(business = "审批任务退回")
    @PostMapping(value = "/rollback")
    @ApiOperation(value = "审批任务退回")
    public Response<String> rollback(@RequestBody @Validated(Update.class) RollbackQO qo) {
        CheckMsg<Void> msg = processService.rollbackTask(qo);
        if (msg.isSuccess())
            return Response.success("流程回退成功").setMsg("流程回退成功");
        else
            return Response.businessFailResponse(msg.getMsgInfo());
    }

    /**
     * 任务撤回
     */
    @TxTraceable(business = "审批任务撤回")
    @PostMapping(value = "/revoke")
    @ApiOperation(value = "审批任务撤回")
    public Response<String> revoke(@RequestBody @Validated(Update.class) RevokeQO qo) {
        CheckMsg<Void> msg = processService.revokeTask(qo);
        if (msg.isSuccess())
            return Response.success("任务撤回成功").setMsg("任务撤回成功");
        else
            return Response.businessFailResponse(msg.getMsgInfo());
    }

    @TxTraceable(business = "终审任务撤回")
    @PostMapping("/revokeProcess")
    @ApiOperation(value = "终审任务撤回")
    public Response<String> revokeProcess(@RequestBody @Validated(Update.class) RevokeQO qo) {
        CheckMsg<Void> msg = processService.revokeProcess(qo);
        if (msg.isSuccess())
            return Response.success("任务撤回成功").setMsg("任务撤回成功");
        else
            return Response.businessFailResponse(msg.getMsgInfo());
    }

    @PostMapping(value = "/nodeCanReturnList")
    @ApiOperation(value = "获取可退回节点")
    public Response<List<NodeInfoVO>> findUser(@RequestBody @Validated(Query.class) ProcessQO qo) {
        try {
            CheckMsg<List<NodeInfoVO>> msg = processService.queryTaskCanReturnHiNodeList(qo);
            if (msg.isSuccess())
                return Response.success(msg.getData());
            else
                return Response.businessFailResponse(msg.getMsgInfo());
        }catch (Exception e) {
            log.error("获取可退回节点", e);
            return Response.businessFailResponse("获取可退回节点");
        }
    }

    /**
     * 流程委托(会签)
     * @param qo
     * @return
     */
    @TxTraceable(business = "流程委托(会签)")
    @PostMapping(value = "/delegate")
    @ApiOperation(value = "流程委托(会签)")
    public Response<String> delegate(@RequestBody @Validated(DelegateQO.Delegate.class) DelegateQO qo) {
        CheckMsg<Void> msg = processService.delegateTask(qo);
        if (msg.isSuccess())
            return Response.success("流程委托成功").setMsg("流程委托成功");
        else
            return Response.businessFailResponse(msg.getMsgInfo());
    }

    /**
     * 委托撤回(会签撤回)
     * @param qo
     * @return
     */
    @TxTraceable(business = "委托撤回(会签撤回)")
    @PostMapping(value = "/delegateRollback")
    @ApiOperation(value = "委托撤回(会签撤回)")
    public Response<String> delegateRollback(@RequestBody @Validated(Update.class) DelegateQO qo) {
        CheckMsg<Void> msg = processService.delegateRollbackProcess(qo);
        if (msg.isSuccess())
            return Response.success("流程委托回退成功").setMsg("流程委托回退成功");
        else
            return Response.businessFailResponse(msg.getMsgInfo());
    }

    @PostMapping(value = "/findUser")
    @ApiOperation(value = "获取用户")
    public Response<Pair<Boolean, IdentityInfo>> findUser(@RequestParam("billId") String billId, @RequestParam("positionId") String positionId) {
        ExecutionContext context = new ExecutionContext() {
            @Override
            public String getId() {
                return "";
            }

            @Override
            public String getProcessDefinitionId() {
                return "";
            }

            @Override
            public String getProcessInstanceId() {
                return "";
            }

            @Override
            public String getCurrentNodeId() {
                return positionId;
            }

            @Override
            public String getBusinessKey() {
                return billId;
            }

            @Override
            public Object getVar(String varName) {
                return null;
            }

            @Override
            public void setVariable(String variableName, Object value) {

            }

            @Override
            public void setVariableLocal(String variableName, Object value) {

            }

            @Override
            public void setTransientVariable(String variableName, Object variableValue) {

            }

            @Override
            public void setTransientVariableLocal(String variableName, Object variableValue) {

            }

            @Override
            public Boolean needCalculateExecutor() {
                return null;
            }
        };
//        IPositionService<String> positionService = positionServiceManager.getPositionService(positionId);
        try {
            ThreadLocalUtil.set(positionId);
            Pair<Boolean, IdentityInfo> booleanIdentityInfoPair = pcxWorkflowListener.calculateAssignOrGroups(context);
            return Response.success(booleanIdentityInfoPair);
        }catch (Exception e) {
            log.error("获取用户异常", e);
            return Response.businessFailResponse("获取用户异常");
        }finally {
            ThreadLocalUtil.remove();
        }
    }
}
