package com.pty.pcx.controller.bas;


import com.pty.pcx.api.bas.IPcxMadExpecoService;
import com.pty.pcx.qo.bas.PcxBasExpExpecoQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.pty.pub.common.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Indexed
@RequestMapping(value = "/pcx/madExpeco")
@Api(value = "部门经济分类表接口", tags = {"经济分类表控制层"})
@Slf4j
@RestSchema(schemaId = "/PcxMadExpecoController")
@RestController
public class PcxMadExpecoController {

        @Autowired
        private IPcxMadExpecoService pcxMadExpecoService;

        @PostMapping(value = "/getAll")
        @ApiOperation(value = "查询所有的")
        public Response<?> getAll(@RequestBody PcxBasExpExpecoQO pcxBasExpExpecoQO) {
            try{
                return pcxMadExpecoService.getAll(pcxBasExpExpecoQO);
            }catch (Exception e){
                return Response.fail().setMsg(e.getMessage());
            }
        }
}
